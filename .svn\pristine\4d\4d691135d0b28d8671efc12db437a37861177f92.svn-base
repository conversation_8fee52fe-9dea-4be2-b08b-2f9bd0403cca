package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;

/**
 * 系統代碼維護
 *
 */
public class PRE9004 extends SuperBean {

	/** 查詢條件 */
	private String q_id;
	private String q_codeKind;
	private String q_codeName;
	private String q_enable;

	/** 資料欄位 */
	private String id;			//主鍵值
	private String codeKind;	//代碼類別
	private String code;		//代碼
	private String codeName;	//代碼名稱
	private String codeDesc;	//代碼說明
	private String codeParam1;	//代碼參數1
	private String codeParam2;	//代碼參數2
	private String codeParam3;	//代碼參數2
	private String remark;		//備註
	private String enable;		//是否啟用(Y:啟動,N:停用 )
	
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}
	public String getQ_codeKind() {return checkGet(q_codeKind);}
	public void setQ_codeKind(String q_codeKind) {this.q_codeKind = checkSet(q_codeKind);}
	public String getQ_codeName() {return checkGet(q_codeName);}
	public void setQ_codeName(String q_codeName) {this.q_codeName = checkSet(q_codeName);}
	public String getQ_enable() {return checkGet(q_enable);}
	public void setQ_enable(String q_enable) {this.q_enable = checkSet(q_enable);}
	
	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getCodeKind() {return checkGet(codeKind);}
	public void setCodeKind(String codeKind) {this.codeKind = checkSet(codeKind);}
	public String getCode() {return checkGet(code);}
	public void setCode(String code) {this.code = checkSet(code);}
	public String getCodeName() {return checkGet(codeName);}
	public void setCodeName(String codeName) {this.codeName = checkSet(codeName);}
	public String getCodeDesc() {return checkGet(codeDesc);}
	public void setCodeDesc(String codeDesc) {this.codeDesc = checkSet(codeDesc);}
	public String getCodeParam1() {return checkGet(codeParam1);}
	public void setCodeParam1(String codeParam1) {this.codeParam1 = checkSet(codeParam1);}
	public String getCodeParam2() {return checkGet(codeParam2);}
	public void setCodeParam2(String codeParam2) {this.codeParam2 = checkSet(codeParam2);}
	public String getCodeParam3() {return checkGet(codeParam3);}
	public void setCodeParam3(String codeParam3) {this.codeParam3 = checkSet(codeParam3);}
	public String getRemark() {return checkGet(remark);}
	public void setRemark(String remark) {this.remark = checkSet(remark);}
	public String getEnable() {return checkGet(enable);}
	public void setEnable(String enable) {this.enable = checkSet(enable);}

	@Override
	public Object doQueryOne() throws Exception {
		PRE9004 obj = this;
		SystemCode o = ServiceGetter.getInstance().getPrefixService().getSystemCodeById(Common.getInt(id));
		if (null!=o) {
	        obj.setId(o.getId().toString());
	        obj.setCodeKind(o.getCodeKind());
	        obj.setCode(o.getCode());
	        obj.setCodeName(o.getCodeName());
	        obj.setCodeDesc(o.getCodeDesc());
	        obj.setCodeParam1(o.getCodeParam1());
	        obj.setCodeParam2(o.getCodeParam2());
	        obj.setCodeParam3(o.getCodeParam3());
	        obj.setRemark(o.getRemark());
	        obj.setEnable(("Y".equalsIgnoreCase(o.getEnable())?"Y":"N"));
		} else 
			this.setErrorMsg("查無該筆資料！");
		return obj;
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		//this.processCurrentPageAttribute(ServiceGetter.getInstance().getPrefixService().countSystemCode(getQ_codeKind(), getQ_codeName()));
		//if (getTotalRecord() > 0) {
			java.util.List<SystemCode> objList = ServiceGetter.getInstance().getPrefixService().querySystemCode(getQ_codeKind(), getQ_codeName());			
			if (objList != null && objList.size() > 0) {
				//codeKind == 00, 只有這個作業會用到
				java.util.Map<String, String> systemCode = TcfiView.getSystemCodeMap("00");
				java.util.Iterator<?> it = objList.iterator();
				SystemCode o;
				String[] rowArray = new String[5];
				while (it.hasNext()) {
					o = (SystemCode) it.next();
					rowArray = new String[5];
					rowArray[0] = Common.get(o.getId());
					rowArray[1] = Common.get(systemCode.get(o.getCodeKind()));
					rowArray[2] = Common.get(o.getCode());
					rowArray[3] = Common.get(o.getCodeName());
					rowArray[4] = ("Y".equalsIgnoreCase(o.getEnable())?"是":"否");
					arrList.add(rowArray);	
				}
			}else{
				this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
			}
		//}
		return arrList;
	}

	@Override
	public void doCreate() throws Exception {
		String codeKind = getCodeKind();
		
		if(getUpdateCheck()){
			SystemCode o = new SystemCode();
			o.setCodeKind(getCodeKind());
			o.setCode(getCode());
			o.setCodeName(getCodeName());
			o.setCodeDesc(getCodeDesc());
			o.setCodeParam1(getCodeParam1());
			o.setCodeParam2(getCodeParam2());
			o.setCodeParam3(getCodeParam3());
			o.setRemark(getRemark());
			o.setEnable(("Y".equalsIgnoreCase(getEnable())?"Y":"N"));
			o.setModIdNo(getLoginUserId());
			o.setModDate(Datetime.getYYYMMDD());
			o.setModTime(Datetime.getHHMMSS());
			o = ServiceGetter.getInstance().getPrefixService().insertSystemCode(o);
			setId(Common.get(o.getId()));
		}else{
			throw new MoeaException("已存在相同代碼類別的資料，請重新輸入！");
		}
		//重新載入
		reloadLoader(codeKind);
	}

	@Override
	public void doUpdate() throws Exception {
		SystemCode o = ServiceGetter.getInstance().getPrefixService().getSystemCodeById(Common.getInt(id));
		if(null==o) throw new MoeaException("資料不存在!");
		String codeKind = getCodeKind();
		
		if(getUpdateCheck()){
			o.setCodeKind(getCodeKind());
			o.setCode(getCode());
			o.setCodeName(getCodeName());
			o.setCodeDesc(getCodeDesc());
			o.setCodeParam1(getCodeParam1());
			o.setCodeParam2(getCodeParam2());
			o.setCodeParam3(getCodeParam3());
			o.setRemark(getRemark());
			o.setEnable(("Y".equalsIgnoreCase(getEnable())?"Y":"N"));
			o.setModIdNo(getLoginUserId());
			o.setModDate(Datetime.getYYYMMDD());
			o.setModTime(Datetime.getHHMMSS());
			o = ServiceGetter.getInstance().getPrefixService().updateSystemCode(o);
		}else{
			throw new MoeaException("已存在相同代碼類別的資料，請重新輸入！");
		}
		//重新載入
		reloadLoader(codeKind);
	}

	@Override
	public void doDelete() throws Exception {
		SystemCode o = ServiceGetter.getInstance().getPrefixService().getSystemCodeById(Common.getInt(id));
		if(null==o) throw new MoeaException("資料不存在!");
		String codeKind = o.getCodeKind();
		
		//delete - 只設定停用, 不刪除
		ServiceGetter.getInstance().getPrefixService().deleteSystemCode(o);

		//重新載入
		reloadLoader(codeKind);
	}
	
	/** 檢核 codeKind 與 code 是否重複 */
	protected boolean getUpdateCheck(){
		SystemCode o = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(this.getCodeKind(), this.getCode());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getId().equals(Common.getInt(this.getId())))
				return true;
		}
		return false;
	}
	
	private void reloadLoader(String codeKind) {
		if( PrefixConstants.CODE_KIND_02.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode02Loader().reload();
		} else if( PrefixConstants.CODE_KIND_03.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode03Loader().reload();
		} else if( PrefixConstants.CODE_KIND_04.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode04Loader().reload();
		} else if( PrefixConstants.CODE_KIND_05.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode05Loader().reload();
		} else if( PrefixConstants.CODE_KIND_06.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode06Loader().reload();
		} else if( PrefixConstants.CODE_KIND_07.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode07Loader().reload();
		} else if( PrefixConstants.CODE_KIND_08.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode08Loader().reload();
		} else if( PrefixConstants.CODE_KIND_09.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode09Loader().reload();
		} else if( PrefixConstants.CODE_KIND_10.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode10Loader().reload();
		} else if( PrefixConstants.CODE_KIND_11.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode11Loader().reload();
		} else if( PrefixConstants.CODE_KIND_12.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode12Loader().reload();
		} else if( PrefixConstants.CODE_KIND_13.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode13Loader().reload();
		} else if( PrefixConstants.CODE_KIND_14.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode14Loader().reload();
		} else if( PrefixConstants.CODE_KIND_15.equals(codeKind) ) {
			ServiceGetter.getInstance().getSystemCode15Loader().reload();
		}
	}
}
