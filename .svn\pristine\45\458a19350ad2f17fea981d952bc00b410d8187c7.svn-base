package com.kangdainfo.tcfi.view.pre;
/*
程式目的：民眾申請案件統計表
程式代號：pre2002
撰寫日期：103.05.30
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE1009 extends SuperBean {
	
	public static final String MSG_KEY = "PRE1009_MSG";
	
	private String q_yearMonth ;
	
	private String receiveThisMonth ;
	private String receiveLastMonth ;
	private String inDeadLineDone ;
	private String outDeadLineDone ;
	private String inDeadLineUndone ;
	private String outDeadLineUndone ;
	
	public String getQ_yearMonth() {return checkGet(q_yearMonth);}
	public void setQ_yearMonth(String s) {q_yearMonth = checkSet(s);}
	
	public String getReceiveThisMonth() {return checkGet(receiveThisMonth);}
	public void setReceiveThisMonth(String s) {receiveThisMonth = checkSet(s);}
	public String getReceiveLastMonth() {return checkGet(receiveLastMonth);}
	public void setReceiveLastMonth(String s) {receiveLastMonth = checkSet(s);}
	public String getInDeadLineDone() {return checkGet(inDeadLineDone);}
	public void setInDeadLineDone(String s) {inDeadLineDone = checkSet(s);}
	public String getOutDeadLineDone() {return checkGet(outDeadLineDone);}
	public void setOutDeadLineDone(String s) {outDeadLineDone = checkSet(s);}
	public String getInDeadLineUndone() {return checkGet(inDeadLineUndone);}
	public void setInDeadLineUndone(String s) {inDeadLineUndone = checkSet(s);}
	public String getOutDeadLineUndone() {return checkGet(outDeadLineUndone);}
	public void setOutDeadLineUndone(String s) {outDeadLineUndone = checkSet(s);}

	public void doCreate() throws Exception{}
	public void doUpdate() throws Exception{}
	public void doDelete() throws Exception{}
	public Object doQueryOne() throws Exception{return null;}
	public ArrayList<?> doQueryAll() throws Exception {return null;}

	public File doPrintPdf() throws Exception {
		try {
	        File report = null ;
	        String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre1009_1.jasper");
	        Map<String, Object> parameters = new HashMap<String,Object>();
	        String yearMonth = getQ_yearMonth() ;
	        parameters.put("yearMonth", Common.formatYYYMMDD(yearMonth,2));//收文月份
	        parameters.put("printDate", Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印日期
	        parameters.put("producer", getLoginUserName());//製表人

		    SQLJob sqljob = new SQLJob();
		    sqljob.appendSQL("select");
		    if (Integer.parseInt(yearMonth.substring(0,3)) < 106)
		    	sqljob.appendSQL("'商　業　司\r\n六　科 ' as z0");
		    else
		    	sqljob.appendSQL("'中　部　辦\r\n公　室 ' as z0");
		    sqljob.appendSQL(",to_char(c2+c3) as z1");
		    sqljob.appendSQL(",to_char(c2) as z2");
		    sqljob.appendSQL(",to_char(c3) as z3");
		    sqljob.appendSQL(",to_char(c5+c6) as z4");
		    sqljob.appendSQL(",to_char(c5) as z5");
		    sqljob.appendSQL(",to_char((c5/decode((c5+c6),0,1,(c5+c6)))*100,'990.9')||'%' as z6");
		    sqljob.appendSQL(",to_char(c6) as z7");
		    sqljob.appendSQL(",to_char((c6/decode((c5+c6),0,1,(c5+c6)))*100,'990.9')||'%' as z8");
		    sqljob.appendSQL(",to_char(c8+c9) as z9");
		    sqljob.appendSQL(",to_char(c8) as z10");
		    sqljob.appendSQL(",to_char((c8/decode((c8+c9),0,1,(c8+c9)))*100,'990.9')||'%' as z11");
		    sqljob.appendSQL(",to_char(c9) as z12");
		    sqljob.appendSQL(",to_char((c9/decode((c8+c9),0,1,(c8+c9)))*100,'990.9')||'%' as z13");
		    sqljob.appendSQL("from (");
		    sqljob.appendSQL("select");
		    sqljob.appendSQL("a.currYM");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date like a.currYM||'%') as c2");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date < a.currYM||'00' and (close_date is null or close_date > a.currYM||'00')) as c3");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date < a.currYM||'99' and close_date like a.currYM||'%' and nvl((select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no),0) <= a.limit) as c5");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date < a.currYM||'99' and close_date like a.currYM||'%' and nvl((select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no),0) > a.limit) as c6");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date < a.currYM||'99' and (close_date is null or close_date > a.currYM||'99') and nvl((select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no),0) <= a.limit) as c8");
		    sqljob.appendSQL(",(select count(1) from eicm.cedb1000 a where prefix_no like substr(a.currYM,1,3)||'%' and receive_date < a.currYM||'99' and (close_date is null or close_date > a.currYM||'99') and nvl((select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no),0) > a.limit) as c9");
		    sqljob.appendSQL("from (");
		    sqljob.appendSQL("select");
		    sqljob.appendSQL("? as currYM,2 as limit");
		    sqljob.appendSQL("from dual");
		    sqljob.appendSQL(") a");
		    sqljob.appendSQL(") z");
		    sqljob.addParameter(yearMonth);

		    List<?> dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	        report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	        return report ;
		} // try
		catch( Exception e ) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<300) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null ;
		} // catch
	} // doPrintfPdf()	

} // PRE1009()