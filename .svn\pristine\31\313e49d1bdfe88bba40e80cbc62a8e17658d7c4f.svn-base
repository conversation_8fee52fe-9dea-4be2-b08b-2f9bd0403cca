// ### TopLink Mapping Workbench 9.0.3 generated source code ###

package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class Eicm3000 extends BaseModel {
	private static final long serialVersionUID = 1L;

	private java.lang.String approveResult;
	private java.lang.String companyName;
	private java.lang.String seqNo;
	private java.lang.String telixNo;

	public java.lang.String getApproveResult() {return approveResult;}
	public void setApproveResult(java.lang.String approveResult) {this.approveResult = approveResult;}

	public java.lang.String getCompanyName() {return companyName;}
	public void setCompanyName(java.lang.String companyName) {this.companyName = companyName;}

	public java.lang.String getSeqNo() {return seqNo;}
	public void setSeqNo(java.lang.String seqNo) {this.seqNo = seqNo;}

	public java.lang.String getTelixNo() {return telixNo;}
	public void setTelixNo(java.lang.String telixNo) {this.telixNo = telixNo;}

}