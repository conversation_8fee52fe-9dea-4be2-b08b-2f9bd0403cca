package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;

/**
 * 排程(PRE0005)
 * 重新執行索引失敗的排程
 */
public class Pre0005QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		ServiceGetter.getInstance().getIndexUpdateService().doRetryError();
	}

}