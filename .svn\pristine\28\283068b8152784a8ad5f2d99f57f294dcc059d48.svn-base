package com.kangdainfo.tcfi.view.pre;


public interface IApprove {
	// 是否有同名的公司名稱, 必然要詢問
	boolean existSameCompanyNames = false;
	// 是否有衝突的公司名稱(如:同申請人的設立案或同統編的變更案)
	// 是否有衝突的公司名稱(如:同申請人的且同公司名稱)
	boolean existConflictCompanyNames = false;
	// 是否已經檢查過衝突的公司名稱, 若已檢查過, 則不再開檢還視窗
	boolean everCheckedConflict = false;

	String displayValue = "";

	String getBanNo();
	String getPrefixNo();
	String getTelixNo();
	String getApplyId();
	String getApplyName();
	String getCompanyName();
	String getFunctionName();
	String gethRemark();
	String getResetCloseDateFlag();

	void setChinaBusitemMark(String busItemMark);
	// String saveToDb(PrefixVo prefixVo, IApprove approve, ApproveService
	// approveService, String userId);
}
