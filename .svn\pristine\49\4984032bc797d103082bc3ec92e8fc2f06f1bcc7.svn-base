package com.kangdainfo.common.model.bo;


public class CommonAuth implements java.io.Serializable {
	private static final long serialVersionUID = -2463911894933795940L;
	private Long id;
	private CommonDtree commonDtree;
	private int auth;
	private String editId;
	private String editDate;
	private String editTime;

	public CommonAuth() {
	}

	public CommonAuth(Long id, int auth) {
		this.id = id;
		this.auth = auth;
	}

	public CommonAuth(Long id,
			CommonDtree commonDtree, int auth, String editId, String editDate,
			String editTime) {
		this.id = id;
		this.commonDtree = commonDtree;
		this.auth = auth;
		this.editId = editId;
		this.editDate = editDate;
		this.editTime = editTime;
	}

	public Long getId() {return this.id;}
	public void setId(Long id) {this.id = id;}

	public CommonDtree getCommonDtree() {return this.commonDtree;}
	public void setCommonDtree(CommonDtree commonDtree) {this.commonDtree = commonDtree;}

	public int getAuth() {return this.auth;}
	public void setAuth(int auth) {this.auth = auth;}

	public String getEditId() {return this.editId;}
	public void setEditId(String editId) {this.editId = editId;}

	public String getEditDate() {return this.editDate;}
	public void setEditDate(String editDate) {this.editDate = editDate;}

	public String getEditTime() {return this.editTime;}
	public void setEditTime(String editTime) {this.editTime = editTime;}

}
