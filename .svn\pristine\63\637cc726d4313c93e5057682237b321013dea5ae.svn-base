<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String groupId = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(groupId)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDBC000 WHERE GROUP_ID=?");
		sqljob.addParameter(groupId);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>