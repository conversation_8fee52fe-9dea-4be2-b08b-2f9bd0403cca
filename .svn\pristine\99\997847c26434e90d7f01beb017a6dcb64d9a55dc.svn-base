<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- USE JNDI CONNECTION -->
	<!-- <bean id="eedbDataSource" class="org.springframework.jndi.JndiObjectFactoryBean"> 
		<property name="jndiName"> <value>java:comp/env/jdbc/eedb</value> </property> 
		</bean> -->
	<bean id="eedbDataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${eedb.dataSource.driverClassName}" />
		<property name="url" value="${eedb.dataSource.url}" />
		<property name="username" value="${eedb.dataSource.username}" />
		<property name="password" value="${eedb.dataSource.password}" />
		<property name="validationQuery" value="${eedb.dataSource.validationQuery}" />
		<property name="poolPreparedStatements" value="${eedb.dataSource.poolPreparedStatements}" />
		<property name="maxOpenPreparedStatements" value="${eedb.dataSource.maxOpenPreparedStatements}" />
		<property name="maxActive" value="${eedb.dataSource.maxActive}" />
		<property name="maxIdle" value="${eedb.dataSource.maxIdle}" />
	</bean>

	<bean id="eedbGeneralQueryDao" class="com.kangdainfo.tcfi.model.eedb.dao.EedbGeneralQueryDao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>

	<bean id="eedb1000Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb1000Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb1100Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb1100Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb1300Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb1300Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb3000Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb3100Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb3300Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedb5000Dao" class="com.kangdainfo.tcfi.model.eedb.dao.Eedb5000Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="eedbV8000Dao" class="com.kangdainfo.tcfi.model.eedb.dao.EedbV8000Dao">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>

</beans>