<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE1007"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String year = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("year")));
try {
	if ( ( year != null && !"".equals(year) ) )
	{
		String checkResult = PRE1007.checkForjsp(year);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>