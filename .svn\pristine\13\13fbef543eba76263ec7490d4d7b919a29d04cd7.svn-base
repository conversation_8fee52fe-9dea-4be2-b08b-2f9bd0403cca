package com.kangdainfo.tcfi.lucene.bo;

import org.apache.commons.lang.builder.ToStringBuilder;

public class IndexData {
	
	/** 索引鍵值 */
	private String id;
	/** 預查編號 */
	private String prefixNo;
	/** 統一編號 */
	private String banNo;
	/** 預查種類 */
	private String applyKind;
	/** 公司名稱 */
	private String companyName;
	/** 特取名稱 */
	private String specialName;
	/** 核覆結果 */
	private String approveResult;
	/** 公司狀態 */
	private String cmpyStatus;
	/** 廢止生效日期 */
	private String revokeAppDate;
	/** 核准設立日期 */
	private String setupDate;
	/** 申請日期 */
	private String receiveDate;
	/** 保留期限 */
	private String reserveDate;
	/** 申請人 */
	private String applyName;
	/** 組織型態 */
	private String orgnType;
	/** 排序類別(1:已收文,2:預查,3:公司) */
	private String sortby;
	/** 索引類別(1:公司,2:已收文,3:預查) */
	private String indexType;
	/** 排序預查編號(已收文:PREFIX_NO,預查:PREFIX_NO,公司:空白) */
	private String prefixNoSort;
	/** 公司狀態是否廢止(cmpyStatus:04,05,06,09 為y, 其他為n) */
	private String cmpyRevoke;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public String getId() {return id;}
	public void setId(String s) {this.id = s;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getApplyKind() {return applyKind;}
	public void setApplyKind(String s) {this.applyKind = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String s) {this.specialName = s;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String s) {this.approveResult = s;}
	public String getCmpyStatus() {return cmpyStatus;}
	public void setCmpyStatus(String s) {this.cmpyStatus = s;}
	public String getRevokeAppDate() {return revokeAppDate;}
	public void setRevokeAppDate(String s) {this.revokeAppDate = s;}
	public String getSetupDate() {return setupDate;}
	public void setSetupDate(String s) {this.setupDate = s;}
	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String s) {this.reserveDate = s;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String s) {this.applyName = s;}
	public String getOrgnType() {return orgnType;}
	public void setOrgnType(String s) {this.orgnType = s;}
	public String getSortby() {return sortby;}
	public void setSortby(String s) {this.sortby = s;}
	public String getIndexType() {return indexType;}
	public void setIndexType(String s) {this.indexType = s;}
	public String getPrefixNoSort() {return prefixNoSort;}
	public void setPrefixNoSort(String s) {this.prefixNoSort = s;}
	public String getCmpyRevoke() {return cmpyRevoke;}
	public void setCmpyRevoke(String s) {this.cmpyRevoke = s;}

}