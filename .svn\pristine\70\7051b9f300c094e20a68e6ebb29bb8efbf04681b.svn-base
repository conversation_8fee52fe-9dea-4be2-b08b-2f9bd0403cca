package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgRegister;

public class OssmOrgRegisterDao extends BaseDaoJdbc implements RowMapper<OssmOrgRegister> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSS.OSSM_ORG_REGISTER WHERE TELIX_NO = ?";
	public OssmOrgRegister findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		logger.debug(sqljob);
		return getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static String SQL_updateOrgCapitalAmtByTelixNo = "UPDATE OSSS.OSSM_ORG_REGISTER SET ORG_CAPITAL_AMT = 0 WHERE TELIX_NO = ? AND ORG_CAPITAL_AMT IS NULL";
	public int updateOrgCapitalAmtByTelixNo(String telixNo) {
		if (telixNo == null)
			return -1;
		SQLJob sqljob = new SQLJob(SQL_updateOrgCapitalAmtByTelixNo);
		sqljob.addParameter(telixNo);
		logger.info(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private static String SQL_updateOrgRealCapitalAmtByTelixNo = "UPDATE OSSS.OSSM_ORG_REGISTER SET ORG_REAL_CAPITAL_AMT = 0 WHERE TELIX_NO = ? AND ORG_REAL_CAPITAL_AMT IS NULL";
	public int updateOrgRealCapitalAmtByTelixNo(String telixNo) {
		if (telixNo == null)
			return -1;
		SQLJob sqljob = new SQLJob(SQL_updateOrgRealCapitalAmtByTelixNo);
		sqljob.addParameter(telixNo);
		logger.info(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	@Override
	public OssmOrgRegister mapRow(ResultSet rs, int idx) throws SQLException {
		OssmOrgRegister obj = null;
		if(null!=rs) {
			obj = new OssmOrgRegister();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setOrgName(rs.getString("ORG_NAME"));
			obj.setOrgAreaCode(rs.getString("ORG_AREA_CODE"));
			obj.setOrgNeiborCode(rs.getString("ORG_NEIBOR_CODE"));
			obj.setOrgAddr(rs.getString("ORG_ADDR"));
			obj.setOrgZipCode(rs.getString("ORG_ZIP_CODE"));
			obj.setOrgCapitalAmt(rs.getInt("ORG_CAPITAL_AMT"));
			obj.setOrgRealCapitalAmt(rs.getInt("ORG_REAL_CAPITAL_AMT"));
			obj.setBussAreaCode(rs.getString("BUSS_AREA_CODE"));
			obj.setBussNeiborCode(rs.getString("BUSS_NEIBOR_CODE"));
			obj.setBussAddr(rs.getString("BUSS_ADDR"));
			obj.setBussZipCode(rs.getString("BUSS_ZIP_CODE"));
			obj.setOrgTel(rs.getString("ORG_TEL"));
			obj.setOrgAddrComb(rs.getString("ORG_ADDR_COMB"));
			obj.setBussAddrComb(rs.getString("BUSS_ADDR_COMB"));
			obj.setIfBussCard(rs.getString("IF_BUSS_CARD"));
			obj.setNtaxBanNo(rs.getString("NTAX_BAN_NO"));
			obj.setBranchAreaCode(rs.getString("BRANCH_AREA_CODE"));
			obj.setBranchNeiborCode(rs.getString("BRANCH_NEIBOR_CODE"));
			obj.setBranchAddr(rs.getString("BRANCH_ADDR"));
			obj.setBranchZipCode(rs.getString("BRANCH_ZIP_CODE"));
			obj.setBranchAddrComb(rs.getString("BRANCH_ADDR_COMB"));
			obj.setLawApplyName(rs.getString("LAW_APPLY_NAME"));
			obj.setBranchSetupNum(rs.getInt("BRANCH_SETUP_NUM"));
			obj.setBranchChangeNum(rs.getInt("BRANCH_CHANGE_NUM"));
			obj.setBranchDeregisterNum(rs.getInt("BRANCH_DEREGISTER_NUM"));
			obj.setForinBranchDeregstrNum(rs.getInt("FORIN_BRANCH_DEREGSTR_NUM"));
			obj.setBranchTel(rs.getString("BRANCH_TEL"));
			obj.setBranchFax(rs.getString("BRANCH_FAX"));
			obj.setBranchName(rs.getString("BRANCH_NAME"));
			obj.setBranchBanNo(rs.getString("BRANCH_BAN_NO"));
			obj.setBranchAbolishDate(rs.getDate("BRANCH_ABOLISH_DATE"));
			obj.setNewOrgType(rs.getString("NEW_ORG_TYPE"));
			obj.setPrefixNoName(rs.getString("PREFIX_NO_NAME"));
			obj.setPrefixNoItem(rs.getString("PREFIX_NO_ITEM"));
			obj.setEngOrgName(rs.getString("ENG_ORG_NAME"));
			obj.setEngApplyName(rs.getString("ENG_APPLY_NAME"));
			obj.setEngPartnerName(rs.getString("ENG_PARTNER_NAME"));
			obj.setEngBussItem(rs.getString("ENG_BUSS_ITEM"));
		}
		return obj;
	}

}