package com.kangdainfo.common.util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.web.util.WebConstants;

public abstract class SessionDataBean extends DefaultBean {

	/** SESSION */
	public HttpSession getSession() {
		ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes(); 
		HttpServletRequest request = attr.getRequest();
		return request.getSession(false);
	}
	
	public CommonUser getCurrentUser() {
		return (CommonUser) getSession().getAttribute(WebConstants.SESSION_CURRENT_USER);
	}
	
	public String getLoginUserId() {
		CommonUser u = getCurrentUser();
		if(null!=u)
			return u.getUserId();
		return null;
	}
	
	public String getLoginUserName() {
		CommonUser u = getCurrentUser();
		if(null!=u)
			return u.getUserName();
		return null;
	}

}
