<!DOCTYPE html>
<%--
程式目的：預查申請資料收文
程式代號：PRE1001
撰寫日期：103.04.16
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
--%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.Cedbc000"%>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
String staffUnit = cedbc000.getStaffUnit();
if ("init".equals(obj.getState())){
	//do nothing
} else if("getNoAndSave".equals(obj.getState())) {
	//空白表單收文 或 一維條碼
	obj.save();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE1001"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript" src="../../js/commonUtils.js"></script>
<script type="text/javascript" src="../../js/jquery.splendid.textchange.js"></script>
<script type="text/javascript">

function ajaxGetOnLineCnt() {
	//查詢待分文件數
	$.post( getVirtualPath() + "tcfi/ajax/ajaxPre1001.jsp", function( data ) {
		var cnt = "";
		try {
			cnt = data;
			if( data.length > 20 ) throw 'ERROR'; 
		} catch(e) {
			cnt = "";
		}
		$('#onLineCnt').val(cnt);
	});
}

function ajaxGetCurrPrefixNo() {
	//查詢現況取號最大號
	$.post( getVirtualPath() + "tcfi/ajax/jsonPre1001GetPrefixNo.jsp", function( data ) {
		var no = "";
		try {
			no = data;
			if( data.length > 20 ) throw 'ERROR'; 
		} catch(e) {
			no = "連線異常";
		}
		$('#prefixNo').val(no);
	});
}

function ajaxLoadCompanyNames(telixNo) {
	//查詢公司名稱資料
	$("#companyNames").hide();
	$("#companyNames").find('tbody').empty();
	if($("#companyNames").find('tbody > tr').size() == 0) {
		$.post( getVirtualPath() + "tcfi/ajax/jsonEedb3000.jsp?q2=Y&q=" + telixNo, function( data ) {
			if(data) {
				var html = '';
				for(var i in data) {
					html += '<tr>'+
					'<td style="text-align:center;color: #000000;">'+ data[i].SEQ_NO +'</td>' +
					'<td style="color: #000000;">'+ data[i].COMPANY_NAME +'</td>'+
					'</tr>';
				}
				$("#companyNames").find("tbody").append(html);
				$("#companyNames").show();
			}
		});
	}
}

function resetForm() {
	//清空畫面欄位
	$("#banNo").val('');
	$("#oldCompanyName").val('');
	$("#companyAddr").val('');
	$("#applyName").val('');
	$("#applyId").val('');
	$("#applyTel").val('');
	$("#attorAddr").val('');
	$("#attorTel").val('');
	$("#attorNo").val('');
	$("#attorName").val('');
	$("#contactName").val('');
	$("#contactAddr").val('');
	$("#contactTel").val('');
	$("#contactCel").val('');
	$("#applyLawName").val('');
	$("#applyBanNo").val('');	
	$("input[name=contactGetKind]").prop('checked', false);
	showMsgBar("");
	syncChangeTypeCheckbox('');
	$("#companyNames").find('tbody').empty();
	$("#companyNames").hide();
}

function syncChangeTypeCheckbox(changeType) {
	$('#setup').removeAttr('checked');
	$('#changeName').removeAttr('checked');
	$('#changeItem').removeAttr('checked');
	if( '0' == changeType ) {
		$('#setup').attr('checked', true);
	} else if( '1' == changeType ) {
		$('#changeName').attr('checked', true);
	} else if( '2' == changeType ) {
		$('#changeItem').attr('checked', true);
	} else if( '3' == changeType ) {
		$('#changeName').attr('checked', true);
		$('#changeItem').attr('checked', true);
	}
}

function printPre1005(){
	var r = Math.floor(Math.random()*10);
	var prefixNo = $('#prefixNo').val();
	var url = getVirtualPath() + 'tcfi/pre/pre1005.jsp?PREFIX_NO_START='+ prefixNo +'&PREFIX_NO_END='+ prefixNo +'&from=pre1001';
	window.parent.frames['pre1001_f'+r].location = url;
	var shouldPrint = commonUtils.getURLParameter("printAddressTag");
	if(shouldPrint == 'Y') {
		var url2 = getVirtualPath() + 'tcfi/pre/pre1005.jsp?PREFIX_NO_START='+ prefixNo +'&PREFIX_NO_END='+ prefixNo +'&from=pre1001&printAddressTag=Y';
		window.parent.frames['pre1001_f'+((r+1)%10)].location = url2;
	}
	$("#telixNo").focus();
}

//暫時停用線上收文按鈕3秒
function disableOnline() {
	$("#onLineCntBtn").attr('disabled', true);
	setInterval(function() {
		$("#onLineCntBtn").removeAttr('disabled');
	}, 3000);
}
//附件檢核
function chkAttachment() {
	if( !!$("input[name=isPrefixForm]:checked").val() ) {
		if( $("input[name=prefixFormNo]").val().length != 9 ) {
			alert('附件之預查編號必填且須符合規範');
			return false;
		}
	}
	if( $("input[name=prefixFormNo]").val() != '' ) {
		if( !$("input[name=isPrefixForm]").prop("checked") ) {
			alert('有填預查編號時附件必須勾選');
			return false;
		}
	}
	
	if( !!$("input[name=isOtherSpec]:checked").val() ) {
		if( $("input[name=otherSpecRemark]").val() == '' ) {
			alert('附件之其他欄位勾選時註記必須填寫');
			return false;
		}
	}
	if ($("input[name=otherSpecRemark]").val() != '' ) {
		if( !$("input[name=isOtherSpec]").prop("checked") ) {
			alert('有填寫註記時需勾選附件之其他欄位');
			return false;
		}
	}
	return true;
}

$( document ).ready(function() {
	if ( $("#state").val() == "init" )
		$("#openPre1005").hide();
	else 
		$("#openPre1005").show();
	setDisplayItem("spanClear,spanUpdate,spanDelete,spanListPrint,spanListHidden","H");
	//收件日期-預設系統時間
	$('#receiveDate').val(commonUtils.getTWDate(true));
	//
	try {
		$("#telixNo").focus();
	} catch(e){}

	if($("#ERRMSG").text().indexOf('一維條碼收文存檔成功') != -1 || $("#ERRMSG").text().indexOf('紙本收文存檔成功') != -1) {
		$("#openPre1005").show();
		printPre1005();
	}

	//查詢 待分文件數 與 現況取號最大號
	ajaxGetOnLineCnt();
	ajaxGetCurrPrefixNo();
	setInterval(function() {
		ajaxGetOnLineCnt();
		ajaxGetCurrPrefixNo();
	}, 10000);
	//取號存檔
	$("#getNoAndSave").click(function() {
<%--
		// 103/10/20 依問題單 TCFI-UT-PRE1001-03 修改程式
		// 取號存檔按下後, 游標依然會固定在網路收文號欄位
		// 103/10/30 優仲表示errorMsg跳不出來, 因此調整了本方法
--%>
		$("#telixNo").focus();
    	$("#openPre1005").hide();
		var msg = "" ;
		var isValidate = true;
		if( isValidate && (!$('#setup').attr('checked') && !$('#changeName').attr('checked') && !$('#changeItem').attr('checked')) ) {
	    	msg ="未選擇預查種類";
	    	isValidate = false;
		}
		if( isValidate && (!$("input[name=contactGetKind]").is(':checked')) ) {
	    	msg = "未選擇領件方式";
	    	isValidate = false;
		}
		if( isValidate && ( $("input[name=contactGetKind]:checked").val() == 2 && $("input[name=getKindRemark]").val() == '' ) ) {
			msg = "未填寫郵寄註記";
	    	isValidate = false;
		}
		if (!chkAttachment()) {
			return;
		}
		
		showMsgBar(msg);
		if(!isValidate) {
			return false;
		}

		if( "" == $("#telixNo").val()) {
			if(!confirm("無網路收文號，請確定是否為空白表單收文")) {
				return;
			}
		}
		
		if ("" == $("#telixNo").val() && "" == $("#receiptNo").val()) {
			alert("空白表單收文必須填收據號");
			return;
		}

		if(isValidate && form1.contactGetKind.length > 0) {
			if( $('#printAddressTag').attr('checked') ) {
				form1.action = 'pre1001_00.jsp?printAddressTag=Y';
			} else {
				form1.action = 'pre1001_00.jsp';
			}
			form1.state.value = 'getNoAndSave';
	   		form1.submit();

	   		disableOnline();
	    } 
	});
   	//列印回執聯
    $("#printReceiptBtn").click(function() {
    	var prop = "width=640,height=200,scrollbars=0,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
    	window.open(getVirtualPath() + "tcfi/pre/pre1005.jsp?shortcut=Y","pre1005",prop);
  	}); 
   	//列印申請表
    $("#printApplyBtn").click(function() {
    	var prop = "width=640,height=200,scrollbars=0,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
    	window.open(getVirtualPath() + "tcfi/pre/pre1004.jsp?shortcut=Y","pre1004",prop);
  	});
   	//線上收文
    $("#onLineCntBtn").click(function() {
    	// 103/10/20 依問題單 TCFI-UT-PRE1001-03 修改程式
		// 取號存檔按下後, 游標依然會固定在網路收文號欄位
    	$("#telixNo").focus();
    	$("#openPre1005").hide();
		if($("#onLineCnt").val() == 0 || $("#onLineCnt").val() == '') {
			alert("目前尚無待收文的線上申辦案件");
			return;
		} else {
			var target = "pre1001_01";
	    	var prop = "top=0,left=0,width=1024,height=680,scrollbars=0,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
	    	window.open("",target,prop);
	    	form1.state.value = "init";
	    	form1.target = target;
	    	form1.action = "pre1001_01.jsp";
			form1.submit();
	    	showMsgBar("");
	   		disableOnline();
	    	form1.state.value = "";
			form1.target = "";
	    	form1.action = "pre1001_00.jsp";
		}
	});
	//預查種類-設立
    $('#setup').click(function(){
    	if(this.checked) {
    		$('#changeName').removeAttr('checked');
    		$('#changeItem').removeAttr('checked');
    	}
    });
	//預查種類-名稱變更
    $('#changeName').click(function(){
    	if(this.checked) {
    		$('#setup').removeAttr('checked');
    	}
    });
	//預查種類-所營變更
    $('#changeItem').click(function(){
    	if(this.checked) {
    		$('#setup').removeAttr('checked');
    	}
    });
    //網路收文號
    $("#telixNo").on('textchange', function(e) {
		if($(this).val().length == 11) {
			e.preventDefault();
			$("#openPre1005").hide();
			// 103/10/30 依使用者要求網路收文號字首均改為大寫
			var firstChar = $(this).val()[0].toUpperCase();
			var restChars = $(this).val().substring(1);
		    firstChar += restChars;
			$("#telixNo").val(firstChar);
			$.post( getVirtualPath() + "tcfi/ajax/jsonEedb1000.jsp?q=" + $(this).val(), function( data ) {
				if (data != null) {
					$("#banNo").val(data.BAN_NO);
					$("#oldCompanyName").val(data.OLD_COMPANY_NAME);
					$("#companyAddr").val(data.COMPANY_ADDR);
					$("#applyName").val(data.APPLY_NAME);
					$("#applyId").val(data.APPLY_ID);
					$('#applyTel').val(data.CONTACT_TEL);
					$("#attorAddr").val(data.ATTOR_ADDR);
					$("#attorTel").val(data.ATTOR_TEL);
					$("#attorNo").val(data.ATTOR_NO);
					$("#attorName").val(data.ATTOR_NAME);
					$("#contactName").val(data.CONTACT_NAME);
					$("#contactAddr").val(data.CONTACT_ADDR);
					$("#contactTel").val(data.CONTACT_TEL);
					$("#contactCel").val(data.CONTACT_CEL);
					$("#closed").val(data.CLOSED);
					$("#orgType").val(data.ORGN_TYPE);
					syncChangeTypeCheckbox(data.CHANGE_TYPE);
					if( '1' == data.CONTACT_GET_KIND ) {
						$("input[name=contactGetKind][value=1]").attr('checked',true);
						$("input[name=contactGetKind][value=2]").removeAttr('checked');
						$("input[name=contactGetKind][value=3]").removeAttr('checked');//2024/03/17 新增線上列印
					} else if( '2' == data.CONTACT_GET_KIND ) {
						$("input[name=contactGetKind][value=1]").removeAttr('checked');
						$("input[name=contactGetKind][value=2]").attr('checked',true);
						$("input[name=contactGetKind][value=3]").removeAttr('checked');//2024/03/17 新增線上列印
					} else if( '3' == data.CONTACT_GET_KIND ) {//2024/03/17 新增線上列印 
						$("input[name=contactGetKind][value=1]").removeAttr('checked');//2024/03/17 新增線上列印
						$("input[name=contactGetKind][value=2]").removeAttr('checked');//2024/03/17 新增線上列印
						$("input[name=contactGetKind][value=3]").attr('checked',true);//2024/03/17 新增線上列印
					} else {
						$("input[name=contactGetKind][value=1]").removeAttr('checked');
						$("input[name=contactGetKind][value=2]").removeAttr('checked');
						$("input[name=contactGetKind][value=3]").removeAttr('checked');//2024/03/17 新增線上列印
					}
					if( $("#applyName").val() == $("#contactName").val() ) {
						if( "" == $('#receiveId').val() ) {
							$('#receiveId').val(data.APPLY_ID);
						}
					}
<%-- ZZ99999免繳預查審查費已隨107.11.08修訂之公司登記規費收費準則自107.11.01廢除.HB@20190320
					var changeType = "-1";
					if ( $("#setup").attr("checked") && !$("#changeName").attr("checked") && !$("#changeItem").attr("checked") )
						changeType = "0";
					else if ( !$("#setup").attr("checked") && $("#changeName").attr("checked") && !$("#changeItem").attr("checked") )
						changeType = "1";
					else if ( !$("#setup").attr("checked") && !$("#changeName").attr("checked") && $("#changeItem").attr("checked") )
						changeType = "2";
					else if ( !$("#setup").attr("checked") && $("#changeName").attr("checked") && $("#changeItem").attr("checked") )
						changeType = "3";
					
					var x = getRemoteData(getVirtualPath() + "tcfi/ajax/jsonPre1001ChkNoNeedPay.jsp?telixNo="+$("#telixNo").val() 
						+ "&changeType=" + changeType + "&banNo=" + $("#banNo").val());
					
					var isNoNeedPay = "";
					if ( x == "true" ) {
						isNoNeedPay = "本案免繳審查費";
						alert(isNoNeedPay);
					} // if
					showMsgBar(isNoNeedPay);
--%>
				} // if
			});
			
			ajaxLoadCompanyNames($(this).val());

			$.post( getVirtualPath() + "tcfi/ajax/jsonEedb3300.jsp?q=" + $(this).val(), function( orgCorp ) {
				if (orgCorp != null) {
					$("#applyLawName").val(orgCorp.ORG_CORP_NAME);
					$("#applyBanNo").val(orgCorp.ORG_CORP_NO);				
				} // if
			});
			
		} else {
			//reset
			resetForm();
		}
	});
	//列印回執聯
	$('#openPre1005').click(function() {
		printPre1005();
	});
});
</SCRIPT>
<style>
#iframe{
  height: 580px;
  width: 100%;
}
</style>
</head>
<body>
<form id="form1" name="form1" method="post" action="#">
<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1001'/>
</c:import>

<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td>
		<%if (staffUnit.equals("六科")) {%>
			<input type="button" class="toolbar_default" id="onLineCntBtn" name="onLineCntBtn" value="線上收文" disabled/>
		<%} else { %>
			<input type="button" class="toolbar_default" id="onLineCntBtn" name="onLineCntBtn" value="線上收文" />
		<%}%>
			<input type="text" style="font-size:18px;font-weight: bold" class="field_RO" id="onLineCnt" name="onLineCnt" size="5" readonly value=""> 筆
		</td>
		<td id="spanDoConfirm">
			<input type="button" class="toolbar_default" id="printApplyBtn" name="printApply" value="列印申請表" onClick="whatButtonFireEvent(this.name)" />&nbsp;
		</td>
		<td id="spanDoConfirm">
			<input type="button" class="toolbar_default" id="printReceiptBtn" name="printReceipt" value="列印回執聯" />&nbsp;
		</td>
		<td style="text-align: right;padding-right:15px;">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value='PRE1006,PRE4001,PRE1003,1001OnlyCmp,1001OnlyLms'/>
			</c:import>
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" onclick="javascript:top.close();" />&nbsp;
		</td>
	</tr>
</table>

<table class="table_form" width="100%" cellspacing="0" cellpadding="2">
	<tr>
		<td class="td_form" width="100px">預查編號</td>
		<td class="td_form_white" width="120">
		<!-- 103/10/30 依優仲轉述使用者之要求, 將預查編號之字體條大, 並改成粗體 -->
			<input type="text" style="font-size:18px;font-weight: bold" class="field_RO" id="prefixNo" name="prefixNo" size="20" readonly value="<%=obj.getPrefixNo()%>" />
		</td>
		<td class="td_form" width="90px"><font color="red">*</font>預查種類</td>
		<td class="td_form_white" colspan="3">
			<input type="checkbox" id="setup" name="setup" value="Y" >設立
			<input type="checkbox" id="changeName" name="changeName" value="Y" >名稱變更
			<input type="checkbox" id="changeItem" name="changeItem" value="Y" >所營變更
		</td>
	</tr>
	<tr>
		<td class="td_form">網路收文號</td>
		<td class="td_form_white">
		<!--  103/10/30 依使用者要求將網路收文號固定為11碼 -->
			<input type="text" class="field" id="telixNo" name="telixNo" size="20" maxLength="11" value="" />
			<input type="button" class="toolbar_default" id="getNoAndSave" name="getNoAndSave" value="取號存檔">
			<input type="checkbox" id="printAddressTag" name="printAddressTag" value="Y" />地址條
		</td>
		<td class="td_form">原名稱</td>
		<td class="td_form_white">
			<input type="text" class="field_RO cmex" id="oldCompanyName" name="oldCompanyName" readonly size="30" value="">
		</td>
		<td class="td_form" width="140px">統一編號</td>
		<td class="td_form_white" width="140px">
			<input type="text" class="field_RO" id="banNo" name="banNo" size="10" value="" readonly />
		</td>
	</tr>
	<tr>
		<td class="td_form">*領件方式</td>
		<td class="td_form_white" colspan="3">
		    <input type="radio" name="contactGetKind" valus="3">線上列印  <!--2024/03/17 新增線上列印 -->
			<input type="radio" name="contactGetKind" value="1">自取
			<input type="radio" name="contactGetKind" value="2">郵寄
			<input type="text" class="field" name="getKindRemark" size="6" maxlength="10" value="">
		</td>
		<td class="td_form">收件日期</td>
		<td class="td_form_white">
			<input type="text" class="field_RO" id="receiveDate" name="receiveDate" size="20" readonly value="">
		</td>
	</tr>
	<tr>
		<td class="td_form">收據號</td>
		<td class="td_form_white" colspan="5">
			<input type="text" class="field" id="receiptNo" name="receiptNo" size="20">
		</td>
	</tr>
	<tr>
		<td class="td_form">附件</td>
		<td class="td_form_white" colspan="5">
			<input type="checkbox" id="isPrefixForm" name="isPrefixForm" value="Y" />
			<select class="field" id="docType" name="docType">
				<option value="1">預查表正本</option>
				<!-- 103/10/30 依優仲轉述之使用者要求將 預查表影本 自下拉式選單中移除 -->
				<option value="3">線上申辦檢還申請書</option>
			</select>
			<input type="text" class="field" id="prefixFormNo" name="prefixFormNo" size="9" maxlength="9" value=""
				onkeyup="if(this.value!=''){$('#isPrefixForm').attr('checked',true);}else{$('#isPrefixForm').removeAttr('checked');};" />
			<input type="checkbox" id="isOtherForm" name="isOtherForm" value="Y" />其他機關核准函
			<input type="checkbox" id="isSpec" name="isSpec" value="Y" />說明書
			<input type="checkbox" id="isOtherSpec" name="isOtherSpec" value="Y" />其他
			<input type="text" class="field" id="otherSpecRemark" name="otherSpecRemark" size="20" maxlength="500" value=""
				onkeyup="if(this.value!=''){$('#isOtherSpec').attr('checked',true);}else{$('#isOtherSpec').removeAttr('checked');};" />
		</td>
	</tr>
	<tr>
		<td class="td_form">*申請人姓名</td>
		<td class="td_form_white">
			<input type="text" class="field cmex" id="applyName" name="applyName" size="25" value="" />
		</td>
		<td class="td_form">*身分ID</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="applyId" name="applyId" size="24" maxlength="18" value="" />
		</td>
	</tr>
	<tr>
		<td class="td_form">所代表法人</td>
		<td class="td_form_white">
			<input type="text" class="field" id="applyLawName" name="applyLawName" size="25" value="" />
		</td>
		<td class="td_form">法人統編</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="applyBanNo" name="applyBanNo" size="24" maxlength="18" value="" />
		</td>
	</tr>
	<tr>
		<td class="td_form">*申請人地址</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="companyAddr" name="companyAddr" size="67" value="">
			<input type="button" class="openAddresser" value="地址" style="display:none;" />
		</td>
		<td class="td_form">*申請人電話</td>
		<td class="td_form_white">
			<input type="text" class="field" id="applyTel" name="applyTel" size="18" value="" />
		</td>
	</tr>
	<tr>
		<td class="td_form">代理人姓名</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field cmex" id="attorName" name="attorName" size="25" value="" />
		</td>
		<td class="td_form">代理人證書號碼</td>
		<td class="td_form_white">
			<input type="text" class="field" id="attorNo" name="attorNo" size="18" value="" />
		</td>
	</tr>
	<tr>
		<td class="td_form">事務所所在地</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="attorAddr" name="attorAddr" size="67" value="" />
			<input type="button" class="openAddresser" value="地址" style="display:none;" />
		</td>
		<td class="td_form">代理人電話</td>
		<td class="td_form_white">
			<input type="text" class="field" id="attorTel" name="attorTel" size="18" value="">
		</td>
	</tr>
	<tr>
		<td class="td_form">*收件人姓名</td>
		<td class="td_form_white" colspan="5">
			<input type="text" class="field cmex" id="contactName" name="contactName" size="25" value="">
			<input type="hidden" class="field" id="receiveId" name="receiveId" size="24" maxlength="18" value="">
		</td>
	</tr>
	<tr>
		<td class="td_form">*聯絡地址</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="contactAddr" name="contactAddr" size="67" value="">
			<input type="button" class="toolbar_default" style="display:''" id="openPre1005" name="openPre1005" value='補列印回執聯' >
		</td>
		<td class="td_form">*簡訊回覆通知電話</td>
		<td class="td_form_white">
			<input type="text" class="field" id="contactCel" name="contactCel" size="18" value="">
		</td>
	</tr>
</table>
		
<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>

<table style="width: 400px" border="1" id="companyNames">
	<thead>
		<tr>
			<th class="thead" width="50px">序號</th>
			<th class="thead">新公司名稱</th>
		</tr>
	</thead>
	<tbody>
	</tbody>
</table>

<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">

<tr><td style="text-align:left;">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="closed" name="closed" value="" />
<input type="hidden" id="orgType" name="orgType" value=""/>

<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
</table>

</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>