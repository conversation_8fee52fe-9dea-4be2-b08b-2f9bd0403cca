<!DOCTYPE html>
<%
/**
程式目的：有限合夥基本資料查詢
程式代號：pre3013
程式日期：1051012
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3013" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3013">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3013"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function init() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				document.getElementById("state").value = "queryAll";
				break;
			case "doClose":
				window.close();
				break;
			case "doReset":
				form1.reset();
				document.getElementById("q_banNo").value = "";
				document.getElementById("q_companyName").value = "";
				document.getElementById("q_prefixNo").value = "";
				document.getElementById("q_specialName").value = "";
				document.getElementById("q_respName").value = "";
				document.getElementById("q_respIdNo").value = "";
				break;
			case "checkAll":
				commonUtils.all("banNos");
				break;
			case "disAll":
				commonUtils.unAll("banNos");
				break;
			case "sendData":
				var $checks = $("input[name=banNos]").filter(":checked");
				if( $checks.size() > 0) {
					form1.state.value = "init";
					form1.action = getVirtualPath() + "tcfi/pre/pre3013_00.jsp";
					$.cookie("activeTabIndex", 0);
					form1.submit();
				} else {
					alert("請至少選取一筆案件!!");	
				}
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};

	//有執行查詢的時候, 才顯示清單區
	if($('#state').val() != "init") {
		$('#listContainer').show();
	}
	//查出資料只有一筆時, 自動顯示明細
	if( $("#resultTable tr").size() == 2 ) {
		$("input[type=checkbox]").attr("checked", true);
		$("#sendData").trigger("click");
	}
}

function checkField(){
	var alertStr="";
	alertStr += checkQuery();
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function queryOne(banNo) {
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;
    var comefrom = document.getElementById("comefrom").value;
	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=1,resizable=1";
	var url = getVirtualPath() + "tcfi/pre/pre3013_00.jsp?banNos="+banNo+"&comefrom="+comefrom;
	window.open(url,'pre3013Detail',prop);
}

var e;
function defaultQuery() {
	if (!e) e = window.event;
	var keyCode = e.keyCode || e.which;
    if (keyCode == '13') {
    	document.getElementById("doQueryAll").click();
    }
}
</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3013'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- Form area -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="bg_toolbar" width="100%" height="100%">
		<tr>
			<td>
				<input class="toolbar_default" type="button" id="doReset" name="doReset" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				<input class="toolbar_default" type="submit" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
			</td>
			<td colspan="3" style="text-align:right;">
				<input class="toolbar_default" type="button" id="doClose" name="doClose" value="離　開" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>  
	</table>
	<table class="table_form" width="100%" height="100%">
		<tr>
			<td class="td_form" width="15%">統一編號：</td>
			<td class="td_form_white" width="20%"> 
				<input class="field_Q" type="text" id="q_banNo" name="q_banNo" size="10" maxlength="8" value="<%=obj.getQ_banNo()%>" onkeydown="defaultQuery();" />
			</td>
        	<td class="td_form" width="15%">有限合夥名稱：</td>
        	<td class="td_form_white" width="50%"> 
           		<input class="field_Q" type="text" id="q_companyName" name="q_companyName" size="50" maxlength="50" value="<%=obj.getQ_companyName()%>" onkeydown="defaultQuery();" />
        	</td>
		</tr>
		<tr>
		    <td class="td_form">預查編號：</td>
		    <td class="td_form_white"> 
				<input class="field_Q" type="text" id="q_prefixNo" name="q_prefixNo" size="10" maxlength="9" value="<%=obj.getQ_prefixNo()%>" onkeydown="defaultQuery();" />
			</td>
			<td class="td_form">特取名稱：</td>
			<td class="td_form_white"> 
				<input class="field_Q cmex" type="text" id="q_specialName" name="q_specialName" size="20" maxlength="50" value="<%=obj.getQ_specialName()%>" onkeydown="defaultQuery();" />
			</td>
		</tr>
		<tr>
		    <td class="td_form">負責人姓名：</td>
		    <td class="td_form_white"> 
				<input class="field_Q cmex" type="text" id="q_respName" name="q_respName" size="10" maxlength="10" value="<%=obj.getQ_respName()%>" onkeydown="defaultQuery();" />
			</td>
			<td class="td_form">負責人身分ID：</td>
			<td class="td_form_white"> 
				<input class="field_Q" type="text" id="q_respIdNo" name="q_respIdNo" size="20" maxlength="50" value="<%=obj.getQ_respIdNo()%>" onkeydown="defaultQuery();" />
			</td>
		</tr>	 		
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
	<div id="listContainer" style="display:none;">
	<table class="table_form">
		<tr><td>
		<input class="toolbar_default" type="button" id="checkAll" name="checkAll" value="全部選取" onClick="whatButtonFireEvent(this.name)" />&nbsp;
		<input class="toolbar_default" type="button" id="disAll" name="disAll" value="取消選取" onClick="whatButtonFireEvent(this.name)" />&nbsp;
		<input class="toolbar_default" type="button" id="sendData" name="sendData" value="確認送出" onClick="whatButtonFireEvent(this.name)" />
		</td></tr>
		<tr><td class="tab_line1"></td></tr>
		<tr><td class="tab_line1"></td></tr>
	</table>
	<table id="resultTable" class="table_form" width="100%" cellspacing="0" cellpadding="0">
 		<thead id="listTHEAD">
  			<tr>
			    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">NO.</a></th>
			    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">統一編號</a></th>
			    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查編號</a></th>
			    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">負責人</a></th>
			    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">有限合夥名稱</a></th>
			    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">特取名稱</a></th>
			    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">有限合夥狀態</a></th>    
  			</tr>
  		</thead>
		<tbody id="listTBODY">
		  <%
		  boolean primaryArray[] = {true,false,false,false,false,false};
		  boolean displayArray[] = {true,true,true,true,true,true};
		  String[] alignArray = {"center","center","left","left","left","left"};
		  out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),"banNos"));
		  %>
		</tbody>
	</table>
	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2">
		<tr><td style="text-align:left;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<input type="hidden" id="comefrom" name="comefrom" value="<%=obj.getComefrom()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>