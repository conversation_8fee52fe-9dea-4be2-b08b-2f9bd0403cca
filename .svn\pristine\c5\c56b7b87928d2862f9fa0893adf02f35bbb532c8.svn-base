package com.kangdainfo.tcfi.util;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc053;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc054;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao;

public class AreaCodeOptionHelper {
	public AreaCodeOptionHelper() {
	}

	/**
	 * 透過 areaCode 查詢 areaName 並回傳
	 * @param areaCode
	 * @return
	 */
	public static String getAreaZoneName(String areaCode) {
		try {
			Cedbc054Dao cedbc054Dao = ServiceGetter.getInstance().getPrefixService().getCedbc054Dao();
			Cedbc054 c054 = new Cedbc054();
			if (areaCode.length() == 2 && (areaCode.equals("63") || areaCode.equals("64"))) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "00000000");
				return c054 == null ? "" : c054.getAreaName();
			} else if (areaCode.length() == 5 && (areaCode.substring(0, 1).equals("63") || areaCode.substring(0, 1).equals("64"))) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "00000");
				return c054 == null ? "" : c054.getAreaName();
			} else if (areaCode.length() == 7) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "000");
				return c054 == null ? "" : c054.getAreaName();
			} else {
				c054 = cedbc054Dao.findByAreaCode(areaCode);
				return c054 == null ? "" : c054.getAreaName();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 透過國藉代碼取得國藉名稱
	 * @param countryCode String 國藉代碼
	 * @return String 國藉名稱
	 */
	public static String getCountryName(String countryCode) {
		try {
			Cedbc053Dao cedbc053Dao = new Cedbc053Dao();
			Cedbc053 c053 = new Cedbc053();
			if (countryCode != null && !countryCode.equals("")) {
				c053 = cedbc053Dao.findByCountryCode(countryCode);
				return c053 == null ? "" : c053.getCountry();
			}
		} catch (Exception me) {
			me.printStackTrace();
		}
		return null;
	}
}