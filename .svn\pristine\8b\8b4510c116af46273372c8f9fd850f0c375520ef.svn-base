package com.kangdainfo.tcfi.loader;

/**
 * 公司狀態
 *
 */
public class SystemCode10Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_10";
	private static final String CODE_KIND = "10";//10:公司狀態

	//singleton
	private static SystemCode10Loader instance;
	public SystemCode10Loader() {
		if (SystemCode10Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode10Loader.instance);
		}
		SystemCode10Loader.instance = this;
	}
	public static SystemCode10Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}