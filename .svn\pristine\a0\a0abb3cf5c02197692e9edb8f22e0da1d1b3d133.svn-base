package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1100;

public class Cedb1100Dao extends BaseDaoJdbc implements RowMapper<Cedb1100> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1100 WHERE PREFIX_NO = ?";
	public Cedb1100 findByPrefixNo(String prefixNo, String approveResult) {
		if(null==prefixNo || "".equals(prefixNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1100> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : (Cedb1100) list.get(0) ;
	}

	private static final String SQL_findByTelixNo = "SELECT * FROM CEDB1100 WHERE TELIX_NO = ?";
	public Cedb1100 findByTelixNo(String telixNo) {
		if(null==telixNo || "".equals(telixNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1100> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : (Cedb1100) list.get(0);
	}
	
	private static final String SQL_findByBanNo = "SELECT * FROM CEDB1100 WHERE BAN_NO = ?";
	public List<Cedb1100> findByBanNo(String banNo, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findByBanNo);
		sqljob.addParameter(banNo);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findBetween = "SELECT * FROM CEDB1100 WHERE (PREFIX_NO BETWEEN ? AND ?)";
	public List<Cedb1100> doReadBetweenPrefixNo(String start, String end, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findBetween);
		sqljob.addParameter(start);
		sqljob.addParameter(end);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}

		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findBetweenBanNo = "SELECT * FROM CEDB1100 WHERE (BAN_NO BETWEEN ? AND ?)";
	public List<Cedb1100> doReadBetweenBanNo(String start, String end, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findBetweenBanNo);
		sqljob.addParameter(start);
		sqljob.addParameter(end);

		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_ReadAllByApplyId = "SELECT * FROM CEDB1100 WHERE APPLY_ID = ?";
	public List<Cedb1100> doReadAllByApplyId(String applyId) {
		SQLJob sqljob = new SQLJob(SQL_ReadAllByApplyId);
		sqljob.addParameter(applyId);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_ReadAllByApplyName = "SELECT * FROM CEDB1100 WHERE APPLY_NAME = ?";
	public List<Cedb1100> doReadAllByApplyName(String applyName, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_ReadAllByApplyName);
		sqljob.addParameter(applyName);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_ReadLikeCompanyName = "SELECT * FROM CEDB1100 WHERE COMPANY_NAME LIKE ?";
	public List<Cedb1100> doReadLikeCompanyName(String companyName, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_ReadLikeCompanyName);
		sqljob.addLikeParameter(companyName);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_ReadLikeTelixNo = "SELECT * FROM CEDB1100 WHERE Telix_No = ?";
	public List<Cedb1100> doReadAllByTelixNo(String telixNo, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_ReadLikeTelixNo);
		sqljob.addParameter(telixNo);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
			//sqljob.addParameter(approveResult);
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public int insert(Cedb1100 obj) {
		if(obj == null)	return 0;
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1100 (");
		sqljob.appendSQL("PREFIX_NO,BAN_NO,APPLY_KIND,APPLY_NAME,APPLY_ID,");//5
		sqljob.appendSQL("APPLY_ADDR,APPLY_TEL,ATTOR_NAME,ATTOR_NO,ATTOR_ADDR,");//10
		sqljob.appendSQL("ATTOR_TEL,GET_KIND,APPLY_TYPE,COMPANY_NAME,TELIX_NO,");//15
		sqljob.appendSQL("RECEIVE_DATE,RECEIVE_TIME,APPROVE_DATE,APPROVE_TIME,APPROVE_RESULT,");//20
		sqljob.appendSQL("RESERVE_MARK,RESERVE_DATE,GET_DATE,GET_TIME,SPECIAL_NAME,");//25
		sqljob.appendSQL("COMPANY_STUS,REG_UNIT,REMARK,ASSIGN_DATE,ASSIGN_TIME,");//30
		sqljob.appendSQL("ID_NO,STAFF_NAME,UPDATE_CODE,CODE_NO,CODE_NAME,");//35
		sqljob.appendSQL("UPDATE_ID_NO,UPDATE_DATE,UPDATE_TIME,REG_DATE,CONTROL_CD1,");//40
		sqljob.appendSQL("CONTROL_CD2,ZONE_CODE,APPROVE_MARK,CLOSE_DATE,CLOSE_TIME,");//45
		sqljob.appendSQL("WORK_DAY,REMARK1,OLD_COMPANY_NAME,PREFIX_STATUS,RESERVE_DAYS,");//50
		sqljob.appendSQL("REFUND_NO,EXTEND_REASON,EXTEND_OTHER,EXTEND_DATE,OTHER_REASON");//55
		sqljob.appendSQL(")VALUES(");
		sqljob.appendSQL("?,?,?,?,?,");//5
		sqljob.appendSQL("?,?,?,?,?,");//10
		sqljob.appendSQL("?,?,?,?,?,");//15
		sqljob.appendSQL("?,?,?,?,?,");//20
		sqljob.appendSQL("?,?,?,?,?,");//25
		sqljob.appendSQL("?,?,?,?,?,");//30
		sqljob.appendSQL("?,?,?,?,?,");//35
		sqljob.appendSQL("?,?,?,?,?,");//40
		sqljob.appendSQL("?,?,?,?,?,");//45
		sqljob.appendSQL("?,?,?,?,?,");//50
		sqljob.appendSQL("?,?,?,?,?");//55
		sqljob.appendSQL(")");
		//1
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyId());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//6
		sqljob.addParameter(obj.getApplyAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAttorName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAttorNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAttorAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//11
		sqljob.addParameter(obj.getAttorTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//16
		sqljob.addParameter(obj.getReceiveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getReceiveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//21
		sqljob.addParameter(obj.getReserveMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getReserveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSpecialName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//26
		sqljob.addParameter(obj.getCompanyStus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRegUnit());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAssignDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAssignTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//31
		sqljob.addParameter(obj.getIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getStaffName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCodeNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCodeName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//36
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRegDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getControlCd1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//41
		sqljob.addParameter(obj.getControlCd2());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getZoneCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCloseDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCloseTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//46
		sqljob.addParameter(obj.getWorkDay());
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		sqljob.addParameter(obj.getRemark1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getOldCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getReserveDays());
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		//51
		sqljob.addParameter(obj.getRefundNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getExtendReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getExtendOther());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getExtendDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getOtherReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1100 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1100 obj = null;
		if(null!=rs) {
			obj = new Cedb1100();
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setApplyKind(Common.get(rs.getString("APPLY_KIND")));
			obj.setApplyName(Common.get(rs.getString("APPLY_NAME")));
			obj.setApplyId(Common.get(rs.getString("APPLY_ID")));
			obj.setApplyAddr(Common.get(rs.getString("APPLY_ADDR")));
			obj.setApplyTel(Common.get(rs.getString("APPLY_TEL")));
			obj.setAttorName(Common.get(rs.getString("ATTOR_NAME")));
			obj.setAttorNo(Common.get(rs.getString("ATTOR_NO")));
			obj.setAttorAddr(Common.get(rs.getString("ATTOR_ADDR")));
			obj.setAttorTel(Common.get(rs.getString("ATTOR_TEL")));
			obj.setGetKind(Common.get(rs.getString("GET_KIND")));
			obj.setApplyType(Common.get(rs.getString("APPLY_TYPE")));
			obj.setCompanyName(Common.get(rs.getString("COMPANY_NAME")));
			obj.setTelixNo(Common.get(rs.getString("TELIX_NO")));
			obj.setReceiveDate(Common.get(rs.getString("RECEIVE_DATE")));
			obj.setReceiveTime(Common.get(rs.getString("RECEIVE_TIME")));
			obj.setApproveDate(Common.get(rs.getString("APPROVE_DATE")));
			obj.setApproveTime(Common.get(rs.getString("APPROVE_TIME")));
			obj.setApproveResult(Common.get(rs.getString("APPROVE_RESULT")));
			obj.setReserveMark(Common.get(rs.getString("RESERVE_MARK")));
			obj.setReserveDate(Common.get(rs.getString("RESERVE_DATE")));
			obj.setGetDate(Common.get(rs.getString("GET_DATE")));
			obj.setGetTime(Common.get(rs.getString("GET_TIME")));
			obj.setSpecialName(Common.get(rs.getString("SPECIAL_NAME")));
			obj.setCompanyStus(Common.get(rs.getString("COMPANY_STUS")));
			obj.setRegUnit(Common.get(rs.getString("REG_UNIT")));
			obj.setRemark(Common.get(rs.getString("REMARK")));
			obj.setAssignDate(Common.get(rs.getString("ASSIGN_DATE")));
			obj.setAssignTime(Common.get(rs.getString("ASSIGN_TIME")));
			obj.setIdNo(Common.get(rs.getString("ID_NO")));
			obj.setStaffName(Common.get(rs.getString("STAFF_NAME")));
			obj.setUpdateCode(Common.get(rs.getString("UPDATE_CODE")));
			obj.setCodeNo(Common.get(rs.getString("CODE_NO")));
			obj.setCodeName(Common.get(rs.getString("CODE_NAME")));
			obj.setUpdateIdNo(Common.get(rs.getString("UPDATE_ID_NO")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateTime(Common.get(rs.getString("UPDATE_TIME")));
			obj.setRegDate(Common.get(rs.getString("REG_DATE")));
			obj.setControlCd1(Common.get(rs.getString("CONTROL_CD1")));
			obj.setControlCd2(Common.get(rs.getString("CONTROL_CD2")));
			obj.setZoneCode(Common.get(rs.getString("ZONE_CODE")));
			obj.setApproveMark(Common.get(rs.getString("APPROVE_MARK")));
			obj.setCloseDate(Common.get(rs.getString("CLOSE_DATE")));
			obj.setCloseTime(Common.get(rs.getString("CLOSE_TIME")));
			obj.setWorkDay((rs.getFloat("WORK_DAY")));
			obj.setRemark1(Common.get(rs.getString("REMARK1")));
			obj.setOldCompanyName(Common.get(rs.getString("OLD_COMPANY_NAME")));
			obj.setPrefixStatus(Common.get(rs.getString("PREFIX_STATUS")));
			obj.setReserveDays(rs.getInt("RESERVE_DAYS"));

			obj.setRefundNo(Common.get(rs.getString("REFUND_NO")));
			obj.setExtendReason(Common.get(rs.getString("EXTEND_REASON")));
			obj.setExtendOther(Common.get(rs.getString("EXTEND_OTHER")));
			obj.setExtendDate(Common.get(rs.getString("EXTEND_DATE")));
			obj.setOtherReason(Common.get(rs.getString("OTHER_REASON")));
		}
		return obj;
	}

}