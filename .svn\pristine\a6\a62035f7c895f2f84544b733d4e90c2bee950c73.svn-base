package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;

import com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao;

public interface Pre8005Service {
	public Cedbc000Dao getCedbc000Dao();
	public void setCedbc000Dao(Cedbc000Dao dao);
	public Cedb1000Dao getCedb1000Dao();
	public void setCedb1000Dao(Cedb1000Dao dao);
	public Cedb1010Dao getCedb1010Dao();
	public void setCedb1010Dao(Cedb1010Dao dao);
	public void doSave( String[] checkedPrefixNo, String[] seqNo, String[] idNo, String userId ) throws Exception ;
}