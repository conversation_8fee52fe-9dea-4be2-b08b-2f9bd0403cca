<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/home/<USER>"%>
<%
if ("Y".equals(com.kangdainfo.common.util.Common.get(request.getParameter("logout")))) {
	session.invalidate();
}
%>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="shortcut icon" href="<c:url value="/images/pre/gcis.ico"/>">
<title><%=application.getServletContextName()%></title>
<style type="text/css">
<!--
body {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
}
-->
</style>
<script type="text/javascript">
function checkField(){
	if (document.getElementById("userID").value==""){ 
		alert("請輸入使用者帳號");
		return false; 
	}	
	if (document.getElementById("userPWD").value==""){ 
		alert("請輸入使用者密碼");
		return false;
	}	
	form1.state.value = "submit";
	form1.submit();
}

function init() {
	form1.userID.focus();
<% if( "test".equals(com.kangdainfo.ServiceGetter.getInstance().getEnv()) ) { %>
	form1.userID.value = "B123456789";
	form1.userPWD.value = "1qaz1234";
<% } else if( "local".equals(com.kangdainfo.ServiceGetter.getInstance().getEnv()) ) { %>
	form1.userID.value = "H123456789";
	form1.userPWD.value = "12345678uhb";
<% } %>
}
</script>
<script type="text/javascript" src="js/function.js"></script>
</head>
<body onLoad="init();">
<form method="post" name="form1" action="preAuth.jsp" onSubmit="return checkField()" autocomplete="off" target="_top">
<input type="hidden" name="isSSO" value="N"/>
<input type="hidden" name="state" />
<table width="900" border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td colspan="3"><img src="<c:url value="/images/index/login_01.png"/>" width="900" height="410" alt="" /></td>
	</tr>
	<tr>
		<td rowspan="4"><img src="<c:url value="/images/index/login_02.png"/>" width="558" height="190" alt="" /></td>
		<td width="281" height="33">
			<input name="userID" id="userID" style="FONT-SIZE: 16pt; width: 220px;" tabindex="1" value="" size="30" maxlength="100">
		</td>
		<td rowspan="4"><img src="<c:url value="/images/index/login_04.png"/>" width="61" height="190" alt="" /></td>
	</tr>
	<tr>
		<td>
			<img src="<c:url value="/images/index/login_05.png"/>" width="281" height="14" alt="" /></td>
	</tr>
	<tr>
		<td width="281" height="35">
			<input type="password" style="FONT-SIZE: 16pt; width: 220px;" maxlength="12" size="23" name="userPWD" id="userPWD" tabindex="2" value=""/>
		</td>
	</tr>
	<tr>
		<td><img src="<c:url value="/images/index/login_07.png"/>" alt="" width="281" height="108" usemap="#loginMap" border="0" /></td>
	</tr>
</table>
<map name="loginMap" id="loginMap">
  <area shape="rect" coords="112,2,195,42" onClick="checkField();" />
  <area shape="rect" coords="197,-2,279,41" onClick="document.all.item('userID').value='';document.all.item('userPWD').value='';return;" />
</map>
</form>
</body>
</html>