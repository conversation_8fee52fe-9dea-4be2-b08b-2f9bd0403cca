package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyStatus;
import com.kangdainfo.util.lang.CommonStringUtils;

public class CmpyStatusDao extends BaseDaoJdbc implements RowMapper<CmpyStatus> {

	private static final String SQL_findByRcvNoAndRegUnit = "SELECT * FROM CMPY_STATUS WHERE RCV_NO = ? AND REG_UNIT = ?";
	public CmpyStatus findByRcvNoAndRegUnit(String rcvNo, String regUnit) {
		if(null==rcvNo || "".equals(rcvNo)) return null;
		if(null==regUnit || "".equals(regUnit)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByRcvNoAndRegUnit);
		sqljob.addParameter(rcvNo);
		sqljob.addParameter(regUnit);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (CmpyStatus) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CmpyStatus insert(CmpyStatus bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getRcvNo())) return null;
		if(CommonStringUtils.isEmpty(bo.getRegUnit())) return null;
		//check exist
		CmpyStatus t = findByRcvNoAndRegUnit(bo.getRcvNo(), bo.getRegUnit());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO CMPY_STATUS (");
		sqljob.appendSQL(" RCV_NO");
		sqljob.appendSQL(",REG_UNIT");
		sqljob.appendSQL(",PREFIX_NO");
		sqljob.appendSQL(",REG_DATE");
		sqljob.appendSQL(") VALUES (?,?,?,?)");
		sqljob.addParameter(bo.getRcvNo());
		sqljob.addParameter(bo.getRegUnit());
		sqljob.addParameter(bo.getPrefixNo());
		sqljob.addParameter(bo.getRegDate());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		return findByRcvNoAndRegUnit(bo.getRcvNo(), bo.getRegUnit());
	}
	
	public CmpyStatus update(CmpyStatus bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getRcvNo())) return null;
		if(CommonStringUtils.isEmpty(bo.getRegUnit())) return null;
		//check exist
		CmpyStatus t = findByRcvNoAndRegUnit(bo.getRcvNo(), bo.getRegUnit());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE CMPY_STATUS SET");
			sqljob.appendSQL("PREFIX_NO = ?");
			sqljob.appendSQL(",REG_DATE = ?");
			sqljob.appendSQL("WHERE RCV_NO = ?");
			sqljob.appendSQL("AND REG_UNIT = ?");
			sqljob.addParameter(bo.getPrefixNo());
			sqljob.addParameter(bo.getRegDate());
			sqljob.addParameter(bo.getRcvNo());
			sqljob.addParameter(bo.getRegUnit());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
			return findByRcvNoAndRegUnit(bo.getRcvNo(), bo.getRegUnit());
		}
	}
	
	public void delete(CmpyStatus bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getRcvNo())
				&& CommonStringUtils.isNotEmpty(bo.getRegUnit()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM CMPY_STATUS");
			sqljob.appendSQL(" WHERE RCV_NO = ?");
			sqljob.appendSQL("   AND REG_UNIT = ?");
			sqljob.addParameter(bo.getRcvNo());
			sqljob.addParameter(bo.getRegUnit());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	public void deleteWhenPre3005(String prefixNo) {  
		// 檢還撤件撤回退費時使用
		// 依照傳入的預查編號清除全部的紀錄
		SQLJob sqljob = new SQLJob("DELETE FROM CMPY_STATUS");
		sqljob.appendSQL(" WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	public CmpyStatus mapRow(ResultSet rs, int idx) throws SQLException {
		CmpyStatus obj = null;
		if(null!=rs) {
			obj = new CmpyStatus();
			obj.setRcvNo(rs.getString("RCV_NO"));
			obj.setRegUnit(rs.getString("REG_UNIT"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setRegDate(rs.getString("REG_DATE"));
		}
		return obj;
	}

}