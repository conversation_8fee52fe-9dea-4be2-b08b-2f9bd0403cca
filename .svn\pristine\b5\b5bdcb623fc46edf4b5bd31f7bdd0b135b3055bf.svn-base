package com.kangdainfo.tcfi.loader;

/**
 * 系統參數資料
 *
 */
public class SystemCode01Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_01";
	private static final String CODE_KIND = "01";//01:系統參數資料
	//singleton
	private static SystemCode01Loader instance;
	public SystemCode01Loader() {
		if (SystemCode01Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode01Loader.instance);
		}
		SystemCode01Loader.instance = this;
	}
	public static SystemCode01Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}