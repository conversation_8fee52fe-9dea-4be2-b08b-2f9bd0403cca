<?xml version="1.0" encoding="UTF-8"  ?>
<!-- Created with iReport - A designer for JasperReports -->
<!DOCTYPE jasperReport PUBLIC "//JasperReports//DTD Report Design//EN" "http://jasperreports.sourceforge.net/dtds/jasperreport.dtd">
<jasperReport
		 name="pre4007"
		 columnCount="1"
		 printOrder="Vertical"
		 orientation="Portrait"
		 pageWidth="595"
		 pageHeight="842"
		 columnWidth="555"
		 columnSpacing="0"
		 leftMargin="20"
		 rightMargin="20"
		 topMargin="20"
		 bottomMargin="20"
		 whenNoDataType="NoPages"
		 isTitleNewPage="false"
		 isSummaryNewPage="false">
	<property name="ireport.zoom" value="1.6105100000000008" />
	<property name="ireport.x" value="0" />
	<property name="ireport.y" value="0" />
	<property name="ireport.scriptlethandling" value="0" />
	<property name="ireport.encoding" value="UTF-8" />
	<import value="java.util.*" />
	<import value="net.sf.jasperreports.engine.*" />
	<import value="net.sf.jasperreports.engine.data.*" />

	<parameter name="printDate" isForPrompting="true" class="java.lang.String"/>
	<parameter name="printTime" isForPrompting="true" class="java.lang.String"/>
	<parameter name="receiveDate" isForPrompting="true" class="java.lang.String"/>
	<parameter name="totalReceive" isForPrompting="true" class="java.lang.String"/>
	<parameter name="totalComplete" isForPrompting="true" class="java.lang.String"/>
	<parameter name="sum" isForPrompting="true" class="java.lang.String"/>

	<field name="type" class="java.lang.String"/>
	<field name="workDay" class="java.lang.String"/>
	<field name="num" class="java.lang.String"/>
	<field name="averageDay" class="java.lang.String"/>

		<background>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</background>
		<title>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</title>
		<pageHeader>
			<band height="56"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="0"
						y="0"
						width="555"
						height="24"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Center">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="12" isBold="true" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[審核作業流程與時間統計分析表]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="40"
						width="80"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[報表類別：全部]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="0"
						y="24"
						width="117"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[報表編號：CESC1570R2]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="429"
						y="40"
						width="56"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Right">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[列印時間：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="429"
						y="24"
						width="56"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Right">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[列印日期：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="485"
						y="24"
						width="70"
						height="16"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{printDate}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="485"
						y="40"
						width="70"
						height="16"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{printTime}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="-378"
						y="-23"
						width="56"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[收文日期 : ]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="-378"
						y="-23"
						width="56"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[收文日期 : ]]></text>
				</staticText>
			</band>
		</pageHeader>
		<columnHeader>
			<band height="32"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="50"
						y="0"
						width="168"
						height="16"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{receiveDate}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="0"
						y="0"
						width="56"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[收文日期：]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="369"
						y="0"
						width="78"
						height="16"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{totalReceive}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="369"
						y="16"
						width="78"
						height="16"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{totalComplete}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="303"
						y="0"
						width="66"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Center">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[收文總件數：]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="303"
						y="16"
						width="66"
						height="16"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Center">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[辦結總件數：]]></text>
				</staticText>
			</band>
		</columnHeader>
		<detail>
			<band height="20"  isSplitAllowed="true" >
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="0"
						y="0"
						width="55"
						height="20"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{type}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="138"
						y="0"
						width="74"
						height="20"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{workDay}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="250"
						y="0"
						width="47"
						height="20"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{num}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="316"
						y="0"
						width="80"
						height="20"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$F{averageDay}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="55"
						y="0"
						width="83"
						height="20"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[[起迄總工作天數]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="212"
						y="0"
						width="38"
						height="20"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[／件數]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="297"
						y="0"
						width="19"
						height="20"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[] =]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="396"
						y="0"
						width="159"
						height="20"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Left">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[(四捨五入)]]></text>
				</staticText>
			</band>
		</detail>
		<columnFooter>
			<band height="0"  isSplitAllowed="true" >
			</band>
		</columnFooter>
		<pageFooter>
			<band height="20"  isSplitAllowed="true" >
			</band>
		</pageFooter>
		<summary>
			<band height="18"  isSplitAllowed="true" >
				<staticText>
					<reportElement
						x="0"
						y="0"
						width="55"
						height="18"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Left">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[總計]]></text>
				</staticText>
				<textField isStretchWithOverflow="false" isBlankWhenNull="false" evaluationTime="Now" hyperlinkType="None"  hyperlinkTarget="Self" >
					<reportElement
						x="316"
						y="0"
						width="80"
						height="18"
						key="textField"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<textFieldExpression   class="java.lang.String"><![CDATA[$P{sum}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement
						x="396"
						y="0"
						width="159"
						height="18"
						key="staticText"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement textAlignment="Left">
						<font fontName="標楷體" pdfFontName="kaiu.ttf" size="10" isPdfEmbedded ="true" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[(加總)]]></text>
				</staticText>
				<staticText>
					<reportElement
						x="297"
						y="0"
						width="19"
						height="18"
						key="staticText-1"/>
					<box topBorder="None" topBorderColor="#000000" leftBorder="None" leftBorderColor="#000000" rightBorder="None" rightBorderColor="#000000" bottomBorder="None" bottomBorderColor="#000000"/>
					<textElement>
						<font fontName="標楷體" pdfFontName="kaiu.ttf" pdfEncoding ="Identity-H"/>
					</textElement>
				<text><![CDATA[  =]]></text>
				</staticText>
			</band>
		</summary>
</jasperReport>
