  
-- Create Trigger
CREATE OR REPLACE TRIGGER EICM.TGI_CEDB1000
BEFORE INSERT OR UPDATE ON EICM.CEDB1000
referencing new as nu
For Each Row
Begin
  INSERT INTO EICM.INDEX_LOG (WS_ID,PARAM1,EXECUTE_DATE,CREATE_DATE,CREATE_USER,STATUS,REMARK)
  VALUES ('WS10002',:nu.PREFIX_NO,(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),nu.UPDATE_ID_NO,'0','From EICM.TGI_CEDB1000');
End;
