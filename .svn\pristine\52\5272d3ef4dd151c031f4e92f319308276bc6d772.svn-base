<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ page import="org.owasp.esapi.*" %>
<%@ page import="com.kangdainfo.common.util.*" %>
<html>
<head>
<script type="text/javascript">
function init(){
	form1.submit();
}
</script>
</head>
<body onload="init();">
<form action="tcfi/pre/pre3002.jsp" id="form1" name="form1" method="post" >
<input type="hidden" name="isSSO" value="N"/>
<input type="hidden" name="action" value="login" />
<input type="hidden" name="progID" value="PRE3002" />
<input type="hidden" name="nodeId" value="24" />
<%
String userID = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("userID")));
if( !"".equals(userID) ) {
	out.write("<input type=\"hidden\" name=\"userID\" value=\""+userID+"\" />");
}
String userPWD = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("userPWD")));
if( !"".equals(userPWD) ) {
	out.write("<input type=\"hidden\" name=\"userPWD\" value=\""+userPWD+"\" />");
}
String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
if( !"".equals(prefixNo) ) {
	out.write("<input type=\"hidden\" name=\"prefixNo\" value=\""+prefixNo+"\" />");
}
%>
</form>
</body>
</html>