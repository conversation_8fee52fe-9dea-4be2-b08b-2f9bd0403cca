package com.kangdainfo.common.util;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.LinkedList;
import java.util.List;

public class SQLJob {
	private StringBuffer sql;
	private List<Object> parameters;
	private List<Integer> sqltypes;

	public SQLJob()
	{
		sql = new StringBuffer();
		parameters = new ArrayList<Object>();
		sqltypes = new ArrayList<Integer>();
	}

	public SQLJob(String cmd)
	{
		sql = new StringBuffer();
		parameters = new LinkedList<Object>();
		sqltypes = new LinkedList<Integer>();
		this.appendSQL(cmd);
	}

	public SQLJob(String cmd, Object... params)
	{
		sql = new StringBuffer();
		parameters = new LinkedList<Object>();
		sqltypes = new LinkedList<Integer>();
		this.appendSQL(cmd);
		this.addParameters(params);
	}

	public void appendSQL(String cmd) {
		if(null != cmd) {
			this.sql.append(cmd.trim()).append(" ");
		}
	}

	public void addLikeParameter(String param) {
		parameters.add( "%"+param+"%" );
	}

	public void addPrefixLikeParameter(String param) {
		parameters.add( "%"+param );
	}

	public void addSuffixLikeParameter(String param) {
		parameters.add( param+"%" );
	}

	public void addParameter(Object obj) {
		parameters.add(obj);
	}

	public void addParameters(Object... params) {
		for (Object obj : params)
			addParameter(obj);
	}

	public void addSqltypes(Integer obj) {
		sqltypes.add(obj);
	}

	public void addSqltypes(Integer... objs) {
		for (Integer obj : objs)
			addSqltypes(obj);
	}

	public void appendSQLCondition(String cmd)
	{
		if( isFirstCondition() )
			this.appendSQL("where " + cmd);
		else
			this.appendSQL("and " + cmd);
	}

	protected boolean isFirstCondition()
	{
		if( null == this.getSQL() || "".equals(this.getSQL()) )
			return true;

		if( 0 > this.getSQL().toLowerCase().indexOf("where") )
			return true;

		return false;
	}

	public String getSQL()
	{
		if( null == this.sql )
			return null;
		return this.sql.toString().trim();
	}
	
	public Object[] getParametersArray()
	{
		if( null == parameters )
			return null;
		return parameters.toArray();
	}
	
	public int[] getSqltypesArray()
	{
		if( null == sqltypes || sqltypes.isEmpty() )
			return null;
		
		int[] array = new int[sqltypes.size()];
		for(int i=0;i<sqltypes.size();i++) {
			array[i] = sqltypes.get(i);
		}
		return array;
	}

	public String toString()
	{
		StringBuffer sb = new StringBuffer("SQLJob{");
		sb.append("[sql:"+getSQL()+"]");
		if( null!=parameters && !parameters.isEmpty() )
		{
			boolean isFirst = true;
			sb.append("[parameters:");
			for(Object obj : parameters)
			{
				if( !isFirst )
					sb.append(" , ");
				sb.append((null==obj?"null":obj.toString()));
				isFirst = false;
			}
			sb.append("]");
		}
		if( null!=sqltypes && !sqltypes.isEmpty() )
		{
			boolean isFirst = true;
			sb.append("[sqltypes:");
			for(Integer t : sqltypes)
			{
				if( !isFirst )
					sb.append(" , ");
				sb.append((null==t?"null":t.toString()));
				isFirst = false;
			}
			sb.append("]");
		}
		sb.append("}");
		return sb.toString();
	}

	public void appendBetweenInterval(String columnCondition, Object startValue, Object endValue )
	{
		if(null != startValue && null !=  endValue)
		{
			appendSQLCondition( columnCondition + " >= ? ");
			addParameter(startValue);
			appendSQLCondition( columnCondition + " <= ? ");
			addParameter(endValue);
		}
		else if( null != startValue && null ==  endValue )
		{
			appendSQLCondition( columnCondition + " >= ? ");
			addParameter(startValue);
		}
		else if( null == startValue && null !=  endValue )
		{
			appendSQLCondition( columnCondition + " <= ? ");
			addParameter(endValue);
		}
	}

	public static Date decorateStartDate(Date date)
	{
		if( null==date ) return null;
		Calendar cal = GregorianCalendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 1);
		return cal.getTime();
	}

	public static Date decorateEndDate(Date date)
	{
		if( null==date ) return null;
		Calendar cal = GregorianCalendar.getInstance();
		cal.setTime(date);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		cal.set(Calendar.MILLISECOND, 998);
		return cal.getTime();
	}

	public List<Object> getParameters() {	return parameters;	}
	public void setParameters(List<Object> parameters) {	this.parameters = parameters;	}

	public StringBuffer getSql() {	return sql;	}
	public void setSql(StringBuffer sql) {	this.sql = sql;	}

	public List<Integer> getSqltypes() {return sqltypes;}
	public void setSqltypes(List<Integer> sqltypes) {this.sqltypes = sqltypes;}

}