package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixQueryVo;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.service.CaseFlowService;
import com.kangdainfo.tcfi.service.Pre4001Service;
import com.kangdainfo.tcfi.service.TrackLogService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 案件資料查詢
 *
 */
public class Pre4001ServiceImpl implements Pre4001Service {
	private Logger logger = Logger.getLogger(this.getClass());

	public PrefixQueryVo findPrefixQueryVo(String prefixNo, String userId) {
		if(logger.isInfoEnabled()) logger.info("[findPrefixQueryVo]prefixNo:"+prefixNo);
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		Cedb1000 m = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null!=m) {
			String companyStus = m.getCompanyStus();
			String reserveDate = m.getReserveDate();
			String approveResult = m.getApproveResult();
			String prefixStatus = m.getPrefixStatus();
			String closeDate = m.getCloseDate();
			//若是尚未發文結案的案件，要新增查閱案件的歷程
			if ( null==closeDate || "".equals(closeDate) ) {
				caseFlowService.addCaseFlow(prefixNo, userId, PrefixConstants.PREFIX_STATUS_Q);
			}
			PrefixQueryVo vo = new PrefixQueryVo();
			//營業項目
			vo.setCedb1002s(cedb1002Dao.findByPrefixNo(prefixNo));
			//案件歷程
			vo.setCedb1010s(cedb1010Dao.findByPrefixNo(prefixNo));
			for(Cedb1010 cedb1010 : vo.getCedb1010s()) {
				cedb1010.setProcessStatus(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(cedb1010.getProcessStatus()));
				cedb1010.setProcessDate(Datetime.formatRocDate(cedb1010.getProcessDate())+"  "+Datetime.formatRocTime(cedb1010.getProcessTime()));
				cedb1010.setIdNo(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
			}

			//公司名稱
			String companyName01 = "";
			String companyName02 = "";
			String companyName03 = "";
			String companyName04 = "";
			String companyName05 = "";
			String approveResult01 = "";
			String approveResult02 = "";
			String approveResult03 = "";
			String approveResult04 = "";
			String approveResult05 = "";
			String remark01 = "";
			String remark02 = "";
			String remark03 = "";
			String remark04 = "";
			String remark05 = "";
			String showSame01 = "";
			String showSame02 = "";
			String showSame03 = "";
			String showSame04 = "";
			String showSame05 = "";
			List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1001s && !cedb1001s.isEmpty()) {
				String seqNo = "";

				for(Cedb1001 cedb1001 : cedb1001s) {
					seqNo = cedb1001.getSeqNo();
					if("01".equals(seqNo)) {
						companyName01 = Common.get(cedb1001.getCompanyName());
						approveResult01 = Common.get(cedb1001.getApproveResult());
						remark01 = Common.get(cedb1001.getRemark());
						//同名公司
						vo.setCedb1004s_1(loadCedb1004s(prefixNo, seqNo));
						showSame01 = getSameName(vo.getCedb1004s_1());
					} else if("02".equals(seqNo)) {
						companyName02 = Common.get(cedb1001.getCompanyName());
						approveResult02 = Common.get(cedb1001.getApproveResult());
						remark02 = Common.get(cedb1001.getRemark());
						//同名公司
						vo.setCedb1004s_2(loadCedb1004s(prefixNo, seqNo));
						showSame02 = getSameName(vo.getCedb1004s_2());
					} else if("03".equals(seqNo)) {
						companyName03 = Common.get(cedb1001.getCompanyName());
						approveResult03 = Common.get(cedb1001.getApproveResult());
						remark03 = Common.get(cedb1001.getRemark());
						//同名公司
						vo.setCedb1004s_3(loadCedb1004s(prefixNo, seqNo));
						showSame03 = getSameName(vo.getCedb1004s_3());
					} else if("04".equals(seqNo)) {
						companyName04 = Common.get(cedb1001.getCompanyName());
						approveResult04 = Common.get(cedb1001.getApproveResult());
						remark04 = Common.get(cedb1001.getRemark());
						//同名公司
						vo.setCedb1004s_4(loadCedb1004s(prefixNo, seqNo));
						showSame04 = getSameName(vo.getCedb1004s_4());
					} else if("05".equals(seqNo)) {
						companyName05 = Common.get(cedb1001.getCompanyName());
						approveResult05 = Common.get(cedb1001.getApproveResult());
						remark05 = Common.get(cedb1001.getRemark());
						//同名公司
						vo.setCedb1004s_5(loadCedb1004s(prefixNo, seqNo));
						showSame05 = getSameName(vo.getCedb1004s_5());
					}
				}
			}
			vo.setCompanyName01(companyName01);
			vo.setCompanyName02(companyName02);
			vo.setCompanyName03(companyName03);
			vo.setCompanyName04(companyName04);
			vo.setCompanyName05(companyName05);
			vo.setApproveResult01(approveResult01);
			vo.setApproveResult02(approveResult02);
			vo.setApproveResult03(approveResult03);
			vo.setApproveResult04(approveResult04);
			vo.setApproveResult05(approveResult05);
			vo.setRemark01(remark01);
			vo.setRemark02(remark02);
			vo.setRemark03(remark03);
			vo.setRemark04(remark04);
			vo.setRemark05(remark05);
			vo.setShowSame01(showSame01);
			vo.setShowSame02(showSame02);
			vo.setShowSame03(showSame03);
			vo.setShowSame04(showSame04);
			vo.setShowSame05(showSame05);

			vo.setPrefixNo(m.getPrefixNo());
			vo.setApplyWay(TcfiView.getApplyWayByTelixNo(m.getTelixNo()));
			
			String setup = "N";
			String changeName = "N";
			String changeItem = "N";
			Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(m.getPrefixNo());
			if(null!=cedb1023 && null!=cedb1023.getChangeType() && !"".equals(cedb1023.getChangeType())) {
				String changeType = cedb1023.getChangeType();
				if(PrefixConstants.CHANGE_TYPE_0.equals(changeType)) {
					setup = "Y";
					changeName = "N";
					changeItem = "N";
				} else if(PrefixConstants.CHANGE_TYPE_1.equals(changeType)) {
					setup = "N";
					changeName = "Y";
					changeItem = "N";
				} else if(PrefixConstants.CHANGE_TYPE_2.equals(changeType)) {
					setup = "N";
					changeName = "N";
					changeItem = "Y";
				} else if(PrefixConstants.CHANGE_TYPE_3.equals(changeType)) {
					setup = "N";
					changeName = "Y";
					changeItem = "Y";
				}
			} else {
				if("1".equals(m.getApplyKind())) {
					setup = "Y";
					changeName = "N";
					changeItem = "N";
				} else {
					setup = "N";
					changeName = "Y";
					changeItem = "N";
				}
			}
			vo.setClosed(cedb1023.getClosed());
			vo.setSetup(setup);
			vo.setChangeName(changeName);
			vo.setChangeItem(changeItem);
			vo.setTelixNo(m.getTelixNo());
			vo.setBanNo(m.getBanNo());
			vo.setGetKind(m.getGetKind());
			vo.setGetKindDesc(getGetKindDesc(m));
			//特取名稱
			vo.setSpecialName(m.getSpecialName());
			//本次預查名稱
			if(PrefixConstants.APPROVE_RESULT_Y.equals(approveResult))
				vo.setCompanyName(m.getCompanyName());
			else
				vo.setCompanyName("");
			//提示文字
			vo.setReserveTip(TcfiView.getReserveTip(approveResult, reserveDate, prefixStatus, companyStus));
			//承辦人
			vo.setStaffName(m.getStaffName());
			//前次預查名稱
			vo.setLastCompanyName(getLastPrefixCompanyName(m));
			//前次異動者
			vo.setUpdateName(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(m.getUpdateIdNo()));
			//備註紀錄
			vo.setRemark1(m.getRemark1());
			//審核結果
			vo.setApproveRemark(m.getApproveRemark());
			//公司現況主檔名稱
			vo.setMainFileCompanyName(getMainFileCompanyName(m));
			//註記說明(檢還/撤件)
			vo.setRemark(m.getRemark());
			//核覆結果
			vo.setApproveResultDesc(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(m.getApproveResult()));
			//案件狀態
			vo.setPrefixStatusDesc(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(m.getPrefixStatus()));
			//保留期限
			vo.setReserveDate(m.getReserveDate());
			//保留天數
			vo.setReserveDays(m.getReserveDays());
			//延展保留期限註記
			vo.setReserveMark(m.getReserveMark());
			//申請人姓名
			vo.setApplyName(m.getApplyName());
			//申請人身分ID
			vo.setApplyId(m.getApplyId());
			//申請人電話
			vo.setApplyTel(m.getApplyTel());
			//法人資料
			Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(m.getPrefixNo());
			if(null!=cedb1022) {
				//所代表法人
				vo.setApplyLawName(cedb1022.getApplyLawName());
				//法人統編
				vo.setApplyBanNo(cedb1022.getApplyBanNo());
			}
			//申請人地址
			vo.setApplyAddr(m.getApplyAddr());
			//代理人姓名
			vo.setAttorName(m.getAttorName());
			//代理人證書號碼╱代理人身分ID
			vo.setAttorNo(m.getAttorNo());
			//代理人聯絡電話
			vo.setAttorTel(m.getAttorTel());
			//事務所所在地
			vo.setAttorAddr(m.getAttorAddr());
			if(null!=cedb1023) {
				//收件人姓名
				vo.setReceiveName(TcfiView.normalizeName(cedb1023.getGetName()));
				//簡訊通知回覆電話
				vo.setContactCel(cedb1023.getContactCel());
				//寄件日期
				vo.setSendDateTime(Datetime.formatRocDate(m.getGetDate())+" "+Datetime.formatRocTime(m.getGetTime()));
				//聯絡地址
				vo.setReceiveAddr(cedb1023.getGetAddr());
			}
			//收件日期時間
			vo.setReceiveDateTime(Datetime.formatRocDate(m.getReceiveDate())+" "+Datetime.formatRocTime(m.getReceiveTime()));
			//收文登打日期時間
			vo.setReceiveKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_2));
			//分文日期時間
			vo.setAssignDateTime(Datetime.formatRocDate(m.getAssignDate())+" "+Datetime.formatRocTime(m.getAssignTime()));
			//審核日期時間
			vo.setApproveDateTime(Datetime.formatRocDate(m.getApproveDate())+" "+Datetime.formatRocTime(m.getApproveTime()));
			//發文登打日期時間
			vo.setIssueKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_7));
			//發文日期時間
			vo.setCloseDateTime(Datetime.formatRocDate(m.getCloseDate())+" "+Datetime.formatRocTime(m.getCloseTime()));
			//領件日期時間
			vo.setGetDateTime(Datetime.formatRocDate(m.getGetDate())+" "+Datetime.formatRocTime(m.getGetTime()));
			//公司設立日期
			vo.setCmpySetupDate(Common.get(cedb2000Dao.findSetupDateByBanNo(m.getBanNo())));
			//是否有預查表附件
			vo.setIsPrefixForm("Y".equalsIgnoreCase(Common.get(m.getIsPrefixForm()))?"Y":"N");
			//附件-預查表編號
			vo.setPrefixFormNo(m.getPrefixFormNo());
			//是否有其他機關核准函附件
			vo.setIsOtherForm("Y".equalsIgnoreCase(Common.get(m.getIsOtherForm()))?"Y":"N");
			//是否有說明書附件
			vo.setIsSpec("Y".equalsIgnoreCase(Common.get(m.getIsSpec()))?"Y":"N");
			//是否有其他附件
			vo.setIsOtherSpec("Y".equalsIgnoreCase(Common.get(m.getIsOtherSpec()))?"Y":"N");
			//其他附件註記
			vo.setOtherSpecRemark(m.getOtherSpecRemark());
			//正副本別
			vo.setDocType(m.getDocType());
			//領件方式註記
			vo.setGetKindRemark(m.getGetKindRemark());
		    // 自由填列事項（不納入預查審核項目）- 國外匯款使用英文名稱(僅提供銀行開戶使用)
			vo.setExtRemitEname(m.getExtRemitEname());
			
			//新增查詢類的個資軌跡
			trackLogService.doSearchTrack("PRE4001", vo);
			return vo;
		}
		return null;
	}
	
	private List<Cedb1004> loadCedb1004s(String prefixNo, String seqNo) {
		List<Cedb1004> results = new ArrayList<Cedb1004>();
		List<Cedb1004> datas = cedb1004Dao.findByPrefixNoAndSeqNo(prefixNo, seqNo);
		if(null!=datas && !datas.isEmpty()) {
			String sameBanNo = "";
			Cedb2000 cedb2000;
			for(Cedb1004 data : datas) {
				sameBanNo = Common.get(data.getSameBanNo());
				if(!"".equals(sameBanNo)) {
					cedb2000 = cedb2000Dao.findByBanNo(sameBanNo);
					if(null!=cedb2000) {
						data.setCmpyStatus(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(cedb2000.getStatusCode()));
					}
				}
				results.add(data);
			}
		}
		return results;
	}
	
	private static final String BLANK = " ";
	private String getSameName(List<Cedb1004> cedb1004s) {
		StringBuffer sb = new StringBuffer();
		if(null!=cedb1004s && !cedb1004s.isEmpty()) {
			String sameBanNo = "";
			String sameCompanyName = "";
			String samePrefixNo = "";
			String cmpyStatus = "";
			for(Cedb1004 cedb1004 : cedb1004s) {
				if(!"".equals(sb.toString())) sb.append("\n");
				sameBanNo = Common.get(cedb1004.getSameBanNo());
				sameCompanyName = Common.get(cedb1004.getSameCompanyName());
				samePrefixNo = Common.get(cedb1004.getSamePrefixNo());
				cmpyStatus = Common.get(cedb1004.getCmpyStatus());
				if(!"".equals(sameBanNo)) {
					sb.append(sameBanNo);
					if(!"".equals(cmpyStatus)) {
						sb.append(BLANK);
						sb.append(cmpyStatus);
					}
					sb.append(BLANK);
					sb.append(sameCompanyName);
				} else {
					sb.append(samePrefixNo);
					sb.append(BLANK);
					sb.append(sameCompanyName);
				}
			}
		}
		return sb.toString();
	}

	private String getGetKindDesc(Cedb1000 m) {
		String getKindDesc = "";
		if(CommonStringUtils.isNotEmpty(m.getGetKindRemark())) {
			getKindDesc += "\t\t郵資"+Common.get(m.getGetKindRemark());
		}
		//掛號號碼(抓1027的最後一筆)，排除平信
		Cedb1027 cedb1027 = cedb1027Dao.findLastOneByPrefixNo(m.getPrefixNo());
		if(cedb1027 != null && !"04".equals(cedb1027.getPostType())) {
			if(CommonStringUtils.isNotEmpty(cedb1027.getPostNo())) {
				getKindDesc += "\t\t掛號號碼"+Common.get(cedb1027.getPostNo());
			}
		}
		return getKindDesc;
	}
	
	private String getLastPrefixCompanyName(Cedb1000 m) {
		if(null==m) return null;
		if(CommonStringUtils.isEmpty(m.getBanNo())) return null;

		List<Cedb1000> datas = cedb1000Dao.getLastPrefix(m.getPrefixNo(), m.getBanNo());
		if (null!=datas && !datas.isEmpty()) {
			Cedb1000 lastOne = datas.get(0);
			if(null!=lastOne) {
				String reserveDate = Common.get(lastOne.getReserveDate());
				String companyName = Common.get(lastOne.getCompanyName());
				//查3個月內
				String threeMonthAgo = Datetime.getDateAdd("m", -3, Datetime.getYYYMMDD());
				if( reserveDate.compareTo(threeMonthAgo) >= 0 ) {
					return companyName;
				}
			}
		}
		return null;
	}

	public String getMainFileCompanyName(Cedb1000 m) {
		String oldCompanyName = Common.get(m.getOldCompanyName());
		if(CommonStringUtils.isNotEmpty(oldCompanyName))
			return oldCompanyName;
		else {
			String banNo = Common.get(m.getBanNo());
			if(CommonStringUtils.isNotEmpty(banNo)) {
				try {
					Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
					if (cedb2000 != null) {
						return cedb2000.getCompanyName();
					}
				} catch(Exception e) {
				}
			}
			return null;
		}
	}

	private Cedb1000Dao cedb1000Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1004Dao cedb1004Dao;
	private Cedb1010Dao cedb1010Dao;
	private Cedb1022Dao cedb1022Dao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1027Dao cedb1027Dao;
	private Cedb2000Dao cedb2000Dao;
	private CaseFlowService caseFlowService;
	private TrackLogService trackLogService;

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}
	
	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}

	public Cedb1004Dao getCedb1004Dao() {return cedb1004Dao;}
	public void setCedb1004Dao(Cedb1004Dao dao) {this.cedb1004Dao = dao;}

	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}

	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}

	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}

	public Cedb1027Dao getCedb1027Dao() {return cedb1027Dao;}
	public void setCedb1027Dao(Cedb1027Dao dao) {this.cedb1027Dao = dao;}

	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}

	public CaseFlowService getCaseFlowService() {return caseFlowService;}
	public void setCaseFlowService(CaseFlowService service) {this.caseFlowService = service;}

	public TrackLogService getTrackLogService() {return trackLogService;}
	public void setTrackLogService(TrackLogService service) {this.trackLogService = service;}

}