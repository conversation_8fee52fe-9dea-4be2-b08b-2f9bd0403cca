package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 全文檢索內容檔(FTXT_SEARCH_MAS)
 *
 */
public class FtxtSearchMas extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 公司統編 */
	private String banNo;
	/** 預查編號 */
	private String prefixNo;
	/** 公司名稱 */
	private String companyName;
	/** 公司狀況代碼 */
	private String statusCode;
	/** 保留期限 */
	private String reserveDate;
	/** 申請人姓名 */
	private String applyName;

	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getStatusCode() {return statusCode;}
	public void setStatusCode(String statusCode) {this.statusCode = statusCode;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String reserveDate) {this.reserveDate = reserveDate;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String applyName) {this.applyName = applyName;}

}