package com.kangdainfo.tcfi.loader;

/**
 * 公司型態
 *
 */
public class SystemCode04Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_04";
	private static final String CODE_KIND = "04";//04:公司型態
	//singleton
	private static SystemCode04Loader instance;
	public SystemCode04Loader() {
		if (SystemCode04Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode04Loader.instance);
		}
		SystemCode04Loader.instance = this;
	}
	public static SystemCode04Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}