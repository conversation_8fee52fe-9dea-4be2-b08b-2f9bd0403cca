package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.view.pre.PRE2004;

/**
 * 預查核准領件編號維護(PRE2004)
 *
 */
public interface Pre2004Service {
	/** 查詢前一天的最大號 */
	public String queryStartNo();
	/** 查詢全部的最大號 */
	public String queryEndNo();
	/** 查詢待領件資料 */
	public List<PRE2004> queryPre2004s(String prefixNoStart, String prefixNoEnd);
	/** 儲存現況已核准領件的最大號 */
	public void saveCedb1021(String maxPrefixNo, String modIdNo);
	/** 結案 */
	public String doCloseCase(String prefixNo, String modIdNo);
	/** 檢查申請撤件退費中 */
	public String checkRemark(String prefixNo);
}