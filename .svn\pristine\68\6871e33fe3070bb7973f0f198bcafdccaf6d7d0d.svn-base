--DROP TABLE EICM.CEDB1110;
-- Create table
create table EICM.CEDB1110 (
  PREFIX_NO      NVARCHAR2(9) not null,
  ID_NO          NVARCHAR2(10),
  PROCESS_DATE   NVARCHAR2(7),
  PROCESS_TIME   NVARCHAR2(7),
  PROCESS_STATUS NVARCHAR2(1),
  WORK_DAY       NUMBER(5,2)
)
-- Add comments to the table 
comment on table EICM.CEDB1110 is '預查封存案件過程記錄檔';
-- Add comments to the columns 
comment on column EICM.CEDB1110.PREFIX_NO is '預查編號';
comment on column EICM.CEDB1110.ID_NO is '承辦人員識別碼';
comment on column EICM.CEDB1110.PROCESS_DATE is '處理日期';
comment on column EICM.CEDB1110.PROCESS_TIME is '處理時間';
comment on column EICM.CEDB1110.PROCESS_STATUS is '案件流程代號';
comment on column EICM.CEDB1110.WORK_DAY is '工作日';

--Synonymn(use dba to run)
CREATE OR REPLACE SYNONYM "EICM4AP"."CEDB1110" FOR "EICM"."CEDB1110";
--GRANT(use dba to run)
grant all on EICM.CEDB1110 to EICM4AP;

-- Create/Recreate indexes 
create index EICM.CEDB1110_IDX1 on EICM.CEDB1110 (PREFIX_NO)
  tablespace EICM_IDX
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
create index EICM.CEDB1110_IDX2 on EICM.CEDB1110 (PREFIX_NO,PROCESS_STATUS)
  tablespace EICM_IDX
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
