package com.kangdainfo.tcfi.model.eicm.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * VIEW
 * 外國公司資料檔(CEDB2006)
 *
 */
public class Cedb2006 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 統一編號 */
	private String banNo;
	/** 公司名稱（外文） */
	private String foreignName;
	/** 公司地址（中文） */
	private String foreignAddr;
	/** 公司地址（外文） */
	private String foreignForAddr;
	/** 公司國籍別 */
	private String nativeCode;
	/** 母公司設立日期 */
	private Date setupDate;
	/** 母公司營業日期 */
	private Date operateDate;
	/** 資本幣別 */
	private String currencyType;
	/** 資本總額 */
	private Long capitalAmt;
	/** 每股金額 */
	private Long stockAmt;
	/** 實收資本額 */
	private Long realAmt;
	/** 股份種類(1：普通股;2：特別股) */
	private String stockType;
	/** 訴訟代理人姓名 */
	private String appointName;
	/** 訴訟代理人姓名（外文） */
	private String appointForName;
	/** 訴訟代理人國籍別 */
	private String appointNativeCode;
	/** 訴訟代理人身分證件別(1：身分證;2：外僑居留證;3：華僑身分證明) */
	private String appointIdCode;
	/** 訴訟代理人身分證件字號 */
	private String appointIdNo;
	/** 訴訟代理人中國境內住址 */
	private String appointAddress;
	/** 代表人在中國境內之法律行為 */
	private String respLawActivity;
	/** 資料異動者 */
	private String updateUser;
	/** 資料異動日期 */
	private String updateDate;
	/** 資料異動時間 */
	private String updateTime;

	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getForeignName() {return foreignName;}
	public void setForeignName(String s) {this.foreignName = s;}
	public String getForeignAddr() {return foreignAddr;}
	public void setForeignAddr(String s) {this.foreignAddr = s;}
	public String getForeignForAddr() {return foreignForAddr;}
	public void setForeignForAddr(String s) {this.foreignForAddr = s;}
	public String getNativeCode() {return nativeCode;}
	public void setNativeCode(String s) {this.nativeCode = s;}
	public Date getSetupDate() {return setupDate;}
	public void setSetupDate(Date d) {this.setupDate = d;}
	public Date getOperateDate() {return operateDate;}
	public void setOperateDate(Date d) {this.operateDate = d;}
	public String getCurrencyType() {return currencyType;}
	public void setCurrencyType(String s) {this.currencyType = s;}
	public Long getCapitalAmt() {return capitalAmt;}
	public void setCapitalAmt(Long l) {this.capitalAmt = l;}
	public Long getStockAmt() {return stockAmt;}
	public void setStockAmt(Long l) {this.stockAmt = l;}
	public Long getRealAmt() {return realAmt;}
	public void setRealAmt(Long l) {this.realAmt = l;}
	public String getStockType() {return stockType;}
	public void setStockType(String s) {this.stockType = s;}
	public String getAppointName() {return appointName;}
	public void setAppointName(String s) {this.appointName = s;}
	public String getAppointForName() {return appointForName;}
	public void setAppointForName(String s) {this.appointForName = s;}
	public String getAppointNativeCode() {return appointNativeCode;}
	public void setAppointNativeCode(String s) {this.appointNativeCode = s;}
	public String getAppointIdCode() {return appointIdCode;}
	public void setAppointIdCode(String s) {this.appointIdCode = s;}
	public String getAppointIdNo() {return appointIdNo;}
	public void setAppointIdNo(String s) {this.appointIdNo = s;}
	public String getAppointAddress() {return appointAddress;}
	public void setAppointAddress(String s) {this.appointAddress = s;}
	public String getRespLawActivity() {return respLawActivity;}
	public void setRespLawActivity(String s) {this.respLawActivity = s;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String s) {this.updateUser = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}

}