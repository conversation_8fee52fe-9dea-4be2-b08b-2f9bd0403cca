package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import java.io.File ;

import com.ekera.presearch.Examine;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1300;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.lang.CommonStringUtils;
import com.kangdainfo.util.report.JasperReportMaker;

/*
程式目的：檢還、撤件、撤回退費
程式代號：pre3005
撰寫日期：103.06.09
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE3005 extends SuperBean {
	private Logger logger = Logger.getLogger(this.getClass());
	
	private String q_prefixNo;		// 預查編號(查詢)
	private String q_returnDateStart;
	private String q_returnDateEnd;
	
	private String prePrefixNo;		// 預查編號
	private String prefixNo;		// 檢還本案預查編號
	private String applyName;		// 申請人姓名
	private String applyId;			// 申請人身分證字號
	private String tel;				// 電話
	private String addr;			// 地址
	private String banNo;			// 公司統編
	private String companyName;		// 公司名稱
	private String getKind;			// 取件方式
	private String processType;		// 作業型別
	private String receiveDate;		// 收件日期
	private String receiveTime;		// 收件日期
	private String approveDate;		// 核覆日期
	private String approveTime;		// 核覆日期
	private String reserveDate;		// 保留期限
	private String getDate;			// 取件日期
	private String getTime;			// 取件日期
	private String approveResult;	// 核覆結果
	private String prefixStatus;	// 案件狀態
	private String receiptNo;		// 收據編號
	private String receiptState;	// 出納科收據狀態
	private String refundNo;		// 退費公文號
	private String accountDate;
	private String returnDate;
	private String popErrorMsg;
	private String contactName;
	private String contactAddr;
	private String noPayMark;

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	public String getQ_returnDateStart() {return checkGet(q_returnDateStart);}
	public void setQ_returnDateStart(String s) {q_returnDateStart = checkSet(s);}
	public String getQ_returnDateEnd() {return checkGet(q_returnDateEnd);}
	public void setQ_returnDateEnd(String s) {q_returnDateEnd = checkSet(s);}
	
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	public String getPrePrefixNo() {return checkGet(prePrefixNo);}
	public void setPrePrefixNo(String s) {prePrefixNo = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getApplyId() {return checkGet(applyId);}
	public void setApplyId(String s) {applyId = checkSet(s);}
	public String getTel() {return checkGet(tel);}
	public void setTel(String s) {tel = checkSet(s);}
	public String getAddr() {return checkGet(addr);}
	public void setAddr(String s) {addr = checkSet(s);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {banNo = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {getKind = checkSet(s);}
	public String getProcessType() {return checkGet(processType);}
	public void setProcessType(String s) {processType = checkSet(s);}
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {receiveDate = checkSet(s);}
	public String getReceiveTime() {return checkGet(receiveTime);}
	public void setReceiveTime(String s) {receiveTime = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {approveDate = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {approveTime = checkSet(s);}
	public String getReserveDate() {return checkGet(reserveDate);}
	public void setReserveDate(String s) {reserveDate = checkSet(s);}
	public String getGetDate() {return checkGet(getDate);}
	public void setGetDate(String s) {getDate = checkSet(s);}
	public String getGetTime() {return checkGet(getTime);}
	public void setGetTime(String s) {getTime = checkSet(s);}
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {approveResult = checkSet(s);}
	public String getPefixStatus() {return checkGet(prefixStatus);}
	public void setPrefixStatus(String s) {prefixStatus = checkSet(s);}
	public String getReceiptNo() {return checkGet(receiptNo);}
	public void setReceiptNo(String s) {receiptNo = checkSet(s);}
	public String getReceiptState() {return checkGet(receiptState);}
	public void setReceiptState(String s) {receiptState = checkSet(s);}
	public String getRefundNo() {return checkGet(refundNo);}
	public void setRefundNo(String s) {refundNo = checkSet(s);}
	public String getAccountDate() {return checkGet(accountDate);}
	public void setAccountDate(String s) {accountDate = checkSet(s);}
	public String getReturnDate() {return checkGet(returnDate);}
	public void setReturnDate(String s) {returnDate = checkSet(s);}
	public String getPopErrorMsg() {return checkGet(popErrorMsg);}
	public void setPopErrorMsg(String s) {popErrorMsg = checkSet(s);}
	public String getNoPayMark() {return checkGet(noPayMark);}
	public void setNoPayMark(String s) {noPayMark = checkSet(s);}
	public String getContactName() {return contactName;}
	public void setContactName(String s) {this.contactName = checkSet(s);}
	public String getContactAddr() {return contactAddr;}
	public void setContactAddr(String s) {this.contactAddr = checkSet(s);}

	// detail 查詢欄位 
	private String q_dateStart;
	private String q_dateEnd;
	/** 撤件案件數 */
	private String queryResult1;
	/** 撤回退費案件數 */
	private String queryResult2;
	/** 檢還案件數 */
	private String queryResult3;
	public String getQ_dateStart() {return checkGet(q_dateStart);}
	public void setQ_dateStart(String s) {q_dateStart = checkSet(s);}
	public String getQ_dateEnd() {return checkGet(q_dateEnd);}
	public void setQ_dateEnd(String s) {q_dateEnd = checkSet(s);}
	public String getQueryResult1() {return checkGet(queryResult1);}
	public void setQueryResult1(String s) {queryResult1 = checkSet(s);}
	public String getQueryResult2() {return checkGet(queryResult2);}
	public void setQueryResult2(String s) {queryResult2 = checkSet(s);}
	public String getQueryResult3() {return checkGet(queryResult3);}
	public void setQueryResult3(String s) {queryResult3 = checkSet(s);}
		  
	public Object doQueryOne() throws Exception{ 
		
		PRE3005 pre3005 = this;
		String qPrefixNo = getQ_prefixNo();
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( qPrefixNo );
		pre3005 = new PRE3005();
		pre3005.setQ_prefixNo(qPrefixNo);
		if ( cedb1000 == null ){
			pre3005.setErrorMsg("查無資料，請變更查詢條件");
        }else {
        	PrefixReceiptNo prefixReceiptNo = null;
        	Eedb1300 eedb1300 = null;
        	prefixReceiptNo = ServiceGetter.getInstance().getPre3005Service().selectPrefixReceiptNoByPrefixNo(cedb1000.getPrefixNo());
        	if ( !"".equals(Common.get(cedb1000.getTelixNo())) && (cedb1000.getTelixNo().startsWith("OSC") || cedb1000.getTelixNo().startsWith("OSS"))) {
        		// 一站式案件, 至ossmFeeMain撈取收據資料
				OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(cedb1000.getTelixNo());
				if(null!=ossmApplMain) {
					String processNo = "B";
				    if ( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals( ossmApplMain.getApplyType()) ) {
				    	processNo = "I";
				    }
	        		OssmFeeMain ossmFeeMainTmp = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(cedb1000.getTelixNo(),processNo) ;
	        		if (ossmFeeMainTmp != null) {
	        			eedb1300 = new Eedb1300();
	        			eedb1300.setTelixNo(ossmFeeMainTmp.getTelixNo());
	        			eedb1300.setReceiptNo(ossmFeeMainTmp.getReceiptNo());
	        			eedb1300.setAmount(String.valueOf(ossmFeeMainTmp.getAmount()));
	        			eedb1300.setPayDate(ossmFeeMainTmp.getPayDate());
	        			eedb1300.setReceiptTitle(cedb1000.getApplyName());
	        			eedb1300.setReceiptDate(ossmFeeMainTmp.getAccountDate());
	    				eedb1300.setPayType(TcfiView.getPayTypeDesc(ossmFeeMainTmp.getPayType()));
	        		} 
				}
        	}else {
        		// 線上申辦, 從eedb1300中撈取資料
        		eedb1300 = ServiceGetter.getInstance().getPrefixService().getEedb1300ByTelixNo(cedb1000.getTelixNo());
        		
        	}
        	
        	if (eedb1300!=null) {
        		
        		pre3005.setAccountDate(eedb1300.getAccountDate());
        		pre3005.setReturnDate(eedb1300.getReturnDate());
        	}
        	if (prefixReceiptNo!=null) {
        		pre3005.setReceiptNo("商登預字第"+prefixReceiptNo.getReceiptNo());
        		pre3005.setContactName(prefixReceiptNo.getRecipientName());
             	pre3005.setContactAddr(prefixReceiptNo.getRecipientAddr());
        	}
        	
        	
        	pre3005.setPrefixNo( cedb1000.getPrefixNo() );
        	// 申請人基本資料
        	pre3005.setApplyName( cedb1000.getApplyName() );
        	pre3005.setApplyId( cedb1000.getApplyId() );
        	pre3005.setTel( cedb1000.getApplyTel() );
        	pre3005.setBanNo( cedb1000.getBanNo() );
        	pre3005.setAddr( cedb1000.getApplyAddr() );
         	pre3005.setCompanyName( cedb1000.getCompanyName() );
         	
         	if (cedb1000.getBanNo() != null && !"".equals(cedb1000.getBanNo())) {
         		pre3005.setNoPayMark(ServiceGetter.getInstance().getNoPayMarkService().getNoPayMark4Pre3005(cedb1000.getBanNo(), cedb1000.getPrefixNo(), cedb1000.getTelixNo()));
         	}
         	
         	
         	// 申請案資料
         	pre3005.setGetKind( cedb1000.getGetKind() );
         	pre3005.setRefundNo( cedb1000.getRefundNo() );
         	pre3005.setReceiveDate( Common.formatYYYMMDD(cedb1000.getReceiveDate(), 4) );
         	pre3005.setReceiveTime( Common.formatHHMMSS(cedb1000.getReceiveTime(), 1) );
         	pre3005.setApproveDate( Common.formatYYYMMDD(cedb1000.getApproveDate(), 4) );
         	pre3005.setApproveTime( Common.formatHHMMSS(cedb1000.getApproveTime(), 1) );
         	pre3005.setReserveDate( Common.formatYYYMMDD(cedb1000.getReserveDate(), 4) );
         	pre3005.setGetDate( Common.formatYYYMMDD(cedb1000.getGetDate(), 4) );
         	pre3005.setGetTime( Common.formatHHMMSS(cedb1000.getGetTime(), 1) );
         	pre3005.setApproveResult( Common.get(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode( cedb1000.getApproveResult())));
         	pre3005.setPrefixStatus( Common.get(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode( cedb1000.getPrefixStatus())) );
         	
         	if ( !"".equals(Common.get(pre3005.getReturnDate())) ) {
         		pre3005.setReceiptState("已退費");
            }
         	else if ( !"".equals( Common.get(pre3005.getAccountDate()) ) ) {
         		pre3005.setReceiptState("已與台銀對帳");
         	}
         	pre3005.setPopErrorMsg(prefixErrorMsg(cedb1000, pre3005));
         	//寫入個資軌跡 (查詢類)
         	ServiceGetter.getInstance().getTrackLogService().insertApplyPerson("PR3005", PrefixConstants.TRACK_LOG_SEARCH,
             		cedb1000.getPrefixNo(), cedb1000.getApplyId(), cedb1000.getApplyName(), cedb1000.getApplyTel(), cedb1000.getApplyAddr());
        }
		return pre3005;
	}
	
	public void doWithdraw() throws Exception{
		try{
			// 選擇退費時, 從receipt_no_set取退還書編號寫入至prefix_receipt_no.return_no, 並且將系統日期寫入return_date
			// 選擇其他作業型別時, 修改主檔的approve_result為N, 並且在主檔的REMARK欄位裡留下註記
			String userName = getLoginUserName();
			String status = "";
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( getPrefixNo() );
			PrefixReceiptNo prefixReceiptNo = ServiceGetter.getInstance().getPre3005Service().selectPrefixReceiptNoByPrefixNo(getPrefixNo());
			if(cedb1000 == null){
				this.setErrorMsg("查無預查資料！");
				return;
			} else if (prefixReceiptNo != null && (prefixReceiptNo.getReturnNo() != null && !"".equals(prefixReceiptNo.getReturnNo()))) {
				this.setErrorMsg("該案件已辦理過退費！");
				return;
			}
			
			if ("04".equals(getProcessType()) || "03".equals(getProcessType())) {
				if (prefixReceiptNo != null) {
					prefixReceiptNo.setRefund("Y");
					prefixReceiptNo.setRecipientName(getContactName());
					prefixReceiptNo.setRecipientAddr(getContactAddr());
				}
				
				ReceiptNoSetup receiptNoSetup = ServiceGetter.getInstance().getPre5001Service().selectReceiptNoSetupByReceiptType("0");
				int subtract = Integer.parseInt(receiptNoSetup.getEndReceiptNo())-Integer.parseInt(receiptNoSetup.getUsedReceiptNo());
				if (subtract==0) {
					setErrorMsg("1;退還書編號已用罄, 請使用收據編號維護作業更新可用的收據號");
					return ;
				} else {
					if (prefixReceiptNo != null) {
						prefixReceiptNo.setReturnNo(String.valueOf(Integer.parseInt(receiptNoSetup.getUsedReceiptNo()) + 1));
						prefixReceiptNo.setReturnDate(Calendar.getInstance().getTime());
						prefixReceiptNo.setReturnUser(userName);
						prefixReceiptNo.setReturnType(getProcessType());
					}
					receiptNoSetup.setUsedReceiptNo(String.valueOf(Integer.parseInt(receiptNoSetup.getUsedReceiptNo()) + 1));
				}
				
				status = cedb1000.getPrefixStatus();
				cedb1000.setUpdateIdNo( getLoginUserId() ); 
				cedb1000.setUpdateDate( Datetime.getYYYMMDD() );
				cedb1000.setUpdateTime( Datetime.getHHMMSS() );
				if ("03".equals(getProcessType())) {
					cedb1000.setRemark( Datetime.getYYYMMDD() +"因撤回退費"+ userName + "修改核覆結果:" + getPrePrefixNo());
					status = PrefixConstants.PREFIX_STATUS_E ;
					cedb1000.setReserveDate(null);
					cedb1000.setPrefixStatus(status);
					cedb1000.setCompanyName("");
					cedb1000.setRegDate("");
					cedb1000.setRegUnit("");
					cedb1000.setCompanyStus("");
					cedb1000.setApproveResult("N");
					cedb1000.setReserveDate(null);
					cedb1000.setUpdateIdNo( getLoginUserId() ); 
					cedb1000.setUpdateDate( Datetime.getYYYMMDD() );
					cedb1000.setUpdateTime( Datetime.getHHMMSS() );
					cedb1000.setApproveMark( getProcessType() );
					cedb1000.setRefundNo( getRefundNo() );
					Eedb1000 eedb1000 = null;
			        boolean eedbFlag = false;
			        if ( !"".equals(Common.get(cedb1000.getTelixNo())) && "E".equals(status) ) {
			        	if ( Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS")) {
			        		// 介接回寫一站式
			        		ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(cedb1000.getPrefixNo(), getLoginUserId());
			        	}else {
			        		// 回寫公司線上申辦
			        		eedb1000 = ServiceGetter.getInstance().getPre3005Service().getEedb1000ByTelixNo(cedb1000.getTelixNo());
			        		if(eedb1000 != null){
			            		eedb1000.setApproveResult("5");
			            		eedb1000.setProcessStatus("ZZ");
			            		eedb1000.setApproveDate(Datetime.getYYYMMDD());
			            		eedb1000.setApproveTime(Datetime.getHHMMSS());
			            		eedb1000.setApproveMail("");
			            		eedbFlag = true;
			        		}
			        	}
			        }
			        //備份
			        ServiceGetter.getInstance().getBackupService().doBackup( cedb1000.getPrefixNo(), getLoginUserId());
			        //存檔
					ServiceGetter.getInstance().getPre3005Service().doSaveFor03( cedb1000, eedb1000, prefixReceiptNo, receiptNoSetup, status, eedbFlag ) ;	
					//介接投審會
					ServiceGetter.getInstance().getMoeaicApproveService().notifyMoeaic(cedb1000.getPrefixNo());
					this.setState("updateSuccess");
				} else {
					//備份
			        ServiceGetter.getInstance().getBackupService().doBackup( cedb1000.getPrefixNo(), getLoginUserId());
			        //存檔
					ServiceGetter.getInstance().getPre3005Service().doSaveFor04( cedb1000, prefixReceiptNo, receiptNoSetup, status ) ;	
					//介接投審會
					ServiceGetter.getInstance().getMoeaicApproveService().notifyMoeaic(cedb1000.getPrefixNo());
					this.setState("updateSuccess");
				}
			} else {
				if ( "01".equals(getProcessType()) ) {
					if ( "".equals(Common.get(getPrePrefixNo())) ){
						this.setErrorMsg("預查編號太短或未填寫 (PREFIX NO ERROR)");
						return;
					}
					cedb1000.setRemark( Datetime.getYYYMMDD() + "因檢還" + userName + "修改核覆結果:" + getPrePrefixNo());
					status = PrefixConstants.PREFIX_STATUS_9 ;
				}else if ( "02".equals(getProcessType()) ) {
					cedb1000.setRemark( Datetime.getYYYMMDD() +"因撤件"+ userName + "修改核覆結果:" + getPrePrefixNo());
					status = PrefixConstants.PREFIX_STATUS_A ;
					
					if (getNoPayMark().startsWith("已免繳一次")) {
						ServiceGetter.getInstance().getNoPayMarkService().updateNotUsed(banNo);
					}
				}
				
				cedb1000.setReserveDate(null);
				cedb1000.setPrefixStatus(status);
				cedb1000.setCompanyName("");
				cedb1000.setRegDate("");
				cedb1000.setRegUnit("");
				cedb1000.setCompanyStus("");
				cedb1000.setApproveResult("N");
				cedb1000.setReserveDate(null);
				cedb1000.setUpdateIdNo( getLoginUserId() ); 
				cedb1000.setUpdateDate( Datetime.getYYYMMDD() );
				cedb1000.setUpdateTime( Datetime.getHHMMSS() );
				cedb1000.setApproveMark( getProcessType() );
				cedb1000.setRefundNo( getRefundNo() );
				
		        Eedb1000 eedb1000 = null;
		        boolean eedbFlag = false;
		        
		        if ( !"".equals(Common.get(cedb1000.getTelixNo())) && ("A".equals(status) || "E".equals(status)) ) {
		        	if ( Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS")) {
		        		// 介接回寫一站式
		        		ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(cedb1000.getPrefixNo(), getLoginUserId());
		        	} else {
		        		// 回寫公司線上申辦
		        		eedb1000 = ServiceGetter.getInstance().getPre3005Service().getEedb1000ByTelixNo(cedb1000.getTelixNo());
		        		if(eedb1000 != null){
		            		eedb1000.setApproveResult("5");
		            		eedb1000.setProcessStatus("ZZ");
		            		eedb1000.setApproveDate(Datetime.getYYYMMDD());
		            		eedb1000.setApproveTime(Datetime.getHHMMSS());
		            		eedb1000.setApproveMail("");
		            		eedbFlag = true;
		        		}
		        	}
		        }
		        //備份
		        ServiceGetter.getInstance().getBackupService().doBackup( cedb1000.getPrefixNo(), getLoginUserId());
		        //存檔
				ServiceGetter.getInstance().getPre3005Service().doSaveFor0102( cedb1000, eedb1000, status, eedbFlag ) ;	
				//介接投審會
				ServiceGetter.getInstance().getMoeaicApproveService().notifyMoeaic(cedb1000.getPrefixNo());
				//智慧型預查回傳預查審核結果
				ServiceGetter.getInstance().getApproveService().doPreSearchVerified(cedb1000.getPrefixNo());
				this.setState("updateSuccess");
			}
		}catch (Exception e) {
			e.printStackTrace();
	        this.setState("updateError");
			this.setErrorMsg("更新失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
	}

	private void getApplyName(Cedb1000 cedb1000, Cedb1023 cedb1023, StringBuilder applyName, StringBuilder applyAddr) {
		try {
			Cedb1022 cedb1022 = ServiceGetter.getInstance().getPre3005Service().selectCedb1022ByPrefixNo(cedb1000.getPrefixNo());
			if ("0".equals(cedb1023.getChangeType())) {
				if (cedb1022 != null && !"".equals(Common.get(cedb1022.getApplyLawName()))) {
					if (!"".equals(Common.get(cedb1000.getAttorName()))) {
						applyName.append(cedb1022.getApplyLawName() + "　代理人：" + cedb1000.getAttorName());
						applyAddr.append(cedb1000.getAttorAddr());
					} else {
						if (cedb1000.getApplyName().indexOf(cedb1022.getApplyLawName()) >= 0) {
							applyName.append(cedb1000.getApplyName());
						} else {
							applyName.append(cedb1022.getApplyLawName() + "　代表人：" + cedb1000.getApplyName());
						}
						applyAddr.append(cedb1000.getApplyAddr());
					}
				} else {
					if (!"".equals(Common.get(cedb1000.getAttorName()))) {
						applyName.append(cedb1000.getApplyName() + "　代理人：" + cedb1000.getAttorName());
						applyAddr.append(cedb1000.getAttorAddr());
					} else {
						applyName.append(cedb1000.getApplyName());
						applyAddr.append(cedb1000.getApplyAddr());
					}
				}
			} else {
				String oldCompanyName = Common.get(cedb1000.getOldCompanyName());
				String tempName = "";
				if(CommonStringUtils.isNotEmpty(oldCompanyName))
					tempName = oldCompanyName;
				else {
					String banNo = Common.get(cedb1000.getBanNo());
					if(CommonStringUtils.isNotEmpty(banNo)) {
						try {
							Cedb2000 cedb2000 = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(banNo);
							if (cedb2000 != null) {
								tempName = cedb2000.getCompanyName();
							}
						} catch(Exception e) {
						}
					}
				}
				if (!"".equals(Common.get(cedb1000.getAttorName()))) {
					applyName.append(tempName + "　代理人：" + cedb1000.getAttorName());
					applyAddr.append(cedb1000.getAttorAddr());
				} else {
					if (cedb1022 != null
							&& !"".equals(Common.get(cedb1022.getApplyLawName()))
							&& tempName.indexOf(cedb1022.getApplyLawName()) >= 0) {
						applyName.append(tempName);
					} else {
						applyName.append(tempName + "　代表人：" + cedb1000.getApplyName());
					}
					applyAddr.append(cedb1000.getApplyAddr());
				}
			}
			
			System.out.println("applyname:"+applyName);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public File doPrintPdf() throws Exception {
		File report = null ;
		try {
			Map<String, Object> parameters = new HashMap<String,Object>();
			String jasperPath = "";
			String imageAccountantPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/accountant.png");
			String imageCashierPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/cashier.png");
			String imageDirectorPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/director.png");
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( getPrefixNo() ) ;
			Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo( getPrefixNo() ) ;
			if ( cedb1000 == null ) {
				setErrorMsg( "查無預查資料，無法列印退還書" ) ;
			}else {
				CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
				Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
				if ("預查科".equals(cedbc000.getStaffUnit())) {
					jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre3005_tco.jasper");
					StringBuilder applyName = new StringBuilder("");
					StringBuilder applyAddr = new StringBuilder("");
					PrefixReceiptNo prefixReceiptNo = ServiceGetter.getInstance().getPre3005Service().selectPrefixReceiptNoByPrefixNo(cedb1000.getPrefixNo());
					String returnType = "";
					if ("01".equals(prefixReceiptNo.getReturnType()))
						returnType = "檢還";
					else if ("02".equals(prefixReceiptNo.getReturnType()))
						returnType = "撤件";
					else if ("03".equals(prefixReceiptNo.getReturnType()))
						returnType = "撤回退費";
					else if ("04".equals(prefixReceiptNo.getReturnType())) 
						returnType = "退費";
					
					String remark = "";
					if (cedb1000.getTelixNo().startsWith("OSS")||cedb1000.getTelixNo().startsWith("OSC")) 
						remark = "線上申辦";
					
					getApplyName(cedb1000, cedb1023,  applyName, applyAddr);
					parameters.put("printDate", "中華民國"+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印時間
					parameters.put("amount", prefixReceiptNo.getAmount());
					parameters.put("payDate", prefixReceiptNo.getPayDate());
					parameters.put("applyName", applyName.toString());
					parameters.put("applyAddr", applyAddr.toString());
					parameters.put("prefixNo", prefixReceiptNo.getPrefixNo());
					parameters.put("receiptNo", prefixReceiptNo.getReceiptNo());
					parameters.put("contactName", prefixReceiptNo.getRecipientName());
					parameters.put("contactAddr", prefixReceiptNo.getRecipientAddr());
					parameters.put("returnNo", prefixReceiptNo.getReturnNo()==null?"":"預查"+prefixReceiptNo.getReturnNo());
					parameters.put("returnType", returnType);
					parameters.put("remark", remark);
					parameters.put("staffName", user.getUserName());
					parameters.put("imageAccountantPath", imageAccountantPath);
					parameters.put("imageCashierPath", imageCashierPath);
					parameters.put("imageDirectorPath", imageDirectorPath);
				} else {
					jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre3005.jasper");
					Eedb1300 eedb1300 = null;
		        	if ( !"".equals(Common.get(cedb1000.getTelixNo())) && (cedb1000.getTelixNo().startsWith("OSC") || cedb1000.getTelixNo().startsWith("OSS") )) {
		        		// 一站式案件, 至ossmFeeMain撈取收據資料
						OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(cedb1000.getTelixNo());
						if(null!=ossmApplMain) {
							String processNo = "B";
						    if ( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals( ossmApplMain.getApplyType()) ) {
						    	processNo = "I";
						    }
			        		OssmFeeMain ossmFeeMainTmp = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(cedb1000.getTelixNo(),processNo);
			        		if (ossmFeeMainTmp != null) {
				        		eedb1300 = new Eedb1300();
			        			eedb1300.setTelixNo(ossmFeeMainTmp.getTelixNo());
			        			eedb1300.setReceiptNo(ossmFeeMainTmp.getReceiptNo());
			        			eedb1300.setAmount(Integer.toString(ossmFeeMainTmp.getAmount()));
			        			eedb1300.setPayDate(ossmFeeMainTmp.getPayDate());
			        			if ( "0".equals(cedb1023.getChangeType()) ) {
			        				// 103/10/03
			        				// 繳款人有以下幾種可能; 案由為變更時(名稱變更, 所營變更), 繳款人為"原公司名稱"
			        				// 否則, 繳款人為"申請人"
			        				// 但是若這件案件在一站式那邊當初是以法人身分申請, 則繳款人為當初在一站式網頁填的"法人公司名稱"
			        				// 假如找不到"法人公司名稱", 則暫時還是用cedb1000的"公司名稱"取代
			        				OssmOrgChange ossmOrgChange = ServiceGetter.getInstance().getPrefixService().getOssmOrgChangeByTelixNo(cedb1000.getTelixNo());
			        				if ( ossmOrgChange == null ) {
			        					eedb1300.setReceiptTitle(cedb1000.getApplyName());
			        				} // if
			        				else {
			        					if ( "2".equals(ossmOrgChange.getApplicantType())) {
			        						if ( ossmOrgChange.getCorpName() != null && !"".equals(ossmOrgChange.getCorpName()))
			        							eedb1300.setReceiptTitle(ossmOrgChange.getCorpName() + "　代表人："+ ossmOrgChange.getCorpApplyName());
			        						else 
			        							eedb1300.setReceiptTitle(cedb1000.getApplyName());
			        					} // if
			        					else {
			        						eedb1300.setReceiptTitle(cedb1000.getApplyName());
			        					} // if
			        				} // else
			        			} // if
			        			else
			        				eedb1300.setReceiptTitle(cedb1000.getOldCompanyName());
			        			eedb1300.setReceiptDate(ossmFeeMainTmp.getAccountDate());
			        			eedb1300.setReceiptNo(ossmFeeMainTmp.getReceiptNo());
		        				eedb1300.setPayType(TcfiView.getPayTypeDesc(ossmFeeMainTmp.getPayType()));
			        		}
						}
		        	}else {
		        		// 線上申辦, 從eedb1300中撈取資料
		        		eedb1300 = ServiceGetter.getInstance().getPrefixService().getEedb1300ByTelixNo(cedb1000.getTelixNo()) ;
		        	}

		        	if (eedb1300!=null) {
		        		String receiptNo = Common.get(eedb1300.getReceiptNo());
		        		if(!"".equals(receiptNo) && receiptNo.length() > 5){
		        			if(receiptNo.startsWith("J")) {
		        				receiptNo =  receiptNo.substring(1, 4) + "J字第" + receiptNo.substring(4) + "號";
		        			}else{
		        				receiptNo =  receiptNo.substring(1, 3) + receiptNo.substring(0, 1) + "字第" + receiptNo.substring(3) + "號";
		        			}
		        		}
			        	parameters.put("receiptNo", receiptNo)  ;
			        	parameters.put("telixNo", Common.get(eedb1300.getTelixNo()));
			        	parameters.put("refundNo", Common.get(cedb1000.getRefundNo()));
			        	parameters.put("payDate", Common.get(eedb1300.getPayDate()));
			        	parameters.put("accountDate", Common.get(eedb1300.getReceiptDate()));
			        	parameters.put("amount", Common.get(eedb1300.getAmount()));
			        	parameters.put("payerName", Common.get(eedb1300.getReceiptTitle()));
			        	parameters.put("payType", Common.get(eedb1300.getPayType()));
		        	}
				}
			}
			ArrayList<PRE3005> dataList = new ArrayList<PRE3005>();
			dataList.add(new PRE3005());
			report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
		}catch( Exception e ) {
			e.printStackTrace();
	        setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return report ;
	}
	
	public Object printDetail() throws Exception {
		PRE3005 pre3005 = this ;
		try{
			List<Map<String,Object>> rs1 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendSQL("02"));
			List<Map<String,Object>> rs2 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendSQL("03"));
			List<Map<String,Object>> rs3 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendSQL("01"));
			pre3005.setQueryResult1(Common.get( rs1.get(0).get( "sum" )));
			pre3005.setQueryResult2(Common.get( rs2.get(0).get( "sum" )));
			pre3005.setQueryResult3(Common.get( rs3.get(0).get( "sum" )));
		}catch (Exception e) {
			e.printStackTrace();
			this.setErrorMsg("查詢失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return pre3005;	
	}
	
	
	public String prefixErrorMsg( Cedb1000 cedb1000,PRE3005 pre3005 ) {
		if ( PrefixConstants.COMPANY_STATUS_01.equals( cedb1000.getCompanyStus() )
				|| PrefixConstants.COMPANY_STATUS_00.equals( cedb1000.getCompanyStus() )  )
            return "請注意，本案件尚在公司登記中";
        if ( PrefixConstants.COMPANY_STATUS_03.equals( cedb1000.getCompanyStus() ) )
            return "請注意，本案件已完成公司登記";
        // getPrePrefixNo() != null && 
        if ( "".equals(Common.get(pre3005.getReceiptNo()))) 
        	return "請注意，本案件繳費尚未過入帳日，無法取得收據資料!";
        String today = Datetime.getYYYMMDD();
        if (cedb1000.getReserveDate() != null && today.compareTo(cedb1000.getReserveDate()) > 0)
            return "請注意，本案件保留期限已過期，請檢查保留期限！";
        
        return "查詢成功";
	}
	
	public static String checkOutputRpt( String prefixNo ) {
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( prefixNo ) ;
		if ( cedb1000 == null )
			return "列印失敗，查無預查資料！";
		else {
			if ( "".equals(Common.get(cedb1000.getTelixNo())) )
				return "列印失敗，本案件繳費尚未過入帳日，無法取得收據資料!!";
			if ( cedb1000.getTelixNo().startsWith("OSC") || cedb1000.getTelixNo().startsWith("OSS") ) {
				// 一站式案件, 至ossmFeeMain撈取收據資料
				OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(cedb1000.getTelixNo());
				if(null!=ossmApplMain) {
					String processNo = "B";
					if ( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals( ossmApplMain.getApplyType()) ) {
						processNo = "I";
					}
					OssmFeeMain ossmFeeMainTmp = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(cedb1000.getTelixNo(),processNo) ;
					if ( ossmFeeMainTmp == null || "".equals(Common.get(ossmFeeMainTmp.getReceiptNo())) ) {
						return "列印失敗，本案件繳費尚未過入帳日，無法取得收據資料!!";
					}
				} else {
					return "列印失敗，本案件繳費尚未過入帳日，無法取得收據資料!!";
				}
	          }else {
	        	  Eedb1300 eedb1300 = ServiceGetter.getInstance().getPrefixService().getEedb1300ByTelixNo(cedb1000.getTelixNo()) ;
	        	  if ( eedb1300 == null || "".equals(Common.get(eedb1300.getReceiptNo())) ) {
	        		//  return "列印失敗，本案件繳費尚未過入帳日，無法取得收據資料!!";
	        	  }
	          }
	          return "ok";
		  } 
	} 
	
	public SQLJob appendSQL( String processType ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select count(1) as sum ");
		sqljob.appendSQL("from eicm.cedb1000 ");
		sqljob.appendSQL("where SUBSTR( remark, 1, 7 ) >= ");
		sqljob.appendSQL(Common.sqlChar(getQ_dateStart()));
		sqljob.appendSQL("	and SUBSTR( remark, 1, 7 ) <= ");
		sqljob.appendSQL(Common.sqlChar(getQ_dateEnd()));
		sqljob.appendSQL("	and approve_mark = ");
		sqljob.appendSQL(Common.sqlChar(processType));
		//sqljob.addParameter(getQ_dateStart());
		//sqljob.addParameter(getQ_dateEnd());
		//sqljob.addParameter(processType);
		return sqljob ;
	}
	
	public File printRecipientList() throws Exception {
		File report = null ;
		try {
			Map<String, Object> parameters = new HashMap<String,Object>();
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre3005_2.jasper");
			List<PrefixReceiptNo> receiptList = ServiceGetter.getInstance().getPre3005Service().selectPrefixReceiptNoByReturnDate(Datetime.getYYYYMMDDFromRocDate(getQ_returnDateStart()), Datetime.getYYYYMMDDFromRocDate(getQ_returnDateEnd()));
			if (receiptList == null || receiptList.size() == 0) {
				PrefixReceiptNo prefixReceiptNo = new PrefixReceiptNo();
				receiptList.add(prefixReceiptNo);
			}
				
			parameters.put("printDate", "中華民國"+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印時間
			parameters.put("returnDateStart", getQ_returnDateStart());
			parameters.put("returnDateEnd", getQ_returnDateEnd());
//			report = JasperReportMaker.makePdfReport(receiptList, parameters, jasperPath);// 2024/03/01修改，按客戶需求調整為word
			report = JasperReportMaker.makeDocReport(receiptList, parameters, jasperPath);
		}catch( Exception e ) {
			e.printStackTrace();
	        setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return report ;
	}

	public ArrayList<?> doQueryAll() throws Exception {return null ;}
	
	public void doCreate() throws Exception{}
	  
	public void doUpdate() throws Exception{}
	
	public void doDelete() throws Exception{}
}