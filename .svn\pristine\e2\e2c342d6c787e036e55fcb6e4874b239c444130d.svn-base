package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 所營項目輔助查詢
 *
 */
public class PRE3001_01 extends SuperBean {
	/** 所營項目關鍵字 */
	private String keyword;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" ITEM_CODE");
		sqljob.appendSQL(",BUSINESS_ITEM");
		sqljob.appendSQL("FROM BUSI_ITEM");
		sqljob.appendSQL("WHERE (LENGTH(ITEM_CODE) = 7)");
		if(!"".equals(getKeyword())) {
			sqljob.appendSQL("AND BUSINESS_ITEM LIKE ?");
			sqljob.addLikeParameter(getKeyword());
		}
		sqljob.appendSQL("ORDER BY ITEM_CODE");
		java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<Map<String,Object>> it = objList.iterator();
			Map<String,Object> o;
			String[] rowArray = new String[2];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[2];
				rowArray[0] = Common.get(o.get("ITEM_CODE"));
				rowArray[1] = Common.get(o.get("BUSINESS_ITEM"));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	public static String getQuerylist(ArrayList<?> objList, String queryAllFlag) {
    	boolean even = false;
    	StringBuilder sb = new StringBuilder();
    	if (objList!=null && objList.size()>0) {
			String rowArray[] = new String[2];
			java.util.Iterator<?> it = objList.iterator();

			while(it.hasNext()) {
				rowArray= (String[])it.next();
				String classTR="listTROdd", classTD = "listTDOdd";				
				if (even) {
					classTR = "listTREven";
					classTD = "listTDEven";
				}				
				//顯示TR
				sb.append("<tr");
				sb.append(" class='").append(classTR).append("'");
				sb.append(" onmouseover=\"this.className='listTRMouseover'\"");
				sb.append(" onmouseout=\"this.className='").append(classTR).append("'\"");
				sb.append(" >\n");
				sb.append("<td><input type=\"button\" class=\"toolbar_default\" value=\"插入\" onclick=\"addItem('").append(Common.get(rowArray[0])).append("','").append(Common.get(rowArray[1])).append("');\"/></td>\n");
				sb.append("<td class='").append(classTD).append("' style=\"text-align:left\">").append(Common.get(rowArray[0])).append("</td>\n");
				sb.append("<td class='").append(classTD).append("' style=\"text-align:left\">").append(Common.get(rowArray[1])).append("</td>\n");
				sb.append("</tr>\n");
				even = !even;
			}
    	} else {
    		if ("true".equals(queryAllFlag)) sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>").append("查無資料，請您重新輸入查詢條件！").append("</td></tr>");
    	}
		return sb.toString();
    }    

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getKeyword() {return checkGet(keyword);}
	public void setKeyword(String s) {this.keyword = checkSet(s);}

}