package com.kangdainfo.tcfi.view.pre;

/*
程式目的：核准領件編號維護作業
程式代號：pre2004
撰寫日期：103.06.24
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.ekera.presearch.Examine;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;

public class PRE2004 extends SuperBean {
	private Logger logger = Logger.getLogger(this.getClass());
	
	private String q_prefixStart; // 核覆日期起
	private String q_prefixEnd; // 核覆日期迄

	private String[] beChecked;

	// ----------------------------------getters and setters of local variable bellow ---------------------------
	public String getQ_prefixStart() {return checkGet(q_prefixStart);}
	public void setQ_prefixStart(String s) {q_prefixStart = checkSet(s);}
	public String getQ_prefixEnd() {return checkGet(q_prefixEnd);}
	public void setQ_prefixEnd(String s) {q_prefixEnd = checkSet(s);}  
	public String[] getBeChecked() {return beChecked;}
	public void setBeChecked(String[] s) {beChecked = s;}
	// ----------------------------------function never used bellow----------------------------------------------
	public Object doQueryOne() throws Exception {
		PRE2004 obj = this;
		//預設起號
		obj.setQ_prefixStart(Common.get(ServiceGetter.getInstance().getPre2004Service().queryStartNo()));
		//預設迄號
		obj.setQ_prefixEnd(Common.get(ServiceGetter.getInstance().getPre2004Service().queryEndNo()));
		if( "".equals(obj.getQ_prefixEnd()) ) {
			obj.setQ_prefixEnd(obj.getQ_prefixStart());
		}
		return obj;
	}

	public void doUpdate() throws Exception {
		// 1.檢核
		if (null == beChecked || beChecked.length == 0)
			throw new MoeaException("請先勾選要修改的預查編號");

		String maxPrefixNo = "";
		String prefixNo = "";
		String error = "";
		StringBuffer errorSb = new StringBuffer("");
		String loginUserId = getLoginUserId();
		if( "".equals(loginUserId) ) {
			throw new MoeaException("請重新登入系統");
		}

		for (int i = 0; i < beChecked.length; i++) {
			error = "";
			prefixNo = Common.get(beChecked[i]);
			//備份主檔
			ServiceGetter.getInstance().getBackupService().doBackup(prefixNo, loginUserId);
			//結案
			error = ServiceGetter.getInstance().getPre2004Service().doCloseCase(prefixNo, loginUserId);
			if( "".equals(error) ) {
				//沒錯的才會結案, 故才要算進 最大的結案編號
				if (maxPrefixNo.compareTo(prefixNo) < 0)
					maxPrefixNo = prefixNo;
				//寫入歷程
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(prefixNo, loginUserId, PrefixConstants.PREFIX_STATUS_8);
				//智慧型預查回傳預查審核結果
				ServiceGetter.getInstance().getApproveService().doPreSearchVerified(prefixNo);
			} else {
				if( !"".equals(errorSb.toString()) ) errorSb.append(",");
				errorSb.append(prefixNo).append(":").append(error);
			}
		}

		// 紀錄現況已核准領件的最大號
		if( !"".equals(maxPrefixNo) ) {
			ServiceGetter.getInstance().getPre2004Service().saveCedb1021(maxPrefixNo, loginUserId);
		}
		
		if( !"".equals(errorSb.toString()) ) {
			throw new MoeaException(errorSb.toString());
		}
	}

	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>();
		List<PRE2004> list = ServiceGetter.getInstance().getPre2004Service().queryPre2004s(getQ_prefixStart(), getQ_prefixEnd());
		String[] rowArray = new String[8];
		if(null!=list && !list.isEmpty()) {
			for(PRE2004 o : list) {
				rowArray = new String[9];
				rowArray[0] = Common.get(o.getPrefixNo());
				rowArray[1] = TcfiView.getApplyWayByTelixNo(Common.get(o.getTelixNo()));
				rowArray[2] = Common.get(o.getTelixNo());
				rowArray[3] = Common.get(o.getChangeType());
				rowArray[4] = Common.get(o.getApplyName());
				rowArray[5] = Common.get(o.getAttorName());
				rowArray[6] = Common.get(o.getApplyDateTime());
				rowArray[7] = Common.get(o.getGetKind());
				rowArray[8] = Common.get(o.getAssignDateTime());
				dataList.add(rowArray);
			}
		}
		return dataList;
	} // doQueryAll()
	
	public static String checkRemark(String prefixNo) {
		return Common.get(ServiceGetter.getInstance().getPre2004Service().checkRemark(prefixNo));
	}

	public void doCreate() throws Exception {}
	public void doDelete() throws Exception {}
	
	/** 預查編號 */
	private String prefixNo;
	/** 網路收文號 */
	private String telixNo;
	/** 預查種類 */
	private String changeType;
	/** 申請人 */
	private String applyName;
	/** 代理人 */
	private String attorName;
	/** 申請時間 */
	private String applyDateTime;
	/** 領件方式 */
	private String getKind;
	/** 分文時間 */
	private String assignDateTime;

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}
	public String getChangeType() {return checkGet(changeType);}
	public void setChangeType(String s) {this.changeType = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {this.applyName = checkSet(s);}
	public String getAttorName() {return checkGet(attorName);}
	public void setAttorName(String s) {this.attorName = checkSet(s);}
	public String getApplyDateTime() {return checkGet(applyDateTime);}
	public void setApplyDateTime(String s) {this.applyDateTime = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {this.getKind = checkSet(s);}
	public String getAssignDateTime() {return checkGet(assignDateTime);}
	public void setAssignDateTime(String s) {this.assignDateTime = checkSet(s);}

}