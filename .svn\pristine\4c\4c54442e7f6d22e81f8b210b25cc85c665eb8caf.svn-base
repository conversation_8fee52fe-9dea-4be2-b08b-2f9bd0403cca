<!-- 
程式目的：收文預查編號年度統計表
程式代號：pre1007
程式日期：1030528
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->

<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1007">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1007" />
</jsp:include>

<%
if ( "update".equals(obj.getState()) ) {
	obj.update() ;
} // end if
else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE1007)obj.queryOne();
	// obj.queryAll() ;
} // end else if
else if ( "print".equals(obj.getState()) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		session.setAttribute("PRE1007_MSG", "請等候報表輸出...");
		obj.outputFile(response, report, "PRE1007.pdf");
		session.setAttribute("PRE1007_MSG", "執行成功");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_year,"年度");
	alertStr += checkYYY(form1.q_year,"");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("print") ;
				var target = 'PRE1007_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				break;
			case "doClear":
				form1.q_year.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var year = form1.q_year.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre1007.jsp?year='+year, "");
		if ( x == 'ok'  ) {
			document.getElementById("ERRMSG").innerHTML = "請等候報表輸出...";
			whatButtonFireEvent("doPrintPdf");
			setTimeout("checkProcessStatus()", 3000);//等3秒
		} // if
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}

function checkProcessStatus() {
	document.getElementById("ERRMSG").innerHTML = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre1007Msg.jsp', "");;
}

function keyDown() {
  if (event.keyCode==13) {
	  doSomeCheck();
  }
}

</script>
</head>

<body topmargin="5" >
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1007'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- Query Area  -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="10%">年度：</td>
			<td class="td_form_white"> 
				<input class="field_Q" type="text" name="q_year" size="10" maxlength="3" value="<%=obj.getQ_year()%>" onKeyDown="keyDown()">
				<span id="addButtonSpan">
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				</span>
			</td>
		</tr>			
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
	<tr><td style="text-align:center;">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
	<!-- 新增按鈕區 -->
	</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>