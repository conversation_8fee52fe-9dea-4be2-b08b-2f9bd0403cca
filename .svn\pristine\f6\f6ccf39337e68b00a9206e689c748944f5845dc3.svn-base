<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.kangdainfo.sys.common.Constants"%>
<%@ page import="com.google.gson.*"%>
<%@ include file="../../home/<USER>"%>
<%
/**
 * 限專業經營
 */
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	Map<String,String> data = new HashMap<String,String>();
	String sql = "SELECT A.ITEM_CODE AS CODE, (SUBSTR(B.NAME,INSTR(B.NAME,'、',1)+1)) AS NAME FROM RESTRICTION_ITEM A LEFT JOIN RESTRICTION B ON A.RESTRICTION_ID = B.ID WHERE B.ENABLE='Y' ORDER BY 1";
	List<Map<String, Object>> list = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql);
	if (list != null && list.size() > 0) {
		java.util.Map<String, Object> obj;
		String code, name;
		String[] splits;
		for (int i = 0; i < list.size(); i++) {
			obj = (java.util.Map<String, Object>) list.get(i);
			code = Common.get(obj.get("CODE"));
			name = Common.get(obj.get("NAME"));
			data.put(code, name);
		}
	}
	Gson gson = new GsonBuilder().create();
	out.write(gson.toJson(data));
} catch (Exception e) {
	e.printStackTrace();
}
%>