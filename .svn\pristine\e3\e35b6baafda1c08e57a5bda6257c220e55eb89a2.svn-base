package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.LoginLog;

public class PRE9006 extends SuperBean {

	private String q_loginId;
	private String q_loginName;
	private String q_loginDateStart;
	private String q_loginDateEnd;
	
	public String getQ_loginId() {return checkGet(q_loginId);}
	public void setQ_loginId(String q_loginId) {this.q_loginId = checkSet(q_loginId);}
	public String getQ_loginName() {return checkGet(q_loginName);}
	public void setQ_loginName(String q_loginName) {this.q_loginName = checkSet(q_loginName);}
	public String getQ_loginDateStart() {return checkGet(q_loginDateStart);}
	public void setQ_loginDateStart(String q_loginDateStart) {this.q_loginDateStart = checkSet(q_loginDateStart);}
	public String getQ_loginDateEnd() {return checkGet(q_loginDateEnd);}
	public void setQ_loginDateEnd(String q_loginDateEnd) {this.q_loginDateEnd = checkSet(q_loginDateEnd);}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<LoginLog> objList = ServiceGetter.getInstance().getPrefixService().getLoginLog(
				getQ_loginId(), getQ_loginName(), getQ_loginDateStart(), getQ_loginDateEnd());
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<?> it = objList.iterator();
			LoginLog o;
			String[] rowArray = new String[5];
			while (it.hasNext()) {
				o = (LoginLog) it.next();
				rowArray = new String[5];
				rowArray[0] = Common.get(o.getLoginId());
				rowArray[1] = Common.get(o.getLoginName());
				rowArray[2] = Common.formatYYYMMDD(o.getLoginDate(), 4) + " " + Common.formatHHMMSS(o.getLoginTime());
				rowArray[3] = Common.get(o.getLoginIp());
				rowArray[4] = Common.get(o.getLoginStatus());
				arrList.add(rowArray);	
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	
	@Override
	public void doCreate() throws Exception {}

	@Override
	public void doUpdate() throws Exception {}

	@Override
	public void doDelete() throws Exception {}

}
