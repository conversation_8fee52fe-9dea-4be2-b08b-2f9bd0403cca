package com.kangdainfo.tcfi.service;

import java.util.List;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesDao;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesRcverDao;

public interface Pre8006Service {
	public DeclaratoryStatutesDao getDeclaratoryStatutesDao();
	public void setDeclaratoryStatutesDao(DeclaratoryStatutesDao dao);
	public DeclaratoryStatutesRcverDao getDeclaratoryStatutesRcverDao();
	public void setDeclaratoryStatutesRcverDao(DeclaratoryStatutesRcverDao dao);
	public int doInsert( DeclaratoryStatutes bo1, List<DeclaratoryStatutesRcver> list  );
	public int doUpdate( DeclaratoryStatutes oldBo1, DeclaratoryStatutes bo1, List<DeclaratoryStatutesRcver> list  );
	public int doDelete( DeclaratoryStatutes bo1  );
	public void doConvert();
	public void doConvertInstruction();
}