package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.PartNameLog;

public class PartNameLogDao extends BaseDaoJdbc implements RowMapper<PartNameLog> {

	private static final String SQL_findByBanNo = "SELECT * FROM PART_NAME_LOG WHERE BAN_NO = ?";
	public List<PartNameLog> findByBanNo(String banNo) {
		SQLJob sqljob = new SQLJob(SQL_findByBanNo);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<PartNameLog>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public void insert(PartNameLog bo) {
		//check
		if(null!=bo) {
			//insert
			SQLJob sqljob = new SQLJob("INSERT INTO PART_NAME_LOG (");
			sqljob.appendSQL(" BAN_NO");
			sqljob.appendSQL(",ID_NO");
			sqljob.appendSQL(",OLD_NAME");
			sqljob.appendSQL(",NEW_NAME");
			sqljob.appendSQL(",LOG_DATE");
			sqljob.appendSQL(",LOG_TIME");
			sqljob.appendSQL(") VALUES (?,?,?,?,?,?)");
			sqljob.addParameter(bo.getBanNo());
			sqljob.addParameter(bo.getIdNo());
			sqljob.addParameter(bo.getOldName());
			sqljob.addParameter(bo.getNewName());
			sqljob.addParameter(bo.getLogDate());
			sqljob.addParameter(bo.getLogTime());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public PartNameLog mapRow(ResultSet rs, int idx) throws SQLException {
		PartNameLog obj = null;
		if(null!=rs) {
			obj = new PartNameLog();
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setOldName(rs.getString("OLD_NAME"));
			obj.setNewName(rs.getString("NEW_NAME"));
			obj.setLogDate(rs.getString("LOG_DATE"));
			obj.setLogTime(rs.getString("LOG_TIME"));
		}
		return obj;
	}

}