<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/transitional.dtd">  
<!--
程式目的：一站式案件查詢
程式代號：PRE4020
撰寫日期：103.12.04
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4020" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4020">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE4020"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/> 
<SCRIPT LANGUAGE="JAVASCRIPT">
//for IE8 Array indexOf
if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = Array.prototype.indexOf || function(obj, start) {
		for (var i = (start || 0), j = this.length; i < j; i++) {
			if (this[i] === obj) {
				return i;
			}
		}
		return -1;
	};
}

var telixNos;
$(document).ready(function() {
	//畫面初始
	var t_telixNos = $('input[name="telixNos"]').map(function() {
		return $(this).val();
	}).get().join('-');
	telixNos = t_telixNos.match(/[^-]+/g) || [];

	//事件註冊
	//離開
	$('#sc_close').click(function(){
		form1.action = "pre4020.jsp";
		form1.state.value = "init";
		form1.submit();
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = telixNos.indexOf($("#telixNo").val());
		var pno = $("#telixNo").val();
		if(++currentIndex >= telixNos.length) {
			pno = telixNos[0];
		} else {
			pno = telixNos[currentIndex];
		}
		$("#telixNo").val(pno).trigger(jQuery.Event('keypress',{which: $.ui.keyCode.ENTER}));
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = telixNos.indexOf($("#prefixNo").val());
		var pno = $("#telixNo").val();
		if((currentIndex-1) < 0) {
			pno = telixNos[(telixNos.length-1)];
		} else {
			pno = telixNos[--currentIndex];
		}
		$("#telixNo").val(pno).trigger(jQuery.Event('keypress',{which: $.ui.keyCode.ENTER}));
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		var pno = telixNos[telixNos.length - 1];
		$("#telixNo").val(pno).trigger(jQuery.Event('keypress',{which: $.ui.keyCode.ENTER}));
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		var pno = telixNos[0];
		$("#telixNo").val(pno).trigger(jQuery.Event('keypress',{which: $.ui.keyCode.ENTER}));
	});
	//上層頁籤
	$("#tabs").tabs({active:0});

	$("#telixNo").keypress(function(e){
		if(e.which == 13) {
			$.post( getVirtualPath() + "tcfi/pre/pre4020_01.jsp?q=" + $("#telixNo").val(), function( data ) {
				if(!data)
					return;

				$("#tabs").tabs({active:0});
				$('#telixNo').val(data.ossmApplMain.telixNo);
				$('#current').val(data.ossmApplMain.telixNo);
				$('#prefixNo').html("").html(data.ossmApplMain.prefixNo);

				if('C1000'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('設立');
				else if('L1000'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('名稱及所營事業變更');
				else if('L1100'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('名稱變更');
				else if('L1010'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('所營事業變更');
				else if('L1110'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('名稱及所營事業變更');
				else if('L0100'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('線上審核');
				else if('L0010'==data.ossmApplMain.applyType)
					$('#applyType').html("").html('馬上辦');

				if('1'==data.ossmApplMain.writerType)
					$('#writerType').html("").html('申請人親自辦理');
				else if('2'==data.ossmApplMain.writerType)
					$('#writerType').html("").html('委託代理人');
				$('#userid').html("").html(data.ossmApplMain.userid);
				$('#banNo').html("").html(data.ossmApplMain.banNo);
				$('#applyName').html("").html(data.ossmApplMain.applyName);
				$('#applyId').html("").html(data.ossmApplMain.applyId);
				$('#applyAddr').html("").html(data.ossmApplMain.applyAddrComb);
				$('#attorName').html("").html(data.ossmApplMain.attorName);
				$('#attorId').html("").html(data.ossmApplMain.attorId);
				$('#attorNo').html("").html(data.ossmApplMain.attorNo);
				$('#attorTel').html("").html(data.ossmApplMain.attorTel);
				$('#attorAddr').html("").html(data.ossmApplMain.attorAddrComb);
				$('#contactName').html("").html(data.ossmApplMain.contactName);
				$('#contactTel').html("").html(data.ossmApplMain.contactTel);
				$('#contactEmail').html("").html(data.ossmApplMain.contactEmail);
				$('#contactCel').html("").html(data.ossmApplMain.contactCel);
				$('#contactAddr').html("").html(data.ossmApplMain.contactAddrComb);

				//公司名稱
				$('#ossmOrgNames tr').each(function (i, row) {
					if(i > 1) {$(row).remove(); }
				});
				for(var i=0; i<data.ossmOrgNames.length; i++) {
					addOssmOrgName((i%2==0)?"listTREven":"listTROdd", data.ossmOrgNames[i].seqNo, data.ossmOrgNames[i].orgName );
				}

				//營業項目
				$('#ossmBussItems tr').each(function (i, row) {
					if(i > 1) {$(row).remove(); }
				});
				for(var i=0; i<data.ossmBussItems.length; i++) {
					addOssmBussItem((i%2==0)?"listTREven":"listTROdd", data.ossmBussItems[i].seqNo, data.ossmBussItems[i].busiItemNo, data.ossmBussItems[i].busiItem );
				}
				//繳費紀錄
				if(data.ossmFeeMain) {
					$('#feeReceiptNo').html("").html(data.ossmFeeMain.receiptNo);
					$('#feeAmount').html("").html(data.ossmFeeMain.amount);
					$('#feePayDateTime').html("").html(commonUtils.formatTwDate(data.ossmFeeMain.payDate)+' '+commonUtils.formatTwTime(data.ossmFeeMain.payTime));
					if('1'==data.ossmFeeMain.payType)
						$('#feePayType').html("").html("信用卡");
					else if('2'==data.ossmFeeMain.payType)
						$('#feePayType').html("").html("金融帳戶");
					else if('3'==data.ossmFeeMain.payType)
						$('#feePayType').html("").html("晶片金融卡");
					$('#feeReceiptPrintDate').html("").html(data.ossmFeeMain.receiptPrintDate);
					if('Y'==data.ossmFeeMain.returnFlag)
						$('#feeReturnFlag').html("").html("已退費不印收據");
				}

				//案件流程
				$('#ossmApplFlows tr').each(function (i, row) {
					if(i > 1) {$(row).remove(); }
				});
				//馬上辦
				$('#atonceStatus').html("").html("未申請馬上辦");
				for(var i=0; i<data.ossmApplFlows.length; i++) {
					if('J'==data.ossmApplFlows[i].processNo) {
						if('000'==data.ossmApplFlows[i].processStatus)
							$('#atonceStatus').html("").html("<font color=red>馬上辦申請中，尚未送件</font>");
						else if('103'==data.ossmApplFlows[i].processStatus)
							$('#atonceStatus').html("").html("<font color=red>馬上辦已送件</font>");
					}
					
					//案件流程
					addOssmApplFlow((i%2==0)?"listTREven":"listTROdd"
							,data.ossmApplFlows[i].seqNo
							,data.ossmApplFlows[i].processNo
							,data.ossmApplFlows[i].processStatus
							,data.ossmApplFlows[i].updateTime
							,data.ossmApplFlows[i].updateUser);
				}

				showMsgBar('查詢成功!');
				return false;
			});
	    }
	});

	if( "" == $("#telixNo").val() ) {
		$("#telixNo").val(telixNos[0]).trigger(jQuery.Event('keypress',{which: $.ui.keyCode.ENTER}));
	}

});


function addOssmOrgName(tr_class, seqNo, orgName) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ orgName +'</td>';
	txt += '</tr>';
	$("#ossmOrgNames").append(txt);
}

function addOssmBussItem(tr_class, seqNo, busiItemNo, busiItem) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItemNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItem +'</td>';
	txt += '</tr>';
	$("#ossmBussItems").append(txt);
}

function addOssmApplFlow(tr_class, seqNo, processNo, processStatus, updateTime, updateUser) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ processNo +'</td>';
	txt += '<td style="text-align:left">'+ processStatus +'</td>';
	txt += '<td style="text-align:left">'+ updateTime +'</td>';
	txt += '<td style="text-align:left">'+ updateUser +'</td>';
	txt += '</tr>';
	$("#ossmApplFlows").append(txt);
}

</SCRIPT>
</head>
<body>
<form id="form1" name="form1" method="post">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE4020'/>
</c:import>

<table id="shareBar" width="100%" border="0" cellpadding="2" cellspacing="0">
	<tr>
		<td style="text-align: right;padding-right:15px;">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
				<c:param name="shortcut" value='N'/>
			</c:import>
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" />
		</td>
	</tr>
</table>

<table class="title_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<td class="title_form_label" style="width:100px" >網路流水號</td>
		<td class="title_form_value" style="width:150px" >
			<input type="text" class="field_RO" id="telixNo" name="telixNo" size="20" value="" readonly />
		</td>
		<td class="title_form_label" style="width:100px" >預查編號</td>
		<td class="title_form_value" style="width:150px" id="prefixNo" ></td>
		<td class="title_form_value">
			<table>
				<tr><td>
					<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
				</td>
				<td width="100" align="center">
					<input type="text" class="field_RO" id="current" name="current" size="20" value="" readonly />
 				</td>
				<td>
					<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
 				</td></tr>
			</table>
		</td>
	</tr>
</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1"><span>案件資料</span></a></li>
		<li><a href="#fragment-2"><span>公司名稱</span></a></li>
		<li><a href="#fragment-3"><span>營業項目</span></a></li>
		<li><a href="#fragment-4"><span>繳費紀錄</span></a></li>
		<li><a href="#fragment-5"><span>案件流程</span></a></li>
	</ul>
	<div id="fragment-1" style="padding-top:0px;padding-left:4px;">
		<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="4" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" style="width:120px">申辦類型</td>
				<td class="td_form_white" style="width:200px" id="applyType"></td>
				<td class="td_form" style="width:120px">辦理方式</td>
				<td class="td_form_white" id="writerType"></td>
			</tr>
			<tr>
				<td class="td_form">網路申請帳號</td>
				<td class="td_form_white" id="userid"></td>
				<td class="td_form">公司統編</td>
				<td class="td_form_white" id="banNo"></td>
			</tr>
			<tr>
				<td class="td_form">申請人姓名</td>
				<td class="td_form_white" id="applyName"></td>
				<td class="td_form">申請人身分ID</td>
				<td class="td_form_white" id="applyId"></td>
			</tr>
			<tr>
				<td class="td_form">申請人地址</td>
				<td class="td_form_white" id="applyAddr" colspan="3"></td>
			</tr>
			<tr>
				<td class="td_form">代理人姓名</td>
				<td class="td_form_white" id="attorName"></td>
				<td class="td_form">代理人身分ID</td>
				<td class="td_form_white" id="attorId"></td>
			</tr>
			<tr>
				<td class="td_form">代理人證書號碼</td>
				<td class="td_form_white" id="attorNo"></td>
				<td class="td_form">代理人聯絡電話</td>
				<td class="td_form_white" id="attorTel"></td>
			</tr>
			<tr>
				<td class="td_form">代理人地址</td>
				<td class="td_form_white" id="attorAddr" colspan="3"></td>
			</tr>
			<tr>
				<td class="td_form">聯絡人姓名</td>
				<td class="td_form_white" id="contactName"></td>
				<td class="td_form">聯絡電話</td>
				<td class="td_form_white" id="contactTel"></td>
			</tr>
			<tr>
				<td class="td_form">聯絡人信箱</td>
				<td class="td_form_white" id="contactEmail"></td>
				<td class="td_form">行動電話</td>
				<td class="td_form_white" id="contactCel"></td>
			</tr>
			<tr>
				<td class="td_form">聯絡人地址</td>
				<td class="td_form_white" id="contactAddr" colspan="3"></td>
			</tr>
			<tr>
				<td class="td_form">馬上辦申請狀態</td>
				<td class="td_form_white" id="atonceStatus" colspan="3"></td>
			</tr>
		</table>
	</div>
	<div id="fragment-2" style="padding-top:0px;padding-left:4px;">
		<TABLE id="ossmOrgNames" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="2" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" style="text-align: left;width:70px;">序號</td>
				<td class="td_form" style="text-align: left;">公司名稱</td>
			</tr>
		</TABLE>
	</div>
	<div id="fragment-3" style="padding-top:0px;padding-left:4px;">
		<TABLE id="ossmBussItems" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="3" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" style="text-align: left;width: 70px;">序號</td>
				<td class="td_form" style="text-align: left;width:100px;">代碼</td>
				<td class="td_form" style="text-align: left">營業項目</td>
			</tr>
		</table>
	</div>
	<div id="fragment-4" style="padding-top:0px;padding-left:4px;">
		<TABLE id="ossmFeeMain" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="4" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" style="width:120px;">收據號碼</td>
				<td class="td_form_white" style="width:200px;" id="feeReceiptNo"></td>
				<td class="td_form" style="width:120px;">繳費金額</td>
				<td class="td_form_white" id="feeAmount"></td>
			</tr>
			<tr>
				<td class="td_form">付款日期</td>
				<td class="td_form_white" id="feePayDateTime"></td>
				<td class="td_form">付款方式</td>
				<td class="td_form_white" id="feePayType"></td>
			</tr>
			<tr>
				<td class="td_form">收據列印日期</td>
				<td class="td_form_white" id="feeReceiptPrintDate"></td>
				<td class="td_form">備註</td>
				<td class="td_form_white" id="feeReturnFlag"></td>
			</tr>
		</TABLE>
	</div>
	<div id="fragment-5" style="padding-top:0px;padding-left:4px;">
		<TABLE id="ossmApplFlows" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="4" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" style="text-align: left;width: 70px;">序號</td>
				<td class="td_form" style="text-align: left;width:100px;">代碼</td>
				<td class="td_form" style="text-align: left">狀態</td>
				<td class="td_form" style="text-align: left">處理時間</td>
				<td class="td_form" style="text-align: left">處理人員</td>
			</tr>
		</TABLE>
	</div>
</div>

<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<%
if(null!=obj.getTelixNos()) {
	for(String n : obj.getTelixNos()) {
out.write("<input type='hidden' name='telixNos' value='"+n+"' />\n");
	}
}
%>
</form>
</body>
</html>