<%@page import="com.kangdainfo.sys.common.Constants"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.ekera.presearch.Examine"%>

<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		String xml = new Examine().searchPrefixResult(q);
		out.write(gson.toJson(xml));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>