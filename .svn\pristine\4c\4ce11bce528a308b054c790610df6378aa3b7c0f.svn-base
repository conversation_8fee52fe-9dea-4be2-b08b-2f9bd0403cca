
/**
 * Please modify this class to meet your needs
 * This class is not complete
 */

package tw.org.moea.online.oss;

import java.util.logging.Logger;

/**
 * This class was generated by Apache CXF 2.2.5
 * Mon Jun 17 16:05:27 CST 2013
 * Generated source version: 2.2.5
 * 
 */

@javax.jws.WebService(
                      serviceName = "CaseStatusService",
                      portName = "CaseStatusPort",
                      targetNamespace = "http://tw/org/moea/online/oss",
                      wsdlLocation = "http://127.0.0.1:8080/oss/ossws/UpdateCaseStatusService?wsdl",
                      endpointInterface = "tw.org.moea.online.oss.CaseStatus")
                      
public class CaseStatusImpl implements CaseStatus {

    private static final Logger LOG = Logger.getLogger(CaseStatusImpl.class.getName());

    /* (non-Javadoc)
     * @see tw.org.moea.online.oss.CaseStatus#updateCaseStatus(tw.org.moea.online.oss.UpdateCaseStatusBEAN  inBean )*
     */
    public java.lang.String updateCaseStatus(UpdateCaseStatusBEAN inBean) { 
        LOG.info("Executing operation updateCaseStatus");
        LOG.info((null==inBean?"null":inBean.toString()));
        try {
            java.lang.String _return = "";
            return _return;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
    }

}
