package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 備註歷史
 *
 */
public class PRE4001_02 extends SuperBean {

	private String prefixNo;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		if(!"".equals(getPrefixNo())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" A.PREFIX_NO");
			sqljob.appendSQL(",A.REMARK1");
			sqljob.appendSQL(",A.UPDATE_ID_NO");
			sqljob.appendSQL(",(SELECT STAFF_NAME FROM CEDBC000 WHERE ID_NO=A.UPDATE_ID_NO AND ROWNUM=1) AS UPDATE_NAME");
			sqljob.appendSQL(",A.UPDATE_DATE");
			sqljob.appendSQL(",A.UPDATE_TIME");
			sqljob.appendSQL("FROM CEDB1006 A");
			sqljob.appendSQL("WHERE A.PREFIX_NO=?");
			sqljob.addParameter(getPrefixNo());
			sqljob.appendSQL("AND A.REMARK1 IS NOT NULL");
			sqljob.appendSQL("ORDER BY A.UPDATE_DATE||A.UPDATE_TIME");
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[5];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[5];
					rowArray[0] = Common.get(o.get("PREFIX_NO"));
					rowArray[1] = Common.get(o.get("REMARK1"));
					rowArray[2] = Common.get(o.get("UPDATE_NAME"));
					rowArray[3] = Common.get(o.get("UPDATE_DATE"));
					rowArray[4] = Common.get(o.get("UPDATE_TIME"));
					arrList.add(rowArray);	
				}
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

}