package com.kangdainfo.moea.bo;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;


public class Form1110 {
	public Form1110() {}

	/**
	 * 線上申辦收文代辦案件數
	 */
	private String waiting;
	private String contactGetKind;
	private String applyType;
	private String docType;	// added by girlie 20080513 附件docType: 1-正本 2-副本
	private String caseCode;
	private String prefixNo;
	private String banNo;
	private String zoneCode;
	private String companyName;
	private String telixNo;
	private String processorName;
	private String receiveDate;
	private String receiveTime;
	private String applyName;
	private String applyId;
	private String attorName;
	private String attorId;
	private String attorNo;
	private String attorTel;
	private String attorAddr;
	private String applyCompanyName;
	private String seqNo;
	private String companyAddr;
	private String companyAddr2;
	private String attorAreaCode;
	private List<Cedb1001> applyCompanyNames;
	private List<Cedb1002> bussinessItems;
	private String contactName;
	private String contactAreaCode;
	private String contactTel;
	private String contactAddr;
	private String orgCorpNo;
	private String orgCorpName;
	private boolean originalflag = true;
	private String idNo;
	private String extRemitEname;

	private String noteString = "";
	private String isEicmEedbAlive="true";
	private boolean reissue = false; // added by KyLin 094.10.14
	// KyLin : SMS function extension
	private String contactCel;
	private String changeType;
	private boolean setup;
	private boolean changeName;
	private boolean changeItem;
	// KyLin : 線上申辦沒有抓聯絡人的村里
	private String contactVillage;
	//Added by BryanLin 20080416
	private String isPrefixForm;
	private String prefixFormNo;
	private String isOtherForm;
	private String isSpec;
	private boolean checkPrefixForm;
	private boolean checkOtherForm;
	private boolean checkSpec;
	//Added by BryanLin 20080418
	private String approveResult;
	private String approveRemark;
	//Added by BryanLin 20080429
	private String getKindRemark;
	private String isOtherSpec;
	private String otherSpecRemark;
	private boolean checkOtherSpec;
	private String noNeedPay;
	private String prefixStatus;
	private String withdrawDate;
	//added by girlie 20080625
	//公司名稱及所營事業登記預查申請表時 需要在 審查結果 列出 cedb1011 資料
	//cedb1011 資料如下列
	private String c1011ForeignMark; //外商註記
	private String c1011CompanyName; //合併消滅註記1
	private String c1011BanNo;	   //合併消滅註記2
	private String c1011ChinaMark; //大陸商註記
	private String printName;          //指定列印機
	private String applyWay;
	private String applyTel;
	private String tempString;
	private String takeMethod = ""; //2024/03/18 為了線上列印新增而設的參數，跟"自取"共享欄位
	private List<Cedb1002> bussinessItemsAfter5;
	

	// 判斷申請方式.
	public String getApplyWay() {
		if (this.telixNo == null || this.telixNo.length() == 0) {
			this.applyWay = "紙本送件";
		} else if (this.telixNo.startsWith("O")) {
			this.applyWay = "一站式";
		} else if (this.telixNo.startsWith("Z") || this.telixNo.startsWith("0")) {
			this.applyWay = "一維條碼";
		} else if (this.telixNo.startsWith("L")) {
			this.applyWay = "線上審核";
		} else if (this.telixNo.startsWith("A")) {
			this.applyWay = "線上申辦";
		} else {
			this.applyWay = "無法判斷";
		}
		
		return this.applyWay;
	}

	public String getPrintName() {return printName;}
	public void setPrintName(String printName) {this.printName = printName;}

	/**
	 * 取得線上申辦收文代辦案件數
	 */
	public String getWaiting() {return waiting;}

	/**
	 * 設定線上申辦收文代辦案件數
	 */
	public void setWaiting(String waiting) {this.waiting = waiting;}

	public String getApplyType() {return applyType;}
	public void setApplyType(String applyType) {this.applyType = applyType;}

	/**
	 * added by girlie 20080513 回覆字串於附件docType: 1-正本 2-副本
	 */
	public String getDocType() {return docType;}

	/**
	 * added by girlie 20080513 設定附件docType值: 1-正本 2-副本
	 */
	public void setDocType(String docType) {this.docType = docType;}

	public String getCaseCode() {return caseCode;}
	public void setCaseCode(String s) {this.caseCode = s;}

	public String getContactGetKind() {return contactGetKind;}
	public void setContactGetKind(String s) {this.contactGetKind = s;}

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}

	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}

	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String s) {this.zoneCode = s;}

	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}

	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String s) {this.telixNo = s;}

	public String getProcessorName() {return processorName;}
	public void setProcessorName(String s) {this.processorName = s;}

	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}

	public String getApplyName() {return applyName;}
	public void setApplyName(String s) {this.applyName = s;}

	public String getApplyId() {return applyId;}
	public void setApplyId(String s) {this.applyId = s;}

	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}

	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}

	public String getAttorTel() {return attorTel;}
	public void setAttorTel(String s) {this.attorTel = s;}

	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}

	public String getApplyCompanyName() {return applyCompanyName;}
	public void setApplyCompanyName(String s) {this.applyCompanyName = s;}


	public String getSeqNo() {return seqNo;}
	public void setSeqNo(String s) {this.seqNo = s;}

	public String getCompanyAddr() {return companyAddr;}
	public void setCompanyAddr(String s) {this.companyAddr = s;}

	public String getCompanyAddr2() {return companyAddr2;}
	public void setCompanyAddr2(String s) {this.companyAddr2 = s;}
	
	public String getAttorAreaCode() {return attorAreaCode;}
	public void setAttorAreaCode(String s) {this.attorAreaCode = s;}

	public String getContactName() {return contactName;}
	public void setContactName(String s) {this.contactName = s;}

	public String getContactAddr() {return contactAddr;}
	public void setContactAddr(String s) {this.contactAddr = s;}

	public String getContactAreaCode() {return contactAreaCode;}
	public void setContactAreaCode(String s) {this.contactAreaCode = s;}

	public String getOrgCorpName() {return orgCorpName;}
	public void setOrgCorpName(String s) {this.orgCorpName = s;}

	public String getOrgCorpNo() {return orgCorpNo;}
	public void setOrgCorpNo(String s) {this.orgCorpNo = s;}

	public String getReceiveTime() {return receiveTime;}
	public void setReceiveTime(String s) {this.receiveTime = s;}

	public List<Cedb1001> getApplyCompanyNames() {
		return applyCompanyNames;
	}

	public void setApplyCompanyNames(List<Cedb1001> applyCompanyNames) {
		this.applyCompanyNames = applyCompanyNames;
	}

	public List<Cedb1002> getBussinessItems() {
		return bussinessItems;
	}

	public void setBussinessItems(List<Cedb1002> bussinessItems) {
		this.bussinessItems = bussinessItems;
	}
	
	public List<Cedb1002> getBussinessItemsAfter5() {
		return bussinessItemsAfter5;
	}

	public void setBussinessItemsAfter5(List<Cedb1002> bussinessItemsAfter5) {
		this.bussinessItemsAfter5 = bussinessItemsAfter5;
	}

	public String getContactTel() {return contactTel;}
	public void setContactTel(String s) {this.contactTel = s;}

	public boolean isOriginalflag() {return originalflag;}
	public void setOriginalflag(boolean b) {this.originalflag = b;}

	public String getIdNo() {return idNo;}
	public void setIdNo(String s) {this.idNo = s;}

	public String getNoteString() {return noteString;}
	public void setNoteString(String s) {this.noteString = s;}

	public String getIsEicmEedbAlive() {return isEicmEedbAlive;}
	public void setIsEicmEedbAlive(String s) {this.isEicmEedbAlive = s;}

	public String getContactCel() {return contactCel;}
	public void setContactCel(String s) {this.contactCel = s;}

	public String getChangeType() {return changeType;}
	public void setChangeType(String s) {this.changeType = s;}

	public boolean isSetup() {return setup;}
	public void setSetup(boolean b) {this.setup = b;}

	public boolean isChangeName() {return changeName;}
	public void setChangeName(boolean b) {this.changeName = b;}

	public boolean isChangeItem() {return changeItem;}
	public void setChangeItem(boolean b) {this.changeItem = b;}

	public boolean isReissue() {return reissue;}
	public void setReissue(boolean b) {this.reissue = b;}

	public String getContactVillage() {return contactVillage;}
	public void setContactVillage(String s) {this.contactVillage = s;}

	public String getIsPrefixForm() {return isPrefixForm;}
	public void setIsPrefixForm(String s) {this.isPrefixForm = s;}

	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String s) {this.prefixFormNo = s;}

	public String getIsOtherForm() {return isOtherForm;}
	public void setIsOtherForm(String s) {this.isOtherForm = s;}

	public String getIsSpec() {return isSpec;}
	public void setIsSpec(String s) {this.isSpec = s;}

	public boolean isCheckPrefixForm() {return checkPrefixForm;}
	public void setCheckPrefixForm(boolean b) {this.checkPrefixForm = b;}

	public boolean isCheckOtherForm() {return checkOtherForm;}
	public void setCheckOtherForm(boolean b) {this.checkOtherForm = b;}

	public boolean isCheckSpec() {return checkSpec;}
	public void setCheckSpec(boolean b) {this.checkSpec = b;}

	public String getApproveRemark() {return approveRemark;}
	//public void setApproveRemark(String s) {this.approveRemark = s;}

	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String s) {this.approveResult = s;}

	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String s) {this.getKindRemark = s;}

	public String getIsOtherSpec() {return isOtherSpec;}
	public void setIsOtherSpec(String s) {this.isOtherSpec = s;}

	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = s;}

	public boolean isCheckOtherSpec() {return checkOtherSpec;}
	public void setCheckOtherSpec(boolean b) {this.checkOtherSpec = b;}

	public String getC1011ForeignMark() {return c1011ForeignMark;}
	public void setC1011ForeignMark(String s) {c1011ForeignMark = s;}

	public String getC1011CompanyName() {return c1011CompanyName;}
	public void setC1011CompanyName(String s) {c1011CompanyName = s;}

	public String getC1011BanNo() {return c1011BanNo;}
	public void setC1011BanNo(String s) {c1011BanNo = s;}

	public String getNoNeedPay() {return noNeedPay;}
	public void setNoNeedPay(String s) {this.noNeedPay = s;}

	public String getC1011ChinaMark() {return c1011ChinaMark;}
	public void setC1011ChinaMark(String s) {this.c1011ChinaMark = s;}

	public String getPrefixStatus() {return prefixStatus;}
	public void setPrefixStatus(String s) {this.prefixStatus = s;}

	public String getWithdrawDate() {return withdrawDate;}
	public void setWithdrawDate(String s) {this.withdrawDate = s;}

	public void setApplyWay(String applyWay) {
		this.applyWay = applyWay;
	}

	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}

	public String getAttorId() {return attorId;}
	public void setAttorId(String s) {this.attorId = s;}
	
	public String getTempString() {return tempString;}
	public void setTempString(String s) {this.tempString = s;}
	
	public String getExtRemitEname() {return this.extRemitEname;}
	public void setExtRemitEname(String s) {this.extRemitEname = s;}

	public String getTakeMethod() {//2024/03/18 為了線上列印新增而設的參數，跟"自取"共享欄位
		return takeMethod;//2024/03/18 為了線上列印新增而設的參數，跟"自取"共享欄位
	}

	public void setTakeMethod(String takeMethod) {//2024/03/18 為了線上列印新增而設的參數，跟"自取"共享欄位
		this.takeMethod = takeMethod;//2024/03/18 為了線上列印新增而設的參數，跟"自取"共享欄位
	}
}
