package com.kangdainfo.tcfi.view.pre;
/*
程式目的：自取案件處理
程式代號：pre2002
撰寫日期：103.05.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class PRE2002 extends SuperBean {
	
	private String q_prefixNo;
	
	private String applyName;
	private String applyId;
	private String tel;
	private String banNo;
	private String addr;
	private String companyName;
	
	private String receiveDate;
	private String receiveTime;
	private String approveDate;
	private String approveTime;
	private String closeDate;
	private String closeTime;
	private String getDate;
	private String getTime;
	private String approveResult;
	private String remark1;
	private String getKind;
	
	private String hiddenPrefixNo;
	
	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getApplyId() {return checkGet(applyId);}
	public void setApplyId(String s) {applyId = checkSet(s);}
	public String getTel() {return checkGet(tel);}
	public void setTel(String s) {tel = checkSet(s);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {banNo = checkSet(s);}
	public String getAddr() {return checkGet(addr);}
	public void setAddr(String s) {addr = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {receiveDate = checkSet(s);}
	public String getReceiveTime() {return checkGet(receiveTime);}
	public void setReceiveTime(String s) {receiveTime = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {approveDate = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {approveTime = checkSet(s);}
	public String getCloseDate() {return checkGet(closeDate);}
	public void setCloseDate(String s) {closeDate = checkSet(s);}
	public String getCloseTime() {return checkGet(closeTime);}
	public void setCloseTime(String s) {closeTime = checkSet(s);}
	public String getGetDate() {return checkGet(getDate);}
	public void setGetDate(String s) {getDate = checkSet(s);}
	public String getGetTime() {return checkGet(getTime);}
	public void setGetTime(String s) {getTime = checkSet(s);}
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {approveResult = checkSet(s);}
	public String getRemark1() {return checkGet(remark1);}
	public void setRemark1(String s) {remark1 = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {getKind = checkSet(s);}
	public String getHiddenPrefixNo() {return checkGet(hiddenPrefixNo);}
	public void setHiddenPrefixNo(String s) {hiddenPrefixNo = checkSet(s);}
	//---------------------------------------------------------------------------------------------------

	public void doUpdate() throws Exception {
		String prefixNo = getQ_prefixNo();
		if(null==getLoginUserId()) {
			setErrorMsg("存檔失敗，請重新登入系統");
		} else {
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(prefixNo);
			if ( cedb1000 == null ) {
				setErrorMsg("存檔失敗，查無案件資料");
			} else {
				if ( !PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())
						&& !PrefixConstants.APPROVE_RESULT_N.equals(cedb1000.getApproveResult()) ) {
					setErrorMsg( "案件未結案，無法發文" );
				} else if ( cedb1000.getCloseDate() == null || "".equals( cedb1000.getCloseDate() ) ) {
					setErrorMsg( "注意，案件尚未發文" );
				} else {
					//備份
					ServiceGetter.getInstance().getBackupService().doBackup(cedb1000.getPrefixNo(), getLoginUserId());
					//存檔
					cedb1000.setGetDate(Datetime.getYYYMMDD());
					cedb1000.setGetTime(Datetime.getHHMMSS());
					cedb1000.setUpdateDate(Datetime.getYYYMMDD());
					cedb1000.setUpdateTime(Datetime.getHHMMSS());
					cedb1000.setUpdateIdNo(getLoginUserId());
					cedb1000.setRemark1(getRemark1());
				    ServiceGetter.getInstance().getPrefixService().updateCedb1000GetDateTime(cedb1000);
				}
			}
		}
	}

	public Object doQueryOne() throws Exception{ 
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( getQ_prefixNo() ) ;
		PRE2002 pre2002 = this ;
		if ( cedb1000 == null ) {
            setErrorMsg( "查無資料，請變更查詢條件" );
            pre2002.setApplyName( "" ) ;
         	pre2002.setApplyId( "" )  ;
         	pre2002.setTel( "" )  ;
         	pre2002.setBanNo( "" )  ;
         	pre2002.setAddr( "" )  ;
         	pre2002.setCompanyName( "" )  ;
         	pre2002.setReceiveDate( "" )  ;
         	pre2002.setReceiveTime( "" )  ;
         	pre2002.setApproveDate( "" )  ;
         	pre2002.setApproveTime( "" ) ;
         	pre2002.setCloseDate( "" )  ;
         	pre2002.setCloseTime( "" )  ;
         	pre2002.setGetDate( "" )  ;
         	pre2002.setGetTime( "" ) ;
         	pre2002.setApproveResult( "")  ;
         	pre2002.setRemark1( "")  ;
         	pre2002.setGetKind( "")  ;
         	pre2002.setHiddenPrefixNo("");
      	    return pre2002 ;
        } // end if    
        else {
          if ( !"Y".equals( cedb1000.getApproveResult() ) && !"N".equals( cedb1000.getApproveResult() )  )  
        	setErrorMsg( "案件未結案無法發文" ) ;
            
          if ( cedb1000.getCloseDate() == null || "".equals( cedb1000.getCloseDate() ) ) {
              setErrorMsg( "注意！案件尚未發文" ) ;
          } //end if	
          pre2002.setHiddenPrefixNo(cedb1000.getPrefixNo());
       	  pre2002.setApplyName( cedb1000.getApplyName());
       	  pre2002.setApplyId( cedb1000.getApplyId());
       	  pre2002.setTel( cedb1000.getApplyTel());
       	  pre2002.setBanNo( cedb1000.getBanNo());
       	  pre2002.setAddr( cedb1000.getApplyAddr());
       	  pre2002.setCompanyName(cedb1000.getCompanyName());
       	  pre2002.setReceiveDate(Common.formatYYYMMDD(cedb1000.getReceiveDate(),4));
       	  pre2002.setReceiveTime(Common.formatHHMMSS(cedb1000.getReceiveTime(),1));
       	  pre2002.setApproveDate(Common.formatYYYMMDD(cedb1000.getApproveDate(),4));
       	  pre2002.setApproveTime(Common.formatHHMMSS(cedb1000.getApproveTime(),1));
       	  pre2002.setCloseDate(Common.formatYYYMMDD(cedb1000.getCloseDate(),4));
       	  pre2002.setCloseTime(Common.formatHHMMSS(cedb1000.getCloseTime(),1));
       	  pre2002.setGetDate(Common.formatYYYMMDD(cedb1000.getGetDate(),4));
       	  pre2002.setGetTime(Common.formatHHMMSS(cedb1000.getGetTime(),1));
       	  pre2002.setRemark1(cedb1000.getRemark1());
       	  pre2002.setGetKind(cedb1000.getGetKind());
       	  pre2002.setApproveResult(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(cedb1000.getApproveResult()));
       	  //寫入個資軌跡 (查詢類)
       	  ServiceGetter.getInstance().getTrackLogService().insertApplyPerson("PRE2002", PrefixConstants.TRACK_LOG_SEARCH,
       			cedb1000.getPrefixNo(), cedb1000.getApplyId(), cedb1000.getApplyName(), cedb1000.getApplyTel(), cedb1000.getApplyAddr());
       	  return pre2002;
		}
	}

	public ArrayList<?> doQueryAll() throws Exception {return null;}
	public void doCreate() throws Exception{}
	public void doDelete() throws Exception{}	
}