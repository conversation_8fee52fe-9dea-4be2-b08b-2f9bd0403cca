<!DOCTYPE html>
<!--
程式目的：同音同義字維護
程式代號：PRE8010
撰寫日期：103.04.28
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8010">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8010" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8010)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}else{
		obj.setQueryAllFlag("false");
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}else{
		obj.setQueryAllFlag("false");
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}


if ("true".equals(obj.getQueryAllFlag())) {
	objList = (java.util.ArrayList) obj.queryAll();
}
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		//alertStr += checkQuery();  
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.sameName1, "同音同義字");  
		alertStr += checkEmpty(form1.sameName, "同音同義字");
		alertStr += checkEmpty(form1.source, "字源"); 
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function init(){
	if (form1.state.value=="insertSuccess") {
		alert("請注意，同音同義字儲存後，將於明日正式生效。");
	}
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function f1() {
		if(confirm("同音同義字\""+form1.sameName.value+"\"與"+"\""+form1.sameName1.value+"\"將一併刪除，確定要繼續？")){
			beforeSubmit();	
			//genJSONString();
			form1.state.value = "delete";
			document.forms[0].submit();
		}
}
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<!--Query區============================================================-->
<div id="queryContainer" style="width:600px;height:100px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form" width="30%">同音同義字：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_sameName1" size="2" maxlength="1" value="<%=obj.getQ_sameName1()%>">
        </td>
    </tr>
	<tr>
        <td class="td_form" width="30%">字源：</td>
        <td class="td_form_white">
        	<input class="field_Q" type="text" name="q_source" size="25" maxlength="25" value="<%=obj.getQ_source()%>">
        </td>
    </tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="submit" name="querySubmit" value="確　　定" >
			<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8010'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<!--Toolbar區============================================================-->
<tr>
<td class="bgToolbar" style="text-align:left">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="true" id="delete_mine" name="delete_mine" value="刪　除" onClick="f1();">
	</span>
	<input class="field_P" type="hidden" id="id" name="id" value="<%=obj.getId()%>">	
	<input type="hidden" name="q_id" value="<%=obj.getQ_id()%>">	
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td>
</tr>

<!--Form區============================================================-->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
       <tr>
            <td nowrap class="td_form" width="20%"><font color="red">*</font>同音同義字：</td>
            <td nowrap class="td_form_white" width="80%">
              <input class="field" type="text" name="sameName1" size="2" maxlength="1" value="<%=obj.getSameName1()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form"><font color="red">*</font>同音同義字：</td>
            <td nowrap class="td_form_white">
              <input class="field" type="text" name="sameName" size="2" maxlength="1" value="<%=obj.getSameName()%>">
            </td> 
        </tr>   
        <tr>
            <td nowrap class="td_form"><font color="red">*</font>字源：</td>
            <td nowrap class="td_form_white">
              <input class="field" type="text" name="source" size="50" maxlength="50" value="<%=obj.getSource()%>">
            </td> 
        </tr>
		<tr>
		  	<td nowrap class="td_form">異動資訊：</td>
		  	<td nowrap class="td_form_white" > [
		    	<input class="field_RO" type="text" name="editID" size="10" value="<%=obj.getEditID()%>">
		    	/
		    	<input class="field_RO" type="text" name="editDate" size="7" value="<%=obj.getEditDate()%>">
		    	] 
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
		<tr>
	    <th class="listTH" ><a class="text_link_w">NO.</a></th>
	    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">同音同義字</a></th>
	    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">同音同義字</a></th>
	    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">字源</a></th>
	    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">異動人員</a></th>
	    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">異動日期</a></th>
		</tr>
	</thead>
	<tbody id="listTBODY">
	  <%
	  boolean primaryArray[] = {true, false, false, false, false, false};
	  boolean displayArray[] = {false, true, true, true, true, true};
	  String[] alignArray = {"center", "center", "center", "left", "center", "center"};
	  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag()));
	  %>
	</tbody>
</table>
</div>
</td></tr>

</table>	
</form>
</body>
</html>