package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.NumberFormat;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.BusiItem;

/*
程式目的：營業項目代碼維護
程式代號：pre8011
撰寫日期：103.06.11
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE8011 extends SuperBean {	
	
	private String q_itemCode ;			// 營業項目代碼
	private String q_businessItem ;		// 營業項目
	private String q_masterCode ;		// 特許事業許可主管機關
  
	private String itemCode ;        	// 營業項目代碼
	private String businessItem ;		// 營業項目
	private String masterCode ;			// 特許事業許可主管機關
	private String moeaPostDate;		// 經濟部公告日期
	private String moeaPostNo;			// 經濟部公告函號
	private String reserve365;			// 保留一年註記
	private String clickItemCode ;		// 專門用來處理點選清單內資料時的營業項目代碼
	private String[] idListWillChange ;	// 紀錄要更改的restrictionID
	
	// ----------------------------------getters and setters of local variable bellow ---------------------------
	public String getQ_itemCode() {return checkGet(q_itemCode);}
	public void setQ_itemCode(String s) {q_itemCode = checkSet(s);}
	public String getQ_businessItem() {return checkGet(q_businessItem);}
	public void setQ_businessItem(String s) {q_businessItem = checkSet(s);}
	public String getQ_masterCode() {return checkGet(q_masterCode);}
	public void setQ_masterCode(String s) {q_masterCode = checkSet(s);}
 
	public String getItemCode() {return checkGet(itemCode);}
	public void setItemCode(String s) {itemCode = checkSet(s);}
	public String getBusinessItem() {return checkGet(businessItem);}
	public void setBusinessItem(String s) {businessItem = checkSet(s);}
	public String getMasterCode() {return checkGet(masterCode);}
	public void setMasterCode(String s) {masterCode = checkSet(s);}
	public String getMoeaPostDate() {return checkGet(moeaPostDate);}
	public void setMoeaPostDate(String s) {moeaPostDate = checkSet(s);}
	public String getMoeaPostNo() {return checkGet(moeaPostNo);}
	public void setMoeaPostNo(String s) {moeaPostNo = checkSet(s);}
	public String getReserve365() {return checkGet(reserve365);}
	public void setReserve365(String s) {reserve365 = checkSet(s);}
	public String getClickItemCode() {return checkGet(clickItemCode);}
	public void setClickItemCode(String s) {clickItemCode = checkSet(s);}
	public String[] getIdListWillChange() {return idListWillChange;}
	public void setIdListWillChange(String s[]) {idListWillChange = s;}
	// ----------------------------------------------------------------------------------------------------------

	// ----------------------------------function never used bellow----------------------------------------------
	public void doCreate() throws Exception{	  
		// 1. 依使用者輸入之帳號置資料庫中查詢有無該筆資料
		// 2. 若該帳號已存在表重複輸入, 釋出錯誤訊息
		// 3. 若無則建立新物件obj, 將使用者所輸入之資訊存於此中
		// 4. 將obj丟回資料庫
		if("".equals(Common.get(getItemCode() ) ) ) 
			throw new MoeaException("營業項目代碼不可空白");
		
		if(getUpdateCheck()){
			  BusiItem obj = new BusiItem();
			  obj.setItemCode(getItemCode().toUpperCase()) ;
			  obj.setBusinessItem(getBusinessItem());
			  obj.setMasterCode(getMasterCode());
			  obj.setMoeaPostDate(getMoeaPostDate());
			  obj.setMoeaPostNo(getMoeaPostNo());   
			  obj.setReserve365(getReserve365());
			  obj.setModIdNo(getLoginUserId());
			  obj.setModDate(Datetime.getYYYMMDD());
			  obj.setModTime(Datetime.getHHMMSS()); 
			  obj = ServiceGetter.getInstance().getPrefixService().insertBusiItem(obj);
			  this.setClickItemCode(obj.getItemCode());
		}else{
			throw new MoeaException("已存在相同營業項目代碼的資料，請重新輸入！");
		}
	} // end doCreate()
	  
	public void doUpdate() throws Exception{
		// 建立新物件obj, 並將資料庫中相應之資料存入其中
		// 依使用者輸入之資訊更改obj之內容
		// 將obj丟回資料庫
		BusiItem o = ServiceGetter.getInstance().getPrefixService().queryBusiItemByItemCode(getClickItemCode());
		if(o == null)	throw new MoeaException("資料不存在!");
		if(getUpdateCheck()){
			  o.setBusinessItem(getBusinessItem());
			  o.setMasterCode(getMasterCode());
			  o.setMoeaPostDate(getMoeaPostDate());
			  o.setMoeaPostNo(getMoeaPostNo());   
			  o.setReserve365(getReserve365());
			  o.setModIdNo(getLoginUserId());
			  o.setModDate(Datetime.getYYYMMDD());
			  o.setModTime(Datetime.getHHMMSS());
			  ServiceGetter.getInstance().getPrefixService().saveBusiItem(o);
		}else{
			throw new MoeaException("已存在相同營業項目代碼的資料，請重新輸入！");
		}
  } // end doUpdate()		
	  
	public void doDelete() throws Exception{	
		BusiItem o = ServiceGetter.getInstance().getPrefixService().queryBusiItemByItemCode(getClickItemCode());
		if(o == null)	throw new MoeaException("資料不存在!");
		ServiceGetter.getInstance().getPrefixService().deleteBusiItem(o);
		this.setClickItemCode("");
	} // end doDelete()	
	  
	public Object doQueryOne() throws Exception{ 
		PRE8011 pre8011 = this;
		BusiItem busiItem = ServiceGetter.getInstance().getPrefixService().queryBusiItemByItemCode(getClickItemCode()) ;
		if(busiItem != null){
			pre8011.setClickItemCode(busiItem.getItemCode());
			pre8011.setItemCode(busiItem.getItemCode());
			pre8011.setBusinessItem(busiItem.getBusinessItem());
			pre8011.setMasterCode(busiItem.getMasterCode());
			pre8011.setReserve365(busiItem.getReserve365());
			pre8011.setMoeaPostDate(busiItem.getMoeaPostDate());
			pre8011.setMoeaPostNo(busiItem.getMoeaPostNo());
			pre8011.setEditID(busiItem.getModIdNo());
			pre8011.setEditDate(busiItem.getModDate());
		}else{
			pre8011.setClickItemCode("");
			this.setErrorMsg("查無該筆資料！");
		}
		return pre8011 ;
	} // end doQueryOne() 
	
	public ArrayList<String[]> doQueryAll() throws Exception {
		if("".equals(Common.get(getQ_itemCode())) && "".equals(Common.get(getQ_businessItem())) && "".equals(Common.get(getQ_masterCode()))) return null;
		List<BusiItem> itemList = ServiceGetter.getInstance().getPrefixService().queryAllBusiItem( getQ_itemCode().toUpperCase(), getQ_businessItem(), getQ_masterCode(), 7) ;
		ArrayList<String[]> dataList = new ArrayList<String[]>() ;
		if ( itemList != null && itemList.size() > 0) {
			java.util.Iterator<BusiItem> it = itemList.iterator();
			BusiItem o;
			String[] rowArray = new String[3];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[3] ;
				rowArray[0] = o.getItemCode() ;
				rowArray[1] = Common.get(o.getBusinessItem()) ;
				rowArray[2] = Common.get(o.getMasterCode()) ;
				dataList.add(rowArray);
			}
		}else{
			this.setErrorMsg( "查無營業項目代碼，請變更查詢條件" ) ;
		}
		return dataList ;
	} // doQueryAll()
  
	public ArrayList<String[]> queryAllRestriction() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>() ;
		try {
			BusiItem busiItem = ServiceGetter.getInstance().getPrefixService().queryBusiItemByItemCode(getItemCode());
			if(busiItem != null){
				this.setItemCode(busiItem.getItemCode());
				this.setBusinessItem(busiItem.getBusinessItem());
				String orgType = "";
				List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendSQL());
				if(rs != null && rs.size() > 0){
					String[] rowArray = new String[8];
					for(Map<String,Object> d:rs){
						rowArray = new String[8];
						rowArray[0] = Common.get(d.get("id"));
						rowArray[1] = Common.get(d.get("checkItem"));
						rowArray[2] = Common.get(d.get("name"));
						rowArray[3] = "Y".equals(Common.get(d.get("name_special")))?"是":"否";
						rowArray[4] = "Y".equals(Common.get(d.get("item_special")))?"是":"否";
						orgType = Common.get(d.get("org_type"));
						if("01".equals(orgType))	rowArray[5] = "公司";
						else if("02".equals(orgType))	rowArray[5] = "股份有限公司";
						else if("03".equals(orgType))	rowArray[5] = "限本國公司";
						else if("04".equals(orgType))	rowArray[5] = "限中華郵政股份有限公司";
						else if("05".equals(orgType))	rowArray[5] = "限中央存款保險股份有限公司 ";
						else rowArray[5] = "";
						rowArray[6] = "Y".equals(Common.get(d.get("enable")))?"開啟":"關閉";
						String[] strings = Common.get(d.get("name")).split("、");
						rowArray[7] = Integer.toString(NumberFormat.cnNumericToArabic(strings[0], false));
						dataList.add( rowArray );
					}
				}else{
					setErrorMsg( "查無營業項目限制條件!!" );
				}
			}else{
				setErrorMsg( "查無營業項目資料!!" ) ;
			}
		}catch( Exception e ) {
		  e.printStackTrace();
		  setErrorMsg("查詢失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		
		sort(dataList);
		return dataList;
	}
	
	public SQLJob appendSQL() throws Exception{
		SQLJob sqlJob = new SQLJob("select id, name, name_special, item_special, org_type, enable, ");
		sqlJob.appendSQL("(select case when count(1) > 0 then 'Y' else 'N' end from Restriction_Item where Restriction_Id = r.id and item_code = ?) as checkItem ");
		sqlJob.appendSQL("from Restriction r order by code");
		sqlJob.addParameter(getItemCode());
		return sqlJob;
	}
  
	/** 營業項目規則存檔 */
	public void doSaveRestriction() throws Exception{
		try {
			ServiceGetter.getInstance().getPre8011Service().doSave(getItemCode(), idListWillChange, getLoginUserId());
			setState("updateError");
			setErrorMsg("修改成功");
		}catch( Exception e ) {
			e.printStackTrace();
			setState("updateSuccess");
			setErrorMsg("修改失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
	} 
  	
	/** 組合自訂checkbox清單 */
	public static String getQuerylist(boolean primaryArray[], boolean displayArray[], String[] arrAlign,
			ArrayList<String[]> objList, String checkboxName) {
		
	  	StringBuilder sb = new StringBuilder();
	  	if (objList!=null && objList.size()>0) {
	  		String pk = "", checked = "", classTR = "", classTD = "";
	  		String rowArray[] = new String[primaryArray.length];
	  		java.util.Iterator<String[]> it = objList.iterator();
	  		int column = 0;
	  		while(it.hasNext()) {
	  			column++;
	  			rowArray = it.next();
	  			pk = rowArray[0];
	  			checked = rowArray[1];			
	  			if(column % 2 == 0){
	  				classTR = "listTREven";
	  				classTD = "listTDEven";
	  			}else{
	  				classTR = "listTROdd";
	  				classTD = "listTDOdd";
	  			}				

	  			//顯示TR
	  			sb.append(" <tr id=\"").append("listContainerRow").append(pk).append("\"");
	  			sb.append(" class='").append(classTR).append("' onmouseover=\"this.className='listTRMouseover'\" onmouseout=\"this.className='").append(classTR).append("'\" onClick=\"listContainerRowClick('").append(pk).append("');\" >\n");	
					
				//顯示TD
	  			sb.append(" <td class='listTD' >").append("<input type='checkbox' ").append(" id=\"").append(checkboxName).append("\" name=\"").append(checkboxName).append("\" value=\"").append(pk).append("\" ").append("Y".equals(checked)?"checked":"").append("></td>\n");
	  			for(int i=0;i<displayArray.length;i++){
	  				if (displayArray[i]){
	  					if (arrAlign!=null && arrAlign.length>0)
	  						sb.append(" <td class='").append(classTD).append("' style=\"text-align:").append(arrAlign[i]).append("\"").append(">");
	  					else
	  						sb.append(" <td class='").append(classTD).append("' >");
	  					if (i == rowArray.length-1)	{
	  						sb.append("<span style=\"display:none\">");
	  						sb.append(Common.checkGet(rowArray[i]));
	  						sb.append("</span>");
	  					}
	  					else {
	  						sb.append(Common.checkGet(rowArray[i]));
	  					} 
						sb.append("</td>\n");
	  				}
	  			}
	  			sb.append("</tr>\n");
	  		}
	  	} else {
	  		sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>").append("查無資料，請您重新輸入查詢條件！").append("</td></tr>");
	  	}
		return sb.toString();
	}
  
	/** 檢核 營業項目 是否重複 */
	protected boolean getUpdateCheck(){
		//getItemCode().toUpperCase()
		BusiItem o = ServiceGetter.getInstance().getPrefixService().queryBusiItemByItemCode(getItemCode().toUpperCase());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getItemCode().equals(this.getClickItemCode()))
				return true;
		}
		return false;
	}
	
	public List<String[]> sort(List<String[]> list) {
	// 重複N次
		for (int i = 0;i<list.size();i++) {
			boolean swapped = false;
		//比較到上一輪最後一個
			for (int j = 0;j<list.size()-1-i;j++) {
				String[] temp = new String[8];
				if ( Integer.parseInt(list.get(j)[7]) > Integer.parseInt(list.get(j+1)[7]) ) {
					for (int index = 0; index < 8; index++) {
						temp[index] = list.get(j)[index];
						list.get(j)[index] = list.get(j+1)[index];
						list.get(j+1)[index] = temp[index];
					}
					swapped  = true;
				} // if
			} // for
			if (!swapped) {
				break;
			} // if
		} // for
		
		return list;
	} // sort
} // PPE4008()
