package com.kangdainfo.tcfi.loader;

import java.util.List;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.LmsdRegUnit;
import com.kangdainfo.tcfi.model.lms.dao.LmsdRegUnitDao;

public class LmsdRegUnitLoader extends BaseLoader {
	private static final String CACHE_NAME = "CACHE_NAME_LMSD_REG_UNIT";
	
	private static LmsdRegUnitLoader Instance;
	
	private LmsdRegUnitDao lmsdRegUnitDao;
	
	public LmsdRegUnitDao getLmsdRegUnitDao() {
		return lmsdRegUnitDao;
	}

	public void setLmsdRegUnitDao(LmsdRegUnitDao lmsdRegUnitDao) {
		this.lmsdRegUnitDao = lmsdRegUnitDao;
	}

	public LmsdRegUnitLoader() {
		if(LmsdRegUnitLoader.Instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist: "
					+ LmsdRegUnitLoader.Instance);
		}
		LmsdRegUnitLoader.Instance = this;
	}
	
	public static LmsdRegUnitLoader getInstance() {
		return LmsdRegUnitLoader.Instance;
	}
	
	@SuppressWarnings("unchecked")
	public List<LmsdRegUnit> loadLmsdRegUnit() {
		if(getServletContext().getAttribute(CACHE_NAME) == null)
			reload();
		return (List<LmsdRegUnit>) getServletContext().getAttribute(CACHE_NAME);
	}
	
	public LmsdRegUnit getByRegUnitCode(String regUnitCode) {
		List<LmsdRegUnit> dataList = loadLmsdRegUnit();
		for(LmsdRegUnit data : dataList) {
			if(null != data.getRegUnitCode() && data.getRegUnitCode().equals(regUnitCode)){
				return data;
			}
		}
		return null;
	}
	
	public String getAgencyNameByRegUnitCode(String regUnitCode) {
		LmsdRegUnit data = getByRegUnitCode(regUnitCode);
		if(data != null) {
			return data.getAgencyName();
		}else {
			return "";
		}
	}

	@Override
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(CACHE_NAME, lmsdRegUnitDao.findAll());
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}

}
