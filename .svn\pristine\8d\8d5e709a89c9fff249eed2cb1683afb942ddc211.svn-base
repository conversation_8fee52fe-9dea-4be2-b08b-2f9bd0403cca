package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;

/**
 * 系統公告維護(PRE9005)
 *
 */
public interface Pre9005Service {
	/** 查詢 系統公告 (used by PRE9005) */
	public SystemNews getSystemNewsById(Integer id);
	/** 查詢 系統公告 (used by PRE9005) */
	public Integer countSystemNews(String id, String subject, String content, String startDate, String endDate);
	/** 查詢 系統公告 (used by PRE9005) */
	public List<SystemNews> querySystemNews(String id, String subject, String content, String startDate, String endDate);
	/** 新增 系統公告 (used by PRE9005) */
	public SystemNews insertSystemNews(SystemNews o);
	/** 修改 系統公告 (used by PRE9005) */
	public SystemNews updateSystemNews(SystemNews o);
	/** 刪除 系統公告 (used by PRE9005) */
	public void deleteSystemNewsById(Integer id);
}