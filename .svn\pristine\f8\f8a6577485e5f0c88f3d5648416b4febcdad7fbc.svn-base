<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q) && q != null) {
		
		//q=098004265*A220962513*0980206*095737-
		//098004265&#x2a;A220962513&#x2a;0980206&#x2a;095737-
		//ESAPI will replace * with &#x2a;
		String[] composite = q.split("-")[0].split("&#x2a;");

		String prefixNo = composite[0];
		String updateIdNo = composite[1];
		String updateDate = composite[2];
		String updateTime = composite[3];
		com.kangdainfo.tcfi.model.eicm.bo.PrefixLogVo vo = ServiceGetter.getInstance().getPre3007Service().findPrefixLog(prefixNo, updateIdNo, updateDate, updateTime);
		out.write(gson.toJson(vo));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>