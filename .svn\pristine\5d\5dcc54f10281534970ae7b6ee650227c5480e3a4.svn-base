package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Queue;

/**
 * 更新一站式案件狀態
 *
 */
public interface UpdateOsssStatusService {

	/**
	 * 新增
	 * @param prefixNo
	 * @param modIdNo
	 */
	public void insertQueue(String prefixNo, String modIdNo);

	/**
	 * 取得待執行的排程
	 * @return
	 */
	public Queue getToDoQueue();
	
	/**
	 * 執行同步
	 * 1.查詢以預查編號查詢 Cedb1000
	 *   1.1 沒資料時不處理, remark:查無cedb1000資料
	 *   1.2 有資料時, 判斷 Cedb1000.TELIX_NO
	 *      1.2.1 若 TELIX_NO 為O開頭, 表示為一站式案件, 繼續處理
	 *      1.2.2 若 TELIX_NO 不為O開頭, 表示非一站式案件, 不處理, remark:非一站式案件
	 *   1.3 有資料時, 判斷 Cedb1000.APPROVE_RESULT
	 *      1.3.1 若APPROVE_RESULT='Y' 或 APPROVE_RESULT='N' 表示已審核, 傳送狀態(103)案件處理完成
	 *           若一站式申請類別為(L0100)線上審核時, 傳送狀態(103)案件處理完成
	 *      1.3.2 若APPROVE_RESULT不為'Y'或'N'時, 判斷RCV_CHECK
	 *   1.4 判斷 Cedb1000.RCV_CHECK
	 *      1.4.1 若RCV_CHECK='Y' 表示已收文確認, 傳送狀態(102)案件審理中
	 *      1.4.2 若RCV_CHECK<>'Y' 表示已收文尚未收文確認, 傳送狀態(101)已接收資料
	 *           若一站式申請類別為(L0100)線上審核時, 傳送狀態(103)案件處理完成
	 * @param queue
	 */
	public void doSyncOsss(Queue queue);

	/**
	 * 更新執行失敗的排程,讓執行失敗的排程重做
	 */
	public void updateResetErrorQueue();

	/**
	 * 查詢執行失敗的排程
	 * @return List<Queue>
	 */
	public List<Queue> queryErrorQueues();

	/**
	 * 更新一站式資本總額
	 * @param prefixNo
	 */
	public void updateOrgCapitalAmt(String prefixNo);

}