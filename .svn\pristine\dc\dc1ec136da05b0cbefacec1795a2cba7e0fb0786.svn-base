package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1110;

public class Cedb1110Dao extends BaseDaoJdbc implements RowMapper<Cedb1110> {
	private static final String SQL_findAll = "SELECT * FROM CEDB1110";
	private static final String SQL_defaultOrder = "ORDER BY PREFIX_NO, PROCESS_DATE, PROCESS_TIME, PROCESS_STATUS";

	public String findProcessDateTime(String prefixNo, String processStatus) {
		String result = "";
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL("(");
		sqljob.appendSQL("case");
		sqljob.appendSQL("when nvl(process_date,' ') = ' ' then process_date");
		sqljob.appendSQL("when nvl(process_time,' ') = ' ' then process_time");
		sqljob.appendSQL("else");
		sqljob.appendSQL("substr(process_date,1,3)||'/'||substr(process_date,4,2)||'/'||substr(process_date,6,2)||'  '||substr(process_time,1,2)||':'||substr(process_time,3,2)||':'||substr(process_time,5,2)");
		sqljob.appendSQL("end");
		sqljob.appendSQL(") as RESULT");
		sqljob.appendSQL("FROM cedb1110");
		sqljob.appendSQL("WHERE prefix_no = ?");
		sqljob.appendSQL("AND process_status = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(processStatus);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Map<String,Object>> list = getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray());
		if(null!=list && !list.isEmpty()) {
			Map<String,Object> map = list.get(0);
			if(null!=map){
				result = (String)map.get("RESULT");
			}
		}
		if(logger.isDebugEnabled()) logger.debug("[findProcessDateTime][prefixNo:"+prefixNo+"][processStatus:"+processStatus+"][result:"+result+"]");
		return result;
	}

	public List<Cedb1110> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static String SQL_insert = "INSERT INTO Cedb1110 (PREFIX_NO,ID_NO,PROCESS_DATE,PROCESS_TIME,PROCESS_STATUS,WORK_DAY) "
			+ "VALUES (?, ?, ?, ?, ?, ?) ";
	public void insert(Cedb1110 cedb1110) {
		if(null!=cedb1110) {
			SQLJob sqljob = new SQLJob(SQL_insert);
			sqljob.addParameter(cedb1110.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1110.getIdNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1110.getProcessDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1110.getProcessTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1110.getProcessStatus());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1110.getWorkDay());
			sqljob.addSqltypes(java.sql.Types.FLOAT);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}

	@Override
	public Cedb1110 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1110 obj = null;
		if (null != rs) {
			obj = new Cedb1110();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setProcessDate(rs.getString("PROCESS_DATE"));
			obj.setProcessTime(rs.getString("PROCESS_TIME"));
			obj.setProcessStatus(rs.getString("PROCESS_STATUS"));
			obj.setWorkDay(rs.getFloat("WORK_DAY"));
		}
		return obj;
	}

}