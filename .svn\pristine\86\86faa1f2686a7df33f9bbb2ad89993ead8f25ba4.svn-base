package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmlCmpyTranMoeaic;

public class CsmlCmpyTranMoeaicDao
	extends BaseDaoJdbc
	implements RowMapper<CsmlCmpyTranMoeaic>
{
	private static final String SQL_findByPrefixNo = "SELECT * FROM CSML_CMPY_TRAN_MOEAIC WHERE PREFIX_NO = ?";
	public List<CsmlCmpyTranMoeaic> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<CsmlCmpyTranMoeaic>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public void insert(String prefixNo, String banNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("INSERT INTO CSML_CMPY_TRAN_MOEAIC (");
		sqljob.appendSQL("IDNTPK, PREFIX_NO, BAN_NO, UPD_CODE, UPD_DATE");
		sqljob.appendSQL(")");
		sqljob.appendSQL("select max(idntpk)+1,?,?,'1',sysdate from CSML_CMPY_TRAN_MOEAIC");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(banNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public CsmlCmpyTranMoeaic mapRow(ResultSet rs, int idx) throws SQLException {
		CsmlCmpyTranMoeaic obj = null;
		if(null!=rs) {
			obj = new CsmlCmpyTranMoeaic();
			obj.setIdntpk(rs.getLong("IDNTPK"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setUpdCode(rs.getString("UPD_CODE"));
			obj.setUpdDate(rs.getDate("UPD_DATE"));
			obj.setTransferDate(rs.getDate("TRANSFER_DATE"));
			obj.setStatus(rs.getString("STATUS"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setReceiveNo(rs.getString("RECEIVE_NO"));
			obj.setRespDate(rs.getString("RESP_DATE"));
			obj.setCaseCode(rs.getString("CASE_CODE"));
			obj.setCaseDesc(rs.getString("CASE_DESC"));
			obj.setMoeaicNo(rs.getString("MOEAIC_NO"));
			obj.setCmpyName(rs.getString("CMPY_NAME"));
			obj.setImpAmount(rs.getLong("IMP_AMOUNT"));
		}
		return obj;
	}

}