<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.google.gson.*"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	Gson gson = new GsonBuilder().create();
	List<Map<String,String>> maplist = obj.pagingSearch();
	out.write(gson.toJson(maplist));
} catch (Exception e) {
	e.printStackTrace();
}
%>