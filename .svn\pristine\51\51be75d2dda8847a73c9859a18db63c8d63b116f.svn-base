package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class ReceiveVo extends BaseModel {
	private static final long serialVersionUID = 1L;

	private String id;
	private String errmsg;
	private String onLineCnt;
	private String prefixNo;
	private String setup; // changeName, changeItem (checkbox)
	private String prefixType;
	private String telixNo;
	private String oldCompanyName;
	private String banNo;
	private String contactGetKind; // radio
	private String getKindRemark;
	private String applyType;
	private String receiveDate;
	private String checkPrefixForm;
	private String docType; // select
	private String prefixFormNo;
	private String checkOtherForm; // checkSpec, checkOtherSpec
	private String otherSpecRemark;
	private String applyName;
	private String applyId;
	private String applyAddr;
	private String applyTel;
	private String attorName;
	private String attorId;
	private String attorNo;
	private String attorAddr;
	private String receiveName;
	private String receiveId;
	private String receiveAddr;
	private String receiveTel;
	private String onlineDocNum;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getErrmsg() {
		return errmsg;
	}

	public void setErrmsg(String errmsg) {
		this.errmsg = errmsg;
	}

	public String getOnLineCnt() {
		return onLineCnt;
	}

	public void setOnLineCnt(String onLineCnt) {
		this.onLineCnt = onLineCnt;
	}

	public String getPrefixNo() {
		return prefixNo;
	}

	public void setPrefixNo(String prefixNo) {
		this.prefixNo = prefixNo;
	}

	public String getSetup() {
		return setup;
	}

	public void setSetup(String setup) {
		this.setup = setup;
	}

	public String getPrefixType() {
		return prefixType;
	}

	public void setPrefixType(String prefixType) {
		this.prefixType = prefixType;
	}

	public String getTelixNo() {
		return telixNo;
	}

	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}

	public String getOldCompanyName() {
		return oldCompanyName;
	}

	public void setOldCompanyName(String oldCompanyName) {
		this.oldCompanyName = oldCompanyName;
	}

	public String getBanNo() {
		return banNo;
	}

	public void setBanNo(String banNo) {
		this.banNo = banNo;
	}

	public String getContactGetKind() {
		return contactGetKind;
	}

	public void setContactGetKind(String contactGetKind) {
		this.contactGetKind = contactGetKind;
	}

	public String getGetKindRemark() {
		return getKindRemark;
	}

	public void setGetKindRemark(String getKindRemark) {
		this.getKindRemark = getKindRemark;
	}

	public String getApplyType() {
		return applyType;
	}

	public void setApplyType(String applyType) {
		this.applyType = applyType;
	}

	public String getReceiveDate() {
		return receiveDate;
	}

	public void setReceiveDate(String receiveDate) {
		this.receiveDate = receiveDate;
	}

	public String getCheckPrefixForm() {
		return checkPrefixForm;
	}

	public void setCheckPrefixForm(String checkPrefixForm) {
		this.checkPrefixForm = checkPrefixForm;
	}

	public String getDocType() {
		return docType;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	public String getPrefixFormNo() {
		return prefixFormNo;
	}

	public void setPrefixFormNo(String prefixFormNo) {
		this.prefixFormNo = prefixFormNo;
	}

	public String getCheckOtherForm() {
		return checkOtherForm;
	}

	public void setCheckOtherForm(String checkOtherForm) {
		this.checkOtherForm = checkOtherForm;
	}

	public String getOtherSpecRemark() {
		return otherSpecRemark;
	}

	public void setOtherSpecRemark(String otherSpecRemark) {
		this.otherSpecRemark = otherSpecRemark;
	}

	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}

	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}

	public String getApplyAddr() {
		return applyAddr;
	}

	public void setApplyAddr(String applyAddr) {
		this.applyAddr = applyAddr;
	}

	public String getApplyTel() {
		return applyTel;
	}

	public void setApplyTel(String applyTel) {
		this.applyTel = applyTel;
	}

	public String getAttorName() {
		return attorName;
	}

	public void setAttorName(String attorName) {
		this.attorName = attorName;
	}

	public String getAttorId() {
		return attorId;
	}

	public void setAttorId(String attorId) {
		this.attorId = attorId;
	}

	public String getAttorNo() {
		return attorNo;
	}

	public void setAttorNo(String attorNo) {
		this.attorNo = attorNo;
	}

	public String getAttorAddr() {
		return attorAddr;
	}

	public void setAttorAddr(String attorAddr) {
		this.attorAddr = attorAddr;
	}

	public String getReceiveName() {
		return receiveName;
	}

	public void setReceiveName(String receiveName) {
		this.receiveName = receiveName;
	}

	public String getReceiveId() {
		return receiveId;
	}

	public void setReceiveId(String receiveId) {
		this.receiveId = receiveId;
	}

	public String getReceiveAddr() {
		return receiveAddr;
	}

	public void setReceiveAddr(String receiveAddr) {
		this.receiveAddr = receiveAddr;
	}

	public String getReceiveTel() {
		return receiveTel;
	}

	public void setReceiveTel(String receiveTel) {
		this.receiveTel = receiveTel;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getOnlineDocCount() {
		return onlineDocNum;
	}

	public void setOnlineDocCount(String onlineDocCount) {
		this.onlineDocNum = onlineDocCount;
	}

}