--DROP TABLE EICM.FUNCTION_MENU;
-- Create table
CREATE TABLE EICM.FUNCTION_MENU (
	ID INTEGER not null,
	PID INTEGER,
	CODE VARCHAR2(20) not null,
	URL VARCHAR2(200),
	TITLE VARCHAR2(200),
	TARGET VARCHAR2(200),
	SORTED INTEGER,
	ENABLE CHAR(1),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);

-- Add comments to the table 
comment on table EICM.FUNCTION_MENU is '功能選單檔';
-- Add comments to the columns 
comment on column EICM.FUNCTION_MENU.ID is '主鍵值';
comment on column EICM.FUNCTION_MENU.PID is '上層選單鍵值';
comment on column EICM.FUNCTION_MENU.CODE is '功能代碼';
comment on column EICM.FUNCTION_MENU.URL is '功能路徑';
comment on column EICM.FUNCTION_MENU.TITLE is '功能標題';
comment on column EICM.FUNCTION_MENU.TARGET is '連結開啟目標';
comment on column EICM.FUNCTION_MENU.SORTED is '排序';
comment on column EICM.FUNCTION_MENU.ENABLE is '是否啟用(Y:啟動,N:停用 )';
comment on column EICM.FUNCTION_MENU.MOD_ID_NO is '異動人員';
comment on column EICM.FUNCTION_MENU.MOD_DATE is '異動日期';
comment on column EICM.FUNCTION_MENU.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.FUNCTION_MENU
  add constraint PK_FUNCTION_MENU primary key (ID)
  using index ;
-- Create/Recreate indexes 
create unique index EICM.UK_FUNCTION_MENU on EICM.FUNCTION_MENU (CODE);

-- Drop sequence
--DROP sequence EICM.SEQ_FUNCTION_MENU_ID;
-- Create sequence 
create sequence EICM.SEQ_FUNCTION_MENU_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_FUNCTION_MENU
Before Insert ON EICM.FUNCTION_MENU Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_FUNCTION_MENU_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.FUNCTION_MENU for EICM.FUNCTION_MENU;
create or replace synonym EICM4CMPY.FUNCTION_MENU for EICM.FUNCTION_MENU;
create or replace synonym EICM4PREFIX.FUNCTION_MENU for EICM.FUNCTION_MENU;

--GRANT
grant all on EICM.FUNCTION_MENU to EICM4AP;
grant all on EICM.FUNCTION_MENU to EICM4CMPY;
grant all on EICM.FUNCTION_MENU to EICM4PREFIX;

--DATA
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE1','預查收文管理',null,null,1000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE2','預查發文管理',null,null,2000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE3','預查審核管理',null,null,3000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE4','資料查詢列印',null,null,4000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE8','預查維護管理',null,null,5000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (null,'PRE9','系統管理',null,null,6000,'Y');
--PID=1
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1001','預查申請資料收文','../tcfi/pre/pre1001.jsp',null,1000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1003','預查案件收文確認','../tcfi/pre/pre1003.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1004','列印預查申請表','../tcfi/pre/pre1004.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1005','補列印回執聯','../tcfi/pre/pre1005.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1006','收文登打作業','../tcfi/pre/pre1006.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1007','收文預查編號年度統計表','../tcfi/pre/pre1007.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1008','收文預查編號月份統計表','../tcfi/pre/pre1008.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (1,'PRE1009','民眾申請案件統計表','../tcfi/pre/pre1009.jsp',null,1700,'Y');
--PID=2
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2001','發文登打作業','../tcfi/pre/pre2001.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2002','自取案件處理','../tcfi/pre/pre2002.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2003','郵寄掛號登錄及維護','../tcfi/pre/pre2003.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2004','預查核准領件編號維護','../tcfi/pre/pre2004.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2005','申請案郵寄資料列印','../tcfi/pre/pre2005.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2006','當月發文統計資料查詢','../tcfi/pre/pre2006.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2007','當月所有承辦人員馬上辦及檢還件數統計表','../tcfi/pre/pre2007.jsp',null,1700,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE4018','已審核未結案清單','../tcfi/pre/pre4018.jsp',null,1000,'Y');
--PID=3
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3001','預查審核登錄及維護','../tcfi/pre/pre3001.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3002','輔助查詢','../tcfi/pre/pre3002.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3003','全文檢索查詢','../tcfi/pre/pre3003.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3004','馬上辦','../tcfi/pre/pre3004.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3005','檢還、撤件、撤回退費','../tcfi/pre/pre3005.jsp',null,1700,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3006','清算完結','../tcfi/pre/pre3006.jsp',null,1800,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3007','案件異動紀錄查詢','../tcfi/pre/pre3007.jsp',null,1900,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3010','待辦案件清單','../tcfi/pre/pre3010.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3011','今日案件清單','../tcfi/pre/pre3011.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (3,'PRE3012','個人主頁','../tcfi/pre/pre3012.jsp',null,1000,'Y');
--PID=4
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE3008','公司基本資料查詢','../tcfi/pre/pre3008.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4001','案件資料查詢','../tcfi/pre/pre4001.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4002','封裝資料查詢','../tcfi/pre/pre4002.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4003','案件申請方式統計','../tcfi/pre/pre4003.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4004','承辦案件列表','../tcfi/pre/pre4004.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4005','解釋函資料查詢','../tcfi/pre/pre4005.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4006','承辦人員處理案件統計','../tcfi/pre/pre4006.jsp',null,1700,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4007','審核作業流程/時間分析','../tcfi/pre/pre4007.jsp',null,1800,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4008','核准公司名稱列表','../tcfi/pre/pre4008.jsp',null,1900,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4009','清算完結報表','../tcfi/pre/pre4009.jsp',null,2000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4011','預查線上申辦電子收據','../tcfi/pre/pre4011.jsp',null,2100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4012','馬上辦更改申請人清冊','../tcfi/pre/pre4012.jsp',null,2200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4013','預查線上申辦電子核定書','../tcfi/pre/pre4013.jsp',null,2300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4014','電子核定書逾期寄送清冊','../tcfi/pre/pre4014.jsp',null,2400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4015','歷年預查編號查詢','../tcfi/pre/pre8008.jsp',null,2500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4016','展期案件查詢','../tcfi/pre/pre4016.jsp',null,2600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4017','批次分文查詢','../tcfi/pre/pre8003.jsp',null,1000,'Y');
--PID=5
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8001','公司特取名稱維護','../tcfi/pre/pre8001.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8005','人工改分文','../tcfi/pre/pre8005.jsp',null,1000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8006','解釋函登打維護','../tcfi/pre/pre8006.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8007','公司名稱管制備忘','../tcfi/pre/pre8007.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8009','掛號編號維護','../tcfi/pre/pre8009.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8010','同音同義字維護','../tcfi/pre/pre8010.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8011','營業項目代碼維護','../tcfi/pre/pre8011.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8012','禁用字詞維護','../tcfi/pre/pre8012.jsp',null,1700,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8013','營業項目限制條件維護','../tcfi/pre/pre8013.jsp',null,1800,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8014','國家代碼查詢','../tcfi/pre/pre8014.jsp',null,1900,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8016','儀表板','../tcfi/pre/pre8016.jsp',null,2100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (5,'PRE8017','同義詞維護','../tcfi/pre/pre8017.jsp',null,2000,'Y');
--PID=6
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9001','使用者資料維護','../tcfi/pre/pre9001.jsp',null,1000,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9002','群組資料及權限維護','../tcfi/pre/pre9002.jsp',null,1100,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9003','作業功能維護','../tcfi/pre/pre9003.jsp',null,1200,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9004','參數代碼維護','../tcfi/pre/pre9004.jsp',null,1300,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9005','系統公告維護','../tcfi/pre/pre9005.jsp',null,1400,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9006','登入紀錄查詢','../tcfi/pre/pre9006.jsp',null,1500,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9007','個資處理查詢(查詢類)','../tcfi/pre/pre9007.jsp',null,1600,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9008','個資處理查詢(異動類)','../tcfi/pre/pre9008.jsp',null,1700,'Y');


commit;