package com.kangdainfo.tcfi.util;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.util.lang.CommonStringUtils;


public class TcfiView {

	private TcfiView() {
		//avoid instantiation...
	}

    public static JSONArray getJsonBusiItem(String indexKind, String id) throws SQLException, Exception {
    	JSONArray dsField = null;
    	java.util.List<Cedb1002> objList = null;
		if("1".equals(indexKind) && !"".equals(id))
			objList = ServiceGetter.getInstance().getPrefixService().getCedb1002ByBanNo(id);
		
		if(("2".equals(indexKind) || "3".equals(indexKind)) && !"".equals(id))
			objList = ServiceGetter.getInstance().getPrefixService().getCedb1002ByPrefixNo(id);
		
		if(objList != null && objList.size() > 0) {
			JSONObject item = null;
			dsField = new JSONArray();
			for(Cedb1002 dtl : objList){
				item = new JSONObject();
				item.put("itemCode", Common.get(dtl.getBusiItemNo()));
				item.put("itemName", Common.get(dtl.getBusiItem()));
				dsField.add(item);	
			}
		}  	
    	return dsField;
    }
    
	public static String getStatusName(String type, String statusCode, String orgCode, boolean isHtml){
		StringBuffer sb = new StringBuffer();
		String statusName = "";
		if(!"".equals(Common.get(statusCode))){
			if("有限合夥".equals(orgCode)) {// 2024/04/17 註記:後續可能會移除，有限合夥跟公司及預查系統碼需釐清後決定是否移除，暫時分離有限合夥做法
				statusName = statusCode;
			} else if("1".equals(type)) {
				statusName = Common.get(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(orgCode + statusCode));
				if("".equals(statusName)) {
    				statusName = Common.get(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(statusCode));
				}
			} else {
				statusName = Common.get(ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(statusCode));
			}
    		
    		if(isHtml){
    			//TODO 有限合夥狀態顯示顏色
        		if("有限合夥".equals(orgCode)){// 2024/04/17 註記:後續可能會移除，有限合夥跟公司及預查系統碼需釐清後決定是否移除，暫時分離有限合夥做法
        			sb.append(statusName);
        		}else if("01".equals(statusCode) || "02".equals(statusCode)){
        			sb.append(statusName);
        		}else{
        			sb.append("<font ");
        			if("03".equals(statusCode)) //重整
        				sb.append("color='DarkBlue' >");
        			else if("04".equals(statusCode))// 解散
        				sb.append("color='DarkSlateGray' >");
        			else if("05".equals(statusCode))// 撤銷
        				sb.append("color='Brown' >");
        			else// 破產
        				sb.append("color='Gray' >");
        			sb.append(statusName).append("</font>");
        		}
    		}else{
    			sb.append(statusName);
    		}
    	}
    	return sb.toString();
    }
	
	/**
	 * 有限合夥找狀態
	 * 2024/04/17
	 * @param currStatus
	 * @return String
	 */
	public static String getStatusNameForLmsmBussMain(String currStatus, boolean isHtml) {
		StringBuffer sb = new StringBuffer();
		String statusName = "";
		boolean prefixSgn = false;
		
		if(!"".equals(Common.get(currStatus))){
			// 先根據有限合夥db找狀態，若無則根據預查系統碼列為申請案當中
			statusName = Common.get(ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDescByCode(currStatus));
			if("".equals(statusName)) {
    			statusName = Common.get(ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(currStatus));
    			prefixSgn = true;
			}
    		
    		if(isHtml && !prefixSgn){// TODO 邏輯暫時比照公司登記相似的方式
    			if("01".equals(currStatus)){ // 存活
        			sb.append(statusName);
        		}else{
        			sb.append("<font ");
        			if("02".equals(currStatus)) //停業
        				sb.append("color='DarkBlue' >");
        			else if("03".equals(currStatus))// 解散/撤銷
        				sb.append("color='DarkSlateGray' >");
        			else if("06".equals(currStatus) || "07".equals(currStatus))// 廢止
        				sb.append("color='Brown' >");
        			else if("08".equals(currStatus))// 破產
        				sb.append("color='Gray' >");
        			sb.append(statusName).append("</font>");
        		}
    		}else{
    			sb.append(statusName);
    		}
    	}
    	return sb.toString();
	}
   
    /** 檢索檔預設日期排除 */
    public static String checkIndexDate(String date){
    	if("9999999".equals(Common.get(date))) return "";
    	else return date;
    }
    
    /** 查詢SystemCode */
    public static java.util.Map<String,String> getSystemCodeMap(String codeKind){
    	java.util.Map<String, String> map = new java.util.HashMap<String, String>();
    	java.util.List<SystemCode> list = ServiceGetter.getInstance().getPrefixService().getSystemCodesByCodeKind(codeKind);
    	for(SystemCode o : list){
    		map.put(o.getCode(), o.getCodeName());
    	}
    	return map;
    }
    
    /** 取得營業項目List<Map<String,Object>>物件*/
    public static List<Map<String,Object>> getJsonBusiItemCodeKind(String codeKind, String code){
    	SQLJob sqljob = new SQLJob("SELECT ITEM_CODE AS CODE, BUSINESS_ITEM AS NAME FROM BUSI_ITEM WHERE 1 = 1 ");
		if(!"".equals(codeKind)){
			sqljob.appendSQL(" AND LENGTH(ITEM_CODE) = ? ");
			sqljob.addParameter(codeKind);
		}
		if(!"".equals(code)){
			sqljob.appendSQL(" AND ITEM_CODE LIKE ? ");
			sqljob.addSuffixLikeParameter(code);
		}
		sqljob.appendSQL(" ORDER BY ITEM_CODE");
		
		return ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
    }

	// 判斷申請方式.
	public static String getApplyWayByTelixNo(String telixNo) {
		if (telixNo == null || telixNo.length() == 0)
			return "紙本送件";
		if (telixNo.startsWith("A"))
			return "線上申辦";
		if (telixNo.startsWith("Z") || telixNo.startsWith("0"))
			return "一維條碼";
		if (telixNo.startsWith("L"))
			return "線上審核";
		if (telixNo.startsWith("O"))
			return "一站式";
		return "無法判斷";
	}

	public static String getPrefixStusDesc(String code) {
		return ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(code);
	}

	/** 查詢 預查案件提示文字 */
	public static String getReserveTip(String approveResult, String reserveDate, String prefixStatus, String companyStus) {
		//提示文字
		String reserveTip = "";
		if( PrefixConstants.COMPANY_STATUS_03.equals(companyStus) ) {
			reserveTip = "本案件已完成登記";
		} else if( PrefixConstants.COMPANY_STATUS_01.equals(companyStus) || PrefixConstants.COMPANY_STATUS_00.equals(companyStus) ) {
			reserveTip = "本案件正在登記中";
		} else if( PrefixConstants.APPROVE_RESULT_Y.equals(approveResult) && "".equals(Common.get(reserveDate)) ) {
			reserveTip = "本案件尚無保留期限";
		} else if( PrefixConstants.APPROVE_RESULT_Y.equals(approveResult) && Datetime.getYYYMMDD().compareTo(Datetime.getDateAdd("d",15,reserveDate)) <= 0 ) {
			reserveTip = "預查名稱保留中";
		} else if( PrefixConstants.APPROVE_RESULT_Y.equals(approveResult) && Datetime.getYYYMMDD().compareTo(Datetime.getDateAdd("d",15,reserveDate)) > 0 ) {
			//保留期限逾期15天
			reserveTip = "本案件保留期限已過期，請檢查保留期限";
		} else if( PrefixConstants.PREFIX_STATUS_B.equals(prefixStatus) ) {
			reserveTip = "本案件曾經辦過「遺失補發」";
		} else if( PrefixConstants.PREFIX_STATUS_C.equals(prefixStatus) ) {
			reserveTip = "本案件曾經辦過「馬上辦」";
		} else {
		}
		return reserveTip;
	}
	
	/** 查詢 一站式繳款方式 */
	public static String getPayTypeDesc(Integer payType) {
		String payTypeDesc = "";
		if(null!=payType) {
			if( 1 ==payType ) payTypeDesc = "信用卡";
			else if( 2 ==payType ) payTypeDesc = "金融帳戶";
			else if( 3 ==payType ) payTypeDesc = "晶片金融卡";
		}
		return payTypeDesc;
	}
	
	public static String normalizeName(String name) {
		if(CommonStringUtils.isNotEmpty(name)) {
			name = name.replaceFirst("'s", "小姐");
			name = name.replaceFirst("&#39;s", "小姐");
			name = name.replaceFirst("'S", "小姐");
			name = name.replaceFirst("&#39;S", "小姐");
			name = name.replaceFirst("'r", "先生");
			name = name.replaceFirst("&#39;r", "先生");
			name = name.replaceFirst("'R", "先生");
			name = name.replaceFirst("&#39;R", "先生");
		}
		return name;
	}

}