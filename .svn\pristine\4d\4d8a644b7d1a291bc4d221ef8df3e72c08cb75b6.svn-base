package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023L;

/**
 * 公司預查申請案收件人異動記錄檔(CEDB1023L)
 *
 */
public class Cedb1023LDao
	extends BaseDaoJdbc
	implements RowMapper<Cedb1023L>
{
	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1023L " +
			" WHERE PREFIX_NO = ? " +
			" ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1023L> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedb1023L>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByComposite = "SELECT * FROM CEDB1023L " +
			" WHERE PREFIX_NO = ? " +
			" AND UPDATE_ID_NO = ? " +
			" AND UPDATE_DATE = ? " +
			" AND UPDATE_TIME = ? " +
			" ORDER BY PREFIX_NO, UPDATE_ID_NO, UPDATE_DATE, UPDATE_TIME";
	public Cedb1023L findByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		SQLJob sqljob = new SQLJob(SQL_findByComposite);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(updateIdNo);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1023L> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	private static String SQL_insert = "INSERT INTO CEDB1023L (" +
			"PREFIX_NO,GET_ADDR,GET_NAME,SMS,CONTACT_CEL," +
			"CHANGE_TYPE,CLOSED,ORG_TYPE," +
			"UPDATE_DATE,UPDATE_TIME,UPDATE_ID_NO" +
			") VALUES (" +
			"?,?,?,?,?,"+
			"?,?,?,"+
			"?,?,?"+
			")";

	public int insert(Cedb1023L o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob(SQL_insert);
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getSms());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getContactCel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getChangeType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getClosed());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getOrgType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public Cedb1023L mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1023L obj = null;
		if (null != rs) {
			obj = new Cedb1023L();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setGetAddr(rs.getString("GET_ADDR"));
			obj.setGetName(rs.getString("GET_NAME"));
			obj.setSms(rs.getString("SMS"));
			obj.setContactCel(rs.getString("CONTACT_CEL"));
			obj.setChangeType(rs.getString("CHANGE_TYPE"));
			obj.setClosed(rs.getString("CLOSED"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
		}
		return obj;
	}

}