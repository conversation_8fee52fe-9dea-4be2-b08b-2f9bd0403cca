package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;

public class Cedb1027Dao extends BaseDaoJdbc implements RowMapper<Cedb1027> {

	private static final String SQL_defaultOrder = "ORDER BY GET_DATE, GET_TIME";

	private static final String SQL_findAll = "SELECT * FROM CEDB1027";
	public List<Cedb1027> SQL_findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1027 WHERE PREFIX_NO=?";
	public List<Cedb1027> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findLastOneByPrefixNo = "SELECT * FROM CEDB1027 WHERE PREFIX_NO=?";
	public Cedb1027 findLastOneByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findLastOneByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1027> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		//get last one
		return list.isEmpty() ? null : list.get(list.size()-1);
	}

	private static final String SQL_findByPrefixNoAndPostNo = "SELECT * FROM CEDB1027 WHERE PREFIX_NO=? AND POST_NO=?";
	public Cedb1027 findByPrefixNoAndPostNo(String prefixNo, String postNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndPostNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(postNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1027> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public List<Cedb1027> findBetween( String numStart, String numEnd, String type, String postType ) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1027") ;
		if ("prefix".equals(type))
			sqljob.appendSQL(" WHERE PREFIX_NO BETWEEN ? AND ? ");
		else
			sqljob.appendSQL(" WHERE POST_NO BETWEEN ? AND ? ");
        sqljob.appendSQL(" AND POST_TYPE = ? ");
        if ( "03".equals(postType) ) {
        	sqljob.appendSQL(" AND POST_NO IS NOT NULL");
        } // if
        sqljob.appendSQL("ORDER BY Post_NO asc");
		sqljob.addParameter(numStart);
		sqljob.addParameter(numEnd);
		sqljob.addParameter(postType);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this) ;
	}

	public int insert(Cedb1027 obj) {
		if ( obj == null )
			return 0;
		Cedb1027 oldObj = findByPrefixNoAndPostNo(obj.getPrefixNo(), obj.getPostNo());// 修正於2024/01/23 配合掛號新規則，新增pk來驗證。
		if(null!=oldObj) {
	        return 0;
		}

		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1027 (") ;
		sqljob.appendSQL(" PREFIX_NO");
		sqljob.appendSQL(",POST_NO");
		sqljob.appendSQL(",GET_DATE");
		sqljob.appendSQL(",GET_TIME");
		sqljob.appendSQL(",POST_TYPE");
		sqljob.appendSQL(",BACK_DATE");
		sqljob.appendSQL(",BACK_TIME");
		sqljob.appendSQL(",BACK_REASON");
		sqljob.appendSQL(",OTHER_METHOD");
		sqljob.appendSQL(",ATONCE");
		sqljob.appendSQL(",ATONCE_REMARK");
		sqljob.appendSQL(") VALUES (");
		sqljob.appendSQL("?,?,?,?,?,?,?,?,?,?,?");
		sqljob.appendSQL(")");
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPostNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPostType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getOtherMethod());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAtonce());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAtonceRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	} 

	public int update(Cedb1027 obj) {
		if ( obj == null )
			return 0;
		SQLJob sqljob = new SQLJob("UPDATE CEDB1027 SET") ;
		sqljob.appendSQL(" POST_NO=?");
		sqljob.addParameter(obj.getPostNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_DATE=?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_TIME=?");
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",POST_TYPE=?");
		sqljob.addParameter(obj.getPostType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_DATE=?");
		sqljob.addParameter(obj.getBackDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_TIME=?");
		sqljob.addParameter(obj.getBackTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_REASON=?");
		sqljob.addParameter(obj.getBackReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",OTHER_METHOD=?");
		sqljob.addParameter(obj.getOtherMethod());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",ATONCE=?");
		sqljob.addParameter(obj.getAtonce());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",ATONCE_REMARK=?");
		sqljob.addParameter(obj.getAtonceRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL("WHERE PREFIX_NO=?");
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL("AND GET_DATE=?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL("AND GET_TIME=?");
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int delete(Cedb1027 cedb1027) throws Exception {
		
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1027") ;
		sqljob.appendSQL("WHERE PREFIX_NO=?");
		sqljob.appendSQL("and get_date=?");
		sqljob.appendSQL("and get_time=?");
		sqljob.addParameter(cedb1027.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1027.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1027.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public Cedb1027 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1027 obj = null;
		if(null!=rs) {
			obj = new Cedb1027();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setPostNo(rs.getString("POST_NO"));
			obj.setGetDate(rs.getString("GET_DATE"));
			obj.setGetTime(rs.getString("GET_TIME"));
			obj.setPostType(rs.getString("POST_TYPE"));
			obj.setBackDate(rs.getString("BACK_DATE"));
			obj.setBackTime(rs.getString("BACK_TIME"));
			obj.setBackReason(rs.getString("BACK_REASON"));
			obj.setOtherMethod(rs.getString("OTHER_METHOD"));
			obj.setAtonce(rs.getString("ATONCE"));
			obj.setAtonceRemark(rs.getString("ATONCE_REMARK"));
		}
		return obj;
	}

}