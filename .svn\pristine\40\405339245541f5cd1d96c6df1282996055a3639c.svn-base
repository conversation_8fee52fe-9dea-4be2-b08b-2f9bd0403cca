package com.kangdainfo.tcfi.util;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.StringUtility;

/**
 * <p>Title:PrefixDescHelper </p>
 * <p>Description:預查系統會用到代碼轉換成描述的Helper  </p>
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: III</p>
 * <AUTHOR>
 *
 */

public class PrefixDescHelper {

	public PrefixDescHelper() {}

	/**
	 * 將預查種類的代碼轉為描述的字串
	 * ex: 1: 設立 , 2: 變更
	 * @param code String  預查種類代碼
	 * @return String
	 * <AUTHOR>
	 *
	 */
	public static String getPrefixTypeDesc(String prefixNo) {
		if(prefixNo == null || prefixNo.isEmpty()) {
			return "";
		}
		
		String code = ServiceGetter.getInstance().getApproveService().getCedb1023Dao().findChangeTypeByPrefixNo(prefixNo);
		return code = code != null ? code : "" ;
	}

	public static String getApproveResultDesc(String code) {
		return ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(code);
	}

	/**
	 * 預查的公司狀態, 與2000不同
	 * 01 申請設立, 02 撤銷申請設立, 03 核准設立, 04 撤銷核准設立
	 * @param code String
	 * @return String
	 */
	public static String getPrefixCompanyStusDesc(String code) {
		return ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(code);
	}

	/**
	 * 2000的公司狀態
	 * @param code String
	 * @return String
	 */
	public static String getCompanyStusDesc(String code) {
		return ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(code);
	}

	public static String getDateStr(String str) {
		String dateStr="";
		try {
			dateStr = StringUtility.str2DateStr(str);
			return dateStr;
		}catch(Exception ex){
			return "";
		}
	}

	public static String getIdNoDesc(String idNo) {
		return ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(idNo);
	}
	
	public static String getPrefixStusDesc(String code) {
		return ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(code);
	}

}