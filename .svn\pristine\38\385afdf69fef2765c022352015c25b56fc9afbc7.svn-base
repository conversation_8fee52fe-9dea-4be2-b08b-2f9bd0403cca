package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 承辦人員分文設定檔(CEDB1016)
 *
 */
public class Cedb1016 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 承辦人員識別碼 */
	private String idNo;
	/** 承辦人員姓名 */
	private String staffName;
	/** 是否參與分文 */
	private String joinDistribute;
	/** 分文比例值 */
	private Integer percent;
	/** 已使用分文比例值 */
	private Integer percentUsed;
	/** 下一位承辦人 */
	private String nextIdNo;
	/** 分文計數 */
	private Integer distCount;

	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String staffName) {this.staffName = staffName;}
	public String getJoinDistribute() {return joinDistribute;}
	public void setJoinDistribute(String joinDistribute) {this.joinDistribute = joinDistribute;}
	public Integer getPercent() {return percent;}
	public void setPercent(Integer percent) {this.percent = percent;}
	public Integer getPercentUsed() {return percentUsed;}
	public void setPercentUsed(Integer percentUsed) {this.percentUsed = percentUsed;}
	public String getNextIdNo() {return nextIdNo;}
	public void setNextIdNo(String nextIdNo) {this.nextIdNo = nextIdNo;}
	public Integer getDistCount() {return distCount;}
	public void setDistCount(Integer distCount) {this.distCount = distCount;}

}