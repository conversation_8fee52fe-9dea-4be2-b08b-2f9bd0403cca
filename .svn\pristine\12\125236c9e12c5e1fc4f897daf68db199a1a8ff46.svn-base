package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.util.lang.CommonStringUtils;

public class SyncOssQueueDao extends BaseDaoJdbc implements RowMapper<Queue>{

	private static final String SQL_query = "SELECT * FROM SYNC_OSS_QUEUE ";
	public Queue query(){
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition(" STATUS = '0' ");
		sqljob.appendSQL("ORDER BY ID");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Queue> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}
	
	public Queue queryByPk(String id){
		if("".equals(Common.get(id))) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition(" ID = ? ");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Queue> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}
	
	public java.util.List<Queue> queryWithLotsCondition(String prefixNo, String status, String modDateStart, String modDateEnd) {
		SQLJob sqljob = new SQLJob(SQL_query);
		if (prefixNo != null && !"".equals(prefixNo)) {
			sqljob.appendSQLCondition(" PREFIX_NO like ? ");
			sqljob.addParameter(prefixNo+"%");
		}
		if (status != null && !"".equals(status)) {
			sqljob.appendSQLCondition(" STATUS = ? ");
			sqljob.addParameter(status);
		}
		if (modDateStart != null && !"".equals(modDateStart) && modDateEnd != null && !"".equals(modDateEnd)) {
			sqljob.appendSQLCondition(" MOD_DATE BETWEEN ? AND ? ");
			sqljob.addParameter(modDateStart);
			sqljob.addParameter(modDateEnd);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Queue> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList;
		return null;
	}

	public java.util.List<Queue> queryErrorQueues(){
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition("STATUS='3'");
		sqljob.appendSQL("ORDER BY ID");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Queue> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList;
		return null;
	}

	private static final String SQL_insert = "INSERT INTO SYNC_OSS_QUEUE(PREFIX_NO, REMARK, PROCESS_TIME, MOD_ID_NO, MOD_DATE, MOD_TIME, STATUS)  "
			+ "VALUES(?, ?, ?, ?, ?, ?, ?)";
	public int insert(Queue obj){
		if(obj == null) return 0;
		SQLJob sqljob = new SQLJob(SQL_insert);
		sqljob.addParameter(Common.get(obj.getPrefixNo()));
		sqljob.addParameter(Common.get(obj.getRemark()));
		sqljob.addParameter(Common.getInt(obj.getProcessTime()));
		sqljob.addParameter(Common.get(obj.getModId()));
		sqljob.addParameter(Common.get(obj.getModDate()));
		sqljob.addParameter(Common.get(obj.getModTime()));
		sqljob.addParameter(Common.get(obj.getStatus()));
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	public void insert(String prefixNo, String modIdNo) {
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("INSERT INTO SYNC_OSS_QUEUE(");
		sqljob.appendSQL("PREFIX_NO, MOD_ID_NO, MOD_DATE, MOD_TIME, STATUS");
		sqljob.appendSQL(") VALUES (");
		sqljob.appendSQL("?, ?, ?, ?, '0'");
		sqljob.appendSQL(")");
		sqljob.addParameter(Common.get(prefixNo));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(modIdNo));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	public void updateResetErrorQueue() {
		SQLJob sqljob = new SQLJob("UPDATE SYNC_OSS_QUEUE SET STATUS = '0' WHERE STATUS = '3' AND MOD_DATE > to_char(to_char(sysdate-7,'yyyyMMdd')-19110000)");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private static final String SQL_update = "UPDATE SYNC_OSS_QUEUE ";
	public Queue update(Queue obj){
		if(obj == null || "".equals(Common.get(obj.getId()))) return null;
		
		Queue t = queryByPk(obj.getId());
		if(null==t) return null;
		
		SQLJob sqljob = new SQLJob(SQL_update + " SET ");
		sqljob.appendSQL(" PREFIX_NO = ?,");
		sqljob.appendSQL(" REMARK = ?,");
		sqljob.appendSQL(" PROCESS_TIME = ?,");
		sqljob.appendSQL(" STATUS = ?,");
		sqljob.appendSQL(" MOD_ID_NO = ?,");
		sqljob.appendSQL(" MOD_DATE = ?, ");
		sqljob.appendSQL(" MOD_TIME = ? ");
		sqljob.appendSQL(" WHERE ID = ? ");
		sqljob.addParameter(Common.get(obj.getPrefixNo()));
		sqljob.addParameter(Common.get(obj.getRemark()));
		sqljob.addParameter(Common.getLong(obj.getProcessTime()));
		sqljob.addParameter(Common.get(obj.getStatus()));
		sqljob.addParameter(Common.get(obj.getModId()));
		sqljob.addParameter(Common.get(obj.getModDate()));
		sqljob.addParameter(Common.get(obj.getModTime()));
		sqljob.addParameter(Common.get(obj.getId()));
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		return queryByPk(obj.getId());
	}
	
	public void delete(Queue obj) {
		//check pk
		if(null!=obj
				&& CommonStringUtils.isNotEmpty(obj.getId()) ) {
			//delete
			StringBuffer sql = new StringBuffer();
			sql.append(" DELETE FROM SYNC_OSS_QUEUE");
			sql.append(" WHERE ID = ?");
			Object[] parameters = {
					obj.getId()
					};
			getJdbcTemplate().update(sql.toString(), parameters);
		}
	}
	
	@Override
	public Queue mapRow(ResultSet rs, int arg1) throws SQLException {
		Queue obj = null;
		if(null!=rs) {
			obj = new Queue();
			obj.setId(rs.getString("ID"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setProcessTime(rs.getLong("PROCESS_TIME"));
			obj.setStatus(rs.getString("STATUS"));
			obj.setModId(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}

}
