package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;

public class OssmFeeDetailDao extends BaseDaoJdbc implements RowMapper<OssmFeeDetail> {
	private static final String SQL_findByTelixNoAndProcessNo = "SELECT * FROM OSSM_FEE_DETAIL WHERE TELIX_NO = ? AND PROCESS_NO = ?";
	public OssmFeeDetail findByTelixNoAndProcessNo(String telixNo, String processNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNoAndProcessNo);
		sqljob.addParameter(telixNo);
		sqljob.addParameter(processNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<OssmFeeDetail> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return (null==list || list.isEmpty()?null:list.get(0)); 
	}
	
	
	@Override
	public OssmFeeDetail mapRow(ResultSet rs, int idx) throws SQLException {
		OssmFeeDetail obj = null;
		if(null!=rs) {
			obj = new OssmFeeDetail();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setProcessNo(rs.getString("PROCESS_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setFeeType(rs.getString("FEE_TYPE"));
			obj.setFeeCost(rs.getString("FEE_COST"));
			
		}
		return obj;
	}
	
	
} // OssmFeeMainDao()