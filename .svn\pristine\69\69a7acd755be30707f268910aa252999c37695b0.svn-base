#######################################################################
# Database Specific Configurations
#######################################################################

#========== EICM =====================
eicm.dataSource.driverClassName=oracle.jdbc.driver.OracleDriver
#DB11
#eicm.dataSource.url=jdbc:oracle:thin:@************:1521:ICMS1
#DB12
#eicm.dataSource.url=jdbc:oracle:thin:@*************:1521:ICMS2
#RAC
#eicm.dataSource.url=***********************************************************************************************************************************************************************************************************************************************************************************))))
#NDB
#eicm.dataSource.url=jdbc:oracle:thin:@**************:1521:ICMS
#DB19P
eicm.dataSource.url=jdbc:oracle:thin:@**************:9510:ICMS
eicm.dataSource.username=eicm4ap
eicm.dataSource.password=eicm4ap123
eicm.dataSource.validationQuery=SELECT 1 FROM DUAL
eicm.dataSource.poolPreparedStatements=true
eicm.dataSource.maxOpenPreparedStatements=1000
eicm.dataSource.maxActive=1000
eicm.dataSource.maxIdle=8
#========== OSSS =====================
osss.dataSource.driverClassName=oracle.jdbc.driver.OracleDriver
#DB11
#osss.dataSource.url=jdbc:oracle:thin:@************:1521:ICMS1
#DB12
#osss.dataSource.url=jdbc:oracle:thin:@*************:1521:ICMS2
#RAC
#osss.dataSource.url=***********************************************************************************************************************************************************************************************************************************************************************************))))
#NDB
#osss.dataSource.url=jdbc:oracle:thin:@**************:1521:ICMS
#DB19P
osss.dataSource.url=jdbc:oracle:thin:@**************:9510:ICMS
osss.dataSource.username=osss
osss.dataSource.password=Zj6J4u504Cj$
osss.dataSource.validationQuery=SELECT 1 FROM DUAL
osss.dataSource.poolPreparedStatements=true
osss.dataSource.maxOpenPreparedStatements=1000
osss.dataSource.maxActive=1000
osss.dataSource.maxIdle=2
#========== EEDB =====================
eedb.dataSource.driverClassName=net.sourceforge.jtds.jdbc.Driver
eedb.dataSource.url=jdbc:jtds:sqlserver://**************:1445/EICM;tds=8.0;lastupdatecount=true
eedb.dataSource.username=eicm
eedb.dataSource.password=eicm123
eedb.dataSource.validationQuery=SELECT 1
eedb.dataSource.poolPreparedStatements=true
eedb.dataSource.maxOpenPreparedStatements=1000
eedb.dataSource.maxActive=1000
eedb.dataSource.maxIdle=2
#========== ICMS =====================
icms.dataSource.driverClassName=oracle.jdbc.driver.OracleDriver
#DB11
#icms.dataSource.url=jdbc:oracle:thin:@************:1521:ICMS1
#DB12
#icms.dataSource.url=jdbc:oracle:thin:@*************:1521:ICMS2
#RAC
#icms.dataSource.url=***********************************************************************************************************************************************************************************************************************************************************************************))))
#NDB
#icms.dataSource.url=jdbc:oracle:thin:@**************:1521:ICMS
#DB19P
icms.dataSource.url=jdbc:oracle:thin:@**************:9510:ICMS
icms.dataSource.username=icms
icms.dataSource.password=smci2reca
icms.dataSource.validationQuery=SELECT 1 FROM DUAL
icms.dataSource.poolPreparedStatements=true
icms.dataSource.maxOpenPreparedStatements=1000
icms.dataSource.maxActive=1000
icms.dataSource.maxIdle=2
#========== EICM QD=====================
eicmQD.dataSource.driverClassName=oracle.jdbc.driver.OracleDriver
#QD11
#eicmQD.dataSource.url=jdbc:oracle:thin:@*************:1521:ICMS
#NQD
#eicmQD.dataSource.url=jdbc:oracle:thin:@**************:1521:ICMS
#DB19Q
eicmQD.dataSource.url=jdbc:oracle:thin:@**************:9510:ICMS
eicmQD.dataSource.username=eicm4ap
eicmQD.dataSource.password=eicm4ap123
eicmQD.dataSource.validationQuery=SELECT 1 FROM DUAL
eicmQD.dataSource.poolPreparedStatements=true
eicmQD.dataSource.maxOpenPreparedStatements=1000
eicmQD.dataSource.maxActive=1000
eicmQD.dataSource.maxIdle=2