package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 補列印回執聯
 *
 */
public class PRE1005 extends SuperBean {
	private static final String spacingStr = "@";

	private String search_type;
	private String PREFIX_NO;
	private String TELIX_NO;
	private String idNo;
	private String newPrinterString;
	public String getNewPrinterString() {return newPrinterString;}
	public void setNewPrinterString(String newPrinterString) {this.newPrinterString = newPrinterString;}
	
	//PRE1005 使用
	private String PREFIX_NO_START;
	private String PREFIX_NO_END;
	private String type;
	private String print;
	private String tagx;

	public void rePrint() throws Exception{
		this.tagx = "";
		try{			
			String userId = Common.get(getLoginUserId());
			if ("A228277673".equals(userId) || "A223255131".equals(userId) || "C221284805".equals(userId)) {
				//this.print = "printTag2";	//G500 for 新型條碼機
				this.print = "reprintTag2";
			} else {
				//this.print = "printTag";	//OLD
				this.print = "reprintTag";
			}
			//格式為：預查編號@案由@領件方式@列印種類@收文日期@申請人@申請人地址
			tagx = printTag(this);
			if (logger.isInfoEnabled()) logger.info("[tagx:" + tagx+"]");	
			if(!"".equals(Common.get(getTagx())))	this.setErrorMsg("列印開始!");
		}catch (Exception e) {
			e.printStackTrace();
	        this.setState("printError");
	        if(logger.isInfoEnabled()) logger.info("[補印回執聯][補印回執聯時發生錯誤]" + e.getMessage());
			this.setErrorMsg("列印失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		//return tagx;
	}
	
	/** 組出條碼機的字串 */
	private String printTag(PRE1005 pre1005) throws Exception {
		if(logger.isInfoEnabled()) logger.info("[補印回執聯]");
		if(logger.isInfoEnabled()) logger.info("[PREFIX_NO_START:"+pre1005.getPREFIX_NO_START()+"]");
		if(logger.isInfoEnabled()) logger.info("[PREFIX_NO_END:"+pre1005.getPREFIX_NO_END()+"]");
		if(logger.isInfoEnabled()) logger.info("[Print:"+pre1005.getPrint()+"]");
		if(logger.isInfoEnabled()) logger.info("[Type:"+pre1005.getType()+"]");

		List<PRE1005> prefixList = readMutiCedbTagData(pre1005.getPREFIX_NO_START(), pre1005.getPREFIX_NO_END());
	    if(prefixList == null || prefixList.isEmpty()){
			//if(logger.isInfoEnabled()) logger.info("[補印回執聯][補印回執聯時發生錯誤]");
			pre1005.setErrorMsg("查無資料!!");
			return "";
	    }
	    
	    String space = "";
	    //舊條碼機加一個空白
	    if("reprintTag".equals(pre1005.getPrint()))
	    	space = " ";
	    //組出條碼機的字串
	    String tagx = combineTagText(prefixList, pre1005.getType(), space);
	    return tagx;
	}

	private List<PRE1005> readMutiCedbTagData(String prefixNo_start, String prefixNo_end) throws Exception {
		List<PRE1005> results = null;
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.RECEIVE_DATE");
		sqljob.appendSQL(",A.APPLY_KIND");
		sqljob.appendSQL(",A.GET_KIND");
		sqljob.appendSQL(",B.GET_NAME");
		sqljob.appendSQL(",B.GET_ADDR");
		sqljob.appendSQL("FROM CEDB1000 A");
		sqljob.appendSQL(" LEFT OUTER JOIN CEDB1023 B ON A.PREFIX_NO=B.PREFIX_NO");
		sqljob.appendSQL("WHERE A.PREFIX_NO >="+Common.sqlChar(prefixNo_start));
		sqljob.appendSQL("AND A.PREFIX_NO <="+Common.sqlChar(prefixNo_end));
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			results = new ArrayList<PRE1005>();
			PRE1005 o;
			for(Map<String,Object> map : datas) {
				o = new PRE1005();
				o.setPrefixNo(Common.get(map.get("PREFIX_NO")));
				o.setReceiveDate(Common.get(map.get("RECEIVE_DATE")));
				o.setCaseCode(Common.get(map.get("APPLY_KIND")));
				o.setContactGetKind(Common.get(map.get("GET_KIND")));
				o.setContactName(Common.get(map.get("GET_NAME")));
				o.setContactAddr(Common.get(map.get("GET_ADDR")));
				results.add(o);
			}
		}
		return results;
	}

	/**
	 * 組合條碼字串
	 */
	private String combineTagText(List<PRE1005> datas, String type, String space) {
		if (datas == null || datas.isEmpty() || datas.get(0) == null) return "";

		StringBuffer fullTagText = new StringBuffer("");
		String caseCode = "", getKind = "";
		for (PRE1005 o : datas) {
			//多筆分隔字串
			if(!"".equals(fullTagText.toString()))
				fullTagText.append("｜").append(space);
			//申請案由
			caseCode = o.getCaseCode();
			//領件方式，地址條固定為2(郵寄)
			getKind = "T".equals(type) ? "2" : o.getContactGetKind();
			//字串格式：預查編號@案由@領件方式@列印種類@收文日期@申請人@申請人地址
			fullTagText.append(o.getPrefixNo()).append(spacingStr);
			fullTagText.append(caseCode).append(spacingStr);
			fullTagText.append(getKind).append(spacingStr);
			fullTagText.append(type).append(spacingStr);
			fullTagText.append(o.getReceiveDate()).append(spacingStr);
			fullTagText.append(Common.get(o.getContactName())).append(spacingStr);
			fullTagText.append(Common.get(o.getContactAddr()));
		}
		return fullTagText.toString();
	}
	
	public void rePrintTCO() throws Exception{
		System.out.println("rePrintTCO");
		//格式為：預查編號@案由@領件方式@列印種類@收文日期@申請人@申請人地址
		setNewPrinterString(printTag(this));
		setState("rePrintTCOSuccess");
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getSearch_type() {return search_type;}
	public void setSearch_type(String search_type) {this.search_type = search_type;}
	public String getPREFIX_NO() {return PREFIX_NO;}
	public void setPREFIX_NO(String PREFIX_NO) {this.PREFIX_NO = PREFIX_NO;}
	public String getTELIX_NO() {return TELIX_NO;}
	public void setTELIX_NO(String TELIX_NO) {this.TELIX_NO = TELIX_NO;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	
	//PRE1005 使用
	public String getPREFIX_NO_START() {return checkGet(PREFIX_NO_START);}
	public void setPREFIX_NO_START(String s) {this.PREFIX_NO_START = checkSet(s);}
	public String getPREFIX_NO_END() {return checkGet(PREFIX_NO_END);}
	public void setPREFIX_NO_END(String s) {this.PREFIX_NO_END = checkSet(s);}
	public String getType() {return checkGet(type);}
	public void setType(String s) {this.type = checkSet(s);}
	public String getPrint() {return checkGet(print);}
	public void setPrint(String s) {this.print = checkSet(s);}
	public String getTagx() {return checkGet(tagx);}
	public void setTagx(String s) {this.tagx = checkSet(s);}

	//條碼用
	private String caseCode;
	private String contactGetKind;
	private String prefixNo;
	private String receiveDate;
	private String contactName;
	private String contactAddr;

	public String getCaseCode() {return checkGet(caseCode);}
	public void setCaseCode(String s) {this.caseCode = checkSet(s);}
	public String getContactGetKind() {return checkGet(contactGetKind);}
	public void setContactGetKind(String s) {this.contactGetKind = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {this.receiveDate = checkSet(s);}
	public String getContactName() {return checkGet(contactName);}
	public void setContactName(String s) {this.contactName = checkSet(s);}
	public String getContactAddr() {return checkGet(contactAddr);}
	public void setContactAddr(String s) {this.contactAddr = checkSet(s);}

	@Override
	public String toString() {
		return "PRE1005 [search_type=" + search_type
				+ ", PREFIX_NO=" + PREFIX_NO + ", TELIX_NO=" + TELIX_NO
				+ ", idNo=" + idNo + ", PREFIX_NO_START=" + PREFIX_NO_START
				+ ", PREFIX_NO_END=" + PREFIX_NO_END + ", type=" + type
				+ ", print=" + print + ", tagx=" + tagx + "]";
	}
}