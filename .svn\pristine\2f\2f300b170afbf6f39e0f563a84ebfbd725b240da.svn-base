package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.AtonceRecord;
import com.kangdainfo.util.lang.CommonStringUtils;

public class AtonceRecordDao extends BaseDaoJdbc implements RowMapper<AtonceRecord> {

	private static final String SQL_findByPk = "SELECT * FROM ATONCE_RECORD " +
			"WHERE PREFIX_NO = ? AND ATONCE_DATE = ? AND ATONCE_TIME = ? AND ATONCE_ITEM = ?";
	public AtonceRecord findByPk(String prefixNo, String atonceDate, String atonceTime, String atonceItem) {
		//check pk
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		if(CommonStringUtils.isEmpty(atonceDate)) return null;
		if(CommonStringUtils.isEmpty(atonceTime)) return null;
		if(CommonStringUtils.isEmpty(atonceItem)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPk);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(atonceDate);
		sqljob.addParameter(atonceTime);
		sqljob.addParameter(atonceItem);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (AtonceRecord) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNo = "SELECT * FROM ATONCE_RECORD WHERE PREFIX_NO = ?";
	public List<AtonceRecord> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<AtonceRecord>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public AtonceRecord insert(AtonceRecord bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getPrefixNo())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceDate())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceTime())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceItem())) return null;
		//check exist
		AtonceRecord t = findByPk(bo.getPrefixNo(),bo.getAtonceDate(),bo.getAtonceTime(),bo.getAtonceItem());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO ATONCE_RECORD (");
		sqljob.appendSQL(" PREFIX_NO");
		sqljob.appendSQL(",ATONCE_DATE");
		sqljob.appendSQL(",ATONCE_TIME");
		sqljob.appendSQL(",ATONCE_ITEM");
		sqljob.appendSQL(",ID_NO");
		sqljob.appendSQL(",SOURCE");
		sqljob.appendSQL(",REMARK");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getPrefixNo());
		sqljob.addParameter(bo.getAtonceDate());
		sqljob.addParameter(bo.getAtonceTime());
		sqljob.addParameter(bo.getAtonceItem());
		sqljob.addParameter(bo.getIdNo());
		sqljob.addParameter(bo.getSource());
		sqljob.addParameter(bo.getRemark());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		return findByPk(bo.getPrefixNo(),bo.getAtonceDate(),bo.getAtonceTime(),bo.getAtonceItem());
	}
	
	public AtonceRecord update(AtonceRecord bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getPrefixNo())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceDate())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceTime())) return null;
		if(CommonStringUtils.isEmpty(bo.getAtonceItem())) return null;
		//check exist
		AtonceRecord t = findByPk(bo.getPrefixNo(),bo.getAtonceDate(),bo.getAtonceTime(),bo.getAtonceItem());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE ATONCE_RECORD SET");
			sqljob.appendSQL("ID_NO = ?");
			sqljob.appendSQL(",SOURCE = ?");
			sqljob.appendSQL(",REMARK = ?");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.appendSQL("AND ATONCE_DATE = ?");
			sqljob.appendSQL("AND ATONCE_TIME = ?");
			sqljob.appendSQL("AND ATONCE_ITEM = ?");
			sqljob.addParameter(bo.getIdNo());
			sqljob.addParameter(bo.getSource());
			sqljob.addParameter(bo.getRemark());
			sqljob.addParameter(bo.getPrefixNo());
			sqljob.addParameter(bo.getAtonceDate());
			sqljob.addParameter(bo.getAtonceTime());
			sqljob.addParameter(bo.getAtonceItem());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
			return findByPk(bo.getPrefixNo(),bo.getAtonceDate(),bo.getAtonceTime(),bo.getAtonceItem());
		}
	}
	
	public void delete(AtonceRecord bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getPrefixNo())
				&& CommonStringUtils.isNotEmpty(bo.getAtonceDate())
				&& CommonStringUtils.isNotEmpty(bo.getAtonceTime())
				&& CommonStringUtils.isNotEmpty(bo.getAtonceItem()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM ATONCE_RECORD");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.appendSQL("AND ATONCE_DATE = ?");
			sqljob.appendSQL("AND ATONCE_TIME = ?");
			sqljob.appendSQL("AND ATONCE_ITEM = ?");
			sqljob.addParameter(bo.getPrefixNo());
			sqljob.addParameter(bo.getAtonceDate());
			sqljob.addParameter(bo.getAtonceTime());
			sqljob.addParameter(bo.getAtonceItem());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	@Override
	public AtonceRecord mapRow(ResultSet rs, int idx) throws SQLException {
		AtonceRecord obj = null;
		if(null!=rs) {
			obj = new AtonceRecord();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setAtonceDate(rs.getString("ATONCE_DATE"));
			obj.setAtonceTime(rs.getString("ATONCE_TIME"));
			obj.setAtonceItem(rs.getString("ATONCE_ITEM"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setSource(rs.getString("SOURCE"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}
