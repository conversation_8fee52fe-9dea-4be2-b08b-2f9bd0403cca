package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 預查案件基本資料記錄檔(CEDB1000)
 *
 */
public class Cedb1000 extends BaseModel {
	
	public Cedb1000() {
	}
	
	protected Cedb1000(Cedb1000 c1000) {
	}
	
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 申請人地址 */
	private String applyAddr;
	/** 申請人身分證統一編號 */
	private String applyId;
	/** 預查種類 */
	private String applyKind;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人聯絡電話 */
	private String applyTel;
	/** 案件性質 */
	private String applyType;
	/** 核覆日期 */
	private String approveDate;
	/** 檢還/撤件註記(01:檢還,02:撤件,03:撤回退費) */
	private String approveMark;
	/** 核覆結果 */
	private String approveResult;
	/** 核覆時間 */
	private String approveTime;
	/** 分文日期 */
	private String assignDate;
	/** 分文時間 */
	private String assignTime;
	/** 代理人所在地 */
	private String attorAddr;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 代理人聯絡電話 */
	private String attorTel;
	/** 統一編號 */
	private String banNo;
	/** 結案日期 */
	private String closeDate;
	/** 結案時間 */
	private String closeTime;
	/** 異動內容 */
	private String codeName;
	/** 異動內容代碼 */
	private String codeNo;
	/** 公司名稱 */
	private String companyName;
	/** 預查公司狀態 */
	private String companyStus;
	/** 提辦案件 */
	private String controlCd1;
	/** 抽換案件 */
	private String controlCd2;
	/** 領件日期 */
	private String getDate;
	/** 預查結果領取方式 */
	private String getKind;
	/** 領件時間 */
	private String getTime;
	/** 承辦人員識別碼 */
	private String idNo;
	/** 原公司名稱 */
	private String oldCompanyName;
	/** 案件狀態 */
	private String prefixStatus;
	/** 收件日期 */
	private String receiveDate;
	/** 收件時間 */
	private String receiveTime;
	/** 申請／撤銷申請登記日期 */
	private String regDate;
	/** 申登機關代碼 */
	private String regUnit;
	/** 檢還/撤件註記說明 */
	private String remark;
	/** 備註 */
	private String remark1;
	/** 保留期限 */
	private String reserveDate;
	/** 保留天數 */
	private Integer reserveDays;
	/** 延展保留期限註記 */
	private String reserveMark;
	/** 特取名稱 */
	private String specialName;
	/** 承辦人員姓名 */
	private String staffName;
	/** 網路收文電子流水號 */
	private String telixNo;
	/** 異動原因 */
	private String updateCode;
	/** 異動日期 */
	private String updateDate;
	/** 異動人員識別碼 */
	private String updateIdNo;
	/** 異動時間 */
	private String updateTime;
	/** 工作天數 */
	private Float workDay;
	/** 郵遞區號 */
	private String zoneCode;
	/** 審核備註 */
	private String approveRemark;
	/** 是否有預查表附件 */
	private String isPrefixForm;
	/** 附件-預查表編號 */
	private String prefixFormNo;
	/** 是否有其他機關核准函附件 */
	private String isOtherForm;
	/** 是否有說明書附件 */
	private String isSpec;
	/** 領件方式註記 */
	private String getKindRemark;
	/** 是否有其他附件 */
	private String isOtherSpec;
	/** 其他附件註記 */
	private String otherSpecRemark;
	/** 正副本別 */
	private String docType;
	/** 展期案件註記 */
	private String extendMark;
	/** 收文確認註記 */
	private String rcvCheck;
	/** 馬上辦案由 */
	private String atonceType;
	/**　退費公文號 */
	private String refundNo;
	/** 展期原因 */
	private String extendReason;
	/** 展期其他原因 */
	private String extendOther;
	/** 展期日期 */
	private String extendDate;
    /** 非屬公司法第18條同名公司之原因  */
    private String otherReason;
    
    // 自由填列事項（不納入預查審核項目）
    /** 國外匯款使用英文名稱(僅提供銀行開戶使用)  */
    private String extRemitEname;

	/** 虛擬欄位：免繳註記 */
    private String noNeedPay;

    public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String s) {this.applyAddr = s;}
	public String getApplyId() {return applyId;}
	public void setApplyId(String s) {this.applyId = s;}
	public String getApplyKind() {return applyKind;}
	public void setApplyKind(String s) {this.applyKind = s;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String s) {this.applyName = s;}
	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}
	public String getApplyType() {return applyType;}
	public void setApplyType(String s) {this.applyType = s;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String s) {this.approveDate = s;}
	public String getApproveMark() {return approveMark;}
	public void setApproveMark(String s) {this.approveMark = s;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String s) {this.approveResult = s;}
	public String getApproveTime() {return approveTime;}
	public void setApproveTime(String s) {this.approveTime = s;}
	public String getAssignDate() {return assignDate;}
	public void setAssignDate(String s) {this.assignDate = s;}
	public String getAssignTime() {return assignTime;}
	public void setAssignTime(String s) {this.assignTime = s;}
	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}
	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}
	public String getAttorTel() {return attorTel;}
	public void setAttorTel(String s) {this.attorTel = s;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getCloseDate() {return closeDate;}
	public void setCloseDate(String s) {this.closeDate = s;}
	public String getCloseTime() {return closeTime;}
	public void setCloseTime(String s) {this.closeTime = s;}
	public String getCodeName() {return codeName;}
	public void setCodeName(String s) {this.codeName = s;}
	public String getCodeNo() {return codeNo;}
	public void setCodeNo(String s) {this.codeNo = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getCompanyStus() {return companyStus;}
	public void setCompanyStus(String s) {this.companyStus = s;}
	public String getControlCd1() {return controlCd1;}
	public void setControlCd1(String s) {this.controlCd1 = s;}
	public String getControlCd2() {return controlCd2;}
	public void setControlCd2(String s) {this.controlCd2 = s;}
	public String getGetDate() {return getDate;}
	public void setGetDate(String s) {this.getDate = s;}
	public String getGetKind() {return getKind;}
	public void setGetKind(String s) {this.getKind = s;}
	public String getGetTime() {return getTime;}
	public void setGetTime(String s) {this.getTime = s;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String s) {this.idNo = s;}
	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}
	public String getPrefixStatus() {return prefixStatus;}
	public void setPrefixStatus(String s) {this.prefixStatus = s;}
	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}
	public String getReceiveTime() {return receiveTime;}
	public void setReceiveTime(String s) {this.receiveTime = s;}
	public String getRegDate() {return regDate;}
	public void setRegDate(String s) {this.regDate = s;}
	public String getRegUnit() {return regUnit;}
	public void setRegUnit(String s) {this.regUnit = s;}
	public String getRemark() {return remark;}
	public void setRemark(String s) {this.remark = s;}
	public String getRemark1() {return remark1;}
	public void setRemark1(String s) {this.remark1 = s;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String s) {this.reserveDate = s;}
	public Integer getReserveDays() {return reserveDays;}
	public void setReserveDays(Integer i) {this.reserveDays = i;}
	public String getReserveMark() {return reserveMark;}
	public void setReserveMark(String s) {this.reserveMark = s;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String s) {this.specialName = s;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String s) {this.staffName = s;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String s) {this.telixNo = s;}
	public String getUpdateCode() {return updateCode;}
	public void setUpdateCode(String s) {this.updateCode = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateIdNo() {return updateIdNo;}
	public void setUpdateIdNo(String s) {this.updateIdNo = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}
	public Float getWorkDay() {return workDay;}
	public void setWorkDay(Float s) {this.workDay = s;}
	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String s) {this.zoneCode = s;}
	public String getApproveRemark() {return approveRemark;}
	public void setApproveRemark(String s) {this.approveRemark = s;}
	public String getIsPrefixForm() {return isPrefixForm;}
	public void setIsPrefixForm(String s) {this.isPrefixForm = s;}
	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String s) {this.prefixFormNo = s;}
	public String getIsOtherForm() {return isOtherForm;}
	public void setIsOtherForm(String s) {this.isOtherForm = s;}
	public String getIsSpec() {return isSpec;}
	public void setIsSpec(String s) {this.isSpec = s;}
	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String s) {this.getKindRemark = s;}
	public String getIsOtherSpec() {return isOtherSpec;}
	public void setIsOtherSpec(String s) {this.isOtherSpec = s;}
	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = s;}
	public String getDocType() {return docType;}
	public void setDocType(String s) {this.docType = s;}
	public String getExtendMark() {return extendMark;}
	public void setExtendMark(String s) {this.extendMark = s;}
	public String getRcvCheck() {return rcvCheck;}
	public void setRcvCheck(String s) {this.rcvCheck = s;}
	public String getAtonceType() {return atonceType;}
	public void setAtonceType(String s) {this.atonceType = s;}

	public String getRefundNo() {return refundNo;}
	public void setRefundNo(String s) {this.refundNo = s;}
	public String getExtendReason() {return extendReason;}
	public void setExtendReason(String s) {this.extendReason = s;}
	public String getExtendOther() {return extendOther;}
	public void setExtendOther(String s) {this.extendOther = s;}
	public String getExtendDate() {return extendDate;}
	public void setExtendDate(String s) {this.extendDate = s;}
	public String getNoNeedPay() {return noNeedPay;}
	public void setNoNeedPay(String s) {this.noNeedPay = s;}
	public String getOtherReason() {return otherReason;}
	public void setOtherReason(String s) {this.otherReason = s;}
	public String getExtRemitEname() {return extRemitEname;}
	public void setExtRemitEname(String s) {this.extRemitEname = s;}
}
