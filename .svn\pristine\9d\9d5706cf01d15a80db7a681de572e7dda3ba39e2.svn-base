<!DOCTYPE html>
<!--
程式目的：收/發文-收/發文登打-基本資料
程式代號：PRE3004
撰寫日期：103.05.19
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3004">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<%
obj.initPrefixNos();
if("saveBusiItem".equals(obj.getState())) {
	obj.saveBusiItem();
} else if("tempSave".equals(obj.getState())) {
	obj.tempSave();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3004"/></jsp:include>
<%@ include file="../../home/<USER>"%>

<style>

	.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset { float : left; }
	
</style>

<SCRIPT LANGUAGE="JAVASCRIPT">
var prefixNos;
var prefixJson;
var ilocation = -1;
function setfocus(idx) {
	ilocation = idx;
	document.forms[0].focus.value = ilocation;
}

function addSpecialWord() {
	
    if(ilocation < 0) {
    	$(".cedb1002:last").find("[type=button]").click();
		ilocation = $(".cedb1002").size() + 1;
		
    } else {
    	$("#cedb1002s tr").eq(ilocation).find("[type=button]").click();
    }
    
    $("#cedb1002s tr").eq(ilocation).find("input[name=busiItemNo]").val("ZZ99999");
	$("#cedb1002s tr").eq(ilocation).find("input[name=busiItem]").val("除許可業務外，得經營法令非禁止或限制之業務");
}

function changeCase() {
	var e = jQuery.Event("keypress");
	e.which = 13;
	jQuery('#prefixNo').trigger(e);
}

function isReceiveExist() {
	if ($("input[name=receiveName]").val() != '') {
		alert('提示！已有收件人資料，請檢查。');
		return false;
	}
	if ($("input[name=receiveAddr]").val() != '') {
		alert('提示！已有收件人資料，請檢查。');
		return false;
	}
	return true;
}

$(document).ready(function() {
	
	init();
	//離開
	$('#sc_close').click(function(){
		form1.action = "pre3004.jsp";
		form1.state.value = "back";
		form1.submit();
	});
	//異動內容
	$("#btnHisttoryList").click(function() {
		if($("#banNo").val() != '') {
			var prop = "width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
			window.open("pre3001_04.jsp?banNo=" + $("#banNo").val(), "pre3001_04", prop);
		} else {
			alert("查無資料");
		}
	});
	//備註歷史
	$("#btnHistor").click(function(){
		closeReturnWindow();
		returnWindow=window.open('pre3001_08.jsp?prefixNo='+$("#prefixNo").val(),'pre3001_08','width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');
	});
	//外商\大陸商或合併(分割)消滅註記
	$("#btnForeign").click(function(){
		closeReturnWindow();
		returnWindow=window.open("pre3001_05.jsp?prefixNo="+$("#prefixNo").val(),'pre3001_05','width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');
	});
	//複製至收件人
	$("#copyApplyToReceiver").click(function(){
		if(isReceiveExist()){
			$("input[name=receiveName]").val($("input[name=applyName]").val());
			$("input[name=receiveAddr]").val($("input[name=applyAddr]").val());
		}
	});
	//複製至收件人
	$("#copyAttorToReceiver").click(function(){
		if(isReceiveExist()){
			$("input[name=receiveName]").val($("input[name=attorName]").val());
			$("input[name=receiveAddr]").val($("input[name=attorAddr]").val());
		}
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if(++currentIndex >= prefixNos.length) {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		} else {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		$.cookie("activeTabIndex", 0);
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if((currentIndex-1) < 0) {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[(prefixNos.length-1)]+"&hiddenPrefixNos="+prefixNos;
		} else {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[--currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		$.cookie("activeTabIndex", 0);
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var last = prefixNos.length - 1;
		window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[last]+"&hiddenPrefixNos="+prefixNos;
		$.cookie("activeTabIndex", 0);
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		window.location.href = getVirtualPath() + "tcfi/pre/pre3004_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		$.cookie("activeTabIndex", 0);
	});
	//上層頁籤
	$("#tabs").tabs();
	//下層頁籤
	$("#tabs2").tabs();

	$.post(getVirtualPath() + 'tcfi/ajax/jsonAtonceTypes.jsp', function(data) {
		var html = "";
		var codeName, codeName2, codeName3;
		for(var i =0; i<data.length; i++) {
			codeName = data[i].CODENAME;
			if (i==0 || i%3==0)
				html += '<tr>';
			html += '<TD class="fieldlight" nowrap><input type="checkbox" name="atonceReason" value="'+ codeName +'"/>'+ codeName +'</TD>';
			if ((i+1)%3==0)
				html += '</tr>';
		}
		$("#atonceRemarkDiv .remark1Table").append(html);
	});

	$(document).ajaxStop(function() {
		$('.copyToRemark').hide().append("<br/>");

		if( '' != $("#atonceType").val() ) {
			var av = $("#atonceType").val();
			$.each(av.split(','), function(i,v){
				if( '' != v) {
					$('input[name=atonceReason]').eq(v).prop('checked', true);
				}
		    });
		}
	});

    $( "#doAtonce" ).click(function() {
        $( "#dialog-form" ).dialog( "open" );
	});

   $( "#dialog-form" ).dialog({
	      autoOpen: false,
	      height: 270,
	      width: 750,
	      modal: true,
	      title: "馬上辦類型",
	      position: {
	    		my: "center",
	    		at: "center",
	    		of: $("body"),
	    		within: $("body")
	   	  },
	      buttons: {
	        "確定": function() {
	        	$( this ).dialog( "close" );
	        	var values = "";
			     $("input[name='atonceReason']:checked").map(function(){
					var reason = $(this).val() + ", ";
					values += reason;
				 });

			     var data = new Date().toLocaleString() + " " + values.replace(/, $/g, "");
			     var oldData = $("#remark1").val();
			     if(oldData != "")	oldData += " ";
		    	 $("#remark1").val(oldData + data);

		    	 var atonceType = '';
		    	 $("input[name=atonceReason]").each(function(i, v) {
						if( !!$(v).prop('checked') ) {
							atonceType += ' ' + i + ',';
						}
				 });
		    	 $("#atonceType").val(atonceType);
	    		 $("input[name=closed]").hide();
	    		 $("#closedBlock").hide();
		    	 if ($("#atonceType").val().indexOf("15") > -1) {
		    		 $("input[name=closed]").show();
		    		 $("#closedBlock").show();
		    	 } else {
		    		 if( $("input[name=closed]").is(':checked') ) {
			    		 $("#closedBlock").show();
		    		 }
		    	 }
	        }
	      }
    });
   
    $("#cedb1001s,#cedb1101s").on("mouseup", "input[name=companyName]", function(e) {
		$("#hiddenSpecialName").val(commonUtils.getSelectionText());
		var specialName = $("#hiddenSpecialName").val();
		var isApproveChecked = $(this).closest("tr").find('[name=approveResult]').is(':checked');
		if(specialName && isApproveChecked && ($("input[name=changeName]").is(':checked') || $("input[name=setup]").is(':checked'))) {
			$("input[name=specialNameSp]").val(specialName);
		}	
	});


	try {
		prefixNos = JSON.parse($.cookie("prefixNos"));
	}catch(e){}

	$(".openAddresser").click(function(e) {
    	commonUtils.initAddresser(this);
        $('#address-dialog').dialog('open');
    });

	$("#current").val($("#prefixNo").val());

	$("#cedb1002s").delegate('input[type=text]', 'focus', function () {
		var index = $(this).closest('tr').index();
		setfocus(index);
	});

	$("#saveBusiItem").click(function() {
		form1.state.value = "saveBusiItem";
		form1.submit();
	});

	$("#zz9999").click(function(){
		addSpecialWord();
	});

	$("#delBuItem").click(function() {
		$("#cedb1002s input[name=cedb1002Chk]:checked").closest('tr').remove();
		resetCedb1002SeqNo();
	});

	$("#saveCompanyName").click(function(){
		$("#tempSave").trigger("click");
	});

	$("#tempSave").click(function(e) {
		var atonceReason = document.getElementsByName("atonceReason");
		var i = 0;
		var countOfCheckedReason = 0;
		for ( i = 0; i < atonceReason.length; i++ ) {
			if ( atonceReason[i].checked )
				countOfCheckedReason++;
		}
		if ( countOfCheckedReason == 0 && form1.everAtonce.value == "Y" ) {
			if( confirm("按下確定將會刪除這筆案件的馬上辦紀錄；要繼續嗎？")) {
			}else {
				return false;
			}
			
		}
		form1.state.value = "tempSave";
		form1.functionName.value = "atonce";
		var json1 = $("#form1").serializeObject();
	    var data = $.extend(prefixJson, getPrefixVo(), json1);
	    delete data.cedb1006s;
	    delete data.cedb1010s;
	    data.approveResult = $("select[name=approveResult]").val();
	    data.remark = $('[name=remark]').eq(0).val();
	    data.companyName = $("#fragment-1 input[name=companyName]").val();
	    data.applyKind = getApplyKindSave();
	    data.specialName = $('#specialNameSp').val();
	    if(!$("input[name=reserveMark]:checked").val()) {
			data.reserveMark = 'N';
		}
		    
		$("#json").val(JSON.stringify(data));
	    form1.submit();
	});

	$("#save").click(function(e) {
		if(!checkCedb1002s() || checkDuplicateItemCode()) {
			e.preventDefault();
		} else {
			$("#tempSave").trigger("click");
		}
	});

	$("#buItemSelectAll").click(function(){
		commonUtils.all("cedb1002Chk");
	});

	$("#buItemUnSelectAll").change(function(){
		if(this.checked)
			commonUtils.unAll("cedb1002Chk");
	});

	$("#prefixNo").keypress(function(e){
		if(e.which == 13) {
			$.post( getVirtualPath() + "tcfi/ajax/jsonPrefixVo.jsp?from=PRE3004&q=" + $("#prefixNo").val(), function( data ) {
				if(!data)
					return;
		
				commonUtils.mappingJsonByName(data, false);
					
					selectApplyKind(data.applyKind);
					document.getElementById("xMainFileCompanyName").innerText = form1.mainFileCompanyName.value;
					document.getElementById("xCompanyName").innerText = form1.companyName.value;
					$('#specialNameSp').val(data.specialName);

					$('#applyWay').html(data.applyWay);//申請方式
					$('#receiveDateTime').html(data.receiveDateTime);//收件日期時間
					$('#receiveKeyinDateTime').html(data.receiveKeyinDateTime);//收文登打日期時間
					$('#assignDateTime').html(data.assignDateTime);//分文日期時間
					$('#approveDateTime').html(data.approveDateTime);//審核日期時間
					$('#issueKeyinDateTime').html(data.issueKeyinDateTime);//發文登打日期時間
					$('#closeDateTime').html(data.closeDateTime);//發文日期時間
					$('#getDateTime').html(data.getDateTime);//領件日期時間
					$('#reserveTip').html(data.reserveTip);//案件提示文字
					$('#approveResultDesc').val(data.approveResult);//核覆結果
					if ( data.atonceType == null || data.atonceType == "" ) {
						$('#everAtonce').val("N");
					} else {
						$('#everAtonce').val("Y");
					}						
					if( "本案件尚無保留期限" == data.reserveTip ) {
						alert("本案件尚無保留期限，請確認是否完成發文。");
					} else if( "預查名稱保留中" == data.reserveTip ) {
					} else if( "" != data.reserveTip ) {
						alert("請注意，"+data.reserveTip+"！");
					}

					var isSamePerson = $("input[name='applyName']").val() == $("input[name='contactName']").val();
					if(isSamePerson && $("input[name='applyAddr']").val()=="") {
						$("input[name='applyAddr']").val(data.CONTACT_ADDR);
					}

					var applyType = data.applyType;
					var contactGetKind = data.contactGetKind;

					$("input[name=receiveName]").val(data.cedb1023.getName);
					$("input[name=receiveId]").val(data.cedb1023.getName);
					$("input[name=contactCel]").val(data.cedb1023.contactCel);
					$("input[name=receiveAddr]").val(data.cedb1023.getAddr);
					$("input[name=sms]").val(data.cedb1023.sms);
					$("input[name=changeType]").val(data.cedb1023.changeType);
					$("#closedBlock").css({}).hide();
					if (data.cedb1023.closed == 'Y') {
						$("#closedBlock").css({"color":"#FF0000","font-weight":"bold"}).show();
						document.getElementsByName('closed')[0].checked = true;
					} else {
						document.getElementsByName('closed')[0].checked = false;
					}
		    		$("input[name=closed]").hide();
					if (null!=data.atonceType && data.atonceType.indexOf('15') > -1) {
			    		 $("input[name=closed]").show();
						$("#closedBlock").show();
					}
					$("input[name=orgType]").val(data.cedb1023.orgType);
					
					var selectHtml = '';
		             
		                if($("#cedb1001s tr").length == 4) {
							var cedb1001Html = "";
							var seqNo = "";
							var companyName = "";
							var isApprove = "";
							var remark = "";
							
							for(var i=0; i< 5; i++) {

								if(data.cedb1001s[i]) {
									seqNo = data.cedb1001s[i].seqNo;
									companyName = data.cedb1001s[i].companyName;
									if(data.cedb1001s[i].approveResult == 'Y') {
										isApprove = "checked";
										selectHtml = '<select class="orgnType"><option value="XX">更改組織型態</option><option value="有限公司">有限公司</option><option value="股份有限公司">股份有限公司</option>' +
						                '<option value="無限公司">無限公司</option><option value="兩合公司">兩合公司</option></select>';
									} else {
										isApprove = '';
										selectHtml = '';
									}
									remark = commonUtils.trimUndefined(data.cedb1001s[i].remark);
								}else {
									seqNo = commonUtils.padZero(i+1, 2);
									companyName = "";
									isApprove = "";
									remark = "";
								}
								
								cedb1001Html += '<tr>'+
								'<td class="td_form_white" style="text-align: center"><input class="field_RO" readonly type="text" size="2" style="text-align: center" name="seqNo" value="'+ seqNo +'"></td>' +
								'<td class="td_form_white"><input class="field" type="text" size="30" name="companyName" value="'+ companyName +'">'+selectHtml +'</td>' +
								'<td class="td_form_white" style="text-align: center;"><input type="checkbox" '+ isApprove +' name="approveResult" value="Y"></td>' +
								'<td class="td_form_white"><input class="field" type="text" name="showSame0'+ (i+1) +'" size="30" value=""></td>'+
								'<td class="td_form_white" style="text-align: center"><input size="30" type="text" name="remark" class="" value="'+ remark  +'"></td>'+
								'</tr>';
							}
	
						 $("#cedb1001s tr").eq(0).after(cedb1001Html);
					}

					//重構至approve.js
					if(data.cedb1001s && data.cedb1004s)
						showSames(data.cedb1001s, data.cedb1004s);
					
					//法人資訊
					setCedb1022(data.cedb1022);
					//營業項目
					setCedb1002Row(data.cedb1002s);
					//案件流程
					setCedb1010Row(data.cedb1010s);

					$("input[name=remark]", "#cedb1001s").prop('readonly', true).addClass('field_RO');
					$("#cedb1001s input[type=checkbox]").prop('disabled', true);

					prefixJson = $.extend(prefixJson, data);
				});

				if($("#companyNames").find("tr").size() == 1) {
					setTimeout(function(){
						var html = '';
						
						$.post( getVirtualPath() + "tcfi/ajax/jsonCedbc1001.jsp?q2=Y&q=" + $("#prefixNo").val(), function( data ) {
							html += '<tr><td><input name="seqNo" class="inputNoBorder" type="text" value="'+ data[0].SEQ_NO +'" readonly /></td>' +
							'<td><input name="applyCompanyNamesArray" class="inputNoBorder" type="text" value="'+ data[0].COMPANY_NAME +'" readonly /></td></tr>';
							
							$("#companyNames").append(html).show();
						});
	
	
					}, 500);
				}

		    }

		});

	});

	$(window).load(function(){

		changeCase();
		
		$("#cedb1001s").on("change", "input[name=approveResult]", function() {
			$("[name="+$(this).prop('name')+"]").prop("checked", false);
		    $(this).prop("checked", true);
			commonUtils.unAll("approveResultAllNo");
		});

	});

	function getPrefixVo() {
		var prefixVo = {};
		var cedb1001s = [];
		var cedb1002s = [];
		var cedb1022 = {};
		var cedb1023 = {};
		var prefixNo = $("#prefixNo").val();
		var seqNo = "";
		var seq = 1;
		$("input[name=companyName]", "#cedb1001s").each(function(k, v) {

			var companyName = $(v).val();
			if(companyName) {
				var cedb1001 = {};
				cedb1001.prefixNo = prefixNo;
				cedb1001.companyName = companyName;
				//序號要檢查重排
				seqNo = commonUtils.padZero(seq++, 2);
				cedb1001.seqNo = seqNo;

				var approveResult = $(v).closest('td').next('td').find("input[name=approveResult]").is(":checked");
				cedb1001.approveResult = approveResult ? "Y" : "N";
				var remark = $(v).closest('tr').find("input[name=remark]").val();
				if(remark) {
					cedb1001.remark = remark.replaceAll(' ', '').replaceAll('"', "''");
				}
				cedb1001s.push(cedb1001);
			}

		});

		$("input[name=busiItem]").each(function(k, v) {

			var busiItem = $(v).val().replaceAll(' ', '');
			if(busiItem) {
				var cedb1002 = {};
				cedb1002.prefixNo = prefixNo;
				cedb1002.busiItem = busiItem;
				cedb1002.seqNo = $(v).closest('td').prev('td').prev('td').find("input[name=itemSeqNo]").val();
				cedb1002.busiItemNo = $(v).closest('td').prev('td').find("input[name=busiItemNo]").val();
				
				cedb1002s.push(cedb1002);
			}

		});

		cedb1022.prefixNo = prefixNo;
		cedb1022.applyLawName = $('input[name=applyLawName]').val();
		cedb1022.applyBanNo = $('input[name=applyBanNo]').val();

		cedb1023.prefixNo = prefixNo;
		cedb1023.getAddr = $("input[name=receiveAddr]").val();
		cedb1023.getName = $("input[name=receiveName]").val();
		cedb1023.sms = $("input[name=sms]").val();
		cedb1023.contactCel = $("input[name=contactCel]").val();
		cedb1023.changeType = getApplyKind();
		if ($("input[name=closed]").prop("checked")) {
			cedb1023.closed = 'Y';
		} else {
			cedb1023.closed = 'N';
		}

		cedb1023.orgType = $("input[name=orgType]").val();
		prefixVo.cedb1001s = cedb1001s;
		prefixVo.cedb1002s = cedb1002s;
		prefixVo.cedb1022 = cedb1022;
		prefixVo.cedb1023 = cedb1023;

		return prefixVo;
	}
		
	function approveResultAllNoClick(input){
		commonUtils.unAll("approveResult");
	}

	function saveData(printName) {

		if (document.forms[0].prefixFormNo.value.length != 9
				&& document.forms[0].prefixFormNo.value.length > 0) {
			alert("附件預查表編號長度不符，請重新輸入");
			return;
		}

		document.forms[0].method.value = "save";
		document.forms[0].printName.value = printName;
		document.forms[0].submit();
	}

</SCRIPT>
<script type="text/javascript" src="<%=contextPath%>/js/approve.js"></script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE3004'/>
</c:import>

<table id="shareBar" width="100%" border="0" cellpadding="2" cellspacing="0">
	<tr>
		<td >
			<input class="toolbar_default" type="button" id="save" value="存檔" />
			<input class="toolbar_default" type="button" name="btnQuery6" disabled="true" value="列印申請表">
			<input class="toolbar_default" type="button" value="電子核定書" id="previewApproveForm" >&nbsp;
			<input class="toolbar_default" type="button" value="重設發文" id="resetClose" >
		</td>
		<td style="text-align: right;padding-right:15px;">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value='PRE4001,PRE3002,PRE3008,PRE3013'/>
				<c:param name="shortcut" value='N'/>
			</c:import>
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" />
		</td>
	</tr>
</table>

<table class="title_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<td class="title_form_label" width="70px" >預查編號</td>
		<td class="title_form_value" width="100px">
			<input type="text" class="field_RO" id="prefixNo" name="prefixNo" size="10" value="" readonly />
		</td>
		<td style="text-align: left" width="80px">
			<div id="applyWay" style="color:#008000"></div>
		</td>
		<td class="title_form_label" width="70px">預查種類</td>
		<td class="title_form_value" width="400px">
			<input type="checkbox" name="setup" value="true">設立
			<input type="checkbox" name="changeName" value="true">名稱變更
			<input type="checkbox" name="changeItem" value="true">所營變更
			<input type="hidden" name="orgType" value="">
			&nbsp;&nbsp;&nbsp;<input type="checkbox" name="closed" value="Y"><span id="closedBlock">閉鎖性股份有限公司</span>
		</td>
		<td class="title_form_label" width="80px">電子流水號</td>
		<td class="title_form_value">
			<input type="text" class="field_RO" id="telixNo" name="telixNo" size="20" value="" readonly />
		</td>
	</tr>
	<tr>
		<td class="title_form_label">統一編號</td>
		<td class="title_form_value" colspan="2">
			<input type="text" class="field_RO" id="banNo" name="banNo" size="10" value="" readonly />
		</td>
		<td class="title_form_label">領件方式</td>
		<td class="title_form_value">
		    <input type="radio" name="getKind" value="3">線上列印  <!--2024/03/17 新增線上列印 -->
			<input type="radio" name="getKind" value="1">自取
			<input type="radio" name="getKind" value="2">郵寄
			&nbsp;&nbsp;&nbsp;掛號號碼
			<input type="text" class="field" id="postNo" name="postNo" size="12" maxlength="10" value="" />
		</td>
		<td class="title_form_value" colspan="2">
			<table width="255" align="left">
				<tr><td>
					<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
				</td>
				<td width="100" align="center">
					<input type="text" class="field_RO" id="current" size="10" value="" readonly />
 				</td>
				<td>
					<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
 				</td>
				</tr>
			</table>
		</td>
	</tr>
</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1"><span>案件資料</span></a></li>
		<li><a href="#fragment-2"><span>預查名稱</span></a></li>
		<li><a href="#fragment-3"><span>營業項目</span></a></li>
		<li><a href="#fragment-4"><span>案件流程</span></a></li>
		<li>
			<span>
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;訊息列:
				<input class="field_RO" type="text" name="ERRMSG" size="40" value="" style="border-bottom: 1px #000000 dashed;color:red;" readonly>
			</span>
		</li>
	</ul>
	<div id="fragment-1">
		<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="80px">特取名稱</td>
				<td class="td_form_white" width="200px">
					<input type="text" class="field_Q cmex" id="specialNameSp" name="specialNameSp" size="20" value="" />
				</td>
				<td class="td_form" width="100px">本次預查名稱</td>
				<td class="td_form_white" colspan="2">
					<span id=xCompanyName></span>
					<input type="hidden" class="field_RO" id="companyName" name="companyName" readonly size="30" value="">
				</td>
				<td class="td_form_white"><div id="reserveTip" style="color:#FF0000;font-weight:bold;" ></div></td>
			</tr>
			<tr>
				<td class="td_form">承辦人</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="staffName" name="staffName" size="10" value="" readonly />
				</td>
				<td class="td_form" width="100px">前次預查名稱</td>
				<td class="td_form_white" width="185px">
					<input type="text" class="field_RO cmex" id="lastCompanyName" name="lastCompanyName" size="20" value="" readonly />
				</td>
				<td class="td_form" width="100px">收件日期</td>
				<td class="td_form_white"><div id="receiveDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">現況<br/>主檔名稱</td>
				<td class="td_form_white">
					<span id=xMainFileCompanyName></span>
					<input type="hidden" class="field_RO" id="mainFileCompanyName" name="mainFileCompanyName" size="25" value="" readonly />
				</td>
				<td class="td_form">前次異動者</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="updateName" name="updateName" size="10" value="" readonly /><br/>
					<input type="button" class="toolbar_default" id="btnHisttoryList" name="btnHisttoryList" value="異動內容" />
				</td>
				<td class="td_form">收文登打</td>
				<td class="td_form_white"><div id="receiveKeyinDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form" width="80px">
					備註紀錄<br/>
					<input type="button" class="toolbar_default" id="btnHistor" name="btnHistor" value="備註歷史" />
				</td>
				<td class="td_form_white" colspan="3">
					<textarea class="content" id="remark1" name="remark1" cols="50" rows="4"></textarea>
				</td>
				<td class="td_form">分文日期</td>
				<td class="td_form_white"><div id="assignDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">&nbsp;</td>
				<td class="td_form_white" colspan="3">
					<input type="button" class="toolbar_default" id="btnForeign" name="btnForeign" value="外商\大陸商或合併(分割)消滅註記" />
				</td>
				<td class="td_form">審核日期</td>
				<td class="td_form_white" ><div id="approveDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form" >註記說明</td>
				<td class="td_form_white" colspan="3">
					<input type="text" class="field_RO" id="remark" name="remark" size="40" value="" readonly>(檢還,撤件專用)
				</td>
				<td class="td_form">發文登打</td>
				<td class="td_form_white"><div id="issueKeyinDateTime"></div></td>
			</tr>
			<tr>
				<td class="td_form">核覆結果</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="approveResultDesc" name="approveResultDesc" size="5" readonly />
					<select class="field" id="approveResult" name="approveResult" disabled style="display:none;">
						<option value="A">審查中</option>
						<option value="N">不予核准</option>
						<option value="Y">核准保留</option>
					</select>
					<input type="button" class="toolbar_default" value="馬上辦類型" id="doAtonce">
				</td>
				<td class="td_form">案件狀態</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="prefixStatusDesc" name="prefixStatusDesc" size="15" value="" readonly />
				</td>
				<td class="td_form">發文日期</td>
				<td class="td_form_white"><div id="closeDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form" >保留期限</td>
				<td class="td_form_white" colspan="3">
					<input type="text" class="field_RO" name="reserveDate" maxlength="10" size="12" value="" readonly>
					<input type="radio" name="reserveDays" value="180" disabled >保留半年
					<input type="radio" name="reserveDays" value="365" disabled >保留一年
					<input type="checkbox" name="reserveMark" value="Y">延長期限一個月
				</td>
				<td class="td_form">領件日期</td>
				<td class="td_form_white"><div id="getDateTime" ></div></td>
			</tr>
		</TABLE>

			<div id="tabs2">
				<ul>
					<li><a href="#tabs2-f1"><span>申請人資料</span></a></li>
					<li><a href="#tabs2-f2"><span>代理人資料</span></a></li>
					<li><a href="#tabs2-f3"><span>收件人資料</span></a></li>
					<li><a href="#tabs2-f4"><span>自由填列事項</span></a></li>
				</ul>
				<div id="tabs2-f1">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="100px">申請人姓名</td>
							<td class="td_form_white" width="180px">
								<input type="text" class="field_Q cmex" id="applyName" name="applyName" size="10" maxlength="130" value="" />
							</td>
							<td class="td_form" width="100px">身分ID</td>
							<td class="td_form_white" width="185px">
								<input type="text" class="field_Q" id="applyId" name="applyId" size="15" value="" />
							</td>
							<td class="td_form" width="100px">申請人電話</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="applyTel" name="applyTel" size="15" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form">所代表法人</td>
							<td class="td_form_white">
								<input type="text" class="field" id="applyLawName" name="applyLawName" maxlength="130" size="10" value="">
							</td>
							<td class="td_form">法人統編</td>
							<td class="td_form_white" colspan="3">
								<input type="text" class="field" id="applyBanNo" name="applyBanNo" size="15" value="">
							</td>
						</tr>
						<tr>
							<td class="td_form">申請人地址</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="applyAddr" name="applyAddr" size="80" value="" />
								<input type="button" class="toolbar_default" id="copyApplyToReceiver" name="copyApplyToReceiver" value="複製至收件人" />
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f2">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="120px">代理人姓名</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q cmex" id="attorName" name="attorName" size="10" value="" />
							</td>
							<td class="td_form" width="150px">證書號碼╱身分ID</td>
							<td class="td_form_white" width="160px">
								<input type="text" class="field_Q" id="attorNo" name="attorNo" size="15" value="" />
							</td>
							<td class="td_form" width="100px">聯絡電話</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="attorTel" name="attorTel" size="20" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form" width="120px">事務所所在地</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="attorAddr" name="attorAddr" size="80" value="" />
								<input type="button" class="toolbar_default" id="copyAttorToReceiver" name="copyAttorToReceiver" value="複製至收件人" />
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f3">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="120px">收件人姓名</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q cmex" id="receiveName" name="receiveName" size="10" value="" />
							</td>
							<td class="td_form" width="150px">簡訊通知回覆電話</td>
							<td class="td_form_white" width="160px">
								<input type="text" class="field_Q" id="contactCel" name="contactCel" size="15" value="" />
							</td>
							<td class="td_form" width="100px">寄件日期</td>
							<td class="td_form_white">
								<input type="text" class="field_RO" id="getDate" name="getDate" size="9" value="" readonly />
								<input type="text" class="field_RO" id="getTime" name="getTime" size="8" value="" readonly />
							</td>
						</tr>
						<tr>
							<td class="td_form">聯絡地址</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="receiveAddr" name="receiveAddr" size="80" value="" />
								<input type="hidden" id="changeType" name="changeType" value="" />
								<input type="hidden" name="sms" value="">
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f4">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;">&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form_white" colspan="6">（不納入預查審核項目）</td>
						</tr>
						<tr>
							<td class="td_form" width="180px">國外匯款使用英文名稱</td>
							<td class="td_form_white" colspan="5">
								<input class="field_Q" id="extRemitEname" name="extRemitEname" type="text"
								 size="50" value="" maxlength="120" />(僅提供銀行開戶使用)
						</tr>
					</TABLE>
				</div>
			</div>
		</div>
		<div id="fragment-2">
			<TABLE id="cedb1001s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: center;width:50px;">序號</td>
					<td class="td_form" style="text-align: center;width:240px;" >預查名稱</td>
					<td class="td_form" style="text-align: center;width:70px;" >審核結果</td>
					<td class="td_form" style="text-align: center;width:350px;" >同名組織/結果</td>
					<td class="td_form" style="text-align: center">同名註記</td>
				</tr>
				<!-- insert here -->
				
				<tr>
				</tr>
				<tr>
				</tr>
				<tr>
				</tr>
			</TABLE>
		</div>
		<div id="fragment-3">
			<TABLE id="cedb1002s" class="table_form" border="0" width="100%" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: center">
						<input class="toolbar_default2" type="button" id="tempSave" value="暫存" style="display : none"/>
					</td>
					<td class="td_form" style="text-align: left" colspan="6">
						<input class="toolbar_default" type="button" id="delBuItem" value="刪除營業項目">
						<input class="toolbar_default" type="button" id="buItemSelectAll" value="全部選取">
						<input class="toolbar_default" id="buItemUnSelectAll" type="button" value="取消全選">
						<input class="toolbar_default" type="button" value="除許可.." id="zz9999">
					</td>
				</tr>
				<tr>
					<td class="td_form" style="text-align:center;width:50px;padding-top:50px;" valign="top" rowspan="999">
					<input type="image" src="../../images/pre/btn_bi_query.gif" alt="所營項目輔助查詢"
						id="btnItemList" name="btnItemList"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_01.jsp', 'pre3001_01','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_import.gif" alt="匯入營業項目"
						id="btnImportCeItem" name="btnImportCeItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_02.jsp', 'pre3001_02','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_select.gif" alt="線上申辦所營項目"
						id="btnImportEicmItem" name="btnImportEicmItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_03.jsp', 'pre3001_03','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					</td>
					<td class="td_form" style="text-align:center;width:40px;">選取</td>
					<td class="td_form" style="text-align:center;width:60px;">序號</td>
					<td class="td_form" style="text-align:center;width:100px;">代碼</td>
					<td class="td_form" style="text-align:center;width:400px;">營業項目</td>
					<td class="td_form" style="text-align:left;width:50px;"><input class="toolbar_default" type="button"  value="  +  "></td>
					<td class="td_form" style="text-align:center">檢視訊息</td>
				</tr>
			</table>
		</div>
		<div id="fragment-4">
			<TABLE id="cedb1010s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">案件處理狀況</td>
					<td class="td_form" style="text-align: left">案件處理時間</td>
					<td class="td_form" style="text-align: left">處理人員</td>
					<td class="td_form" style="text-align: left">工作日數</td>
				</tr>
			</TABLE>
		</div>
	</div>

<input type="hidden" id="hiddenPrefixNos" name="hiddenPrefixNos" value="<%=obj.getHiddenPrefixNos()%>">
<input type="hidden" id="hiddenSpecialName" name="hiddenSpecialName" value="">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="focus" name="focus" value="">
<input type="hidden" id="functionName" name="functionName" value="atonce">
<input type="hidden" id="json" name="json" value="">
<input type="hidden" id="assignPrefixNo" name="assignPrefixNo" value="<%=obj.getAssignPrefixNo()%>">
<input type="hidden" id="autoApprove" name="autoApprove" value="false">
<input type="hidden" id="hRemark" name="hRemark" value="false">
<input type="hidden" id="prefixStatus" name="prefixStatus" value="">
<input type="hidden" id="atonceType" name="atonceType" value="">
<input type="hidden" id="everAtonce" name="everAtonce" value="">
<input type="checkbox" name="approveResultAllNo" value="true" style="display:none;" />

<div id="dialog-form" class="ui-widget">
	<DIV id="atonceRemarkDiv">
		<TABLE class="remark1Table" width="100%" cellspacing="0">
   			<!-- insert here -->
   		</TABLE>
	</DIV>
</div>

<%
if(null!=obj.getPrefixNos()) {
	for(String p : obj.getPrefixNos()) {
		out.write("<input type='hidden' name='prefixNos' value='"+p+"' />");
	}
}
%>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>