<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.ajax.PopSystemCodeMultiSelect">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>
<%
objList = obj.queryAll();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function doConfirm()
{
	var result = "";
	var checkedItem = document.all['checkedItem'];
	if(null!=checkedItem && checkedItem.length != undefined)
	{
		for (var i=0;i<checkedItem.length;i++)
		{
			if(checkedItem[i].checked)
			{
				var itemArray = checkedItem[i].value.split('-');
				
				if( "" != result )
					result = result + ","
				result = result + "{'CODE':'"+itemArray[0]+"','CODE_NAME':'"+itemArray[1]+"'}";
			}
		}
	}
	else
	{
		if(checkedItem.checked)
		{
			var itemArray = checkedItem[i].value.split('-');
			result = result + "{'CODE':'"+itemArray[0]+"','CODE_NAME':'"+itemArray[1]+"'}";
		}
	}
		
	window.returnValue = "["+result+"]";
	window.close();
}

function closeWindow(){
	window.returnValue = "[]";
	window.close();
}
$(document).ready(function(){
});
</script>
<title>請選擇<%=obj.getCodeKindDesc() %></title>
</head>
<body>
<form id="form1" name="form1" method="post" autocomplete="off">

<!-- HIDDEN DATA AREA -->
<input type="hidden" id="checkedList" name="checkedList" value="<%=obj.getCheckedList()%>">
<!-- HIDDEN DATA AREA -->

<table width="100%" cellspacing="0" cellpadding="0">
<!-- TITLE AREA -->
<tr><td class="bgList">
	<%=obj.getCodeKindDesc() %>
	<input class="toolbar_default" type="button" name="confirm" value="確定(OK)" onClick="doConfirm()">
	<input class="toolbar_default" type="button" name="close" value="取消(Cancel)" onClick="closeWindow()">
</td></tr>
<!-- TITLE AREA -->
<!-- QUERY ALL LIST AREA -->
<tr><td class="bgList">
<div id="listContainer" style="height:340">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" width="5%"><a class="text_link_w" onclick="return sortTable('listTBODY',0,false);" href="#">&nbsp;</a></th>
		<th class="listTH" width="10%"><a class="text_link_w" onclick="return sortTable('listTBODY',1,false);" href="#">代碼</a></th>
		<th class="listTH"><a class="text_link_w" onclick="return sortTable('listTBODY',2,false);" href="#">說明</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	out.write(obj.getQuerylist(objList, obj.getCheckedList()));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- QUERY ALL LIST AREA -->
</table>
</form>
</body>
</html>
