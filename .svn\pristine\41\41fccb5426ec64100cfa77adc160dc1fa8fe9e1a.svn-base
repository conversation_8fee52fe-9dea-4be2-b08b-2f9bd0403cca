package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * VIEW
 * (外國)分公司經理人資料檔(CEDB2013)
 *
 */
public class Cedb2013 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 公司統一編號 */
	private String banNo;
	/** 中文姓名 */
	private String managerName;
	/** 分公司統一編號 */
	private String branchNo;
	/** 外文姓名 */
	private String managerForName;
	/** 身分證件字號 */
	private String idNo;
	/** 身分證件種類 */
	private String idCode;
	/** 到職日期 */
	private String arriveDate;
	/** 郵遞區號 */
	private String managerZoneCode;
	/** 國籍別代碼 */
	private String nativeCode;
	/** 經理人地址 */
	private String address;
	/** 本次核准日期 */
	private String approveDate;
	/** 持有股份或出資額 */
	private String investAmt;
	/** 資料異動日期 */
	private String updateDate;
	/** 資料異動者 */
	private String updateUser;

	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getManagerName() {return managerName;}
	public void setManagerName(String s) {this.managerName = s;}
	public String getBranchNo() {return branchNo;}
	public void setBranchNo(String s) {this.branchNo = s;}
	public String getManagerForName() {return managerForName;}
	public void setManagerForName(String s) {this.managerForName = s;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String s) {this.idNo = s;}
	public String getIdCode() {return idCode;}
	public void setIdCode(String s) {this.idCode = s;}
	public String getArriveDate() {return arriveDate;}
	public void setArriveDate(String s) {this.arriveDate = s;}
	public String getManagerZoneCode() {return managerZoneCode;}
	public void setManagerZoneCode(String s) {this.managerZoneCode = s;}
	public String getNativeCode() {return nativeCode;}
	public void setNativeCode(String s) {this.nativeCode = s;}
	public String getAddress() {return address;}
	public void setAddress(String s) {this.address = s;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String s) {this.approveDate = s;}
	public String getInvestAmt() {return investAmt;}
	public void setInvestAmt(String s) {this.investAmt = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String s) {this.updateUser = s;}

}