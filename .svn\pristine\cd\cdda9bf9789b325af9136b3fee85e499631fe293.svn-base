package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;
import com.kangdainfo.tcfi.model.eicm.bo.RestrictionItem;

public class PRE8013_02 extends PRE8013{
	
	private String restrictionItemList;
	private String[] thisItem;
	private String[] addItem;
	private String optype;

	public String getRestrictionItemList() {return get(restrictionItemList);}
	public void setRestrictionItemList(String restrictionItemList) {this.restrictionItemList = checkSet(restrictionItemList);}
	public String[] getThisItem() {return thisItem;}
	public void setThisItem(String[] thisItem) {this.thisItem = thisItem;}
	public String[] getAddItem() {return addItem;}
	public void setAddItem(String[] addItem) {this.addItem = addItem;}
	public String getOptype() {return checkGet(optype);}
	public void setOptype(String s) {optype = checkSet(s);}

	private String masterCode;
	private String subCode1;
	private String subCode2;
	
	public String getMasterCode() {return checkGet(masterCode);}
	public void setMasterCode(String masterCode) {this.masterCode = checkSet(masterCode);}
	public String getSubCode1() {return checkGet(subCode1);}
	public void setSubCode1(String subCode1) {this.subCode1 = checkSet(subCode1);}
	public String getSubCode2() {return checkGet(subCode2);}
	public void setSubCode2(String subCode2) {this.subCode2 = checkSet(subCode2);}

	public Object doQueryOne() throws Exception {
		PRE8013_02 obj = this;
		Restriction o = ServiceGetter.getInstance().getPrefixService().getRestrictionById(Common.getInt(getId()));
		if (null!=o) {
			obj.setId(Common.get(o.getId()));
			obj.setName(o.getName());
			
			StringBuilder sb = new StringBuilder(1024);
			java.util.List<RestrictionItem> list = ServiceGetter.getInstance().getPrefixService().getRestrictionItemByRestrictionId(Common.getInt(getId()));
			if(list != null && list.size() > 0){
				RestrictionItem item;
				java.util.Iterator<?> it = list.iterator();
				while (it.hasNext()) {
					item = (RestrictionItem) it.next();
					sb.append("addRow('thisItemList', null, 'thisItem'");
					sb.append(",").append(Common.sqlChar(Common.get(item.getId())));
					sb.append(",").append(Common.sqlChar(item.getItemCode()));
					sb.append(",").append(Common.sqlChar(item.getBussinessItem())).append(");\n");
				}
			}
			obj.setRestrictionItemList(sb.toString());
		}else 
			this.setErrorMsg("查無該筆資料！");
		return obj;
	}

	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		return null;
	}

	@Override
	public void doCreate() throws Exception {}

	@Override
	public void doUpdate() throws Exception {
		if ("add".equals(getOptype())) {			
			if(addItem != null || addItem.length > 1)
				ServiceGetter.getInstance().getPrefixService().confirmRestrictionItem(getOptype(), getId(), addItem, getLoginUserId());
			else 
				throw new MoeaException("你必須先選擇右邊要增加的營業項目");
		} else if("remove".equals(getOptype())) {
			if(thisItem != null || thisItem.length > 1)
				ServiceGetter.getInstance().getPrefixService().confirmRestrictionItem(getOptype(), getId(), thisItem, getLoginUserId());
			else 
				throw new MoeaException("你必須先勾選左邊要移除的營業項目");		
		}
	}

	@Override
	public void doDelete() throws Exception {}
	
}
