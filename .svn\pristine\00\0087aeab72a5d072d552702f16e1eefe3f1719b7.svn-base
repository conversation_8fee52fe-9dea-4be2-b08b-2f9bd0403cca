package com.kangdainfo.util.lang;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;

public class CommonArrayUtils extends ArrayUtils {

	/**
	 * 將String array 轉為分割字串 適用於checkbox, radio ..etc
	 * 
	 * @param array
	 * @param separator
	 * @return
	 */
	public static String arrayToString(String[] array, String separator) {
		StringBuffer sb = new StringBuffer();
		if (array == null) {
			sb.append("");
		} else {
			for (int i = 0; i < array.length; i++) {
				if (i > 0) {
					sb.append(separator);
				}
				sb.append(array[i]);
			}
		}
		return sb.toString();
	}

	public static List<String> arrayToList(String[] array) {
		List<String> list = new ArrayList<String>();
		
		if(array == null) 
			return list;
		
		for (String str : array) {
			list.add(str);
		}
		return list;
	}

}