package com.kangdainfo.tcfi.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyQueryVo;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyShareholderVo;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCtrlitem;
import com.kangdainfo.tcfi.model.icms.dao.CsmdCtrlitemDao;
import com.kangdainfo.tcfi.service.Pre3008Service;
import com.kangdainfo.util.report.JasperReportMaker;

public class Pre3008ServiceImpl implements Pre3008Service {
	private Logger logger = Logger.getLogger(this.getClass());
	
	public void doRecover(String banNo) throws Exception {
		if(logger.isInfoEnabled()) logger.info("[doRecover][banNo:"+banNo+"]");
		GeneralityBusitem generBO = ServiceGetter.getInstance().getNoPayMarkService().getByBanNo(banNo);
		if(null!=generBO) {
			String telixNo = generBO.getTelixNo();
			if(null!=telixNo && !"0000".equals(telixNo) && !"".equals(telixNo) ) {
				ServiceGetter.getInstance().getPrefixService().saveOssmFeeMainByTelixNo(telixNo);
			}
		}
		ServiceGetter.getInstance().getNoPayMarkService().updateNotUsed(banNo);
	}

	public CmpyQueryVo findCmpyQueryVo(String banNo) {
		if(logger.isInfoEnabled()) logger.info("[findCmpyQueryVo][banNo:"+banNo+"]");
		CmpyQueryVo vo = null;
		if(!"".equals(Common.get(banNo))) {
			Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
			if(null!=cedb2000) {
				vo = new CmpyQueryVo();
				vo.setBanNo(Common.get(cedb2000.getBanNo()));
				vo.setCompanyName(Common.get(cedb2000.getCompanyName()));
				vo.setPrefixNo(Common.get(cedb2000.getPrefixNo()));
				vo.setSpecialName(Common.get(cedb2000.getPartName()));
				vo.setOrgType(Common.get(cedb2000.getOrgnType()));
				vo.setOrgName(Common.get(cedb2000.getOrgnName()));
				vo.setRespName(Common.get(cedb2000.getRespName()));
				vo.setCompanyStatus(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode( Common.get(cedb2000.getStatusCode())));
				vo.setRegUnit(Common.get(cedb2000.getRegUnit()));
				vo.setRegUnitName(Common.get(cedb2000.getRegUnitName()));
				vo.setCompanyTel(Common.get(cedb2000.getTelNo()));
				vo.setCompanyAddr(Common.get(cedb2000.getCompanyAddr()));
				vo.setCapitalAmount(Common.get(cedb2000.getCapitalAmt()));
				vo.setRealAmt(Common.get(cedb2000.getRealAmt()));
				vo.setControlItem(convertControlItem(Common.get(cedb2000.getControlItem())));
				vo.setTotBranch(Common.get(cedb2000.getTotBranch()));
				vo.setInvestmentCode(Common.get(cedb2000.getInvestmentCode()));
				vo.setOwnerType(Common.get(cedb2000.getOwnerType()));
				vo.setSetupDate(Common.get(cedb2000.getSetupDate()));
				vo.setApproveWord(Common.get(cedb2000.getApproveWord()));
				vo.setApproveNo(Common.get(cedb2000.getApproveNo()));
				vo.setChangeDate(Common.get(cedb2000.getChangeDate()));
				vo.setChangeWord(Common.get(cedb2000.getChangeWord()));
				vo.setChangeNo(Common.get(cedb2000.getChangeNo()));
				vo.setSuspendDate(Common.get(cedb2000.getSuspendDate()));
				vo.setSuspendWord(Common.get(cedb2000.getSuspendWord()));
				vo.setSuspendNo(Common.get(cedb2000.getSuspendNo()));
				vo.setSuspendUnit(Common.get(cedb2000.getSuspendUnit()));
				vo.setSuspendBegDate(Common.get(cedb2000.getSuspendBegDate()));
				vo.setSuspendEndDate(Common.get(cedb2000.getSuspendEndDate()));
				vo.setEndDate(Common.get(cedb2000.getEndDate()));
				//公司狀態存活時(01,02,03),不顯示解撤廢日
				if( "01".equals(Common.get(cedb2000.getStatusCode()))
					|| "02".equals(Common.get(cedb2000.getStatusCode()))
					|| "03".equals(Common.get(cedb2000.getStatusCode()))
						) {
					vo.setEndDate("");
				}
				vo.setCedb2002s(cedb2002Dao.findByBanNo(banNo));
				vo.setShareholders(queryShareholdersByBanNo(banNo));
				vo.setNoNeedPay(ServiceGetter.getInstance().getNoPayMarkService().getNoPayMark(banNo));
				vo.setClosed(Common.get(cedb2000.getClosed()));
				Map<String,Object> cmpyMap = queryCmpyInfoByBanNo(banNo);
				if(null!=cmpyMap) {
					vo.setChinaCode(Common.get(cmpyMap.get("CHINA_CODE")));
				}
			}
		}
		return vo;
	}

	private Map<String,Object> queryCmpyInfoByBanNo(String banNo) {
		Map<String,Object> result = null;
		if(!"".equals(Common.get(banNo))) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" C.CHINA_CODE");
			sqljob.appendSQL("FROM ICMS.CSMX_CMPY_SEQNO_MAP M");
			sqljob.appendSQL("INNER JOIN ICMS.CSMM_CMPY_INFO C on C.BAN_NO=M.BAN_NO and C.CMPY_MODIF_NO=M.CMPY_MODIF_NO" );
			sqljob.appendSQL("WHERE (M.IS_NEWEST='Y' or M.IS_NEWEST='X')");
			sqljob.appendSQL("AND M.BAN_NO=?");
			sqljob.addParameter(banNo);
			List<Map<String,Object>> maps = ServiceGetter.getInstance().getIcmsGeneralQueryDao().queryForList(sqljob);
			if(null!=maps && !maps.isEmpty()) {
				result = maps.get(0);
			}
		}
		return result;
	}

	private List<CmpyShareholderVo> queryShareholdersByBanNo(String banNo) {
		List<CmpyShareholderVo> list = null;
		if(!"".equals(Common.get(banNo))) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("select");
			sqljob.appendSQL(" a.SORT_NO");
			sqljob.appendSQL(",a.NAME");
			sqljob.appendSQL(",a.ID_NO");
			sqljob.appendSQL(",nvl(a.POS_CODE,'')||' '||nvl(A.POS_NAME,'') as POS_CODE_NAME");
			sqljob.appendSQL(",nvl((select corp_name from icms.csmm_corp_list where ban_no=a.ban_no and cmpy_modif_no=m.corp_cmpy_modif_no and seq_no=a.corp_seq_no and rownum=1),'') as CORP_NAME");
			sqljob.appendSQL("FROM Icms.Csmm_Share_Holder a");
			sqljob.appendSQL("inner join Icms.Csmx_Cmpy_Seqno_Map m on m.Ban_No=a.Ban_No and m.Sh_Cmpy_Modif_No=a.Cmpy_Modif_No");
			sqljob.appendSQL("inner join Icms.Csmm_Cmpy_Info b on b.Ban_No=m.Ban_No and b.Cmpy_Modif_No=m.Cmpy_Modif_No" );
			sqljob.appendSQL("WHERE m.Is_Newest > 'N'");
			sqljob.appendSQL("AND a.ban_no=?");
			sqljob.appendSQL("ORDER BY 1");
			sqljob.addParameter(banNo);
			List<Map<String,Object>> maps = ServiceGetter.getInstance().getIcmsGeneralQueryDao().queryForList(sqljob);
			if(null!=maps && !maps.isEmpty()) {
				list = new ArrayList<CmpyShareholderVo>();
				CmpyShareholderVo vo;
				for(Map<String,Object> map : maps) {
					vo = new CmpyShareholderVo();
					vo.setSortNo(Common.get(map.get("SORT_NO")));
					vo.setName(Common.get(map.get("NAME")));
					vo.setIdNo(Common.get(map.get("ID_NO")));
					vo.setPositionName(Common.get(map.get("POS_CODE_NAME")));
					vo.setCorpName(Common.get(map.get("CORP_NAME")));
					list.add(vo);
				}
			}
		}
		return list;
	}

	private String convertControlItem(String ctrlItem) {
		String result = ctrlItem;
		if(!"".equals(ctrlItem)) {
			CsmdCtrlitem ci = csmdCtrlitemDao.findByCtrlItem(ctrlItem);
			if(null!=ci) {
				result += " "+ci.getItemName();
			}
		}
		return result;
	}

	public File generateRptPdf(String banNo) throws Exception {
		if(logger.isInfoEnabled()) logger.info("[generateRptPdf][banNo:"+banNo+"]");
		File report = null;
		try {
	        String reportPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre3008.jasper");

	        Map<String, Object> parameters = new HashMap<String,Object>();
	        parameters.put("printDate", "列印日期："+Datetime.formatRocDate(Datetime.getYYYMMDD()));
	        parameters.put("printTime", "列印時間："+Datetime.formatRocTime(Datetime.getHHMMSS()));
	        parameters.put("org", "company");
	        CmpyQueryVo vo = findCmpyQueryVo(banNo);
	        if(null!=vo) {
	        	//format
	        	//vo.setCapitalAmount( Common.MoneyFormat(vo.getCapitalAmount(),0,true) );
	        	//vo.setRealAmt( Common.MoneyFormat(vo.getRealAmt(),0,true) );
	        	vo.setSetupDate(Datetime.formatRocDate(vo.getSetupDate()));
	        	vo.setChangeDate(Datetime.formatRocDate(vo.getChangeDate()));
	        }
			List<CmpyQueryVo> dataList = new ArrayList<CmpyQueryVo>();
			dataList.add(vo);
			report = JasperReportMaker.makePdfReport(dataList, parameters, reportPath);
		} catch (Exception e) {
	    	e.printStackTrace();
	    	throw e;
		}
	    return report;
	}

	private Cedb2000Dao cedb2000Dao;
	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}

	private Cedb2002Dao cedb2002Dao;
	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}

	private Cedb2004Dao cedb2004Dao;
	public Cedb2004Dao getCedb2004Dao() {return cedb2004Dao;}
	public void setCedb2004Dao(Cedb2004Dao dao) {this.cedb2004Dao = dao;}
	
	private CsmdCtrlitemDao csmdCtrlitemDao;
	public CsmdCtrlitemDao getCsmdCtrlitemDao() {return csmdCtrlitemDao;}
	public void setCsmdCtrlitemDao(CsmdCtrlitemDao dao) {this.csmdCtrlitemDao = dao;}

}