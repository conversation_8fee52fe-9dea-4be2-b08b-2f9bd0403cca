<!DOCTYPE html>
<!--
程式目的：samename_queue 單檔維護
程式代號：pre9009
撰寫日期：103.11.14
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9009">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9008" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
   obj.setQueryAllFlag("true") ;
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE9009)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj = (com.kangdainfo.tcfi.view.pre.PRE9009)obj.queryOne();
		obj.setQueryAllFlag("true");
		// obj.setRcvNo(obj.getRcvNo());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj = (com.kangdainfo.tcfi.view.pre.PRE9009)obj.queryOne();
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if("deleteError".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE9009)obj.queryOne();
	}
}
if ( "true".equals(obj.getQueryAllFlag()) ) 
  objList = (java.util.ArrayList) obj.queryAll();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.prefixNo,"預查編號");
	alertStr += checkEmpty(form1.status,"執行狀態") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function init() {
	if ( form1.state.value == "init" )
		document.getElementById("listContainer").style.display = 'none';
	else
	    document.getElementById("listContainer").style.display = '';
}

function queryOne(id){
	$('#id').val(id); 
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
} 

$(document).ready(function() {
	
	$('#queryOK').click(function(){
		form1.state.value = "queryAll";
		form1.submit();
	});
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "insert":  // 新增時只能填預查編號
				$("#remark").prop("readonly", true);
				$("#processTime").prop("readonly", true);
				$("#status").prop("disabled", true);
				$("#status").val(0);
				break;
			case "update": 
				$("#remark").prop("readonly", false);
				$("#processTime").prop("readonly", false);
				$("#status").prop("readonly", false);
				break;
			case "clear":
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
	
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9009'/>
</c:import>


<!--Query區============================================================-->
<div id="queryContainer" style="width:600px;height:200px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form">預查編號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_prefixNo" size="30" maxlength="9" value="<%=obj.getQ_prefixNo()%>">
        </td>
    </tr>
    <tr>
    <td class="td_form">執行狀態：</td>
        <td class="td_form_white"> 
           <select name="q_status" class="field_Q">
           <option value=""></option>
           <option value="0">待執行</option>
           <option value="1">執行中</option>
           <option value="2">執行成功</option>
           <option value="3">執行失敗</option>
        </td>
    </tr>
    <tr> 
    <td class="td_form">異動日期：</td>
        <td class="td_form_white"> 
           <%=View.getPopCalendar("field_Q", "q_dateStart", obj.getQ_dateStart())%>至 <%=View.getPopCalendar("field_Q","q_dateEnd",obj.getQ_dateEnd()) %>
        </td>
    </tr> 
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="button" id="queryOK" name="queryOK" value="確　　定" >
			<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<!--Toolbar區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bgToolbar" style="text-align:left">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
    <input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
	  <jsp:param name="btnInsert" value="Y" />
	  <jsp:param name="btnQueryAll" value="Y" />
	  <jsp:param name="btnUpdate" value="Y" />
	  <jsp:param name="btnDelete" value="Y" />
	  <jsp:param name="btnClear" value="Y" />
	  <jsp:param name="btnConfirm" value="N" />
	  <jsp:param name="btnConfirmSave" value="Y" />
	  <jsp:param name="btnListHidden" value="N" />
	  <jsp:param name="btnPreview" value="N" />
	  <jsp:param name="btnCancel" value="N" />
	  <jsp:param name="btnListPrint" value="N" />
    </jsp:include>
</td></tr>

<!--Form區============================================================-->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
            <td nowrap class="td_form" width="20%">預查編號：</td>
            <td nowrap class="td_form_white" width="30%">
              <input class="field" type="text" id="prefixNo" name="prefixNo" size="30" maxlength="9" value="<%=obj.getPrefixNo()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="20%">處理結果備註：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" id="remark" name="remark" size="50" maxlength="1000" value="<%=obj.getRemark()%>">
            </td>  
        </tr>    
        <tr>
            <td nowrap class="td_form" width="20%">處理時間(微秒)：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" id="processTime" name="processTime" size="50" maxlength="1000" value="<%=obj.getProcessTime()%>">
            </td>  
        </tr>
        <tr>
            <td class="td_form" width="15%">執行狀態：</td>
			<td class="td_form_white" width="35%"> 
				<select id="status" name="status">
					<option value="" <%="".equals( obj.getStatus() )?"selected":""%>></option>
           			<option value="0" <%="0".equals( obj.getStatus() )?"selected":""%>>待執行</option>
           			<option value="1" <%="1".equals( obj.getStatus() )?"selected":""%>>執行中</option>
           			<option value="2" <%="2".equals( obj.getStatus() )?"selected":""%>>執行成功</option>
           			<option value="3" <%="3".equals( obj.getStatus() )?"selected":""%>>執行失敗</option>
			</td>
        </tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" height="200">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
  	<th class="listTH" style="width:30px"><a class="text_link_w">NO.</a></th>
    <th class="listTH" style="text-align:left;width:300px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    <th class="listTH" style="text-align:left;width:300px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">處理結果備註</a></th>
    <th class="listTH" style="text-align:left;width:300px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">處理時間(微秒)</a></th>
    <th class="listTH" style="text-align:left;width:80px;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">執行狀態</a></th>
    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">異動日期</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false, false, false, false, false};
  boolean displayArray[] = {false, true, true, true, true, true};
  String[] alignArray = {"left", "left", "left", "left", "left", "left"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(), true, null, null, "", true, false));
  %>
  </tbody>
</table>
</div>
</td></tr>

</table>	
</form>
</body>
</html>