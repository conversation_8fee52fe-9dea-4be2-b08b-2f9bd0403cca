<!DOCTYPE html>
<!--
程式目的：預查收費作業
程式代號：pre5001
撰寫日期：105.12.12
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE5001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>    
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE5001" />
</jsp:include>
<%
String fileName = obj.getReceiptNo()+".pdf";
if("getNoAndSave".equals(obj.getState())) {
	//空白表單收文 或 一維條碼$("#state").val("init");
	obj.save();
} else if ("saveSuccess".equals(obj.getState())) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		obj.outputFile(response, report, fileName);
		out.clear();
		out = pageContext.pushBody();
	}
	obj.setState("init");
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function keyDown() {
	if (event.keyCode==13) {
		console.log('1');
		ajaxGetPayName();
	}
}

function ajaxGetPayName() {
	$.post( getVirtualPath() + "tcfi/ajax/ajaxPre5001.jsp?q=" + $('#telixNo').val(), function( data ) {
		var payName = "";
		try {
			payName = data;
			if( data.length > 260 ) throw 'ERROR'; 
		} catch(e) {
			payName = "";
		}
		$('#payName').val(payName);
	});
}

$( document ).ready(function() {
	var msg ="";
	if ( $("#state").val() == "saveSuccess" ) {
		var target = 'PRE5001_'+randomUUID().replace(/\-/g,"");
		window.open("",target);
		form1.target = target;
		form1.submit();
		$("#state").val("init");
		form1.target = '';
	}
	$("#getNoAndSave").click(function() {
		if (!$("input[name=payType]").is(':checked') ) {
    		msg = "請選擇繳費方式";
    		showMsgBar(msg);
    		return;
		}
		if( $("input[name=payType]:checked").val() == 1 && $("input[name=chNo]").val() == '' ) {
			msg = "請輸入支匯票單據號";
			showMsgBar(msg);
	    	return;
		}
		if( "" == $("#telixNo").val()) {
			if(!confirm("無網路收文號，請確定是否為空白表單收文")) {
				return;
			} else {
				if ("" == $("#payName").val()) {
					alert("空白表單收文必須輸入繳款人姓名");
					return;
				}
			}
		}
		form1.action = 'pre5001.jsp';
		form1.state.value = 'getNoAndSave';

		form1.getNoAndSave.setAttribute('disabled', true);
		var oldValue = form1.getNoAndSave.value; 
		form1.getNoAndSave.value = "...請稍後...";
	    setTimeout(function(){
	    	form1.getNoAndSave.value = oldValue;
	    	form1.getNoAndSave.removeAttribute('disabled');
	    }, 3000)
   		form1.submit();
	});
});
</script>
</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE5001'/>
</c:import>

<!--Toolbar區============================================================-->
<table>
	<tr><td style="text-align:left">
	   	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
		<input type="hidden" name="receiptNo" value="<%=obj.getReceiptNo()%>">
		<input type="hidden" name="printPayType" value="<%=obj.getPrintPayType()%>">
		<input type="hidden" name="printChNo" value="<%=obj.getPrintChNo()%>">
		<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	</td></tr>
</table>

<!--Form區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg">
	<table class="table_form">
        <tr>
			<td class="td_form">網路收文號</td>
			<td class="td_form_white">
				<input type="text" class="field_Q" id="telixNo" name="telixNo" size="20" maxLength="11" value="" onKeyDown="keyDown()" />
            </td>
        </tr>
        <tr>
        	<td class="td_form">繳款人姓名</td>
        	<td class="td_form_white">
				<input type="text" class="field_Q cmex" id="payName" name="payName" maxlength="20"/>
            </td>
        </tr>
        <tr>
        	<td class="td_form">繳款方式</td>
        	<td class="td_form_white">
				<input type="radio" class="field_Q" name="payType" value="0"/>現金
				<input type="radio" class="field_Q" name="payType" value="1"/>匯票
				<input type="radio" class="field_Q" name="payType" value="5"/>支票&nbsp;&nbsp;
				支匯票單據號<input type="text" class="field_Q" name="chNo" maxlength="15">
				<br />
				<input type="radio" class="field_Q" name="payType" value="6"/>悠遊卡
				<input type="radio" class="field_Q" name="payType" value="7"/>台灣Pay
            </td>
        </tr>
        <tr>
        	<td class="td_form"></td>
        	<td class="td_form_white">
        		<input type="button" class="toolbar_default" id="getNoAndSave" name="getNoAndSave" value="取號存檔">
        	</td>
        </tr>
	</table>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>