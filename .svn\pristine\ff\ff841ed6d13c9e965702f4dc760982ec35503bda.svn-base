package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.util.PrefixConstants;

/*
程式目的：郵寄掛號登錄及維護
程式代號：pre2003
撰寫日期：103.06.16
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE2003 extends SuperBean {
	// query area
	private String q_prefixNo;
	private String cLetterNo;
	private String lLetterNo;
	private String postType;
	private String authLetterNo;
	
	// 不會顯示在畫面上但用的到的欄位
	private String sysTime;
	private String prefixNo;
	// 申請人資料
	private String applyName;
	private String applyAddr;
	private String applyTel;
	private String companyName;
	// 收件人資料
	private String receiveName;
	private String atonce;
	private String atonceRemark;
	private String receiveAddr;
	// 申請案資料
	private String letterKind;
	private String letterNo;
	private String getKind;
	private String getKindRemark;
	private String receiveDate;
	private String receiveTime;
	private String approveResult;
	private String approveDate;
	private String approveTime;
	private String getDate;
	private String getTime;
	private String refundDate;   // 退費日期
	private String refundTime;   // 退費時間
	private String refundReason;  // 退費原因
	private String refundReasonOther;   //　其他退費原因
	private String caseHow;          // 處理方式
	private String caseHowOther;     // 其他處理方式
	
	private String total;
	private String now;
	
	static List<Cedb1027> cedb1027List = new ArrayList<Cedb1027>();
	private int index;
	int index_2;
	
	public List<Cedb1027> getCedb1027List() {
		return cedb1027List;
	}
	public void setCedb1027List(List<Cedb1027> cedb1027List) {
		PRE2003.cedb1027List = cedb1027List;
	}
	public int getIndex() {return index;}
	public void setIndex(int index) {this.index = index;}
	
	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	public String getCLetterNo() {return checkGet(cLetterNo);}
	public void setCLetterNo(String s) {cLetterNo = checkSet(s);}
	public String getLLetterNo() {return checkGet(lLetterNo);}
	public void setLLetterNo(String s) {lLetterNo = checkSet(s);}
	public String getPostType() {return checkGet(postType);}
	public void setPostType(String s) {postType = checkSet(s);}
	public String getAuthLetterNo() {return checkGet(authLetterNo);}
	public void setAuthLetterNo(String s) {authLetterNo = checkSet(s);}
	
	public String getSysTime() {return checkGet(sysTime);}
	public void setSysTime(String s) {sysTime = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getApplyAddr() {return checkGet(applyAddr);}
	public void setApplyAddr(String s) {applyAddr = checkSet(s);}
	public String getApplyTel() {return checkGet(applyTel);}
	public void setApplyTel(String s) {applyTel = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	
	
	public String getReceiveName() {return checkGet(receiveName);}
	public void setReceiveName(String s) {receiveName = checkSet(s);}
	public String getAtonce() {return checkGet(atonce);}
	public void setAtonce(String s) {atonce = checkSet(s);}
	public String getAtonceRemark() {return checkGet(atonceRemark);}
	public void setAtonceRemark(String s) {atonceRemark = checkSet(s);}
	
	public String getReceiveAddr() {return checkGet(receiveAddr);}
	public void setReceiveAddr(String s) {receiveAddr = checkSet(s);}
	
	public String getLetterKind() {return checkGet(letterKind);}
	public void setLetterKind(String s) {letterKind = checkSet(s);}
	public String getLetterNo() {return checkGet(letterNo);}
	public void setLetterNo(String s) {letterNo = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {getKind = checkSet(s);}
	public String getGetKindRemark() {return checkGet(getKindRemark);}
	public void setGetKindRemark(String s) {getKindRemark = checkSet(s);}
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {receiveDate = checkSet(s);}
	public String getReceiveTime() {return checkGet(receiveTime);}
	public void setReceiveTime(String s) {receiveTime = checkSet(s);}
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {approveResult = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {approveDate = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {approveTime = checkSet(s);}
	public String getGetDate() {return checkGet(getDate);}
	public void setGetDate(String s) {getDate = checkSet(s);}
	public String getGetTime() {return checkGet(getTime);}
	public void setGetTime(String s) {getTime = checkSet(s);}
	public String getRefundDate() {return checkGet(refundDate);}
	public void setRefundDate(String s) {refundDate = checkSet(s);}
	public String getRefundTime() {return checkGet(refundTime);}
	public void setRefundTime(String s) {refundTime = checkSet(s);}
	public String getRefundReason() {return checkGet(refundReason);}
	public void setRefundReason(String s) {refundReason = checkSet(s);}
	public String getRefundReasonOther() {return checkGet(refundReasonOther);}
	public void setRefundReasonOther(String s) {refundReasonOther = checkSet(s);}
	public String getCaseHow() {return checkGet(caseHow);}
	public void setCaseHow(String s) {caseHow = checkSet(s);}
	public String getCaseHowOther() {return checkGet(caseHowOther);}
	public void setCaseHowOther(String s) {caseHowOther = checkSet(s);}
	public String getTotal() {return checkGet(total);}
	public void setTotal(String s) {total = checkGet(s);}
	public String getNow() {return checkGet(now);}
	public void setNow(String s) {now = checkGet(s);}
	
	
	public void init() {
		findLetterNo();
		//findDateAndTime();   // 進入本作業時自動帶出系統日期與時間至"取件日期", "取件時間"
	}
	
	public void findDateAndTime() {
		String date = Datetime.getYYYMMDD();
		String time = Datetime.getHHMMSS();
		date = date.substring(0,3) + "/" + date.substring(3,5) + "/" + date.substring(5);
		time = time.substring(0,2) + ":" + time.substring(2,4) + ":" + time.substring(4);
		setGetDate(date);
		setGetTime(time);
	}
	
	public void findLetterNo() {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		//Cedb1017 useless = new Cedb1017() ;
		List<Cedb1017> cedb1017List = ServiceGetter.getInstance().getPrefixService().queryCedb1017(cedbc000.getStaffUnit()) ;
		PRE2003 pre2003 = this ;
		if (!"".equals(cedb1017List.get(0).getUsedPostNo().trim())) 
			pre2003.setCLetterNo(cedb1017List.get(0).getUsedPostNo().substring(4));	//195916 => 16
		if (!"".equals(cedb1017List.get(1).getUsedPostNo().trim()))
			pre2003.setLLetterNo(cedb1017List.get(1).getUsedPostNo().substring(4));	//03375 => 5
		if (!"".equals(cedb1017List.get(2).getUsedPostNo().trim()))
			pre2003.setAuthLetterNo(cedb1017List.get(2).getUsedPostNo().substring(4));
	} // findLetterNo()
	
	public static String insertSysDate() {
		String date = Datetime.getYYYMMDD();
		String time = Datetime.getHHMMSS();
		date = date.substring(0,3) + "/" + date.substring(3,5) + "/" + date.substring(5);
		time = time.substring(0,2) + ":" + time.substring(2,4) + ":" + time.substring(4);
		return date+","+time;
	} // insertSysTime()
	
	
	// 傳入目前使用到的編號, 將其+1後return
	public String addLetterNo( String inputNo ) {
//		if(inputNo == null || inputNo.trim().isEmpty()){
//			return inputNo;// 修正於2024/01/23 將公文號若輸入空字串或null時，會一併回傳空字串或null，防止前後端報錯。
//		}
		
		String begin = inputNo.substring(0,1);
		String tempNo = inputNo.substring(1);
		tempNo = Integer.toString(Integer.parseInt(tempNo)+1);
		if (inputNo.substring(1,2).equals("0")) {			
			inputNo = inputNo.substring(0,2) + tempNo;			
		} else {
			inputNo = begin + tempNo;			
		}
		return inputNo;
	} // addLetterNo
	
	public PRE2003 returnForm() throws Exception {
		PRE2003 pre2003 = this;
		pre2003.setPrefixNo("");
		pre2003.setApplyName("");
		pre2003.setApplyAddr("");
		pre2003.setApplyTel("");
		pre2003.setCompanyName("");
		pre2003.setReceiveDate("");
		pre2003.setReceiveTime("");
		pre2003.setApproveDate("");
		pre2003.setApproveTime("");
		pre2003.setGetDate("");
		pre2003.setGetTime("");
		pre2003.setLetterNo("");
		pre2003.setLetterKind("");
		pre2003.setApproveResult("");
		pre2003.setGetKindRemark("");
		pre2003.setGetKind("");
		pre2003.setReceiveName("");
		pre2003.setReceiveAddr("");
		return pre2003;
	} // returnForm
	
	
	public void doCreate() throws Exception{	
	} // end doCreate()
		  
	public void doUpdate() throws Exception{
		
		// 先找cedb1023內有沒有prefixNo這個預查編號
		// 沒有的話就新增一筆; 有的話就更新receiveName, receiveAddr這兩個欄位
		if ( "Y".equals(getAtonce()) && ( getAtonceRemark() == null || "".equals(getAtonceRemark()))) {
			setErrorMsg("勾選馬上辦時，請輸入馬上辦註記");
			throw new MoeaException("勾選馬上辦時，請輸入馬上辦註記");
		}
		/*
		if ( "03".equals(getPostType()) && getGetDate().length() != 9 ) {
			System.out.println(getGetDate());
			setErrorMsg("請輸入正確的日期格式：YYY/MM/DD");
			throw new MoeaException("請輸入正確的日期格式：YYY/MM/DD");
		}
		if ( "03".equals(getPostType()) && getGetTime().length()!= 8 ) {
			setErrorMsg("請輸入正確的時間格式：HH/MM/SS");
			throw new MoeaException("請輸入正確的時間格式：HH/MM/SS");
		}
		*/
		if ( "其他".equals( getRefundReason() ) && (getRefundReasonOther() == null || "".equals(getRefundReasonOther()))) {
			setErrorMsg("請輸入詳細退件原因");
			throw new MoeaException("請輸入詳細退件原因");
		}
		if ( "其他".equals( getCaseHow() ) && (getCaseHowOther() == null || "".equals(getCaseHowOther()))) {
			setErrorMsg("請輸入詳細處理方式");
			throw new MoeaException("請輸入詳細處理方式");
		}
		
		String cedb1023Flag = "doNothing" ;
		
		Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo(getPrefixNo());
		if (cedb1023==null){  //新增
			cedb1023 = new Cedb1023();
			cedb1023.setGetName(getReceiveName());
			cedb1023.setGetAddr(getReceiveAddr());
			cedb1023.setPrefixNo(getPrefixNo());
			cedb1023.setSms("");
			cedb1023.setContactCel("");
			cedb1023.setChangeType("");
			cedb1023Flag = "insert";
		} // end if
		else {               // 更新
			cedb1023.setGetName(getReceiveName());
			cedb1023.setGetAddr(getReceiveAddr());
			cedb1023.setPrefixNo(getPrefixNo());
			cedb1023Flag = "update";
		} // end else
		
		// 取得取件日期時間 getTime & getDate
		String getDate = Datetime.getYYYMMDD();
		String getTime = Datetime.getHHMMSS();
		String usedPostNo = "";
		
		getDate = Datetime.formatDate(getGetDate());
		getTime = Datetime.formatTime(getGetTime());
		usedPostNo = getLetterNo();
		
		
		Cedb1027 cedb1027 = new Cedb1027();
		cedb1027.setPrefixNo(getPrefixNo());
		cedb1027.setPostNo(usedPostNo);
		cedb1027.setGetDate(Datetime.formatDate(getGetDate()));
		cedb1027.setGetTime(Datetime.formatTime(getGetTime()));
		if ( "特約-普掛".equals(getLetterKind()) )// 修正於2024/01/23 修改掛號規則
			cedb1027.setPostType("01");
		else if ( "特約-限掛".equals(getLetterKind()) )// 修正於2024/01/23 修改掛號規則
			cedb1027.setPostType("02");
		else if ( "公文掛號".equals(getLetterKind()) )
			cedb1027.setPostType("03");
		else if ( "平信".equals(getLetterKind()) )
			cedb1027.setPostType("04");
		else if ( "限時".equals(getLetterKind()) )
			cedb1027.setPostType("05");
		else if ( "普通掛號".equals(getLetterKind()) )// 修正於2024/01/23 修改掛號規則
			cedb1027.setPostType("06");
		else if ( "限時掛號".equals(getLetterKind()) )// 修正於2024/01/23 修改掛號規則
			cedb1027.setPostType("07");
		
		cedb1027.setBackDate(Datetime.formatDate(getRefundDate()));
		cedb1027.setBackTime(Datetime.formatTime(getRefundTime()));
		if ( "其他".equals( getRefundReason() ) ) 
			cedb1027.setBackReason(getRefundReasonOther());
		else
		    cedb1027.setBackReason(getRefundReason());
		
		if ( "其他".equals( getCaseHow() ) )
			cedb1027.setOtherMethod(getCaseHowOther());
		else
			cedb1027.setOtherMethod(getCaseHow());
		
	    cedb1027.setAtonce(getAtonce());
	    cedb1027.setAtonceRemark(getAtonceRemark());

		Cedb1000 cedb1000 = new Cedb1000();
		cedb1000.setGetDate(getDate);
		cedb1000.setGetTime(getTime);
		cedb1000.setGetKind(getGetKind());
		cedb1000.setGetKindRemark(getGetKindRemark());
		cedb1000.setUpdateDate(Datetime.getYYYMMDD());
		cedb1000.setUpdateTime(Datetime.getHHMMSS());
		cedb1000.setUpdateIdNo(getLoginUserId());
		cedb1000.setPrefixNo(getPrefixNo());
		//備份
		ServiceGetter.getInstance().getBackupService().doBackup(cedb1000.getPrefixNo(), getLoginUserId());
		ServiceGetter.getInstance().getPre2003Service().doUpdate(cedb1023, cedb1027, cedb1000, cedb1023Flag, PrefixConstants.FUN_CODE_2003);
		setState("assignAndSaveSuccess");
		setErrorMsg("新增成功!");
	} // end doUpdate()		
		  
	public void doDelete() throws Exception{			
		
	} // end doDelete()	
	
	public Object doAlternateNext() throws Exception {
		if (index >= 0)
			index--;
		
		if (index+1 > cedb1027List.size())
			setNow("1");
		else if ( index+1 <= 0 )
			setNow(Integer.toString(cedb1027List.size()));
		else
			setNow(Integer.toString(cedb1027List.size() - index));
		PRE2003 pre2003 = this;
		if ( cedb1027List == null || cedb1027List.size() == 0 )
			return pre2003;
		if ( index >= cedb1027List.size() || index < 0 ) {
			index = 0;
            setErrorMsg("已至最後一筆");
		} // if
		
		pre2003.setLetterNo(cedb1027List.get(index).getPostNo());
		pre2003.setGetDate(Datetime.formatRocDate(cedb1027List.get(index).getGetDate()));
		pre2003.setGetTime(Datetime.formatRocTime(cedb1027List.get(index).getGetTime()));
		pre2003.setRefundDate(Datetime.formatRocDate(cedb1027List.get(index).getBackDate()));
		pre2003.setRefundTime(Datetime.formatRocTime(cedb1027List.get(index).getBackTime()));
		pre2003.setRefundReason(cedb1027List.get(index).getBackReason());
		pre2003.setAtonce(cedb1027List.get(index).getAtonce());
		pre2003.setAtonceRemark(cedb1027List.get(index).getAtonceRemark());
		pre2003.setRefundReason(cedb1027List.get(index).getBackReason());
		pre2003.setCaseHow(cedb1027List.get(index).getOtherMethod());
		if (!"招領逾期".equals(pre2003.getRefundReason()) && !"查無此人".equals(pre2003.getRefundReason()) && !"遷移不明".equals(pre2003.getRefundReason()) && !"地址欠詳".equals(pre2003.getRefundReason()) && !"查無此址".equals(pre2003.getRefundReason()) && !"查無拒收".equals(pre2003.getRefundReason())) {
	    	pre2003.setRefundReasonOther(cedb1027List.get(index).getBackReason());
	    } // if
	    if (!"不用寄".equals(pre2003.getCaseHow()) && !"原址寄".equals(pre2003.getCaseHow()) && !"改址".equals(pre2003.getCaseHow())) {
	    	pre2003.setCaseHowOther(cedb1027List.get(index).getOtherMethod());
	    } // if
		if ( "01".equals( cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("特約-普掛");
		} // if
		else if ( "02".equals( cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("特約-限掛");
		} // else if
		else if ( "03".equals(cedb1027List.get(index).getPostType() ) ) {
			pre2003.setLetterKind("公文掛號");
		} // end else
		else if ( "04".equals(cedb1027List.get(index).getPostType() ) ) {
			pre2003.setLetterKind("平信");
		} // end else
		else if ( "05".equals(cedb1027List.get(index).getPostType() ) ) {
			pre2003.setLetterKind("限時");
		} // end else
		else if ( "06".equals(cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("普通掛號");
		} // end else
		else if ( "07".equals(cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("限時掛號");
		} // end else
		else {
		} // end else
		return pre2003;
	} // doAlternate()
	
	public Object doAlternateBack() throws Exception {
		if ( index < cedb1027List.size() )
			index++;
		if (index+1 > cedb1027List.size())
			setNow("1");
		else if ( index+1 <= 0 )
			setNow(Integer.toString(cedb1027List.size()));
		else
			setNow(Integer.toString(cedb1027List.size() - index));
		
		PRE2003 pre2003 = this;
		if ( cedb1027List == null || cedb1027List.size() == 0 )
			return pre2003;
		if ( index >= cedb1027List.size() || index < 0 ) {
			index = cedb1027List.size()-1;
			setErrorMsg("已至第一筆");
		} // if
		
		pre2003.setLetterNo(cedb1027List.get(index).getPostNo());
		pre2003.setGetDate(Datetime.formatRocDate(cedb1027List.get(index).getGetDate()));
		pre2003.setGetTime(Datetime.formatRocTime(cedb1027List.get(index).getGetTime()));
		pre2003.setRefundDate(Datetime.formatRocDate(cedb1027List.get(index).getBackDate()));
		pre2003.setRefundTime(Datetime.formatRocTime(cedb1027List.get(index).getBackTime()));
		pre2003.setAtonce(cedb1027List.get(index).getAtonce());
		pre2003.setAtonceRemark(cedb1027List.get(index).getAtonceRemark());
		pre2003.setRefundReason(cedb1027List.get(index).getBackReason());
		pre2003.setCaseHow(cedb1027List.get(index).getOtherMethod());
		if (!"招領逾期".equals(pre2003.getRefundReason()) && !"查無此人".equals(pre2003.getRefundReason()) && !"遷移不明".equals(pre2003.getRefundReason()) && !"地址欠詳".equals(pre2003.getRefundReason()) && !"查無此址".equals(pre2003.getRefundReason()) && !"查無拒收".equals(pre2003.getRefundReason())) {
	    	pre2003.setRefundReasonOther(cedb1027List.get(index).getBackReason());
	    } // if
	    if (!"不用寄".equals(pre2003.getCaseHow()) && !"原址寄".equals(pre2003.getCaseHow()) && !"改址".equals(pre2003.getCaseHow())) {
	    	pre2003.setCaseHowOther(cedb1027List.get(index).getOtherMethod());
	    } // if
		if ( "01".equals( cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("特約-普掛");
		} // if
		else if ( "02".equals( cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("特約-限掛");
		} // else if
		else if ( "03".equals(cedb1027List.get(index).getPostType() ) ) {
			pre2003.setLetterKind("公文掛號");
		} // end else
		else if ( "04".equals(cedb1027List.get(index).getPostType() ) ) {
			pre2003.setLetterKind("平信");
		} // end else
		else if ( "05".equals(cedb1027List.get(index).getPostType() ) ) {// 2024/01/23 發現邏輯錯誤，修改成5
			pre2003.setLetterKind("限時");
		} // end else
		else if ( "06".equals(cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("普通掛號");
		} // end else
		else if ( "07".equals(cedb1027List.get(index).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
			pre2003.setLetterKind("限時掛號");
		} // end else
		else {
		} // end else
		return pre2003;
	} // doAlternate()
	
	public Object returnNullForm() {
		PRE2003 pre2003 = this;
		pre2003.setGetDate("");
		pre2003.setGetTime("");
		pre2003.setLetterNo("");
		pre2003.setLetterKind("");
		pre2003.setRefundDate("");
		pre2003.setRefundTime("");
		pre2003.setRefundReason("");
	    pre2003.setCaseHow("");
	    pre2003.setAtonce("");
	    pre2003.setAtonceRemark("");
	    return pre2003;
	}
	
		  
	public Object doQueryOne() throws Exception{ 
	
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo( getQ_prefixNo() );
		PRE2003 pre2003 = (PRE2003)returnNullForm() ;
			
		if ( cedb1000 == null ) {
			setErrorMsg("查無資料，請變更查詢條件");
			return returnForm();
		} // end if
		
		cedb1027List = ServiceGetter.getInstance().getPre2003Service().getCedb1027List(getQ_prefixNo());
		
		if ( cedb1027List == null || cedb1027List.size() <= 0 ) {
			setTotal("0");
			setNow("0");
			pre2003.setLetterNo("");
			pre2003.setLetterKind("");
		} // if
		else {
			index = cedb1027List.size()-1;
			setTotal(Integer.toString( cedb1027List.size()));
			setNow(Integer.toString(cedb1027List.size() - index));
			if ( "01".equals( cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
				pre2003.setLetterKind("特約-普掛");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("01");
				//pre2003.setCLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo("");
			} // if
			else if ( "02".equals( cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
				pre2003.setLetterKind("特約-限掛");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("02");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setAuthLetterNo("");
			} // else if
			else if ( "03".equals(cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {
				pre2003.setLetterKind("公文掛號");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("03");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
			} // end else
			else if ( "04".equals(cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {
				pre2003.setLetterKind("平信");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("03");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
			} // end else
			else if ( "05".equals(cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {
				pre2003.setLetterKind("限時");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("03");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
			} // end else
			else if ( "06".equals(cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
				pre2003.setLetterKind("普通掛號");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("03");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
			}
			else if ( "07".equals(cedb1027List.get(cedb1027List.size()-1).getPostType() ) ) {// 修正於2024/01/23 修改掛號規則
				pre2003.setLetterKind("限時掛號");
				pre2003.setLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
				//pre2003.setPostType("03");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo(cedb1027List.get(cedb1027List.size()-1).getPostNo());
			}
			else {
				//pre2003.setPostType("");
				//pre2003.setCLetterNo("");
				//pre2003.setLLetterNo("");
				//pre2003.setAuthLetterNo("");
			} // end else
		} // else
		pre2003.setPrefixNo(cedb1000.getPrefixNo());
		// 將申請人資料依序填入變數內
		pre2003.setApplyName(cedb1000.getApplyName());
		pre2003.setApplyAddr(cedb1000.getApplyAddr());
		pre2003.setApplyTel(cedb1000.getApplyTel());
		pre2003.setCompanyName(cedb1000.getCompanyName());
		// 將申請案資料依序填入
		pre2003.setReceiveDate(Datetime.formatRocDate(cedb1000.getReceiveDate()));
		pre2003.setReceiveTime(Datetime.formatRocTime(cedb1000.getReceiveTime()));
		//20151223 ui上要把核覆日期改為顯示發文日期
		pre2003.setApproveDate(Datetime.formatRocDate(cedb1000.getCloseDate()));
		pre2003.setApproveTime(Datetime.formatRocTime(cedb1000.getCloseTime()));
		//寫入個資軌跡 (查詢類)
		ServiceGetter.getInstance().getTrackLogService().insertApplyPerson("PRE2003", PrefixConstants.TRACK_LOG_SEARCH,
				cedb1000.getPrefixNo(), null, cedb1000.getApplyName(), cedb1000.getApplyTel(), cedb1000.getApplyAddr());
		if("".equals(Common.get(cedb1000.getGetDate()))) {
			//pre2003.setGetDate("");
			//pre2003.setGetTime("");
			pre2003.setLetterNo("");
			pre2003.setLetterKind("");
			pre2003.setRefundDate("");
			pre2003.setRefundTime("");
			pre2003.setRefundReason("");
		    pre2003.setCaseHow("");
		    pre2003.setAtonce("");
		    pre2003.setAtonceRemark("");
		} // if
		else {
			// 本案件已被領件 (不論自取或郵寄)
			
			// 查看是否有郵寄號碼
			Cedb1027 cedb1027 = ServiceGetter.getInstance().getPre2003Service().getCedb1027ByPrefixNo(cedb1000.getPrefixNo());
			if ( cedb1027 == null ) {
				//pre2003.setGetDate("");
				//pre2003.setGetTime("");
				pre2003.setLetterNo("");
				pre2003.setRefundDate("");
				pre2003.setRefundTime("");
				pre2003.setRefundReason("");
				pre2003.setRefundReasonOther("");
				pre2003.setCaseHow("");
				pre2003.setCaseHowOther("");
				pre2003.setAtonce("");
				pre2003.setAtonceRemark("");
			} // if
			else {
				// pre2003.setLetterKind(ServiceGetter.getInstance().getSystemCode07Loader().getCodeNameByCode(cedb1027.getPostType()));
				pre2003.setGetDate(Datetime.formatRocDate(cedb1027.getGetDate()));
				pre2003.setGetTime(Datetime.formatRocTime(cedb1027.getGetTime()));
				pre2003.setLetterNo(Common.get(cedb1027.getPostNo()));
				pre2003.setRefundDate(Datetime.formatRocDate(cedb1027.getBackDate()));
				pre2003.setRefundTime(Datetime.formatRocTime(cedb1027.getBackTime()));
				pre2003.setRefundReason(cedb1027.getBackReason());
				if (!"招領逾期".equals(pre2003.getRefundReason()) && !"查無此人".equals(pre2003.getRefundReason()) && !"遷移不明".equals(pre2003.getRefundReason()) && !"地址欠詳".equals(pre2003.getRefundReason()) && !"查無此址".equals(pre2003.getRefundReason()) && !"查無拒收".equals(pre2003.getRefundReason())) {
				  	pre2003.setRefundReasonOther(cedb1027.getBackReason());
				} // if
				pre2003.setCaseHow(cedb1027.getOtherMethod());
				if (!"不用寄".equals(pre2003.getCaseHow()) && !"原址寄".equals(pre2003.getCaseHow()) && !"改址".equals(pre2003.getCaseHow())) {
				  	pre2003.setCaseHowOther(cedb1027.getOtherMethod());
				} // if
				pre2003.setAtonce(cedb1027.getAtonce());
				pre2003.setAtonceRemark( cedb1027.getAtonceRemark());
			} // else
		} // else
		pre2003.setApproveResult(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(cedb1000.getApproveResult()));
		pre2003.setGetKindRemark(cedb1000.getGetKindRemark());
		pre2003.setGetKind(cedb1000.getGetKind());
		// 將收件人資料依序填入
		Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo( cedb1000.getPrefixNo() );
		if ( cedb1023 == null ) {
			pre2003.setReceiveName("");
			pre2003.setReceiveAddr("");
		} // if
		else {
			pre2003.setReceiveName(cedb1023.getGetName());
			pre2003.setReceiveAddr(cedb1023.getGetAddr());
			//寫入個資軌跡 (查詢類)
			ServiceGetter.getInstance().getTrackLogService().insertGetPerson("PRE2003", PrefixConstants.TRACK_LOG_SEARCH,
					cedb1000.getPrefixNo(), cedb1023.getGetName(), null, cedb1023.getGetAddr());
			
		} // end else
		return pre2003;
	} // end doQueryOne()
	  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {		
		return null ;
	} // doQueryAll()
	
	public File doPrintPdf() throws Exception {
		return null ;
	} // doPrintPdf()
	
	public SQLJob appendSQL( String processType ) {
		SQLJob sqljob = new SQLJob() ;
		return sqljob ;
	} // appendSQL()
	
	public void assignAndSave() throws Exception {
		try {
			//Cedb1017 useLess = new Cedb1017();
			if ( "Y".equals(getAtonce()) && ( getAtonceRemark() == null || "".equals(getAtonceRemark()))) {
				setErrorMsg("勾選馬上辦時，請輸入馬上辦註記");
				throw new MoeaException("勾選馬上辦時，請輸入馬上辦註記");
			}
			if ( "其他".equals( getRefundReason() ) && (getRefundReasonOther() == null || "".equals(getRefundReasonOther()))) {
				setErrorMsg("請輸入詳細退件原因");
				throw new MoeaException("請輸入詳細退件原因");
			}
			if ( "其他".equals( getCaseHow() ) && (getCaseHowOther() == null || "".equals(getCaseHowOther()))) {
				setErrorMsg("請輸入詳細處理方式");
				throw new MoeaException("請輸入詳細處理方式");
			}
			if ("01".equals(getPostType()) || "02".equals(getPostType()) || "03".equals(getPostType()) || "06".equals(getPostType()) || "07".equals(getPostType())) {// 修改於2024/01/24 配合特約內容調整
				CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
				Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
				// 只有特約-普掛與特約-限掛要給號  // 修正於2024/01/23 修改掛號規則(修改註解)
				List<Cedb1017> tempList1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017(cedbc000.getStaffUnit()) ;
				// assign掛號編號以後, 必須在將assign後的掛號編號寫回表格cedb1017
				// 因此設定好form區的欄位以後, 需要再宣告一個cedb1017物件
				// 並將預計要寫回表格資料存在cedb1017物件中
				// 最後再將cedb1017物件寫回cedb1017表格
				Cedb1017 cedb1017 = new Cedb1017();
				if ("01".equals(getPostType())) {   // 特約-普掛// 修正於2024/01/23 修改掛號規則(修改註解)
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(0).getUsedPostNo()));
					cedb1017.setPostType("01");
					cedb1017.setStartPostNo(tempList1017.get(0).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(0).getEndPostNo());
					cedb1017.setStaffUnit(tempList1017.get(0).getStaffUnit());
				} // end if
				else if ("02".equals(getPostType())) {  // 特約-限掛// 修正於2024/01/23 修改掛號規則(修改註解)
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(1).getUsedPostNo()));
					cedb1017.setPostType("02");
					cedb1017.setStartPostNo(tempList1017.get(1).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(1).getEndPostNo());
					cedb1017.setStaffUnit(tempList1017.get(1).getStaffUnit());
				} // end if
				else if ("06".equals(getPostType())) {  // 普掛 新增於2024/01/24 修改掛號規則
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(3).getUsedPostNo()));
					cedb1017.setPostType("06");
					cedb1017.setStartPostNo(tempList1017.get(3).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(3).getEndPostNo());
					cedb1017.setStaffUnit(tempList1017.get(3).getStaffUnit());
				} // end if
				else if ("07".equals(getPostType())) {  // 限掛 新增於2024/01/24 修改掛號規則
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(4).getUsedPostNo()));
					cedb1017.setPostType("07");
					cedb1017.setStartPostNo(tempList1017.get(4).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(4).getEndPostNo());
					cedb1017.setStaffUnit(tempList1017.get(4).getStaffUnit());
				} // end if
				else { // 公文
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(2).getUsedPostNo()));
					cedb1017.setPostType("03");
					cedb1017.setStartPostNo(tempList1017.get(2).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(2).getEndPostNo());
					cedb1017.setStaffUnit(tempList1017.get(2).getStaffUnit());
				} // else
				ServiceGetter.getInstance().getPre2003Service().doAssign(cedb1017);
			} // end if
			String cedb1023Flag = "doNothing" ;
			Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo(getPrefixNo());
			if (cedb1023==null){  //新增
				cedb1023 = new Cedb1023();
				cedb1023.setGetName(getReceiveName());
				cedb1023.setGetAddr(getReceiveAddr());
				cedb1023.setPrefixNo(getPrefixNo());
				cedb1023.setSms("");
				cedb1023.setContactCel("");
				cedb1023.setChangeType("");
				cedb1023Flag = "insert";
			} // end if
			else {               // 更新
				cedb1023.setGetName(getReceiveName());
				cedb1023.setGetAddr(getReceiveAddr());
				cedb1023.setPrefixNo(getPrefixNo());
				cedb1023Flag = "update";
			} // end else
					
				// 取得取件日期時間 getTime & getDate
			String getDate = "" ;
			String getTime = "";
			String usedPostNo = "";
			String postType = getPostType();
			if ( "01".equals( postType ) || "02".equals( postType ) || "03".equals( postType ) || "06".equals( postType ) || "07".equals( postType )) {// 修改於2024/01/24 配合特約內容調整
				CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
				Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
				Cedb1017 tempCedb1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), postType);
				usedPostNo = tempCedb1017.getUsedPostNo();
				getDate = Datetime.getYYYMMDD();
				getTime = Datetime.getHHMMSS();
			} // end if
			else {
				usedPostNo = Datetime.getHHMMSS();
				getDate = Datetime.getYYYMMDD();
				getTime = Datetime.getHHMMSS();
			} // else
			
			Cedb1027 cedb1027 = new Cedb1027();
			cedb1027.setPrefixNo(getPrefixNo());
			cedb1027.setPostNo(usedPostNo);
			cedb1027.setGetDate(getDate);
			cedb1027.setGetTime(getTime);
			cedb1027.setPostType(postType);
			
			
			cedb1027.setBackDate(Datetime.formatDate(getRefundDate()));
			cedb1027.setBackTime(Datetime.formatTime(getRefundTime()));
			if ( "其他".equals( getRefundReason() ) ) 
				cedb1027.setBackReason(getRefundReasonOther());
			else
			    cedb1027.setBackReason(getRefundReason());
			
			if ( "其他".equals( getCaseHow() ) )
				cedb1027.setOtherMethod(getCaseHowOther());
			else
				cedb1027.setOtherMethod(getCaseHow());
			
			cedb1027.setAtonce(getAtonce());
		    
		    if ( cedb1027.getAtonce() != null && "Y".equals(cedb1027.getAtonce()) ) {
		    	if ( getAtonceRemark() == null || "".equals( getAtonceRemark() ) ) {
		    		
		    		setErrorMsg("勾選馬上辦時請填寫註記欄位!");
		    		throw new MoeaException("勾選馬上辦時請填寫註記欄位!");
		    	} // if
		    } // if
		    
			cedb1027.setAtonceRemark(getAtonceRemark());
		    	
		    
		    		
			Cedb1000 cedb1000 = new Cedb1000();
			cedb1000.setGetDate(getDate);
			cedb1000.setGetTime(getTime);
			cedb1000.setGetKind(getGetKind());
			cedb1000.setGetKindRemark(getGetKindRemark());
			cedb1000.setUpdateDate(Datetime.getYYYMMDD());
			cedb1000.setUpdateTime(Datetime.getHHMMSS());
			cedb1000.setUpdateIdNo(getLoginUserId());
			cedb1000.setPrefixNo(getPrefixNo());
			//備份
			ServiceGetter.getInstance().getBackupService().doBackup(cedb1000.getPrefixNo(), getLoginUserId());
			ServiceGetter.getInstance().getPre2003Service().doSave(cedb1023, cedb1027, cedb1000, cedb1023Flag, PrefixConstants.FUN_CODE_2003);
			// doUpdate();
			setState("assignAndSaveSuccess");
			setErrorMsg("存檔成功!");
		} // try
		catch( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
		} // catch
	} // assignAndSave()
	
	public void rollBack() throws Exception {
		List<Cedb1027> cedb1027List = ServiceGetter.getInstance().getPre2003Service().getCedb1027List(getQ_prefixNo());
		if ( cedb1027List != null && cedb1027List.size() > 0 ) {
			Cedb1027 cedb1027 = new Cedb1027();
			cedb1027.setPrefixNo(getPrefixNo());
			cedb1027.setGetDate(Datetime.formatDate(getGetDate()));
			cedb1027.setGetTime(Datetime.formatTime(getGetTime()));
			Cedb1000 cedb1000 = new Cedb1000();
			cedb1000.setGetDate("");
			cedb1000.setGetTime("");
			cedb1000.setGetKind("");
			cedb1000.setUpdateDate("");
			cedb1000.setUpdateTime("");
			cedb1000.setUpdateIdNo("");
			cedb1000.setPrefixNo(getQ_prefixNo());
			//備份
			ServiceGetter.getInstance().getBackupService().doBackup(cedb1000.getPrefixNo(), getLoginUserId());
			ServiceGetter.getInstance().getPre2003Service().rollBack(cedb1000, cedb1027);
			setErrorMsg("取消成功!");
		} // if
		else {
			setErrorMsg("這筆預查編號已經沒有可供刪除的郵寄資料!");
		} // else
		// 
	} // rollBack()	
	
} // PRE2003