<!DOCTYPE html>
<!--
程式目的：營業項目限制條件維護
程式代號：PRE8013
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8013">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8013" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8013)obj.queryOne();
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		//alertStr += checkQuery();
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.code,"限制條件代碼");
		alertStr += checkEmpty(form1.name, "限制條件名稱");
		alertStr += checkEmpty(form1.enable, "狀態");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function init() {
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function checkURL(surl){
	var alertStr = "";
	if (form1.state.value=="insert" || form1.state.value=="insertError" || 
			form1.state.value=="update" || form1.state.value=="updateError") {
		alert("新增或修改狀態無法更換頁標籤，請先點選取消!");
	}else{
		if(form1.id.value==null || form1.id.value == ""){
			alertStr += "請先執行查詢或新增!";
		}
		
		if(alertStr.length!=0){
			alert("請先執行查詢, 若已查到資料則請選取其中一筆資料");
			return false;
		}else {
			form1.state.value="queryAll";
		}
		
		form1.action = surl;
		beforeSubmit();
		form1.submit();
	}
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->
<div id="queryContainer" style="width:700px;height:300px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
		<td nowrap class="queryTDLable">限制條件代碼：</td>
		<td nowrap class="queryTDInput">
			<input class="field_Q" type="text" name="q_code" size="7" maxlength="7" value="<%=obj.getQ_code()%>">
		</td>
	</tr>
	<tr>
		<td nowrap class="queryTDLable">限制條件名稱：</td>
		<td nowrap class="queryTDInput">
			<input class="field_Q" type="text" name="q_name" size="50" maxlength="100" value="<%=obj.getQ_name()%>">
		</td>
	</tr>
	<tr>
		<td nowrap class="queryTDLable">狀態：</td>
		<td nowrap class="queryTDInput">
			<select class="field_Q" name="q_enable">
	    		<%=View.getTextOption("Y;開啟;N;停用;", obj.getQ_enable(), 1)%>
	    	</select>	
		</td>
	</tr>
	<tr>
		<td nowrap class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定" >
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td>
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8013'/>
</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- TOOLBAR AREA -->
<tr><td style="text-align:left">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td>
<table cellpadding=0 cellspacing=0 valign="top">
	<tr>
		<td nowrap ID=t1 CLASS="tab_border1" width="100" height="25">限制條件</td>
		<td nowrap ID=t2 CLASS="tab_border2" width="100"><a href="#" onClick="return checkURL('pre8013_item.jsp');">營業項目</a></td>
	</tr>
	<tr>
		<td nowrap class="tab_line1"></td>
		<td nowrap class="tab_line2"></td>	
	</tr>
</table>
</td></tr>

<!-- FORM AREA -->
<tr><td class="bg">
	<table class="table_form" width="100%">
		<tr>
			<td nowrap class="td_form" width="15%"><font color="red">*</font>限制條件代碼：</td>
			<td nowrap class="td_form_white" width="85%">
				<input class="field" type="text" name="code" size="5" maxlength="5" value="<%=obj.getCode()%>">
			</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form"><font color="red">*</font>限制條件名稱：</td>
		  	<td nowrap class="td_form_white">
		  		<input class="field" type="text" name="name" size="80" maxlength="100" value="<%=obj.getName()%>">
		  	</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form">限制條件項目：</td>
		  	<td nowrap class="td_form_white">
				<input class="field" type="checkbox" name="nameSpecial" value="Y" <%="Y".equals(obj.getNameSpecial())?"checked":""%> >名稱標明專業&nbsp;
				<input class="field" type="checkbox" name="itemSpecial" value="Y" <%="Y".equals(obj.getItemSpecial())?"checked":""%> >營業項目限專業經營&nbsp;
				<input class="field" type="checkbox" name="otherSpecial" value="Y" <%="Y".equals(obj.getOtherSpecial())?"checked":""%> >其他
				<input class="field" type="text" name="otherNote" size="20" maxlength="50" value="<%=obj.getOtherNote()%>">
				<br>
				組織別：
				<%=View.getRadioBoxTextOption("field","orgType","01;公司;02;股份有限公司;03;限本國公司",obj.getOrgType())%>
				<br>　　　　
				<%=View.getRadioBoxTextOption("field","orgType","04;限中華郵政股份有限公司;05;限中央存款保險股份有限公司",obj.getOrgType())%>
			</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form">相關函號：</td>
		  	<td nowrap class="td_form_white">
		  		單位：
		  		<input class="field" type="text" name="relatedUnit" size="20" maxlength="20" value="<%=obj.getRelatedUnit()%>">
		  		日期：
		  		<%=View.getPopCalendar("field","relatedDate",obj.getRelatedDate())%>
		  		<input class="field" type="text" name="relatedNo" size="20" maxlength="20" value="<%=obj.getRelatedNo()%>">
		  		號函 
		  	</td>
		</tr>
		<tr>
			<td class="td_form"><font color="red">*</font>狀態：</td>
			<td class="td_form_white">
				<select class="field" name="enable">
		    		<%=View.getTextOption("Y;開啟;N;停用;", obj.getEnable(), 1)%>
		    	</select>	
			</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form">異動資訊：</td>
		  	<td nowrap class="td_form_white"> [
		    	<input class="field_RO" type="text" name="editID" size="10" value="<%=obj.getEditID()%>">
		    	/
		    	<input class="field_RO" type="text" name="editDate" size="7" value="<%=obj.getEditDate()%>">
		    	] 
			</td>
		</tr>
	</table>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
	<div id="listContainer" style="display:none;">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
		<thead id="listTHEAD">
		<tr>
			<th class="listTH" ><a class="text_link_w" >序號</a></th>
			<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">限制條件名稱</a></th>
			<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">標明專業</a></th>
			<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">營業項目專業經營</a></th>
			<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">組織別</a></th>
			<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">狀態</a></th>
		</tr>
		</thead>
		<tbody id="listTBODY">
		<%
		boolean primaryArray[] = {true,false,false,false,false,false,false};
		boolean displayArray[] = {false,true,true,true,true,true,true};
		String[] alignArray = {"center","left","center","center","left","center","center"};
		out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true,null,null,"",true,true,true));
		%>
		</tbody>
	</table>
	</div>
</td></tr>

</table>
</form>
</body>
</html>