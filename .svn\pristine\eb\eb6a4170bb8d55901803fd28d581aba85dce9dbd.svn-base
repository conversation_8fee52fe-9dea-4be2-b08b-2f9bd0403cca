<!DOCTYPE html>
<!--
程式目的：掛號編號維護
程式代號：pre8009
撰寫日期：103.04.24
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8009">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>    
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8009" />
</jsp:include>

<%
if ("update".equals(obj.getState())) {
	obj.update();
}
obj = (com.kangdainfo.tcfi.view.pre.PRE8009)obj.queryOne();	
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){

	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.LStartPostNo, "限時掛號 起號");  
	   alertStr += checkEmpty(form1.LEndPostNo, "限時掛號 訖號");  
	   alertStr += checkEmpty(form1.startPostNo, "普通掛號 起號");
	   alertStr += checkEmpty(form1.endPostNo, "普通掛號 訖號");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;	
}

function init(){
	//setDisplayItem("spanQueryAll,spanInsert,spanDelete,spanListPrint,spanListHidden","H");
}
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8009'/>
</c:import>

<!--Toolbar區============================================================-->
<table>
	<tr><td style="text-align:left">
	   	<input type="hidden" name="state" value="<%=obj.getState()%>">
		<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
		<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	</td></tr>
</table>

<!--Form區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg" >
	<div id="formContainer" style="height:110">
	<table class="table_form" width="100%" height="100%">
	    <tr>
            <td nowrap class="td_form" >特約-普通掛號&nbsp;&nbsp;起號：</td>
            <td nowrap class="td_form_white">
            	<input type="hidden" name="specialPostType" value="<%=obj.getSpecialPostType()%>"><!-- 修正於2024/01/24 改為特約-普掛 -->
				<input class="field" type="text" name="specialStartPostNo" size="12" maxlength="10" value="<%=obj.getSpecialStartPostNo()%>">
            </td>  
            <td nowrap class="td_form">迄號：</td>
            <td nowrap  class="td_form_white">
				<input class="field" type="text" name="specialEndPostNo" size="12" maxlength="10" value="<%=obj.getSpecialEndPostNo()%>">
            </td>  
            <td nowrap class="td_form">已用過的編號：</td>
            <td nowrap class="td_form_white">
				<input class="field_RO" type="text" name="specialUsedPostNo" size="12" maxlength="10" value="<%=obj.getSpecialUsedPostNo()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="15%">特約-限時掛號&nbsp;&nbsp;起號：</td><!-- 修正於2024/01/24 改為特約-限掛 -->
            <td nowrap class="td_form_white" width="15%">
            	<input type="hidden" name="specialLPostType" value="<%=obj.getSpecialLPostType()%>">
				<input class="field" type="text" name="specialLStartPostNo" size="12" maxlength="10" value="<%=obj.getSpecialLStartPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="15%">迄號：</td>
            <td nowrap class="td_form_white" width="15%">
				<input class="field" type="text" name="specialLEndPostNo" size="12" maxlength="10" value="<%=obj.getSpecialLEndPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="20%">已用過的編號：</td>
            <td nowrap class="td_form_white" width="20%">
				<input class="field_RO" type="text" name="specialLUsedPostNo" size="12" maxlength="10" value="<%=obj.getSpecialLUsedPostNo()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form">普通掛號&nbsp;&nbsp;起號：</td><!-- 修正於2024/01/24 新增普掛 -->
            <td nowrap class="td_form_white">
            	<input type="hidden" name="postType" value="<%=obj.getPostType()%>">
				<input class="field" type="text" name="startPostNo" size="12" maxlength="10" value="<%=obj.getStartPostNo()%>">
            </td>  
            <td nowrap class="td_form">迄號：</td>
            <td nowrap  class="td_form_white">
				<input class="field" type="text" name="endPostNo" size="12" maxlength="10" value="<%=obj.getEndPostNo()%>">
            </td>  
            <td nowrap class="td_form">已用過的編號：</td>
            <td nowrap class="td_form_white">
				<input class="field_RO" type="text" name="usedPostNo" size="12" maxlength="10" value="<%=obj.getUsedPostNo()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="15%">限時掛號&nbsp;&nbsp;起號：</td><!-- 修正於2024/01/24 新增限掛 -->
            <td nowrap class="td_form_white" width="15%">
            	<input type="hidden" name="lPostType" value="<%=obj.getlPostType()%>">
				<input class="field" type="text" name="lStartPostNo" size="12" maxlength="10" value="<%=obj.getlStartPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="15%">迄號：</td>
            <td nowrap class="td_form_white" width="15%">
				<input class="field" type="text" name="lEndPostNo" size="12" maxlength="10" value="<%=obj.getlEndPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="20%">已用過的編號：</td>
            <td nowrap class="td_form_white" width="20%">
				<input class="field_RO" type="text" name="lUsedPostNo" size="12" maxlength="10" value="<%=obj.getlUsedPostNo()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="15%">公文掛號&nbsp;&nbsp;起號：</td>
            <td nowrap class="td_form_white" width="15%">
            	<input type="hidden" name="authPostType" value="<%=obj.getAuthPostType()%>">
				<input class="field" type="text" name="authStartPostNo" size="12" maxlength="10" value="<%=obj.getAuthStartPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="15%">迄號：</td>
            <td nowrap class="td_form_white" width="15%">
				<input class="field" type="text" name="authEndPostNo" size="12" maxlength="10" value="<%=obj.getAuthEndPostNo()%>">
            </td>  
            <td nowrap class="td_form" width="20%">已用過的編號：</td>
            <td nowrap class="td_form_white" width="20%">
				<input class="field_RO" type="text" name="authUsedPostNo" size="12" maxlength="10" value="<%=obj.getAuthUsedPostNo()%>">
            </td>  
        </tr>       
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

</table>
</form>
</body>
</html>



