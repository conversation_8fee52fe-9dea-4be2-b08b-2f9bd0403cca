package com.kangdainfo.tcfi.service;

import java.util.List;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;

/**
 * 同音同義字維護(PRE8010)
 *
 */
public interface Pre8010Service {
	/** 查詢 同音同義字對照檔 by Id */
	public Cedbc058 getCedbc058ById(Integer id);
	/** 查詢 同音同義字對照檔 All Codes */
	public List<Cedbc058> getCedbc058AllCodes();
	/** 查詢 同音同義字對照檔 by sameName1 */
	public List<Cedbc058> getCedbc058BySameName(String sameName);
	/** 查詢 同音同義字對照檔 by sameName, sameName1 */
	public Cedbc058 getCedbc058BySameName(String sameName, String sameName1);
	/** 查詢 同音同義字對照檔 by sameName */
	public Cedbc058 getCedbc058BySameNameForDelete(String sameName);
	/** 查詢 同音同義字對照檔 (used by Pre0004Service 同音同義異動Index排程) */
	public List<Cedbc058> getCedbc058ByStatus(String id, String... status);
	/** 查詢 同音同義字對照檔 by Condition */
	public List<Cedbc058> getCedbc058ByCondition(String sameName1, String source);
	
	public Cedbc058 getCedbc058ByCheck(Cedbc058 obj);
	/** 修改 同音同義字對照檔 */
	public Cedbc058 updateCedbc058(Cedbc058 obj);
	/** 修改 同音同義字對照檔 */
	public void updateCedbc058BeUsed(Integer id);
	/** 新增 同音同義字對照檔 */
	public Cedbc058 insertCedbc058(Cedbc058 obj);
	/** 刪除 同音同義字對照檔 */
	public void deleteCedbc058(Integer id);
	/** 刪除 同音同義字對照檔  刪兩筆*/
	public void deleteCedbc058Both(Integer id, Integer id2);
	/** 將所有CAN_USE='Y'且ENABLED='F'的同音同義字改為ENABLED='Y' */
	public void updateAllCanUseToY();
}