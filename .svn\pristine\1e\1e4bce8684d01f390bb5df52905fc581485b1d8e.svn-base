package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;

public class Eedb1100Dao extends BaseDaoJdbc implements RowMapper<Eedb1100> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB1100 WHERE TELIX_NO = ?";
	public Eedb1100 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb1100> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public Eedb1100 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb1100 obj = null;
		if(null!=rs) {
			obj = new Eedb1100();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setCaseCode(rs.getString("CASE_CODE"));
			obj.setCaseType(rs.getString("CASE_TYPE"));
			obj.setTicketNo(rs.getString("TICKET_NO"));
			obj.setRegUnit(rs.getString("REG_UNIT"));
		}
		return obj;
	}

}
