package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;

/**
 * 排程(PRE0009)
 * 更新執行失敗的排程,讓執行失敗的排程重做
 */
public class Pre0009QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		//一站式同步排程
		ServiceGetter.getInstance().getUpdateOsssStatusService().updateResetErrorQueue();
	}

}