<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	version="2.5">
	<display-name>名稱及所營事業預查系統</display-name>

	<security-constraint>
		<web-resource-collection>
			<web-resource-name>Forbidden</web-resource-name>
			<url-pattern>/*</url-pattern>
			<http-method>OPTIONS</http-method>
			<http-method>PUT</http-method>
			<http-method>DELETE</http-method>
			<http-method>TRACE</http-method>
		</web-resource-collection>
		<auth-constraint />
	</security-constraint>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>/WEB-INF/applicationContext/applicationContext.xml</param-value>
	</context-param>
	<context-param>
		<param-name>filestoreLocation</param-name>
		<param-value>./filestoreLocation</param-value>
	</context-param>
	<context-param>
		<param-name>filestoreLimit</param-name>
		<param-value>15728640</param-value>
	</context-param>
	<context-param>
		<param-name>filestoreAllowedExtList</param-name>
		<param-value>gif,jpg,png,tif,doc,docx,htm,html,xls,xlsx,ppt,pptx,txt,rtf,pdf,xml,zip,rar</param-value>
	</context-param>
	<context-param>
		<param-name>deleteTempFilesOnStartup</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
		<param-name>open_all</param-name>
		<param-value>展開</param-value>
	</context-param>
	<context-param>
		<param-name>close_all</param-name>
		<param-value>閉合</param-value>
	</context-param>

	<listener>
		<listener-class>com.kangdainfo.web.listener.MySpringContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>com.kangdainfo.web.listener.MyServletContextListener</listener-class>
	</listener>

	<!-- ================ Filter (Authenticate) ================ -->
	<filter>
		<filter-name>authenticateFilter</filter-name>
		<filter-class>com.kangdainfo.web.filter.AuthenticateFilter</filter-class>
		<!-- 客製化 serviceLocator -->
		<init-param>
			<param-name>serviceLocator</param-name>
			<param-value>tw.gov.moea.aa.util.ServiceLocator</param-value>
		</init-param>
		<init-param>
			<param-name>accountVo</param-name>
			<param-value>tw.gov.moea.aa.vo.AccountVo</param-value>
		</init-param>
		<init-param>
			<param-name>aaUrl</param-name>
			<param-value>https://ncert.gcis.nat.gov.tw/moea_aa/</param-value>
		</init-param>
		<init-param>
			<param-name>aaLoginUrl</param-name>
			<param-value>https://ncert.gcis.nat.gov.tw/moea_portal/</param-value>
		</init-param>
		<init-param>
			<param-name>syncUrl</param-name>
			<param-value>https://ncert.gcis.nat.gov.tw/moea_aa/services/AuthoriseService</param-value>
		</init-param>
	</filter>
	<filter>
		<filter-name>setCharacterEncodingFilter</filter-name>
		<filter-class>com.kangdainfo.web.filter.SetCharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	
	<filter>
		<filter-name>ClickjackFilter</filter-name>
		<filter-class>org.owasp.esapi.filters.ClickjackFilter</filter-class>
		<init-param>
			<param-name>mode</param-name>
			<param-value>SAMEORIGIN</param-value>
		</init-param>
	</filter>

	<filter-mapping>
		<filter-name>setCharacterEncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter-mapping>
		<filter-name>authenticateFilter</filter-name>
		<url-pattern>/loginSSO.jsp</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>authenticateFilter</filter-name>
		<url-pattern>/home/<USER>/url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>authenticateFilter</filter-name>
		<url-pattern>/ajax/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>authenticateFilter</filter-name>
		<url-pattern>/tcfi/*</url-pattern>
	</filter-mapping>

	<filter-mapping>
		<filter-name>ClickjackFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
	<!-- 插旗監控 START -->
	<filter>
		<filter-name>flagChecker</filter-name>
		<filter-class>com.aceraeb.flagchecker.filter.CheckerFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>flagChecker</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<!-- 插旗監控 END -->
	
	<session-config>
		<session-timeout>240</session-timeout>
	</session-config>

	<welcome-file-list>
		<welcome-file>index.jsp</welcome-file>
	</welcome-file-list>

	<error-page>
		<error-code>404</error-code>
		<location>/error.jsp</location>
	</error-page>
	<error-page>
		<error-code>500</error-code>
		<location>/error.jsp</location>
	</error-page>
	<error-page>
		<exception-type>java.lang.Exception</exception-type>
		<location>/error.jsp</location>
	</error-page>

	<servlet>
		<servlet-name>CompressServlet</servlet-name>
		<servlet-class>com.granule.CompressServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	
	<servlet-mapping>
		<servlet-name>CompressServlet</servlet-name>
		<url-pattern>/combined.js</url-pattern>
	</servlet-mapping>
	
	<servlet-mapping>
		<servlet-name>CompressServlet</servlet-name>
		<url-pattern>/combined.css</url-pattern>
	</servlet-mapping>
	
	<!-- 前端中推會字型 -->
	<servlet>
		<servlet-name>CmexFontServlet</servlet-name>
		<servlet-class>tw.gov.moea.font.servlet.CmexFontServlet</servlet-class>
	</servlet>

	<!-- 前端中推會字型 -->
  	<servlet-mapping>
    	<servlet-name>CmexFontServlet</servlet-name>
    	<url-pattern>/cmexfont/*</url-pattern>
  	</servlet-mapping>
  	
</web-app>