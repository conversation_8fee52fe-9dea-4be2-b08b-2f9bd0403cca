package com.kangdainfo.tcfi.service;

import java.io.File;

import com.kangdainfo.tcfi.model.eicm.bo.CmpyQueryVo;

public interface Pre3013Service {
	/**
	 * @param banNo
	 * @return
	 * @throws Exception
	 */
	public void doRecover(String banNo) throws Exception ;

	/**
	 * @param banNo
	 * @return
	 */
	public CmpyQueryVo findLmsQueryVo(String banNo);

	/**
	 * @param banNo
	 * @return
	 */
	public File generateRptPdf(String banNo) throws Exception;

}