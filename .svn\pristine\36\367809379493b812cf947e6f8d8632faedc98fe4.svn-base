package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;

public class PRE9001 extends SuperBean{

	private String q_id;
	private String q_idNo;
	private String q_staffName;
	private String q_staffUnit;
	
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}
	public String getQ_idNo() {return checkGet(q_idNo);}
	public void setQ_idNo(String q_idNo) {this.q_idNo = checkSet(q_idNo);}
	public String getQ_staffName() {return checkGet(q_staffName);}
	public void setQ_staffName(String q_staffName) {this.q_staffName = checkSet(q_staffName);}
	public String getQ_staffUnit() {return checkGet(q_staffUnit);}
	public void setQ_staffUnit(String q_staffUnit) {this.q_staffUnit = checkSet(q_staffUnit);}

	private String id;
	private String idNo;
	private String idPassword;
	private String staffName;
	private String brPhoneNo;
	private String staffTitle;
	private String groupId;
	private String staffUnit;

	public String getId() {return checkGet(id);}
	public void setId(String s) { id = checkSet(s);}
	public String getIdNo() {return checkGet(idNo);}
	public void setIdNo(String idNo) {this.idNo = checkSet(idNo);}
	public String getIdPassword() {return checkGet(idPassword);}
	public void setIdPassword(String idPassword) {this.idPassword = checkSet(idPassword);}
	public String getStaffName() {return checkGet(staffName);}
	public void setStaffName(String staffName) {this.staffName = checkSet(staffName);}
	public String getBrPhoneNo() {return checkGet(brPhoneNo);}
	public void setBrPhoneNo(String brPhoneNo) {this.brPhoneNo = checkSet(brPhoneNo);}
	public String getStaffTitle() {return checkGet(staffTitle);}
	public void setStaffTitle(String staffTitle) {this.staffTitle = checkSet(staffTitle);}
	public String getGroupId() {return checkGet(groupId);}
	public void setGroupId(String groupId) {this.groupId = checkSet(groupId);}
	public String getStaffUnit() {return checkGet(staffUnit);}
	public void setStaffUnit(String staffUnit) {this.staffUnit = checkSet(staffUnit);}
	
	@Override
	public Object doQueryOne() throws Exception {
		PRE9001 obj = this;
		Cedbc000 c = ServiceGetter.getInstance().getPrefixService().getCedbc000ByIdNo(getId());
		if(c != null){
			obj.setId(c.getIdNo());
			obj.setIdNo(c.getIdNo());
			obj.setIdPassword(c.getIdPassword());
			obj.setStaffName(c.getStaffName());
			obj.setGroupId(c.getGroupId());
			obj.setStaffTitle(c.getStaffTitle());
			obj.setBrPhoneNo(c.getBranchPhoneNo());
			obj.setStaffUnit(c.getStaffUnit());
			obj.setEditID(c.getUpdateUser());
			obj.setEditDate(c.getUpdateDate());			
		}else{
			this.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}

	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		//舊預查人員權限維護 有此條件
		//condition += " AND STAFF_UNIT = '六科' ";
		java.util.List<Cedbc000> objList = ServiceGetter.getInstance().getPrefixService().getCedbc000ByCondition(getQ_idNo(), getQ_staffName(), cedbc000.getStaffUnit());
		if(objList != null && objList.size() > 0){
			java.util.Iterator<Cedbc000> it = objList.iterator();
			Cedbc000 o;
			String[] rowArray = new String[5];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[5];
				rowArray[0] = Common.get(o.getIdNo());
				rowArray[1] = Common.get(o.getStaffName());
				rowArray[2] = Common.get(ServiceGetter.getInstance().getSystemCode02Loader().getCodeNameByCode(o.getGroupId()));
				rowArray[3] = Common.get(o.getStaffTitle());
				rowArray[4] = Common.get(o.getStaffUnit());
				arrList.add(rowArray);
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}

	@Override
	public void doCreate() throws Exception {
		if(getUpdateCheck()){
			Cedbc000 obj = new Cedbc000();
			obj.setIdNo(getIdNo());
			obj.setIdPassword(getIdPassword());
			obj.setStaffName(getStaffName());
			obj.setGroupId(getGroupId());
			obj.setStaffTitle(getStaffTitle());
			obj.setBranchPhoneNo(getBrPhoneNo());
			obj.setStaffUnit(getStaffUnit());
			obj.setUpdateUser(getLoginUserId());
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj = ServiceGetter.getInstance().getPrefixService().insertCedbc000(obj);
			this.setId(obj.getIdNo());
		}else{
			throw new MoeaException("已存在相同帳號的資料，請重新輸入！");
		}
	}

	@Override
	public void doUpdate() throws Exception {
		Cedbc000 obj = ServiceGetter.getInstance().getPrefixService().getCedbc000ByIdNo(getId());
		if(obj == null)	throw new MoeaException("資料不存在!");
		if(getUpdateCheck()){
			obj.setId(getId());
			obj.setIdNo(getIdNo());
			obj.setIdPassword(getIdPassword());
			obj.setStaffName(getStaffName());
			obj.setGroupId(getGroupId());
			obj.setStaffTitle(getStaffTitle());
			obj.setBranchPhoneNo(getBrPhoneNo());
			obj.setStaffUnit(getStaffUnit());
			obj.setUpdateUser(getLoginUserId());
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj = ServiceGetter.getInstance().getPrefixService().updateCedbc000(obj);
			this.setId(obj.getIdNo());
		}else{
			throw new MoeaException("已存在相同帳號的資料，請重新輸入！");
		}
	}

	@Override
	public void doDelete() throws Exception {
		Cedbc000 obj = ServiceGetter.getInstance().getPrefixService().getCedbc000ByIdNo(getId());
		if(null==obj) throw new MoeaException("資料不存在!");	
		ServiceGetter.getInstance().getPrefixService().deleteCedbc000(obj);
		
	}
	
	/** 檢核 ID 是否重複 */
	protected boolean getUpdateCheck(){
		Cedbc000 o = ServiceGetter.getInstance().getPrefixService().getCedbc000ByIdNo(getIdNo());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getIdNo().equals(this.getId()))
				return true;
		}
		return false;
	}

}
