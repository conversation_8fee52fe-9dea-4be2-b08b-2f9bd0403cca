package com.kangdainfo.common.util;

import java.util.List;
import java.util.Vector;
import org.apache.log4j.Logger;
import com.kangdainfo.moea.bo.Form1110;

public class PrintTagHelper {

	private static final String spacingStr = "@";
	private Logger logger = Logger.getLogger(this.getClass());

	public PrintTagHelper() {}

	public static String makeHaleluya(List<Form1110> tagdata, String type, String space) {
		
		if (tagdata == null || tagdata.isEmpty() || tagdata.get(0) == null)	return "";

		String caseCode = "", fulltagtext = "", getKind = "";
		Form1110 theForm;
		for (int i = 0; i < tagdata.size(); i++) {
			theForm = (Form1110) tagdata.get(i);
			
			//多筆分隔字串
			if (i != 0)	fulltagtext += "｜" + space;
			
			//申請案由
			caseCode = theForm.getCaseCode().substring(1, 2);
			
			//領件方式，地址條固定為2(郵寄)
			getKind = "T".equals(type) ? "2" : theForm.getContactGetKind();
			
			//字串格式：預查編號@案由@領件方式@列印種類@收文日期@申請人@申請人地址
			fulltagtext += theForm.getPrefixNo() + spacingStr + caseCode + spacingStr + getKind + spacingStr
					+ type + spacingStr + theForm.getReceiveDate() + spacingStr
					+ Common.get(theForm.getContactName()) + spacingStr + Common.get(theForm.getContactAddr());
		}
		return fulltagtext;
	}

	/**
	 * 判斷是否為空白表單
	 * 
	 * 
	 * 以TelixNo為依據
	 * 
	 * Telix_No沒有值,表示為空白表單,回傳F Telix_NO以"0"(零)開頭表示一維條碼,回傳F 其餘認定為線上申辦,回傳T
	 * 
	 * @param theForm
	 *            Form1110
	 * @return String
	 * <AUTHOR>
	 */
	private String getTypeFlag(Form1110 theForm) {
		String telixNo = theForm.getTelixNo();
		if (telixNo == null)
			return "F";
		if (telixNo.trim().equals(""))
			return "F";

		if (telixNo.startsWith("0"))
			return "F";

		return "T";
	}

	/**
	 * 將Form1110中的資料組合成條碼標籤所需的格式字串
	 * 
	 * 1.預查編號(MaxLength:9) 2.預查種類(1.設立,2.變更)(MaxLength:1)
	 * 3.領件方式(1.自取,2.郵寄)(MaxLength:1) 4.網路收文案件(T/F)(MaxLength:1)
	 * 5.收件日期(MaxLength:7) 6.寄件人(MaxLength:60) 7.寄件地址(MaxLength:150)
	 * 
	 * @param tagdata
	 *            Vector
	 * @return String
	 * <AUTHOR>
	 */
	public String combinePrintTag(Vector<Form1110> tagdata) {
		String tag = "";
		String fulltagtext = "";
		String type = "T";
		if (tagdata != null && !tagdata.isEmpty() && tagdata.get(0) != null) {
			Form1110 oldtheForm = tagdata.get(0);
			type = getTypeFlag(oldtheForm);
			tag = oldtheForm.getPrefixNo() + "@"
					+ oldtheForm.getCaseCode().substring(1, 2) + "@"
					+ oldtheForm.getContactGetKind() + "@" + type + "@"
					+ oldtheForm.getReceiveDate() + "@"
					+ oldtheForm.getContactName() + "@"
					+ oldtheForm.getContactAddr();

			fulltagtext = tag;

			for (int i = 1; i < tagdata.size(); i++) {
				Form1110 theForm;
				theForm = (Form1110) tagdata.get(i);
				// commons.util.debugMsgs.println("#### theForm.getCaseCode" +
				// theForm.getCaseCode());
				// commons.util.debugMsgs.println("#### theForm.getContactGetKind"
				// + theForm.getContactGetKind());
				type = getTypeFlag(theForm);
				tag = "｜" + theForm.getPrefixNo() + "@"
						+ theForm.getCaseCode().substring(1, 2) + "@"
						+ theForm.getContactGetKind() + "@" + type + "@"
						+ theForm.getReceiveDate() + "@"
						+ theForm.getContactName() + "@"
						+ theForm.getContactAddr();

				fulltagtext = fulltagtext + tag;
			}
		}
		// commons.util.debugMsgs.println("########### fulltagtext = "+fulltagtext);
		return fulltagtext;
	}

	/**
	 * 將Form1110中的資料組合成條碼標籤所需的格式字串
	 * 
	 * 僅提供單筆標籤使用
	 * 
	 * 1.預查編號(MaxLength:9) 2.預查種類(1.設立,2.變更)(MaxLength:1)
	 * 3.領件方式(1.自取,2.郵寄)(MaxLength:1) 4.網路收文案件(T/F)(MaxLength:1)
	 * 5.收件日期(MaxLength:7) 6.寄件人(MaxLength:60) 7.寄件地址(MaxLength:150)
	 * 
	 * @param <any>
	 *            Form
	 * @return String
	 * <AUTHOR>
	 */
	public String combineSinglePrintTag(Form1110 theForm) {
		String tag = "";

		if (logger.isInfoEnabled())
			logger.info("#### theForm.getCaseCode" + theForm.getCaseCode());
		
		if (logger.isInfoEnabled())
			logger.info("#### theForm.getContactGetKind" + theForm.getContactGetKind());
		
		String sTelix = "F";
		// if (theForm.getTelixNo() != null &&
		// theForm.getTelixNo().startsWith("Z")) {
		// sTelix = "T";
		// }
		// if (theForm.getContactAddr() != null) {
		// sTelix = "T";
		// }

		tag = theForm.getPrefixNo() + "@"
				+ theForm.getCaseCode().substring(1, 2) + "@"
				+ theForm.getContactGetKind() + "@" + sTelix + "@"
				+ theForm.getReceiveDate() + "@" + theForm.getApplyName() + "@"
				+ theForm.getCompanyAddr();

		return tag;
	}
}
