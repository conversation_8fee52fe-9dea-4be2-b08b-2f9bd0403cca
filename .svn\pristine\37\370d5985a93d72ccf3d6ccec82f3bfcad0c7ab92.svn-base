<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="head.jsp" %>
<html>
<head>
<meta http-equiv="cache-control" content="no-cache" />
<meta http-equiv="expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
<link rel="stylesheet" href="../css/default.css" type="text/css">
<style>
body {
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	color: #FFFFFF;
	background-image: url(../images/header/moea_title_03.png);
}

div.titleBar {
	font-family: "新細明體";
	font-size: 12px;
	background-color: #EDEDED;
	/**
	border-top:1px solid #000000;
	border-bottom:1px solid #000000;
	**/
	padding: 4px 8px 2px 8px;
	text-align: left;
	background-image: url(../images/bg_funbar.gif)
}

a.titleBarButton {
	font-family: "新細明體";
	font-size: 12px;
	background-color: transparent;
	color: #FFFFFF;
	padding: 2px 6px 0px 6px;
	text-decoration: none;
}

a.titleBarButton:hover {
	font-family: transparent;
	font-size: transparent;
	background-color: transparent;
	color: #000099;
	padding: 2px 6px 0px 6px;
	text-decoration: none;
}

a.titleBarButton:active {
	font-family: transparent;
	font-size: transparent;
	background-color: transparent;
	color: #FFFFFF;
	padding: 2px 6px 0px 6px;
	text-decoration: none;
}

.titleBarActiveButton {
	font-family: transparent;
	font-size: transparent;
	background-color: transparent;
	border: 1px outset #EDEDED;
	color: #000000;
	padding: 2px 6px 0px 6px;
	text-decoration: none;
}

div#topbar {
	position: absolute;
	border: 1px solid black;
	padding: 2px;
	background-color: lightyellow;
	width: 550px;
	height: 70px;
	visibility: hidden;
	z-index: 100;
	overflow: auto;
}

#Layer1 {
	position: absolute;
	width: 71px;
	height: 34px;
	z-index: 101;
	left: 846px;
	top: 13px;
}

.common_title {
	background-image: url('../images/header/header_banner.png');
	background-repeat: no-repeat;
	background-position: left top;
}
</style>
<script type="text/javascript" src="../js/popup.js"></script>
<script type="text/javascript" src="../js/function.js"></script>
<script type="text/javascript" src="../js/jquery/jquery.js"></script>
<script type="text/javascript" src="../js/jquery/jquery.cycle.js"></script>
<script type="text/javascript" src="../js/json.js"></script>
<script type="text/javascript">
var boxMsgFlag = false;
var broadcastMsg = "";

function showBoxMsg(msg,b) {
	if(msg !=null && msg.length!=0){    		
		//new popUp(760, 1, 200, 70, "boxMsg", msg, "white", "black", "10pt 細明體", "即時訊息", "#C8E8D9", "black", "#EFF9FF", "gray", "#EFF9FF", true, true, true, true, true);
		if (boxMsgFlag) {			
			//fadeboxout("boxMsg");
			changecontent("boxMsg",msg);			
			fadeboxin("boxMsg");
		} else {
			//790 , 1 , 200 , 70
			if (b!=null && b==true) new popUp(500 , 1 , 350 , 70 , "boxMsg" , msg, "white" , "black" , "10pt 新細明體" , "即時訊息" , "#C8E8D9" , "black" , "#EFF9FF", "#EFF9FF" , "black" , true , true , true , true , false , false , '../images/min.gif' , '../images/max.gif' , '../images/close.gif' , '../images/resize.gif');
			else new popUp(790 , 1 , 200 , 70 , "boxMsg" , msg, "white" , "black" , "10pt 新細明體" , "即時訊息" , "#C8E8D9" , "black" , "#EFF9FF", "#EFF9FF" , "black" , true , true , true , true , false , false , '../images/min.gif' , '../images/max.gif' , '../images/close.gif' , '../images/resize.gif');
			boxMsgFlag = true;
		}
		//3秒後自動消失 , 避免暫存的訊息的誤解
		if (b!=null && b==true) {			
		} else {
			window.setTimeout("fadeboxout('boxMsg');", 3000);
		}		
	} else {
		if (boxMsgFlag) { 
			//hidebox("boxMsg");
			fadeboxout("boxMsg");
		}
	}
}

function BroadCastMsg() {
	var x = getRemoteData('msgBroadCast.jsp');	
	if (x!=null && x.length!=0) {
		if (x!=broadcastMsg) {
			showBoxMsg("<font color='red'>"+x+"</font>",true);
			broadcastMsg = x;
		}
	}
	//window.setTimeout("BroadCastMsg()", 60000);
	// onload="BroadCastMsg();" 
	//try {
	//	getDisplayMsg();	
	//} catch (e) {}	
}

function clearDisplayMsg() {
	//top.fbody.menu.document.getElementById('msgContainer').style.display = 'none';	
	//top.fbody.menu.document.getElementById('msgList').innerHTML = "";	
}
function getDisplayMsg() {
	var menuFrame = top.fbody.menu;
	if (menuFrame!=null && isObj(menuFrame.document.getElementById('msgContainer')) && menuFrame.document.getElementById('msgContainer').style.display=='none') {	
		var x = getRemoteData('../ajax/jsonMsg.jsp');	
		if (x!=null && x.length!=0) {
			var json = JSON.parse(x);
		    var sb = new StringBuffer();	
		    var ids = new StringBuffer();
		    //var count = json.length;
			for (var i=0; i<json.length; i++) {			
			    sb.append('<div><table width="100%" border="0" cellspacing="0" cellpadding="0" background="../images/msg_03.png">');
			    sb.append('<tr>');
			    sb.append('<td width="30" align="right" valign="top"><img src="../images/msg_06.png" alt="" width="21" height="15" /></td>');
			    sb.append('<td><font color="silver">').append(i+1).append(' / ').append(json.length).append('</font><br> ');			    
			    sb.append(json[i].obj2);
			    sb.append('</a></td>');
			    sb.append('</tr>');
			    sb.append('</table>');
			    sb.append("</div>");	
			    ids.append(i==0?'':',').append(json[i].obj0);	
			}	
			if (json.length<2) {
			    sb.append('<div><table width="100%" border="0" cellspacing="0" cellpadding="0" background="../images/msg_03.png">');
			    sb.append('<tr>');
			    sb.append('<td width="30" align="right" valign="top"><img src="../images/msg_06.png" alt="" width="21" height="15" /></td>');
			    sb.append('<td align="center"><a href="listMsgUnRead.jsp" target="mainframe">Read More..</a>');			    
			    sb.append('</td>');
			    sb.append('</tr>');
			    sb.append('</table>');
			    sb.append("</div>");					
			}
			sb.append('<input type="hidden" id="msgIdList" value="').append(ids.toString()).append('">');
			var y = (menuFrame.document.pageYOffset)?(menuFrame.document.pageYOffset):(document.documentElement)?menuFrame.document.documentElement.scrollTop:menuFrame.document.body.scrollTop; 
			top.fbody.menuleft.doNormal();
			var s6 = menuFrame.document.getElementById('msgList');
			s6.innerHTML = sb.toString();
			menuFrame.scrollTo(0,y);
			menuFrame.document.getElementById('msgContainer').style.display = '';
			//menuFrame.document.getElementById('msgContainerTitle').focus();		
			//end:	clearDisplayMsg
			//$.fn.cycle.defaults.autostop = 1;
			$.fn.cycle.defaults.timeout = 6000;			
			$(s6).cycle({
			    fx:     'scrollUp',	    
			    timeout: 6000,
			    delay:  -2000,
			    pause: 1 			    
			});	
		}			
	
	}

}

function init() {
	//BroadCastMsg();
	//window.setInterval("BroadCastMsg()", 60000);	
}
</script>

</head>
<body onLoad="init();">
<div style="position:absolute;padding-top:4px;">
<img src="../images/header/header_banner_logo.png" width="152" height="45" alt="" />
<img src="../images/header/header_banner_title.png" width="332" height="45" alt="" />
</div>
<form>
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td height="49" align="right" valign="top" class="common_title">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
<%
			//if(System.getProperty("os.name").toLowerCase().indexOf("win") >= 0) {
			if(com.kangdainfo.ServiceGetter.getInstance().getEnv().equals("prod") == false) {
%>
						<td rowspan="2" style="width:250px;color:#FF0000;font-size:24px;font-weight:bold;">(測試機)</td>
<%				
			}
%>
						<td>
							<font class="text_w" size="2">使用者：<%=User.getUserName() + "-" + Common.get(User.getUserDept())%></font>
						</td>
					</tr>
					<tr>
						<td>
							<% if (null == User.getIsSSO()) { %>
							<button class="toolbar_default" onclick="parent.window.location.href='frame.jsp;'" >首頁</button>&nbsp;&nbsp;
							<button class="toolbar_default" onclick="parent.window.location.href='logout.jsp;'" >登出</button>
							<% } %> &nbsp;
						</td>
					</tr>
				</table>
			</td>
		</tr>
		<!-- 
		<tr>
			<td colspan="2" background="../images/v1_07.png">
				<table width="100%" border="0" cellspacing="0" cellpadding="0">
					<tr>
						<td><table border="0" cellspacing="0" cellpadding="0">
								<tr>
									<td><img src="../images/v1_07.png" alt="" /></td>
									<td>&nbsp;</td>
									<td><a id="b1" class="titleBarButton" target="fbody"
										href="body.jsp"><%=application.getServletContextName()%></a></td>
									<td>&nbsp;</td>
								</tr>
							</table></td>
						<td align="right"><font class="text_w" size="2"></font>&nbsp;</td>
					</tr>
				</table>
			</td>
		</tr>
		 -->
	</table>
	<div id="topbar"></div>
</form>
</body>
</html>