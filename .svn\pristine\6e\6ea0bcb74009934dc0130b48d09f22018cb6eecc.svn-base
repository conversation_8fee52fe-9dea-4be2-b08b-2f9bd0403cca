package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3000;

public class Eedb3000Dao extends BaseDaoJdbc implements RowMapper<Eedb3000> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB3000 WHERE TELIX_NO = ? ORDER BY TELIX_NO, SEQ_NO";
	public List<Eedb3000> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Eedb3000 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb3000 obj = null;
		if(null!=rs) {
			obj = new Eedb3000();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setPart1(rs.getString("PART1"));
			obj.setPart2(rs.getString("PART2"));
			obj.setPart3(rs.getString("PART3"));
			obj.setPart4(rs.getString("PART4"));
			obj.setPart5(rs.getString("PART5"));
			obj.setPart6(rs.getString("PART6"));
		}
		return obj;
	}

}