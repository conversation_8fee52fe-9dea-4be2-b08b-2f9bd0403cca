<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4003"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String dateStart = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateStart")));
String dateEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateEnd")));
String withLimit = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("withLimit")));
try {
	if ( ( dateStart != null && !"".equals(dateStart) && dateEnd != null && !"".equals(dateEnd ) ) )
	{
		String checkResult = PRE4003.checkForjsp(dateStart, dateEnd, withLimit);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>