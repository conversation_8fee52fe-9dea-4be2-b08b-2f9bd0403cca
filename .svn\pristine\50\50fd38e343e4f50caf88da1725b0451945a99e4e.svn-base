package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1102;

public class Cedb1102Dao extends BaseDaoJdbc implements RowMapper<Cedb1102> {

	public List<Cedb1102> findByPrefixNo(String prefixNo) {
		String sql = "SELECT * FROM CEDB1102 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, SEQ_NO";
		Object[] parameters = {prefixNo};
		return (List<Cedb1102>) getJdbcTemplate().query(sql, parameters, this);
	}

	public Cedb1102 findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		String sql = "SELECT * FROM CEDB1102 WHERE PREFIX_NO = ? AND SEQ_NO = ?";
		Object[] parameters = {prefixNo, seqNo};
		List<Cedb1102> list = getJdbcTemplate().query(sql, parameters, this);
		return list.isEmpty() ? null : list.get(0);
	}

	private static String SQL_insert = "INSERT INTO Cedb1102 (" +
			"PREFIX_NO,SEQ_NO,BUSI_ITEM_NO,BUSI_ITEM" +
			") VALUES (" +
			"?,?,?,?" +
			")";
	public int insert(Cedb1102 obj) {
		if (obj == null)
			return 0;
		SQLJob sqljob = new SQLJob(SQL_insert);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSeqNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBusiItemNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBusiItem());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1102 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1102 obj = null;
		if(null!=rs) {
			obj = new Cedb1102();
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setSeqNo(Common.get(rs.getString("SEQ_NO")));
			obj.setBusiItemNo(Common.get(rs.getString("BUSI_ITEM_NO")));
			obj.setBusiItem(Common.get(rs.getString("BUSI_ITEM")));
		}
		return obj;
	}

}