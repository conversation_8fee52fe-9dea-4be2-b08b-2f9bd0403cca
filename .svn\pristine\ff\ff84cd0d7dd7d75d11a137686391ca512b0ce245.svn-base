package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;

/**
 * 同名比對
 *
 */
public interface SameNameCompareService {

	/**
	 * 建立 新的同名比對排程
	 * @param prefixNo
	 * @param userId
	 */
	public void createSameNameQueue(String prefixNo, String userId);

	/**
	 * 取得 SAMENAME_QUEUE 待執行的資料，並回寫SAMENAME_QUEUE 執行中
	 * @return Queue
	 */
	public Queue getSameNameQueueData();

	/**
	 * 同名公司檢查
	 * @param queueObj
	 */
	public Queue doCheckCmpySameName(Queue queueObj);

	/**
	 * 同名公司檢查
	 * @param prefixNo
	 */
	public void doCheckCmpySameName(String prefixNo) throws MoeaException;

	/**
	 * 完成比對
	 * @param queue
	 */
	public void doCompleteCheck(Queue queue);

	/**
	 * 備份 SAMENAME_QUEUE 到 SAMENAME_QUEUE_H
	 */
	public void doBackupSameNameQueue();

}