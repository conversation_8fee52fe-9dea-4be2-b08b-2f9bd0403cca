package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.ParameterizedPreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;

public class Cedb1002Dao extends BaseDaoJdbc implements RowMapper<Cedb1002> {

	private static final String SQL_defaultOrder = "ORDER BY PREFIX_NO, SEQ_NO";

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1002 WHERE PREFIX_NO = ?";
	public List<Cedb1002> findByPrefixNo(String prefixNo) {
    	SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
    	sqljob.addParameter(prefixNo);
    	sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNoAndSeqNo = "SELECT * FROM CEDB1002 WHERE PREFIX_NO = ? AND SEQ_NO = ?";
	public Cedb1002 findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
    	SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndSeqNo);
    	sqljob.addParameter(prefixNo);
    	sqljob.addParameter(seqNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1002> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	private static final String SQL_findByPrefixNoAndBusiItemNo = "SELECT * FROM CEDB1002 WHERE PREFIX_NO = ? AND BUSI_ITEM_NO = ?";
	public Cedb1002 findByPrefixNoAndBusiItemNo(String prefixNo, String busiItemNo) {
    	SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndBusiItemNo);
    	sqljob.addParameter(prefixNo);
    	sqljob.addParameter(busiItemNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1002> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public List<Cedb1002> findByBanNo(String banNo) {
		String sql = "SELECT BAN_NO as PREFIX_NO, SEQ_NO, BUSI_ITEM_NO, BUSI_ITEM FROM CEDB2002 WHERE BAN_NO = ? ORDER BY SEQ_NO";
		Object[] parameters = {banNo};
		return (List<Cedb1002>) getJdbcTemplate().query(sql, parameters, this);
	}

	private static String sql_saveByObj = "INSERT INTO Cedb1002(BUSI_ITEM, BUSI_ITEM_NO, PREFIX_NO, SEQ_NO) "
			+ "VALUES (?, ?, ?, ?) ";

	public int insert(Cedb1002 cedb1002) {
		if (cedb1002 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);
		
		sqljob.addParameter(cedb1002.getBusiItem());
		sqljob.addParameter(cedb1002.getBusiItemNo());
		sqljob.addParameter(cedb1002.getPrefixNo());
		sqljob.addParameter(cedb1002.getSeqNo() == null ? "" : cedb1002.getSeqNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	

	public int insertBatch(final List<Cedb1002> cedb1002s, int batchSize) {
		int[][] updateCount = getJdbcTemplate().batchUpdate(sql_saveByObj, cedb1002s, batchSize,
				new ParameterizedPreparedStatementSetter<Cedb1002>() {
					@Override
					public void setValues(PreparedStatement ps, Cedb1002 cedb1002) throws SQLException {
						ps.setString(1, cedb1002.getBusiItem());
						ps.setString(2, cedb1002.getBusiItemNo());
						ps.setString(3, cedb1002.getPrefixNo());
						ps.setString(4, cedb1002.getSeqNo() == null ? "" : cedb1002.getSeqNo());
					}
				});
		
		//a value of -2 indicates that a element was processed successfully, but that the number of effected rows is unknown.
		return countEffectRows(updateCount);
	}
	
	private int countEffectRows(int[][] updateCount) {
		
		int effectCount = 0;
		for(int i = 0; i < updateCount.length; i ++){
			for(int j = 0; j < updateCount[i].length; j ++){
				if(updateCount[i][j] == -2)
					effectCount += 1;
			}
		}
		return effectCount;
	}

	public int deleteByPrefixNoAndBusiItemNo(String prefixNo, String busiItemNo) {
		
		if(prefixNo != null && busiItemNo != null) {
			
			SQLJob sqljob = new SQLJob("DELETE FROM Cedb1002 ");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.appendSQL("AND BUSI_ITEM_NO = ?");
			sqljob.addParameter(prefixNo);
			sqljob.addParameter(busiItemNo);
			
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			
			return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
		return 0;
	}
	
	private static String sql_updateByObj = "UPDATE Cedb1002 SET BUSI_ITEM=?, BUSI_ITEM_NO=? WHERE PREFIX_NO=? AND SEQ_NO=?";

	public int update(Cedb1002 cedb1002) {
		if (cedb1002 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_updateByObj);
		
		sqljob.addParameter(cedb1002.getBusiItem());
		sqljob.addParameter(cedb1002.getBusiItemNo());
		sqljob.addParameter(cedb1002.getPrefixNo());
		sqljob.addParameter(cedb1002.getSeqNo() == null ? "" : cedb1002.getSeqNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	private static String sql_deleteByPrefixNoAndSeq = "DELETE FROM Cedb1002 WHERE PREFIX_NO=? AND SEQ_NO=? ";
	
	public int delete(Cedb1002 cedb1002) {
		if (cedb1002 == null)
			return 0;
		
		SQLJob sqljob = new SQLJob(sql_deleteByPrefixNoAndSeq);
		sqljob.addParameter(cedb1002.getPrefixNo());
		sqljob.addParameter(cedb1002.getSeqNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private static String SQL_deleteByPrefixNo = "DELETE FROM CEDB1002 WHERE PREFIX_NO=?";
	public int deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_deleteByPrefixNo);
		sqljob.addParameter(prefixNo);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private int[] getSqlTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}

	@Override
	public Cedb1002 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1002 obj = null;
		if(null!=rs) {
			obj = new Cedb1002();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBusiItem(rs.getString("BUSI_ITEM"));
			obj.setBusiItemNo(rs.getString("BUSI_ITEM_NO"));
		}
		return obj;
	}

}
