package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;

/**
 * 檢還、撤件、撤回退費
 *
 */
public interface Pre3005Service {

	public Eedb1000 getEedb1000ByTelixNo(String telixNo);

	public int doSaveFor0102( Cedb1000 obj, Eedb1000 eedb1000, String status, boolean eedbFlag ) throws Exception ;
	
	public int doSaveFor03( Cedb1000 obj, Eedb1000 eedb1000, PrefixReceiptNo prefixReceiptNo, ReceiptNoSetup receiptNoSetup, String status, boolean eedbFlag) throws Exception ;
	
	public int doSaveFor04( Cedb1000 obj, PrefixReceiptNo prefixReceiptNo, ReceiptNoSetup receiptNoSetup, String status) throws Exception ;

	public PrefixReceiptNo selectPrefixReceiptNoByPrefixNo(String prefixNo) throws Exception;
	
	public Cedb1022 selectCedb1022ByPrefixNo(String prefixNo) throws Exception;
	
	public List<PrefixReceiptNo> selectPrefixReceiptNoByReturnDate(String returnDateStart, String returnDateEnd) throws Exception;
} 