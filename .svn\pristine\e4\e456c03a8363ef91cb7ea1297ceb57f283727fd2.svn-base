package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;


/**
 * 案件歷程管理
 */
public interface CaseFlowService {

	/**
	 * 寫入 案件歷程
	 * @param cedb1000
	 * @param status
	 */
	public void addCaseFlow(Cedb1000 cedb1000, String processStatus);

	/**
	 * 寫入 案件歷程
	 * @param prefixNo
	 * @param idNo
	 * @param processStatus
	 */
	public void addCaseFlow(String prefixNo, String idNo, String processStatus);
	
	/**
	 * 寫入 案件歷程for審核用
	 * @param prefixNo
	 * @param idNo
	 * @param processStatus
	 * @param mod1010
	 */
	public void addCaseFlowForApprove(String prefixNo, String idNo, String processStatus, boolean mod1010);
	
	/**
	 * 寫入 案件歷程for馬上辦用
	 * @param prefixNo
	 * @param idNo
	 * @param processStatus
	 * @param mod1010
	 * @param atonceType
	 */
	public void addCaseFlowForAtonce(String prefixNo, String idNo, String processStatus, String atonceType);

	/**
	 * 刪除案件歷程
	 * @param prefixNo
	 * @param processStatus
	 */
	public void removeCaseFlow(String prefixNo, String processStatus);

	/**
	 * 計算工作時數(含判斷案件狀態)
	 * @param cedb1010
	 * @return Float
	 */
	public Float getWorkDay(Cedb1010 cedb1010);

	/**
	 * 計算工作時數(不判斷案件狀態) 
	 * @param startDate
	 * @param startTime
	 * @param endDate
	 * @param endTime
	 * @return
	 */
	public Float countWorkDay(String startDate, String startTime, String endDate, String endTime);

}