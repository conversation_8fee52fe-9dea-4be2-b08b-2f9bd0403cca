<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="head.jsp" %>
<%
String progID = Common.get(request.getParameter("progID"));
%>
<script type="text/javascript">
var readbg="#FFFFFF";
var editbg="#FFFFFF";
var hasBeenConfirmed = false;
var clearFormFieldFlag = true;
var listToHTMLReportFlag = true;
var listHiddenFlag = false;

var localButtonFireListener = new Object();
localButtonFireListener.whatButtonFireEvent = function(buttonName){
    // do nothing,for override.
};

localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
    return true;
    // do nothing,for override.
};

function getFocusField() {	
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (!like(obj.className,"field_Q") && !like(obj.className,"RO") && like(obj.className,"field")){
			if ((obj.value=="" && obj.type!="hidden") && (obj.readOnly==false||obj.disabled==false)) {
				try { obj.focus(); } catch(e) {}
				break;
			}				
		}
		/**
		if (obj.className=="field"||obj.className=="field_P"||obj.className=="field_N"||obj.className=="field_C"||obj.className=="field_E") {		
			if ((obj.value=="" && obj.type!="hidden") && (obj.readOnly==false||obj.disabled==false)) {
				try { obj.focus(); } catch(e) {}
				break;
			}				
		}
		**/
	}	
}

/*全部欄位都設為readonly,  除query欄位及所有資料*/
function setAllReadonly(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (like(obj.className,"field_Q")){	
			if (like(obj.className,"RO")){	
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){									
	    	    	obj.readOnly = true;	
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.style.backgroundColor=readbg;
					obj.disabled = true;
			   	}
			 }
		}else{			
			if (like(obj.className,"RO")){			
				//obj.readOnly = true;	
				obj.style.backgroundColor=readbg;							
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){
					obj.style.backgroundColor=editbg;			
    	    		obj.readOnly = true;	
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.disabled = true;
		   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
   			  		obj.disabled = true;
   	  			}				
			}else{
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){						
					obj.style.backgroundColor=readbg;			
    	    		obj.readOnly = true;	
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.style.backgroundColor=readbg;
					obj.disabled = true;
		   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
   			  		obj.disabled = true;
   	  			}
   	  		}
   	  	}
	}
}

/*全部欄位都設為readonly並清空, 除query欄位外所有資料*/
function setAllReadonlyClear(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (like(obj.className,"field_Q")){	
			if (like(obj.className,"RO")){	
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){									
	    	    	obj.readOnly = true;	
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.style.backgroundColor=readbg;
					obj.disabled = true;
			   	}
			 }
		}else{	
			if (like(obj.className,"RO")){
				if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.style.backgroundColor=readbg;
					obj.disabled = true;
		   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){		   	  	
   			  		obj.disabled = true;
   	  			}				
				obj.style.backgroundColor=editbg;	
				obj.readOnly = true;
				obj.value="";	
			}else{	
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){						
					obj.style.backgroundColor=readbg;			
        			obj.readOnly = true;	
        			obj.value="";
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")) {
					obj.style.backgroundColor=readbg;										
					obj.disabled = true;
					obj.value = "";
				}else if((obj.type=="checkbox")||(obj.type=="radio")){
					obj.disabled = true;
					obj.checked  = false;
		   	  	}else if((obj.type=="button")&&(obj.className!="toolbar_default")){
   			  		obj.disabled = true;
   	  			}
   	  		}
      	}
	}
}

/*全部欄位都設為open*/
function setAllOpen(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (like(obj.className,"RO")){
			obj.style.backgroundColor=readbg;
			if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
				obj.disabled = true;
	   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
  				obj.disabled = true;
  	  		} else {
				obj.style.backgroundColor=editbg;	
				obj.readOnly = true;
			}
		}else{		
			if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){					
				obj.style.backgroundColor=editbg;			
	        	obj.readOnly = false;			
			}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
				obj.style.backgroundColor=editbg;
				obj.disabled = false;
    	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
	    	  	obj.disabled = false;
    	  	}
   	  	}
	}
}

/*全部欄位都設為open並清空, 除query欄位外所有資料*/
function setAllOpenClear(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (!like(obj.className,"field_Q")){			
			if (like(obj.className,"RO")){			
				obj.style.backgroundColor=readbg;
				if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
					obj.disabled = true;
		   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
	  				obj.disabled = true;
	  	  		} else {
					obj.style.backgroundColor=editbg;	
					obj.readOnly = true;
				}
    	    	obj.value="";
			}else{		
				if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){					
					obj.style.backgroundColor=editbg;			
		        	obj.readOnly = false;			
		        	obj.value="";
				}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")){
					obj.style.backgroundColor=editbg;
					obj.disabled = false;
					obj.value="";
				}else if((obj.type=="checkbox")||(obj.type=="radio")){
					obj.disabled = false;
					obj.checked  = false;
	   		  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
	    		  	obj.disabled = false;
	    		//為了使用單位與非機關使用單位二擇一時清除hidden欄位值
				}else if((obj.type=="hidden")&&(obj.className=="field")){
					obj.value="";
				}
			}
      	}
	}
}

/*除PK欄位外全部欄位都設為open*/
function setUnpkOpen(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (like(obj.className,"RO")){
			obj.style.backgroundColor=readbg;			
			if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
				obj.disabled = true;
	   	  	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
  				obj.disabled = true;
  	  		} else {	
				obj.style.backgroundColor=editbg;	
				obj.readOnly = true;
			}
		}else{		
			if((obj.type=="text")||(obj.type=="textarea")||(obj.type=="password")){
				if (like(obj.className,"field_P") || like(obj.className,"field_EP")){
					obj.style.backgroundColor=readbg;			
	   	    		obj.readOnly = true;
				}else{
					obj.style.backgroundColor=editbg;			
	    	    	obj.readOnly = false;
				}			
			}else if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")){
				if (like(obj.className,"field_P") || like(obj.className,"field_RO") || like(obj.className,"field_QRO")){
					obj.style.backgroundColor=readbg;
					obj.disabled = true;			
				}else{
					obj.style.backgroundColor=editbg;
					obj.disabled = false;	
				}	
	      	}else if ((obj.type=="button")&&(obj.className!="toolbar_default")){
				if (like(obj.className,"field_P")){
					obj.disabled = true;			
				}else{
					obj.disabled = false;	
				}    	  	
      		}
		}
	}
}

/*submit之前將select打開*/
function beforeSubmit(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if((obj.type=="select-one")||(obj.type=="select-multiple")||(obj.type=="select")||(obj.type=="checkbox")||(obj.type=="radio")) {		
			obj.disabled = false;
		} else if(obj.type=="text") {
			if ((like(obj.className,"field_N") || like(obj.className,"field_QN"))) deleteCommas(obj);
			if ((like(obj.className,"field_C") || like(obj.className,"field_QC"))) deleteChr(obj,'.');
		}
	}
	document.all.item("confirm").disabled = true;
}
/*顯示查詢視窗*/
function queryShow(queryName){
	var objHeight = $("#"+queryName).css("height");
	var objWidth = $("#"+queryName).css("width");	
	var top = $("#"+queryName).css("top");
	objHeight = objHeight.substring(0,objHeight.length-2);
	objWidth = objWidth.substring(0,objWidth.length-2);
	var _top = (document.body.clientHeight-Number(objHeight)-80)/2;
	var _left = (document.body.clientWidth-Number(objWidth))/2;
	if (top == "0px") {
		$("#"+queryName).css("top", _top);
	}
	$("#"+queryName).css("left", _left);
	$("#"+queryName).css("display", "block");
}
/*隱藏查詢視窗*/
function queryHidden(queryName){
	$("#"+queryName).css("display", "none");
}

/*按鈕隨著PK值顯示*/
function btnFollowPK(){  	
  	var PKValueflag = false;
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if (obj.className=="field_P" && obj.value!="" && !PKValueflag && obj.type!="button"){
			PKValueflag = true;
			break;
		}			
   	}
   	
	for(var i=0; i<arrLen; i++){  			
		var obj = arrObj[i];
		if ((obj.type=="button")&&(obj.className=="toolbar_default" )){	
			if (PKValueflag){
				if (obj.followPK=="true" || obj.getAttribute('followPK')=='true') {obj.disabled = false; }				
			}else{
				if (obj.followPK=="true" || obj.getAttribute('followPK')=='true'){ obj.disabled = true; }
			}
		}					
   	}   	
   	
}

/*顯示新增,查詢按鈕*/
function btnIQ(){
	if (isObj(document.all.item("btnDuplicate"))) document.all.item("btnDuplicate").disabled = false;
	if($('input[name=btnDuplicate]')) $('input[name=btnDuplicate]').removeAttr('disabled');
	if (isObj(document.all.item("insert"))) document.all.item("insert").disabled = false;  	 
	if($('input[name=insert]')) $('input[name=insert]').removeAttr('disabled');
	if (isObj(document.all.item("queryAll"))) document.all.item("queryAll").disabled = false; 
	if($('input[name=queryAll]')) $('input[name=queryAll]').removeAttr('disabled');
	if (isObj(document.all.item("update"))) document.all.item("update").disabled = true; 
	if($('input[name=update]')) $('input[name=update]').attr('disabled','true');
	if (isObj(document.all.item("delete"))) document.all.item("delete").disabled = true; 
	if($('input[name=delete]')) $('input[name=delete]').attr('disabled','true');
	if (isObj(document.all.item("clear"))) document.all.item("clear").disabled = true;  
	if($('input[name=clear]')) $('input[name=clear]').attr('disabled','true');
	if (isObj(document.all.item("confirm"))) document.all.item("confirm").disabled = true;
	if($('input[name=confirm]')) $('input[name=confirm]').attr('disabled','true');
	if (isObj(document.all.item("confirmSave"))) document.all.item("confirmSave").disabled = true;
	if($('input[name=confirmSave]')) $('input[name=confirmSave]').attr('disabled','true');
  	if (isObj(document.all.item("listPrint"))) document.all.item("listPrint").disabled = false;
	if($('input[name=listPrint]')) $('input[name=listPrint]').removeAttr('disabled');
  	if (isObj(document.all.item("listHidden"))) document.all.item("listHidden").disabled = false; 	  	
	if($('input[name=listHidden]')) $('input[name=listHidden]').removeAttr('disabled');
}
/*顯示取消,確定按鈕*/
function btnCC(){
	if (isObj(document.all.item("btnDuplicate"))) document.all.item("btnDuplicate").disabled = true;
	if($('input[name=btnDuplicate]')) $('input[name=btnDuplicate]').attr('disabled','true');
	if (isObj(document.all.item("insert"))) document.all.item("insert").disabled = true; 
	if($('input[name=insert]')) $('input[name=insert]').attr('disabled','true');
	if (isObj(document.all.item("queryAll"))) document.all.item("queryAll").disabled = true; 
	if($('input[name=queryAll]')) $('input[name=queryAll]').attr('disabled','true');
	if (isObj(document.all.item("update"))) document.all.item("update").disabled = true; 
	if($('input[name=update]')) $('input[name=update]').attr('disabled','true');
	if (isObj(document.all.item("delete"))) document.all.item("delete").disabled = true; 
	if($('input[name=delete]')) $('input[name=delete]').attr('disabled','true');
	if (isObj(document.all.item("clear"))) document.all.item("clear").disabled = false;  
	if($('input[name=clear]')) $('input[name=clear]').removeAttr('disabled');
	if (isObj(document.all.item("confirm"))) document.all.item("confirm").disabled = false;
	if($('input[name=confirm]')) $('input[name=confirm]').removeAttr('disabled');
	if (isObj(document.all.item("confirmSave"))) document.all.item("confirmSave").disabled = false;	
	if($('input[name=confirmSave]')) $('input[name=confirmSave]').removeAttr('disabled');
  	if (isObj(document.all.item("listPrint"))) document.all.item("listPrint").disabled = false;
	if($('input[name=listPrint]')) $('input[name=listPrint]').removeAttr('disabled');
  	if (isObj(document.all.item("listHidden"))) document.all.item("listHidden").disabled = false;
	if($('input[name=listHidden]')) $('input[name=listHidden]').removeAttr('disabled');
}
/*顯示新增,查詢,修改,刪除按鈕*/
function btnIQUD(){
	if (isObj(document.all.item("btnDuplicate"))) document.all.item("btnDuplicate").disabled = false;
	if($('input[name=btnDuplicate]')) $('input[name=btnDuplicate]').removeAttr('disabled');
	if (isObj(document.all.item("insert"))) document.all.item("insert").disabled = false;
	if($('input[name=insert]')) $('input[name=insert]').removeAttr('disabled');
	if (isObj(document.all.item("queryAll"))) document.all.item("queryAll").disabled = false;
	if($('input[name=queryAll]')) $('input[name=queryAll]').removeAttr('disabled');
	if (isObj(document.all.item("update"))) document.all.item("update").disabled = false;
	if($('input[name=update]')) $('input[name=update]').removeAttr('disabled');
	if (isObj(document.all.item("delete"))) document.all.item("delete").disabled = false;
	if($('input[name=delete]')) $('input[name=delete]').removeAttr('disabled');
	if (isObj(document.all.item("clear"))) document.all.item("clear").disabled = true;
	if($('input[name=clear]')) $('input[name=clear]').attr('disabled','true');
	if (isObj(document.all.item("confirm"))) document.all.item("confirm").disabled = true;
	if($('input[name=confirm]')) $('input[name=confirm]').attr('disabled','true');
	if (isObj(document.all.item("confirmSave"))) document.all.item("confirmSave").disabled = true;
	if($('input[name=confirmSave]'))$('input[name=confirmSave]').attr('disabled','true');
  	if (isObj(document.all.item("listPrint"))) document.all.item("listPrint").disabled = false;
	if($('input[name=listPrint]')) $('input[name=listPrint]').removeAttr('disabled');
  	if (isObj(document.all.item("listHidden"))) document.all.item("listHidden").disabled = false;
	if($('input[name=listHidden]')) $('input[name=listHidden]').removeAttr('disabled');
}

/*觸發不同Button之相對應動作*/
function whatButtonFireEvent(buttonName){
	var oldState = "";

	if(!localButtonFireListener.beforeWhatButtonFireEvent(buttonName)){
	   return;
	}
	oldState = document.all("state").value;
	document.all("state").value=buttonName;	
	switch (buttonName)	{
		case "init":
			setAllReadonly();
			btnFollowPK();
			btnIQ();
			break;
		case "insert":
			if (clearFormFieldFlag) setAllOpenClear();
			//if (clearFormFieldFlag) setAllOpen();
			else setAllOpen();
			if (isObj(insertDefault)){
				for(var i=0;i<insertDefault.length;i++){
					if (isObj(document.all.item(insertDefault[i][0])) && isObj(document.all.item(insertDefault[i][0]).type)){					
						if((document.all.item(insertDefault[i][0]).type=="checkbox")){
							if (insertDefault[i][1]==document.all.item(insertDefault[i][0]).value)
								document.all.item(insertDefault[i][0]).checked  = true;
	   		  			} else {
			    		  	document.all.item(insertDefault[i][0]).value=insertDefault[i][1];
				   	  	}
					} else if (isObj(document.all.item(insertDefault[i][0]))) { //radio box
						for (var j = 0; j < document.all.item(insertDefault[i][0]).length; j++) {
							if(isObj(document.all.item(insertDefault[i][0])[j])) {
								if (insertDefault[i][1]==document.all.item(insertDefault[i][0])[j].value)
									document.all.item(insertDefault[i][0])[j].checked = true;
							}
						}
					}
				}
			}
			if (isObj(form1.editID)) form1.editID.value = "<%=User.getUserId()%>";
			if (isObj(form1.editDate)) form1.editDate.value = getToday();
			btnFollowPK();
			btnCC();
			getFocusField();		
			break;
		case "insertSuccess":
			setAllReadonly();
			btnFollowPK();
			btnIQUD();
			break;
		case "insertError":
			setAllOpen();
			//setUnpkOpen();
			btnFollowPK();
			btnCC();
			break;
		case "update":
			if (isObj(form1.editID)) form1.editID.value = "<%=User.getUserId()%>";
			if (isObj(form1.editDate)) form1.editDate.value = getToday();
			setUnpkOpen();
			btnFollowPK();
			btnCC();
			break;
		case "updateSuccess":
			setAllReadonly();
			btnFollowPK();
			btnIQUD();
			break;
		case "updateError":
			setUnpkOpen();
			btnFollowPK();
			btnCC();
			break;		
		case "delete":
			if (hasBeenConfirmed) {
				beforeSubmit();	
				//genJSONString();
				document.forms[0].submit();
			} else {
				if(confirm("確定刪除這筆資料?")){
					beforeSubmit();	
					//genJSONString();
					document.forms[0].submit();
				}
			}
			break;
		case "deleteSuccess":
			setAllReadonlyClear();
			btnFollowPK();
			btnIQ();
			break;
		case "deleteError":
			setAllReadonly();
			btnFollowPK();
			btnIQUD();
			break;					
		case "queryAll":
			queryShow('queryContainer');
			break;
		case "queryCannel":
			queryHidden('queryContainer');
			break;	
		case "queryAllSuccess":
			setAllReadonlyClear();
			btnFollowPK();
			btnIQ();
			break;		
		case "queryOneSuccess":
			setAllReadonly();
			btnFollowPK();
			btnIQUD();
			break;
		case "clear":
			if (oldState=="update" || oldState=="updateError") {
				document.all("state").value="queryOne";
				beforeSubmit();				
				document.forms[0].submit();				
				//setAllReadonly();
			} else {				
				setAllReadonlyClear();
			}
			btnFollowPK();
			btnIQ();
			break;	
		default:
			break;
	}
	localButtonFireListener.whatButtonFireEvent(buttonName); 	
}

/*模仿sql like功能*/
function like(buttonName,likestr){
	return (buttonName.indexOf(likestr)>=0);
}
/*檢查查詢欄位至少輸入一個*/
function checkQuery(){
	var errorflag=true;
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;		
	for(var i=0; i<arrLen; i++){
		obj = arrObj[i];	
		if (like(obj.className,"field_Q")){
			if((obj.type=="checkbox")||(obj.type=="radio")){
				if (obj.checked){
					errorflag=false;
					break;				
				}
			}else{
				if ((parse(obj.value)!="") && (obj.type!="button")){
					errorflag=false;
					break;
				}
			}
		}	
	}
	if (errorflag){
		return "查詢欄位至少輸入一個!\n";
	}else{
		return "";
	}
}


/*列表隱藏*/
function listToHidden(btnObj,formName,listName){
	var formObj=document.all.item(formName);
	var listObj=document.all.item(listName);
	if (btnObj.value=="列表隱藏"){
		formObj.style.height="auto";
		listObj.style.display="none";
		btnObj.value="列表顯示";
	}else{
		formObj.style.height="220px";
		listObj.style.display="block";
		btnObj.value="列表隱藏";	
	}
}

function listToHTMLReportWithColor(listHeadName,listBodyName) {
	window.open('../../home/<USER>');
	listToHTMLReportFlag = true;
}

/*列表檔案*/
function listToHTMLReport(listHeadName,listBodyName){
	var i=0, j=0;
	if (listToHTMLReportFlag) {
		var sb = new StringBuffer();
		sb.append("<table border='0' cellpadding='0' cellspacing='0' bgcolor='#000000' width='100%'><tr><td><table width='100%' border='0' cellpadding='1' cellspacing='1'>");
		//寫入Thead資料
		var listHead = document.getElementById(listHeadName);	
		for(i=0; i<listHead.rows.length; i++){
			sb.append("<tr bgcolor='#CCCCCC' align='center'>");
			for(j=0; j<listHead.rows[i].cells.length; j++){			
				if (isObj(listHead.rows[i].cells[j].childNodes[0].childNodes[0])){
					sb.append("<td>").append(listHead.rows[i].cells[j].childNodes[0].childNodes[0].nodeValue).append("</td>");
				}else{
					sb.append("<td>&nbsp;</td>");
				}
			}
			sb.append("</tr>");
		}
		
		//寫入Tbody資料	
		var listBody = document.getElementById(listBodyName);	
		for(i=0; i<listBody.rows.length; i++){
			sb.append("<tr bgcolor='#FFFFFF' align='center'>");	
			for(j=0; j<listBody.rows[i].cells.length; j++){
				textAlign = "";
				if (isObj(listBody.rows[i].cells[j].childNodes[0])){
					try {			
						textAlign = listBody.rows[i].cells[j].style.getAttribute('textAlign');
					} catch(e) {}					
					if (textAlign!=null && textAlign!="") sb.append("<td style=\"text-align:").append(textAlign).append("\">").append(listBody.rows[i].cells[j].childNodes[0].nodeValue).append("</td>");
					else sb.append("<td>").append(listBody.rows[i].cells[j].childNodes[0].nodeValue).append("</td>");
				}else{
					sb.append("<td>&nbsp;</td>");
				}
			}
			sb.append("</tr>");
		}
		sb.append("</table></td></tr></table>");
		var ie = window.open();
		ie.document.write(sb.toString());
		listToHTMLReportFlag = false;
	} else listToHTMLReportWithColor(listHeadName,listBodyName);
}

function btnQueryAllOnly() {
	setDisplayItem('spanInsert,spanDuplicate,spanUpdate,spanDelete,spanClear,spanConfirm,spanListPrint,spanListHidden','H');
}

var leavePageConfirm = true;
function setBeforePageUnload(on){
    window.onbeforeunload = (on) ? confirmLeavePage : null;
    //window.onunload = (on) ? confirmLeavePage : null;
}

function confirmLeavePage(e) {
	if (isObj(document.all.item("state"))) {
		var s = document.all.item("state").value;
		if (s=="insert" || s=="insertError" || s=="update" || s=="updateError") {
			return "資料尚未儲存，確定要離開?";
		}
	}
}

setBeforePageUnload(true);
//window.onbeforeunload = confirmLeavePage
</script>
<input type='hidden' name='permissionField' value=''>
<span id="spanToolbar">&nbsp;
	<span id="spanInsert" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnInsert")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="false" name="insert"  value="新　增" onClick="whatButtonFireEvent(this.name)">&nbsp;
	</span>
	<% if("Y".equalsIgnoreCase(Common.get(request.getParameter("btnCopy")))){ %>
	<span id="spanDuplicate">
		<input class="toolbar_default" type="button" followPK="false" name="btnDuplicate" value="複製新增" onClick="clearFormFieldFlag=false;whatButtonFireEvent('insert');">&nbsp;
	</span>
	<%}%>
	<span id="spanQueryAll" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnQueryAll")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="false" name="queryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)">&nbsp;
	</span>
	<span id="spanUpdate" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnUpdate")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="true"  name="update" value="修　改" onClick="whatButtonFireEvent(this.name)">&nbsp;
	</span>
	<span id="spanDelete" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnDelete")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="true"  name="delete" value="刪　除" onClick="whatButtonFireEvent(this.name)">&nbsp;
	</span>
	<span id="spanClear" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnClear")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="false" name="clear" value="取　消" onClick="whatButtonFireEvent(this.name)">&nbsp;
	</span>
	<span id="spanConfirm" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnConfirm")))){out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="submit" followPK="false" name="confirm" value="確　定" onClick="setBeforePageUnload(false);">&nbsp;
	</span>
	<span id="spanConfirmSave" <%if(!"Y".equalsIgnoreCase(Common.get(request.getParameter("btnConfirmSave")))){out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="submit" followPK="false" name="confirmSave" value="存　檔" onClick="setBeforePageUnload(false);">&nbsp;
	</span>
	<span id="spanListPrint" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnListPrint")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="false" name="listPrint" value="列表檔案" onClick="listToHTMLReport('listTHEAD','listTBODY')">&nbsp;
	</span>
	<span id="spanListHidden" <%if("N".equalsIgnoreCase(Common.get(request.getParameter("btnListHidden")))){ out.print("style=\"display:none;\""); }%>>
		<input class="toolbar_default" type="button" followPK="false" name="listHidden" value="列表隱藏" onClick="listToHidden(this,'formContainer','listContainer')">&nbsp;
	</span>
</span>