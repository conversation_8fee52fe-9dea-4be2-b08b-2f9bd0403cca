package com.kangdainfo.tcfi.view.pre;
/*
程式目的：清算完結報表
程式代號：pre4009
程式日期：1030508
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

public class PRE4009 extends SuperBean {
	
	private String q_regDateStart;      // 登錄日期Start
	private String q_regDateEnd;      	// 登錄日期End
	private String q_clearDateStart;    // 核備清算日期Start
	private String q_clearDateEnd;		// 核備清算日期End
	private String q_clearUnit;			// 核備清算單位
	
	private String total;               // 總筆數
	
	// ----------------------------------getters and setters of variable bellow ---------------------------
	public String getQ_clearUnit() {return checkGet(q_clearUnit);}
	public void setQ_clearUnit(String s) {q_clearUnit = checkSet(s);}
	public String getQ_regDateStart() {return checkGet(q_regDateStart);}
	public void setQ_regDateStart(String q_regDateStart) {this.q_regDateStart = checkSet(q_regDateStart);}
	public String getQ_regDateEnd() {return checkGet(q_regDateEnd);}
	public void setQ_regDateEnd(String q_regDateEnd) {this.q_regDateEnd = checkSet(q_regDateEnd);}
	public String getQ_clearDateStart() {return checkGet(q_clearDateStart);}
	public void setQ_clearDateStart(String q_clearDateStart) {this.q_clearDateStart = checkSet(q_clearDateStart);}
	public String getQ_clearDateEnd() {return checkGet(q_clearDateEnd);}
	public void setQ_clearDateEnd(String q_clearDateEnd) {this.q_clearDateEnd = checkSet(q_clearDateEnd);}
	
	public String getTotal() {return checkGet(total);}
	public void setTotal(String total) {this.total = checkSet(total);}
	// --------------------getters and setters of variables -------------------------------------
	
    // --------------------functions never used bellow----------------------------------------------------
	public void doCreate() throws Exception{} // end doCreate()
	public void doUpdate() throws Exception{} // end doUpdate()		
	public void doDelete() throws Exception{} // end doDelete()	
	public Object doQueryOne() throws Exception{return null ;} // end doQueryOne()
	// --------------------functions never used-----------------------------------------------
	
	//---------------------functions append SQL strings bellow------------------------------------
	public SQLJob doAppendSQLJob() {
		SQLJob sqljob = new SQLJob();
	    sqljob.appendSQL("SELECT A.BAN_NO, A.COMPANY_NAME, A.STATUS_CODE, ");
	    sqljob.appendSQL("	nvl(( SELECT CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND='10' AND CODE=A.STATUS_CODE ),'') AS COMPANY_STATUS, ");
	    sqljob.appendSQL("	A.CLEAR_DATE, A.CLEAR_WORD, A.CLEAR_NO, A.CLEAR_UNIT, ");
	    sqljob.appendSQL("	nvl(( SELECT UNIT_NAME FROM CEDBC050 WHERE UNIT_CODE = A.CLEAR_UNIT ),'') AS CLEAR_UNIT_NAME, ");
	    sqljob.appendSQL("	A.REG_DATE ");
	    sqljob.appendSQL("FROM CEDB1028 A WHERE A.BAN_NO IS NOT NULL ");
	    if(!"".equals(Common.get(getQ_regDateStart())) && !"".equals(Common.get(getQ_regDateEnd()))){
	    	sqljob.appendSQL(" AND A.REG_DATE >= ? AND A.REG_DATE  <= ? ");
	    	sqljob.addParameter(getQ_regDateStart());
	    	sqljob.addParameter(getQ_regDateEnd());
	    }
	    if(!"".equals(Common.get(getQ_clearDateStart())) && !"".equals(Common.get(getQ_clearDateEnd()))){
	    	sqljob.appendSQL(" AND A.CLEAR_DATE >= ? AND A.CLEAR_DATE  <= ? ");
	    	sqljob.addParameter(getQ_clearDateStart());
	    	sqljob.addParameter(getQ_clearDateEnd());
	    }
	    if(!"".equals(Common.get(getQ_clearUnit()))){
	    	sqljob.appendSQL(" AND A.CLEAR_UNIT = ? ");
	    	sqljob.addParameter(getQ_clearUnit());
	    }
	    if(logger.isInfoEnabled()) logger.info(sqljob);
	    return sqljob ;
	} // doAppendSQLJob()
	
	// --------------------doQueryAll and doPrintPdf bellow-------------------------------------
	public ArrayList<String[]> doQueryAll() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>();
		List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJob());
		if(rs != null && rs.size() > 0){
			int i = 0;
			String[] rowArray = new String[7];
			Map<String,Object> temp = null;
			while( i < rs.size()){
				rowArray = new String[7];
				temp = rs.get(i);
		    	rowArray[0] = Common.get(temp.get("BAN_NO"));
		    	rowArray[1] = Common.get(temp.get("COMPANY_NAME"));
		    	rowArray[2] = Common.get(temp.get("COMPANY_STATUS"));
		    	rowArray[3] = Common.formatYYYMMDD(Common.get(temp.get("CLEAR_DATE")), 4);
		    	rowArray[4] = Common.get(temp.get("CLEAR_WORD"));
		    	rowArray[5] = Common.get(temp.get("CLEAR_NO"));
		    	rowArray[6] = Common.get(temp.get("CLEAR_UNIT_NAME"));
		    	//rowArray[7] = Common.formatYYYMMDD(Common.get(temp.get("REG_DATE")), 4);
		    	dataList.add(rowArray);
		    	i++;
			} // while
			
			setTotal(Integer.toString(rs.size()));
		}else{
			setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return dataList;
	}	
}
