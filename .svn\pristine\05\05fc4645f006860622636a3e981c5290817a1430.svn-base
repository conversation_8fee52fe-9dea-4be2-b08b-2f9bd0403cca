package com.kangdainfo.tcfi.loader;

import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.LmsdCodemapping;
import com.kangdainfo.tcfi.model.lms.dao.LmsdCodemappingDao;

/**
 * 有限合夥各類代碼對照加載類
 * <AUTHOR>
 * 113/04/16
 */
public abstract class LmsdCodemappingLoader extends BaseLoader {
	
	private LmsdCodemappingDao lmsdCodemappingDao;
	
	protected abstract String getCacheName();
	protected abstract String getKind();
	
	public LmsdCodemappingDao getLmsdCodemappingDao() {
		return lmsdCodemappingDao;
	}
	
	public void setLmsdCodemappingDao(LmsdCodemappingDao lmsdCodemappingDao) {
		this.lmsdCodemappingDao = lmsdCodemappingDao;
	}
	
	@SuppressWarnings("unchecked")
	public List<LmsdCodemapping> getDataList() {
		if(getServletContext().getAttribute(getCacheName()) == null) {
			reload();
		}
		return (List<LmsdCodemapping>)getServletContext().getAttribute(getCacheName());
	}
	
	public LmsdCodemapping getDataByCode(String code) {
		List<LmsdCodemapping> dataList = getDataList();
		
		for(LmsdCodemapping data : dataList) {
			if(data.getCode().equals(code)) {
				return data;
			}
		}
		
		return null;
	}
	
	public String getDescByCode(String code) {
		LmsdCodemapping data = getDataByCode(code);
		
		if(data != null) {
			return data.getDescription();
		}else {
			return null;
		}
	}
	
	public String getRegUnitCodeByCode(String code) {
		LmsdCodemapping data = getDataByCode(code);
		
		if(data != null) {
			return data.getRegUnitCode();
		}else {
			return null;
		}
	}
	
	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	@Override
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(getCacheName(), lmsdCodemappingDao.findByKind(getKind()));
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}
}
