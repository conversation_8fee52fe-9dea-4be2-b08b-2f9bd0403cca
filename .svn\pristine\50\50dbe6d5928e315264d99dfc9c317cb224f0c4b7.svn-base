<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%
com.kangdainfo.ServiceGetter.getInstance().getCedbc000CodeLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getCedbc058CodeLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getFunctionMenuLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode02Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode03Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode04Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode05Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode06Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode07Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode08Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode09Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode10Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode11Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode12Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode13Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode14Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemCode15Loader().reload();
com.kangdainfo.ServiceGetter.getInstance().getSystemNewsLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getLmsdCodemappingOrgLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getLmsdCodemappingStatLoader().reload();
com.kangdainfo.ServiceGetter.getInstance().getLmsdRegUnitLoader().reload();
%>