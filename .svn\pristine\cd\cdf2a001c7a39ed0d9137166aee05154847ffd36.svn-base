package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 系統公告維護
 *
 */
public class PRE9005 extends SuperBean {

	/** 查詢條件 */
	private String q_id;
	private String q_subject;
	private String q_content;
	private String q_startDate;
	private String q_endDate;

	/** 資料欄位 */
	private String id;//主鍵值
	private String subject;//主旨
	private String content;//內容
	private String startDate;//開始日期
	private String endDate;//結束日期
	private String isImportant;//重要公告註記(Y:重要,N:一般)
	private String enable;//是否啟用(Y:啟動,N:停用 )

	@Override
	public Object doQueryOne() throws Exception {
		PRE9005 obj = this;
		SystemNews o = ServiceGetter.getInstance().getPre9005Service().getSystemNewsById(Common.getInt(id));
		if (null!=o) {
	        obj.setId(o.getId().toString());
	        obj.setSubject(o.getSubject());
	        obj.setContent(o.getContent());
	        obj.setStartDate(o.getStartDate());
	        obj.setEndDate(o.getEndDate());
	        obj.setIsImportant(("Y".equalsIgnoreCase(o.getIsImportant())?"Y":"N"));
	        obj.setEnable(("Y".equalsIgnoreCase(o.getEnable())?"Y":"N"));
		} else throw new Exception("查無該筆資料！");
		return obj;
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		this.processCurrentPageAttribute(ServiceGetter.getInstance().getPre9005Service().countSystemNews(q_id, q_subject,q_content,q_startDate,q_endDate));
		if (getTotalRecord() > 0) {
			java.util.List<SystemNews> objList = ServiceGetter.getInstance().getPre9005Service().querySystemNews(q_id, q_subject,q_content,q_startDate,q_endDate);			
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<?> it = objList.iterator();
				SystemNews o;
				String[] rowArray = new String[6];
				while (it.hasNext()) {
					o = (SystemNews) it.next();
					rowArray = new String[6];
					rowArray[0] = Common.get(o.getId());
					rowArray[1] = Common.get(o.getSubject());
					rowArray[2] = Common.get(o.getStartDate());
					rowArray[3] = Common.get(o.getEndDate());
					rowArray[4] = Common.get(("Y".equalsIgnoreCase(o.getIsImportant())?"是":"否"));
					rowArray[5] = Common.get(("Y".equalsIgnoreCase(o.getEnable())?"是":"否"));
					arrList.add(rowArray);	
				}
			}
		}
		return arrList;
	}

	@Override
	public void doCreate() throws Exception {
		if(CommonStringUtils.isBlank(subject)) return ;
		
		SystemNews o = new SystemNews();
		o.setSubject(subject);
		o.setContent(content);
		o.setStartDate(startDate);
		o.setEndDate(endDate);
		o.setIsImportant(("Y".equalsIgnoreCase(isImportant)?"Y":"N"));
		o.setEnable(("Y".equalsIgnoreCase(enable)?"Y":"N"));
		o.setModIdNo(getLoginUserId());
		o.setModDate(Datetime.getYYYMMDD());
		o.setModTime(Datetime.getHHMMSS());
		o = ServiceGetter.getInstance().getPre9005Service().insertSystemNews(o);
		setId(Common.get(o.getId()));
		
		ServiceGetter.getInstance().getSystemNewsLoader().reload();
	}

	@Override
	public void doUpdate() throws Exception {
		SystemNews o = ServiceGetter.getInstance().getPre9005Service().getSystemNewsById(Common.getInt(id));
		if(null==o) throw new Exception("資料不存在!");

		o.setSubject(subject);
		o.setContent(content);
		o.setStartDate(startDate);
		o.setEndDate(endDate);
		o.setIsImportant(("Y".equalsIgnoreCase(isImportant)?"Y":"N"));
		o.setEnable(("Y".equalsIgnoreCase(enable)?"Y":"N"));
		o.setModIdNo(getLoginUserId());
		o.setModDate(Datetime.getYYYMMDD());
		o.setModTime(Datetime.getHHMMSS());
		o = ServiceGetter.getInstance().getPre9005Service().updateSystemNews(o);

		ServiceGetter.getInstance().getSystemNewsLoader().reload();
	}

	@Override
	public void doDelete() throws Exception {
		ServiceGetter.getInstance().getPre9005Service().deleteSystemNewsById(Common.getInt(id));

		ServiceGetter.getInstance().getSystemNewsLoader().reload();
	}

	public String getQ_subject() {return checkGet(q_subject);}
	public void setQ_subject(String q_subject) {this.q_subject = checkSet(q_subject);}
	public String getQ_content() {return checkGet(q_content);}
	public void setQ_content(String q_content) {this.q_content = checkSet(q_content);}
	public String getQ_startDate() {return checkGet(q_startDate);}
	public void setQ_startDate(String q_startDate) {this.q_startDate = checkSet(q_startDate);}
	public String getQ_endDate() {return checkGet(q_endDate);}
	public void setQ_endDate(String q_endDate) {this.q_endDate = checkSet(q_endDate);}
	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getSubject() {return checkGet(subject);}
	public void setSubject(String subject) {this.subject = checkSet(subject);}
	public String getContent() {return checkGet(content);}
	public void setContent(String content) {this.content = checkSet(content);}
	public String getStartDate() {return checkGet(startDate);}
	public void setStartDate(String startDate) {this.startDate = checkSet(startDate);}
	public String getEndDate() {return checkGet(endDate);}
	public void setEndDate(String endDate) {this.endDate = checkSet(endDate);}
	public String getIsImportant() {return checkGet(isImportant);}
	public void setIsImportant(String isImportant) {this.isImportant = checkSet(isImportant);}
	public String getEnable() {return checkGet(enable);}
	public void setEnable(String enable) {this.enable = checkSet(enable);}
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}

}