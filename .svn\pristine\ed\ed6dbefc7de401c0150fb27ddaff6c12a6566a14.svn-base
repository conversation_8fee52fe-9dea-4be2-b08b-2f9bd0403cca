package com.kangdainfo.tcfi.model.osss.dao;

import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;

/**
 * 一般查詢
 *
 */
public class OsssGeneralQueryDao extends BaseDaoJdbc {

	public List<Map<String,Object>> queryForList(String sql) {
		if(logger.isDebugEnabled()) logger.debug(sql);
		return getJdbcTemplate().queryForList(sql);
	}
	
	public List<Map<String,Object>> queryForList(String sql, Object[] args) {
		if(logger.isDebugEnabled()) logger.debug(sql);
		return getJdbcTemplate().queryForList(sql, args);
	}
	
	public List<Map<String,Object>> queryForList(SQLJob sqljob) {
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	public List<?> query(String sql, BeanPropertyRowMapper<?> rowMapper) {
		if(logger.isDebugEnabled()) logger.debug(sql);
		return getJdbcTemplate().query(sql, rowMapper);
	}
}
