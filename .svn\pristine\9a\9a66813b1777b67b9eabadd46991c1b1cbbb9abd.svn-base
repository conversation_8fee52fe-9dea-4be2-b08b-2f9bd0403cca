<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.ajax.PopBusiItem">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>
<%
if ( "print".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrint();
	if(report != null){
		obj.outputFile(response, report, "PRE3003R_ITEM.xls");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
}
objList = (java.util.ArrayList) obj.queryAll();
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<title>營業項目清單</title>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(seqNo){
	try{
		$('#currSeqNo').val(seqNo);
		var tbl = document.getElementById('itemTable');
		for(var i=0;i<tbl.rows.length;i++) {
			if(isObj(tbl.rows[i])) {
				tbl.rows[i].className=((i+1)%2==1)?"listTREven":"listTROdd";
			}
		}
		document.getElementById("tr-"+seqNo).className='listTRMouseover';
		document.getElementById("item-"+seqNo).scrollIntoView(false);
	}catch(e){
		alert(e.message);
	}
}
function closeWindow(){
	window.returnValue = "";
	window.close();
}
function doPrint(){
	form1.state.value = "print";
	beforeSubmit();
	form1.submit();
}
$(document).ready(function() {
	window.focus();
	var screenHeight = document.body.clientHeight || document.documentElement.clientHeight || self.innerHeight;
	$('#listContainer').css({"height":screenHeight-90});
	document.body.style.overflow='hidden';
	$('#currSeqNo').val("000");

	if( $.browser.msie ) {
		if( $.browser.version < 9 ) {
			$("#NOT_IE_Table").hide();
			$("#IE_Table").show();
			$('#listContainer').css({"height":screenHeight-60});
		} else {
			$("#NOT_IE_Table").show();
			$("#IE_Table").hide();
		}
	} else {
		$("#NOT_IE_Table").show();
		$("#IE_Table").hide();
	}

	document.onkeydown = function() {
		if (event.keyCode == 38) {
			//方向鍵:上
			try{
				var curr = (parseInt($('#currSeqNo').val(),10)-1);
				if(curr < 1) curr = 1;
				var strSeqNo = '000'+curr;
				queryOne(strSeqNo.substring(strSeqNo.length-3,strSeqNo.length));
				return false;
			}catch(e){alert(e.message);};
		}
		else if (event.keyCode == 40) {
			//方向鍵:下
			try{
				var curr = (parseInt($('#currSeqNo').val(),10)+1);
				if(curr > <%=objList.size()%>) curr = <%=objList.size()%>;
				var strSeqNo = '000'+curr;
				queryOne(strSeqNo.substring(strSeqNo.length-3,strSeqNo.length));
				return false;
			}catch(e){alert(e.message);};
		}
	};
});
</script>
</head>
<title>營業項目</title>
<body onLoad="showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post" autocomplete="off">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>"/>
<input type="hidden" id="currSeqNo" name="currSeqNo" value=""/>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TITLE AREA -->
<tr><td >
<table width="100%" cellpadding="0" cellspacing="0" border="0">
	<tr>
		<td width="20%" style="text-align:right;">序號：</td>	
		<td width="15%" style="text-align:left;"><%=obj.getSeq() %></td>	
		<td width="20%" style="text-align:right;">預查編號：</td>	
		<td width="15%" style="text-align:left;"><%=obj.getPrefixNo() %></td>	
		<td style="text-align:right;">
			<input class="toolbar_default" type="button" id="print" name="print" value="列 印" onClick="doPrint()">
			<input class="toolbar_default" type="button" id="close" name="close" value="離 開" onClick="closeWindow()">
		</td>
	</tr>
	<tr>
		<td style="text-align:right;">統一編號：</td>	
		<td style="text-align:left;"><%=obj.getBanNo() %></td>	
		<td style="text-align:right;">公司名稱：</td>	
		<td style="text-align:left;" colspan="2"><%=obj.getCompanyName() %></td>	
	</tr>
</table>
</td></tr>
<!-- TITLE AREA -->

<!-- QUERY ALL LIST AREA -->
<tr><td class="bgList">
<table width="100%" cellspacing="0" cellpadding="0" id="NOT_IE_Table" style="display:none;" >
	<tr>
		<th class="listTH" style="text-align:left;width:60px;" ><a class="text_link_w" href="#">序號</a></th>
		<th class="listTH" style="text-align:left;width:100px;" ><a class="text_link_w" href="#">營業項目代碼</a></th>
		<th class="listTH" style="text-align:left"><a class="text_link_w" href="#">營業項目內容</a></th>
		<td class="listTH" style="text-align:left;width:12px;" >&nbsp;</td>
	</tr>
</table>
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0" id="IE_Table">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" style="text-align:left;width:60px;" ><a class="text_link_w" href="#">序號</a></th>
		<th class="listTH" style="text-align:left;width:100px;" ><a class="text_link_w" href="#">營業項目代碼</a></th>
		<th class="listTH" style="text-align:left"><a class="text_link_w" href="#">營業項目內容</a></th>
	</tr>
	</thead>
</table>
<table width="100%" cellspacing="0" cellpadding="0" id="itemTable" >
<c:forEach items="<%=objList%>" var="item" varStatus="status"> 
	<tr id="tr-${item[0]}" class="${status.index%2==0?'listTREven':'listTROdd'}" onClick="queryOne('${item[0]}');" >
		<td class="${status.index%2==0?'listTDEven':'listTDOdd'}" style="text-align:left;width:60px;"><div id="item-${item[0]}">${item[0]}</div></td>
		<td class="${status.index%2==0?'listTDEven':'listTDOdd'}" style="text-align:left;width:100px;">${item[1]}</td>
		<td class="${status.index%2==0?'listTDEven':'listTDOdd'}" style="text-align:left;font-weight:bold;">${item[2]}</td>
	</tr>
</c:forEach>
</table>
</div>
</td></tr>
<!-- QUERY ALL LIST AREA -->
</table>
</form>
</body>
</html>