package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc004;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 禁用字詞維護(PRE8012)
 *
 */
public class PRE8012 extends SuperBean {

	//查詢條件
	private String q_seqNo;
	private String q_banWord;
	private String q_letterNo;
	private String q_letterDesc;
	private String q_modifier;
	private String q_modDate;

	private String seqNo; // 禁用名稱序號
	private String banWord; // 禁用名稱
	private String modIdNo;
	private String modDate;
	private String modTime;
	private String unit;
	private String letterNo;
	private String letterDate;
	private String letterDesc;

	public String getSeqNo() {return checkGet(seqNo);}
	public void setSeqNo(String s) {this.seqNo = checkSet(s);}
	public String getBanWord() {return checkGet(banWord);}
	public void setBanWord(String s) {this.banWord = checkSet(s);}
	public String getModIdNo() {return checkGet(modIdNo);}
	public void setModIdNo(String s) {this.modIdNo = checkSet(s);}
	public String getModDate() {return checkGet(modDate);}
	public void setModDate(String s) {this.modDate = checkSet(s);}
	public String getModTime() {return checkGet(modTime);}
	public void setModTime(String s) {this.modTime = checkSet(s);}
	public String getUnit() {return checkGet(unit);}
	public void setUnit(String s) {this.unit = checkSet(s);}
	public String getLetterNo() {return checkGet(letterNo);}
	public void setLetterNo(String s) {this.letterNo = checkSet(s);}
	public String getLetterDate() {return checkGet(letterDate);}
	public void setLetterDate(String s) {this.letterDate = checkSet(s);}
	public String getLetterDesc() {return checkGet(letterDesc);}
	public void setLetterDesc(String s) {this.letterDesc = checkSet(s);}
	public String getQ_seqNo() {return checkGet(q_seqNo);}
	public void setQ_seqNo(String s) {this.q_seqNo = checkSet(s);}
	public String getQ_banWord() {return checkGet(q_banWord);}
	public void setQ_banWord(String s) {this.q_banWord = checkSet(s);}
	public String getQ_letterNo() {return checkGet(q_letterNo);}
	public void setQ_letterNo(String s) {this.q_letterNo = checkSet(s);}
	public String getQ_letterDesc() {return checkGet(q_letterDesc);}
	public void setQ_letterDesc(String s) {this.q_letterDesc = checkSet(s);}
	public String getQ_modifier() {return checkGet(q_modifier);}
	public void setQ_modifier(String s) {this.q_modifier = checkSet(s);}
	public String getQ_modDate() {return checkGet(q_modDate);}
	public void setQ_modDate(String s) {this.q_modDate = checkSet(s);}
	// -----------------------------------------------------------------------------------

	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>();
		List<Cedbc004> datas = ServiceGetter.getInstance().getPre8012Service().queryAll(this);
		if (null!=datas && !datas.isEmpty()) {
			String[] rowArray = new String[9];
			for (Cedbc004 o : datas) {
				rowArray = new String[9];
				rowArray[0] = Common.get(o.getSeqNo());
				rowArray[1] = Common.get(o.getBanWord());
				rowArray[2] = Common.get(o.getLetterDesc());
				rowArray[3] = CommonStringUtils.append(Common.get(o.getUnit()), Common.formatYYYMMDD(o.getLetterDate(),2), Common.get(o.getLetterNo()), ("".equals(Common.get(o.getLetterNo()))?"":"號函") );
				rowArray[4] = Common.get(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(o.getModIdNo()));
				rowArray[5] = Datetime.formatRocDate(o.getModDate());
				rowArray[6] = Common.get(o.getUnit());
				rowArray[7] = Common.get(o.getLetterDate());
				rowArray[8] = Common.get(o.getLetterNo());
				arrayList.add(rowArray);
			}
		} 
		return arrayList;
	}

	public Object doQueryOne() throws Exception {
		Cedbc004 cedbc004 = ServiceGetter.getInstance().getPre8012Service().queryBySeqNo(getSeqNo());
		if(null!=cedbc004) {
			PRE8012 obj = new PRE8012();
			obj.setSeqNo(cedbc004.getSeqNo());
			obj.setBanWord(cedbc004.getBanWord());
			obj.setUnit(cedbc004.getUnit());
			obj.setLetterNo(cedbc004.getLetterNo());
			obj.setLetterDate(cedbc004.getLetterDate());
			obj.setLetterDesc(cedbc004.getLetterDesc());
			obj.setModIdNo(Common.get(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedbc004.getModIdNo())));
			obj.setModDate(cedbc004.getModDate());
			obj.setModTime(cedbc004.getModTime());
			return obj;
		}
		return null;
	}

	public void doCreate() throws Exception {
		if (CommonStringUtils.isBlank(Common.get(getBanWord()))) {
			String msg = "請輸入不宜(不得)使用之文字!!";
			this.setBanWord("");
			throw new MoeaException(msg);
		}
		//檢查禁用名稱是否已存在
		if (ServiceGetter.getInstance().getPre8012Service().isBanWordExits(Common.get(getBanWord()))){
			String msg = "不宜(不得)使用之文字："+getBanWord()+"已存在，請重新輸入!!";
			this.setBanWord("");
			throw new MoeaException(msg);
		}

		Cedbc004 obj = new Cedbc004();
		obj.setBanWord(getBanWord());
		obj.setUnit(getUnit());
		obj.setLetterDate(getLetterDate());
		obj.setLetterDesc(getLetterDesc());
		obj.setLetterNo(getLetterNo());
		obj.setModIdNo(getLoginUserId());
		ServiceGetter.getInstance().getPre8012Service().insertCedbc004(obj);
		obj = ServiceGetter.getInstance().getPre8012Service().queryByBanWord(getBanWord());
		setSeqNo(Common.get(obj.getSeqNo()));
	}

	public void doUpdate() throws Exception {
		Cedbc004 obj = new Cedbc004();
		obj.setSeqNo(getSeqNo());
		obj.setBanWord(getBanWord());
		obj.setUnit(getUnit());
		obj.setLetterDate(getLetterDate());
		obj.setLetterDesc(getLetterDesc());
		obj.setLetterNo(getLetterNo());
		obj.setModIdNo(getLoginUserId());
		ServiceGetter.getInstance().getPre8012Service().updateCedbc004(obj);
	}

	public void doDelete() throws Exception {
		Cedbc004 obj = ServiceGetter.getInstance().getPre8012Service().queryBySeqNo(getSeqNo());
		if(null!=obj) {
			ServiceGetter.getInstance().getPre8012Service().deleteCedbc004(obj);
		} else {
			throw new MoeaException("查無資料，無法刪除，請重新操作 !");
		}
	}

} 