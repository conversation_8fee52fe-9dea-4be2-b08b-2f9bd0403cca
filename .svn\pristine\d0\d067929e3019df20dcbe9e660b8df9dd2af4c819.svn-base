package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司法解釋函令-正副本資料(DECLARATORY_STATUTES_RCVER)
 *
 */
public class DeclaratoryStatutesRcver extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 文號 */
	private String rcvNo;
	/** 正副本別 */
	private String rcverType;
	/** 序號 */
	private int seqNo;
	/** 收件單位 */
	private String rcverOrg;
	
	public String getRcvNo() {
		return rcvNo;
	}
	public void setRcvNo(String rcvNo) {
		this.rcvNo = rcvNo;
	}
	public String getRcverType() {
		return rcverType;
	}
	public void setRcverType(String rcverType) {
		this.rcverType = rcverType;
	}
	public int getSeqNo() {
		return seqNo;
	}
	public void setSeqNo(int seqNo) {
		this.seqNo = seqNo;
	}
	public String getRcverOrg() {
		return rcverOrg;
	}
	public void setRcverOrg(String rcverOrg) {
		this.rcverOrg = rcverOrg;
	}
	
}