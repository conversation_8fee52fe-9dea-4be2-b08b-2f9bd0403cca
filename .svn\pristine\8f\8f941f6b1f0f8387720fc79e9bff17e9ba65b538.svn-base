package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;

public class OssmApplFlowDao extends BaseDaoJdbc implements RowMapper<OssmApplFlow> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSM_APPL_FLOW WHERE TELIX_NO = ? ORDER BY TELIX_NO, PROCESS_NO";
	public List<OssmApplFlow> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmApplFlow>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_TelixNoAndProcessNoEqBorI = "SELECT * FROM OSSM_APPL_FLOW WHERE TELIX_NO = ? AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I') ";
	public List<OssmApplFlow> findByTelixNoAndProcessNoEqBorI(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_TelixNoAndProcessNoEqBorI);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmApplFlow>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByTelixNoAndProcessNo = "SELECT * FROM OSSM_APPL_FLOW " +
			"WHERE TELIX_NO = ? AND PROCESS_NO = ?";
	public OssmApplFlow findByTelixNoAndProcessNo(String telixNo, String processNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNoAndProcessNo);
		sqljob.addParameter(telixNo);
		sqljob.addParameter(processNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<OssmApplFlow> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	public List<OssmApplFlow> doReadAllCase() {
		SQLJob sqljob = new SQLJob("SELECT * FROM OSSM_APPL_FLOW");
		sqljob.appendSQL("WHERE ( TELIX_NO LIKE 'OSC%' OR TELIX_NO LIKE 'OSS%')");
		sqljob.appendSQL("AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I')");
		sqljob.appendSQL("AND PROCESS_STATUS ='003'");
		sqljob.appendSQL("ORDER BY UPDATE_TIME, CREATE_TIME");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmApplFlow>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public List<OssmApplFlow> doReadCaseByRownum(int limit) {
		SQLJob sqljob = new SQLJob("SELECT * FROM (");
		sqljob.appendSQL("SELECT * FROM OSSM_APPL_FLOW");
		sqljob.appendSQL("WHERE (TELIX_NO LIKE 'OSC%' OR TELIX_NO LIKE 'OSS%')");
		sqljob.appendSQL("AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I')");
		sqljob.appendSQL("AND PROCESS_STATUS ='003'");
		sqljob.appendSQL("ORDER BY UPDATE_TIME, CREATE_TIME");
		sqljob.appendSQL(") A WHERE ROWNUM <= ?");
		sqljob.addParameter(String.valueOf(limit));
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmApplFlow>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	//  000：資料登打中
	//  001：資料登打完成
	//  002：等待付款
	//  003：資料已傳送
	//  101：已接收資料
	//  102：案件審理中
	//  103：已結案
	//  104：移文中
	//  105：已撤件
	//  106：等待文件補送中
	private static String updateProcess = "UPDATE OSSM_APPL_FLOW SET PROCESS_STATUS=?, UPDATE_USER=?, RCV_CASE_NO=? "
			+ " WHERE TELIX_NO = ?"
			+ " AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I')";
	
	public int updateProcessByTelixNo(String telixNo, String status, String userId, String prefixNo) {
		if(telixNo == null || status == null || status.length() == 0) return -1;
		SQLJob sqljob = new SQLJob(updateProcess);
		sqljob.addParameter(status);
		sqljob.addParameter(userId);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(telixNo);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	@Override
	public OssmApplFlow mapRow(ResultSet rs, int idx) throws SQLException {
		OssmApplFlow obj = null;
		if(null!=rs) {
			obj = new OssmApplFlow();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getInt("SEQ_NO"));
			obj.setProcessNo(rs.getString("PROCESS_NO"));
			obj.setProcessStatus(rs.getString("PROCESS_STATUS"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setApproveTime(rs.getDate("APPROVE_TIME"));
			obj.setCreateTime(rs.getDate("CREATE_TIME"));
			obj.setUpdateTime(rs.getDate("UPDATE_TIME"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setRcvCaseWd(rs.getString("RCV_CASE_WD"));
			obj.setRcvCaseNo(rs.getString("RCV_CASE_NO"));
			obj.setPostCaseWd(rs.getString("POST_CASE_WD"));
			obj.setPostCaseNo(rs.getString("POST_CASE_NO"));
			obj.setGetKind(rs.getString("GET_KIND"));
		}
		return obj;
	}

}