package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1011;

/**
 * 額外註記
 *
 */
public class PRE3001_05 extends SuperBean {
	/** 預查編號 */
	private String prefixNo;
	/** 外商註記 */
	private String foreignMark;
	/** 大陸商註記 */
	private String chinaMark;
	/** 合併(分割)消滅註記 */
	private String companyName;
	private String banNo;

	public void extraMarkQuery() throws Exception {
		Cedb1011 cedb1011 = ServiceGetter.getInstance().getPrefixService().queryCedb1011ByPrefixNo(getPrefixNo());
		if(cedb1011 != null) {
			setPrefixNo(cedb1011.getPrefixNo());
			setForeignMark(cedb1011.getForeignMark());
			setChinaMark(cedb1011.getChinaMark());
			setCompanyName(cedb1011.getCompanyName());
			setBanNo(cedb1011.getBanNo());
			setErrorMsg("查詢成功");
		} else {
			setForeignMark("");
			setChinaMark("");
			setCompanyName("");
			setBanNo("");
			setErrorMsg("查無資料");
		}
	}

	private String validateExtraMarkSave() {
		String errors = "";
		if (this.prefixNo == null || this.prefixNo.length() == 0 || this.prefixNo.length() != 9)
			errors = "預查編號格式有誤";

		// 存檔時需要注意的事項
		// 1. 有公司名稱不能沒有統編，有公司名稱不能同時有外商
		if (this.companyName != null && this.companyName.length() > 0) {
			if (this.banNo == null || this.banNo.length() == 0)
				errors = "存檔失敗，有公司名稱不能沒有統編，有公司名稱不能同時有外商！";
			if (this.foreignMark != null && this.foreignMark.length() > 0)
				errors = "存檔失敗，有公司名稱不能沒有統編，有公司名稱不能同時有外商！";
			if ("Y".equals(this.chinaMark))
				errors = "存檔失敗，有公司名稱不能沒有統編，有公司名稱不能同時有外商！";
		}
		// 2. 有統編不能沒有公司名稱，有統編不能同時有外商
		if (this.banNo != null && this.banNo.length() > 0) {
			if (this.companyName == null || this.companyName.length() == 0)
				errors = "存檔失敗，有統編不能沒有公司名稱，有統編不能同時有外商！";
			if (this.foreignMark != null && this.foreignMark.length() > 0)
				errors = "存檔失敗，有統編不能沒有公司名稱，有統編不能同時有外商！";
			if ("Y".equals(this.chinaMark))
				errors = "存檔失敗，有統編不能沒有公司名稱，有統編不能同時有外商！";
		}
		// 3. 不能三個欄位都是空的
		if ((this.companyName == null || this.companyName.length() == 0) && (this.banNo == null || this.banNo.length() == 0)
				&& (this.foreignMark == null || this.foreignMark.length() == 0) && !"Y".equals(this.chinaMark))
			errors = "存檔失敗，不能三個欄位都是空的！";

		return errors;
	}

	public void extraMarkSave() throws Exception {
		String errors = validateExtraMarkSave();
		if (!errors.isEmpty()) {
			this.setErrorMsg(errors);
			return;
		} else {
			try {
				Cedb1011 cedb1011 = new Cedb1011();
				cedb1011.setPrefixNo(getPrefixNo());
				cedb1011.setForeignMark(getForeignMark());
				cedb1011.setCompanyName(getCompanyName());
				cedb1011.setBanNo(getBanNo());
				cedb1011.setChinaMark(getChinaMark());

				ServiceGetter.getInstance().getPrefixService().saveCedb1011(cedb1011);
				setErrorMsg("儲存成功");
			} catch (Exception e) {
				setErrorMsg("儲存失敗");
			}
		}
	}

	public void extraMarkDelete() throws Exception {
		try {
			ServiceGetter.getInstance().getPrefixService().deleteCedb1011ByPrefixNo(getPrefixNo());
			setErrorMsg("刪除成功");
		} catch (Exception e) {
			setErrorMsg("刪除失敗");
		}
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public String getForeignMark() {return checkGet(foreignMark);}
	public void setForeignMark(String s) {this.foreignMark = checkSet(s);}

	public String getChinaMark() {return checkGet(chinaMark);}
	public void setChinaMark(String s) {this.chinaMark = checkSet(s);}

	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}

	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}

}