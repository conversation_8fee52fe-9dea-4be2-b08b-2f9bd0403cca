// ### TopLink Mapping Workbench 9.0.3 generated source code ###

package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class Eicm3100 extends BaseModel {
	private static final long serialVersionUID = 1L;

	private java.lang.String approveResult;
	private java.lang.String busiItem;
	private java.lang.String busiItemNo;
	private java.lang.String seqNo;
	private java.lang.String telixNo;

	public java.lang.String getApproveResult() {return approveResult;}
	public void setApproveResult(java.lang.String approveResult) {this.approveResult = approveResult;}

	public java.lang.String getBusiItem() {return busiItem;}
	public void setBusiItem(java.lang.String busiItem) {this.busiItem = busiItem;}

	public java.lang.String getBusiItemNo() {return busiItemNo;}
	public void setBusiItemNo(java.lang.String busiItemNo) {this.busiItemNo = busiItemNo;}

	public java.lang.String getSeqNo() {return seqNo;}
	public void setSeqNo(java.lang.String seqNo) {this.seqNo = seqNo;}

	public java.lang.String getTelixNo() {return telixNo;}
	public void setTelixNo(java.lang.String telixNo) {this.telixNo = telixNo;}

}