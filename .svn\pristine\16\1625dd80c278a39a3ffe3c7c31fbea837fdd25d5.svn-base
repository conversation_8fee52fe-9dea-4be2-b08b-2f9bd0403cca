package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ParameterizedPreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;

public class Cedb1001Dao extends BaseDaoJdbc implements RowMapper<Cedb1001> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1001 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, SEQ_NO";

	public List<Cedb1001> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNoAndSeqNo = "SELECT * FROM CEDB1001 WHERE PREFIX_NO = ? AND SEQ_NO = ?";

	public Cedb1001 findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndSeqNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(seqNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1001> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	private static final String SQL_findApprovedByPrefixNo = "SELECT * FROM CEDB1001 WHERE PREFIX_NO = ? AND APPROVE_RESULT = 'Y'";

	public Cedb1001 findApprovedByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findApprovedByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1001> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}
	
	private static final String SQL_findCompanyNameNotApproved = "SELECT DISTINCT(PREFIX_NO), COMPANY_NAME FROM CEDB1001 WHERE COMPANY_NAME LIKE ? AND APPROVE_RESULT != 'Y' AND PREFIX_NO NOT  IN "
			+ "( SELECT DISTINCT(PREFIX_NO) FROM CEDB1001 WHERE APPROVE_RESULT = 'Y' )";
	
	public List<Map<String, Object>> findCompanyNameNotApproved(String companyName) {
		SQLJob sqljob = new SQLJob(SQL_findCompanyNameNotApproved);
		sqljob.addLikeParameter(companyName);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		try {
			return getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray());
		} catch (DataAccessException e) {
			e.printStackTrace();
			return new ArrayList<Map<String, Object>>();
		}
	}
	
	
	private static String sql_saveByObj = "INSERT INTO Cedb1001(APPROVE_RESULT, COMPANY_NAME, PREFIX_NO, REMARK, SEQ_NO) "
			+ "VALUES (?, ?, ?, ?, ?) ";

	public int insert(Cedb1001 cedb1001) {
		if (cedb1001 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);

		sqljob.addParameter(cedb1001.getApproveResult());
		sqljob.addParameter(cedb1001.getCompanyName());
		sqljob.addParameter(cedb1001.getPrefixNo());
		sqljob.addParameter(cedb1001.getRemark() == null ? "" : cedb1001.getRemark());
		sqljob.addParameter(cedb1001.getSeqNo() == null ? "" : cedb1001.getSeqNo());
		
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getAllTypes());
	}

	private int[] getAllTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}
	
	public int insertBatch(final List<Cedb1001> cedb1001s, int batchSize) {
		int[][] updateCount = getJdbcTemplate().batchUpdate(sql_saveByObj, cedb1001s, batchSize,
				new ParameterizedPreparedStatementSetter<Cedb1001>() {
					@Override
					public void setValues(PreparedStatement ps, Cedb1001 cedb1001) throws SQLException {
						ps.setString(1, cedb1001.getApproveResult());
						ps.setString(2, cedb1001.getCompanyName());
						ps.setString(3, cedb1001.getPrefixNo());
						ps.setString(4, cedb1001.getRemark());
						ps.setString(5, cedb1001.getSeqNo());
					}
				});
		
		//a value of -2 indicates that a element was processed successfully, but that the number of effected rows is unknown.
		return countEffectRows(updateCount);
	}
	
	private int countEffectRows(int[][] updateCount) {
		
		int effectCount = 0;
		for(int i = 0; i < updateCount.length; i ++){
			for(int j = 0; j < updateCount[i].length; j ++){
				if(updateCount[i][j] == -2)
					effectCount += 1;
			}
		}
		return effectCount;
	}
	
	
	private static String sql_updateByObj = "UPDATE Cedb1001 SET APPROVE_RESULT=?, COMPANY_NAME=?, REMARK=? WHERE PREFIX_NO=? AND SEQ_NO=?";
	
	public int update(Cedb1001 cedb1001) {
		if (cedb1001 == null)
			return 0;
		
		SQLJob sqljob = new SQLJob(sql_updateByObj);

		sqljob.addParameter(cedb1001.getApproveResult());
		sqljob.addParameter(cedb1001.getCompanyName());
		sqljob.addParameter(cedb1001.getRemark() == null ? "" : cedb1001.getRemark());
		sqljob.addParameter(cedb1001.getPrefixNo());
		sqljob.addParameter(cedb1001.getSeqNo() == null ? "" : cedb1001.getSeqNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getAllTypes());
	}

	public void updateWhenL0100(String prefixNo) {  
		//線上審核不涉及名稱變更, 故直接核准公司名稱
		SQLJob sqljob = new SQLJob("UPDATE CEDB1001 SET APPROVE_RESULT='Y' WHERE PREFIX_NO=? AND SEQ_NO='01'");
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	public void updateWhenPre3005(String prefixNo) {  
		// 檢還撤件撤回退費時使用
		// 依照傳入的預查編號將該預查編號的每一筆紀錄的approve_result改為N
		SQLJob sqljob = new SQLJob("update cedb1001");
		sqljob.appendSQL(" set approve_result = 'N'");
		sqljob.appendSQL(" WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private static String sql_deleteByPrefixNoAndSeq = "DELETE FROM CEDB1001 WHERE PREFIX_NO=? AND SEQ_NO=? ";
	public int delete(Cedb1001 cedb1001) {
		if (cedb1001 == null)
			return 0;
		SQLJob sqljob = new SQLJob(sql_deleteByPrefixNoAndSeq);
		sqljob.addParameter(cedb1001.getPrefixNo());
		sqljob.addParameter(cedb1001.getSeqNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR, java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	private static String SQL_deleteByPrefixNo = "DELETE FROM CEDB1001 WHERE PREFIX_NO=?";
	public int deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_deleteByPrefixNo);
		sqljob.addParameter(prefixNo);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	@Override
	public Cedb1001 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1001 obj = null;
		if (null != rs) {
			obj = new Cedb1001();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}