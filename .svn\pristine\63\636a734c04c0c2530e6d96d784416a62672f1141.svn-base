<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("telixNo")));
String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
String prefixNoEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNoEnd")));
String type = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("type")));
try {
	if ( ( prefixNo != null && !"".equals(prefixNo) ) || (telixNo != null && !"".equals(telixNo)) )
	{
		String checkResult = ServiceGetter.getInstance().getPre4013Service().precheck(prefixNo, prefixNoEnd, telixNo, type);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>