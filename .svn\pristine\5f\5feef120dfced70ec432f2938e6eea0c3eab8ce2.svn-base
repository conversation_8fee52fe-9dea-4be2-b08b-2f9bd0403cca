package com.kangdainfo.tcfi.scheduling;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.ibm.icu.text.DecimalFormat;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.view.pre.PRE4003;
import com.kangdainfo.util.report.JasperReportMaker;

import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRPdfExporterParameter;
import net.sf.jasperreports.engine.util.JRLoader;

/**
 * 排程(AutoSendReport)
 * 
 */
public class AutoSendReportQuartzJobBean extends BaseQuartzJobBean {
	
	private Log mailLog = LogFactory.getLog("mailLog");
	PRE4003 pre4003 = new PRE4003();
	String[] mailRecipients1 = {"<EMAIL>","TO"};
	String mailHost = "*************";
	String mailProtocol = "smtp";
	
	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		try {
			String subject = Datetime.getYYYMMDD().substring(0,3)+"年"+Datetime.getYYYMMDD().substring(3,5)+"月案件申請方式統計";
			String content = "請參考附件, 謝謝";
			mailLog.info("autoMailStart...");
			List<String[]> recipientsList = new ArrayList<String[]>();
			recipientsList.add(mailRecipients1);
			String dateStart = Datetime.getYYYMMDD().substring(0,5)+"01";
			String dateEnd = Datetime.getYYYMMDD();
			
			File cmpyCase =  autoGenerateReports(dateStart, dateEnd, "1");
			File limitCase = autoGenerateReports(dateStart, dateEnd, "2");
			File allCase =   autoGenerateReports(dateStart, dateEnd, "3");
			mailLog.info("==============================================");
			mailLog.info(cmpyCase.getAbsolutePath());
			mailLog.info(limitCase.getAbsolutePath());
			mailLog.info(allCase.getAbsolutePath());
			mailLog.info("==============================================");
			
			List<File> fileList = new ArrayList<File>();
			fileList.add(cmpyCase);
			fileList.add(limitCase);
			fileList.add(allCase);
			autoSendMailHelper(fileList, recipientsList, subject, content);
			mailLog.info("autoMailEnd...");
		} catch (Exception e) {
			e.printStackTrace();
			mailLog.info(e);
		}
	}

	private void autoSendMailHelper(List<File> fileList, List<String[]> recipientsList, String subject, String content)throws Exception{
        //收件人
        int countTo = 0;
        int countCC = 0;
		for (int i=0; i<recipientsList.size(); i++) {
			if ("TO".equals(recipientsList.get(i)[1]))
				countTo++;
			else if ("CC".equals(recipientsList.get(i)[1])) 
				countCC++;
		}
		
        InternetAddress[] mailAddrsTo = new InternetAddress[countTo];
        InternetAddress[] mailAddrsCC = new InternetAddress[countCC];
        int indexTo = 0;
        int indexCC = 0;
        for (int i=0; i<recipientsList.size(); i++) {
        	if ("TO".equals(recipientsList.get(i)[1]))
        		mailAddrsTo[indexTo] = new InternetAddress(recipientsList.get(i)[0]);
        	else if ("CC".equals(recipientsList.get(i)[1]))
        		mailAddrsCC[indexCC] = new InternetAddress(recipientsList.get(i)[0]);
        }
        
        Properties p = System.getProperties();
        p.put("mail.host", mailHost);
        p.put("mail.transport.protocol", mailProtocol);
        Session se = Session.getDefaultInstance(p, null);

        MimeMessage msg = new MimeMessage(se);
        msg.setFrom(new javax.mail.internet.InternetAddress("<EMAIL>"));
        msg.addRecipients(Message.RecipientType.TO, mailAddrsTo);
        msg.addRecipients(Message.RecipientType.CC, mailAddrsCC);
        msg.setSubject(subject);
        msg.setSentDate(new java.util.Date());
        
        BodyPart messageBodyPart = new MimeBodyPart();
        messageBodyPart.setText(content);
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);
        for (int i=0; i<fileList.size(); i++) {
        	File tempFile = fileList.get(i);
        	addAttachment(multipart, tempFile.getAbsolutePath(), tempFile.getName());
        }
        msg.setContent(multipart);
        Transport.send(msg);
    }

	private static void addAttachment(Multipart multipart, String filePath, String fileName) {
		try {
			DataSource source = new FileDataSource(filePath);
			BodyPart messageBodyPart = new MimeBodyPart();        
			messageBodyPart.setDataHandler(new DataHandler(source));
			messageBodyPart.setHeader("Content-Type",  "application/octet-stream; charset=\"utf-8\"");
			messageBodyPart.setFileName(MimeUtility.encodeText(fileName, "UTF-8", "B"));
			multipart.addBodyPart(messageBodyPart);
		} catch (Exception e) {
	    	e.printStackTrace();
	    }
	}

	public File autoGenerateReports(String dateStart, String dateEnd, String withLimit) throws Exception {  
		File report = null ;
		try {
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4003.jasper");
			
			Map<String, Object> parameters = new HashMap<String,Object>();
			parameters.put("printDate", "列印日期："+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印時間
			parameters.put("printTime", "列印時間："+Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間
			parameters.put("dateStart", Common.formatYYYMMDD(dateStart,2))  ;
			parameters.put("dateEnd", Common.formatYYYMMDD(dateEnd,2)) ;
			parameters.put("withLimit", withLimit);
			Integer total = PRE4003.count(dateStart, dateEnd, withLimit);
			List<?> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(
								PRE4003.doAppendSqljob(dateStart, dateEnd, withLimit),
								BeanPropertyRowMapper.newInstance(PRE4003.class));

			Integer netCount = 0;//網路合計
			Integer sssCount = 0;//設立案件數合計
			Integer cccCount = 0;//變更案件數合計
			Integer totalCount = 0;//合計

			for(Object d4003 : datas) {
				PRE4003 data = (PRE4003)d4003;
				data.setRatio(RatioFormat( Double.parseDouble(data.getTotal()) / new Double(total) )+"%");

				if ( "合計".equals( data.getKind() ) ) {
					sssCount = Integer.parseInt(data.getSss());
					cccCount = Integer.parseInt(data.getCcc());
					totalCount = Integer.parseInt(data.getTotal());
				}

				//if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) || "一維條碼".equals(data.getKind()) ) {
				//	netCount += Integer.parseInt(data.getTotal());
				//}
				//一維條碼 不計入 網路合計(2015.01.14)
				if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) ) {
					netCount += Integer.parseInt(data.getTotal());
				}
			}
				
			//比例
		    parameters.put( "sssRatio", RatioFormat( new Double(sssCount) / new Double(total) )+"%" ) ;
		    parameters.put( "cccRatio", RatioFormat( new Double(cccCount) / new Double(total) )+"%" );
		    parameters.put( "totalRatio", RatioFormat( new Double(totalCount) / new Double(total) )+"%" ) ;
		    parameters.put( "ratioRatio", RatioFormat( new Double(netCount) / new Double(total) )+"%(網路合計)" ) ;

			report = makePdfReport(datas, parameters, jasperPath, withLimit);
		} // end try 
		catch ( Exception e ) {
			e.printStackTrace();
			
		} // end catch
		return report ;
	} // doPrintfPdf()
	
	public String RatioFormat( double inputNum ) {
	    inputNum = inputNum * 100 ;
	    DecimalFormat df = new DecimalFormat("##0.00");
	    String ratio = df.format(inputNum);
	    return ratio ;
	} // RatioFormat()
	
	public static File makePdfReport(List<?> dataList, Map<String, Object> parameters, String jasperFilePath,
			String withLimit) throws Exception {
		File report = null;
		File jrprintFile = JasperReportMaker.createJrprintFile(dataList, parameters, jasperFilePath);
		if (jrprintFile != null)
		{
			report = makePdfReport(jrprintFile, withLimit);
		}
		return report;
	}
	
	public static File makePdfReport(File jrprintFile, String withLimit) throws Exception {
		List<File> list = new ArrayList<File>();
		list.add(jrprintFile);
		return makePdfReport(list, withLimit);
	}
	
	public static File makePdfReport(List<File> jrprintFileList, String withLimit) throws Exception {
		File report = null;
		try {
			if (CollectionUtils.isNotEmpty(jrprintFileList)) {
				List<Object> jasperPrints = new ArrayList<Object>();
				for (File jrprintFile : jrprintFileList) {
					jasperPrints.add(JRLoader.loadObject(jrprintFile));
				}
				String path = "";
				if ("1".equals(withLimit)) {
					path = "/opt/temp/report/案件申請方式統計表_公司案件.pdf";
				} else if ("2".equals(withLimit)) {
					path = "/opt/temp/report/案件申請方式統計表_有限合夥案件.pdf";
				} else if ("3".equals(withLimit)) {
					path = "/opt/temp/report/案件申請方式統計表_全部案件.pdf";
				}
				
				// Use relative path for Unix systems
				report = new File(path);
				report.getParentFile().mkdirs(); 
				report.createNewFile();
				
				
				FileOutputStream fos = new FileOutputStream(report);

				JRPdfExporter exporter = new JRPdfExporter();
				exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrints);
				exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, fos);
				exporter.setParameter(JRPdfExporterParameter.IS_CREATING_BATCH_MODE_BOOKMARKS, Boolean.TRUE);
				exporter.exportReport();

				fos.flush();
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		return report;
	}
}