package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 預查案件額外記錄檔(CEDB1011)
 *
 */
public class Cedb1011 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 外商註記 */
	private String foreignMark;
	/** 合併消滅公司名稱 */
	private String companyName;
	/** 合併消滅公司統編 */
	private String banNo;
	/** 大陸商註記 */
	private String chinaMark;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getForeignMark() {return foreignMark;}
	public void setForeignMark(String foreignMark) {this.foreignMark = foreignMark;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getChinaMark() {return chinaMark;}
	public void setChinaMark(String chinaMark) {this.chinaMark = chinaMark;}

}