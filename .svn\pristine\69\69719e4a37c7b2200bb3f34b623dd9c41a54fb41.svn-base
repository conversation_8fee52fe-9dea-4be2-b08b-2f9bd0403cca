package com.kangdainfo.tcfi.lucene.util;

import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.StringField;
import org.apache.lucene.document.TextField;

import com.kangdainfo.tcfi.lucene.bo.IndexData;

public abstract class IndexDataConverter {

	public static Document convertMapToDoc(IndexData obj) throws Exception {
		Document doc = null;
		if(null!=obj) {
			//顯示用StringField, 檢索用TextField
			doc = new Document();
			//ID:可以檢索
			doc.add(new TextField("ID", obj.getId(), Field.Store.YES));
			//BAN_NO:可以檢索
			doc.add(new TextField("BAN_NO", obj.getBanNo(), Field.Store.YES));
			//PREFIX_NO:可以檢索
			doc.add(new TextField("PREFIX_NO", obj.getPrefixNo(), Field.Store.YES));
			//COMPANY_NAME:可以檢索, 正規化(同音同義)
			doc.add(new StringField("COMPANY_NAME", obj.getCompanyName(), Field.Store.YES));  //顯示用
			String companyNameNor = ChineseConverter.normalization(obj.getCompanyName(),false);//正規化(同音同義)
			doc.add(new TextField("COMPANY_NAME"+"_BAS", companyNameNor, Field.Store.YES));//檢索用
			doc.add(new StringField("COMPANY_NAME"+"_BASH", companyNameNor, Field.Store.YES));//輔助用
			//INDEX_TYPE:可以檢索
			doc.add(new TextField("INDEX_TYPE", obj.getIndexType(), Field.Store.YES));
			//APPLY_KIND:可以檢索
			doc.add(new TextField("APPLY_KIND", obj.getApplyKind(), Field.Store.YES));
			//SPECIAL_NAME:可以檢索, 正規化(同音同義)
			doc.add(new StringField("SPECIAL_NAME", obj.getSpecialName(), Field.Store.YES));  //顯示用
			String specialNameNor = ChineseConverter.normalization(obj.getSpecialName());//正規化(同音同義)
			doc.add(new TextField("SPECIAL_NAME"+"_BAS", specialNameNor, Field.Store.YES));//檢索用
			doc.add(new StringField("SPECIAL_NAME"+"_BASH", specialNameNor, Field.Store.YES));//輔助用
			//APPROVE_RESULT:可以檢索
			doc.add(new TextField("APPROVE_RESULT", obj.getApproveResult(), Field.Store.YES));
			//CMPY_STATUS:可以檢索
			doc.add(new TextField("CMPY_STATUS", obj.getCmpyStatus(), Field.Store.YES));
			//RECEIVE_DATE:可以檢索
			doc.add(new TextField("RECEIVE_DATE", obj.getReceiveDate(), Field.Store.YES));
			//RESERVE_DATE:可以檢索
			doc.add(new TextField("RESERVE_DATE", obj.getReserveDate(), Field.Store.YES));
			//REVOKE_APP_DATE:可以檢索  //2025/06/10  修改 由TextField改為StringField 才能做整個時間範圍(過去10年內資料)判定，否則會被拆為分詞比較導致失真
			doc.add(new StringField("REVOKE_APP_DATE", obj.getRevokeAppDate(), Field.Store.YES));
			//APPLY_NAME:可以檢索
			doc.add(new TextField("APPLY_NAME", obj.getApplyName(), Field.Store.YES));
			//SETUP_DATE:只需要顯示的欄位
			doc.add(new StringField("SETUP_DATE", obj.getSetupDate(), Field.Store.YES));
			//ORGN_TYPE:只需要顯示的欄位
			doc.add(new StringField("ORGN_TYPE", obj.getOrgnType(), Field.Store.YES));
			//SORTBY:只需要顯示的欄位
			doc.add(new StringField("SORTBY", obj.getSortby(), Field.Store.YES));
			//PREFIX_NO_SORT:只需要顯示的欄位
			doc.add(new StringField("PREFIX_NO_SORT", obj.getPrefixNoSort(), Field.Store.YES));
			//CMPY_REVOKE:可以檢索
			doc.add(new TextField("CMPY_REVOKE", obj.getCmpyRevoke(), Field.Store.YES));
		}
		return doc;
	}

}
