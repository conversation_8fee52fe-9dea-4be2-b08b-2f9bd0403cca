package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

public class PRE8016_02 extends SuperBean{
	
	private String searchType;
	private String season;
	private String month;
	private String title;
	
	private String dataTitle;
	private String dataAllNum;		//總收文量
	private String dataRcvNum;		//未辦結案
	
	public String getSearchType() {return checkGet(searchType);}
	public void setSearchType(String searchType) {this.searchType = checkSet(searchType);}
	public String getSeason() {return checkGet(season);}
	public void setSeason(String season) {this.season = checkSet(season);}
	public String getMonth() {return checkGet(month);}
	public void setMonth(String month) {this.month = checkSet(month);}
	public String getTitle() {return checkGet(title);}
	public void setTitle(String title) {this.title = checkSet(title);}
	
	public String getDataTitle() {return dataTitle;}
	public void setDataTitle(String dataTitle) {this.dataTitle = dataTitle;}
	public String getDataAllNum() {return dataAllNum;}
	public void setDataAllNum(String dataAllNum) {this.dataAllNum = dataAllNum;}
	public String getDataRcvNum() {return dataRcvNum;}
	public void setDataRcvNum(String dataRcvNum) {this.dataRcvNum = dataRcvNum;}
	
	@Override
	public Object doQueryOne() throws Exception {
		List<Map<String,Object>> dataList = null;
		dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(getSql());
		
		if(dataList != null && dataList.size() > 0){
			if("1".equals(this.getSeason()))	this.setTitle("第一季");
			else if("2".equals(this.getSeason()))	this.setTitle("第二季");
			else if("3".equals(this.getSeason()))	this.setTitle("第三季");
			else if("4".equals(this.getSeason()))	this.setTitle("第四季");
			else	this.setTitle(this.getSeason() + "月");
			
			Map<String,Object>data = null;
			String title = "", allNum = "", rcvNum = "";
			if("season".equals(this.getSearchType())){
				for(int i=0; i<dataList.size(); i++){
					data = dataList.get(i);
					if(i > 0){
						title += ",";
						allNum += ",";
						rcvNum += ",";
					}
					title += Common.sqlChar(Common.get(data.get("month")) + "月");
					allNum += Common.get(data.get("allNum"));
					rcvNum += "{y:" + Common.get(data.get("rcvNum")) + ",color:'#FF6347'}";
				}
			}else{
				for(int i=0; i<dataList.size(); i++){
					data = dataList.get(i);
					if(i > 0){
						title += ",";
						if("monthRcv".equals(this.getSearchType()))
							rcvNum += ",";
						else
							allNum += ",";
					}
					title += Common.sqlChar(Common.get(data.get("day")) + "日");
					if("monthRcv".equals(this.getSearchType()))
						rcvNum += "{y:" + Common.get(data.get("num")) + ",color:'#FF6347'}";
					else
						allNum += Common.get(data.get("num"));
				}
			}
			this.setDataTitle(title);
			this.setDataAllNum(allNum);
			this.setDataRcvNum(rcvNum);
		}else{
			this.setErrorMsg("查無資料！");
		}
		return this;
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}
	
	public SQLJob getSql(){
		SQLJob sqljob = new SQLJob();
		String YYY = Datetime.getYYY();
		String prefixNoStart = YYY + "000000";
		String prefixNoEnd = YYY + "999999";
		if("season".equals(this.getSearchType())){
			//每季的月統計
			sqljob.appendSQL("select substr(receive_date, 4, 2) as month, ");
			sqljob.appendSQL("		count(1) as allNum, ");
			sqljob.appendSQL("		sum(case when close_date is null then 1 else 0 end) as rcvNum ");
			sqljob.appendSQL("from cedb1000 ");
			sqljob.appendSQL("where prefix_no between ");
			sqljob.appendSQL(Common.sqlChar(prefixNoStart));
			sqljob.appendSQL(" and ");
			sqljob.appendSQL(Common.sqlChar(prefixNoEnd));
			if("1".equals(this.getSeason()))
				sqljob.appendSQL(" and substr(receive_date, 4, 2) in ('01','02','03') ");
			else if("2".equals(this.getSeason()))
				sqljob.appendSQL(" and substr(receive_date, 4, 2) in ('04','05','06') ");
			else if("3".equals(this.getSeason()))
				sqljob.appendSQL(" and substr(receive_date, 4, 2) in ('07','08','09') ");
			else if("4".equals(this.getSeason()))
				sqljob.appendSQL(" and substr(receive_date, 4, 2) in ('10','11','12') ");
			sqljob.appendSQL(" group by substr(receive_date, 4, 2) ");
			sqljob.appendSQL(" order by substr(receive_date, 4, 2) ");
		}else{
			//每月的日統計
			sqljob.appendSQL("select substr(receive_date, 6, 2) as day, ");
			sqljob.appendSQL("		count(1) as num ");
			sqljob.appendSQL("from cedb1000 ");
			sqljob.appendSQL("where prefix_no between ");
			sqljob.appendSQL(Common.sqlChar(prefixNoStart));
			sqljob.appendSQL(" and ");
			sqljob.appendSQL(Common.sqlChar(prefixNoEnd));
			sqljob.appendSQL(" and substr(receive_date, 4, 2) = ");
			sqljob.appendSQL(Common.sqlChar(this.getSeason()));
			if("monthRcv".equals(this.getSearchType()))
				sqljob.appendSQL(" and close_date is null ");
			sqljob.appendSQL(" group by substr(receive_date, 6, 2) ");
			sqljob.appendSQL(" order by substr(receive_date, 6, 2) ");	
		}

		if(logger.isInfoEnabled()) logger.info(sqljob);	
		return sqljob;
	}
}
