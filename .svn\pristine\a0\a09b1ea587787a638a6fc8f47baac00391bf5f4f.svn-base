//for Chrome
var nameColumn;
var has_showModalDialog = !!window.showModalDialog;//定義一個全域性變數判定是否有原生showModalDialog方法
if(!has_showModalDialog &&!!(window.opener)){		
	window.onbeforeunload=function(){
		window.opener.hasOpenWindow = false;		//彈窗關閉時告訴opener：它子視窗已經關閉
	}
}
if(window.showModalDialog == undefined){
	window.showModalDialog = function(url,mixedVar,features){
		if(window.hasOpenWindow){
			alert("您已經打開一個視窗！");//避免多次點選會彈出多個視窗
			window.myNewWindow.focus();
		}
		window.hasOpenWindow = true;
		if(mixedVar) var mixedVar = mixedVar;
		//因window.showmodaldialog 與 window.open 引數不一樣，所以封裝的時候用正則去格式化一下引數
		if(features) var features = features.replace(/(dialog)|(px)/ig,"").replace(/;/g,',').replace(/\:/g,"=");
		var left = (window.screen.width - parseInt(features.match(/width[\s]*=[\s]*([\d]+)/i)[1]))/2;
		var top = (window.screen.height - parseInt(features.match(/height[\s]*=[\s]*([\d]+)/i)[1]))/2;
		window.myNewWindow = window.open(url,"_blank",features);
	}
}
//*********************************************
//函數功能：popSystemCodeReturn 共用代碼 共用輔助視窗
//參　　數：	codeKind 代碼類別
//*********************************************
function popSystemCodeReturn(codeKind){
	var prop="";
	var width = 350;
	var height = 325;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"dialogWidth="+width+"px;dialogHeight="+height+"px;";
	prop=prop+"dialogTop="+top+"px;";
	prop=prop+"dialogLeft="+left+"px;";
	prop=prop+"center:yes;scroll:yes;help:no;resizable:no;status:no";
	closeReturnWindow();
	var url = getVirtualPath() + "tcfi/ajax/popSystemCode.jsp?codeKind="+codeKind;
	var x = window.showModalDialog(url,window,prop);
	if(x!=null && x.length>0) {
		var json = eval('(' + x + ')');
		return json;
	}
	return null;
}

//*********************************************
//函數功能：popSysCode 共用代碼 共用輔助視窗
//參　　數：	codeKind 代碼類別
//			codeColumn 代碼欄位
//			codeNameColumn 名稱欄位
//*********************************************
function popSystemCode(codeKind, codeColumn, codeNameColumn){

	var prop="";
	var width = 350;
	var height = 325;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"dialogWidth="+width+"px;dialogHeight="+height+"px;";
	prop=prop+"dialogTop="+top+"px;";
	prop=prop+"dialogLeft="+left+"px;";
	prop=prop+"center:yes;scroll:yes;help:no;resizable:no;status:no";
	closeReturnWindow();
	var url = getVirtualPath() + "tcfi/ajax/popSystemCode.jsp?codeKind="+codeKind;
	var x = window.showModalDialog(url,window,prop);
	if(x!=null && x.length>0)
	{
		
		var json = eval('(' + x + ')');
		if(null!=json && null!=json.CODE)
			setObjectValue(codeColumn, json.CODE);
		if(null!=json && null!=json.CODE_NAME)
			setObjectValue(codeNameColumn, json.CODE_NAME);
	}
}

//*********************************************
//函數功能：popSysCode 共用代碼 共用輔助視窗(傳回中文)
//參　　數：	codeKind 代碼類別
//			codeNameColumn 名稱欄位
//			codePrefix 代碼開頭(0XX or 1XX or 2XX...)
//*********************************************
function popSystemCodeReturnName(codeKind, codeNameColumn, codePrefix){
	var prop="";
	var width = 350;
	var height = 330;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;
	codePrefix = (codePrefix) ? codePrefix :"";
	
	prop=prop+"dialogWidth="+width+"px;dialogHeight="+height+"px;";
	prop=prop+"dialogTop="+top+"px;";
	prop=prop+"dialogLeft="+left+"px;";
	prop=prop+"center:yes;scroll:yes;help:no;resizable:no;status:no";
	closeReturnWindow();
	var url = getVirtualPath() + "tcfi/ajax/popSystemCode.jsp?codeKind="+codeKind+"&code="+codePrefix+"&codeNameColumn="+codeNameColumn;
	var x = window.showModalDialog(url,window,prop);
	if(!has_showModalDialog) {
		nameColumn = codeNameColumn;
		return; //chrome 返回 因為showModalDialog是阻塞的 open不一樣;			
	}
	if(x!=null && x.length>0)
	{
		var json = eval('(' + x + ')');
		if(null!=json && null!=json.CODE_NAME)
			//setObjectValue(codeNameColumn, json.CODE_NAME);
			setObjectValueAtCursor(codeNameColumn, json.CODE_NAME);
	}
}

function popSystemCodeReturnNameChrome(x){ //為開啟的視窗定義方法，讓開啟的視窗關閉時通過window.opener賦值回來並執行
	if(x!=null && x.length>0)
	{
		var json = eval('(' + x + ')');
		if(null!=json && null!=json.CODE_NAME)
			//setObjectValue(nameColumn, json.CODE_NAME);
			setObjectValueAtCursor(nameColumn, json.CODE_NAME);
	}
}

//*********************************************
//函數功能：popSysCodeMultiSelect 共用代碼-多選 輔助視窗
//參　　數：	codeKind 代碼類別
//          checkedList 已勾選項目
//*********************************************
function popSysCodeMultiSelect(codeKind, checkedList){
	var prop="";
	var width = 500;
	var height = 400;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"dialogWidth="+width+"px;dialogHeight="+height+"px;";
	prop=prop+"dialogTop="+top+"px;";
	prop=prop+"dialogLeft="+left+"px;";
	prop=prop+"center:yes;scroll:yes;help:no;resizable:no;status:no";
	closeReturnWindow();
	var url = getVirtualPath() + "tcfi/ajax/popSystemCodeMultiSelect.jsp?codeKind="+codeKind+"&checkedList="+checkedList;
	var x = window.showModalDialog(url,window,prop);
	if(x!=null && x.length>0) {
		var jsons = eval('(' + x + ')');
		return jsons;
	}
	return null;
}

//popwindow後鎖定主視窗
function blockMainWindow(popwin) {
	popwin.focus();
	window.onfocus=function (){
		if(isObj(popwin)) {
			if(!popwin.closed)
				popwin.focus();
			else
				unblockMainWindow();
		}
	};

	window.document.onfocus=function (){
		if(isObj(popwin)) {
			if(!popwin.closed)
				popwin.focus();
			else
				unblockMainWindow();
		}
	};
	
	window.document.onclick=function (){
		if(isObj(popwin)) {
			if(!popwin.closed)
				popwin.focus();
			else
				unblockMainWindow();
		}
	};
	
	window.document.ondblclick=function (){
		if(isObj(popwin)) {
			if(!popwin.closed)
				popwin.focus();
			else
				unblockMainWindow();
		}
	};
	$.blockUI({ message: null });
}

//popwindow關閉後解除鎖定主視窗
function unblockMainWindow() {
	window.onfocus=null;
	window.document.onfocus=null;
	window.document.onclick=null;
	window.document.ondblclick=null;
	$.unblockUI();
}

//*********************************************
//函數功能：getCheckedValue 取得已選取欄位的值
//*********************************************
function getCheckedValue(radioObj) {
	if(!radioObj)
		return "";
	var radioLength = radioObj.length;
	if(radioLength == undefined)
		if(radioObj.checked)
			return radioObj.value;
		else
			return "";
	for(var i = 0; i < radioLength; i++) {
		if(radioObj[i].checked) {
			return radioObj[i].value;
		}
	}
	return "";
}

//*********************************************
//函數功能：setCheckedValue 設定欄位為已選取
//*********************************************
function setCheckedValue(radioObj, newValue) {
	if(!radioObj)
		return;
	var radioLength = radioObj.length;
	if(radioLength == undefined) {
		radioObj.checked = (radioObj.value == newValue.toString());
		return;
	}
	for(var i = 0; i < radioLength; i++) {
		radioObj[i].checked = false;
		if(radioObj[i].value == newValue.toString()) {
			radioObj[i].checked = true;
		}
	}
}

//*********************************************
//函數功能：btnAllSelect_OnClick 此頁全選
//參　　數：columnName 欄位名稱
//*********************************************
function btnAllSelect_OnClick(columnName) {
	var objList = document.all[columnName];
	if(null!=objList) {
		if (objList.length == undefined) {
			if (!objList.disabled) {
				objList.checked = true;
			}
		} else {
			for (var i=0;i<objList.length;i++) {
				if (!objList[i].disabled)
					objList[i].checked = true;
			}
		}
	}
}

//*********************************************
//函數功能：btnAllNSelect_OnClick 此頁全不選
//參　　數：columnName 欄位名稱
//*********************************************
function btnAllNSelect_OnClick(columnName) {
	var objList = document.all[columnName];
	if(null!=objList) {
		if (objList.length == undefined) {
			if (!objList.disabled) {
				objList.checked = false;
			}
		} else {
			for (var i=0;i<objList.length;i++) {
				if (!objList[i].disabled)
					objList[i].checked = false;
			}
		}
	}
}

/*
 * 判斷輸入字串是否超過
 */
function checkCommentLength(comment, title, total) {
	if(comment.length>total) {
		alert(title+'最多'+ total + '字元');
		return true;
	}
	return false;
}

//*********************************************
//函數功能：比較兩日期是否相差一個月
//         ex. 2011/09/01-2011/10/01 相差一個月, 2010/10/01-2011/10/31 不滿一個月
//             2010/12/28-2011/01/28 相差一個月, 2010/12/28-2011/01/27 不滿一個月
//             2011/02/25-2011/03/25 相差一個月, 2011/02/25-2011/03/24 不滿一個月
//參　　數：dateS, dateE
//傳 回 值：true 代表相差不到一個月
//*********************************************
function  getDateDiff_Month(dateS, dateE) {
	var d1 = new Date(dateS);
	var d2 = new Date(dateE);
	if( d1.getYear()==d2.getYear() && d1.getMonth()==d2.getMonth() ) {
		return true;
	} else {
		d2.setMonth(d2.getMonth()-1);
		return d2<d1;
	}
}

function trimElement(){
	var arrObj = document.forms[0].elements;
	var arrLen = arrObj.length;
	for(var i=0; i<arrLen; i++){
		var obj = arrObj[i];
		if((obj.type=="text")||(obj.type=="hidden")||(obj.type=="textarea")){
			var temp = $.trim(obj.value);
			var temp1 = temp.replace(/\　/g,"");
			if (temp1=='') {
				obj.value = temp1;
			}
    	}
	}
}

//*********************************************
//函數功能：popSystemCodeReturn 共用代碼 共用輔助視窗
//參　　數：	codeKind 代碼類別
//*********************************************
function popBusiItem(seq,indexKind,id,companyName){
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"width="+width+"px;height="+height+"px;";
	prop=prop+"top="+top+"px;";
	prop=prop+"left="+left+"px;";
	prop=prop+"scrollbars=1,resizable=1";

	var url = getVirtualPath() + "tcfi/ajax/popBusiItem.jsp?seq="+seq+"&indexKind="+indexKind+"&id="+id+"&companyName="+encodeURI(companyName);;

	closeReturnWindow();
	returnWindow = window.open(url,'popBusiItem',prop);		

	return null;
}

function getBrowserScrollSize(){

    var css = {
        "border":  "none",
        "height":  "200px",
        "margin":  "0",
        "padding": "0",
        "width":   "200px"
    };

    var inner = $("<div>").css($.extend({}, css));
    var outer = $("<div>").css($.extend({
        "left":       "-1000px",
        "overflow":   "scroll",
        "position":   "absolute",
        "top":        "-1000px"
    }, css)).append(inner).appendTo("body")
    .scrollLeft(1000)
    .scrollTop(1000);

    var scrollSize = {
        "height": (outer.offset().top - inner.offset().top) || 0,
        "width": (outer.offset().left - inner.offset().left) || 0
    };

    outer.remove();
    return scrollSize;
}

//*********************************************
//函數功能：shortcut_pop 快捷列
//參　　數：	functioncode 功能代號
//*********************************************
function shortcut_pop(functioncode) {
	var _url;
	if ( functioncode == '1001onlycmp' ) {
		_url = "pre3008"+".jsp?shortcut=Y&comefrom=pre1001";
		var _target = "_blank";
		var _param = "left=0";
		_param += ",top=0";
		_param += ",scrollbars=1";
		_param += ",resizable=1";
		_param += ",toolbar=0";
		_param += ",menubar=0";
		_param += ",directories=0";
		_param += ",status=0";
		_param += ",location=0";
		_param += ",width="+screen.width;
		_param += ",height="+screen.height*0.96;
		window.open( _url, _target, _param);
	} else if ( functioncode == '1001onlylms' ) {
		_url = "pre3013"+".jsp?shortcut=Y&comefrom=pre1001";
		var _target = "_blank";
		var _param = "left=0";
		_param += ",top=0";
		_param += ",scrollbars=1";
		_param += ",resizable=1";
		_param += ",toolbar=0";
		_param += ",menubar=0";
		_param += ",directories=0";
		_param += ",status=0";
		_param += ",location=0";
		_param += ",width="+screen.width;
		_param += ",height="+screen.height*0.96;
		window.open( _url, _target, _param);
	}
	else {
		var _target = "";
		if ( functioncode == "pre1006" )
			_target = "pre1006";
		else if ( functioncode == "pre1003" )
			_target = "pre1003";
		else if ( functioncode == "pre3004" )
			_target = "pre3004";
		else if ( functioncode == "pre2002" )
			_target = "pre1002";
		else
			_target = "_blank";
		_url = functioncode+".jsp?shortcut=Y";
		var _param = "left=0";
		_param += ",top=0";
		_param += ",scrollbars=1";
		_param += ",resizable=1";
		_param += ",toolbar=0";
		_param += ",menubar=0";
		_param += ",directories=0";
		_param += ",status=0";
		_param += ",location=0";
		_param += ",width="+screen.width;
		_param += ",height="+screen.height*0.96;
		window.open( _url, _target, _param);
	}
	
}

//*********************************************
//函數功能：loadCedbc004Options 禁用名詞
//*********************************************
function loadCedbc004Options(id) {
	var x = document.getElementById(id);
	if( x.options.length == 1 )
	{
		$.post( getVirtualPath() + "tcfi/ajax/jsonCedbc004s.jsp", function( json ) {
			for (var i = 0 ; i < json.length ; i++) {
				if('002' != json[i].SEQ_NO) {
					x.options.add( new Option(json[i].BAN_WORD, json[i].SEQ_NO), i );
				}
			}
		});
	}
}

//*********************************************
//函數功能：loadCedbc053Options 國家名稱
//*********************************************
function loadCedbc053Options(id) {
	var x = document.getElementById(id);
	if( x.options.length == 1 )
	{
		$.post( getVirtualPath() + "tcfi/ajax/jsonCedbc053s.jsp", function( json ) {
			for (var i = 0 ; i < json.length ; i++) {
				if('000' != json[i].COUNTRY_CODE) {
					x.options.add( new Option(json[i].COUNTRY, json[i].COUNTRY_CODE), i );
				}
			}
		});
	}
}