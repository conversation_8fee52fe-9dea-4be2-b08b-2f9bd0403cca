<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING/DTD BEAN/EN" "http://www.springframework.org/dtd/spring-beans.dtd">
<beans>
	<!-- ==================================================================== -->
	<!-- triggers -->
	<!-- ==================================================================== -->
	<bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="triggers">
			<list>
				<ref local="updateAaCacheJobTrigger"/>
			</list>
		</property>
	</bean>
	<!-- ==================================================================== -->
	<!-- 更新AA 權限 排程 -->
	<!-- ==================================================================== -->
	<bean id="updateAaCacheJobTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail">
			<ref bean="updateAaCacheJobDetail"/>
		</property>
		<property name="cronExpression">
	<!-- 
		這裡指定trigger執行那個group的job."0 0 3 * * ?"
		與 在unix like裡的crontab job的設定類似. 這裡表示每天裡的每天3點執行一次
		Seconds Minutes Hours Day-of-Month Month Day-of-Week Year(optional
		field)	
	-->
			<value>0 0 * * * ?</value>
		</property>
	</bean>
	<bean id="updateAaCacheJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject">
			<ref bean="scheduleService"/>
		</property>
		<property name="targetMethod">
			<value>reloadAaCache</value>
		</property>
		<property name="concurrent" value="false" />		
	</bean>
</beans>
