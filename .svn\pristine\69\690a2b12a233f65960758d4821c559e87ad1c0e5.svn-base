package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 案件資料查詢
 *
 */
public class PRE4001 extends SuperBean {
	//查詢條件
	/** 預查編號 */
	private String q_prefixNo;
	/** 電子流水號 */
	private String q_telixNo;
	/** 申請人身分ID */
	private String q_applyId;
	/** 申請人姓名 */
	private String q_applyName;
	/** 統一編號 */
	private String q_banNo;
	/** 公司名稱 */
	private String q_companyName;
	/** 核覆結果 - 審核中 */
	private String q_approveResultA;
	/** 核覆結果 - 核准 */
	private String q_approveResultY;
	/** 核覆結果 - 否准 */
	private String q_approveResultN;
	//預查編號
	private String[] prefixNos;
	//目前的預查編號
	private String current;

	private String functionName;

	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(" ,nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL("  ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE");
		sqljob.appendSQL(",A.APPROVE_RESULT");
		sqljob.appendSQL(",A.APPROVE_DATE");
		sqljob.appendSQL(",A.APPROVE_TIME");
		sqljob.appendSQL(",A.COMPANY_NAME");
		sqljob.appendSQL(",A.RESERVE_DATE");
		sqljob.appendSQL(",A.COMPANY_STUS");
		sqljob.appendSQL(",A.RECEIVE_DATE");
		sqljob.appendSQL(",A.STAFF_NAME");
		sqljob.appendSQL("FROM CEDB1000 A");
		sqljob.appendSQL("WHERE 1=1");
		if( !"".equals(getQ_prefixNo()) ) {
			sqljob.appendSQL("AND (A.PREFIX_NO LIKE ?)");
			sqljob.addSuffixLikeParameter(getQ_prefixNo());
		}
		if( !"".equals(getQ_telixNo()) ) {
			sqljob.appendSQL("AND (A.TELIX_NO = ?)");
			sqljob.addParameter(getQ_telixNo().toUpperCase());
		}
		if( !"".equals(getQ_applyId()) ) {
			sqljob.appendSQL("AND (A.APPLY_ID = ?)");
			sqljob.addParameter(getQ_applyId().toUpperCase());
		}
		if( !"".equals(getQ_applyName()) ) {
			sqljob.appendSQL("AND (A.APPLY_NAME = ?)");
			sqljob.addParameter(getQ_applyName());
		}
		if( !"".equals(getQ_banNo()) ) {
			sqljob.appendSQL("AND (A.BAN_NO = ?)");
			sqljob.addParameter(getQ_banNo());
		}
		if( !"".equals(getQ_companyName()) ) {
			sqljob.appendSQL("AND ( A.COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%"));
			//需同步查詢CEDB1001
			//sqljob.appendSQL("OR A.PREFIX_NO IN ( SELECT PREFIX_NO FROM CEDB1001 WHERE COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%")+" )");
			//改用Lucene查詢
			List<String> tempPrefixNos = ServiceGetter.getInstance().getIndexSearchService().searchPrefixNos(getQ_companyName());
			if(null!=tempPrefixNos && !tempPrefixNos.isEmpty()) {
				sqljob.appendSQL("OR A.PREFIX_NO IN (");
				boolean isFirst = true;
				int count = 0;
				for(String tempPrefixNo : tempPrefixNos) {
					if (count > 999) {
						break;
					}
					if(!isFirst) sqljob.appendSQL(",");
					sqljob.appendSQL("'"+tempPrefixNo+"'");
					isFirst = false;
					count++;
				}
				sqljob.appendSQL(")");
			}
			sqljob.appendSQL(")");
		}
		if( "Y".equals(getQ_approveResultA()) || "Y".equals(getQ_approveResultY()) || "Y".equals(getQ_approveResultN()) ) {
			sqljob.appendSQL("AND (");
			boolean isFirst = true;
			if( "Y".equals(getQ_approveResultA()) ) {
				if(!isFirst) sqljob.appendSQL("OR");
				sqljob.appendSQL("A.APPROVE_RESULT='A'");
				isFirst = false;
			}
			if( "Y".equals(getQ_approveResultY()) ) {
				if(!isFirst) sqljob.appendSQL("OR");
				sqljob.appendSQL("A.APPROVE_RESULT='Y'");
				isFirst = false;
			}
			if( "Y".equals(getQ_approveResultN()) ) {
				if(!isFirst) sqljob.appendSQL("OR");
				sqljob.appendSQL("A.APPROVE_RESULT='N'");
				isFirst = false;
			}
			sqljob.appendSQL(")");
		}
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");
		java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<Map<String,Object>> it = objList.iterator();
			Map<String,Object> o;
			String[] rowArray = new String[10];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[10];
				rowArray[0] = Common.get(o.get("PREFIX_NO"));
				rowArray[1] = Common.get(o.get("APPLY_NAME"));
				rowArray[2] = Common.get(o.get("CHANGE_TYPE"));
				rowArray[3] = ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get(o.get("APPROVE_RESULT")));
				rowArray[4] = Common.get(o.get("COMPANY_NAME"));
				rowArray[5] = Common.get(o.get("RESERVE_DATE"));
				rowArray[6] = Datetime.formatRocDate(Common.get(o.get("APPROVE_DATE")))+" "+Datetime.formatRocTime(Common.get(o.get("APPROVE_TIME")));
				rowArray[7] = ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(Common.get(o.get("COMPANY_STUS")));
				rowArray[8] = Common.get(o.get("RECEIVE_DATE"));
				rowArray[9] = Common.get(o.get("STAFF_NAME"));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}

	public String getQ_telixNo() {return checkGet(q_telixNo);}
	public void setQ_telixNo(String s) {this.q_telixNo = checkSet(s);}

	public String getQ_applyId() {return checkGet(q_applyId);}
	public void setQ_applyId(String s) {this.q_applyId = checkSet(s);}

	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {this.q_applyName = checkSet(s);}

	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {this.q_banNo = checkSet(s);}

	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {this.q_companyName = checkSet(s);}

	public String getQ_approveResultA() {return checkGet(q_approveResultA);}
	public void setQ_approveResultA(String s) {this.q_approveResultA = checkSet(s);}

	public String getQ_approveResultY() {return checkGet(q_approveResultY);}
	public void setQ_approveResultY(String s) {this.q_approveResultY = checkSet(s);}

	public String getQ_approveResultN() {return checkGet(q_approveResultN);}
	public void setQ_approveResultN(String s) {this.q_approveResultN = checkSet(s);}

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] prefixNos) {this.prefixNos = prefixNos;}

	public String getCurrent() {return checkGet(current);}
	public void setCurrent(String s) {this.current = checkSet(s);}

	public String getFunctionName() {return checkGet(functionName);}
	public void setFunctionName(String s) {this.functionName = checkSet(s);}

}