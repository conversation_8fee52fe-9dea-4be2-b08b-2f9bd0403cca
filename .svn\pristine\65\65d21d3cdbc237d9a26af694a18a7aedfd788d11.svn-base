<!--
程式目的：由預查編號或統一編號匯入營業項目
程式代號：PRE3001_02
撰寫日期：103.05.26
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001_02">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<%
if ("init".equals(obj.getState())) {
} else if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
$( document ).ready(function() {
	//全選
	$("#checkAll").click(function(){
		commonUtils.all("chk");
	});
	//全不選
	$("#uncheckAll").click(function(){
		commonUtils.unAll("chk");
	});
	//重新輸入
	$('#btnReset').click(function(){
		form1.reset();
	});
	//執行查詢
	$('#btnSearch').click(function(){
		var prefixNo = $("#prefixNo").val();
		var banNo = $("#banNo").val();
		if (prefixNo == '' && banNo == '') {
			alert("請輸入查詢條件");
			return false;
		} else {
			form1.state.value = "queryAll";
			form1.submit();
		}
	});
	//匯入
	$('#btnImport').click(function(){
		if (window.opener.ilocation == -1) {
			$('input[name="chk"]').each(function(){
				if(this.checked) {
					var itemCode = this.value.split("@")[0];
					var item = this.value.split("@")[1];
					window.opener.insertItem(itemCode, item);
				}
			});
		}
		else {
			$($('input[name="chk"]').get().reverse()).each(function(){
				if(this.checked) {
					var itemCode = this.value.split("@")[0];
					var item = this.value.split("@")[1];
					window.opener.insertItem(itemCode, item);
				}
			});
		}
		
	});
	$('#prefixNo').keydown(function(e){
		var code = e.keyCode || e.which;
		if (code == 13) {
	   		e.preventDefault();
	   		$('#banNo').val('');
	   		if( this.value.length ==0 ) {
	   			alert("請輸入查詢條件");
	   			return false;
	   		} else {
				form1.state.value = "queryAll";
				form1.submit();
	   		}
		}
	});
	$('#banNo').keydown(function(e){
		var code = e.keyCode || e.which;
		if (code == 13) {
	   		e.preventDefault();
	   		$('#prefixNo').val('');
	   		if( this.value.length ==0 ) {
	   			alert("請輸入查詢條件");
	   			return false;
	   		} else {
				form1.state.value = "queryAll";
				form1.submit();
	   		}
		}
	});
	//畫面初始
	$('#prefixNo').focus();
});
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='由預查編號或統一編號匯入營業項目'/>
</c:import>

<!-- TOOLBAR AREA -->
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td style="text-align:left">
			<input class="toolbar_default" type="button" id="checkAll" name="checkAll" value="全部選取" />&nbsp;
			<input class="toolbar_default" type="button" id="uncheckAll" name="uncheckAll" value="取消選取" />&nbsp;
			<input class="toolbar_default" type="button" id="btnReset" name="btnReset" value="重新輸入" />&nbsp;
			<input class="toolbar_default" type="button" id="btnSearch" name="btnSearch" value="執行查詢" />&nbsp;
			<input class="toolbar_default" type="button" id="btnImport" name="btnImport" value="匯入" />&nbsp;
		</td>
		<td align="right">
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" onclick="javascript:window.close();" />&nbsp;
		</td>
	</tr>
</table>
<!-- TOOLBAR AREA -->

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%">
	<tr>
		<td class="td_form" width="160px">預查編號:</td>
		<td class="td_form_white">
			<input class="field" type="text" id="prefixNo" name="prefixNo" size="10" value="<%=obj.getPrefixNo()%>" />
			(由預查資料庫中匯入)
		</td>
	</tr>
	<tr>
		<td class="td_form" width="160px">統一編號:</td>
		<td class="td_form_white">
			<input class="field" type="text" id="banNo" name="banNo" size="10" value="<%=obj.getBanNo()%>" />
			(由已登記公司資料庫中匯入)
		</td>
	</tr>
	</table>
	</div>
</td></tr>

<tr><td class="bg">
	<div id="listContainer">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" style="width:80px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">選取</a></th>
    		<th class="listTH" style="width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">序號</a></th>
    		<th class="listTH" style="width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">代碼</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">營業項目</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = { true,false,false,false};
  			boolean displayArray[] = {false, true, true, true};
  			String[] alignArray = {"left","left","left","left"};
  			out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,"true","chk"));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>