package com.kangdainfo.tcfi.loader;

/**
 * 核覆結果
 *
 */
public class SystemCode05Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_05";
	private static final String CODE_KIND = "05";//05:核覆結果
	//singleton
	private static SystemCode05Loader instance;
	public SystemCode05Loader() {
		if (SystemCode05Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode05Loader.instance);
		}
		SystemCode05Loader.instance = this;
	}
	public static SystemCode05Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}