package com.kangdainfo.tcfi.model.lms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.LmsdRegUnit;

public class LmsdRegUnitDao extends BaseDaoJdbc implements RowMapper<LmsdRegUnit> {
	
	private static final String SQL_findAll = "SELECT * FROM LMS.LMSD_REGUNIT WHERE ENABLE = 'Y' ";

	public List<LmsdRegUnit> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<LmsdRegUnit>)getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	@Override
	public LmsdRegUnit mapRow(ResultSet rs, int idx) throws SQLException {
		LmsdRegUnit obj = null;
		if(null!=rs) {
			obj = new LmsdRegUnit();
			obj.setAgencyCode(rs.getString("AGENCYCODE"));
			obj.setAgencyName(rs.getString("AGENCYNAME"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setKeyinStaffCode(rs.getString("KEYIN_STAFF_CODE"));
			obj.setOid(rs.getString("OID"));
		}
		return obj;
	}

}
