package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1008;

public class Cedb1008Dao extends BaseDaoJdbc implements RowMapper<Cedb1008> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1008 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1008> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedb1008>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static String sql_saveByObj = "INSERT INTO Cedb1008(PREFIX_NO,SEQ_NO,BUSI_ITEM,BUSI_ITEM_NO,UPDATE_DATE,UPDATE_ID_NO,UPDATE_TIME) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?) ";
	
	private static String sql_insertFromCedb1002 = "INSERT INTO CEDB1008(prefix_no, seq_no, busi_item, busi_item_no, update_date, update_time, update_id_no) " 
									+" select prefix_no, seq_no, busi_item, busi_item_no, ?, ?, ? from eicm.cedb1002 where prefix_no = ? ";
	
	public int insertFromCedb1002( String updateDate, String updateTime, String updateUserId, String prefixNo) {
		SQLJob sqljob = new SQLJob(sql_insertFromCedb1002);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		sqljob.addParameter(updateUserId);
		sqljob.addParameter(prefixNo);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	
	
	public int insert(Cedb1008 cedb1008) {
		if (cedb1008 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);
		
		sqljob.addParameter(cedb1008.getPrefixNo());
		sqljob.addParameter(cedb1008.getSeqNo());
		sqljob.addParameter(cedb1008.getBusiItem());
		sqljob.addParameter(cedb1008.getBusiItemNo());
		sqljob.addParameter(cedb1008.getUpdateDate());
		sqljob.addParameter(cedb1008.getUpdateIdNo());
		sqljob.addParameter(cedb1008.getUpdateTime());


		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	private static final String SQL_findByComposite = "SELECT * FROM CEDB1008 WHERE PREFIX_NO = ? AND UPDATE_ID_NO = ? AND UPDATE_DATE = ? AND UPDATE_TIME = ? ORDER BY PREFIX_NO, UPDATE_ID_NO, UPDATE_DATE, UPDATE_TIME, SEQ_NO";
	public List<Cedb1008> findByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		SQLJob sqljob = new SQLJob(SQL_findByComposite);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(updateIdNo);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
	}
	
	private int[] getSqlTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR

		};
	}


	@Override
	public Cedb1008 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1008 obj = null;
		if(null!=rs) {
			obj = new Cedb1008();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBusiItem(rs.getString("BUSI_ITEM"));
			obj.setBusiItemNo(rs.getString("BUSI_ITEM_NO"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
		}
		return obj;
	}

}
