package com.kangdainfo.util.report;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.jasperreports.engine.JRExporter;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JRParameter;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRPdfExporterParameter;
import net.sf.jasperreports.engine.export.JRRtfExporter;

import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.engine.export.ooxml.JRDocxExporter;
import net.sf.jasperreports.engine.fill.JRGzipVirtualizer;
import net.sf.jasperreports.engine.util.JRLoader;

import org.apache.commons.collections.CollectionUtils;

public abstract class JasperReportMaker {
	private static final String FILE_NAME_PREFIX = "TCFI_";

	public static File makePdfReport(File jrprintFile) throws Exception
	{
		List<File> list = new ArrayList<File>();
		list.add(jrprintFile);
		return makePdfReport(list);
	}

	public static File makeXlsReport(File jrprintFile) throws Exception
	{
		List<File> list = new ArrayList<File>();
		list.add(jrprintFile);
		return makeXlsReport(list);
	}

	public static File makeRtfReport(File jrprintFile) throws Exception
	{
		List<File> list = new ArrayList<File>();
		list.add(jrprintFile);
		return makeRtfReport(list);
	}
	public static File makeDocReport(File jrprintFile) throws Exception
	{
		List<File> list = new ArrayList<File>();
		list.add(jrprintFile);
		return makeDocReport(list);
	}
	public static File makePdfReport(List<File> jrprintFileList) throws Exception
	{
		File report = null;
		try {
			if (CollectionUtils.isNotEmpty(jrprintFileList))
			{
				List<Object> jasperPrints = new ArrayList<Object>();
				for(File jrprintFile : jrprintFileList)
				{
					jasperPrints.add(JRLoader.loadObject(jrprintFile));
				}
				
				report = File.createTempFile(FILE_NAME_PREFIX, ".pdf");
				FileOutputStream fos = new FileOutputStream(report);

				JRPdfExporter exporter = new JRPdfExporter();
				exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrints);
				exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, fos);
				exporter.setParameter(JRPdfExporterParameter.IS_CREATING_BATCH_MODE_BOOKMARKS, Boolean.TRUE);
				exporter.exportReport();

				fos.flush();
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		
		return report;
	}

	public static File makeXlsReport(List<File> jrprintFileList) throws Exception
	{
		File report = null;
		try {
			if (CollectionUtils.isNotEmpty(jrprintFileList))
			{
				List<Object> jasperPrints = new ArrayList<Object>();
				for(File jrprintFile : jrprintFileList)
				{
					jasperPrints.add(JRLoader.loadObject(jrprintFile));
				}
				
				report = File.createTempFile(FILE_NAME_PREFIX, ".xls");
				FileOutputStream fos = new FileOutputStream(report);

				JRExporter exporter = new JRXlsExporter();
				exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrints);
				exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, fos);
				exporter.setParameter(JRPdfExporterParameter.IS_CREATING_BATCH_MODE_BOOKMARKS, Boolean.TRUE);
				exporter.setParameter(JRXlsExporterParameter.IS_ONE_PAGE_PER_SHEET, Boolean.FALSE);
				exporter.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
				exporter.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
				exporter.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.FALSE);
				exporter.exportReport();

				fos.flush();
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		
		return report;
	}
	

	public static File makeRtfReport(List<File> jrprintFileList) throws Exception
	{
		File report = null;
		try {
			if (CollectionUtils.isNotEmpty(jrprintFileList))
			{
				List<Object> jasperPrints = new ArrayList<Object>();
				for(File jrprintFile : jrprintFileList)
				{
					jasperPrints.add(JRLoader.loadObject(jrprintFile));
				}
				
				report = File.createTempFile(FILE_NAME_PREFIX, ".rtf");
				FileOutputStream fos = new FileOutputStream(report);

				JRExporter exporter = new JRRtfExporter();
				exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrints);
				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, fos);
				exporter.setParameter(JRExporterParameter.IGNORE_PAGE_MARGINS, Boolean.TRUE);
				exporter.exportReport();

				fos.flush();
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		
		return report;
	}
	public static File makeDocReport(List<File> jrprintFileList) throws Exception
	{
		File report = null;
		try {
			if (CollectionUtils.isNotEmpty(jrprintFileList))
			{
				List<Object> jasperPrints = new ArrayList<Object>();
				for(File jrprintFile : jrprintFileList)
				{
					jasperPrints.add(JRLoader.loadObject(jrprintFile));
				}
				
				report = File.createTempFile(FILE_NAME_PREFIX, ".doc");
				FileOutputStream fos = new FileOutputStream(report);

				JRExporter exporter = new JRRtfExporter();//2024/03/01 修改回來
				exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrints);
				exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, fos);
				exporter.setParameter(JRExporterParameter.IGNORE_PAGE_MARGINS, Boolean.TRUE);
				exporter.exportReport();

				fos.flush();
				fos.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		
		return report;
	}
	public static File makePdfReport(List<?> dataList, Map<String, Object> parameters, String jasperFilePath) throws Exception
	{
		File report = null;
		File jrprintFile = JasperReportMaker.createJrprintFile(dataList, parameters, jasperFilePath);
		if (jrprintFile != null)
		{
			report = JasperReportMaker.makePdfReport(jrprintFile);
		}
		return report;
	}

	public static File makeXlsReport(List<?> dataList, Map<String, Object> parameters, String jasperFilePath) throws Exception
	{
		File report = null;
		File jrprintFile = JasperReportMaker.createJrprintFile(dataList, parameters, jasperFilePath);
		if (jrprintFile != null)
		{
			report = JasperReportMaker.makeXlsReport(jrprintFile);
		}
		return report;
	}

	public static File makeRtfReport(List<?> dataList, Map<String, Object> parameters, String jasperFilePath) throws Exception
	{
		File report = null;
		File jrprintFile = JasperReportMaker.createJrprintFile(dataList, parameters, jasperFilePath);
		if (jrprintFile != null)
		{
			report = JasperReportMaker.makeRtfReport(jrprintFile);
		}
		return report;
	}
	public static File makeDocReport(List<?> dataList, Map<String, Object> parameters, String jasperFilePath) throws Exception
	{
		File report = null;
		File jrprintFile = JasperReportMaker.createJrprintFile(dataList, parameters, jasperFilePath);
		if (jrprintFile != null)
		{
			report = JasperReportMaker.makeDocReport(jrprintFile);
		}
		return report;
	}
	public static File createJrprintFile(List<?> dataList, Map<String, Object> parameters, String jasperFilePath) throws Exception
	{
		try {
			JRGzipVirtualizer virtualizer = new JRGzipVirtualizer(2);
			virtualizer.setReadOnly(true);
			if(null==parameters) parameters = new HashMap<String, Object>();
			parameters.put(JRParameter.REPORT_VIRTUALIZER, virtualizer);

			File sourceFile = new File(jasperFilePath);
			
			JasperReport jasperReport = (JasperReport)JRLoader.loadObject(sourceFile);
			File tempFile = File.createTempFile("JASPER", ".jrprint");
			
			if (CollectionUtils.isNotEmpty(dataList)) {
				JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(dataList);
				JasperFillManager.fillReportToFile(jasperReport, tempFile.getPath(), parameters, dataSource);
			} else {
				JasperFillManager.fillReportToFile(jasperReport, tempFile.getPath(), parameters);
			}
			return tempFile;
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

}