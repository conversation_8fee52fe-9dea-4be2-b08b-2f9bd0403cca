package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 公司預查申請案收件人資料檔(CEDB1023)
 *
 */
public class Cedb1023Dao
	extends BaseDaoJdbc
	implements RowMapper<Cedb1023>
{
	public Cedb1023 findByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1023 WHERE PREFIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		List<?> list = getJdbcTemplate().query(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray(), this);
		return list.isEmpty() ? null : (Cedb1023) list.get(0);
	}

	public int insert(Cedb1023 o) {
		if (null==o) return 0;
		if ( o.getClosed() == null || "".equals(o.getClosed())) {
			o.setClosed("N");
		}
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1023(PREFIX_NO,GET_ADDR,GET_NAME,SMS,CONTACT_CEL,CHANGE_TYPE, CLOSED, ORG_TYPE) VALUES (?,?,?,?,?,?,?,?)");
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getSms());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getContactCel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getChangeType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getClosed());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getOrgType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public int update(Cedb1023 o) {
		if (null==o) return 0;
		if (o.getClosed() == null || "".equals(o.getClosed())) {
			o.setClosed("N");
		}
		SQLJob sqljob = new SQLJob("UPDATE CEDB1023 SET");
		sqljob.appendSQL("  GET_ADDR=?");
		sqljob.appendSQL(" ,GET_NAME=?");
		sqljob.appendSQL(" ,SMS=?");
		sqljob.appendSQL(" ,CONTACT_CEL=?");
		sqljob.appendSQL(" ,CHANGE_TYPE=?");
		sqljob.appendSQL(" ,CLOSED=? ");
		sqljob.appendSQL(" ,ORG_TYPE=? ");
		sqljob.appendSQL(" WHERE PREFIX_NO=?");
		sqljob.addParameter(o.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getSms());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getContactCel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getChangeType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getClosed());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getOrgType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}
	
	public int delete(String prefixNo) throws Exception{
		if(CommonStringUtils.isEmpty(prefixNo)) return 0;
		StringBuffer sb = new StringBuffer();
		sb.append("DELETE FROM CEDB1023 WHERE PREFIX_NO="+Common.sqlChar(prefixNo));
		return getJdbcTemplate().update(sb.toString());
	}
	
	public int set(Cedb1023 cedb1023) {
		if (cedb1023 == null)
			return 0;
		StringBuffer sql = new StringBuffer();
		sql.append(" UPDATE cedb1023 SET ");
		sql.append(" GET_NAME = ?,");
		sql.append(" GET_ADDR = ?");
		sql.append(" WHERE PREFIX_NO = ? ");
		Object[] parameters = { cedb1023.getGetName(), cedb1023.getGetAddr(), cedb1023.getPrefixNo() };
		return getJdbcTemplate().update(sql.toString(), parameters);
	}
	
	public static String decodeChangeType = "SELECT DECODE(CHANGE_TYPE,'0','設立預查','1','名稱變更','2','所營變更','3','名稱及所營變更','') AS CHANGE_TYPE FROM CEDB1023 WHERE PREFIX_NO=?";
	public String findChangeTypeByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		SQLJob sqljob = new SQLJob(decodeChangeType);
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		List<String> list = getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), String.class);
		return list.isEmpty() ? "" : list.get(0);
	}
	
	private static final String SQL_SELECT_BY_PREFIXNOS = 
			"SELECT * FROM CEDB1023  WHERE PREFIX_NO IN ";// 2024/04/09 新增SQL 
	
	public List<Cedb1023> selectByPrefixNos(List<String> prefixNos) {// 2024/04/09 新增方法
		StringBuilder inClausePlaceholders = new StringBuilder("(");
		List<List<String>> prefixNosLists = partitionList(prefixNos, 1000);
		
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		inClausePlaceholders.append("?");
		        if (j < prefixNosLists.get(i).size() - 1) {
		            inClausePlaceholders.append(", ");
		        }
	    	}
	    	inClausePlaceholders.append(") or PREFIX_NO IN ( ");
	    }
	    inClausePlaceholders.delete(inClausePlaceholders.length() - 19, inClausePlaceholders.length());
	    String sql = SQL_SELECT_BY_PREFIXNOS + inClausePlaceholders;
	    
	    SQLJob sqljob = new SQLJob(sql);
	    for (String prefix : prefixNos) {
	    	sqljob.addParameter(prefix);
	    } 
	    
	    for (int i = 0; i < prefixNos.size(); i++) {
	    	sqljob.addSqltypes(java.sql.Types.NVARCHAR);
	    }
	    
	    List<Cedb1023> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	    return list;
	}
	
	private static final String SQL_SELECT_BY_CHANGE_TYPE_AND_PREFIXNOS = 
			"SELECT * FROM CEDB1023  WHERE CHANGE_TYPE = ? AND PREFIX_NO IN ";// 2024/04/09 新增SQL 
	
	public List<Cedb1023> selectByChangeTypeAndPrefixNos(String changeType, List<String> prefixNos) {// 2024/04/09 新增方法
		StringBuilder inClausePlaceholders = new StringBuilder("(");
		List<List<String>> prefixNosLists = partitionList(prefixNos, 1000);
		
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		inClausePlaceholders.append("?");
		        if (j < prefixNosLists.get(i).size() - 1) {
		            inClausePlaceholders.append(", ");
		        }
	    	}
	    	
	    	if (i == 0) {
	    		inClausePlaceholders.append(") OR (CHANGE_TYPE = ? AND PREFIX_NO IN ( ");
	    	}else {
	    		inClausePlaceholders.append(")) OR (CHANGE_TYPE = ? AND PREFIX_NO IN ( ");
	    	}
	    }
	    
	    inClausePlaceholders.delete(inClausePlaceholders.length() - 40, inClausePlaceholders.length());
	    
	    String sql = SQL_SELECT_BY_CHANGE_TYPE_AND_PREFIXNOS + inClausePlaceholders;
	    
	    SQLJob sqljob = new SQLJob(sql);
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	sqljob.addParameter(changeType);
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		sqljob.addParameter(prefixNosLists.get(i).get(j));
	    	}
	    } 
	    
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	sqljob.addSqltypes(java.sql.Types.VARCHAR);
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
	    	}
	    }
	    
	    List<Cedb1023> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	    
	    return list;
	}

	public void deleteByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return;
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1023 WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1023 mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1023 obj = null;
		if (null != rs) {
			obj = new Cedb1023();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setGetAddr(rs.getString("GET_ADDR"));
			obj.setGetName(rs.getString("GET_NAME"));
			obj.setSms(rs.getString("SMS"));
			obj.setContactCel(rs.getString("CONTACT_CEL"));
			obj.setChangeType(rs.getString("CHANGE_TYPE"));
			obj.setClosed(rs.getString("CLOSED"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
		}
		return obj;
	}

	/**
	 * 2024/04/09 新增，針對oracle 使用in時不能超過1000個使用
	 * @param list
	 * @param partitionSize
	 * @return List<List<T>>
	 */
	public static <T> List<List<T>> partitionList(List<T> list, int partitionSize) {
	    List<List<T>> partitions = new ArrayList<>();
	    for (int i = 0; i < list.size(); i += partitionSize) {
	        partitions.add(list.subList(i, Math.min(i + partitionSize, list.size())));
	    }
	    return partitions;
	}
}