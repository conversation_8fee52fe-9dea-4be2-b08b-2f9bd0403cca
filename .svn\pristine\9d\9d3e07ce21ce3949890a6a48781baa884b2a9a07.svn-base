package com.kangdainfo.tcfi.view.pre;

/*
程式目的：預查線上申辦電子收據
程式代號：pre4011
撰寫日期：103.05.12
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE4011 extends SuperBean {	

	private static final String FEE_CODE_COPY = "I1";	//抄錄/影印
	private static final String FEE_CODE_LOOKUP = "I2";	//查閱費
	private static final String FEE_CODE_REVIEW = "I3";	//審查費
	private static final String FEE_CODE_CHI_PROVE = "J1";	//中文證明書
	private static final String FEE_CODE_ENG_PROVE = "J2";	//英文證明書

	private String q_telixNo;       // 網路收文號
	private String q_prefixNo;      // 預查文號
	private String q_type;          // 查詢類別

	private String telixNo;         // 電子收文流水號
	private String receiptNo;       // 收據代碼
	private String applyName;       // 申請人姓名
	private String incomeCode;      // 收入代號
	private String incomeName;      // 收入代號
	private String amount;          // 金額
	private String totalAmount;		// 金額總額(阿拉伯數字)
	private String applyKind;       // 事由
	private String chineseAmount;   // 金額(中文)
	private String payDate;         // 繳款日期
	private String remark;          // 備註

	// ----------------------------------getters and setters of local variable bellow ---------------------------

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	public String getQ_telixNo() {return checkGet(q_telixNo);}
	public void setQ_telixNo(String s) {q_telixNo = checkSet(s);}
	public String getQ_type() {return checkGet(q_type);}
	public void setQ_type(String s) {q_type = checkSet(s);}

	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {telixNo = checkSet(s);}
	public String getReceiptNo() {return checkGet(receiptNo);}
	public void setReceiptNo(String s) {receiptNo = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getIncomeCode() {return checkGet(incomeCode);}
	public void setIncomeCode(String s) {incomeCode = checkSet(s);}
	public String getIncomeName() {return checkGet(incomeName);}
	public void setIncomeName(String s) {incomeName = checkSet(s);}
	public String getAmount() {return checkGet(amount);}
	public void setAmount(String s) {amount = checkSet(s);}
	public String getTotalAmount() {return checkGet(totalAmount);}
	public void setTotalAmount(String s) {totalAmount = checkSet(s);}
	public String getApplyKind() {return checkGet(applyKind);}
	public void setApplyKind(String s) {applyKind = checkSet(s);}
	public String getChineseAmount() {return checkGet(chineseAmount);}
	public void setChineseAmount(String s) {chineseAmount = checkSet(s);}
	public String getPayDate() {return checkGet(payDate);}
	public void setPayDate(String s) {payDate = checkSet(s);}
	public String getRemark() {return checkGet(remark);}
	public void setRemark(String s) {remark = checkSet(s);}

	// ----------------------------------------------------------------------------------------------------------
	// ----------------------------------function never used bellow----------------------------------------------
	/** 列印前檢核 */
	public static String ajaxCheck(String prefixNo, String telixNo) throws Exception {
		//1:檢查是否有傳入參數
		if("".equals(Common.get(prefixNo)) && "".equals(Common.get(telixNo))) {
			return "查無資料";
		}
		//2:檢查主檔是否有資料
		Cedb1000 cedb1000 = null;
		if(!"".equals(Common.get(prefixNo))) {
			cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(prefixNo);
			if(null==cedb1000) {
				return "查無資料";
			}
		} else if(!"".equals(Common.get(telixNo))) {
			cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByTelixNo(telixNo);
			if(null==cedb1000) {
				return "查無資料";
			}
		}
		//3:檢查是否有網路流水號
		if(null==cedb1000 || null==cedb1000.getTelixNo() || "".equals(cedb1000.getTelixNo())) {
			return "查無資料";
		}
		telixNo = cedb1000.getTelixNo();
		if( telixNo.startsWith("OSC") || telixNo.startsWith("OSS") ) {
			//一站式
			//4-1:檢查主檔是否有資料
			OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(telixNo);
			if(null==ossmApplMain) {
				return "查無電子案號的申請資料";
			}
			//流程類別
			String processNo = PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType())?"I":"B";
			//4-2:檢查流程檔
			OssmApplFlow ossmApplFlow = ServiceGetter.getInstance().getPrefixService().getOssmApplFlowByTelixNoAndProcessNo(telixNo, processNo);
			if(null==ossmApplFlow) {
				return "查無案件流程資料";
			}
			if(!PrefixConstants.OSSS_STATUS_103.equals(ossmApplFlow.getProcessStatus())) {
				return "案件尚未審核完成" ;
			}
			//4-3:檢查收費主檔
			OssmFeeMain ossmFeeMain = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(telixNo, processNo); 
			if(null==ossmFeeMain) {
				return "查無收據資料";
			}
			if("Y".equals(Common.get(ossmFeeMain.getReturnFlag()))) {
				return "此案件已辦理退費，不再提供電子收據列印";
			}
			//4-4:檢查收據明細
			OssmFeeDetail ossmFeeDetail = ServiceGetter.getInstance().getPrefixService().getOssmFeeDetailByTelixNoAndProcessNo(telixNo, processNo);
			if(null==ossmFeeDetail) {
				return "查無收據明細資料";
			}
		} else if ( telixNo.startsWith("Z") ) {
			//一維條碼
			//5-1:檢查主檔是否有資料
			Eedb1000 eedb1000 = ServiceGetter.getInstance().getPrefixService().findEedb1000ByTelixNo(telixNo);
			if(null==eedb1000) {
				return "查無電子案號的申請紀錄";
			}
			Eedb5000 eedb5000 = ServiceGetter.getInstance().getPrefixService().findEedb5000ByTelixNo(telixNo);
			if(null==eedb5000) {
				return "查無電子案號的繳費紀錄";
			}
			if ("".equals(Common.get(eedb5000.getReceiptNo())) || !eedb5000.getReceiptNo().startsWith("J") ) {
				return "非使用電子收據案件，98年5月4日後繳費案件才可列印電子收據！";
			}
			if(!"ZZ".equals(eedb1000.getProcessStatus())) {
				return "本案件尚在審核中，或尚未發文結案!";
			}
			if ("5".equals(eedb1000.getApproveResult())) {
				return "該收據已申請退費!";
			}
			if ("Y".equals(eedb5000.getReturnFlag()) && "".equals(Common.get(eedb5000.getReturnDate())) ) {
				return "此案件尚在退費作業中，請等待退費作業完成再列印收據!";
			}
			String amount = Common.get(eedb5000.getAmount());
			if("".equals(amount)) amount = "0";
			String returnAmount = Common.get(eedb5000.getReturnAmount());
			if("".equals(returnAmount)) returnAmount = "0";
			if (Integer.parseInt(amount) - Integer.parseInt(returnAmount) <= 0 ) {
				return "該收據已申請退費!";
			}
		}
		return "ok";
	}

	/** 列印收據 */
	public File doPrintPdf() throws Exception {
		File report = null;
		try {
			Cedb1000 cedb1000 = null;
			if("telix".equals(getQ_type())) {
				cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByTelixNo(getQ_telixNo());
			} else {
				cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(getQ_prefixNo());
			}

			if(null!=cedb1000) {
				String telixNo = Common.get(cedb1000.getTelixNo());
				if(!"".equals(telixNo)) {
					String receiptNo = "";
					String payDate = "";
					String applyName = "";
					String incomeName = "";
					String incomeCode = "";
					String amount = "";
					String applyKind = "";
					String chineseAmount = "";
					String remark = "";
					if(telixNo.startsWith("OSC") || telixNo.startsWith("OSS")) {
						OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(telixNo);
						//流程類別
						String processNo = PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType())?"I":"B";
						OssmFeeMain ossmFeeMain = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(telixNo, processNo);
						if(null!=ossmFeeMain) {
							Cedb1022 cedb1022 = ServiceGetter.getInstance().getPrefixService().getCedb1022ByPrefixNo(cedb1000.getPrefixNo());
							String tempNo = Common.get(ossmFeeMain.getReceiptNo());
							if(tempNo.length() > 5 ) {
								receiptNo = tempNo;
							}
							
							String tempPayDate = Common.get(ossmFeeMain.getPayDate());
							if(!"".equals(tempPayDate)) {
								payDate = "開立日期：中華民國 "+tempPayDate.substring(0,3)+" 年　"+tempPayDate.substring(3,5)+" 月　"+tempPayDate.substring(5)+"　日";
							}

							if("B".equals(processNo) && !ossmApplMain.getApplyType().startsWith("L")) {
								applyName = (cedb1022 != null && cedb1022.getApplyLawName() != null) ? cedb1022.getApplyLawName(): cedb1000.getApplyName();
							} else if("B".equals(processNo) && ossmApplMain.getApplyType().startsWith("L")) {
								applyName = cedb1000.getOldCompanyName();
							} else if("I".equals(processNo)) {
								applyName = ossmApplMain.getOrgName();
							} else {
								applyName = cedb1000.getCompanyName();
							}

							String feeType = checkFeeType(telixNo);
							incomeName = "3".equals(feeType)?"抄錄費":"審查費";
							incomeCode = "3".equals(feeType)?"05266501014 審查費":"05266501014 審查費";
							amount = Common.get(ossmFeeMain.getAmount());

							applyKind = "";
							if("B".equals(processNo)) {
								if(ossmApplMain.getApplyType().startsWith("L")) {
									applyKind = "公司名稱預查";
								} else {
									applyKind = "公司設立名稱預查";
								}
							} else if("I".equals(processNo)) {
								applyKind = "公司名稱預查";
							}

							chineseAmount = getChineseAmountOnList(amount) + "元整";
							
							String payType = (ossmFeeMain.getPayType() != null) ? Integer.toString(ossmFeeMain.getPayType()) : "0";
							remark = getRemark(payType);
						}
					} else if (telixNo.startsWith("Z")) {
						Eedb1000 eedb1000 = ServiceGetter.getInstance().getPrefixService().findEedb1000ByTelixNo(telixNo);
						Eedb1100 eedb1100 = ServiceGetter.getInstance().getPrefixService().findEedb1100ByTelixNo(telixNo);
						Eedb5000 eedb5000 = ServiceGetter.getInstance().getPrefixService().findEedb5000ByTelixNo(telixNo);
						if(null!=eedb1000 && null!=eedb1100 && null!=eedb5000) {
							String tempNo = Common.get(eedb5000.getReceiptNo());
							if(tempNo.length() > 5 ) {
								receiptNo = tempNo;
							}
							
							String tempPayDate = Common.get(eedb5000.getPayDate());
							if(!"".equals(tempPayDate)) {
								payDate = "中華民國 "+tempPayDate.substring(0,3)+" 年　"+tempPayDate.substring(3,5)+" 月　"+tempPayDate.substring(5)+"　日";
							}

							if ("Z2".equals(eedb1100.getCaseCode())) {
								applyName = eedb1000.getCompanyName();
							} else if ("Z1".equals(eedb1100.getCaseCode())) {
								if ("2".equals(eedb1000.getRoleType())) {
									Eedb3300 eedb3300 = ServiceGetter.getInstance().getPrefixService().findEedb3300ByTelixNo(telixNo);
									applyName = eedb3300.getOrgCorpName();
								} else {
									applyName = eedb1000.getApplyName();
								}
							} else if("G1".equals(eedb1100.getCaseCode()) || "G2".equals(eedb1100.getCaseCode())) {
								applyName = eedb1000.getCompanyName();
							} else {
								applyName = eedb1000.getCompanyName();
							}
							incomeName = "審查費";
							incomeCode = "05266501014";
							amount = Common.get(eedb5000.getAmount());
							if("".equals(amount)) amount = "0";
							
							applyKind = "07 公司名稱預查";
							
							chineseAmount = getChineseAmountOnList(amount) + "元整";
						}
					}

					Map<String, Object> parameters = new HashMap<String,Object>();
					//列印日期
					parameters.put("printDate", formatDate(Datetime.getYYYMMDD()));
					//章戳 - 主辦出納
					parameters.put("image1", ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/cashier.png"));
					//章戳 - 主辦會計
					parameters.put("image2", ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/accountant.png"));
					//章戳 - 部長
					parameters.put("image3", ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/director.png"));
					//背景浮水印
					parameters.put("image4", ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/stamp_4_v2.jpg"));
					//收據號碼
					parameters.put("receiptNo", receiptNo);
					//繳款日期
					parameters.put("payDate", payDate);

					ArrayList<PRE4011> dataList = new ArrayList<PRE4011>();

					PRE4011 data = new PRE4011();
					data.setApplyName( applyName );
					data.setTelixNo( telixNo ) ;
					data.setIncomeName( incomeName ) ;
					data.setIncomeCode( incomeCode ) ;
					data.setAmount( "$" + amount ) ;
					data.setTotalAmount("$" + amount);
					data.setApplyKind( applyKind ) ;
					data.setChineseAmount( chineseAmount ) ;
					data.setRemark(remark.isEmpty() ? "現金" : remark);
					dataList.add( data ) ;

					String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4011.jasper");
					report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
				}
			}
			return report;
		} catch( Exception e ) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		}
	}

	private String checkFeeType(String telixNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select * from ossm_case_fee_total where telix_no = '" + telixNo + "'");
		List<Map<String,Object>> list = ServiceGetter.getInstance().getOsssGeneralQueryDao().queryForList(sqljob);
		if (list == null || list.isEmpty())
			return null;
		// I1, J1, J2
		String copyItemCodes = FEE_CODE_COPY + ','
							+ FEE_CODE_CHI_PROVE + ','
							+ FEE_CODE_ENG_PROVE;
		// I2, I3
		String examineItemCodes = FEE_CODE_LOOKUP + ','
							+ FEE_CODE_REVIEW;
		boolean isCopy = false;
		boolean isExamine = false;
		boolean isRegister = false;
		for (int i = 0; i<list.size(); i++) {
			if (copyItemCodes.contains(Common.get(list.get(i).get("item_code"))))
				isCopy = true;
			else if (examineItemCodes.contains(Common.get(list.get(i).get("item_code"))))
				isExamine = true;
			else
				isRegister = true;
		}
		if (isRegister)
			return "1";
		else if (isExamine)
			return "2";
		else if (isCopy)
			return "3";
		else
			return null;
	}

	private String getChineseAmountOnList( String inputAmount ) {
		int amount = Integer.parseInt( inputAmount ) ;
		String[] numberArray = {"零","壹","貳","參","肆","伍","陸","柒","捌","玖"};
		String[] decimalArray = {"","拾","佰","仟","萬",""};
		StringBuffer chineseAmount = new StringBuffer();
		int index = 0;
		while(amount > 0) {
			if (amount%10 != 0) {
				chineseAmount.append(decimalArray[index]).append(numberArray[amount%10]);
			} // end if
			index = index + 1;
			amount = amount / 10;
		} // end while
		return chineseAmount.reverse().toString();
	} //  getChineseAmount() 

	private String formatDate(String inputDate) {
		String tempDate = "";
		if(null!=inputDate) {
			String year = inputDate.substring(0, 3);
			String month = inputDate.substring(3, 5);
			String day = inputDate.substring(5, 7);
			tempDate = tempDate.concat(year + " 年　" + month + " 月　" + day + " 日　");
		}
		return tempDate;
	}
	
	private String getRemark(final String payType) {
		String result = "";
		switch(payType) {
			case PrefixConstants.OSSS_PAY_BY_CREDIT_CARD:
				result = "規費金額不含交易手續費，信用卡繳費手續費以信用卡帳單為主。";
				break;
			case PrefixConstants.OSSS_PAY_BY_TRANS:
				result = "規費金額不含交易手續費，金融帳戶轉帳手續費為新台幣15元。";
				break;
			case PrefixConstants.OSSS_PAY_BY_IC_CARD:
				result = "規費金額不含交易手續費，晶片金融卡交易手續費為新台幣15元。";
				break;
			case PrefixConstants.OSSS_PAY_BY_IBON:
				result = "規費金額不含交易手續費，ibon繳費手續費為新台幣23元。";
				break;
			case PrefixConstants.OSSS_PAY_BY_LIFEET:
				result = "規費金額不含交易手續費，Life-ET繳費手續費為新台幣28元。";
				break;
			case PrefixConstants.OSSS_PAY_BY_FAMIPORT:
				result = "規費金額不含交易手續費，FamiPort繳費手續費為新台幣28元。";
				break;
			default:
				result = "";
		}
		return result;
	}

	public ArrayList<?> doQueryAll() throws Exception {return null;}
	public void doCreate() throws Exception{}
	public void doUpdate() throws Exception{}
	public void doDelete() throws Exception{}
	public Object doQueryOne() throws Exception{return null;}

}