  
-- Create Trigger
CREATE OR REPLACE TRIGGER EICM.TGI_PART_NAME_LOG
BEFORE INSERT OR UPDATE ON EICM.PART_NAME_LOG
referencing new as nu
For Each Row
Begin
  INSERT INTO EICM.INDEX_LOG (WS_ID,PARAM1,EXECUTE_DATE,CREATE_DATE,CREATE_USER,STATUS,REMARK)
  VALUES ('WS10001',:nu.BAN_NO,(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),:nu.ID_NO,'0','From EICM.TGI_PART_NAME_LOG');
End;
