package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * 管制項目代碼檔(CSMD_CTRLITEM)
 *
 */
public class CsmdCtrlitem extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 管制項目代碼 */
	private String ctrlItem;
	/** 管制項目名稱 */
	private String itemName;
	/** 啟用 */
	private String enable;
	/** 資料異動日期 */
	private Date updateDate;
	/** 資料異動人員 */
	private String updateUser;

	public String getCtrlItem() {return ctrlItem;}
	public void setCtrlItem(String ctrlItem) {this.ctrlItem = ctrlItem;}
	public String getItemName() {return itemName;}
	public void setItemName(String itemName) {this.itemName = itemName;}
	public String getEnable() {return enable;}
	public void setEnable(String enable) {this.enable = enable;}
	public Date getUpdateDate() {return updateDate;}
	public void setUpdateDate(Date updateDate) {this.updateDate = updateDate;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	
}