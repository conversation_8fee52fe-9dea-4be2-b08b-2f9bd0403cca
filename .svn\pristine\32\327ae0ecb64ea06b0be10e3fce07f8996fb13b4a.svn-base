package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class Restriction extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
	/** 限制條件代碼 */
	private String code;
	/** 限制條件名稱 */
	private String name;
	/** 限制條件項目_名稱標明專業 */
	private String nameSpecial;
	/** 限制條件項目_營業項目限專業經營 */
	private String itemSpecial;
	/** 限制條件項目_其他 */
	private String otherSpecial;
	/** 限制條件項目_其他內容 */
	private String otherNote;
	/** 限制條件項目_組織別 */
	private String orgType;
	/** 相關函號_單位 */
	private String relatedUnit;
	/** 相關函號_日期 */
	private String relatedDate;
	/** 相關函號_文號 */
	private String relatedNo;
	/** 狀態 */
	private String enable;
	/** 異動人員 */
	private String modIdNo;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;
	
	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}
	
	/** 限制條件代碼 */
	public String getCode() {return code;}
	/** 限制條件代碼 */
	public void setCode(String code) {this.code = code;}
	
	/** 限制條件名稱 */
	public String getName() {return name;}
	/** 限制條件名稱 */
	public void setName(String name) {this.name = name;}
	
	/** 限制條件項目_名稱標明專業 */
	public String getNameSpecial() {return nameSpecial;}
	/** 限制條件項目_名稱標明專業 */
	public void setNameSpecial(String nameSpecial) {this.nameSpecial = nameSpecial;}
	
	/** 限制條件項目_營業項目限專業經營 */
	public String getItemSpecial() {return itemSpecial;}
	/** 限制條件項目_營業項目限專業經營 */
	public void setItemSpecial(String itemSpecial) {this.itemSpecial = itemSpecial;}
	
	/** 限制條件項目_其他 */
	public String getOtherSpecial() {return otherSpecial;}
	/** 限制條件項目_其他 */
	public void setOtherSpecial(String otherSpecial) {this.otherSpecial = otherSpecial;}
	
	/** 限制條件項目_其他內容 */
	public String getOtherNote() {return otherNote;}
	/** 限制條件項目_其他內容 */
	public void setOtherNote(String otherNote) {this.otherNote = otherNote;}
	
	/** 限制條件項目_組織別 */
	public String getOrgType() {return orgType;}
	/** 限制條件項目_組織別 */
	public void setOrgType(String orgType) {this.orgType = orgType;}
	
	/** 相關函號_單位 */
	public String getRelatedUnit() {return relatedUnit;}
	/** 相關函號_單位 */
	public void setRelatedUnit(String relatedUnit) {this.relatedUnit = relatedUnit;}
	
	/** 相關函號_日期 */
	public String getRelatedDate() {return relatedDate;}
	/** 相關函號_日期 */
	public void setRelatedDate(String relatedDate) {this.relatedDate = relatedDate;}
	
	/** 相關函號_文號 */
	public String getRelatedNo() {return relatedNo;}
	/** 相關函號_文號 */
	public void setRelatedNo(String relatedNo) {this.relatedNo = relatedNo;}
	
	/** 狀態 */
	public String getEnable() {return enable;}
	/** 狀態 */
	public void setEnable(String enable) {this.enable = enable;}
	
	/** 異動人員 */
	public String getModIdNo() {return modIdNo;}
	/** 異動人員 */
	public void setModIdNo(String modIdNo) {this.modIdNo = modIdNo;}

	/** 異動日期 */
	public String getModDate() {return modDate;}
	/** 異動日期 */
	public void setModDate(String modDate) {this.modDate = modDate;}

	/** 異動時間 */
	public String getModTime() {return modTime;}
	/** 異動時間 */
	public void setModTime(String modTime) {this.modTime = modTime;}
}
