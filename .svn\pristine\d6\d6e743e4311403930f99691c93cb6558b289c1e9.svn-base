package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1007;

public class Cedb1007Dao extends BaseDaoJdbc implements RowMapper<Cedb1007> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1007 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1007> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedb1007>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static String sql_saveByObj = "INSERT INTO Cedb1007(PREFIX_NO,SEQ_NO,COMPANY_NAME,APPROVE_RESULT,UPDATE_DATE,UPDATE_TIME,UPDATE_ID_NO,REMARK) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?) ";

	public int insert(Cedb1007 cedb1007) {
		if (cedb1007 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);
		
		sqljob.addParameter(cedb1007.getPrefixNo());
		sqljob.addParameter(cedb1007.getSeqNo());
		sqljob.addParameter(cedb1007.getCompanyName());
		sqljob.addParameter(cedb1007.getApproveResult());
		sqljob.addParameter(cedb1007.getUpdateDate());
		sqljob.addParameter(cedb1007.getUpdateTime());
		sqljob.addParameter(cedb1007.getUpdateIdNo());
		sqljob.addParameter(cedb1007.getRemark());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	private static final String SQL_findByComposite = "SELECT * FROM CEDB1007 WHERE PREFIX_NO = ? AND UPDATE_ID_NO = ? AND UPDATE_DATE = ? AND UPDATE_TIME = ? ORDER BY PREFIX_NO, UPDATE_ID_NO, UPDATE_DATE, UPDATE_TIME, SEQ_NO";
	public List<Cedb1007> findByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		SQLJob sqljob = new SQLJob(SQL_findByComposite);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(updateIdNo);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
	}
	
	private int[] getSqlTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}
	
	/**
	 * 翻寫自 Facade1007.doReadApproveCmpyName()
	 * @param prefixNo
	 * @return
	 */
	public String findApproveCmpyName(String prefixNo) {

		String cmpyName = "";
		String sql = "SELECT COMPANY_NAME FROM CEDB1007 WHERE PREFIX_NO = ? AND APPROVE_RESULT = 'Y' ORDER BY UPDATE_DATE DESC,UPDATE_TIME DESC";
		Object[] parameters = {prefixNo};
		
		List<Cedb1007> list = getJdbcTemplate().query(sql, parameters, this);
		if(list != null) {
			return list.get(list.size()-1).getCompanyName();
		}

		return cmpyName;
	}

	@Override
	public Cedb1007 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1007 obj = null;
		if(null!=rs) {
			obj = new Cedb1007();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}
