<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8016">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8016" />
</jsp:include>
<%
	String isPop = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("isPop")));
	obj = (com.kangdainfo.tcfi.view.pre.PRE8016)obj.queryOne();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<style type="text/css">
	.td_form {
		font-size: 15px;
		text-align:center;
		font-weight:bold;
		background-color: #dddddd;
		color: #000000;
		border-top: 1px solid silver;
	}
</style>

<script src="./../../js/jquery/highcharts.src.js"></script>
<script src="./../../js/jquery/highcharts-more.src.js"></script>
<!-- 
<script type='text/javascript' src='http://code.jquery.com/jquery-git.js'></script>
<script src="http://code.highcharts.com/highcharts.js"></script>
<script src="http://code.highcharts.com/modules/exporting.js"></script>
 -->
<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){}

function init() {
	if(form1.tabId.value=="1"){
		  document.getElementById("paint").style.display = '';
		  $('#paint').height('400px');
	      document.getElementById("count").style.display = 'none';
	      $('#doPrint').show();
	}else{
		  document.getElementById("paint").style.display = 'none';
	      document.getElementById("count").style.display = '';
	}
	<%if("Y".equals(isPop)){%>
		printsetup();
	<%}%>
}

function popPre8016(urlStr) {
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;
	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	var url = getVirtualPath() + urlStr;
	window.open(url,'PRE8016',prop);
}

<% if("1".equals(obj.getTabId())){ %>
	$(function () {	    
		$('#paint').highcharts({
			chart: {
	            type: 'column'
	        },
	        title: {
	            text: <%=Datetime.getYYY()%> + '年度收文量'
	        },
	        xAxis: {
	            categories: ['第一季', '第二季', '第三季', '第四季']
	        },
	        yAxis: {
	            min: 0,
	            title: {
	                align: 'high',
	                offset: 0,
	                text: '案件量',
	                rotation: 0,
					x: 20,
	                y: -27
	            },
	            labels: {
	                overflow: 'justify',
	                style: {
			            fontSize: '16px'
			        },
					x: 20
	            }
	        },
            plotOptions: {
				bar: {
					dataLabels: {enabled: true}
	            },
	            column: {
	                pointPadding: 0.2,
	                borderWidth: 0
	            },
				series: {
					cursor: 'pointer',
					//event事件
					point: {
						events: {
							click: function() {
								if(form1.season.value == "")	return;
								var tSeason = form1.season.value;
								if(tSeason == "第一季")	tSeason = "1";
								else if(tSeason == "第二季")	tSeason = "2";
								else if(tSeason == "第三季")	tSeason = "3";
								else if(tSeason == "第四季")	tSeason = "4";
								else return;
								
								var url = "tcfi/pre/pre8016_01.jsp?season="+tSeason+"&searchType=season";
								popPre8016(url);
							}
						}
					},
					//值顯示在畫面上
	                dataLabels: {
	                    enabled: true,
	                    style: {
	                        fontWeight: 'bold'
	                    },
	                    formatter: function() {
	                        return this.y;
	                    }
	                }
				}   
	        },
	        legend: {
	            layout: 'vertical',
	            align: 'right',
	            verticalAlign: 'top',
	            x: 0,
	            y: 0,
	            floating: true,
	            borderWidth: 1,
	            backgroundColor: '#FFFFFF',
	            shadow: true
	        },
	        credits: {
	            enabled: false
	        },
	        //滑鼠游標
	        tooltip: {
	            formatter: function() {
	            	form1.season.value = this.x;
	                return '<b>'+ this.series.name +'</b><br/>'+
	                    this.x +': '+ this.y;
	            }
	        },
	        //Data
	        series: [{
	            name: '總收文量',
	            data: [<%=obj.getSeason1()%>, <%=obj.getSeason2()%>, <%=obj.getSeason3()%>, <%=obj.getSeason4()%>],
	            pointWidth: 40,
	            fontSize: '16px'
	        	},{
				name: '未辦結案件量',color:'#FF6347',
	            data: [
				{y:<%=obj.getSeason1Close()%>,color:'#FF6347'},{y:<%=obj.getSeason2Close()%>,color:'#FF6347'},
				{y:<%=obj.getSeason3Close()%>,color:'#FF6347'},{y:<%=obj.getSeason4Close()%>,color:'#FF6347'}],
	            pointWidth: 40,
	            fontSize: '16px'
			}]
	    });
	});
<% } %>

function printsetup(){ 
	//if( $.browser.msie ) {
	//	wb.execwb(7,1); 
	//} else {
	//	window.print();
	//}
	$('#doPrint').hide();
	window.print();
	//$('#doPrint').show();
} 
	
$(document).ready(function() {
	$('#doPrint').click(function(){
		<%if("Y".equals(isPop)){%>
			printsetup();
		<%}else{%>	
			var url = "tcfi/pre/pre8016_00.jsp?tabId=" + form1.tabId.value + "&isPop=Y";
			popPre8016(url);
		<%}%>
	});
});
</script>
</head>

<body topmargin="0" onLoad="init();showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post" autocomplete="off" >
<table width="100%" cellspacing="0" cellpadding="0" >
	<tr><td>
		<div id="buttonPrint" style="width:90%" align="right">
			<br>
			<br>
			<input class="toolbar_default" followPK="false" type="button" id="doPrint" name="doPrint" value="列　印" style="display:none"></input>
			<br>
			<br>
		</div>
		<div id="paint" style="width:90%"></div>
		<div id="count">
		<% if("2".equals(obj.getTabId())){ %>
					<table border="1" width="100%">
						<tr><td nowrap width="40%" valign="top">
								<table width="100%">
									<tr><td class="td_form" width="100%" colspan="2">
										預查編號現況
									</td></tr>
									<tr>
										<td class="td_form_white" width="60%">
											已收文預查編號
										</td>
										<td class="td_form_white" width="40%">
											<%=obj.getPrefixNoReceive()%>
										</td>
									</tr>
									<tr>
										<td class="td_form_white">
											已發文預查編號
										</td>
										<td class="td_form_white">
											<%=obj.getPrefixNoClose()%>
										</td>
									</tr>
									<tr>
										<td class="td_form_white">
											收發文預查編號差異
										</td>
										<td class="td_form_white">
											<%=obj.getPrefixNoDifferent()%>
										</td>
									</tr>
									<tr>
										<td colspan="2" align="center">
											<input class="toolbar_default" followPK="false" type="submit" name="refresh" value="資料更新">
										</td>
									</tr>
								</table>
							</td>
							<td nowrap width="60%" valign="top">
								<table width="100%">
									<tr><td class="td_form" width="100%" colspan="3">
										案件辦理情形
									</td></tr>
									<%=obj.getBuildPrefixNotData(obj.getPrefixNoCount(),"PRE8016_2","PRE8016_3","PRE8016_4","PRE8016_5","PRE8016_6")%>
								</table>
							</td>
						</tr>
					</table>
		<% } %>
		</div>
	</td></tr>
	<tr><td>
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>"/>
		<input type="hidden" id="tabId" name="tabId" value="<%=obj.getTabId()%>"/>
		<input type="hidden" id="season" name="season" value=""/>
	</td></tr>
</table>
</form>
<!-- 
<object classid="CLSID:8856F961-340A-11D0-A96B-00C04FD705A2" height=0 id=wb name=wb width=0></object> 
 -->
</body>
</html>