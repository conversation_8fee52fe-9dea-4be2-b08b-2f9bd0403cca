package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1003;

public class Cedb1003Dao extends BaseDaoJdbc implements RowMapper<Cedb1003> {

	public List<Cedb1003> findByPrefixNo(String prefixNo) {
		String sql = "SELECT * FROM Cedb1003 WHERE PREFIX_NO = ? ORDER BY APPROVE_OPINION_NO";
		Object[] parameters = {prefixNo};
		return (List<Cedb1003>) getJdbcTemplate().query(sql, parameters, this);
	}
	
	private static String sql_saveByObj = "INSERT INTO Cedb1003(BUSI_ITEM, APPROVE_OPINION_NO, APPROVE_OPINION) "
			+ "VALUES (?, ?, ?) ";
	
	public int insert(Cedb1003 Cedb1003) {
		if (Cedb1003 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);

		sqljob.addParameter(Cedb1003.getPrefixNo());
		sqljob.addParameter(Cedb1003.getApproveOpinionNo());
		sqljob.addParameter(Common.get(Cedb1003.getApproveOpinionNo()));

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	private int[] getSqlTypes() {
		return new int[] {
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}
	
	@Override
	public Cedb1003 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1003 obj = null;
		if(null!=rs) {
			obj = new Cedb1003();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApproveOpinionNo(rs.getString("APPROVE_OPINION_NO"));
			obj.setApproveOpinion(rs.getString("APPROVE_OPINION"));
		}
		return obj;
	}

}
