<%@ page import="com.kangdainfo.sys.common.Constants"%>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.util.TcfiView"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>

<%

response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	
	Gson gson = new GsonBuilder().create();
	String sql = "SELECT ITEM_CODE, RESERVE_365 FROM BUSI_ITEM WHERE RESERVE_365  IS NOT NULL";
	List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql);
	HashMap map = new HashMap();
	
	if (null != datas && !datas.isEmpty()) {
		for(Map<String,Object> data : datas) {
			map.put(data.get("ITEM_CODE"), data.get("RESERVE_365"));
		}
		out.write(gson.toJson(map));
	}

} catch (Exception e) {
	e.printStackTrace();
}
%>