package com.kangdainfo.common.model.bo;

import java.util.HashSet;
import java.util.Set;

public class CommonDtree implements java.io.Serializable {

	private static final long serialVersionUID = 6664568160394787120L;
	private Long id;
	private int sysid;
	private Long pid;
	private String name;
	private String url;
	private String title;
	private String target;
	private String icon;
	private String iconOpen;
	private String opened;
	private String btnRead;
	private String btnWrite;
	private Integer sorted;
	private Set<CommonAuth> commonAuths = new HashSet<CommonAuth>(0);

	public CommonDtree() {
	}

	public CommonDtree(Long id, int sysid) {
		this.id = id;
		this.sysid = sysid;
	}

	public CommonDtree(Long id, int sysid, Long pid, String name,
			String url, String title, String target, String icon,
			String iconOpen, String opened, String btnRead, String btnWrite,
			Integer sorted, Set<CommonAuth> commonAuths) {
		this.id = id;
		this.sysid = sysid;
		this.pid = pid;
		this.name = name;
		this.url = url;
		this.title = title;
		this.target = target;
		this.icon = icon;
		this.iconOpen = iconOpen;
		this.opened = opened;
		this.btnRead = btnRead;
		this.btnWrite = btnWrite;
		this.sorted = sorted;
		this.commonAuths = commonAuths;
	}

	public Long getId() {return this.id;}
	public void setId(Long id) {this.id = id;}

	public int getSysid() {return this.sysid;}
	public void setSysid(int sysid) {this.sysid = sysid;}

	public Long getPid() {return this.pid;}
	public void setPid(Long pid) {this.pid = pid;}

	public String getName() {return this.name;}
	public void setName(String name) {this.name = name;}

	public String getUrl() {return this.url;}
	public void setUrl(String url) {this.url = url;}

	public String getTitle() {return this.title;}
	public void setTitle(String title) {this.title = title;}

	public String getTarget() {return this.target;}
	public void setTarget(String target) {this.target = target;}

	public String getIcon() {return this.icon;}
	public void setIcon(String icon) {this.icon = icon;}

	public String getIconOpen() {return this.iconOpen;}
	public void setIconOpen(String iconOpen) {this.iconOpen = iconOpen;}

	public String getOpened() {return this.opened;}
	public void setOpened(String opened) {this.opened = opened;}

	public String getBtnRead() {return this.btnRead;}
	public void setBtnRead(String btnRead) {this.btnRead = btnRead;}

	public String getBtnWrite() {return this.btnWrite;}
	public void setBtnWrite(String btnWrite) {this.btnWrite = btnWrite;}

	public Integer getSorted() {return this.sorted;}
	public void setSorted(Integer sorted) {this.sorted = sorted;}

	public Set<CommonAuth> getCommonAuths() {return this.commonAuths;}
	public void setCommonAuths(Set<CommonAuth> commonAuths) {this.commonAuths = commonAuths;}

}