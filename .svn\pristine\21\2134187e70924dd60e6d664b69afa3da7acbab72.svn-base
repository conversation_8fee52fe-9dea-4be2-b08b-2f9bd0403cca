package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;

public class Cedb1000Dao extends BaseDaoJdbc implements RowMapper<Cedb1000> {

	public List<Cedb1000> findListWhenPRE2004(String prefixStart, String prefixEnd, String[] prefixList) {
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("SELECT * FROM CEDB1000 A");
		sqljob.appendSQL("WHERE (A.APPROVE_RESULT='Y' OR A.APPROVE_RESULT='N')");//已審查
		sqljob.appendSQL("AND A.APPROVE_DATE IS NOT NULL");//已審查
		sqljob.appendSQL("AND (CLOSE_DATE IS NULL OR CLOSE_DATE= '')");//未結案
		sqljob.appendSQL("AND PREFIX_STATUS NOT IN ('A','E')");//預查狀態不為(A)撤件、(E)撤回退費
		sqljob.appendSQL(" AND NOT EXISTS (SELECT PREFIX_NO FROM CEDB1010 B WHERE A.PREFIX_NO= B.PREFIX_NO AND PROCESS_STATUS='8') ");//未發文
		if(!"".equals(Common.get(prefixStart))) {
			sqljob.appendSQL("AND PREFIX_NO >= ?");
			sqljob.addParameter(prefixStart);
		}
		if(!"".equals(Common.get(prefixEnd))) {
			sqljob.appendSQL("AND PREFIX_NO <= ?");
			sqljob.addParameter(prefixEnd);
		}
		if(null!=prefixList && prefixList.length>0) {
			sqljob.appendSQL("AND PREFIX_NO IN (");
			for (int i=0; i<prefixList.length; i++) {
				if(i>0) sqljob.appendSQL(",");
				sqljob.appendSQL("?");
				sqljob.addParameter(prefixList[i]);
			}
			sqljob.appendSQL(")");
		}
		sqljob.appendSQL(" ORDER BY A.PREFIX_NO ");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public Cedb1000 findSmallestNotAssign() {
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL( "SELECT * FROM ( SELECT * FROM CEDB1000 WHERE ID_NO IS NULL AND ASSIGN_DATE IS NULL AND RCV_CHECK = 'Y' ORDER BY PREFIX_NO ) WHERE ROWNUM = 1" );
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1000 WHERE PREFIX_NO = ?";
	public Cedb1000 findByPrefixNo(String prefixNo) {
		return findByPrefixNo(prefixNo, null);
	}
	public Cedb1000 findByPrefixNo(String prefixNo, String approveResult) {
		if ("".equals(Common.get(prefixNo)))
			return null;
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	private static final String SQL_findByTelixNo = "SELECT * FROM CEDB1000 WHERE TELIX_NO = ?";
	public Cedb1000 findByTelixNo(String telixNo) {
		return findByTelixNo(telixNo, null);
	}
	public Cedb1000 findByTelixNo(String telixNo, String approveResult) {
		if ("".equals(Common.get(telixNo)))
			return null;
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	private static final String SQL_findByBanNo = "SELECT * FROM CEDB1000 WHERE BAN_NO = ?";
	public List<Cedb1000> findByBanNo(String banNo, String approveResult) {
		if ("".equals(Common.get(banNo)))
			return null;
		SQLJob sqljob = new SQLJob(SQL_findByBanNo);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNoForAtonce = "SELECT * FROM CEDB1000 WHERE PREFIX_NO = ? AND CLOSE_DATE IS NOT NULL";
	public Cedb1000 findReadByPrefixNoForAtonce(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoForAtonce);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}
	
	private static final String SQL_findByBanNoForAtonce = "SELECT * FROM CEDB1000 WHERE BAN_NO = ? AND CLOSE_DATE IS NOT NULL";
	public List<Cedb1000> findReadByBanNoForAtonce(String banNo) {
		SQLJob sqljob = new SQLJob(SQL_findByBanNoForAtonce);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_waitForCloseKeyin = "SELECT * FROM CEDB1000 t1 WHERE (((((t1.APPROVE_RESULT <> 'A') AND (TRIM(t1.APPROVE_Date) IS NOT NULL)) AND ((t1.CLOSE_DATE IS NULL) OR (t1.CLOSE_DATE = ''))) AND (t1.PREFIX_STATUS NOT IN ('A', 'E'))) AND NOT ((t1.PREFIX_NO IN (SELECT t0.PREFIX_NO FROM CEDB1010 t0 WHERE (t0.PROCESS_STATUS = '7')))))";
	/**
	 * 取得發文待登打資料
	 * @return
	 */
	public List<Cedb1000> goReadAllWaitForCloseKeyin() {
		SQLJob sqljob = new SQLJob(SQL_waitForCloseKeyin);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	/**
	 * 取得待預查審核案件，搜尋key值為 applyId SELECT * FROM CEDB1000 WHERE APPLY_ID =
	 * 'applyId' AND CLOSE_DATE IS NOT NULL
	 * 
	 * @param applyId
	 *            String 申請人身份証號
	 */
	private static final String SQL_findByApplyIdForAtonce = "SELECT * FROM CEDB1000 WHERE APPLY_ID = ? AND CLOSE_DATE IS NOT NULL";
	public List<Cedb1000> findReadByApplyIdForAtonce(String applyId) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyIdForAtonce);
		sqljob.addParameter(applyId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByApplyId = "SELECT * FROM CEDB1000 WHERE APPLY_ID = ?";
	public List<Cedb1000> findReadByApplyId(String applyId, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyId);
		sqljob.addParameter(applyId);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByApplyIdForWithdraw = "SELECT * FROM CEDB1000 a WHERE (APPLY_ID = ? or 0 < (select count(1) from cedb1001 where company_name = a.company_name and prefix_no = ? )) AND PREFIX_NO != ? AND APPROVE_RESULT = 'Y' AND RESERVE_DATE > ? AND (COMPANY_STUS IS NULL OR COMPANY_STUS != '03') ORDER BY PREFIX_NO ASC";
	public List<Cedb1000> findByApplyIdForWithdraw(String applyId, String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyIdForWithdraw);
		sqljob.addParameter(applyId);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(Datetime.getDateAdd("d",-90,Datetime.getYYYMMDD()));
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByApplyIdAndCompanyNameForWithdraw = "SELECT * FROM CEDB1000 a WHERE (APPLY_ID = ? or company_name = ?) AND PREFIX_NO != ? AND APPROVE_RESULT = 'Y' AND RESERVE_DATE > ? AND (COMPANY_STUS IS NULL OR COMPANY_STUS != '03') ORDER BY PREFIX_NO ASC";
	public List<Cedb1000> findByApplyIdAndCompanyNameForWithdraw(String applyId, String companyName, String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyIdAndCompanyNameForWithdraw);
		sqljob.addParameter(applyId);
		sqljob.addParameter(companyName);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(Datetime.getDateAdd("d",-90,Datetime.getYYYMMDD()));
		System.out.println(sqljob);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByApplyNameForAtonce = "SELECT * FROM CEDB1000 WHERE APPLY_NAME = ? AND CLOSE_DATE IS NOT NULL";
	public List<Cedb1000> findReadByApplyNameForAtonce(String applyName) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyNameForAtonce);
		sqljob.addParameter(applyName);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByCompanyNameForAtonce = "SELECT * FROM CEDB1000 WHERE COMPANY_NAME LIKE ? AND CLOSE_DATE IS NOT NULL";
	public List<Cedb1000> findReadByLikeCompanyNameForAtonce(String companyName) {
		SQLJob sqljob = new SQLJob(SQL_findByCompanyNameForAtonce);
		sqljob.addLikeParameter(companyName);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByApplyName = "SELECT * FROM CEDB1000 WHERE APPLY_NAME LIKE ? ";
	public List<Cedb1000> findByLikeApplyName(String applyName, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyName);
		sqljob.addLikeParameter(applyName);
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByCompanyName = "SELECT * FROM CEDB1000 WHERE COMPANY_NAME LIKE ? ";
	public List<Cedb1000> findByLikeCompanyName(String companyName, String approveResult) {
		SQLJob sqljob = new SQLJob(SQL_findByCompanyName);
		sqljob.addLikeParameter(companyName);
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public List<Cedb1000> findBySql(String sql) {
		SQLJob sqljob = new SQLJob(sql);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	/**
	 * sql 根據收文時間尋找確認收文資料
	 */
	private static final String SQL_SELECT_BY_RCVDATE_INTERVAL = 
			"SELECT * FROM CEDB1000 " 
			+ " WHERE ID_NO IS NOT NULL AND (CAST( RECEIVE_DATE AS NUMBER) BETWEEN ? AND ? ) AND "
			+ " (CAST( RECEIVE_TIME AS NUMBER) BETWEEN ? AND ?)";// 2024/03/27 新增SQL
	
	/**
	 * 根據收文時間尋找確認收文資料
	 * @param receiveDateStart
	 * @param receiveDateEnd
	 * @param receiveTimeStart
	 * @param receiveTimeEnd
	 * @return List<Cedb1000>
	 */
	public List<Cedb1000> selectCedb1000ByReceiveDateAndTimeInterval(String receiveDateStart, String receiveDateEnd, 
			String receiveTimeStart, String receiveTimeEnd) {// 2024/03/27 新增方法
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_RCVDATE_INTERVAL);
		sqljob.addParameter(receiveDateStart);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		sqljob.addParameter(receiveDateEnd);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		sqljob.addParameter(receiveTimeStart);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		sqljob.addParameter(receiveTimeEnd);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list;
	}
	
	/**
	 * sql 根據預查編號列表尋找確認收文資料
	 */
	private static final String SQL_SELECT_BY_PREFIXNOS = 
			"SELECT * FROM CEDB1000 "
			+ " WHERE AND ID_NO IS NOT NULL AND PREFIX_NO IN ";// 2024/04/07 新增SQL
	
	/**
	 * 根據預查編號列表尋找確認收文資料
	 * @param prefixNos
	 * @return List<Cedb1000>
	 */
	public List<Cedb1000> selectCedb1000ByPrefixNos(List<String> prefixNos) {// 2024/04/07 新增方法
		StringBuilder inClausePlaceholders = new StringBuilder("(");
		List<List<String>> prefixNosLists = partitionList(prefixNos, 1000);
		
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		inClausePlaceholders.append("?");
		        if (j < prefixNosLists.get(i).size() - 1) {
		            inClausePlaceholders.append(", ");
		        }
	    	}
	    	if (i == 0) {
	    		inClausePlaceholders.append(") or (ID_NO IS NOT NULL AND PREFIX_NO IN ( ");
	    	}else {
	    		inClausePlaceholders.append(")) or (ID_NO IS NOT NULL AND PREFIX_NO IN ( ");
	    	}
	    }
	    inClausePlaceholders.delete(inClausePlaceholders.length() - 42, inClausePlaceholders.length());
	    String sql = SQL_SELECT_BY_PREFIXNOS + inClausePlaceholders;
	    
	    SQLJob sqljob = new SQLJob(sql);
	    for (String prefix : prefixNos) {
	    	sqljob.addParameter(prefix);
	    } 
	    
	    for (int i = 0; i < prefixNos.size(); i++) {
	    	sqljob.addSqltypes(java.sql.Types.NVARCHAR);
	    }
	    
	    List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	    return list;
	}

	/** 收文確認 */
	public void updateRcvCheck(String prefixNo, String rcvCheck, String updateIdNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" RCV_CHECK=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(rcvCheck);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(updateIdNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public void resetCloseDate(String prefixNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" PREFIX_STATUS='5'");
		sqljob.appendSQL(",RESERVE_DATE=null");
		sqljob.appendSQL(",CLOSE_DATE=null");
		sqljob.appendSQL(",CLOSE_TIME=null");
		sqljob.appendSQL(",GET_DATE=null");
		sqljob.appendSQL(",GET_TIME=null");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public void updateReserveDate(String prefixNo, String reserveDate) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" RESERVE_DATE= ?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(reserveDate);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public void resetCloseDateAtPre2001(String prefixNo, String updateIdNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" PREFIX_STATUS='5'");
		sqljob.appendSQL(",RESERVE_DATE=null");
		sqljob.appendSQL(",RESERVE_DAYS=null");
		sqljob.appendSQL(",CLOSE_DATE=null");
		sqljob.appendSQL(",CLOSE_TIME=null");
		sqljob.appendSQL(",GET_DATE=null");
		sqljob.appendSQL(",GET_TIME=null");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(updateIdNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	private static String updateApproveResult = "UPDATE cedb1000 SET APPROVE_RESULT = ? WHERE prefix_no = ?";
	public void updateApproveResult(String approveResult, String prefixNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(updateApproveResult);
		sqljob.addParameter(approveResult);
		sqljob.addParameter(prefixNo);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	/** 分文 */
	public void updateAssignData(String prefixNo, String updateIdNo, String staffName) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" RCV_CHECK=''");  
		sqljob.appendSQL(",STAFF_NAME=?");
		sqljob.appendSQL(",ID_NO=?");
		sqljob.appendSQL(",ASSIGN_DATE=?");
		sqljob.appendSQL(",ASSIGN_TIME=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO=?");
		sqljob.addParameter(staffName);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(updateIdNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(updateIdNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	private static final String SQL_findLastPrefix = "SELECT * FROM CEDB1000 WHERE BAN_NO = ? AND approve_result ='Y' and prefix_no < ? ORDER BY PREFIX_NO DESC";
	public List<Cedb1000> getLastPrefix(String thisPrefixNo, String thisBanNo) {
		SQLJob sqljob = new SQLJob(SQL_findLastPrefix);
		sqljob.addParameter(thisBanNo);
		sqljob.addParameter(thisPrefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static String sql_insert =
			"INSERT INTO CEDB1000 (PREFIX_NO, APPLY_ADDR, APPLY_ID, APPLY_KIND, APPLY_NAME, " +
			"APPLY_TEL, APPLY_TYPE, APPROVE_DATE, APPROVE_MARK, APPROVE_RESULT, APPROVE_TIME, " +
			"ASSIGN_DATE, ASSIGN_TIME, ATTOR_ADDR, ATTOR_NAME, ATTOR_NO, ATTOR_TEL, BAN_NO, " +
			"CLOSE_DATE, CLOSE_TIME, CODE_NAME, CODE_NO, COMPANY_NAME, COMPANY_STUS, " +
			"CONTROL_CD1, CONTROL_CD2, GET_DATE, GET_KIND, GET_TIME, ID_NO, OLD_COMPANY_NAME, " +
			"PREFIX_STATUS, RECEIVE_DATE, RECEIVE_TIME, REG_DATE, REG_UNIT, REMARK, REMARK1, " +
			"RESERVE_DATE, RESERVE_DAYS, RESERVE_MARK, SPECIAL_NAME, STAFF_NAME, TELIX_NO, " +
			"UPDATE_CODE, UPDATE_DATE, UPDATE_ID_NO, UPDATE_TIME, WORK_DAY, ZONE_CODE, " +
			"APPROVE_REMARK, IS_PREFIX_FORM, PREFIX_FORM_NO, IS_OTHER_FORM, IS_SPEC, " +
			"GET_KIND_REMARK, IS_OTHER_SPEC, OTHER_SPEC_REMARK, DOC_TYPE, EXTEND_MARK, " +
			"RCV_CHECK, ATONCE_TYPE, REFUND_NO, OTHER_REASON, EXT_REMIT_ENAME) " +
			"VALUES (?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, " +
			"?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?, ?, ?, ?, ? , ?, " +
			"?, ?, ?, ?, ?, ?, ?, ?, ? , ?, ?, ?, ?, ?, ?) ";

	public int insert(Cedb1000 obj) {
		if (obj == null)
			return 0;
		SQLJob sqljob = new SQLJob(sql_insert);

		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addParameter(obj.getApplyAddr());
		sqljob.addParameter(obj.getApplyId());
		sqljob.addParameter(obj.getApplyKind());
		sqljob.addParameter(obj.getApplyName());
		sqljob.addParameter(obj.getApplyTel());
		sqljob.addParameter(obj.getApplyType());
		sqljob.addParameter(obj.getApproveDate());
		sqljob.addParameter(obj.getApproveMark());
		sqljob.addParameter(obj.getApproveResult());
		sqljob.addParameter(obj.getApproveTime());
		sqljob.addParameter(obj.getAssignDate());
		sqljob.addParameter(obj.getAssignTime());
		sqljob.addParameter(obj.getAttorAddr());
		sqljob.addParameter(obj.getAttorName());
		sqljob.addParameter(obj.getAttorNo());
		sqljob.addParameter(obj.getAttorTel());
		sqljob.addParameter(obj.getBanNo());
		sqljob.addParameter(obj.getCloseDate());
		sqljob.addParameter(obj.getCloseTime());
		sqljob.addParameter(obj.getCodeName());
		sqljob.addParameter(obj.getCodeNo());
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addParameter(obj.getCompanyStus());
		sqljob.addParameter(obj.getControlCd1());
		sqljob.addParameter(obj.getControlCd2());
		sqljob.addParameter(obj.getGetDate());
		sqljob.addParameter(obj.getGetKind());
		sqljob.addParameter(obj.getGetTime());
		sqljob.addParameter(obj.getIdNo());
		sqljob.addParameter(obj.getOldCompanyName());
		sqljob.addParameter(obj.getPrefixStatus());
		sqljob.addParameter(obj.getReceiveDate());
		sqljob.addParameter(obj.getReceiveTime());
		sqljob.addParameter(obj.getRegDate());
		sqljob.addParameter(obj.getRegUnit());
		sqljob.addParameter(obj.getRemark());
		sqljob.addParameter(obj.getRemark1());
		sqljob.addParameter(obj.getReserveDate());
		sqljob.addParameter(obj.getReserveDays());
		sqljob.addParameter(obj.getReserveMark());
		sqljob.addParameter(obj.getSpecialName());
		sqljob.addParameter(obj.getStaffName());
		sqljob.addParameter(obj.getTelixNo());
		sqljob.addParameter(obj.getUpdateCode());
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addParameter(obj.getWorkDay());
		sqljob.addParameter(obj.getZoneCode());
		sqljob.addParameter(obj.getApproveRemark());
		sqljob.addParameter(obj.getIsPrefixForm());
		sqljob.addParameter(obj.getPrefixFormNo());
		sqljob.addParameter(obj.getIsOtherForm());
		sqljob.addParameter(obj.getIsSpec());
		sqljob.addParameter(obj.getGetKindRemark());
		sqljob.addParameter(obj.getIsOtherSpec());
		sqljob.addParameter(obj.getOtherSpecRemark());
		sqljob.addParameter(obj.getDocType());
		sqljob.addParameter(obj.getExtendMark());
		sqljob.addParameter(obj.getRcvCheck());
		sqljob.addParameter(obj.getAtonceType());
		sqljob.addParameter(obj.getRefundNo());
		sqljob.addParameter(obj.getOtherReason());
		sqljob.addParameter(obj.getExtRemitEname());

		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}

	private static String sql_updateByObj =
			"UPDATE Cedb1000 " +
			"SET APPLY_ADDR=?, APPLY_ID=?, APPLY_KIND=?, APPLY_NAME=?, APPLY_TEL=?, " +
			"APPLY_TYPE=?, APPROVE_DATE=?, APPROVE_MARK=?, APPROVE_RESULT=?, APPROVE_TIME=?, " +
			"ASSIGN_DATE=?, ASSIGN_TIME=?, ATTOR_ADDR=?, ATTOR_NAME=?, ATTOR_NO=?, ATTOR_TEL=?, " +
			"BAN_NO=?, CLOSE_DATE=?, CLOSE_TIME=?, CODE_NAME=?, CODE_NO=?, COMPANY_NAME=?, " +
			"COMPANY_STUS=?, CONTROL_CD1=?, CONTROL_CD2=?, GET_DATE=?, GET_KIND=?, GET_TIME=?, " +
			"ID_NO=?, OLD_COMPANY_NAME=?, PREFIX_STATUS=?, RECEIVE_DATE=?, RECEIVE_TIME=?, " +
			"REG_DATE=?, REG_UNIT=?, REMARK=?, REMARK1=?, RESERVE_DATE=?, RESERVE_DAYS=?, " +
			"RESERVE_MARK=?, SPECIAL_NAME=?, STAFF_NAME=?, TELIX_NO=?, UPDATE_CODE=?, " +
			"UPDATE_DATE=?, UPDATE_ID_NO=?, UPDATE_TIME=?, WORK_DAY=?, ZONE_CODE=?, " +
			"APPROVE_REMARK=?, IS_PREFIX_FORM=?, PREFIX_FORM_NO=?, IS_OTHER_FORM=?, IS_SPEC=?, " +
			"GET_KIND_REMARK=?, IS_OTHER_SPEC=?, OTHER_SPEC_REMARK=?, DOC_TYPE=?, " +
			"EXTEND_MARK=?, RCV_CHECK=?, ATONCE_TYPE=?, REFUND_NO=?, Extend_Reason=?, " +
			"Extend_Other=?, OTHER_REASON=?, Extend_Date=?, EXT_REMIT_ENAME=? " +
			"WHERE PREFIX_NO=? ";

	public int update(Cedb1000 cedb1000) {
		if (cedb1000 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_updateByObj);

		sqljob.addParameter(cedb1000.getApplyAddr());
		sqljob.addParameter(cedb1000.getApplyId());
		sqljob.addParameter(cedb1000.getApplyKind());
		sqljob.addParameter(cedb1000.getApplyName());
		sqljob.addParameter(cedb1000.getApplyTel());
		sqljob.addParameter(cedb1000.getApplyType());
		sqljob.addParameter(cedb1000.getApproveDate());
		sqljob.addParameter(cedb1000.getApproveMark());
		sqljob.addParameter(cedb1000.getApproveResult());
		sqljob.addParameter(cedb1000.getApproveTime());
		sqljob.addParameter(cedb1000.getAssignDate());
		sqljob.addParameter(cedb1000.getAssignTime());
		sqljob.addParameter(cedb1000.getAttorAddr());
		sqljob.addParameter(cedb1000.getAttorName());
		sqljob.addParameter(cedb1000.getAttorNo());
		sqljob.addParameter(cedb1000.getAttorTel());
		sqljob.addParameter(cedb1000.getBanNo());
		sqljob.addParameter(cedb1000.getCloseDate());
		sqljob.addParameter(cedb1000.getCloseTime());
		sqljob.addParameter(cedb1000.getCodeName());
		sqljob.addParameter(cedb1000.getCodeNo());
		sqljob.addParameter(cedb1000.getCompanyName());
		sqljob.addParameter(cedb1000.getCompanyStus());
		sqljob.addParameter(cedb1000.getControlCd1());
		sqljob.addParameter(cedb1000.getControlCd2());
		sqljob.addParameter(cedb1000.getGetDate());
		sqljob.addParameter(cedb1000.getGetKind());
		sqljob.addParameter(cedb1000.getGetTime());
		sqljob.addParameter(cedb1000.getIdNo());
		sqljob.addParameter(cedb1000.getOldCompanyName());
		sqljob.addParameter(cedb1000.getPrefixStatus());
		sqljob.addParameter(cedb1000.getReceiveDate());
		sqljob.addParameter(cedb1000.getReceiveTime());
		sqljob.addParameter(cedb1000.getRegDate());
		sqljob.addParameter(cedb1000.getRegUnit());
		sqljob.addParameter(cedb1000.getRemark());
		sqljob.addParameter(cedb1000.getRemark1());
		sqljob.addParameter(cedb1000.getReserveDate());
		sqljob.addParameter(cedb1000.getReserveDays());
		sqljob.addParameter(cedb1000.getReserveMark());
		sqljob.addParameter(cedb1000.getSpecialName());
		sqljob.addParameter(cedb1000.getStaffName());
		sqljob.addParameter(cedb1000.getTelixNo());
		sqljob.addParameter(cedb1000.getUpdateCode());
		sqljob.addParameter(cedb1000.getUpdateDate());
		sqljob.addParameter(cedb1000.getUpdateIdNo());
		sqljob.addParameter(cedb1000.getUpdateTime());
		sqljob.addParameter(cedb1000.getWorkDay());
		sqljob.addParameter(cedb1000.getZoneCode());
		sqljob.addParameter(cedb1000.getApproveRemark());
		sqljob.addParameter(cedb1000.getIsPrefixForm());
		sqljob.addParameter(cedb1000.getPrefixFormNo());
		sqljob.addParameter(cedb1000.getIsOtherForm());
		sqljob.addParameter(cedb1000.getIsSpec());
		sqljob.addParameter(cedb1000.getGetKindRemark());
		sqljob.addParameter(cedb1000.getIsOtherSpec());
		sqljob.addParameter(cedb1000.getOtherSpecRemark());
		sqljob.addParameter(cedb1000.getDocType());
		sqljob.addParameter(cedb1000.getExtendMark());
		sqljob.addParameter(cedb1000.getRcvCheck());
		sqljob.addParameter(cedb1000.getAtonceType());
		sqljob.addParameter(cedb1000.getRefundNo());
		sqljob.addParameter(cedb1000.getExtendReason());
		sqljob.addParameter(cedb1000.getExtendOther());
		sqljob.addParameter(cedb1000.getOtherReason());
		sqljob.addParameter(cedb1000.getExtendDate());
		sqljob.addParameter(cedb1000.getExtRemitEname());
		
		sqljob.addParameter(cedb1000.getPrefixNo()); // primary key
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlUpdateTypes());
	}

	public int updateGetDateTime(Cedb1000 obj) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" GET_DATE=?");
		sqljob.appendSQL(",GET_TIME=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL(",REMARK1=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRemark1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int updateAppendix(String prefixNo, String isPrefixForm,
			String docType, String prefixFormNo, String isOtherForm,
			String isSpec, String isOtherSpec, String otherSpecRemark,
			String userId) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" IS_PREFIX_FORM=?");
		sqljob.appendSQL(",DOC_TYPE=?");
		sqljob.appendSQL(",PREFIX_FORM_NO=?");
		sqljob.appendSQL(",IS_OTHER_FORM=?");
		sqljob.appendSQL(",IS_SPEC=?");
		sqljob.appendSQL(",IS_OTHER_SPEC=?");
		sqljob.appendSQL(",OTHER_SPEC_REMARK=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(Common.get(isPrefixForm));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(docType));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(prefixFormNo));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(isOtherForm));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(isSpec));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(isOtherSpec));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(otherSpecRemark));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(Datetime.getYYYMMDD()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(Datetime.getHHMMSS()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(userId));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(prefixNo));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int updateExtend(String prefixNo, String extendMark, String extendReason, String extendOther, String extendDate, String userId) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" EXTEND_MARK=?");
		sqljob.appendSQL(",EXTEND_REASON=?");
		sqljob.appendSQL(",EXTEND_OTHER=?");
		sqljob.appendSQL(",EXTEND_DATE=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(Common.get(extendMark));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(extendReason));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(extendOther));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(extendDate));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Datetime.getHHMMSS());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(userId));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(prefixNo));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int setWhenPre2001(Cedb1000 obj) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" REMARK1=?");
		sqljob.appendSQL(",APPROVE_REMARK=?");
		sqljob.appendSQL(",PREFIX_STATUS=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getRemark1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int setWhenPre2003(Cedb1000 obj) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" GET_DATE=?");
		sqljob.appendSQL(",GET_TIME=?");
		sqljob.appendSQL(",GET_KIND=?");
		sqljob.appendSQL(",GET_KIND_REMARK=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKindRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int setWhenPre8005(Cedb1000 obj) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" STAFF_NAME=?");
		sqljob.appendSQL(",ID_NO=?");
		sqljob.appendSQL(",RCV_CHECK=?");
		sqljob.appendSQL(",ASSIGN_DATE=?");
		sqljob.appendSQL(",ASSIGN_TIME=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getStaffName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRcvCheck());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAssignDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getAssignTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int setWhenPre3005(Cedb1000 obj) {
		if ( obj == null ) {
			return 0;
		} // end if

		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE CEDB1000 SET");
		sqljob.appendSQL(" REFUND_NO=?");
		sqljob.appendSQL(",APPROVE_MARK=?");
		sqljob.appendSQL(",APPROVE_RESULT=?");
		sqljob.appendSQL(",REMARK=?");
		sqljob.appendSQL(",UPDATE_DATE=?");
		sqljob.appendSQL(",UPDATE_TIME=?");
		sqljob.appendSQL(",UPDATE_ID_NO=?");
		sqljob.appendSQL(",PREFIX_STATUS=?");
		sqljob.appendSQL(",COMPANY_NAME=?");
		sqljob.appendSQL(",REG_DATE=?");
		sqljob.appendSQL(",REG_UNIT=?");
		sqljob.appendSQL(",COMPANY_STUS=?");
		sqljob.appendSQL(",RESERVE_DATE=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getRefundNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRegDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRegUnit());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCompanyStus());		
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getReserveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	private int[] getSqlUpdateTypes() {
		return new int[] { java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.NUMERIC, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.NUMERIC,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR };
	}

	private int[] getSqlTypes() {
		return new int[] { java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.NUMERIC,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.NUMERIC, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR, java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR  };
	}

	/**
	 * 分文查詢用
	 */
	public Cedb1000 findForAssign() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDB1000 ");
		sqljob.appendSQL("WHERE RCV_CHECK = 'Y' ");//已收文確認
		sqljob.appendSQL("AND ID_NO IS NULL ");//尚未分文
		sqljob.appendSQL("AND CLOSE_DATE IS NULL ");
		sqljob.appendSQL("ORDER BY PREFIX_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1000> list = getJdbcTemplate().query(sqljob.getSQL(),this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}
	
	public String findPrefixNoForAssign() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select prefix_no from ( ");
		sqljob.appendSQL("SELECT prefix_no FROM CEDB1000 ");
		sqljob.appendSQL("WHERE RCV_CHECK = 'Y' ");//已收文確認
		sqljob.appendSQL("AND ID_NO IS NULL ");//尚未分文
		sqljob.appendSQL("AND CLOSE_DATE IS NULL ");
		sqljob.appendSQL("AND PREFIX_STATUS NOT IN ('A','E')");   // 排除撤件, 撤回退費案件
		sqljob.appendSQL("ORDER BY PREFIX_NO ");
		sqljob.appendSQL(") WHERE ROWNUM = 1");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Map<String,Object>> list = getJdbcTemplate().queryForList(sqljob.getSQL());
		if(null!=list && !list.isEmpty())
			return (String)list.get(0).get("prefix_no");
		return null;
	}

	private static final String findAllByApplyIdSql = "SELECT * FROM CEDB1000 WHERE APPLY_ID = ?";
	public List<Cedb1000> findAllByApplyId(String applyId) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(findAllByApplyIdSql);
		sqljob.addParameter(applyId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String findByIdnoAndAssignDateSql = "SELECT * FROM CEDB1000 WHERE ID_NO = ? AND ASSIGN_DATE = ?";
	public List<Cedb1000> findByIdnoAndAssignDate(String userId, String assignDate, String approveResult) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(findByIdnoAndAssignDateSql);
		sqljob.addParameter(userId);
		sqljob.addParameter(assignDate);
		
		if(approveResult != null) {
			sqljob.appendSQL(" AND APPROVE_RESULT IN ( " + approveResult + " )");
		}
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query	(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
	}

	public List<Cedb1000> doReadBetweenPrefixNo(String start, String end) {
		if (end.compareTo(start) < 0)
			return null;
		return getBetweenResult(start, end);
	}

	private List<Cedb1000> getBetweenResult(String start, String end) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDB1000 WHERE PREFIX_NO BETWEEN ? AND ?");
		sqljob.addParameter(start);
		sqljob.addParameter(end);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	/**
	 * 人工改分文(PRE8005)查詢用
	 */
	public List<Cedb1000> findAssignForPre8005(String searchType, String startNo, String endNo) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1000 ");
		sqljob.appendSQL("WHERE ID_NO IS NOT NULL ");	//已分文
		sqljob.appendSQL("AND ID_NO <> 'LL'" );			//不為"線上審核"案件
		sqljob.appendSQL("AND APPROVE_RESULT = 'A' ");	//審查中(A)
		if("prefix".equals(searchType))
			//預查編號查詢
			sqljob.appendSQL("AND prefix_no BETWEEN ? AND ? ");
		else if("receive".equals(searchType))
			//收文日期查詢
			sqljob.appendSQL("AND RECEIVE_DATE BETWEEN ? AND ? ");
		else if("assign".equals(searchType))
			//分文日期查詢
			sqljob.appendSQL("AND ASSIGN_DATE BETWEEN ? AND ? ");
		
		sqljob.addParameter(startNo);
		sqljob.addParameter(endNo);
		sqljob.appendSQL("ORDER BY PREFIX_NO");
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	//用於封裝作業, 其餘作業不可刪除案件
	public int deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1000 WHERE PREFIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	/**
	 * 查詢已分文件數
	 */
	public Integer doReadDistributedCountByDayForUser(String idNo)  {
		if("".equals(Common.get(idNo))) return 0;
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("SELECT COUNT(ID_NO) AS COUNT FROM CEDB1000");
		sqljob.appendSQL("WHERE ID_NO=?");
		sqljob.appendSQL("AND ASSIGN_DATE=?");
		sqljob.addParameter(idNo);
		sqljob.addParameter(Datetime.getYYYMMDD());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), Integer.class);
	}
	
	/**
	 * 判斷是否已分文，
	 * @param prefixNo
	 * @return true -> 未分文
	 */
	public boolean checkIsNotAssign(String prefixNo) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1000 WHERE PREFIX_NO = ? AND RCV_CHECK != 'Y' AND ID_NO IS NULL");
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this).isEmpty();
	}
	

	public List<Cedb1000> findForCorrectReserveDate() {
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("SELECT * FROM CEDB1000 A");
		sqljob.appendSQL("WHERE A.PREFIX_NO IN (");
		sqljob.appendSQL("  SELECT PREFIX_NO FROM (");
		sqljob.appendSQL("    SELECT PREFIX_NO, COUNT(1) CNT FROM ATONCE_RECORD");
		sqljob.appendSQL("    WHERE ATONCE_DATE=to_char(to_char(sysdate,'yyyyMMdd')-19110000)");
		sqljob.appendSQL("    AND ATONCE_ITEM='延期'");
		sqljob.appendSQL("    GROUP BY prefix_no");
		sqljob.appendSQL("  ) T");
		sqljob.appendSQL("  WHERE T.CNT > 1");
		sqljob.appendSQL(")");
		sqljob.appendSQL("AND CLOSE_DATE IS NOT NULL");
		sqljob.appendSQL("AND RESERVE_DATE > to_char(to_char(ADD_MONTHS(to_date(CLOSE_DATE+19110000,'yyyyMMdd'), decode(reserve_days,180,7,13) )-1,'yyyyMMdd')-19110000)");
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public String mapRow2(ResultSet rs, int idx) throws SQLException {
		String prefix_no = "";
		if (rs != null) {
			prefix_no = rs.getString("PREFIX_NO");
		}
		return prefix_no;
	}

	@Override
	public Cedb1000 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1000 obj = null;
		if (null != rs) {
			obj = new Cedb1000();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApplyAddr(rs.getString("APPLY_ADDR"));
			obj.setApplyId(rs.getString("APPLY_ID"));
			obj.setApplyKind(rs.getString("APPLY_KIND"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setApplyTel(rs.getString("APPLY_TEL"));
			obj.setApplyType(rs.getString("APPLY_TYPE"));
			obj.setApproveDate(rs.getString("APPROVE_DATE"));
			obj.setApproveMark(rs.getString("APPROVE_MARK"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setApproveTime(rs.getString("APPROVE_TIME"));
			obj.setAssignDate(rs.getString("ASSIGN_DATE"));
			obj.setAssignTime(rs.getString("ASSIGN_TIME"));
			obj.setAttorAddr(rs.getString("ATTOR_ADDR"));
			obj.setAttorName(rs.getString("ATTOR_NAME"));
			obj.setAttorNo(rs.getString("ATTOR_NO"));
			obj.setAttorTel(rs.getString("ATTOR_TEL"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setCloseDate(rs.getString("CLOSE_DATE"));
			obj.setCloseTime(rs.getString("CLOSE_TIME"));
			obj.setCodeName(rs.getString("CODE_NAME"));
			obj.setCodeNo(rs.getString("CODE_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setCompanyStus(rs.getString("COMPANY_STUS"));
			obj.setControlCd1(rs.getString("CONTROL_CD1"));
			obj.setControlCd2(rs.getString("CONTROL_CD2"));
			obj.setGetDate(rs.getString("GET_DATE"));
			obj.setGetKind(rs.getString("GET_KIND"));
			obj.setGetTime(rs.getString("GET_TIME"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setOldCompanyName(rs.getString("OLD_COMPANY_NAME"));
			obj.setPrefixStatus(rs.getString("PREFIX_STATUS"));
			obj.setReceiveDate(rs.getString("RECEIVE_DATE"));
			obj.setReceiveTime(rs.getString("RECEIVE_TIME"));
			obj.setRegDate(rs.getString("REG_DATE"));
			obj.setRegUnit(rs.getString("REG_UNIT"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setRemark1(rs.getString("REMARK1"));
			obj.setReserveDate(rs.getString("RESERVE_DATE"));
			obj.setReserveDays(rs.getInt("RESERVE_DAYS"));
			obj.setReserveMark(rs.getString("RESERVE_MARK"));
			obj.setSpecialName(rs.getString("SPECIAL_NAME"));
			obj.setStaffName(rs.getString("STAFF_NAME"));
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setUpdateCode(rs.getString("UPDATE_CODE"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setWorkDay(rs.getFloat("WORK_DAY"));
			obj.setZoneCode(rs.getString("ZONE_CODE"));
			obj.setApproveRemark(rs.getString("APPROVE_REMARK"));
			obj.setIsPrefixForm(rs.getString("IS_PREFIX_FORM"));
			obj.setPrefixFormNo(rs.getString("PREFIX_FORM_NO"));
			obj.setIsOtherForm(rs.getString("IS_OTHER_FORM"));
			obj.setIsSpec(rs.getString("IS_SPEC"));
			obj.setGetKindRemark(rs.getString("GET_KIND_REMARK"));
			obj.setIsOtherSpec(rs.getString("IS_OTHER_SPEC"));
			obj.setOtherSpecRemark(rs.getString("OTHER_SPEC_REMARK"));
			obj.setDocType(rs.getString("DOC_TYPE"));
			obj.setExtendMark(rs.getString("EXTEND_MARK"));
			obj.setRcvCheck(rs.getString("RCV_CHECK"));
			obj.setAtonceType(rs.getString("ATONCE_TYPE"));

			obj.setRefundNo(rs.getString("REFUND_NO"));
			obj.setExtendReason(rs.getString("EXTEND_REASON"));
			obj.setExtendOther(rs.getString("EXTEND_OTHER"));
			obj.setExtendDate(rs.getString("EXTEND_DATE"));
			obj.setOtherReason(rs.getString("OTHER_REASON"));
			obj.setExtRemitEname(rs.getString("EXT_REMIT_ENAME"));
		}
		return obj;
	}

	/**
	 * 2024/04/09 新增，針對oracle 使用in時不能超過1000個使用
	 * @param list
	 * @param partitionSize
	 * @return List<List<T>>
	 */
	public static <T> List<List<T>> partitionList(List<T> list, int partitionSize) {
	    List<List<T>> partitions = new ArrayList<>();
	    for (int i = 0; i < list.size(); i += partitionSize) {
	        partitions.add(list.subList(i, Math.min(i + partitionSize, list.size())));
	    }
	    return partitions;
	}
}