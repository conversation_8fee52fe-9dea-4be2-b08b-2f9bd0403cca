package com.kangdainfo.common.util;

import java.util.*;
import java.text.*;

public class DateTimeFormatter {
  public static SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");

  public static void main(String args[]) {
//    System.out.println(getTwDateByDay("0950228", 15));
    System.out.println(getSystemDate());
  }

  /**
   * 取得前 n 天的日期 yyyyMMdd
   * -1 昨天, 3 三天後

   *
   * @param monthDif int
   * @return String
   */
  public static String getDateStrByDay(int dayDif) {
      Calendar cal = GregorianCalendar.getInstance();
      cal.add(Calendar.DATE, dayDif); // 針對月份增減
      return yyyyMMdd.format(cal.getTime());
  }

  /**
   * 輸入一個民國年日期，和你想要差幾天的整數

   * 回你一個民國年日期..EX: 輸入0950125, 3 輸出0950128
   *
   * @param twDate String
   * @param diff int
   * @return String
   */
  public static String getTwDateByDay(String twDate, int diff) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(changStrToDate(twDate));
    cal.add(Calendar.DATE, diff);
    return west2TwDate(yyyyMMdd.format(cal.getTime()));
  }

  /**
   * 西元日期轉民國日期

   *
   * @param date String
   * @return String
   */
  public static String west2TwDate(String date) {
      if (date == null || date.length() < 8) { // yyyyMMdd 至少八碼
          return date;
      }
      int i = Integer.parseInt(date.substring(0, 4)) - 1911;
      return (i < 100) ? ("0" + i + date.substring(4, 8)) : (i + date.substring(4, 8));
  }


  /**
   * 將傳入的Calendar的時間以 HHmmss 的格式回傳,24小時制

   * eg. 傳入時間為下午四點三分五十秒,回傳160350
   * @param calender GregorianCalendar 時間的來源

   * @return String 以HHmmss表示時間的字串

   */
  public static String toTimeString(java.util.GregorianCalendar calender) {
    SimpleDateFormat df = new SimpleDateFormat("HHmmss");
    return df.format(calender.getTime());
  }

  /**
   * 將傳入的Calendar的日期以 民國年月日 的格式回傳,民國年為三碼
   * eg. 傳入日期為2004.06.02,回傳0930602
   * @param calender GregorianCalendar 時間的來源

   * @return String 以七碼表示民國年月日的字串

   */
  public static String toDateString(java.util.GregorianCalendar calender) {
//    GregorianCalendar calenderClone = (GregorianCalendar) calender.clone();
//    int year = calenderClone.get(Calendar.YEAR);
//
//    year = year - 1911;
//    calenderClone.set(Calendar.YEAR, year);
//    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calenderClone.getTime()));
//    sb = sb.deleteCharAt(0);
//    return sb.toString();
      // 因為潤年的關係，必須這樣搭配（修改日期：2008.02.29，因處理潤年問題，線上申辦會員版同樣有錯，一起處理）.
      // 因為有些地方是帶Calendar進來的，不可以直接取今天的Calendar.
//      Calendar cal = GregorianCalendar.getInstance();
      Calendar cal = (GregorianCalendar) calender.clone();
      String westDate = yyyyMMdd.format(cal.getTime());
      cal.add(Calendar.YEAR, -1911);  // 民國年減1911
      String twDate = yyyyMMdd.format(cal.getTime());
      return twDate.substring(1, 4) + westDate.substring(4, 8);

  }

  /**
   * 取得一年前的日期,以七碼民國年月日表示
   * eg. 系統日期為2004.06.02,回傳0920602
   * @return String 以七碼民國年月日表示系統日期
   */
  public static String getDateYearAgo() {
    GregorianCalendar calender = new java.util.GregorianCalendar();
    GregorianCalendar calenderClone = (GregorianCalendar) calender.clone();

    int year = calenderClone.get(Calendar.YEAR);
    year = year - 1912;
    calenderClone.set(Calendar.YEAR, year);
    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calenderClone.getTime()));
    sb = sb.deleteCharAt(0);
    return sb.toString();
  }

  /**
   * 取得半年前的日期,以七碼民國年月日表示
   * eg. 系統日期為0940701,回傳0940102
   * @return String 以七碼民國年月日表示系統日期
   */
  public static String getDateHalfYearAgo() {
    Calendar calendar = Calendar.getInstance();
    int year = calendar.get(Calendar.YEAR);
    year -= 1911;
    calendar.set(Calendar.YEAR, year);
    calendar.add(Calendar.MONTH, -6);
    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calendar.getTime()));
    sb = sb.deleteCharAt(0);
    return sb.toString();
  }

  /**
   * 取得三個月前的日期,以七碼民國年月日表示
   * eg. 系統日期為0940701,回傳0940102
   * @return String 以七碼民國年月日表示系統日期
   */
  public static String getDate3MonthAgo() {
    Calendar calendar = Calendar.getInstance();
    int year = calendar.get(Calendar.YEAR);
    year -= 1911;
    calendar.set(Calendar.YEAR, year);
    calendar.add(Calendar.MONTH, -3);
    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calendar.getTime()));
    sb = sb.deleteCharAt(0);
    return sb.toString();
  }

  /**
   * 取得系統日期,以七碼民國年月日表示
   * eg. 系統日期為2004.06.02,回傳0930602
   * @return String 以七碼民國年月日表示系統日期
   */
  public static String getSystemDate1() {
    return toDateString(new java.util.GregorianCalendar());
  }
  
	public static String getSystemDate() {
		Calendar ca = new java.util.GregorianCalendar();
		int year = ca.get(Calendar.YEAR);
		int month = ca.get(Calendar.MONTH);
		int day = ca.get(Calendar.DATE);
		if (year <= 1911) {// 重新抓當年
			System.out.println("Date Error：" + year + "-"
					+ (month + 1) + "-" + day);
			year = Calendar.getInstance().get(Calendar.YEAR);
		}
		return (year - 1911) + (month < 9 ? "0" : "") + (month + 1)
				+ (day < 10 ? "0" : "") + day;
	}

  /**
   * 取得系統時間,以 HHmmss 的格式回傳,24小時制

   * eg. 系統時間為下午四點三分五十秒,回傳160350
   * @return String 以HHmmss表示時間的字串

   */
  public static String getSystemTime() {
    return toTimeString(new java.util.GregorianCalendar());
  }

  /**
   * 計算兩個日期相差的天數
   * @param startdate Date
   * @param enddate Date
   * @return long
   */
  public static long getDateExpression(Date startdate, Date enddate) {
    long removedate = (enddate.getTime() - startdate.getTime()) / 86400000;
    return removedate;
  }

  /**
   * 取得今年字串.
   *
   * @return String
   */
  public static String getSystemYear() {
    return DateTimeFormatter.getSystemDate().substring(0, 3);
  }

  /**
   * 取得去年字串.
   *
   * @return String
   */
  public static String getLastYear() {
    Calendar calendar = Calendar.getInstance();
    int year = calendar.get(Calendar.YEAR);
    year -= 1912;
    calendar.set(Calendar.YEAR, year);
    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calendar.getTime()));
    sb = sb.deleteCharAt(0);
    return sb.toString().substring(0, 3);
  }

  /**
   * 取得前年字串.
   *
   * @return String
   */
  public static String getLast2Year() {
    Calendar calendar = Calendar.getInstance();
    int year = calendar.get(Calendar.YEAR);
    year -= 1913;
    calendar.set(Calendar.YEAR, year);
    StringBuffer sb = new StringBuffer(yyyyMMdd.format(calendar.getTime()));
    sb = sb.deleteCharAt(0);
    return sb.toString().substring(0, 3);
  }

  /**
   * 將日期字串轉換成Date
   * 日期的格式為-->  0930101
   * @param date String
   * @return Date
   */
  public static java.util.Date changStrToDate(String date) {
    int year = Integer.parseInt(date.substring(0, 3)) + 1911;
    String mon = date.substring(3, 5);
    String day = date.substring(5, 7);
    java.util.Date rdate = java.sql.Date.valueOf(String.valueOf(year) + "-" +
                                                 mon + "-" + day);
    return rdate;
  }

  /**
   * 輸入日期(java.util.Date)回傳星期編號
   * 列如:星期一的編號為 "2"
   * @param sdate Date
   * @return int
   */
  public static int getWeekOfDay(Date sdate) {
    Calendar calNow = Calendar.getInstance();
    calNow.setTime(sdate);
    int weekday = calNow.get(Calendar.DAY_OF_WEEK);
    return weekday;
  }

  /**
   * 輸入日期(java.util.Date)回傳星期字串
   * 列如:星期一的字串為 "Mon"
   * @param sdate Date
   * @return String
   */
  public static String getStrWeekOfDay(Date sdate) {
    Calendar calNow = Calendar.getInstance();
    calNow.setTime(sdate);
    int weekday = calNow.get(Calendar.DAY_OF_WEEK);

    switch (weekday) {
      case 1:
        return "Sun";
      case 2:
        return "Mon";
      case 3:
        return "Tue";
      case 4:
        return "Wed";
      case 5:
        return "Thu";
      case 6:
        return "Fri";
      case 7:
        return "Sat";
      default:
        return "";
    }
  }

  public static int getYearDiff(Date eDate,Date sDate)
  {
	  Calendar sCalendar = Calendar.getInstance();
	  sCalendar.clear();
	  sCalendar.setTime(sDate);
	  Calendar eCalendar = Calendar.getInstance();
	  eCalendar.setTime(eDate);
	  
	  Calendar clone = (Calendar) sCalendar.clone(); // Otherwise changes are been reflected.
	  int years = -1;
	  while (!clone.after(eCalendar)) {
	      clone.add(Calendar.YEAR, 1);
	      years++;
	  }
	  return years;
  }
  
  public static String conToWesternDate(String date) {
		if (date == null || date.length() == 0)
			return null;

		try {
			int i = Integer.parseInt(date.substring(0, 3)) + 1911;
			return (i + date.substring(3));
		} catch (Exception ex) {
			return "error";
		}
  }
  
  /**
   * 根據系統轉換中華民國區域時間
   * @param YYYMMdd
   * @return String
   * 2024/05/17
   */
  public static String ROCTaiwanDateFormatter(String YYYMMdd, boolean addROC) {
	  StringBuffer sb;
	  if(addROC == true) {
		  sb = new StringBuffer("中華民國"); 
	  }else {
		  sb = new StringBuffer();
	  }
	  
	  if (YYYMMdd.length() == 7) {
		  String year = YYYMMdd.substring(0,3);
		  String month = YYYMMdd.substring(3,5);
		  String day = YYYMMdd.substring(5,7);
		 
		  sb.append(year);
		  sb.append("年");
		  sb.append(month);
		  sb.append("月");
		  sb.append(day);
		  sb.append("日");
	  }else {
		  sb.append("  年");
		  sb.append("  月");
		  sb.append("  日");
	  }
	  
	  return sb.toString();
  }
}
