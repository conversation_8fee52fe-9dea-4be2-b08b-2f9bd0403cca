--DROP TABLE EICM.BUSI_ITEM;
-- Create table
CREATE TABLE EICM.BUSI_ITEM (
	ID NUMBER(15) not null,
	ITEM_CODE VARCHAR2(7),
	BUSINESS_ITEM VARCHAR2(160),
	MASTER_CODE VARCHAR2(11),
	MOEA_POST_DATE VARCHAR2(7),
	MOEA_POST_NO NVARCHAR2(50),
	RESERVE_365 VARCHAR2(2),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.BUSI_ITEM is '營業項目代碼檔';
-- Add comments to the columns 
comment on column EICM.BUSI_ITEM.ID is '主鍵值';
comment on column EICM.BUSI_ITEM.ITEM_CODE is '營業項目代碼';
comment on column EICM.BUSI_ITEM.BUSINESS_ITEM is '營業項目';
comment on column EICM.BUSI_ITEM.MASTER_CODE is '特許事業中央主管機代碼';
comment on column EICM.BUSI_ITEM.MOEA_POST_DATE is '經濟部公告函日期';
comment on column EICM.BUSI_ITEM.MOEA_POST_NO is '經濟部公告函文號';
comment on column EICM.BUSI_ITEM.RESERVE_365 is '保留期限一年註記(Y:保留一年)';
comment on column EICM.BUSI_ITEM.MOD_ID_NO is '異動人員';
comment on column EICM.BUSI_ITEM.MOD_DATE is '異動日期';
comment on column EICM.BUSI_ITEM.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.BUSI_ITEM
  add constraint PK_BUSI_ITEM primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_BUSI_ITEM_ID;
-- Create sequence 
create sequence EICM.SEQ_BUSI_ITEM_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
set define off;
Create Or Replace Trigger EICM.TG_BUSI_ITEM
Before Insert ON EICM.BUSI_ITEM Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_BUSI_ITEM_ID.NextVal into :nu.id From Dual;
End;
/

-- SYNONYM 
create or replace synonym EICM4AP.BUSI_ITEM for EICM.BUSI_ITEM;
create or replace synonym EICM4CMPY.BUSI_ITEM for EICM.BUSI_ITEM;
create or replace synonym EICM4PREFIX.BUSI_ITEM for EICM.BUSI_ITEM;

--GRANT
grant all on EICM.BUSI_ITEM to EICM4AP;
grant all on EICM.BUSI_ITEM to EICM4CMPY;
grant all on EICM.BUSI_ITEM to EICM4PREFIX;
