package com.kangdainfo.tcfi.view.pre;

/*
程式目的：已審核未結案清單
程式代號：pre4018
撰寫日期：103.06.23
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.util.report.JasperReportMaker;


public class PRE4018 extends SuperBean {	

	private String q_dateStart;		// 核覆日期起
	private String q_dateEnd;		// 核覆日期迄

	private String prefixNo;		// 預查編號
	private String companyName;		// 公司名稱
	private String changeTypeName;	// 預查種類
	private String applyName;		// 申請人
	private String assignDateTime;	// 分文日期時間
	private String staffName;		// 承辦人姓名
	private String extendReason;	// 展期原因
	private String extendMark;
	private String assignDate;		// 分文日期
	private String assignTime;		// 分文時間
	private String approveDate;		// 審核日期
	private String approveTime;		// 審核時間
	
	private String workDay;
	private String workHour;

	// getters and setters of local variable bellow 
	public String getQ_dateStart() {return checkGet(q_dateStart);}
	public void setQ_dateStart(String s) {q_dateStart = checkSet(s);}
	public String getQ_dateEnd() {return checkGet(q_dateEnd);}
	public void setQ_dateEnd(String s) {q_dateEnd = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getStaffName() {return checkGet(staffName);}
	public void setStaffName(String s) {staffName = checkSet(s);}
	public String getChangeTypeName() {return checkGet(changeTypeName);}
	public void setChangeTypeName(String s) {this.changeTypeName = checkSet(s);}
	public String getAssignDateTime() {return checkGet(assignDateTime);}
	public void setAssignDateTime(String s) {this.assignDateTime = checkSet(s);}
	public String getExtendReason() {return checkGet(extendReason);}
	public void setExtendReason(String s) {this.extendReason = checkSet(s);}
	public String getExtendMark() {return checkGet(extendMark);}
	public void setExtendMark(String s) {this.extendMark = checkSet(s);}
	public String getAssignDate() {return checkGet(assignDate);}
	public void setAssignDate(String s) {this.assignDate = checkSet(s);}
	public String getAssignTime() {return checkGet(assignTime);}
	public void setAssignTime(String s) {this.assignTime = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {this.approveDate = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {this.approveTime = checkSet(s);}
	
	public String getWorkDay() {return checkGet(workDay);}
	public void setWorkDay(String s) {this.workDay = checkSet(s);}
	
	public String getWorkHour() {return checkGet(workHour);}
	public void setWorkHour(String s) {this.workHour = checkSet(s);}
	
	// -----------------------------------------------------------------------

  // ----------------------------------function never used bellow----------------------------------------------
	public SQLJob doAppendSqljob() {
		SQLJob sqljob = new SQLJob();
		
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.COMPANY_NAME");
		sqljob.appendSQL(",nvl(c13.code_name,decode(a.apply_kind,'1','設立','2','變更','')) as CHANGE_TYPE_NAME");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(",(case when nvl(a.assign_date,' ') = ' ' then a.assign_date");
		sqljob.appendSQL("       when nvl(a.assign_time,' ') = ' ' then a.assign_time");
		sqljob.appendSQL("       else substr(a.assign_date,1,3)||'/'||substr(a.assign_date,4,2)||'/'||substr(a.assign_date,6,2)||'  '||substr(a.assign_time,1,2)||':'||substr(a.assign_time,3,2)||':'||substr(a.assign_time,5,2)");
		sqljob.appendSQL("       end");
		sqljob.appendSQL(") AS ASSIGN_DATE_TIME");
		sqljob.appendSQL(",A.STAFF_NAME");
		sqljob.appendSQL(",A.ASSIGN_DATE");
		sqljob.appendSQL(",A.ASSIGN_TIME");
		sqljob.appendSQL(",A.APPROVE_DATE");
		sqljob.appendSQL(",A.APPROVE_TIME");
		sqljob.appendSQL(",A.extend_mark");
		sqljob.appendSQL(",decode(A.EXTEND_MARK,'Y',(decode(a.extend_reason,'90',a.extend_other,c14.code_name)),'') AS EXTEND_REASON");
		sqljob.appendSQL(" FROM CEDB1000 a");
		sqljob.appendSQL(" left outer join cedb1023 b on a.prefix_no = b.prefix_no" );
		sqljob.appendSQL(" left outer join system_code c13 on c13.code_kind='13' and c13.code=b.change_type");
		sqljob.appendSQL(" left outer join system_code c14 on c14.code_kind='14' and c14.code=a.extend_reason");
		//sqljob.appendSQL(" WHERE ( A.APPROVE_RESULT = 'Y' or A.APPROVE_RESULT = 'N' )");//已審查
		//sqljob.appendSQL(" and A.APPROVE_DATE is not null" );//同上
		//sqljob.appendSQL(" AND A.close_date is null" );  // 未結案
		sqljob.appendSQL(" WHERE A.APPROVE_RESULT = 'A'" );   // --審查中
	    sqljob.appendSQL(" AND A.ASSIGN_DATE IS NOT NULL" );  // --已分文
	    sqljob.appendSQL(" AND A.CLOSE_DATE IS NULL" ); 
		sqljob.appendSQL(" AND a.assign_date between ? and  ?" );
		sqljob.appendSQL(" ORDER BY PREFIX_NO ");  
		sqljob.addParameter(getQ_dateStart());
		sqljob.addParameter(getQ_dateEnd());
		return sqljob;
	} // doAppendSqljob()

	@SuppressWarnings("unchecked")
	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>();
		try {
			List<PRE4018> datas = (List<PRE4018>) ServiceGetter.getInstance().getEicmGeneralQueryDao().query(doAppendSqljob(), BeanPropertyRowMapper.newInstance(PRE4018.class));
			if ( null==datas || datas.isEmpty() ) {
				setErrorMsg("查無資料，請變更查詢日期");
				throw new MoeaException( "查無資料，請變更查詢日期" ) ;
			} // end if
			else {
				String[] rowArray = new String[9] ;
				Float approveWorkDay = 0f;
				Float workDay = 0f;
				for(PRE4018 data : datas) {
					
					workDay = ServiceGetter.getInstance().getCaseFlowService().countWorkDay(data.getAssignDate(),data.getAssignTime(),data.getApproveDate(),data.getApproveTime());

					rowArray = new String[9];
					rowArray[0] = Common.get(data.getPrefixNo());
					rowArray[1] = Common.get(data.getCompanyName());
					rowArray[2] = Common.get(data.getChangeTypeName());
					rowArray[3] = Common.get(data.getApplyName());
					rowArray[4] = (new DecimalFormat("0").format(workDay))+"天";
					rowArray[5] = Common.get(data.getAssignDateTime());
					rowArray[6] = Common.get(data.getStaffName());
					rowArray[7] = Common.get(data.getExtendReason());
					if ( "Y".equals(Common.get(data.getExtendMark())) ) {
				    	rowArray[8] = "0.64";
				    } // if
				    else if ( !"".equals(Common.get(data.getApproveDate())) && !"".equals(Common.get(data.getApproveTime())) ) {
				        approveWorkDay = ServiceGetter.getInstance().getCaseFlowService().countWorkDay(data.getAssignDate(),data.getAssignTime(),data.getApproveDate(),data.getApproveTime());
				        rowArray[8] = (new DecimalFormat("0.0").format(approveWorkDay*8));
				    } // else if
				    else {
				        rowArray[8] = (new DecimalFormat("0.0").format(workDay*8));
				    } // else
					dataList.add(rowArray);
				}
			} // end else 
		} // try
		catch( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
		} // catch
		return dataList;
	} // doQueryAll()
	
	public String DateFormat( String inputDate ) {
		  String tempDate = "" ;
		  String year = inputDate.substring(0, 3) ;
		  String month = inputDate.substring(3, 5) ;
		  String day = inputDate.substring(5) ;
		  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
		  return tempDate ;
	  } // DateFormat()
	  
	  public String TimeFormat( String inputTime ) {
		  String tempTime = "" ;
		  String hour = inputTime.substring(0, 2) ;
		  String minute = inputTime.substring(2, 4) ;
		  String second = inputTime.substring(4) ;
		  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
		  return tempTime ;
	  } // TimeFormat()
	
	public File doPrintPdf() throws Exception {
		File report = null ;  
		try {       
		  if ( "".equals( getQ_dateEnd() ) ) 
			  setQ_dateEnd( getQ_dateStart() ) ;     // 若只輸入起始日則查詢當天的資料
		
	          String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4018.jasper");
	      
	          Map<String, Object> parameters = new HashMap<String,Object>();

	          String printDate = Datetime.getYYYMMDD() ;//列印時間年月日
	          String printTime = Datetime.getHHMMSS() ;    // 列印時間時分秒                                                    
	       
	          printDate = Datetime.formatRocDate( printDate ) ;
	          printTime = Datetime.formatRocTime( printTime ) ;
	      
	          parameters.put("printDate", printDate);//列印時間
		      parameters.put("printTime", printTime);//列印時間
		  /*
		      String year = getQ_dateStart().substring(0, 3) ;
		      String month = getQ_dateStart().substring(3, 5) ;
		      String day = getQ_dateStart().substring(5) ;
		      String yearE = getQ_dateEnd().substring(0, 3) ;
		      String monthE = getQ_dateEnd().substring(3, 5) ;
		      String dayE = getQ_dateEnd().substring(5) ;
		  */
		      parameters.put("dateStart", DateFormat(getQ_dateStart()) )  ;
		      parameters.put("dateEnd", DateFormat(getQ_dateEnd())  ) ;
		  	  System.out.println();
		      
	   	      if(logger.isInfoEnabled()) logger.info(doAppendSqljob());	
	   	      List<?> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(doAppendSqljob(), BeanPropertyRowMapper.newInstance(PRE4018.class));
	   	      ArrayList<PRE4018> dataList = new ArrayList<PRE4018>();
	   	      if ( rs == null || rs.size() <= 0 ) {
	   	    	  PRE4018 data = new PRE4018();
	   	    	  dataList.add(data);
	   	      } // if
	   	      else {
		        Float approveWorkDay = 0f;
				Float workDay = 0f;
				for(Object d4018 : rs) {
					PRE4018 data = (PRE4018)d4018;
		        	workDay = ServiceGetter.getInstance().getCaseFlowService().countWorkDay(data.getAssignDate(),data.getAssignTime(),data.getApproveDate(),data.getApproveTime());
		        	data.setWorkDay((new DecimalFormat("0").format(workDay))+"天");
					if ( "Y".equals(Common.get(data.getExtendMark())) ) {
				    	data.setWorkHour("0.64");
				    } // if
				    else if ( !"".equals(Common.get(data.getApproveDate())) && !"".equals(Common.get(data.getApproveTime())) ) {
				        approveWorkDay = ServiceGetter.getInstance().getCaseFlowService().countWorkDay(data.getAssignDate(),data.getAssignTime(),data.getApproveDate(),data.getApproveTime());
				        data.setWorkHour(new DecimalFormat("0.0").format(approveWorkDay*8));
				    } // else if
				    else {
				        data.setWorkHour(new DecimalFormat("0.0").format(workDay*8));
				    } // else
					dataList.add(data);
		        } // end while
	   	      } // end else
	   	      
	   	      report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	          setErrorMsg("報表印製成功");
	          return report;
	   	   
	    } // end try
		catch ( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		} // catch
	    catch( Exception e ) {
	    	e.printStackTrace();
	        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	        return null;
		} // end catch
	  } // doPrintfPdf()	
	
	/*
	public static String checkForjsp( String dateStart, String dateEnd, String staff ) {
		  List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(doAppendSqljob(), BeanPropertyRowMapper.newInstance(PRE4018.class));
		  if ( rs == null || rs.size() <= 0 )
			  return "查無資料，請變更查詢條件！";
		  else
			  return "ok";
	  } 
	*/
	public void doCreate() throws Exception {}
	public void doUpdate() throws Exception {}
	public void doDelete() throws Exception {}
	public Object doQueryOne() throws Exception {return null;}
	
} // PPE4008()