package com.kangdainfo.tcfi.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Type;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.GregorianCalendar;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.TreeMap;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.log4j.Logger;

import com.ekera.presearch.Examine;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.DateTimeFormatter;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.StringUtility;
import com.kangdainfo.moea.bo.SearchAllData;
import com.kangdainfo.sys.common.Constants;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;
import com.kangdainfo.tcfi.model.eedb.bo.EedbV8000;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb5000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.EedbV8000Dao;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.model.eicm.bo.UpdateHistoryData;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1003Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1007Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1009Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1011Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1017Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1019Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1100Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc055Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiveDao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeDetailDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;
import com.kangdainfo.tcfi.service.ApproveService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.PrefixDescHelper;
import com.kangdainfo.tcfi.util.SearchAllFilter;
import com.kangdainfo.tcfi.view.pre.IApprove;
import com.kangdainfo.tcfi.view.pre.PRE3001;
import com.kangdainfo.util.lang.CommonStringUtils;

public class ApproveServiceImpl implements ApproveService {
	private Logger logger = Logger.getLogger(this.getClass());
	private Log assignLog = LogFactory.getLog("assignLog");
	private Log saveToDBLog = LogFactory.getLog("saveToDBLog");

	private Cedb1000Dao cedb1000Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1004Dao cedb1004Dao;
	private Cedb2000Dao cedb2000Dao;
	private Cedb2002Dao cedb2002Dao;
	private Cedb2004Dao cedb2004Dao;
	private ReceiveDao receiveDao;
	private Cedb1019Dao cedb1019Dao;
	private Cedb1017Dao cedb1017Dao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1010Dao cedb1010Dao;
	private Cedbc000Dao cedbc000Dao;
	private Cedb1022Dao cedb1022Dao;
	private OssmApplMainDao ossmApplMainDao;
	private OssmApplFlowDao ossmApplFlowDao;
	private OssmFeeMainDao ossmFeeMainDao;
	private OssmFeeDetailDao ossmFeeDetailDao;
	private OssmOrgChangeDao ossmOrgChangeDao;
	private EedbV8000Dao eedbV8000Dao;
	private Eedb1000Dao eedb1000DaoEicm;
	private OssmOrgNameDao ossmOrgNameDao;
	private OssmBussItemDao ossmBussItemDao;
	private Eedb1100Dao eedb1100Dao;
	private Eedb3000Dao eedb3000Dao;
	private Eedb3300Dao eedb3300Dao;
	private Eedb5000Dao eedb5000Dao;
	private Eedb3100Dao eedb3100Dao;
	private Cedb1011Dao cedb1011Dao;
	private Cedb1003Dao cedb1003Dao;
	private Cedb1009Dao cedb1009Dao;
	private Cedbc055Dao cedbc055Dao;
	private Eedb1002Dao eedb1002Dao;
	private Cedbc004Dao cedbc004Dao;
	private Cedbc053Dao cedbc053Dao;
	private Cedb1006Dao cedb1006Dao;
	private Cedb1007Dao cedb1007Dao;
	private Cedb1008Dao cedb1008Dao;
	private Cedb1100Dao cedb1100Dao;
	
	private static Gson gson = new Gson();


	@Override
	public TreeMap<String, PrefixVo> getWaitForCloseKeyinQueryResult() {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		List<Cedb1000> cedb1000s = cedb1000Dao.goReadAllWaitForCloseKeyin();
		Cedb1000 cedb1000;
		PrefixVo prefixVo;
		String pno;
		for (int i = 0; i < cedb1000s.size(); i++) {
			cedb1000 = cedb1000s.get(i);
			if (cedb1000 != null) {
				pno = cedb1000.getPrefixNo();
				prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(pno, null);
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				prefixVo.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(pno));

				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(pno, prefixVo);
			}
		}
		return caseDatas;
	}

	@Override
	public TreeMap<String, PrefixVo> getCaseSelection(IApprove formBean, String functionName, boolean isPlusTenTecs, String approveResult) {
		if (formBean.getBanNo() != null && formBean.getBanNo().length() > 0) {
			return getQueryResultByBan(formBean.getBanNo().toUpperCase(), functionName, approveResult);
		} else if (formBean.getPrefixNo() != null && formBean.getPrefixNo().length() > 5) {
			return getQueryResultByPrefixNo(formBean.getPrefixNo(), isPlusTenTecs, functionName, approveResult);
		} else if (formBean.getTelixNo() != null
				&& (formBean.getTelixNo().length() == 11 || formBean.getTelixNo().length() == 16)) {
			return getQueryResultByTelixNo(formBean.getTelixNo(), approveResult);
		} else if (formBean.getApplyId() != null && formBean.getApplyId().length() > 0) {
			return getQueryResultByApplyId(formBean.getApplyId().toUpperCase(), functionName, approveResult);
		} else if (formBean.getApplyName() != null && formBean.getApplyName().length() > 0) {
			return getQueryResultByApplyName(formBean.getApplyName(), functionName, approveResult);
		} else if (formBean.getCompanyName() != null && formBean.getCompanyName().length() > 0) {
			return getQueryResultByCompanyName(formBean.getCompanyName(), functionName, approveResult);
		} else if (formBean instanceof PRE3001 && ((PRE3001) formBean).getAssignDate() != null) {
			String userId = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser().getUserId();
			return getQueryResultByUserIdAndAssignDate(userId, ((PRE3001) formBean).getAssignDate(), approveResult);
		}

		return new TreeMap<String, PrefixVo>();
	}

	private TreeMap<String, PrefixVo> getQueryResultByUserIdAndAssignDate(String userId, String assignDate, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		List<Cedb1000> cedb1000s = cedb1000Dao.findByIdnoAndAssignDate(userId, assignDate, null);
		// 查不到資料時, 回傳空的 caseDatas
		if (cedb1000s == null) {
			return caseDatas;
		}
		Cedb1000 cedb1000;
		PrefixVo prefixVo;
		String pno;
		for (int i = 0; i < cedb1000s.size(); i++) {
			cedb1000 = (Cedb1000) cedb1000s.get(i);
			pno = "";
			prefixVo = null;
			if (cedb1000 != null) {
				pno = cedb1000.getPrefixNo();
				prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(pno, approveResult);
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(pno, prefixVo);
			}
		}
		return caseDatas;
	}

	private TreeMap<String, PrefixVo> getQueryResultByCompanyName(String companyName, String functionName, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		// 查不到資料時, 回傳空的 caseDatas

		List<Cedb1000> cedb1000s = null;
		List<Cedb1006> cedb1006s = null;
		if ("atonce".equals(functionName)) {
			cedb1000s = cedb1000Dao.findReadByLikeCompanyNameForAtonce(companyName);
		} else if ("approve-change-record".equals(functionName)) {
			String[] companyNames = { companyName, companyName + "有限公司", companyName + "股份有限公司", companyName + "無限公司",
					companyName + "兩合公司" };

			cedb1006s = cedb1006Dao.findByCompanyName(companyNames);
		} else { // receive-keyin, approve , query
			cedb1000s = cedb1000Dao.findByLikeCompanyName(companyName, approveResult);
		}

		if ("approve-change-record".equals(functionName)) {
			int i = 0;
			PrefixVo prefixVo;
			for (Cedb1006 cedb1006 : cedb1006s) {
				cedb1006.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1006.getApplyKind()));
				prefixVo = new PrefixVo();

				try {
					BeanUtils.copyProperties(prefixVo, cedb1006);
					prefixVo.setApproveResult("Y".equals(cedb1006.getApproveResult())?"核准保留":"N".equals(cedb1006.getApproveResult())?"不予核准":"A".equals(cedb1006.getApproveResult())?"審查中":"");
					prefixVo.setUpdateName(getStaffNameByIdNo(cedb1006.getUpdateIdNo()));
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				setCaseDataOtherDatas(prefixVo);
				
				// 放入TreeMap
				caseDatas.put(String.valueOf(i++), prefixVo); // 感覺上應該要是cedb1006.getPrefixNo()+(i++), HB@20190306
			}
		} else {
			String pno;
			PrefixVo prefixVo;
			for (Cedb1000 cedb1000 : cedb1000s) {
				pno = cedb1000.getPrefixNo();
				prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(pno, approveResult);
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(pno, prefixVo);
			}
		}

		return caseDatas;
	}

	/**
	 * 從cedb1000取得馬上辦資料
	 * 
	 * @param applyName
	 *            String 申請人姓名
	 * @param approveResult
	 * @throws MoeaException
	 * @throws Exception
	 * @return TreeMap
	 * <AUTHOR>
	 */
	private TreeMap<String, PrefixVo> getQueryResultByApplyName(String applyName, String functionName, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		List<Cedb1000> cedb1000s = null;
		List<Cedb1006> cedb1006s = null;
		// 查不到資料時, 回傳空的 caseDatas
		if ("atonce".equals(functionName)) {
			cedb1000s = cedb1000Dao.findReadByApplyNameForAtonce(applyName);
		} else if ("receive-keyin".equals(functionName) || "approve".equals(functionName) || "keyin".equals(functionName) || "query".equals(functionName)) {
			cedb1000s = cedb1000Dao.findByLikeApplyName(applyName, approveResult);
		} else if ("approve-change-record".equals(functionName)) {
			cedb1006s = cedb1006Dao.findByApplyName(applyName);
		}

		if ("approve-change-record".equals(functionName)) {
			PrefixVo prefixVo;
			int i = 0;
			for (Cedb1006 cedb1006 : cedb1006s) {
				prefixVo = new PrefixVo();
				cedb1006.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1006.getApplyKind()));
				try {
					BeanUtils.copyProperties(prefixVo, cedb1006);
					prefixVo.setApproveResult("Y".equals(cedb1006.getApproveResult())?"核准保留":"N".equals(cedb1006.getApproveResult())?"不予核准":"A".equals(cedb1006.getApproveResult())?"審查中":"");
					prefixVo.setUpdateName(getStaffNameByIdNo(cedb1006.getUpdateIdNo()));
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}

				setCaseDataOtherDatas(prefixVo);
				caseDatas.put(prefixVo.getPrefixNo() + (i++), prefixVo);

			}
		} else {
			PrefixVo prefixVo;
			for (Cedb1000 cedb1000 : cedb1000s) {
				prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(cedb1000.getPrefixNo(), approveResult);
				cedb1000.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1000.getPrefixNo()));
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(cedb1000.getPrefixNo(), prefixVo);
			}
		}

		return caseDatas;
	}

	/**
	 * 從cedb1000取得馬上辦資料
	 * @param approveResult
	 * @param applyID
	 *            String 申請人身分證字號
	 * 
	 * @throws MoeaException
	 * @throws Exception
	 * @return TreeMap
	 * <AUTHOR>
	 */
	private TreeMap<String, PrefixVo> getQueryResultByApplyId(String applyId, String functionName, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		List<Cedb1000> cedb1000s = null;
		List<Cedb1006> cedb1006s = null;

		// 查不到資料時, 回傳空的 caseDatas
		if ("atonce".equals(functionName)) {
			cedb1000s = cedb1000Dao.findReadByApplyIdForAtonce(applyId);
		} else if ("approve-change-record".equals(functionName)) {
			cedb1006s = cedb1006Dao.findByApplyId(applyId);
		} else { // receive-keyin, approve
			cedb1000s = cedb1000Dao.findReadByApplyId(applyId, approveResult);
		}

		if ("approve-change-record".equals(functionName)) {
			int i = 0;
			for (Cedb1006 cedb1006 : cedb1006s) {
				String pno = cedb1006.getPrefixNo();
				cedb1006.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1006.getApplyKind()));
				PrefixVo prefixVo = new PrefixVo();
				try {
					BeanUtils.copyProperties(prefixVo, cedb1006);
					prefixVo.setApproveResult("Y".equals(cedb1006.getApproveResult())?"核准保留":"N".equals(cedb1006.getApproveResult())?"不予核准":"A".equals(cedb1006.getApproveResult())?"審查中":"");
					prefixVo.setUpdateName(getStaffNameByIdNo(cedb1006.getUpdateIdNo()));
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
				
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(pno + (i++), prefixVo);
			}
		} else {
			PrefixVo prefixVo;
			for (Cedb1000 cedb1000 : cedb1000s) {
				prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(cedb1000.getPrefixNo(), approveResult);
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				prefixVo.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1000.getPrefixNo()));
				setCaseDataOtherDatas(prefixVo);
				// 放入TreeMap
				caseDatas.put(cedb1000.getPrefixNo(), prefixVo);
			}
		}

		return caseDatas;
	}

	/**
	 * 從BAN_NO取得cedb1000的資料
	 * @param approveResult
	 * @param banNo
	 *            String 公司統一編號
	 * 
	 * @return Map
	 * 
	 */
	private TreeMap<String, PrefixVo> getQueryResultByTelixNo(String telixNo, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		System.out.println("---telixNo:" + telixNo);
		Cedb1000 cedb1000;
		// 查不到資料的處理: 回傳 empty的caseDatas
		cedb1000 = cedb1000Dao.findByTelixNo(telixNo, approveResult);
		if (cedb1000 == null)
			return caseDatas;
		String pno = cedb1000.getPrefixNo();
		PrefixVo prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(pno, approveResult);
		// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
		setCaseDataOtherDatas(prefixVo);
		// 放入TreeMap
		caseDatas.put(pno, prefixVo);
		return caseDatas;
	}

	/**
	 * 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料,前次預查公司名稱
	 * 以及法人資料(CEDB1022) <-- 20041028新增
	 * 
	 * @param caseData
	 *            CaseData
	 */
	private void setCaseDataOtherDatas(PrefixVo caseData) {
		// TODO 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
	}

	/**
	 * 從cedb1000取得馬上辦資料
	 * 
	 * @param banNo
	 *            String 公司統一編號
	 * @param functionName
	 * @param approveResult
	 * @throws MoeaException
	 * @throws Exception
	 * @return TreeMap
	 * <AUTHOR>
	 */
	public TreeMap<String, PrefixVo> getQueryResultByBan(String banNo, String functionName, String approveResult) {
		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		System.out.println("---BNO:" + banNo);
		// 查不到資料時, 回傳空的 caseDatas
		List<Cedb1000> cedb1000s = new ArrayList<Cedb1000>();
		List<Cedb1006> cedb1006s = null;

		if ("atonce".equals(functionName)) {
			cedb1000s = cedb1000Dao.findReadByBanNoForAtonce(banNo);
		} else if ("approve-change-record".equals(functionName)) {
			cedb1006s = cedb1006Dao.findByBanNo(banNo);
		} else { // query, approve
			cedb1000s = cedb1000Dao.findByBanNo(banNo, approveResult);
		}

		if ("approve-change-record".equals(functionName)) {
			
			for (int i = 0; i < cedb1006s.size(); i++) {
				Cedb1006 cedb1006 = (Cedb1006) cedb1006s.get(i);
				cedb1006.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1006.getApplyKind()));
				if (cedb1006 != null) {
					String pno = cedb1006.getPrefixNo();
					PrefixVo prefixVo = new PrefixVo();
					try {
						BeanUtils.copyProperties(prefixVo, cedb1006);
						prefixVo.setApproveResult("Y".equals(cedb1006.getApproveResult())?"核准保留":"N".equals(cedb1006.getApproveResult())?"不予核准":"A".equals(cedb1006.getApproveResult())?"審查中":"");
						prefixVo.setUpdateName(getStaffNameByIdNo(cedb1006.getUpdateIdNo()));
					} catch (IllegalAccessException e) {
						e.printStackTrace();
					} catch (InvocationTargetException e) {
						e.printStackTrace();
					}
					
					setCaseDataOtherDatas(prefixVo);
					caseDatas.put(pno + i, prefixVo);
				}
			}
		} else {

			PrefixVo prefixVo;
			for (int i = 0; i < cedb1000s.size(); i++) {
				Cedb1000 cedb1000 = (Cedb1000) cedb1000s.get(i);
				if (cedb1000 != null) {
					String pno = cedb1000.getPrefixNo();
					prefixVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(cedb1000.getPrefixNo(), approveResult);
					// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
					setCaseDataOtherDatas(prefixVo);
					// 放入TreeMap
					caseDatas.put(pno, prefixVo);
				}
			}
		}
		return caseDatas;
	}

	/**
	 * 將傳入的公司名稱,去除%,並且把單引號(')改為兩個單引號('') 同時去除公司型態
	 * 
	 * @param companyName
	 *            String 公司名稱
	 * @return String 處理後的公司名稱
	 * <AUTHOR>
	 */
	private static String cleanupString(String companyName) {
		String result = companyName.trim();
		result = result.replaceAll("(股份有限公司|有限公司|無限公司|兩合公司)", "");
		result = result.replaceAll("%", "");
		result = result.replaceAll("'", "''");
		return result;
	}
	
	/**
	 * ?? 依照傳入的字,取出同音同義字 舉例來說,傳入台,依照資料庫的資料,回傳(台,臺....)
	 * 
	 * @param word
	 *            String
	 */
	public Collection<String> doReadByCedbc058Map(String word) {
		// 將原始的字加入要回傳的ArrayList
		ArrayList<String> wordlist = new ArrayList<String>();
		wordlist.add(word);
		// 改由Constants對照同音同義

		// Zion 20080522
		HashSet<String> sameWord = Constants.C_cedbc058Map.get(word);
		if (sameWord != null) {
			for (Iterator<String> iter = sameWord.iterator(); iter.hasNext();)
				wordlist.add(iter.next());
		}
		return wordlist;
	}

	/**
	 * 將公司名稱的同音同義字,產生所有可能的組合
	 * 
	 * @param samewords
	 *            ArrayList
	 * @return Collection
	 */
	private static Collection<String> getSameWordCombinations(ArrayList<Collection<String>> samewords) {
		Collection<String> set1 = samewords.get(0);
		if (samewords != null && samewords.size() > 1) {
			Collection<String> set2 = samewords.get(1);
			set1 = produceCombination(set1, set2);
			for (int i = 2; i < samewords.size(); i++) {
				set1 = produceCombination(set1, samewords.get(i));
			}
		}
		return set1;
	}

	/**
	 * 傳入的兩個內含字串的Collection,產生兩個Collection相乘後的組合 假設傳入的set1內含台,臺,set2內含証,證
	 * 回傳的結果為台證,臺證,臺証,台証
	 * 
	 * @param set1
	 *            Collection
	 * @param set2
	 *            Collection
	 * @return Collection
	 */
	private static Collection<String> produceCombination(Collection<String> set1, Collection<String> set2) {
		ArrayList<String> result = new ArrayList<String>();
		String word1;
		for (Iterator<String> itor1 = set1.iterator(); itor1.hasNext();) {
			word1 = itor1.next();
			for (Iterator<String> itor2 = set2.iterator(); itor2.hasNext();) {
				result.add(word1 + itor2.next());
			}
		}
		return result;
	}

	/**
	 * 將傳入的公司名稱,每個字都去cedbc058檢查是否有同音同義字
	 * 
	 * 最後回傳的Collection,內含多個Collections,就是該公司名稱個別字每個同音同義字
	 * 
	 * 舉例來說,假設傳入的公司名稱為台證,依照資料庫的資料 回傳的Collection內容如下
	 * 
	 * 臺證 台証
	 * 
	 * 其中台和臺被放到一個Collection中,該Collection又被放置到回傳的ArrayList中的第一個position 其餘依此類推
	 * 
	 * @param firmName
	 *            String
	 * @return ArrayList
	 */
	private ArrayList<Collection<String>> getSameWords(String firmName) {
		ArrayList<Collection<String>> sameWordList = new ArrayList<Collection<String>>();
		String word = "";
		int length = firmName.length();

		for (int i = 0; i < length; i++) {
			word = firmName.substring(i, i + 1);
			// 取出該字的所有同音同義字
			sameWordList.add(doReadByCedbc058Map(word));
		}
		return sameWordList;
	}

	/**
	 * 將傳入的字串前後加上單引號 例如: 傳入abcd,回傳'abcd'
	 * 
	 * @param value
	 *            String 前後要加上單引號的字串
	 * @return String
	 * <AUTHOR>
	 */
	public String quoteString(String value) {
		return "'" + value + "'";
	}

	private String getFirmEqualSQL(String value) {
		String model = " SELECT BAN_NO FROM CEDB2000 WHERE COMPANY_NAME = {0} ";
		Object[] args = { quoteString(value) };
		return java.text.MessageFormat.format(model, args);
	}

	private String getPrefixEqualSQL(String value) {
		String model = " SELECT PREFIX_NO FROM CEDB1000 WHERE APPROVE_RESULT = ''Y'' AND COMPANY_NAME = {0} ";
		Object[] args = { quoteString(value) };
		return java.text.MessageFormat.format(model, args);
	}

	// 輔助查詢單次查詢時間上限
	static int TimeoutSecs = 20 * 60;

	private Cedb1000 getCedb1000(Cedb1000 therow) {
		try {
			return cedb1000Dao.findByPrefixNo(therow.getPrefixNo(), null);
		} catch (Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}

	private Cedb2000 getCedb2000(Cedb2000 therow) {
		try {
			return cedb2000Dao.findByBanNo(therow.getBanNo());
		} catch (Exception ex) {
			ex.printStackTrace();
			return null;
		}
	}

	/**
	 * 將DatabaseRow的資料放到Cedb1000或是Cedb200的物件中, 再放置到SearchAllData物件對應的欄位 type =
	 * true,表示傳入的DatabaseRow為Cedb1000的record type =
	 * false,表示傳入的DatabaseRow為Cedb2000的record
	 * 
	 * @param type
	 *            boolean
	 * @param theRow
	 *            DatabaseRow
	 * @return SearchAllData
	 */
	private SearchAllData getSearchAllData(boolean type, Object theRow) {
		SearchAllData data = new SearchAllData();
		Cedb1000 cedb1000;
		Cedb2000 cedb2000;
		if (type) {
			cedb1000 = getCedb1000((Cedb1000) theRow);
			data.setCedb1000(cedb1000);
		} else {
			cedb2000 = getCedb2000((Cedb2000) theRow);
			data.setCedb2000(cedb2000);
		}
		return data;
	}

	/**
	 * 將所有符合條件的cedb2000的資料放到Cedb2000再放置到SearchAllData物件對應的欄位
	 * 回傳的Vector對應傳入的Vector rows
	 * 
	 * @param rows
	 *            Vector 含DatabaseRow objects的Vector, 每個DatabaseRow代表一筆Cedb2000
	 *            record
	 * @return Vector
	 * @throws MoeaException
	 */
	private List<SearchAllData> produceFirmSearchAllData(java.util.Date querryStart, List<Cedb2000> rows) {
		List<SearchAllData> result = new ArrayList<SearchAllData>();
		for (Cedb2000 crnrow : rows) {
			result.add(getSearchAllData(false, crnrow));
			if ((new java.util.Date().getTime() - querryStart.getTime()) > TimeoutSecs * 1000)
				System.out.println("error.searchall.TOO_MANY_RST");
		}
		return result;
	}

	private String getPrefixEqualSQL(Collection<String> combinations) {
		String result = "";
		Object[] objarray = combinations.toArray();
		int length = objarray.length;
		if (length > 0) {
			result = result + getPrefixEqualSQL((String) objarray[0]);
			for (int i = 1; i < length; i++) {
				result = result + " UNION " + getPrefixEqualSQL((String) objarray[i]);
			}
		}
		return result;
	}

	private String getFirmEqualSQL(Collection<String> combinations) {
		StringBuffer result = new StringBuffer("");
		Object[] objarray = combinations.toArray();
		int length = objarray.length;
		if (length > 0) {
			result.append(getFirmEqualSQL((String) objarray[0]));
			for (int i = 1; i < length; i++) {
				result.append(" UNION ").append(getFirmEqualSQL((String) objarray[i]));
			}
		}
		return result.toString();
	}

	public String saveToPrefixDataBase(PrefixVo prefixVo, IApprove approve, String userId, String funCode) {
		if(logger.isInfoEnabled()) logger.info("[saveToPrefixDataBase]");
		// 取得系統日期時間, 如0931120 142005
		GregorianCalendar systemCal = new java.util.GregorianCalendar();
		String sDate = DateTimeFormatter.toDateString(systemCal);
		String sTime = DateTimeFormatter.toTimeString(systemCal);
		if(logger.isInfoEnabled()) logger.info("[sDate:"+sDate+"][sTime:"+sTime+"]");

		// 馬上辦的 Enrolment另外處理, 從另一個session取出
		// 取得目前要存檔的這筆案件資料, 一筆案件資料(CaseData)即包含有3個form, 故可由此(CaseData)取得需要的3個form

		// 取出三個 form的資料內容

		// 將公司詳細資料分別由各個form寫入CEDB1000, 1001, 1002 (理論上會有3個form, 1010不用回寫)
		// user打好的新資料
		Cedb1000 cedb1000_New = new Cedb1000();
		List<Cedb1001> cedb1001s_New = new ArrayList<Cedb1001>(prefixVo.getCedb1001s());
		List<Cedb1002> cedb1002s_New = new ArrayList<Cedb1002>(prefixVo.getCedb1002s());

		try {
			BeanUtils.copyProperties(cedb1000_New, prefixVo);
		} catch (IllegalAccessException e2) {
			e2.printStackTrace();
		} catch (InvocationTargetException e2) {
			e2.printStackTrace();
		}
		cedb1000_New.setUpdateDate(sDate);
		cedb1000_New.setUpdateTime(sTime);

		if (cedb1000_New.getIdNo() == null || "".equals(cedb1000_New.getIdNo())) {
			cedb1000_New.setRcvCheck("N");
		}

		Cedb1000 cedb1000Tmp = new Cedb1000();
		try {
			BeanUtils.copyProperties(cedb1000Tmp, prefixVo);
		} catch (IllegalAccessException e1) {
			e1.printStackTrace();
		} catch (InvocationTargetException e1) {
			e1.printStackTrace();
		}
		// 營業項目第一筆如果是空的, 要刪掉 (for 登打)
		if (cedb1002s_New.size() == 1) {
			Cedb1002 cedb1002New = cedb1002s_New.get(0);
			if (cedb1002New.getBusiItem() == null || cedb1002New.getBusiItem().equals("")) {
				cedb1002s_New.remove(0);
			}
		}

		// 重排營業項目編號
		for (int j = 0; j < cedb1002s_New.size(); j++) {
			Cedb1002 cedb1002 = cedb1002s_New.get(j);
			cedb1002.setSeqNo(StringUtility.padL(String.valueOf(j + 1), "0", 3));
		}

		Cedb1000 cedb1000_Old = cedb1000Dao.findByPrefixNo(cedb1000_New.getPrefixNo(), null);
		Cedb1022 cedb1022_Old;// 法人資料
		Cedb1023 cedb1023_Old;// 收件人資料

		try {
			cedb1023_Old = cedb1023Dao.findByPrefixNo(cedb1000_New.getPrefixNo());
		} catch (Exception ex1023) {
			cedb1023_Old = null;
		}
		try {
			cedb1022_Old = cedb1022Dao.findByPrefixNo(cedb1000_New.getPrefixNo());
		} catch (Exception ex1022) {
			cedb1022_Old = null;
		}

		// 更新 DB Cedb1000 的公司名稱審核, 通常如果不是核准(Y), 必須清掉公司名稱
		// 在詳細資料頁面按存檔的處理

		if (null == cedb1000_New.getApproveResult()) {
			cedb1000_New.setApproveResult("A");
			//審查中的案件，存檔時，再次檢查同公司名。
			ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(cedb1000_New.getPrefixNo(), userId);
		}
		
		if (!cedb1000_New.getApproveResult().equals(cedb1000_Old.getApproveResult())) {
			// 由核准Y變成其它(A或N), 1001 一律設為N
			if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_Old.getApproveResult())
					&& !PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())) {
				for (int i = 0; i < cedb1001s_New.size(); i++) {
					Cedb1001 cedb1001 = cedb1001s_New.get(i);
					cedb1001.setApproveResult(PrefixConstants.APPROVE_RESULT_N);
				}
				// 由A Y N 狀態變成Y
			} else if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())) {
				// 如果還沒選擇公司名稱, 仍然視為審核中 A
				cedb1000_New.setApproveResult(PrefixConstants.APPROVE_RESULT_A);
				// 取得要寫回1000的資料, 核准的公司名稱
				for (int i = 0; i < cedb1001s_New.size(); i++) {
					Cedb1001 cedb1001_New = cedb1001s_New.get(i);
					if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1001_New.getApproveResult())) {
						cedb1000_New.setCompanyName(cedb1001_New.getCompanyName());
						cedb1000_New.setApproveResult(PrefixConstants.APPROVE_RESULT_Y);
						break;
					}
				}
			}
		}

		// 新舊相同, 而且都是A或都是Y, 則根據1001要修改1000的狀態
		if ((PrefixConstants.APPROVE_RESULT_A.equals(cedb1000_New.getApproveResult()) || PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult()))
				&& cedb1000_New.getApproveResult().equals(cedb1000_Old.getApproveResult())) {
			// 取得要寫回1000的資料, 核准的公司名稱
			for (int i = 0; i < cedb1001s_New.size(); i++) {
				Cedb1001 cedb1001_New = cedb1001s_New.get(i);
				if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1001_New.getApproveResult())) {
					cedb1000_New.setCompanyName(cedb1001_New.getCompanyName());
					cedb1000_New.setApproveResult(PrefixConstants.APPROVE_RESULT_Y);
					break;
				}
			}
		}
		
		String approveResultAllNo = null;
		try {
			approveResultAllNo = BeanUtils.getSimpleProperty(approve, "approveResultAllNo");
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		} catch (NoSuchMethodException e) {
			e.printStackTrace();
		}

		// 根據1001, 修改1000的審核結果
		String approveResult = "A";
		String approveCompanyName = null;

		// 審核, 在公司名稱頁面選全部不准, 則以此為第一優先
		if (approveResultAllNo != null && approveResultAllNo.equalsIgnoreCase("true")) {
			cedb1000_New.setApproveResult(PrefixConstants.APPROVE_RESULT_N);
		} else {
			// 沒有選全部不准, 可能是准某個名稱, 也可能是審核中
			cedb1000_New.setApproveResult(PrefixConstants.APPROVE_RESULT_A);
		}

		// 取得要寫回1000的資料, 核准的公司名稱, 以及清掉被核准的1001的同名註記
		for (int i = 0; i < cedb1001s_New.size(); i++) {
			Cedb1001 cedb1001_New = cedb1001s_New.get(i);
			if (approveResultAllNo != null && approveResultAllNo.equalsIgnoreCase("true")) {
				// 不准
				approveResult = "N";
				cedb1001_New.setApproveResult("N");
				System.out.println("0----------MaintainBo 1000的cedb1000_New.getApproveResult():"
						+ cedb1000_New.getApproveResult());
			} else if (cedb1001_New.getApproveResult() != null && cedb1001_New.getApproveResult().equals("Y")) {
				approveResultAllNo = "false";
				approveResult = "Y";
				approveCompanyName = cedb1001s_New.get(i).getCompanyName();
				cedb1001_New.setRemark("");
				cedb1001Dao.update(cedb1001_New);
				break;
			}
		}

		System.out.println("1----------MaintainBo 1000的cedb1000_New.getApproveResult():" + cedb1000_New.getApproveResult());
		if ("Y".equals(approveResult) && !"Y".equals(cedb1000_New.getApproveResult())) {
			cedb1000_New.setApproveResult("Y");
			cedb1000_New.setCompanyName(approveCompanyName);
			System.out.println("XXXXX ----------MaintainBo 1000的 ApproveResult -> Y ");
		} else if ("Y".equals(approveResult) && !cedb1000_New.getCompanyName().equals(approveCompanyName)) {
			cedb1000_New.setApproveResult("Y");
			cedb1000_New.setCompanyName(approveCompanyName);
			System.out.println("XXXXX2 ----------MaintainBo 1000的 ApproveResult -> Y ");
		} else if ("A".equals(approveResult) && "Y".equals(cedb1000_New.getApproveResult())) {
			cedb1000_New.setApproveResult("A");
			cedb1000_New.setCompanyName(null);
			cedb1000_New.setSpecialName(null);
			System.out.println("XXXXX ----------MaintainBo 1000的 ApproveResult -> A");
		}

		// 經過各個rule過濾後, 如果審核結果仍然不是Y,則清掉公司名稱及特取名稱, 及1001的審核結果

		if (!PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())) {
			cedb1000_New.setCompanyName(null);
			cedb1000_New.setSpecialName(null);
			for (int i = 0; i < cedb1001s_New.size(); i++) {
				Cedb1001 cedb1001_New = cedb1001s_New.get(i);
				cedb1001_New.setApproveResult(PrefixConstants.APPROVE_RESULT_N);
			}
		} else if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())) {
			// 經過各個rule過濾後, 如果審核結果是Y,則清掉1001的同名註記
			for (int i = 0; i < cedb1001s_New.size(); i++) {
				Cedb1001 cedb1001_New = cedb1001s_New.get(i);
				if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1001_New.getApproveResult())) {
					cedb1001_New.setRemark("");
				}
			}
		}

		if(!"approve".equals(approve.getFunctionName())) {
			// 寫入處理人員
			cedb1000_New.setUpdateIdNo(userId);
		}
		
		//備份
		ServiceGetter.getInstance().getBackupService().doBackup(cedb1000_New.getPrefixNo(), userId);

		// 寫入審核日期, 保留期限及案件狀態
		this.setExtend(approve, cedb1000_New, cedb1000_Old, userId);
		this.setApproveDateAndTime(approve, cedb1000_New, cedb1000_Old, sDate, sTime);
		this.setupPrefixStatus(approve, cedb1000_New, cedb1000_Old, userId);
		this.setReserveDate(approve, cedb1000_New, cedb1000_Old);
		// 儲存EEDB1000(Oracle), 通報及 1010(收發文登打日期..XD）
		this.setEedb1000(approve, cedb1000_New);
		// cedb1000_New.setExtendMark(cedb1000_Old.getExtendMark());
		
		this.set1010(approve, cedb1000_New,cedb1000_Old, userId);
		// 檢查特取名稱
		if (!this.isValidateSpecialName(approve, cedb1000_New)) {
			System.out.println("特取名稱錯誤，特取名稱必須包含於公司名稱之中(Invalidate SpecialName)");
			return null;
		}
		
		// =====儲存1023==== (1023的資料, 包含於詳細資料的Form中)
		if (cedb1023_Old == null) {
			// commons.util.debugMsgs.println("---------cedb1023_Old key值不存在, 做Insert");
			Cedb1023 cedb1023Clone = new Cedb1023();
			try {
				BeanUtils.copyProperties(cedb1023Clone, prefixVo.getCedb1023());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			cedb1023Clone.setPrefixNo(cedb1000_New.getPrefixNo());
			cedb1023Dao.insert(cedb1023Clone);
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023Clone);
		} else {
			System.out.println("---------cedb1023_Old key值存在, 做Update");
			Cedb1023 cedb1023Clone = (Cedb1023) cedb1023_Old;
			try {
				BeanUtils.copyProperties(cedb1023Clone, prefixVo.getCedb1023());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			cedb1023Dao.update(cedb1023Clone);
			//寫入個資軌跡TrackLog(CEDB1000)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023Clone);
		}

		if(null!=prefixVo.getCedb1022()) {
			if(null==cedb1022_Old) {
				Cedb1022 cedb1022_New = new Cedb1022();
				cedb1022_New.setApplyBanNo(prefixVo.getCedb1022().getApplyBanNo());
				cedb1022_New.setApplyLawName(prefixVo.getCedb1022().getApplyLawName());
				cedb1022_New.setPrefixNo(cedb1000_New.getPrefixNo());
				cedb1022Dao.insert(cedb1022_New);
			} else {
				cedb1022_Old.setApplyBanNo(prefixVo.getCedb1022().getApplyBanNo());
				cedb1022_Old.setApplyLawName(prefixVo.getCedb1022().getApplyLawName());
				cedb1022Dao.update(cedb1022_Old);
			}
		}

		cedb1000Dao.update(cedb1000_New);
		//寫入個資軌跡TrackLog(CEDB1000)
		ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1000_New);
		saveCedb1002s(prefixVo.getCedb1002s(), prefixVo.getPrefixNo());

		return cedb1000_New.getPrefixNo();
	}

	/**
	 * 1010案件狀況檔(for 審核用)
	 * 
	 * @param caseForm
	 *            CaseForm
	 * @param cedb1000_New
	 *            Cedb1000
	 */
	public void set1010(IApprove approve, Cedb1000 cedb1000_New, Cedb1000 cedb1000_Old, String userId) {
		// 審核
		boolean add1010orNot = false;
		if (approve.getFunctionName().startsWith("approve")) {
			if ( !Common.get(cedb1000_Old.getApproveResult()).equals(Common.get(cedb1000_New.getApproveResult())) ) {
				add1010orNot = true;
			}
			else {
				add1010orNot = false;
			}
			if ("Y".equals(cedb1000_New.getApproveResult()) && !(cedb1000_New.getCompanyName() == null)
					&& !cedb1000_New.getCompanyName().equals("")) {
				// 核准且有公司名稱
				ServiceGetter.getInstance().getCaseFlowService()
						.addCaseFlowForApprove(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_5, add1010orNot);
			} else if ("N".equalsIgnoreCase(cedb1000_New.getApproveResult())) {
				// 不准
				ServiceGetter.getInstance().getCaseFlowService()
						.addCaseFlowForApprove(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_5, add1010orNot);
			} else {
				// 其它情況視為承辦決行中
				ServiceGetter.getInstance().getCaseFlowService()
						.addCaseFlowForApprove(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_4, add1010orNot);
			}
			
		}

		// 收發文登打
		if (approve.getFunctionName().indexOf("keyin") != -1) {
			Cedbc000 cedbc000 = cedbc000Dao.findByIdNo(userId);
			// 105/7/20 六科的需求:結案後如有改地址應不算入辦理公文時間。
			// 若這個案件已有結案日期, 使用收文登打作業異動案件資料不會update cedb1010的紀錄
			String closeDate = cedb1000_Old.getCloseDate();
			if (PrefixConstants.USER_GROUP_12.equals(cedbc000.getGroupId())) {
				if ( null == closeDate || "".equals(closeDate) ) 
					ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_2);
			} else if(approve.getFunctionName().indexOf("receive-keyin") != -1) { //收文登打
				if ( null == closeDate || "".equals(closeDate) )
					ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_2);
			} else {
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_7);
			}
		}
		// 馬上辦(怎能不處理...KM!)
		if (approve.getFunctionName().startsWith("atonce")) {
			if (cedb1000_New.getRemark1().indexOf("遺失補發") != -1) {
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlowForAtonce(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_B, cedb1000_New.getAtonceType());
			} else { // 馬上辦
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlowForAtonce(cedb1000_New.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_C, cedb1000_New.getAtonceType());
			}
		}
		
	}

	/**
	 * 決定是否寫入EEDB1000的Method(for 審核用)
	 * 
	 * @param caseForm
	 *            CaseForm
	 * @param cedb1000_New
	 *            Cedb1000
	 * @param cedb1002s_New
	 *            Vector
	 * @param loginID
	 *            String
	 * @param uow
	 *            UnitOfWork
	 */
	public void setEedb1000(IApprove approve, Cedb1000 cedb1000_New) {
		// 不是審核也不是登打，零分
		if (!approve.getFunctionName().startsWith("approve") && !approve.getFunctionName().startsWith("keyin"))
			return;
		// 不是線上申辦，零分

		if (cedb1000_New.getTelixNo() == null || cedb1000_New.getTelixNo().startsWith("0"))
			return;
		// 沒有審核結果，還是零分

		if (cedb1000_New.getApproveResult() == null || cedb1000_New.getApproveResult().length() == 0)
			return;

		saveToEedb1000(cedb1000_New); // 不管怎樣都去判斷一下

	}

	// 審核: 儲存的EEDB1000
	// KyLin : 只有Y->N, A或N->Y,A 才要再寫eedb1000。94.09.15
	public void saveToEedb1000(Cedb1000 cedb1000_New) {
		String telixNo = cedb1000_New.getTelixNo();
		if (telixNo == null) {
			System.out.println("儲存CEEedb1000失敗");
			return;
		}

		try {

			Eedb1000 ceEedb1000 = eedb1000DaoEicm.findByTelixNo(telixNo);
			Eedb1000 ceEedb1000Clone = new Eedb1000();
			BeanUtils.copyProperties(ceEedb1000Clone, ceEedb1000);

			// String oldProcessStatus = ceEedb1000.getProcessStatus();
			String oldApproveResult = ceEedb1000.getApproveResult();
			String oldReserveDate = ceEedb1000.getReserveDate();
			String newApproveDate = cedb1000_New.getApproveDate();
			String newApproveTime = cedb1000_New.getApproveTime();
			String newApproveResult = cedb1000_New.getApproveResult();
			String newReserveDate = cedb1000_New.getReserveDate();
			String newCloseDate = cedb1000_New.getCloseDate();

			// 還沒有發文，可以不用
			if (newCloseDate == null || newCloseDate.length() == 0)
				return;
			// Eedb裡的審核結果與Cedb裡的不能一樣

			if (newReserveDate != null && newReserveDate.length() != 0) {
				if (newReserveDate.equals(oldReserveDate))
					return;
			} else if (newApproveResult.equalsIgnoreCase(oldApproveResult)) {
				return;
			}
			// if ("Y".equalsIgnoreCase(newApproveResult) &&
			// (newReserveDate == null || newReserveDate.length() == 0))
			// return;

			// 原來的Oracle Eedb1000必須已經有審核結果了Y或N，否則零分

			// if (!"Y".equalsIgnoreCase(oldApproveResult) &&
			// !"N".equalsIgnoreCase(oldApproveResult))
			// return;
			// 原來的Oracle Eedb1000的處理情形不是ZZ也不用再寫了
			// if (!"ZZ".equalsIgnoreCase(oldProcessStatus))
			// return;

			if ("Y".equalsIgnoreCase(newApproveResult)) {
				ceEedb1000Clone.setApproveDate(newApproveDate);
				ceEedb1000Clone.setApproveTime(newApproveTime);
				ceEedb1000Clone.setApproveResult(newApproveResult);
				ceEedb1000Clone.setProcessStatus("ZZ");
				ceEedb1000Clone.setReserveDate(newReserveDate);
				ceEedb1000Clone.setApproveMail(null);
			} else if ("N".equalsIgnoreCase(newApproveResult)) {
				ceEedb1000Clone.setApproveDate(newApproveDate);
				ceEedb1000Clone.setApproveTime(newApproveTime);
				ceEedb1000Clone.setApproveResult(newApproveResult);
				ceEedb1000Clone.setProcessStatus("ZZ");
				ceEedb1000Clone.setReserveDate(null);
				ceEedb1000Clone.setApproveMail(null);
			} else if ("A".equalsIgnoreCase(newApproveResult)) {
				ceEedb1000Clone.setApproveDate(null);
				ceEedb1000Clone.setApproveTime(null);
				ceEedb1000Clone.setApproveResult(null);
				ceEedb1000Clone.setProcessStatus("BB");
				ceEedb1000Clone.setReserveDate(null);
				ceEedb1000Clone.setApproveMail(null);
			}

			eedb1000DaoEicm.insert(ceEedb1000Clone);
		} catch (Exception ex) {
			System.out.println("請取CEEedb1000失敗");
		}

	}

	/**
	 * 檢查特取名稱 審核及發文登打時檢查特取名稱, 特取名稱中的各個字都必須包含於公司名稱中
	 */
	public boolean isValidateSpecialName(IApprove approve, Cedb1000 cedb1000_New) {
		String specialName = cedb1000_New.getSpecialName();
		String companyName = cedb1000_New.getCompanyName();
		String aWord = null;
		// 審核及登打才檢查
		if (specialName != null && specialName.trim().length() > 0 && companyName != null && companyName.trim().length() > 0) {
			if (approve.getFunctionName().startsWith("keyin") || approve.getFunctionName().startsWith("approve")) {
				for (int i = 0; i < specialName.length(); i++) {
					aWord = specialName.substring(i, i + 1);
					System.out.println("==== MaintainBo aWord:" + aWord);
					System.out.println("==== MaintainBo indexOf(aWord):" + companyName.indexOf(aWord));
					// -1 表示不含這個字
					if (companyName.indexOf(aWord) == -1) {
						return false;
					}
				}
			}
		}
		return true;
	}

	private void setExtend( IApprove approve, Cedb1000 cedb1000_New, Cedb1000 cedb1000_Old, String userId ) {
		if (approve.getFunctionName().startsWith("approve")) {
			String oldExtendMark = Common.get(cedb1000_Old.getExtendMark());

			String prefixNo = Common.get(cedb1000_New.getPrefixNo());
			String newExtendMark = Common.get(cedb1000_New.getExtendMark());
			String newExtendReason = Common.get(cedb1000_New.getExtendReason());
			String newExtendOther = Common.get(cedb1000_New.getExtendOther());
			
			// 展期存檔時, 只有當使用者變更展期狀態時才會更新/新增cedb1010內的資料
			if(!"".equals(newExtendMark)) {
				//update extend
				if("Y".equalsIgnoreCase(newExtendMark)) {
					String newExtendDate = Datetime.getYYYMMDD();
					cedb1000Dao.updateExtend(prefixNo, newExtendMark, newExtendReason, newExtendOther, newExtendDate, userId);
					cedb1000_New.setExtendDate(newExtendDate);
				} else {
					cedb1000Dao.updateExtend(prefixNo, newExtendMark, "", "", "", userId);
					cedb1000_New.setExtendDate("");
				}
				if(!newExtendMark.equals(oldExtendMark)) {
					//add CaseFlow or update CaseFlow.Workday
					ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(prefixNo, userId, PrefixConstants.PREFIX_STATUS_3);
					ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(prefixNo, userId, PrefixConstants.PREFIX_STATUS_4);
				}
			}
		}
	}

	/**
	 * 決定保留期限的Method(for 審核用及馬上辦) KyLin Modified :
	 * 保留期限的計算改成以發文日期加半年(一年）2005.10.06
	 * 
	 * @param caseForm
	 *            CaseForm
	 * @param cedb1000_New
	 *            Cedb1000
	 */
	private void setReserveDate(IApprove approve, Cedb1000 cedb1000_New, Cedb1000 cedb1000_Old) {
		if(logger.isInfoEnabled()) logger.info("[setReserveDate]"+approve.getFunctionName());
		// 不核准保留的情況，或審核中情況，保留期限要清空
		if (!PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())) {
			cedb1000_New.setReserveDate(null);
			cedb1000_New.setReserveDays(0);
			cedb1000_New.setReserveMark("N");
			return;
		}
		// 假如發文日期被清掉了，保留期限要清空
		if ("".equals(Common.get(cedb1000_New.getCloseDate()))) {
			cedb1000_New.setReserveDate(null);
			return;
		}
		// 假如發現沒有發文日期，不給保留期限
		if ("".equals(Common.get(cedb1000_Old.getCloseDate()))) {
			return;
		}

		if (!(cedb1000_New.getReserveDays() == 365))
			cedb1000_New.setReserveDays(180); // 若非保留一年，預設保留半年

		if (approve.getFunctionName().startsWith("approve") || approve.getFunctionName().startsWith("atonce")) {
			String closeDate = StringUtility.dateStr2Str(cedb1000_Old.getCloseDate());
			int year = StringUtility.str2intYear(closeDate);
			int month = StringUtility.str2intMonth(closeDate);
			int day = StringUtility.str2intDay(closeDate);
			GregorianCalendar calendar = new GregorianCalendar(year, month, day); // 取得發文日的Calendar
			// 核准保留的情況
			if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())
					&& !"".equals(Common.get(cedb1000_New.getCompanyName())) ) {
				if (cedb1000_New.getReserveDays().intValue() == 365) {
					// 保留一年，加一年減一天
					calendar.add(Calendar.YEAR, 1);
					calendar.add(Calendar.DATE, -1);
				} else {
					// 保留半年，加6個月減一天
					calendar.add(Calendar.MONTH, 6);
					calendar.add(Calendar.DATE, -1);
				}
				// 假如有勾延長保留期限一年，多增加一個月
				if ("Y".equalsIgnoreCase(cedb1000_New.getReserveMark())) {
					calendar.add(Calendar.MONTH, 1);
				}
				String sDate = DateTimeFormatter.toDateString(calendar);
				cedb1000_New.setReserveDate(sDate);
			}
		}

		// 馬上辦延長保留期限
		if (approve.getFunctionName().startsWith("atonce")) {
			// cedb1000_New.setCloseDate(cedb1000_New.getReserveDate()); // 應優仲要求每次存檔時不異動發文日期
			
			// 沒有選擇延長一個月，丟掉
			if (cedb1000_New.getReserveMark() == null || !cedb1000_New.getReserveMark().equals("Y"))
				return;
			// 上次選過延長一個月了，丟掉
			if (cedb1000_Old.getReserveMark() != null && cedb1000_Old.getReserveMark().equals("Y"))
				return;
			// 沒有核准保留，再丟掉
			if (!cedb1000_Old.getApproveResult().equals("Y"))
				return;
			// 之前居然沒有保留期限，還是丟掉
			if (cedb1000_Old.getReserveDate() == null || cedb1000_Old.getReserveDate().length() == 0)
				return;

			// 通過嚴格的品質管控，才可以讓他延長一個月
			String reserveDate = StringUtility.dateStr2Str(cedb1000_Old.getReserveDate());
			int oldYear = StringUtility.str2intYear(reserveDate);
			int oldMonth = StringUtility.str2intMonth(reserveDate);
			int oldDay = StringUtility.str2intDay(reserveDate);
			GregorianCalendar oldCalendar = new GregorianCalendar(oldYear, oldMonth, oldDay);
			oldCalendar.add(Calendar.MONTH, 1);
			String sDate = DateTimeFormatter.toDateString(oldCalendar);
			cedb1000_New.setReserveDate(sDate);
		}

	}

	/** 決定核覆日期及時間的Method(for 審核用) */
	private void setupPrefixStatus(IApprove approve, Cedb1000 cedb1000_New, Cedb1000 cedb1000_Old, String idNo) {
		if (logger.isInfoEnabled()) logger.info("[setupPrefixStatus]");
		if (approve.getFunctionName().startsWith("approve")) {
			//審核(approve)

			// 檢查是否已發文結案, 結案的case不再更改時間
			Cedb1000 curr = cedb1000Dao.findByPrefixNo(cedb1000_New.getPrefixNo(), null);
			if(null!=curr) {
				if ( PrefixConstants.PREFIX_STATUS_8.equals(curr.getPrefixStatus()) ) {
					if (logger.isInfoEnabled()) logger.info("[案件狀態：8][不更新(cedb1000.prefix_status)]");
					cedb1000_New.setPrefixStatus(cedb1000_Old.getPrefixStatus());
					return;
				} else if ( PrefixConstants.PREFIX_STATUS_C.equals(curr.getPrefixStatus()) ) {
					if (logger.isInfoEnabled()) logger.info("[案件狀態：C][不更新(cedb1000.prefix_status)]");
					cedb1000_New.setPrefixStatus(cedb1000_Old.getPrefixStatus());
					return;
				} else if ( PrefixConstants.PREFIX_STATUS_9.equals(curr.getPrefixStatus()) ) {
					if (logger.isInfoEnabled()) logger.info("[案件狀態：9][更新(cedb1000.prefix_status)為8]");
					cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_8);
					if(CommonStringUtils.isNotEmpty(curr.getAtonceType())) {
						cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_C);
					}
					return;
				} else if ( PrefixConstants.PREFIX_STATUS_A.equals(curr.getPrefixStatus()) ) {
					if (logger.isInfoEnabled()) logger.info("[案件狀態：A][不更新(cedb1000.prefix_status)]");
					cedb1000_New.setPrefixStatus(cedb1000_Old.getPrefixStatus());
					return;
				} else if ( PrefixConstants.PREFIX_STATUS_E.equals(curr.getPrefixStatus()) ) {
					if (logger.isInfoEnabled()) logger.info("[案件狀態：E][不更新(cedb1000.prefix_status)]");
					cedb1000_New.setPrefixStatus(cedb1000_Old.getPrefixStatus());
					return;
				} else {
					//do nothing
				}
			}

			if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult())
					&& !"".equals(Common.get(cedb1000_New.getCompanyName())) ) {
				// 核准且有公司名稱
				cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_5);
			} else if (PrefixConstants.APPROVE_RESULT_N.equals(cedb1000_New.getApproveResult())) {
				// 不准
				cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_5);
			} else {
				// 其它情況視為承辦決行中
				cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_4);
			}
		} else if (approve.getFunctionName().startsWith("keyin")) {
			//登打(keyin)
			List<Cedb1010> cedb1010s = cedb1010Dao.findByPrefixNo(cedb1000_Old.getPrefixNo());
			if (cedb1010s != null) {
				// 先當作收文登打

				// modified by girlie 20080617 do not set default "2"
				// cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_2);

				// added by girlie 20080617
				/*
				 * 1. 確認此id_no的cedbc000.staff_title值
				 * 
				 * 
				 * 
				 * 2. 判斷cedbc000.staff_title值是否'登打人員' 2.a. 是'登打人員'時: 日期時間是屬於
				 * 收文登打 2.b. 非'登打人員'時: 日期時間是屬於 發文登打
				 */
				// 1. 確認此id_no的cedbc000.staff_title值

				try {
					Cedbc000 cedbc000 = cedbc000Dao.findByIdNo(idNo);
					// 2. 判斷cedbc000.staff_title值是否'登打人員'
					// 2.a. 是'登打人員'時: 日期時間是屬於 收文登打
					// replaced by girlie 20080620
					// if("登打人員".equals(cedbc000.getStaffTitle())){
					if ("12".equals(cedbc000.getGroupId())) {
						cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_2);
						// 2.b. 非'登打人員'時: 日期時間是屬於 發文登打
					} else {
						cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_7);
					}
				} catch (Exception e) {
					System.out.println("XXXX  ------判斷收發文登打!!");
				}
				for (int i = 0; i < cedb1010s.size(); i++) {
					Cedb1010 cedb1010 = (Cedb1010) cedb1010s.get(i);
					// 如果已審核, 則視為發文登打

					if (cedb1010.getProcessStatus().equals(PrefixConstants.PREFIX_STATUS_5)) {
						cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_7);
					}
				}
			} else {
				System.out.println("----- MaintainBo, setPrefixStatus, 不存在1010的資料, 無法判斷登打的1010狀態!!");
			}
		} else if (approve.getFunctionName().startsWith("atonce")) {
			//馬上辦(atonce)
			if (cedb1000_New.getRemark1().indexOf("遺失補發") != -1) {
				cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_B);
			} else {
				// 馬上辦
				cedb1000_New.setPrefixStatus(PrefixConstants.PREFIX_STATUS_C);
			}
		}

	}

	/**
	 * 決定核覆日期及時間的Method(for 審核用)
	 * 
	 * @param caseForm
	 *            CaseForm
	 * @param cedb1000_New
	 *            Cedb1000
	 */
	public void setApproveDateAndTime(IApprove approve, Cedb1000 cedb1000_New,Cedb1000 cedb1000_Old, String sDate, String sTime) {
		if (logger.isInfoEnabled()) logger.info("[setApproveDateAndTime]");
		boolean hasChange = false;//判斷是否有異動
		// 只有透過"pre3001"這支作業修改沒有結案的案件才會異動審核日期
		if (approve.getFunctionName().startsWith("approve")) {
			if ("".equals(Common.get(cedb1000_Old.getCloseDate()))) {
				if ( !Common.get(cedb1000_Old.getApproveResult()).equals(Common.get(cedb1000_New.getApproveResult())) ) {
					if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult()) 
							&& !"".equals(Common.get(cedb1000_New.getCompanyName()))) {
						// 核准且有公司名稱
						cedb1000_New.setApproveDate(sDate);
						cedb1000_New.setApproveTime(sTime);
						hasChange = true;
					} else if (PrefixConstants.APPROVE_RESULT_N.equals(cedb1000_New.getApproveResult())) {
						// 不准
						cedb1000_New.setApproveDate(sDate);
						cedb1000_New.setApproveTime(sTime);
						hasChange = true;
					} else {
						//審核中
						cedb1000_New.setApproveDate(null);
						cedb1000_New.setApproveTime(null);
						hasChange = true;
					}
				}else{
					//異動的審核結果為 Y 或 N 時
					if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000_New.getApproveResult()) ||
							PrefixConstants.APPROVE_RESULT_N.equals(cedb1000_New.getApproveResult())){
						//原本審核日期為空則可以異動審核日期
						if("".equals(Common.get(cedb1000_Old.getApproveDate()))){
							cedb1000_New.setApproveDate(sDate);
							cedb1000_New.setApproveTime(sTime);
							hasChange = true;
						}
					}
				}
			}

			if(!hasChange) {
				//若沒異動的話, 就存舊的, 避免連續存兩次的時候, 審核日期不見的問題, add by jack 20141215
				cedb1000_New.setApproveDate(cedb1000_Old.getApproveDate());
				cedb1000_New.setApproveTime(cedb1000_Old.getApproveTime());
			}
		}
	}

	private void logWithTime(String msg, Long timeStart, Long timeSegement) {
		timeSegement = System.currentTimeMillis()-timeStart;
		saveToDBLog.info(msg+timeSegement);
	}

	/**
	 * Insert data into relavant CEDB Tables without auto-approve
	 * <p>
	 * 公司基本資料 CEDB1000 <br>
	 * 公司名稱資料 CEDB1001 <br>
	 * 公司營業項目 CEDB1002 <br>
	 * 案件過程 CEDB1010 <br>
	 * 法人資料 CEDB1022 <br>
	 * 郵寄收件人資料 CEDB1023 <br>
	 * 
	 * @return String 無法成功轉換的案件 PK; 如果return null 表示轉檔成功,沒有無法轉換的資料
	 * <AUTHOR> 20080417
	 */
	public String saveToPrefixDataBaseNoApprove(PrefixVo prefixVo, IApprove approve, String userId, String funCode) {
		if (logger.isInfoEnabled()) logger.info("[saveToPrefixDataBaseNoApprove]");
		Long timeStart = System.currentTimeMillis();
		Long timeSegement = new Long(1);
		saveToDBLog.info(prefixVo.getPrefixNo()+",1.審查作業存檔商業邏輯開始---"+timeStart);
		// 取得系統日期時間, 如0931120 142005
		GregorianCalendar systemCal = new java.util.GregorianCalendar();
		String sDate = DateTimeFormatter.toDateString(systemCal);
		String sTime = DateTimeFormatter.toTimeString(systemCal);
		if (logger.isInfoEnabled()) logger.info("[sDate:"+sDate+"][sTime:"+sTime+"]");

		Cedb1000 cedb1000_New = new Cedb1000();
		List<Cedb1001> cedb1001s_New = new ArrayList<Cedb1001>(prefixVo.getCedb1001s());
		List<Cedb1002> cedb1002s_New = new ArrayList<Cedb1002>(prefixVo.getCedb1002s());

		try {
			BeanUtils.copyProperties(cedb1000_New, prefixVo);
			logWithTime(prefixVo.getPrefixNo()+",2.將頁面資料copy至Cedb1000---", timeStart, timeSegement);
		} catch (IllegalAccessException e1) {
			e1.printStackTrace();
		} catch (InvocationTargetException e1) {
			e1.printStackTrace();
		}

		cedb1000_New.setUpdateDate(sDate);
		cedb1000_New.setUpdateTime(sTime);
		logWithTime(prefixVo.getPrefixNo()+",3.設定異動日期完成---", timeStart, timeSegement);
		if("receive-keyin".equals(approve.getFunctionName())) {
			ServiceGetter.getInstance().getPrefixService().doIsNeedRollBackToReceive(cedb1000_New);
		}

		// 營業項目第一筆如果是空的, 要刪掉 (for 登打)
		if (cedb1002s_New.size() == 1) {
			Cedb1002 cedb1002New = (Cedb1002) cedb1002s_New.get(0);
			if (cedb1002New.getBusiItem() == null || cedb1002New.getBusiItem().equals("")) {
				cedb1002s_New.remove(0);
			}
		}

		// 重排營業項目編號
		for (int j = 0; j < cedb1002s_New.size(); j++) {
			Cedb1002 cedb1002 = (Cedb1002) cedb1002s_New.get(j);
			cedb1002.setSeqNo(StringUtility.padL(String.valueOf(j + 1), "0", 3));
		}

		// DB的 1000,1001,1002.1023 舊資料

		Cedb1000 cedb1000_Old = cedb1000Dao.findByPrefixNo(cedb1000_New.getPrefixNo(), null);
		// 馬上辦-遺失補發 判斷是不是做過了.
		// if (caseForm.getStrutsConfigFormName().startsWith("atonce")) {
		// if (isTheCaseReIssueedBefore(cedb1000_Old) &&
		// cedb1000_New.getRemark1().indexOf("遺失補發") != -1) {
		// errors.add("ERROR", new ActionError("error.reissue.save"));
		// return null;
		// }
		// }

		// System.out.println("---設完Form的資料 MaintainBO cedb1000_Old.getTelixNo():"
		// + cedb1000_Old.getTelixNo());
		Cedb1022 cedb1022_Old;// 法人資料
		Cedb1023 cedb1023_Old;// 收件人資料

		try {
			cedb1023_Old = cedb1023Dao.findByPrefixNo(cedb1000_New.getPrefixNo());
		} catch (Exception ex1023) {
			cedb1023_Old = null;
		}

		try {
			cedb1022_Old = cedb1022Dao.findByPrefixNo(cedb1000_New.getPrefixNo());
		} catch (Exception ex1022) {
			cedb1022_Old = null;
		}
		logWithTime(prefixVo.getPrefixNo()+",4.準備寫入異動人員---", timeStart, timeSegement);
		// 寫入處理人員
		cedb1000_New.setUpdateIdNo(userId);
		// 儲存EEDB1000(Oracle), 通報及 1010(收發文登打日期..XD）
		logWithTime(prefixVo.getPrefixNo()+",5.寫入異動人員完成,準備update eedb1000---", timeStart, timeSegement);
		
		this.setEedb1000(approve, cedb1000_New);
		logWithTime(prefixVo.getPrefixNo()+",6.update eedb1000完成, 準備寫入案件歷程---", timeStart, timeSegement);
		
		this.set1010(approve, cedb1000_New, cedb1000_Old, userId);
		logWithTime(prefixVo.getPrefixNo()+",7.寫入案件歷程cedb1010完成---", timeStart, timeSegement);
		// 檢查特取名稱
		if (!this.isValidateSpecialName(approve, cedb1000_New)) {
			System.out.println("特取名稱錯誤，特取名稱必須包含於公司名稱之中(Invalidate SpecialName)");
			return null;
		}

		// =====儲存1000====
		// 把抓出來的Cedb1000拿去註冊, 可以註冊完再修改, 亦可修改完再註冊, 此處採用前法
		Cedb1000 cedb1000Clone = cedb1000Dao.findByPrefixNo(cedb1000_Old.getPrefixNo(), null);
		if (cedb1000Clone != null) {
			try {
				BeanUtils.copyProperties(cedb1000Clone, cedb1000_New);
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
		}
		
		logWithTime(prefixVo.getPrefixNo()+",8.準備寫入cedb1001---", timeStart, timeSegement);
		saveCedb1001s(prefixVo.getCedb1001s(), prefixVo.getPrefixNo());
		logWithTime(prefixVo.getPrefixNo()+",9.寫入cedb1001完成, 準備寫入cedb1002---", timeStart, timeSegement);
		saveCedb1002s(prefixVo.getCedb1002s(), prefixVo.getPrefixNo());
		logWithTime(prefixVo.getPrefixNo()+",10.寫入cedb1002完成, 準備寫入cedb1022---", timeStart, timeSegement);
		// =====儲存1022==== (1022的資料, 包含於詳細資料的Form中)
		if (prefixVo.getCedb1022() != null && cedb1022_Old == null) {
			// commons.util.debugMsgs.println("---------cedb1022_Old key值不存在, 做Insert");
			Cedb1022 cedb1022_New = new Cedb1022();
			Cedb1022 cedb1022Clone = cedb1022_New;
			try {
				BeanUtils.copyProperties(cedb1022Clone, prefixVo.getCedb1022());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			cedb1022Clone.setPrefixNo(cedb1000_New.getPrefixNo());
			cedb1022Dao.insert(cedb1022Clone);
		} else {
			// commons.util.debugMsgs.println("---------cedb1022_Old key值存在, 做Update");
			Cedb1022 existingCedb1022 = cedb1022_Old;
			Cedb1022 cedb1022Clone = existingCedb1022;
			if (prefixVo.getCedb1022() != null) {
				try {
					BeanUtils.copyProperties(cedb1022Clone, prefixVo.getCedb1022());
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				} catch (InvocationTargetException e) {
					e.printStackTrace();
				}
				cedb1022Dao.update(cedb1022Clone);
			}
		}
		logWithTime(prefixVo.getPrefixNo()+",11.寫入cedb1022完成, 準備寫入cedb1023---", timeStart, timeSegement);
		// =====儲存1023==== (1023的資料, 包含於詳細資料的Form中)
		if (cedb1023_Old == null) {
			Cedb1023 cedb1023_New = new Cedb1023();
			Cedb1023 cedb1023Clone = cedb1023_New;
			try {
				BeanUtils.copyProperties(cedb1023Clone, prefixVo.getCedb1023());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			cedb1023Clone.setPrefixNo(cedb1000_New.getPrefixNo());
			cedb1023Dao.insert(cedb1023Clone);
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023Clone);
		} else {
			Cedb1023 existingCedb1023 = (Cedb1023) cedb1023_Old;
			Cedb1023 cedb1023Clone = existingCedb1023;
			try {
				BeanUtils.copyProperties(cedb1023Clone, prefixVo.getCedb1023());
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			cedb1023Dao.update(cedb1023Clone);
			//寫入個資軌跡TrackLog(CEDB1000)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023Clone);
		}

		logWithTime(prefixVo.getPrefixNo()+",12.寫入cedb1023完成, 進行備份---", timeStart, timeSegement);
		//備份
		ServiceGetter.getInstance().getBackupService().doBackup(cedb1000_Old.getPrefixNo(), userId);
		logWithTime(prefixVo.getPrefixNo()+",13.備份完成---", timeStart, timeSegement);
		this.setExtend(approve, cedb1000_New, cedb1000_Old, userId);
		
		logWithTime(prefixVo.getPrefixNo()+",14.設定prefixStatus, approveDate/Time, reserveDate---", timeStart, timeSegement);
		//設定預查狀態
		this.setupPrefixStatus(approve, cedb1000_New, cedb1000_Old, userId);
		//審核時需紀錄審核時間
		this.setApproveDateAndTime(approve, cedb1000_New, cedb1000_Old, sDate, sTime);
		//設定保留期限
		this.setReserveDate(approve, cedb1000_New, cedb1000_Old);
		logWithTime(prefixVo.getPrefixNo()+",15.上述設定完成, 準備異動預查主檔cedb1001並寫入個資軌跡---", timeStart, timeSegement);
		//清空[檢還/撤件註記]
		cedb1000_New.setApproveMark("");
		cedb1000Dao.update(cedb1000_New);
		//寫入個資軌跡TrackLog(CEDB1000)
		ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1000_New);
		logWithTime(prefixVo.getPrefixNo()+",16.異動預查主黨與寫入個資軌跡完成, 準備異動陸商註記---", timeStart, timeSegement);
		// 如果是審查，重新檢查陸商營業項目
		if (approve.getFunctionName().equals("approve")) {
			StringBuffer sb = new StringBuffer();
			if (PrefixConstants.APPROVE_RESULT_A.equals(cedb1000_New.getApproveResult())) {
				boolean fChinaCmpy = false;
				Cedb1001 c1001;
				for (Iterator<Cedb1001> iter = cedb1001s_New.iterator(); iter.hasNext();) {
					c1001 = iter.next();
					if (c1001.getCompanyName().startsWith("大陸商")) {
						fChinaCmpy = true;
					}
				}

				if (fChinaCmpy && cedb1002s_New != null) {
					Cedb1002 c1002;
					for (Iterator<Cedb1002> iter = cedb1002s_New.iterator(); iter.hasNext();) {
						c1002 = iter.next();
						if (!Constants.C_CHINA_BUS_ITEM.contains(c1002.getBusiItemNo())) {
							if (sb.length() == 0)
								sb.append("大陸商禁止登記下列營業項目：");
							sb.append("\\n" + c1002.getBusiItemNo() + " " + c1002.getBusiItem());
						}
					}
				}
			}
			approve.setChinaBusitemMark(sb.toString());
		}
		
		logWithTime(prefixVo.getPrefixNo()+",17.陸商註記完成, 寫入同名比對排程---", timeStart, timeSegement);
		// 紙本預先查詢, 只要還沒有審查結果，公司名稱都應該再找最新的同名，因為公司名稱可能被user改變
		if ((cedb1000_New.getApproveResult() == null || PrefixConstants.APPROVE_RESULT_A.equals(cedb1000_New.getApproveResult()))) {
			ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(cedb1000_New.getPrefixNo(), userId);
		}
		
		logWithTime(prefixVo.getPrefixNo()+",18.寫入同名比對排程完成---", timeStart, timeSegement);
		// 變更案如果缺少統編或統編錯誤, 必須有訊息警告

		if (cedb1000_New.getApplyKind() != null && cedb1000_New.getApplyKind().equals("2")) {
			if (cedb1000_New.getBanNo() == null || cedb1000_New.getBanNo().trim().length() != 8) {
				System.out.println("特別注意-變更案存檔成功，但是統編錯誤(Save OK And BanNo Error)");
			}
		}

		// 審核, 如果存成功但是仍然是審查中, 要有訊息警告
		if (approve.getFunctionName().startsWith("approve") &&
				PrefixConstants.APPROVE_RESULT_A.equals(cedb1000_New.getApproveResult()) ) {
			System.out.println("特別注意-存檔成功，但審查結果仍為審查中(Save OK And Approving)");
		}
		
		logWithTime(prefixVo.getPrefixNo()+",19.準備重設closeDate---", timeStart, timeSegement);
		if("Y".equals(approve.getResetCloseDateFlag())) {
			resetCloseDate(prefixVo.getPrefixNo());
		}

		logWithTime(prefixVo.getPrefixNo()+",20.重設closeDate完成---", timeStart, timeSegement);
		saveToDBLog.info(prefixVo.getPrefixNo()+",21.審核作業存檔商業邏輯結束---"+System.currentTimeMillis());

		return cedb1000_New.getPrefixNo();
	}
	
	public int saveCedb1002s(List<Cedb1002> cedb1002s, String prefixNo) {
		
		if(cedb1002s != null && !cedb1002s.isEmpty()) {
			cedb1002Dao.deleteByPrefixNo(cedb1002s.get(0).getPrefixNo());
			return cedb1002Dao.insertBatch(cedb1002s, cedb1002s.size());
		} else {
			return cedb1002Dao.deleteByPrefixNo(prefixNo);
		}
	}
	
	public int saveCedb1001s(List<Cedb1001> cedb1001s, String prefixNo) {
		if(cedb1001s != null && !cedb1001s.isEmpty()) {
			cedb1001Dao.deleteByPrefixNo(prefixNo);
			return cedb1001Dao.insertBatch(cedb1001s, cedb1001s.size());
		} else {
			return cedb1001Dao.deleteByPrefixNo(prefixNo);
		}
	}

	public List<SearchAllData> getEqualSearchAllDatas(List<String> companyNames) throws Exception {
		List<SearchAllData> result = new ArrayList<SearchAllData>();
		// 計時器，避免Querry 時間過長佔住 Request
		java.util.Date querryStart = new java.util.Date();

		List<Cedb2000> firmResult = new ArrayList<Cedb2000>();
		List<Cedb1000> prefixResult = new ArrayList<Cedb1000>();

		List<String> combinations = new ArrayList<String>();
		String firmName;
		Collection<String> sameWordCombinations;
		for (int i = 0; i < companyNames.size(); i++) {
			// 取出每個公司名稱
			// 將公司名稱去除組織型態
			firmName = cleanupString(companyNames.get(i));
			if (firmName.length() < 2) {
				// 只輸入一個字,不予處理
				continue;
			}

			// 取出所有同音同義字的組合
			sameWordCombinations = getSameWordCombinations(getSameWords(firmName));
			// 將所有同音同義字的組合,每個組合都附上四種公司型態
			if(null!=sameWordCombinations && !sameWordCombinations.isEmpty()) {
				String firmname;
				for (Iterator<String> itor = sameWordCombinations.iterator(); itor.hasNext();) {
					firmname = itor.next();

					combinations.add(firmname + "股份有限公司");
					combinations.add(firmname + "有限公司");
					combinations.add(firmname + "無限公司");
					combinations.add(firmname + "兩合公司");
				}
			}
		}

		int eachsize = 5;
		List<String> templist = new ArrayList<String>();
		if (combinations.size() > 0) // 如果是0迴圈應也不會執行 ??
		{
			String o;
			for (int k = 0; k < combinations.size(); k++) {
				o = combinations.get(k);
				templist.add(o);

				if (eachsize == templist.size()) // 這段的用意是 ?? 取前5的倍數才跑?
				{
					// 取出現有公司名稱和任一個同音同義字的組合相同的公司資料
					firmResult.addAll(cedb2000Dao.findBySql(getFirmEqualSQL(templist)));

					// 取出現有預查案件中公司名稱或是特取名稱和任一個同音同義字的組合相同的預查案件
					prefixResult.addAll(cedb1000Dao.findBySql(getPrefixEqualSQL(templist)));

					templist = new ArrayList<String>(); // ?? GC ?
				}
			}
		}

		// 取出現有公司名稱和任一個同音同義字的組合相同的公司資料
		if (!templist.isEmpty())
			firmResult.addAll(cedb2000Dao.findBySql(getFirmEqualSQL(templist)));

		// 取出現有預查案件中公司名稱或是特取名稱和任一個同音同義字的組合相同的預查案件
		if (!templist.isEmpty())
			prefixResult.addAll(cedb1000Dao.findBySql(getPrefixEqualSQL(templist)));

		// 查詢出的公司資料,每一個案件放到一個CEDB2000的Object,再放到SearchAllData物件
		result.addAll(produceFirmSearchAllData(querryStart, firmResult));

		// 查詢出的預查案件,每一個案件放到一個CEDB1000的Object,再放到SearchAllData物件
		result.addAll(producePrefixSearchAllData(querryStart, prefixResult));

		// 去掉不合條件的資料
		result = SearchAllFilter.SearchAllDatasFilter(result); // 有點亂 ??
		return result;
	}

	/**
	 * 將所有符合條件的cedb1000的資料放到Cedb1000再放置到SearchAllData物件對應的欄位
	 * 回傳的Vector對應傳入的Vector rows
	 * 
	 * @param rows
	 *            Vector 含DatabaseRow objects的Vector, 每個DatabaseRow代表一筆Cedb1000
	 *            record
	 * @return Vector
	 * @throws MoeaException
	 */
	private List<SearchAllData> producePrefixSearchAllData(java.util.Date querryStart, List<Cedb1000> rows) {
		List<SearchAllData> result = new ArrayList<SearchAllData>();
		for (Cedb1000 c1000 : rows) {
			result.add(getSearchAllData(true, c1000));
			if ((new java.util.Date().getTime() - querryStart.getTime()) > TimeoutSecs * 1000)
				System.out.println("error.searchall.TOO_MANY_RST");
		}
		return result;
	}

	/**
	 * 設立案的檢還, 進行對資料的查詢動作, 以applyID查詢 因為如果是設立案, 必須把同一申請人的舊案件檢還
	 * 
	 * @param applyId
	 *            String
	 * @param prefixNo
	 *            String
	 * @return Vector
	 */
	public List<SearchAllData> getEqualSearchAllDatasByApplyId(String applyId, String prefixNo) {
		List<Cedb1000> cedb1000s;
		List<SearchAllData> result = new ArrayList<SearchAllData>();
		// 申請人錯誤, 回傳空Vector
		System.out.println("====SameWordBo  getEqualSearchAllDatasByApplyId= applyId:" + applyId + ":prefixNo" + prefixNo);
		if (applyId == null || "".equals(applyId.trim())) {
			return result;
		}
		try {
			// 找出同一申請人,且是設立案的CEDB1000資料
			// 先找同一找出同一申請人
			cedb1000s = cedb1000Dao.findAllByApplyId(applyId);
			if (cedb1000s != null && !cedb1000s.isEmpty()) {
				Cedb1000 cedb1000;
				SearchAllData searchAlldata;
				for (int i = 0; i < cedb1000s.size(); i++) {
					cedb1000 = cedb1000s.get(i);
					// 不包含自己(同一預查編號), 且只取設立案
					if (cedb1000.getPrefixNo() != null && !cedb1000.getPrefixNo().equals(prefixNo)
							&& cedb1000.getApplyKind() != null && "1".equals(cedb1000.getApplyKind())) {
						searchAlldata = new SearchAllData();
						System.out.println("====SameWordBo 不包含自身的同一統編的資料:" + cedb1000.getPrefixNo());
						searchAlldata.setCedb1000(cedb1000);
						result.add(searchAlldata);
					}
				}
			}
		} catch (Exception ex) {
		}
		return result;
	}

	/**
	 * 輔助查詢, 進行對資料的查詢動作, 一次查詢多個公司名稱
	 * 
	 * @param companyNames
	 *            Vector
	 * @throws MoeaException
	 * @throws Exception
	 * @return Vector
	 */
	public List<SearchAllData> getEqualSearchAllDatas(String companyName) throws Exception {
		List<String> namelist = new ArrayList<String>();
		namelist.add(companyName);
		return getEqualSearchAllDatas(namelist);
	}

	/**
	 * 變更案的檢還, 進行對資料的查詢動作, 以統編查詢 因為如果是變更案, 必須把同一統編的舊變更案件檢還
	 * 
	 * @param banNo
	 *            String
	 * @param prefixNo
	 *            String
	 * @return Vector
	 */
	public List<SearchAllData> getEqualSearchAllDatasByBanNo(String banNo, String prefixNo) {
		List<Cedb1000> cedb1000s;
		List<SearchAllData> result = new ArrayList<SearchAllData>();
		// 統編錯誤, 回傳空Vector
		System.out.println("====SameWordBo  getEqualSearchAllDatasByBanNo= banNo:" + banNo + ":prefixNo" + prefixNo);
		if (banNo == null || "".equals(banNo.trim())) {
			return result;
		}
		try {
			// 找出同一統編,且是變更案的CEDB1000資料
			// 先找同一統編
			cedb1000s = cedb1000Dao.findByBanNo(banNo, null);
			if (cedb1000s != null && !cedb1000s.isEmpty()) {
				Cedb1000 cedb1000;
				SearchAllData searchAlldata;
				for (int i = 0; i < cedb1000s.size(); i++) {
					cedb1000 = cedb1000s.get(i);
					// commons.util.debugMsgs.println("====SameWordBo 的 同一統編的資料:"+cedb1000.getPrefixNo());
					// 不包含自己(同一預查編號), 且只取變更案
					if (cedb1000.getPrefixNo() != null && !cedb1000.getPrefixNo().equals(prefixNo)
							&& cedb1000.getApplyKind() != null && "2".equals(cedb1000.getApplyKind())) {
						searchAlldata = new SearchAllData();
						System.out.println("====SameWordBo 不包含自身的同一預查編號的資料:" + cedb1000.getPrefixNo());
						searchAlldata.setCedb1000(cedb1000);
						result.add(searchAlldata);
					}
				}
			}
		} catch (Exception ex) {
		}
		return result;
	}

	/**
	 * 檢查公司名稱是否符合格式
	 * 檢查規則
	 *  1.中間不可有空白
	 *  2.需要是「股份有限公司」、「有限公司」、「無限公司」、「兩合公司」結尾
	 *  3.以上不能重複出現
	 */
	@Override
	public boolean doVerifyCmpyNames(PrefixVo prefixVo) {
		boolean result = true;
		// 檢查公司名稱格式
		List<Cedb1001> cedb1001s = prefixVo.getCedb1001s();
		Cedb1001 cedb1001;
		String companyName;
		for (int i = 0; i < cedb1001s.size(); i++) {
			cedb1001 = cedb1001s.get(i);
			//有審核通過的才要檢查
			if ("Y".equals(cedb1001.getApproveResult())) {
				companyName = cedb1001.getCompanyName();
				// 空白
				if( result && (companyName.indexOf(" ") != -1) ) {
					result = false;
				}
				// 結尾
				if( result ) {
					boolean endFlag = false;
					if(CommonStringUtils.endsWith(companyName, "股份有限公司")) {
						endFlag = true;
					} else if(CommonStringUtils.endsWith(companyName, "有限公司")) {
						endFlag = true;
					} else if(CommonStringUtils.endsWith(companyName, "無限公司")) {
						endFlag = true;
					} else if(CommonStringUtils.endsWith(companyName, "兩合公司")) {
						endFlag = true;
					}
					if(!endFlag) {
						result = false;
					}
				}
				// 不能重複
				if( result ) {
					if(CommonStringUtils.endsWith(companyName, "股份有限公司")) {
						String t_name = companyName.replaceFirst("股份有限公司", "");
						if( t_name.indexOf("有限公司") > -1 ) {
							result = false;
						} else if( t_name.indexOf("無限公司") > -1 ) {
							result = false;
						} else if( t_name.indexOf("兩合公司") > -1 ) {
							result = false;
						}
					} else if(CommonStringUtils.endsWith(companyName, "有限公司")) {
						String t_name = companyName.replaceFirst("有限公司", "");
						if( t_name.indexOf("無限公司") > -1 ) {
							result = false;
						} else if( t_name.indexOf("兩合公司") > -1 ) {
							result = false;
						}
					} else if(CommonStringUtils.endsWith(companyName, "無限公司")) {
						String t_name = companyName.replaceFirst("無限公司", "");
						if( t_name.indexOf("有限公司") > -1 ) {
							result = false;
						} else if( t_name.indexOf("兩合公司") > -1 ) {
							result = false;
						}
					} else if(CommonStringUtils.endsWith(companyName, "兩合公司")) {
						String t_name = companyName.replaceFirst("兩合公司", "");
						if( t_name.indexOf("有限公司") > -1 ) {
							result = false;
						} else if( t_name.indexOf("無限公司") > -1 ) {
							result = false;
						}
					}
				}
			}
		}
		return result;
	}

	@Override
	public boolean doVerifyBusiItem(PrefixVo prefixVo, String functionName) {
		List<Cedb1001> cedb1001s_New = new ArrayList<Cedb1001>(prefixVo.getCedb1001s());
		List<Cedb1002> cedb1002s_New = new ArrayList<Cedb1002>(prefixVo.getCedb1002s());
		Cedb1023 cedb1023_New = prefixVo.getCedb1023();

		try {

			if (cedb1002s_New.size() == 1) {
				Cedb1002 cedb1002New = cedb1002s_New.get(0);
				if (cedb1002New.getBusiItem() == null || cedb1002New.getBusiItem().equals("")) {
					cedb1002s_New.remove(0);
				}
			}

			for (int i = 0; i < cedb1001s_New.size(); i++) {
				Cedb1001 c1001 = cedb1001s_New.get(i);

				// 有審核結果

				if ("Y".equals(c1001.getApproveResult()) ||
				// 非不予核准並且非承辦審核功能
						(!"N".equals(c1001.getApproveResult()) && !functionName.startsWith("approve"))) {
					// 非僅變更公司名稱
					if (!"1".equals(cedb1023_New.getChangeType())) {
						if (cedb1002s_New.isEmpty())
							return false;

						if (cedb1002s_New.size() == 1) {
							Cedb1002 cedb1002New = cedb1002s_New.get(0);
							if ("ZZ99999".equals(cedb1002New.getBusiItemNo()))
								return false;
						}
					}
					break;
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 審核畫面用; 從PREFIX_NO開始取得10筆,100筆,1000筆 或 10000筆(1開頭, 0結尾), 以MAP回傳回來
	 * PREFIX_NO必須小於9位數(含), 大於5位數(含)
	 * 
	 * @param prefixNo
	 *            String 預查偏號
	 * @return Map
	 * <AUTHOR>
	 */
	@Override
	public TreeMap<String, PrefixVo> getQueryResultByPrefixNo(String prefixNo, boolean isAppendTenRecs, String functionName, String approveResult) {

		final int iPREFIX_NO_LEN = 9;

		TreeMap<String, PrefixVo> caseDatas = new TreeMap<String, PrefixVo>();
		String pno = "";
		BigInteger bInt, bIntPno;
		int receiveRecords = 0;
		// 預查編號長度在5~8時的處理
		if (receiveRecords < 1 && prefixNo.length() > 4 && prefixNo.length() < 9) {
			pno = StringUtility.padR(prefixNo, "0", iPREFIX_NO_LEN - 1) + "1";
			bInt = new BigInteger("10", 10);
			bInt = bInt.pow(iPREFIX_NO_LEN - prefixNo.length());
			receiveRecords = bInt.intValue();
			// 多找十筆的選項
			if (isAppendTenRecs)
				receiveRecords = receiveRecords + 10;
		} // 預查編號長度等於9時的處理
		else if (prefixNo.length() == 9) {
			receiveRecords = 1;
			pno = prefixNo;
		} else {
			System.out.println("PREFIX_NO INVALID LENGTH");
		}

		// 預查編號小於9位數的做模糊查詢
		if (logger.isInfoEnabled())
			logger.info("range=:" + receiveRecords);
		if (prefixNo.length() < iPREFIX_NO_LEN) {
			bIntPno = new BigInteger(pno, 10);
			for (int i = 0; i < receiveRecords; i++) {
				if (bIntPno.toString().length() < iPREFIX_NO_LEN) {
					pno = StringUtility.padL(bIntPno.toString(), "0", iPREFIX_NO_LEN);
				} else {
					pno = bIntPno.toString();
				}
				Cedb1000 cedb1000;
				// 查不到資料的處理
				if ("atonce".equals(functionName)) {
					cedb1000 = cedb1000Dao.findReadByPrefixNoForAtonce(pno);
				} else {
					cedb1000 = cedb1000Dao.findByPrefixNo(pno, approveResult);
				}

				if (cedb1000 == null) {
					bIntPno = bIntPno.add(new BigInteger(Integer.toString(1)));
					continue;
				} else {
					cedb1000.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1000.getPrefixNo()));
				}

				PrefixVo caseData = ServiceGetter.getInstance().getPrefixService().findPrefixData(cedb1000.getPrefixNo(), approveResult);
				caseData.setUpdateName(getStaffNameByIdNo(cedb1000.getUpdateIdNo()));
				caseData.setApplyKind(cedb1000.getApplyKind());
				// 設定CaseData中的收件人資料(CEDB1023), 公司主檔現況名稱(CEDB2000), 審核意見歷史資料
				// setCaseDataOtherDatas(caseData); 用 service 一次包好
				// 放入TreeMap
				caseDatas.put(pno, caseData);
				// 每次+1
				bIntPno = bIntPno.add(new BigInteger(Integer.toString(1)));
			}
		}
		// 預查編號9位數時, 只需查一筆
		else if (prefixNo.length() == iPREFIX_NO_LEN) {
			
			PrefixVo caseData = null;
			int i = 0;
			if ("approve-change-record".equals(functionName)) {
				List<Cedb1006> cedb1006s = cedb1006Dao.findByPrefixNo(prefixNo);
				for (Cedb1006 cedb1006 : cedb1006s) {
					caseData = new PrefixVo();
					cedb1006.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1006.getApplyKind()));
					try {
						BeanUtils.copyProperties(caseData, cedb1006);
					    caseData.setApproveResult("Y".equals(cedb1006.getApproveResult())?"核准保留":"N".equals(cedb1006.getApproveResult())?"不予核准":"A".equals(cedb1006.getApproveResult())?"審查中":"");
					    caseData.setUpdateName(getStaffNameByIdNo(cedb1006.getUpdateIdNo()));
					} catch (IllegalAccessException e) {
						e.printStackTrace();
					} catch (InvocationTargetException e) {
						e.printStackTrace();
					}

					caseDatas.put(pno + i++, caseData);
				}
			} else if("atonce".equals(functionName)) {
				Cedb1000 cedb1000 = cedb1000Dao.findReadByPrefixNoForAtonce(pno);
				if(cedb1000 != null) {
					caseData = ServiceGetter.getInstance().getPrefixService().findPrefixData(cedb1000.getPrefixNo(), approveResult);
					if (caseData.getPrefixNo() != null) {
						caseData.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(caseData.getPrefixNo()));
						caseDatas.put(pno, caseData);
					}
				}
			} else  {
				caseData = ServiceGetter.getInstance().getPrefixService().findPrefixData(pno, approveResult);
				if (caseData.getPrefixNo() != null) {
					caseData.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(caseData.getPrefixNo()));
					caseDatas.put(pno, caseData);
				}
			}

		}
		return caseDatas;
	}

	@Override
	public synchronized String doAssign(String userId, String userName) {
		assignLog.info("分文作業開始---"+System.currentTimeMillis());
		String prefixNo = "";
		try {
			// 1.取一筆尚未分文案件
			assignLog.info("取第一筆待分文案件---"+System.currentTimeMillis());
			prefixNo = cedb1000Dao.findPrefixNoForAssign();
			if (null != prefixNo) {
				// 2.備份
				assignLog.info("取待分文案件結束, 取到的是"+prefixNo+", 備份至cedb1006---"+System.currentTimeMillis());
				ServiceGetter.getInstance().getBackupService().doBackup(prefixNo, userId);
				assignLog.info("備份結束, 進行分文---"+System.currentTimeMillis());
				// 3.分文
				cedb1000Dao.updateAssignData(prefixNo, userId, userName);
				assignLog.info("分文結束, 寫入案件歷程---"+System.currentTimeMillis());
				// 4.寫入案件歷程
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(prefixNo, userId, PrefixConstants.PREFIX_STATUS_3);
				assignLog.info("寫入案件歷程結束, 新增同名比對排程---"+System.currentTimeMillis());
				// 5.新增 同名比對排程
				ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(prefixNo, userId);
				assignLog.info("新增同名比對排程結束---"+System.currentTimeMillis());
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return prefixNo;
	}

	/**
	 * 查詢已分文件數
	 */
	@Override
	public Integer getDistributedCountByDayForUser(String idNo) {
		Integer result = 0;
		result = cedb1000Dao.doReadDistributedCountByDayForUser(idNo);

		if (result != null) {
			return result;
		}

		return 0;
	}

	@Override
	public void insertCedb1002s(List<Cedb1002> cedb1002s) {
		for (Cedb1002 cedb1002 : cedb1002s) {
			if (cedb1002Dao.findByPrefixNoAndSeqNo(cedb1002.getPrefixNo(), cedb1002.getSeqNo()) == null) {
				cedb1002Dao.insert(cedb1002);
			} else {
				cedb1002Dao.update(cedb1002);
			}
		}
	}

	@Override
	public List<Cedbc000> queryAllcedbc000s() {
		return cedbc000Dao.queryAll();
	}

	/**
	 * 根據公司統編, 查詢 2000 及 1000的資料
	 * 
	 * @param banNo
	 *            String
	 * @return Vector
	 * <AUTHOR>
	 */
	@Override
	public List<UpdateHistoryData> getUpdateHistoryDatas(String banNo) {
		// 由UpdateHistoryDatas組成的Vector, 存放查到的異動資料
		List<UpdateHistoryData> updateHistoryDatas = new ArrayList<UpdateHistoryData>();
		List<Cedb1000> cedb1000s;
		Cedb2000 cedb2000;// 2000中找到的同統編公司
		Cedb1000 cedb1000;
		UpdateHistoryData updateHistoryData;
		// 查2000的異動內容
		cedb2000 = cedb2000Dao.findByBanNo(banNo);
		if (cedb2000 != null) {
			// cedb2000.setStatusDescr(statusDescr);
			updateHistoryData = new UpdateHistoryData();
			updateHistoryData.setCedb2000(cedb2000);
			// 放入Vector searchAllDatas中
			updateHistoryDatas.add(updateHistoryData);
		}

		// 查1000的異動內容
		cedb1000s = cedb1000Dao.findByBanNo(banNo, null);
		for (int i = 0; i < cedb1000s.size(); i++) {
			updateHistoryData = new UpdateHistoryData();
			cedb1000 = cedb1000s.get(i);
			cedb1000.setApproveResult(ServiceGetter.getInstance().getSystemCode05Loader()
					.getCodeNameByCode(cedb1000.getApproveResult()));
			try {
				cedb1000.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(cedb1000.getPrefixNo()));
			} catch (NumberFormatException e) {
				cedb1000.setApplyKind(""); // 沒有applyKind資料時直接設為空字串
			}
			updateHistoryData.setCedb1000(cedb1000);
			// 放入Vector searchAllDatas中
			updateHistoryDatas.add(updateHistoryData);
		}
		// 不會回傳 null, 但查不到資料時回傳的Vector有可能是Empty
		return updateHistoryDatas;
	}

	// 寫入審核的案件過程的1010--給馬上辦用的
	public void saveTo1010Atonce(Cedb1000 cedb1000_New, String idNo, String processStatus) {

		String prefixNo = cedb1000_New.getPrefixNo();
		// 讀不到結案的那筆cedb1010，要離開.
		if (cedb1010Dao.findByPrefixNoAndStatus(prefixNo, "8") == null) {
			return;
		}

		// 取得系統日期時間, 如0931120 142005
		String sDate = Datetime.getYYYMMDD();
		String sTime = Datetime.getHHMMSS();

		// 製作已審核的1010
		Cedb1010 cedb1010 = new Cedb1010();
		cedb1010.setPrefixNo(cedb1000_New.getPrefixNo());
		cedb1010.setIdNo(idNo);
		cedb1010.setProcessDate(sDate);
		cedb1010.setProcessTime(sTime);
		cedb1010.setProcessStatus(processStatus);
		// 計算工作天數
		// WorkerDayHelper wdh = new WorkerDayHelper();
		double workDay = 0.0; // = wdh.getWorkDayHour(cedb1010Last, cedb1010);
		Double dWorkDay = new Double(workDay);
		cedb1010.setWorkDay(dWorkDay.floatValue());

		cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, processStatus);

		if (cedb1010 != null) {
			// 做update
			if (logger.isInfoEnabled())
				logger.info("XXXX  ------1010做Update!!");

			Cedb1010 cedb1010Clone = new Cedb1010();
			cedb1010Clone.setPrefixNo(prefixNo);
			cedb1010Clone.setIdNo(idNo);
			cedb1010Clone.setProcessDate(sDate);
			cedb1010Clone.setProcessTime(sTime);
			cedb1010Clone.setProcessStatus(processStatus);
			cedb1010Clone.setWorkDay(dWorkDay.floatValue());
			cedb1010Dao.updateByPrefixNoAndStatus(cedb1010Clone);
		} else {
			// 做Insert
			if (logger.isInfoEnabled())
				logger.info("XXXX  ------1010做Insert!!");
			cedb1010Dao.insert(cedb1010);

		}
	}

	/**
	 * 重設發文；已發文的案件，點選後本案件變為已審核未發文的狀態
	 * @param prefixNo
	 * @return
	 */
	@Override
	public String resetCloseDate(String prefixNo) {

		try {
			cedb1000Dao.resetCloseDate(prefixNo);
			cedb1010Dao.deleteByPrefixNoAndStatus(prefixNo, "6");
			cedb1010Dao.deleteByPrefixNoAndStatus(prefixNo, "7");
			return "";
		} catch (Exception e) {
			e.printStackTrace();
			return "error";
		}
	}
	
	/**
	 * 查詢檢還清單
	 * @return
	 */
	@Override
	public List<Cedb1000> findWithdraw(String applyId, String prefixNo) {
		if(applyId == null || prefixNo == null) return new ArrayList<Cedb1000>(); 
		return cedb1000Dao.findByApplyIdForWithdraw(applyId, prefixNo);
	}
	
	/**
	 * 查詢檢還清單
	 * @return
	 */
	@Override
	public List<Cedb1000> findWithdrawWithIdAndCompanyName(String applyId, String companyName, String prefixNo) {
		if(applyId == null || prefixNo == null) return new ArrayList<Cedb1000>(); 
		return cedb1000Dao.findByApplyIdAndCompanyNameForWithdraw(applyId, companyName, prefixNo);
	}
	
	/**
	 * 執行檢還所執行的動作
	 * @param prefixNos
	 * @return
	 */
	@Override
	public List<String> doWithdraw(String prefixNo, List<String> pnos, String withdrawType) throws Exception {
		String userId = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser().getUserId();
		String userName = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser().getUserName();

		StringBuilder sb = new StringBuilder();
		String status = "";
		Cedb1000 cedb1000;
		for(String pno : pnos) {
			cedb1000 = cedb1000Dao.findByPrefixNo(pno, null);
			
			if(cedb1000 == null)
				continue;
			
			//remark wording
			if ( "01".equals(withdrawType) ) {
				sb.append(DateTimeFormatter.getSystemDate()).append("因檢還").append(userName).append("修改核覆結果:").append(prefixNo);
				status = PrefixConstants.PREFIX_STATUS_9;  //檢還
				cedb1000.setApproveMark("01"); //(01:檢還)
			} else if( "02".equals(withdrawType) ) {
				sb.append(DateTimeFormatter.getSystemDate()).append("因撤件").append(userName).append("修改核覆結果:").append(prefixNo);
				status = PrefixConstants.PREFIX_STATUS_A; //撤件
				cedb1000.setApproveMark("02"); //(02:撤件)
			}
			
			cedb1000.setUpdateIdNo(userId);
			cedb1000.setApproveResult("N");
			cedb1000.setUpdateDate(DateTimeFormatter.getSystemDate());
			cedb1000.setUpdateTime(DateTimeFormatter.getSystemTime());
			cedb1000.setRemark(sb.toString());
			cedb1000.setReserveDate(null);
			cedb1000.setPrefixStatus(status);
			cedb1000.setCompanyName(null);
			
			/**
			 * QA10904160037: 針對在【審核-預查審核-資料檢核】(pre3001_07.jsp)進行「撤件」的案件, 補回寫至一站式或一維條碼系統
			 * 	這段參考自com.kangdainfo.tcfi.view.pre.PRE3005.doWithdraw()
			 */			
			if ( !"".equals(Common.get(cedb1000.getTelixNo())) && ("A".equals(status) || "E".equals(status)) ) {
	        	if ( Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS")) {
	        		// 介接回寫一站式
	        		ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(cedb1000.getPrefixNo(), userId);
	        	} else {
	        		// 回寫公司線上申辦
	        		Eedb1000 eedb1000 = ServiceGetter.getInstance().getPre3005Service().getEedb1000ByTelixNo(cedb1000.getTelixNo());
	        		if(eedb1000 != null){
	            		eedb1000.setApproveResult("5");
	            		eedb1000.setProcessStatus("ZZ");
	            		eedb1000.setApproveDate(Datetime.getYYYMMDD());
	            		eedb1000.setApproveTime(Datetime.getHHMMSS());
	            		eedb1000.setApproveMail("");
	            		eedb1000DaoEicm.update(eedb1000);
	        		}
	        	}
	        }
			
			//更新公司名稱
			for(Cedb1001 cedb1001: cedb1001Dao.findByPrefixNo(pno)) {
				cedb1001.setApproveResult("N");
				cedb1001Dao.update(cedb1001);
			}
			//更新主檔
			cedb1000Dao.update(cedb1000);
			//寫案件歷程
			ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(prefixNo, userId, status);
			//介接投審會
			ServiceGetter.getInstance().getMoeaicApproveService().notifyMoeaic(pno);
		}
		return pnos;
	}
	
	/** 智慧型預查回傳預查審核結果 */
	public Boolean doPreSearchVerified(String prefixNo) {
		Boolean result = new Examine().setPreVerifiedResult(prefixNo);
		if(logger.isInfoEnabled()) logger.info("[preSearch][prefixNo:" + prefixNo + "回傳智慧型預查審核結果" + (result ? "成功" : "失敗") + "]");
		return result;
	}
	
	public Cedbc000 selectCedbc000ById(String idNo) {
		return cedbc000Dao.findByIdNo(idNo);
	}

	public void convertJsonToObject(PrefixVo prefixVo) {
//		Type type = new TypeToken<List<Cedb1001>>() {}.getType();
//		prefixVo.setCedb1001s((List) gson.fromJson(prefixVo.getCedb1001s().toString(), type));
		
		Type typeOfCedb1001s = new TypeToken<List<Cedb1001>>(){}.getType();
		String json1001s = gson.toJson(prefixVo.getCedb1001s(), typeOfCedb1001s);
		List<Cedb1001> cedb1001s = gson.fromJson(json1001s, typeOfCedb1001s);
		prefixVo.setCedb1001s(cedb1001s);
		
		Type typeOfCedb1002s = new TypeToken<List<Cedb1002>>(){}.getType();
		String json1002s = gson.toJson(prefixVo.getCedb1002s(), typeOfCedb1002s);
		List<Cedb1002> cedb1002s = gson.fromJson(json1002s, typeOfCedb1002s);
		prefixVo.setCedb1002s(cedb1002s);
	}

	// 傳入idNo,return StaffName
	public String getStaffNameByIdNo(String idNo) {
		Cedbc000 cedbc000 = cedbc000Dao.findByIdNo(idNo);
	    if (cedbc000!=null)
	    	return cedbc000.getStaffName();
	    else
	    	return idNo;
	}

	public List<Eedb1002> getEedb1002ByPrefixNo(String prefixNo) {
		return eedb1002Dao.findByPrefixNo(prefixNo);
	}

	public List<Eedb1002> getEedb1002ByTelixNo(String telixNo) {
		return eedb1002Dao.findByTelixNo(telixNo);
	}

	public Cedbc000 getCedbc000ByIdNoAndPwd(String idNo, String pwd) {
		return cedbc000Dao.findByIdNoAndPwd(idNo, pwd);
	}

	public Cedbc000 getCedbc000ByIdNo(String idNo) {
		return cedbc000Dao.findByIdNo(idNo);
	}

	public Cedb1000 getCedb1000ByPrefixNo(String prefixNo) {
		return cedb1000Dao.findByPrefixNo(prefixNo, null);
	}

	public Cedb1000 getCedb1000ByTelixNo(String telixNo) {
		return cedb1000Dao.findByTelixNo(telixNo, null);
	}

	public List<Cedb1001> getCedb1001ByPrefixNo(String prefixNo) {
		return cedb1001Dao.findByPrefixNo(prefixNo);
	}

	public Eedb1000 getEedb1000ByTelixNo(String telixNo) {
		return eedb1000DaoEicm.findByTelixNo(telixNo);
	}

	public Eedb1100 getEedb1100ByTelixNo(String telixNo) {
		return eedb1100Dao.findByTelixNo(telixNo);
	}

	public Eedb3300 getEedb3300ByTelixNo(String telixNo) {
		return eedb3300Dao.findByTelixNo(telixNo);
	}

	public Eedb5000 getEedb5000ByTelixNo(String telixNo) {
		return eedb5000Dao.findByTelixNo(telixNo);
	}

	public List<EedbV8000> getEedbV8000ByTelixNo(String telixNo) {
		return eedbV8000Dao.findByTelixNo(telixNo);
	}

	public OssmApplMain getOssmApplMainByTelixNo(String telixNo) {
		return ossmApplMainDao.findByTelixNo(telixNo);
	}

	public OssmApplFlow getOssmApplFlowByTelixNoAndProcessNo(String telixNo, String processNo) {
		return ossmApplFlowDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}

	public OssmFeeMain getOssmFeeMainByTelixNoAndProcessNo(String telixNo, String processNo) {
		return ossmFeeMainDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}

	public OssmFeeDetail getOssmFeeDetailByTelixNoAndProcessNo(String telixNo, String processNo) {
		return ossmFeeDetailDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}

	public List<Cedb1002> getCedb1002ByPrefixNo(String prefixNo) {
		return cedb1002Dao.findByPrefixNo(prefixNo);
	}

	public Cedb1002 getCedb1002ByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		return cedb1002Dao.findByPrefixNoAndSeqNo(prefixNo, seqNo);
	}

	public List<Cedb1002> getCedb1002ByBanNo(String banNo) {
		return cedb1002Dao.findByBanNo(banNo);
	}

	public Cedb2000 getCedb2000ByBanNo(String banNo) {
		return cedb2000Dao.findByBanNo(banNo);
	}

	public List<Cedb2002> getCedb2002ByBanNo(String banNo) {
		return cedb2002Dao.findByBanNo(banNo);
	}

	public List<Cedb2004> getCedb2004ByBanNo(String banNo) {
		return cedb2004Dao.findByBanNo(banNo);
	}

	public void saveCedb1017(Cedb1017 obj) {
		cedb1017Dao.update(obj);
	}

	public String getIssueKeyinDateTime(String prefixNo) {
		return cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_7);
	}

	public String getReceiveKeyinDateTime(String prefixNo) {
		return cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_2);
	}

	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}

	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}

	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}

	public Cedb2004Dao getCedb2004Dao() {return cedb2004Dao;}
	public void setCedb2004Dao(Cedb2004Dao dao) {this.cedb2004Dao = dao;}

	public Cedb1019Dao getCedb1019Dao() {return cedb1019Dao;}
	public void setCedb1019Dao(Cedb1019Dao dao) {this.cedb1019Dao = dao;}

	public Cedb1017Dao getCedb1017Dao() {return cedb1017Dao;}
	public void setCedb1017Dao(Cedb1017Dao dao) {this.cedb1017Dao = dao;}

	public ReceiveDao getReceiveDao() {return receiveDao;}
	public void setReceiveDao(ReceiveDao dao) {this.receiveDao = dao;}

	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}

	public Cedbc000Dao getCedbc000Dao() {return cedbc000Dao;}
	public void setCedbc000Dao(Cedbc000Dao dao) {this.cedbc000Dao = dao;}

	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}

	public OssmApplMainDao getOssmApplMainDao() {return ossmApplMainDao;}
	public void setOssmApplMainDao(OssmApplMainDao dao) {this.ossmApplMainDao = dao;}

	public OssmOrgNameDao getOssmOrgNameDao() {return ossmOrgNameDao;}
	public void setOssmOrgNameDao(OssmOrgNameDao dao) {this.ossmOrgNameDao = dao;}

	public OssmBussItemDao getOssmBussItemDao() {return ossmBussItemDao;}
	public void setOssmBussItemDao(OssmBussItemDao dao) {this.ossmBussItemDao = dao;}

	public Cedb1009Dao getCedb1009Dao() {return cedb1009Dao;}
	public void setCedb1009Dao(Cedb1009Dao dao) {this.cedb1009Dao = dao;}

	public Cedbc055Dao getCedbc055Dao() {return cedbc055Dao;}
	public void setCedbc055Dao(Cedbc055Dao dao) {this.cedbc055Dao = dao;}

	public Cedb1003Dao getCedb1003Dao() {return cedb1003Dao;}
	public void setCedb1003Dao(Cedb1003Dao dao) {this.cedb1003Dao = dao;}

	public Cedb1011Dao getCedb1011Dao() {return cedb1011Dao;}
	public void setCedb1011Dao(Cedb1011Dao dao) {this.cedb1011Dao = dao;}

	public Eedb3100Dao getEedb3100Dao() {return eedb3100Dao;}
	public void setEedb3100Dao(Eedb3100Dao dao) {this.eedb3100Dao = dao;}

	public Cedb1004Dao getCedb1004Dao() {return cedb1004Dao;}
	public void setCedb1004Dao(Cedb1004Dao dao) {this.cedb1004Dao = dao;}

	public Eedb1000Dao getEedb1000DaoEicm() {return eedb1000DaoEicm;}
	public void setEedb1000DaoEicm(Eedb1000Dao dao) {this.eedb1000DaoEicm = dao;}

	public Eedb1100Dao getEedb1100Dao() {return eedb1100Dao;}
	public void setEedb1100Dao(Eedb1100Dao dao) {this.eedb1100Dao = dao;}

	public Eedb3000Dao getEedb3000Dao() {return eedb3000Dao;}
	public void setEedb3000Dao(Eedb3000Dao dao) {this.eedb3000Dao = dao;}

	public Eedb3300Dao getEedb3300Dao() {return eedb3300Dao;}
	public void setEedb3300Dao(Eedb3300Dao dao) {this.eedb3300Dao = dao;}

	public Eedb5000Dao getEedb5000Dao() {return eedb5000Dao;}
	public void setEedb5000Dao(Eedb5000Dao dao) {this.eedb5000Dao = dao;}

	public EedbV8000Dao getEedbV8000Dao() {return eedbV8000Dao;}
	public void setEedbV8000Dao(EedbV8000Dao dao) {this.eedbV8000Dao = dao;}

	public OssmOrgChangeDao getOssmOrgChangeDao() {return ossmOrgChangeDao;}
	public void setOssmOrgChangeDao(OssmOrgChangeDao dao) {this.ossmOrgChangeDao = dao;}

	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}

	public OssmApplFlowDao getOssmApplFlowDao() {return ossmApplFlowDao;}
	public void setOssmApplFlowDao(OssmApplFlowDao dao) {this.ossmApplFlowDao = dao;}

	public OssmFeeMainDao getOssmFeeMainDao() {return ossmFeeMainDao;}
	public void setOssmFeeMainDao(OssmFeeMainDao dao) {this.ossmFeeMainDao = dao;}

	public OssmFeeDetailDao getOssmFeeDetailDao() {return ossmFeeDetailDao;}
	public void setOssmFeeDetailDao(OssmFeeDetailDao dao) {this.ossmFeeDetailDao = dao;}

	public Eedb1002Dao getEedb1002Dao() {return eedb1002Dao;}
	public void setEedb1002Dao(Eedb1002Dao dao) {this.eedb1002Dao = dao;}

	public Cedbc004Dao getCedbc004Dao() {return cedbc004Dao;}
	public void setCedbc004Dao(Cedbc004Dao dao) {this.cedbc004Dao = dao;}

	public Cedbc053Dao getCedbc053Dao() {return cedbc053Dao;}
	public void setCedbc053Dao(Cedbc053Dao dao) {this.cedbc053Dao = dao;}

	public Cedb1006Dao getCedb1006Dao() {return cedb1006Dao;}
	public void setCedb1006Dao(Cedb1006Dao dao) {this.cedb1006Dao = dao;}

	public Cedb1007Dao getCedb1007Dao() {return cedb1007Dao;}
	public void setCedb1007Dao(Cedb1007Dao dao) {this.cedb1007Dao = dao;}

	public Cedb1008Dao getCedb1008Dao() {return cedb1008Dao;}
	public void setCedb1008Dao(Cedb1008Dao dao) {this.cedb1008Dao = dao;}

	public Cedb1100Dao getCedb1100Dao() {return cedb1100Dao;}
	public void setCedb1100Dao(Cedb1100Dao dao) {this.cedb1100Dao = dao;}

}