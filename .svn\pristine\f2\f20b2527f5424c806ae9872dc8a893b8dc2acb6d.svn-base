<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/transitional.dtd">  
<!--
程式目的：一站式案件查詢
程式代號：PRE4020
撰寫日期：103.12.04
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4020" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4020">
	<jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>
<%
if ("init".equals(obj.getState())) {
	//初始值
} else if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE4020"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<script>
function validateInput() {
	var hasInput = false;
	if( ''!=$('#q_prefixNo').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_telixNo').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_applyId').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_applyName').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_banNo').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_companyName').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_userid').val() ) {
		hasInput = true;
	}
	return hasInput;
}

$( document ).ready(function() {
	//事件註冊
	$('#q_prefixNo').keydown(function(e){
		if ( e.which == 13 ) {
			if( this.value.length >= 5 ) {
				e.preventDefault();
				$('#doSearch').click();
			} else {
				showMsgBar('預查編號太短，請確認後再執行查詢!');
				$("#listContainer").hide();
			}
		}
	});
	$('#q_telixNo, #q_applyId, #q_applyName, #q_banNo, #q_companyName, #q_userid').keydown(function(e){
		if ( e.which == 13 ) {
			if( this.value.length > 0 ) {
				e.preventDefault();
				$('#doSearch').click();
			}
		}
	});
	//重新輸入
	$('#resetForm').click(function(){
		$('#q_prefixNo, #q_telixNo, #q_applyId, #q_applyName, #q_banNo, #q_companyName, #q_userid').val('');
		showMsgBar('&nbsp;');
		$("#listContainer").hide();
	});
	//執行查詢
	$('#doSearch').click(function(){
		if( validateInput() ) {
			form1.state.value = "queryAll";
			form1.submit();
		} else {
			showMsgBar('未輸入任何查詢條件，請確認後再執行查詢!');
			$("#listContainer").hide();
		}
	});
	//全選
	$("#checkAll").click(function(){
		commonUtils.all("telixNos");
	});
	//全不選
	$("#uncheckAll").click(function(){
		commonUtils.unAll("telixNos");
	});
	//確認送出
	$("#showDetail").click(function(){
		var $checks = $("input[name=telixNos]").filter(":checked");
		if( $checks.size() > 0) {
			form1.state.value = "init";
			form1.action = getVirtualPath() + "tcfi/pre/pre4020_00.jsp";
			form1.submit();
		}
	});

<% if( null!=objList && objList.size() == 1 ) { %>
//只有一筆資料時, 直接顯示明細
commonUtils.all("telixNos");
$("#showDetail").click();
<% } %>

<% if ("true".equals(obj.getQueryAllFlag())){ %>
//執行查詢以後才顯示清單
$("#listContainer").show();
<% } %>

$("#checkAll").click();
});

function queryOne(v) {
}
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE4020'/>
</c:import>

<!-- TOOLBAR AREA -->
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="left">
			<input class="toolbar_default" type="button" id="resetForm" name="resetForm" value="重新輸入" />&nbsp;
			<input class="toolbar_default" type="button" id="doSearch" name="doSearch" value="執行查詢" />&nbsp;
		</td>
		<td align="right">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
			</c:import>
		</td>
	</tr>
</table>
<!-- TOOLBAR AREA -->

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr >
		<td class="td_form" width="140px">預查編號</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_prefixNo" name="q_prefixNo" size="15" maxlength="9" value="<%=obj.getQ_prefixNo() %>" />
		</td>
		<td class="td_form" width="100px">電子流水號</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_telixNo" name="q_telixNo" size="25" maxlength="16" value="<%=obj.getQ_telixNo() %>" onblur="toUpper(this);"/>
		</td>
	</tr>
	<tr >
		<td class="td_form">申請人身分ID</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_applyId" name="q_applyId" size="15" value="<%=obj.getQ_applyId() %>" onblur="toUpper(this);"/>
		</td>
		<td class="td_form">申請人姓名</td>
		<td class="td_form_white">
			<input type="text" class="field cmex" id="q_applyName" name="q_applyName" size="25" value="<%=obj.getQ_applyName() %>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">統一編號</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_banNo" name="q_banNo" size="15" maxlength="8" value="<%=obj.getQ_banNo() %>" />
		</td>
		<td class="td_form">預查名稱</td>
		<td class="td_form_white">
			<input type="text" class="field cmex" id="q_companyName" name="q_companyName" size="50" value="<%=obj.getQ_companyName() %>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">網路申請帳號</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field" id="q_userid" name="q_userid" size="15" value="<%=obj.getQ_userid() %>" />
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="bgList">
	<div id="listContainer" style="display:none;">
	<table>
		<tr><td>
			<input class="toolbar_default" type="button" id="checkAll" name="checkAll" value="全部選取">&nbsp;
			<input class="toolbar_default" type="button" id="uncheckAll" name="uncheckAll" value="取消選取">&nbsp;
			<input class="toolbar_default" type="button" id="showDetail" name="showDetail" value="確認送出">&nbsp;
		</td></tr>
	</table>
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
  		    <th class="listTH" width="3%">&nbsp;</th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">預查編號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查種類</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查名稱</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">申請人</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">網路流水號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">核覆結果</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">保留期限</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = {false,false,false,false, true,false,false};
  			boolean displayArray[] = { true, true, true, true, true, true, true};
  			String[] alignArray = {"left","left","left","left","left","left","left"};
  			out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,"true","telixNos"));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>