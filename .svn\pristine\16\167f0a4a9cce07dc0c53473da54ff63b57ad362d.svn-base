package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;

public class Cedb2000Dao extends BaseDaoJdbc implements RowMapper<Cedb2000> {

	private static String sql_findByBanNo = "SELECT"
			+ "  ban_no"
			+ " ,reg_unit"
			+ " ,reg_unit_name"
			+ " ,sub_reg_unit"
			+ " ,orgn_type"
			+ " ,(decode(orgn_type,'07','外國公司登記','08','外國公司辦事處登記',orgn_name)) as orgn_name"
			+ " ,busi_type"
			+ " ,Busi_Type_Name"
			+ " ,owner_type"
			+ " ,company_name"
			+ " ,part_name"
			+ " ,prefix_no"
			+ " ,tel_no"
			+ " ,area_code"
			+ " ,zone_code"
			+ " ,company_addr"
			+ " ,capital_amt"
			+ " ,investment_code"
			+ " ,tot_dir"
			+ " ,dir_beg_date"
			+ " ,dir_end_date"
			+ " ,tot_sup"
			+ " ,sup_beg_date"
			+ " ,sup_end_date"
			+ " ,Resp_Name"
			+ " ,status_code"
			+ " ,suspend_beg_date"
			+ " ,suspend_end_date"
			+ " ,setup_date"
			+ " ,approve_word"
			+ " ,approve_no"
			+ " ,change_date"
			+ " ,change_word"
			+ " ,change_no"
			+ " ,fiscal_date"
			+ " ,attor_name"
			+ " ,attor_id"
			+ " ,account_name"
			+ " ,account_id"
			+ " ,tot_branch"
			+ " ,old_ban_no"
			+ " ,old_company_name"
			+ " ,control_item"
			+ " ,case_status"
			+ " ,stock_type"
			+ " ,tot_stock"
			+ " ,stock_amt"
			+ " ,bond_stock"
			+ " ,real_amt"
			+ " ,update_user"
			+ " ,Update_Date"
			+ " ,Update_Time"
			+ " ,common_stock"
			+ " ,specially_stock"
			+ " ,warrant_stock"
			+ " ,clear_unit"
			+ " ,clear_date"
			+ " ,clear_word"
			+ " ,Clear_no"
			+ " ,stock_issue"
			+ " ,suspend_unit"
			+ " ,suspend_date"
			+ " ,suspend_word"
			+ " ,suspend_no"
			+ " ,open_date"
			+ " ,single_company"
			+ " ,end_date"
			+ " ,end_word"
			+ " ,end_no"
			+ " ,File_No"
			+ " ,Status_Descr"
			+ " ,Fax_No"
			+ " ,closed"
			+ " FROM CEDB2000";

	public Cedb2000 findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.appendSQLCondition(" BAN_NO = ? ");
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb2000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : (Cedb2000)list.get(0);
	}
	
	public String findSetupDateByBanNo(String banNo) {
		Cedb2000 o = findByBanNo(banNo);
		if(null!=o) {
			return o.getSetupDate();
		}
		return null;
	}

	public List<Cedb2000> findBySql(String sql) {
		SQLJob sqljob = new SQLJob(sql);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	@Override
	public Cedb2000 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb2000 obj = null;
		if(null!=rs) {
			obj = new Cedb2000();
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setRegUnit(Common.get(rs.getString("REG_UNIT")));
			obj.setRegUnitName(Common.get(rs.getString("REG_UNIT_NAME")));
			obj.setSubRegUnit(Common.get(rs.getString("SUB_REG_UNIT")));
			obj.setOrgnType(Common.get(rs.getString("ORGN_TYPE")));
			obj.setOrgnName(Common.get(rs.getString("ORGN_NAME")));
			obj.setBusiType(Common.get(rs.getString("BUSI_TYPE")));
			obj.setBusiTypeName(Common.get(rs.getString("BUSI_TYPE_NAME")));
			obj.setOwnerType(Common.get(rs.getString("OWNER_TYPE")));
			obj.setCompanyName(Common.get(rs.getString("COMPANY_NAME")));
			obj.setPartName(Common.get(rs.getString("PART_NAME")));
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setTelNo(Common.get(rs.getString("TEL_NO")));
			obj.setAreaCode(Common.get(rs.getString("AREA_CODE")));
			obj.setZoneCode(Common.get(rs.getString("ZONE_CODE")));
			obj.setCompanyAddr(Common.get(rs.getString("COMPANY_ADDR")));
			obj.setCapitalAmt(rs.getLong("CAPITAL_AMT"));
			obj.setInvestmentCode(Common.get(rs.getString("INVESTMENT_CODE")));
			obj.setTotDir(Common.get(rs.getString("TOT_DIR")));
			obj.setDirBegDate(Common.get(rs.getString("DIR_BEG_DATE")));
			obj.setDirEndDate(Common.get(rs.getString("DIR_END_DATE")));
			obj.setTotSup(Common.get(rs.getString("TOT_SUP")));
			obj.setSupBegDate(Common.get(rs.getString("SUP_BEG_DATE")));
			obj.setSupEndDate(Common.get(rs.getString("SUP_END_DATE")));
			obj.setRespName(Common.get(rs.getString("RESP_NAME")));
			obj.setStatusCode(Common.get(rs.getString("STATUS_CODE")));
			obj.setSuspendBegDate(Common.get(rs.getString("SUSPEND_BEG_DATE")));
			obj.setSuspendEndDate(Common.get(rs.getString("SUSPEND_END_DATE")));
			obj.setSetupDate(Common.get(rs.getString("SETUP_DATE")));
			obj.setApproveWord(Common.get(rs.getString("APPROVE_WORD")));
			obj.setApproveNo(Common.get(rs.getString("APPROVE_NO")));
			obj.setChangeDate(Common.get(rs.getString("CHANGE_DATE")));
			obj.setChangeWord(Common.get(rs.getString("CHANGE_WORD")));
			obj.setChangeNo(Common.get(rs.getString("CHANGE_NO")));
			obj.setFiscalDate(Common.get(rs.getString("FISCAL_DATE")));
			obj.setAttorName(Common.get(rs.getString("ATTOR_NAME")));
			obj.setAttorId(Common.get(rs.getString("ATTOR_ID")));
			obj.setAccountName(Common.get(rs.getString("ACCOUNT_NAME")));			
			obj.setAccountId(Common.get(rs.getString("ACCOUNT_ID")));
			obj.setTotBranch(rs.getInt("TOT_BRANCH"));
			obj.setOldBanNo(Common.get(rs.getString("OLD_BAN_NO")));
			obj.setOldCompanyName(Common.get(rs.getString("OLD_COMPANY_NAME")));
			obj.setControlItem(Common.get(rs.getString("CONTROL_ITEM")));
			obj.setCaseStatus(Common.get(rs.getString("CASE_STATUS")));
			obj.setStockType(Common.get(rs.getString("STOCK_TYPE")));
			obj.setTotStock(rs.getLong("TOT_STOCK"));
			obj.setStockAmt(rs.getLong("STOCK_AMT"));
			obj.setBondStock(rs.getLong("BOND_STOCK"));
			obj.setRealAmt(rs.getLong("REAL_AMT"));
			obj.setUpdateUser(Common.get(rs.getString("UPDATE_USER")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateTime(Common.get(rs.getString("UPDATE_TIME")));
			obj.setCommonStock(rs.getLong("COMMON_STOCK"));
			obj.setSpeciallyStock(rs.getLong("SPECIALLY_STOCK"));
			obj.setWarrantStock(rs.getLong("WARRANT_STOCK"));
			obj.setClearUnit(Common.get(rs.getString("CLEAR_UNIT")));
			obj.setClearDate(Common.get(rs.getString("CLEAR_DATE")));
			obj.setClearWord(Common.get(rs.getString("CLEAR_WORD")));
			obj.setClearNo(Common.get(rs.getString("CLEAR_NO")));
			obj.setStockIssue(Common.get(rs.getString("STOCK_ISSUE")));
			obj.setSuspendUnit(Common.get(rs.getString("SUSPEND_UNIT")));
			obj.setSuspendDate(Common.get(rs.getString("SUSPEND_DATE")));
			obj.setSuspendWord(Common.get(rs.getString("SUSPEND_WORD")));
			obj.setSuspendNo(Common.get(rs.getString("SUSPEND_NO")));
			obj.setOpenDate(Common.get(rs.getString("OPEN_DATE")));
			obj.setSingleCompany(Common.get(rs.getString("SINGLE_COMPANY")));
			obj.setEndDate(Common.get(rs.getString("END_DATE")));
			obj.setEndWord(Common.get(rs.getString("END_WORD")));
			obj.setEndNo(Common.get(rs.getString("END_NO")));
			obj.setFileNo(Common.get(rs.getString("FILE_NO")));
			obj.setStatusDescr(Common.get(rs.getString("STATUS_DESCR")));		
			obj.setFaxNo(Common.get(rs.getString("FAX_NO")));
			obj.setClosed(Common.get(rs.getString("CLOSED")));
		}
		return obj;
	}
}
