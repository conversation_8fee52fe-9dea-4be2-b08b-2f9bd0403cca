package com.kangdainfo.tcfi.view.test;

import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 客服語音介接
 *
 */
public class CustomerVoice extends SuperBean {

	/** 查詢條件 */
	private String q_prefixNo;
	/** 資料欄位 */
	private String prefixNo;
	private String applyAddr;
	private String applyId;
	private String applyKind;
	private String applyKindDesc;
	private String applyName;
	private String applyTel;
	private String applyType;
	private String approveDate;
	private String approveMark;
	private String approveResult;
	private String approveResultDesc;
	private String approveTime;
	private String assignDate;
	private String assignTime;
	private String attorAddr;
	private String attorName;
	private String attorNo;
	private String attorTel;
	private String banNo;
	private String closeDate;
	private String closeTime;
	private String codeName;
	private String codeNo;
	private String companyName;
	private String companyStus;
	private String companyStusDesc;
	private String controlCd1;
	private String controlCd2;
	private String getDate;
	private String getKind;
	private String getTime;
	private String idNo;
	private String oldCompanyName;
	private String prefixStatus;
	private String receiveDate;
	private String receiveTime;
	private String regDate;
	private String regUnit;
	private String remark;
	private String remark1;
	private String reserveDate;
	private String reserveDays;
	private String reserveMark;
	private String specialName;
	private String staffName;
	private String telixNo;
	private String updateCode;
	private String updateDate;
	private String updateIdNo;
	private String updateTime;
	private String workDay;
	private String zoneCode;

	@SuppressWarnings("unchecked")
	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" PREFIX_NO");
		sqljob.appendSQL(",APPLY_ADDR");
		sqljob.appendSQL(",APPLY_ID");
		sqljob.appendSQL(",APPLY_KIND");
		sqljob.appendSQL(",DECODE(APPLY_KIND,'1','設立','2','變更','') APPLY_KIND_DESC");
		sqljob.appendSQL(",APPLY_NAME");
		sqljob.appendSQL(",APPLY_TEL");
		sqljob.appendSQL(",APPLY_TYPE");
		sqljob.appendSQL(",APPROVE_DATE");
		sqljob.appendSQL(",APPROVE_MARK");
		sqljob.appendSQL(",APPROVE_RESULT");
		sqljob.appendSQL(",decode((select count(e.prefix_no) from cedb1021 e where a.prefix_no > e.prefix_no),1,'審查中',decode(approve_result ,'Y','核予備查','A','審查中','N','未便准予保留','')) APPROVE_RESULT_DESC");
		sqljob.appendSQL(",APPROVE_TIME");
		sqljob.appendSQL(",ASSIGN_DATE");
		sqljob.appendSQL(",ASSIGN_TIME");
		sqljob.appendSQL(",ATTOR_ADDR");
		sqljob.appendSQL(",ATTOR_NAME");
		sqljob.appendSQL(",ATTOR_NO");
		sqljob.appendSQL(",ATTOR_TEL");
		sqljob.appendSQL(",BAN_NO");
		sqljob.appendSQL(",CLOSE_DATE");
		sqljob.appendSQL(",CLOSE_TIME");
		sqljob.appendSQL(",CODE_NAME");
		sqljob.appendSQL(",CODE_NO");
		sqljob.appendSQL(",COMPANY_NAME");
		sqljob.appendSQL(",COMPANY_STUS");
		sqljob.appendSQL(",decode(COMPANY_STUS,'01','申請設立','02','撤銷申請設立','03','核准設立','04','撤銷核准設立','') AS COMPANY_STUS_DESC");
		sqljob.appendSQL(",CONTROL_CD1");
		sqljob.appendSQL(",CONTROL_CD2");
		sqljob.appendSQL(",GET_DATE");
		sqljob.appendSQL(",GET_KIND");
		sqljob.appendSQL(",GET_TIME");
		sqljob.appendSQL(",ID_NO");
		sqljob.appendSQL(",OLD_COMPANY_NAME");
		sqljob.appendSQL(",PREFIX_STATUS");
		sqljob.appendSQL(",RECEIVE_DATE");
		sqljob.appendSQL(",RECEIVE_TIME");
		sqljob.appendSQL(",REG_DATE");
		sqljob.appendSQL(",REG_UNIT");
		sqljob.appendSQL(",REMARK");
		sqljob.appendSQL(",REMARK1");
		sqljob.appendSQL(",RESERVE_DATE");
		sqljob.appendSQL(",RESERVE_DAYS");
		sqljob.appendSQL(",RESERVE_MARK");
		sqljob.appendSQL(",SPECIAL_NAME");
		sqljob.appendSQL(",STAFF_NAME");
		sqljob.appendSQL(",TELIX_NO");
		sqljob.appendSQL(",UPDATE_CODE");
		sqljob.appendSQL(",UPDATE_DATE");
		sqljob.appendSQL(",UPDATE_ID_NO");
		sqljob.appendSQL(",UPDATE_TIME");
		sqljob.appendSQL(",WORK_DAY");
		sqljob.appendSQL(",ZONE_CODE");
		sqljob.appendSQL("FROM CEDB1000 A");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(q_prefixNo);
		List<CustomerVoice> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(sqljob, BeanPropertyRowMapper.newInstance(CustomerVoice.class));			
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[53];
			for(CustomerVoice o : objList) {
				rowArray = new String[53];
				rowArray[0] = Common.get(o.getPrefixNo());
				rowArray[1] = Common.get(o.getApplyAddr());
				rowArray[2] = Common.get(o.getApplyId());
				rowArray[3] = Common.get(o.getApplyKind());
				rowArray[4] = Common.get(o.getApplyKindDesc());
				rowArray[5] = Common.get(o.getApplyName());
				rowArray[6] = Common.get(o.getApplyTel());
				rowArray[7] = Common.get(o.getApplyType());
				rowArray[8] = Common.get(o.getApproveDate());
				rowArray[9] = Common.get(o.getApproveMark());
				rowArray[10] = Common.get(o.getApproveResult());
				rowArray[11] = Common.get(o.getApproveResultDesc());
				rowArray[12] = Common.get(o.getApproveTime());
				rowArray[13] = Common.get(o.getAssignDate());
				rowArray[14] = Common.get(o.getAssignTime());
				rowArray[15] = Common.get(o.getAttorAddr());
				rowArray[16] = Common.get(o.getAttorName());
				rowArray[17] = Common.get(o.getAttorNo());
				rowArray[18] = Common.get(o.getAttorTel());
				rowArray[19] = Common.get(o.getBanNo());
				rowArray[20] = Common.get(o.getCloseDate());
				rowArray[21] = Common.get(o.getCloseTime());
				rowArray[22] = Common.get(o.getCodeName());
				rowArray[23] = Common.get(o.getCodeNo());
				rowArray[24] = Common.get(o.getCompanyName());
				rowArray[25] = Common.get(o.getCompanyStus());
				rowArray[26] = Common.get(o.getCompanyStusDesc());
				rowArray[27] = Common.get(o.getControlCd1());
				rowArray[28] = Common.get(o.getControlCd2());
				rowArray[29] = Common.get(o.getGetDate());
				rowArray[30] = Common.get(o.getGetKind());
				rowArray[31] = Common.get(o.getGetTime());
				rowArray[32] = Common.get(o.getIdNo());
				rowArray[33] = Common.get(o.getOldCompanyName());
				rowArray[34] = Common.get(o.getPrefixStatus());
				rowArray[35] = Common.get(o.getReceiveDate());
				rowArray[36] = Common.get(o.getReceiveTime());
				rowArray[37] = Common.get(o.getRegDate());
				rowArray[38] = Common.get(o.getRegUnit());
				rowArray[39] = Common.get(o.getRemark());
				rowArray[40] = Common.get(o.getRemark1());
				rowArray[41] = Common.get(o.getReserveDate());
				rowArray[42] = Common.get(o.getReserveDays());
				rowArray[43] = Common.get(o.getReserveMark());
				rowArray[44] = Common.get(o.getSpecialName());
				rowArray[45] = Common.get(o.getStaffName());
				rowArray[46] = Common.get(o.getTelixNo());
				rowArray[47] = Common.get(o.getUpdateCode());
				rowArray[48] = Common.get(o.getUpdateDate());
				rowArray[49] = Common.get(o.getUpdateIdNo());
				rowArray[50] = Common.get(o.getUpdateTime());
				rowArray[51] = Common.get(o.getWorkDay());
				rowArray[52] = Common.get(o.getZoneCode());
				arrList.add(rowArray);
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getApplyAddr() {return checkGet(applyAddr);}
	public void setApplyAddr(String s) {this.applyAddr = checkSet(s);}
	public String getApplyId() {return checkGet(applyId);}
	public void setApplyId(String s) {this.applyId = checkSet(s);}
	public String getApplyKind() {return checkGet(applyKind);}
	public void setApplyKind(String s) {this.applyKind = checkSet(s);}
	public String getApplyKindDesc() {return checkGet(applyKindDesc);}
	public void setApplyKindDesc(String s) {this.applyKindDesc = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {this.applyName = checkSet(s);}
	public String getApplyTel() {return checkGet(applyTel);}
	public void setApplyTel(String s) {this.applyTel = checkSet(s);}
	public String getApplyType() {return checkGet(applyType);}
	public void setApplyType(String s) {this.applyType = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {this.approveDate = checkSet(s);}
	public String getApproveMark() {return checkGet(approveMark);}
	public void setApproveMark(String s) {this.approveMark = checkSet(s);}
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {this.approveResult = checkSet(s);}
	public String getApproveResultDesc() {return checkGet(approveResultDesc);}
	public void setApproveResultDesc(String s) {this.approveResultDesc = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {this.approveTime = checkSet(s);}
	public String getAssignDate() {return checkGet(assignDate);}
	public void setAssignDate(String s) {this.assignDate = checkSet(s);}
	public String getAssignTime() {return checkGet(assignTime);}
	public void setAssignTime(String s) {this.assignTime = checkSet(s);}
	public String getAttorAddr() {return checkGet(attorAddr);}
	public void setAttorAddr(String s) {this.attorAddr = checkSet(s);}
	public String getAttorName() {return checkGet(attorName);}
	public void setAttorName(String s) {this.attorName = checkSet(s);}
	public String getAttorNo() {return checkGet(attorNo);}
	public void setAttorNo(String s) {this.attorNo = checkSet(s);}
	public String getAttorTel() {return checkGet(attorTel);}
	public void setAttorTel(String s) {this.attorTel = checkSet(s);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}
	public String getCloseDate() {return checkGet(closeDate);}
	public void setCloseDate(String s) {this.closeDate = checkSet(s);}
	public String getCloseTime() {return checkGet(closeTime);}
	public void setCloseTime(String s) {this.closeTime = checkSet(s);}
	public String getCodeName() {return checkGet(codeName);}
	public void setCodeName(String s) {this.codeName = checkSet(s);}
	public String getCodeNo() {return checkGet(codeNo);}
	public void setCodeNo(String s) {this.codeNo = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}
	public String getCompanyStus() {return checkGet(companyStus);}
	public void setCompanyStus(String s) {this.companyStus = checkSet(s);}
	public String getCompanyStusDesc() {return checkGet(companyStusDesc);}
	public void setCompanyStusDesc(String s) {this.companyStusDesc = checkSet(s);}
	public String getControlCd1() {return checkGet(controlCd1);}
	public void setControlCd1(String s) {this.controlCd1 = checkSet(s);}
	public String getControlCd2() {return checkGet(controlCd2);}
	public void setControlCd2(String s) {this.controlCd2 = checkSet(s);}
	public String getGetDate() {return checkGet(getDate);}
	public void setGetDate(String s) {this.getDate = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {this.getKind = checkSet(s);}
	public String getGetTime() {return checkGet(getTime);}
	public void setGetTime(String s) {this.getTime = checkSet(s);}
	public String getIdNo() {return checkGet(idNo);}
	public void setIdNo(String s) {this.idNo = checkSet(s);}
	public String getOldCompanyName() {return checkGet(oldCompanyName);}
	public void setOldCompanyName(String s) {this.oldCompanyName = checkSet(s);}
	public String getPrefixStatus() {return checkGet(prefixStatus);}
	public void setPrefixStatus(String s) {this.prefixStatus = checkSet(s);}
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {this.receiveDate = checkSet(s);}
	public String getReceiveTime() {return checkGet(receiveTime);}
	public void setReceiveTime(String s) {this.receiveTime = checkSet(s);}
	public String getRegDate() {return checkGet(regDate);}
	public void setRegDate(String s) {this.regDate = checkSet(s);}
	public String getRegUnit() {return checkGet(regUnit);}
	public void setRegUnit(String s) {this.regUnit = checkSet(s);}
	public String getRemark() {return checkGet(remark);}
	public void setRemark(String s) {this.remark = checkSet(s);}
	public String getRemark1() {return checkGet(remark1);}
	public void setRemark1(String s) {this.remark1 = checkSet(s);}
	public String getReserveDate() {return checkGet(reserveDate);}
	public void setReserveDate(String s) {this.reserveDate = checkSet(s);}
	public String getReserveDays() {return checkGet(reserveDays);}
	public void setReserveDays(String s) {this.reserveDays = checkSet(s);}
	public String getReserveMark() {return checkGet(reserveMark);}
	public void setReserveMark(String s) {this.reserveMark = checkSet(s);}
	public String getSpecialName() {return checkGet(specialName);}
	public void setSpecialName(String s) {this.specialName = checkSet(s);}
	public String getStaffName() {return checkGet(staffName);}
	public void setStaffName(String s) {this.staffName = checkSet(s);}
	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}
	public String getUpdateCode() {return checkGet(updateCode);}
	public void setUpdateCode(String s) {this.updateCode = checkSet(s);}
	public String getUpdateDate() {return checkGet(updateDate);}
	public void setUpdateDate(String s) {this.updateDate = checkSet(s);}
	public String getUpdateIdNo() {return checkGet(updateIdNo);}
	public void setUpdateIdNo(String s) {this.updateIdNo = checkSet(s);}
	public String getUpdateTime() {return checkGet(updateTime);}
	public void setUpdateTime(String s) {this.updateTime = checkSet(s);}
	public String getWorkDay() {return checkGet(workDay);}
	public void setWorkDay(String s) {this.workDay = checkSet(s);}
	public String getZoneCode() {return checkGet(zoneCode);}
	public void setZoneCode(String s) {this.zoneCode = checkSet(s);}

}