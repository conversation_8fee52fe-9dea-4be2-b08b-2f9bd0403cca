package com.kangdainfo.tcfi.scheduling;

import java.util.List;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;

/**
 * 排程(PRE0014) - 一站式案件同步失敗資料補正
 * 若一站式案件同步失敗
 * 可能是因為 OSSS.OSSM_ORG_REGISTER.ORG_CAPITAL_AMT(資本總額)是NULL，導致一站式WebService異常
 * 需釐正資料後，再同步一次
 */
public class Pre0014QuartzJobBean extends BaseQuartzJobBean {
	protected void executeJob(JobExecutionContext context)
			throws JobExecutionException
	{
		//1.查詢同步失敗的案件
		List<Queue> datas = ServiceGetter.getInstance().getUpdateOsssStatusService().queryErrorQueues();
		if(null!=datas && !datas.isEmpty()) {
			for(Queue data : datas) {
				log.info("[PREFIX_NO:"+data.getPrefixNo()+"]");
				ServiceGetter.getInstance().getUpdateOsssStatusService().updateOrgCapitalAmt(data.getPrefixNo());
			}
		}
	}
}