package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;
import com.kangdainfo.tcfi.model.eicm.dao.SystemNewsDao;
import com.kangdainfo.tcfi.service.Pre9005Service;

public class Pre9005ServiceImpl
	implements Pre9005Service
{
	public SystemNews getSystemNewsById(Integer id) {
		return systemNewsDao.findById(id);
	}

	public Integer countSystemNews(String id, String subject, String content, String startDate, String endDate) {
		return systemNewsDao.countByCondition(id, subject, content, startDate, endDate);
	}

	public List<SystemNews> querySystemNews(String id, String subject, String content, String startDate, String endDate) {
		return systemNewsDao.findByCondition(id, subject, content, startDate, endDate);
	}

	public SystemNews insertSystemNews(SystemNews o) {
		return systemNewsDao.insert(o);
	}

	public SystemNews updateSystemNews(SystemNews o) {
		return systemNewsDao.update(o);
	}

	public void deleteSystemNewsById(Integer id) {
		SystemNews o = new SystemNews();
		o.setId(id);
		systemNewsDao.delete(o);
	}

	private SystemNewsDao systemNewsDao;
	public SystemNewsDao getSystemNewsDao() {return systemNewsDao;}
	public void setSystemNewsDao(SystemNewsDao dao) {this.systemNewsDao = dao;}

}