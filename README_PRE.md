# PREFIX 專案技術文件

## 專案概述

PREFIX 是一個基於 Spring MVC 架構的 Java Web 應用程式，主要用於處理預查申請相關業務流程。系統採用傳統的 JSP + Spring 架構，提供完整的文件收發、審核、查詢等功能。

## 專案架構概述

### 整體架構模式

-   **架構模式**: MVC (Model-View-Controller)
-   **前端技術**: JSP + JavaScript + jQuery
-   **後端框架**: Spring Framework (傳統 XML 配置)
-   **資料存取**: 直接 JDBC + Spring 整合
-   **安全框架**: ESAPI + 自訂身份驗證

### 目錄結構

```
WebContent/
├── tcfi/                    # 主要業務模組
│   ├── pre/                 # 預查相關功能
│   │   ├── pre1001_00.jsp   # 預查申請資料收文
│   │   ├── pre2001_*.jsp    # 預查申請資料登打
│   │   ├── pre3001_00.jsp   # 預查審核作業
│   │   ├── pre4001*.jsp     # 案件資料查詢
│   │   ├── pre5001.jsp      # 預查收費作業
│   │   └── ...
│   ├── ajax/                # AJAX 處理頁面
│   └── common/              # 共用元件
├── home/                    # 系統框架頁面
│   ├── head.jsp             # 共用標頭
│   ├── meta.jsp             # Meta 資訊
│   └── secure.jsp           # 安全檢查
├── js/                      # JavaScript 函式庫
└── images/                  # 圖片資源
```

### 主要模組劃分

1. **PRE1xxx**: 收文相關功能
2. **PRE2xxx**: 登打相關功能
3. **PRE3xxx**: 審核相關功能
4. **PRE4xxx**: 查詢相關功能
5. **PRE5xxx**: 收費相關功能
6. **PRE8xxx**: 系統管理功能
7. **PRE9xxx**: 權限管理功能

## 技術依賴分析

### Spring Framework 版本

-   **Spring MVC**: 傳統 XML 配置模式
-   **依賴注入**: 使用 `ServiceGetter` 服務定位器模式
-   **配置檔案**: 位於 `applicationContext/` 目錄

### 第三方函式庫

```xml
<!-- 主要依賴 (推測) -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-webmvc</artifactId>
</dependency>
<dependency>
    <groupId>org.owasp.esapi</groupId>
    <artifactId>esapi</artifactId>
</dependency>
<dependency>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson</artifactId>
</dependency>
```

### 資料庫連線

系統支援多個資料庫連線：

-   **EEDB**: 主要業務資料庫
-   **EICM**: 電子化資料庫
-   **OSSS**: 線上服務系統
-   **ICMS**: 整合管理系統

### 安全框架

-   **ESAPI**: OWASP 企業安全 API
-   **自訂驗證**: `AuthenticateFilter` 過濾器
-   **編碼保護**: HTML 編碼防護 XSS

## 核心功能模組

### PRE1001 - 預查申請資料收文

**檔案**: `pre1001_00.jsp`
**功能**: 處理預查申請的收文作業

```java
// 核心業務邏輯範例
if ("init".equals(obj.getState())){
    //do nothing
} else if("getNoAndSave".equals(obj.getState())) {
    //空白表單收文 或 一維條碼
    obj.save();
}
```

**主要功能**:

-   線上收文處理
-   一維條碼掃描
-   申請表產製

### PRE2001 - 預查申請資料登打

**檔案**: `pre2001_*.jsp` 系列
**功能**: 申請資料的登打與維護

```javascript
// AJAX 查詢範例
$.post(getVirtualPath() + 'tcfi/pre/pre2001_02.jsp', { q: queryData }, function (data) {
    var vo = gson.fromJson(data, PrefixQueryVo.class);
    // 處理回傳資料
});
```

### PRE3001 - 預查審核作業

**檔案**: `pre3001_00.jsp`
**功能**: 預查案件的審核流程

```java
// 審核狀態處理
if("saveBusiItem".equals(obj.getState())) {
    obj.saveBusiItem();
} else if("assignNewOne".equals(obj.getState())) {
    obj.assignNewOne();
} else if("tempSave".equals(obj.getState())) {
    obj.tempSave();
}
```

### PRE4001 - 案件資料查詢

**檔案**: `pre4001*.jsp` 系列
**功能**: 已審核案件的查詢與管理

```javascript
// 案件查詢 AJAX
$.post(getVirtualPath() + 'tcfi/pre/pre4001_03.jsp?q=' + $('#current').val(), function (data) {
    if (!data) {
        showMsgBar('查無資料!');
        return;
    }
    prefixVo = data;
    $('#currPrefixNo').html(data.prefixNo);
});
```

### PRE5001 - 預查收費作業

**檔案**: `pre5001.jsp`
**功能**: 預查案件的收費處理

```java
// 收費處理邏輯
if("getNoAndSave".equals(obj.getState())) {
    obj.save();
} else if ("saveSuccess".equals(obj.getState())) {
    // 收費成功處理
}
```

## 系統需求

### 運行環境

-   **JDK 版本**: Java 8+ (推測)
-   **應用程式伺服器**: Apache Tomcat 8.x+
-   **資料庫**: Oracle Database
-   **瀏覽器支援**: IE 8+, Chrome, Firefox

### 開發環境設置

```bash
# 1. 安裝 JDK 8+
# 2. 安裝 Tomcat 8.x
# 3. 設置資料庫連線
# 4. 部署 WAR 檔案到 Tomcat
```

## 安全性機制

### 身份驗證流程

```java
// preAuth.jsp - 身份驗證入口
boolean isPass = ServiceGetter.getInstance()
    .getAuthenticationService()
    .authenticate(request);
if(!isPass) {
    session.invalidate();
    response.sendRedirect("index.jsp?error="+errorFlg);
} else {
    response.sendRedirect("home/frame.jsp");
}
```

### ESAPI 整合

```java
// 輸入資料編碼保護
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
```

### 權限控制

```jsp
<!-- 功能權限檢查 -->
<jsp:include page="../../home/<USER>">
    <jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1001" />
</jsp:include>
```

### 資料保護措施

1. **XSS 防護**: ESAPI HTML 編碼
2. **SQL 注入防護**: 參數化查詢
3. **CSRF 防護**: Session 驗證
4. **存取控制**: 功能權限檢查

## 開發約束

### 編碼規範

1. **檔案編碼**: UTF-8
2. **命名慣例**:
    - JSP 檔案: `preXXXX_XX.jsp`
    - Java 類別: PascalCase
    - 變數: camelCase

### JSP 開發模式

```jsp
<%-- 標準 JSP 頁面結構 --%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PREXXXX">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
```

### 資料庫存取模式

```java
// 使用 ServiceGetter 取得服務
ServiceGetter.getInstance().getPreXXXXService().methodName();
```

### AJAX 處理模式

```javascript
// 標準 AJAX 呼叫模式
$.post(getVirtualPath() + 'tcfi/ajax/ajaxXXXX.jsp', function (data) {
    // 處理回傳資料
});
```

## 部署與維護

### 建置流程

1. **編譯**: Maven 或 Ant 建置
2. **打包**: 產生 WAR 檔案
3. **部署**: 部署至 Tomcat
4. **設定**: 修改資料庫連線設定

### 設定檔管理

```
WEB-INF/
├── web.xml              # Web 應用程式設定
├── applicationContext/  # Spring 設定檔
├── classes/
│   ├── ESAPI.properties # ESAPI 安全設定
│   └── log4j.properties # 日誌設定
```

### 日誌機制

```java
// 系統日誌輸出
System.out.println((new java.util.Date()) + " onlineFilePath:" + obj.getOnlineFilePath());
```

## 新人開發者快速上手指南

### 1. 環境準備

```bash
# 克隆專案
git clone [repository-url]

# 設置開發環境
# - 安裝 JDK 8+
# - 安裝 Tomcat
# - 設置 IDE (Eclipse/IntelliJ)
```

### 2. 專案導入

1. 將專案導入 IDE
2. 設置 Tomcat 伺服器
3. 配置資料庫連線
4. 啟動應用程式

### 3. 開發流程

1. **新增功能**: 建立對應的 JSP 頁面和 Java 類別
2. **修改現有功能**: 找到對應的 `preXXXX` 檔案進行修改
3. **測試**: 在本地 Tomcat 環境測試
4. **部署**: 打包並部署到測試環境

### 4. 常用開發模式

```java
// 1. 建立 View Bean
public class PREXXXX {
    private String state;
    // getter/setter methods

    public void save() {
        // 業務邏輯處理
    }
}
```

```jsp
<!-- 2. 建立 JSP 頁面 -->
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PREXXXX">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
if("save".equals(obj.getState())) {
    obj.save();
}
%>
```

### 5. 除錯技巧

1. **日誌輸出**: 使用 `System.out.println()` 進行除錯
2. **瀏覽器開發工具**: 檢查 AJAX 請求與回應
3. **資料庫查詢**: 直接查詢資料庫確認資料狀態

## 重要技術決策與設計考量

### 1. 服務定位器模式

**決策**: 使用 `ServiceGetter` 而非直接依賴注入
**考量**: 簡化 Spring 配置，但增加了程式碼耦合度

### 2. JSP + Java Bean 模式

**決策**: 採用 JSP 內嵌 Java 程式碼
**考量**: 快速開發，但維護性較差

### 3. 多資料庫支援

**決策**: 支援多個資料庫連線
**考量**: 滿足不同業務需求，但增加了配置複雜度

### 4. ESAPI 安全框架

**決策**: 整合 OWASP ESAPI
**考量**: 提供企業級安全保護

## 常見問題與解決方案

### Q1: 如何新增一個新的功能模組？

**A**:

1. 建立對應的 Java View Bean 類別
2. 建立 JSP 頁面
3. 在 `secure.jsp` 中新增權限檢查
4. 更新選單配置

### Q2: 如何處理 AJAX 請求？

**A**:

1. 建立對應的 AJAX 處理 JSP 頁面
2. 設置正確的 Content-Type
3. 使用 Gson 處理 JSON 資料

### Q3: 如何進行資料庫操作？

**A**:

1. 透過 `ServiceGetter` 取得對應的 Service
2. 在 Service 中實作資料庫操作邏輯
3. 使用參數化查詢防止 SQL 注入

## 聯絡資訊

如有技術問題，請聯絡開發團隊或參考相關技術文件。

---

*最後更新日期: 2025/09/24
*文件版本: 1.0
