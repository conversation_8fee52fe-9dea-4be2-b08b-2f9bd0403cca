package com.kangdainfo.tcfi.service.impl;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.jasperreports.engine.data.JRMapCollectionDataSource;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.DateTimeFormatter;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.StringUtility;
import com.kangdainfo.moea.bo.Form1110;
import com.kangdainfo.moea.bo.SeqNoComparatorFor3000;
import com.kangdainfo.moea.bo.SeqNoComparatorFor3100;
import com.kangdainfo.tcfi.model.crmsmoea.bo.CsyssUser;
import com.kangdainfo.tcfi.model.crmsmoea.dao.CsyssUserDao;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3000;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;
import com.kangdainfo.tcfi.model.eedb.bo.EedbV8000;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb5000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.EedbV8000Dao;
import com.kangdainfo.tcfi.model.eicm.bo.BusiItem;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1007;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1008;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1011;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1021;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1028;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc053;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc055;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyMemoInfo;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.tcfi.model.eicm.bo.LoginLog;
import com.kangdainfo.tcfi.model.eicm.bo.PartNameLog;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;
import com.kangdainfo.tcfi.model.eicm.bo.RestrictionItem;
import com.kangdainfo.tcfi.model.eicm.bo.SynonymWord;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.BusiItemDao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1003Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1007Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1009Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1011Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1017Dao;
import com.kangdainfo.tcfi.model.eicm.dao.PostRecordDao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1019Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1021Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1028Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc055Dao;
import com.kangdainfo.tcfi.model.eicm.dao.CmpyMemoInfoDao;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesDao;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesRcverDao;
import com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao;
import com.kangdainfo.tcfi.model.eicm.dao.FunctionMenuAuthDao;
import com.kangdainfo.tcfi.model.eicm.dao.FunctionMenuDao;
import com.kangdainfo.tcfi.model.eicm.dao.LoginLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.SameNameQueueDao;
import com.kangdainfo.tcfi.model.eicm.dao.PartNameLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiveDao;
import com.kangdainfo.tcfi.model.eicm.dao.RestrictionDao;
import com.kangdainfo.tcfi.model.eicm.dao.RestrictionItemDao;
import com.kangdainfo.tcfi.model.eicm.dao.SyncOssQueueDao;
import com.kangdainfo.tcfi.model.eicm.dao.SynonymWordDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCountry;
import com.kangdainfo.tcfi.model.icms.bo.CsmmCmpyInfo;
import com.kangdainfo.tcfi.model.icms.dao.CsmdCountryDao;
import com.kangdainfo.tcfi.model.icms.dao.CsmmCmpyInfoDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBusiItemDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBussMainDao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmBussItem;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeDetailDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;
import com.kangdainfo.tcfi.service.PrefixService;
import com.kangdainfo.tcfi.util.AreaCodeOptionHelper;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.PrefixDescHelper;
import com.kangdainfo.util.TaiwanDateUtil;
import com.kangdainfo.util.lang.CommonStringUtils;
import com.kangdainfo.util.report.JasperReportMaker;

public class PrefixServiceImpl implements PrefixService {

	private Logger logger = Logger.getLogger(this.getClass());
	private Log findPrefixDataLog = LogFactory.getLog("findPrefixDataLog");
	
	private Cedb1000Dao cedb1000Dao;
	private com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao eedb1000DaoEicm;
	private com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao eedb1002Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1004Dao cedb1004Dao;
	private Cedb2000Dao cedb2000Dao;
	private Cedb2002Dao cedb2002Dao;
	private Cedb2004Dao cedb2004Dao;
	private CmpyMemoInfoDao cmpyMemoInfoDao;
	private ReceiveDao receiveDao;
	private Cedb1019Dao cedb1019Dao;
	private Cedb1017Dao cedb1017Dao;
	private PostRecordDao postRecordDao;
	private DeclaratoryStatutesDao declaratoryStatutesDao;
	private DeclaratoryStatutesRcverDao declaratoryStatutesRcverDao;
	private CsmmCmpyInfoDao csmmCmpyInfoDao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1010Dao cedb1010Dao;
	private Cedb1007Dao cedb1007Dao;
	private Cedb1008Dao cedb1008Dao;
	private SystemCodeDao systemCodeDao;
	private FunctionMenuDao functionMenuDao;
	private FunctionMenuAuthDao functionMenuAuthDao;
	private Cedbc000Dao cedbc000Dao;
	private Cedb1022Dao cedb1022Dao;
	private OssmApplMainDao ossmApplMainDao;
	private OssmApplFlowDao ossmApplFlowDao;
	private OssmFeeMainDao ossmFeeMainDao;
	private OssmFeeDetailDao ossmFeeDetailDao;
	private OssmOrgChangeDao ossmOrgChangeDao;
	private EedbV8000Dao eedbV8000Dao;
	private Eedb1000Dao eedb1000Dao;
	private OssmOrgNameDao ossmOrgNameDao;
	private OssmBussItemDao ossmBussItemDao;
	private RestrictionDao restrictionDao;
	private RestrictionItemDao restrictionItemDao;
	private LoginLogDao loginLogDao;
	private SameNameQueueDao sameNameQueueDao;
	private SyncOssQueueDao syncOssQueueDao;
	private Eedb1100Dao eedb1100Dao;
	private Eedb1300Dao eedb1300Dao;
	private Eedb3000Dao eedb3000Dao;
	private Eedb3300Dao eedb3300Dao;
	private Eedb5000Dao eedb5000Dao;
	private Eedb3100Dao eedb3100Dao;
	private Cedb1011Dao cedb1011Dao;
	private Cedb1003Dao cedb1003Dao;
	private Cedb1009Dao cedb1009Dao;
	private Cedb1006Dao cedb1006Dao;
	private Cedb1021Dao cedb1021Dao;
	private Cedb1027Dao cedb1027Dao;
	private Cedbc053Dao cedbc053Dao;
	private Cedbc055Dao cedbc055Dao;
	private BusiItemDao busiItemDao;
	private SynonymWordDao synonymWordDao;
	private Cedb1028Dao cedb1028Dao;
	private Cedbc054Dao cedbc054Dao;
	private EicmGeneralQueryDao eicmGeneralQueryDao;
	private PartNameLogDao partNameLogDao;
	private CsmdCountryDao csmdCountryDao;
	private LmsmBusiItemDao lmsBusiItemDao;
	private LmsmBussMainDao lmsmBussMainDao;
	private CsyssUserDao csyssUserDao;
	
	public String getApplyKindDesc(String prefixNo) {
		String result = "";
		SQLJob sqljob = new SQLJob("SELECT");
		sqljob.appendSQL("nvl(c13.code_name,decode(a.apply_kind,'1','設立','2','變更','')) as CHANGE_TYPE");
		sqljob.appendSQL("from cedb1000 a");
		sqljob.appendSQL("  left outer join cedb1023 b on a.prefix_no = b.prefix_no");
		sqljob.appendSQL("  left outer join system_code c13 on c13.code_kind='13' and c13.code=b.change_type");
		sqljob.appendSQL("WHERE A.PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		if(logger.isInfoEnabled()) logger.info(sqljob);
		List<Map<String,Object>> datas = eicmGeneralQueryDao.queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			result = (String)datas.get(0).get("CHANGE_TYPE");
		}
		return result;
	}

	public List<Cedb1002> getCedb1002sByPrefixNo(String prefixNo) {
		return cedb1002Dao.findByPrefixNo(prefixNo);
	}

	public void insertCedb1002(Cedb1002 obj) {
		cedb1002Dao.insert(obj);
	}

	public int deleteCedb1002ByPrefixNoAndBusiItemNo(String prefixNo, String busiItemNo) {
		return cedb1002Dao.deleteByPrefixNoAndBusiItemNo(prefixNo, busiItemNo);
	}

	public Cedbc000 getCedbc000ByIdNoAndPwd(String idNo, String pwd) {
		return cedbc000Dao.findByIdNoAndPwd(idNo, pwd);
	}

	public Cedbc000 getCedbc000ByIdNo(String idNo) {
		return cedbc000Dao.findByIdNo(idNo);
	}
	
	public List<Cedbc000> getCedbc000ByCondition(String id, String name, String unit) {
		return cedbc000Dao.queryByCondition(id, name, unit);
	}
	
	public Cedbc000 insertCedbc000(Cedbc000 obj){
		return cedbc000Dao.insert(obj);
	}

	public Cedbc000 updateCedbc000(Cedbc000 obj){
		return cedbc000Dao.update(obj);
	}
	
	public void deleteCedbc000(Cedbc000 obj){
		cedbc000Dao.delete(obj);
	}
	
	public void setAssignData(String prefixNo, String updateIdNo, String staffName) {
		cedb1000Dao.updateAssignData(prefixNo, updateIdNo, staffName);
	}
	
	public Cedb1000 getSmallestNotAssign() {
		return cedb1000Dao.findSmallestNotAssign();
	}
	
	public Cedb1000 getCedb1000ByPrefixNo(String prefixNo) {
		return cedb1000Dao.findByPrefixNo(prefixNo, null);
	}
	
	public List<Cedb1000> getCedb1000ForPre8005(String searchType, String startNo, String endNo) {
		return cedb1000Dao.findAssignForPre8005(searchType, startNo, endNo);
	}
	
	public List<Cedb1000> getBetweenPrefixNo(String prefixStart, String prefixEnd) {
		return cedb1000Dao.doReadBetweenPrefixNo(prefixStart, prefixEnd);
	}
	
	public Cedb1000 getCedb1000ByTelixNo(String telixNo) {
		return cedb1000Dao.findByTelixNo(telixNo, null);
	}
	
	public List<Cedb1000> getCedb1000ListWhenPRE2004(String prefixStart, String prefixEnd, String[] prefixList) {
		return cedb1000Dao.findListWhenPRE2004(prefixStart, prefixEnd, prefixList);
	}
	
	public List<Cedb1001> getCedb1001ByPrefixNo(String prefixNo) {
		return cedb1001Dao.findByPrefixNo(prefixNo);
	}

	public Cedb1001 getApprovedCedb1001ByPrefixNo(String prefixNo) {
		return cedb1001Dao.findApprovedByPrefixNo(prefixNo);
	}

	public List<com.kangdainfo.tcfi.model.eicm.bo.Eedb1000> getEedb1000ListWhenPRE2004(String prefixStart, String prefixEnd, String[] prefixList) {
		return eedb1000DaoEicm.getEedb1000ListWhenPRE2004(prefixStart, prefixEnd, prefixList);
	}
	
	public List<com.kangdainfo.tcfi.model.eicm.bo.Eedb1002> getEedb1002ListWhenPRE2004(String prefixNo) {
		return eedb1002Dao.findByPrefixNo(prefixNo);
	}

	public List<EedbV8000> getEedbV8000ByTelixNo(String telixNo) {
		return eedbV8000Dao.findByTelixNo(telixNo);
	}
	
	public OssmApplMain getOssmApplMainByTelixNo(String telixNo) {
		return ossmApplMainDao.findByTelixNo(telixNo);
	}
	
	public OssmApplFlow getOssmApplFlowByTelixNoAndProcessNo(String telixNo, String processNo) {
		return ossmApplFlowDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}
	
	public OssmFeeMain getOssmFeeMainByTelixNoAndProcessNo(String telixNo, String processNo) {
		return ossmFeeMainDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}
	
	public void saveOssmFeeMainByTelixNo(String telixNo) {
		ossmFeeMainDao.saveByTelixNo(telixNo);
	}
	
    public OssmFeeDetail getOssmFeeDetailByTelixNoAndProcessNo(String telixNo, String processNo) {
    	return ossmFeeDetailDao.findByTelixNoAndProcessNo(telixNo, processNo);
	}
    
    public OssmOrgChange getOssmOrgChangeByTelixNo(String telixNo) {
    	return ossmOrgChangeDao.findByTelixNo(telixNo);
	}

	public List<Cedb1002> getCedb1002ByPrefixNo(String prefixNo) {
		return cedb1002Dao.findByPrefixNo(prefixNo);
	}

	public Cedb1002 getCedb1002ByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		return cedb1002Dao.findByPrefixNoAndSeqNo(prefixNo, seqNo);
	}

	public Cedb1011 queryCedb1011ByPrefixNo(String prefixNo ) {
		return cedb1011Dao.queryByPrefixNo(prefixNo);
	}

	public void deleteCedb1011ByPrefixNo(String prefixNo) {
		cedb1011Dao.deleteByPrefixNo(prefixNo);
	}
	
	public int saveCedb1011( Cedb1011 obj ) {
		int result = -1;
		if(null!=obj && null!=obj.getPrefixNo()) {
			Cedb1011 old = cedb1011Dao.queryByPrefixNo(obj.getPrefixNo());
			if(null==old) {
				result = cedb1011Dao.insert(obj);
			} else {
				result = cedb1011Dao.update(obj);
			}
		}
		return result;
	} 

	public List<Cedb1002> getCedb1002ByBanNo(String banNo) {
		return cedb1002Dao.findByBanNo(banNo);
	}
	
	public List<DeclaratoryStatutes> queryDeclaratoryStatutes(DeclaratoryStatutes bo, String endTime) {
		return declaratoryStatutesDao.find(bo, endTime);
	}
	
	public List<DeclaratoryStatutesRcver> queryDeclaratoryStatutesRcver(DeclaratoryStatutes bo) {
		return declaratoryStatutesRcverDao.find(bo);
	}
	
	public int deleteDeclaratoryStatutesRcver(DeclaratoryStatutes bo) {
		return declaratoryStatutesRcverDao.delete(bo);
	}
	
	public int insertDeclaratoryStatutesRcver(DeclaratoryStatutesRcver bo) {
		return declaratoryStatutesRcverDao.insert(bo);
	}
	
	public List<Cedb1017> queryCedb1017(String staffUnit) {
		return cedb1017Dao.findAll(staffUnit);
	}
	
	public Cedb1017 queryCedb1017ByPostType(String staffUnit, String postType) {
		return cedb1017Dao.findByPostType(staffUnit, postType);
	}
	
	public List<Cedb1019> queryCedb1019(Cedb1019 bo) {
		return cedb1019Dao.find(bo);
	}
	
	public List<Cedb1019> queryCedb1019() {
		return cedb1019Dao.list();
	}
	
	public List<Cedb1021> getCedb1021List(String prefixStart, String prefixEnd) {
		return cedb1021Dao.findList(prefixStart, prefixEnd);
	}
	
	public Cedb1021 getCedb1021ByPrefixNo(String prefixNo) {
		return cedb1021Dao.findCedb1021ByPrefixNo(prefixNo);
	}
	
	public int insertCedb1021(Cedb1021 obj) {
		return cedb1021Dao.insert(obj);
	}
	
	public int updateCedb1021(Cedb1021 obj) {
		return cedb1021Dao.update(obj);
	}
	
	public Cedb1022 getCedb1022ByPrefixNo(String prefixNo) {
		return cedb1022Dao.findByPrefixNo(prefixNo);
	}
	
	public Cedb1023 getCedb1023ByPrefixNo( String prefixNo ) {
		return cedb1023Dao.findByPrefixNo(prefixNo) ;
	}

	public List<Cedb1027> getCedb1027ByPrefixNo( String prefixNo ) {
		return cedb1027Dao.findByPrefixNo(prefixNo);
	}
	
	public List<Cedb1027> getCedb1027ByNoStartAndNoEnd( String NoStart, String NoEnd, String type, String postType ) {
		return cedb1027Dao.findBetween(NoStart, NoEnd, type, postType) ;
	}

	public Cedb1027 getLastOneCedb1027ByPrefixNo(String prefixNo) {
		return cedb1027Dao.findLastOneByPrefixNo(prefixNo);
	}
	
	public List<PostRecord> getPostRecordByPrefixNo( String prefixNo ) {
		return postRecordDao.findByPrefixNo(prefixNo);
	}
	
	public PostRecord getPostRecordByPrefixNoAndPostNo( String prefixNo, String postNo ) {
		return postRecordDao.findByPrefixNoAndPostNo(prefixNo, postNo);
	}
	
	public List<PostRecord> getPostRecordByNoStartAndNoEnd( String NoStart, String NoEnd, String type, String postType ) {
		return postRecordDao.findBetween(NoStart, NoEnd, type, postType) ;
	}
	
	public PostRecord getLastOnePostRecordByPrefixNo(String prefixNo) {
		return postRecordDao.findLastOneByPrefixNo(prefixNo);
	}
	
	public PostRecord getMainPostRecordByPrefixNo(String prefixNo) {
		return postRecordDao.findMainByPrefixNo(prefixNo);
	}
	
	public int insertPostRecord(PostRecord obj) {
		return postRecordDao.insert(obj);
	}

	public DeclaratoryStatutes getDeclaratoryStatutes(String rcvNo) {
		return declaratoryStatutesDao.findByRcvNo(rcvNo);
	}

	public Cedb2000 getCedb2000ByBanNo(String banNo) {
		return cedb2000Dao.findByBanNo(banNo);
	}
	
	public List<Cedb2002> getCedb2002ByBanNo(String banNo) {
		return cedb2002Dao.findByBanNo(banNo);
	}
	
	public List<Cedb2004> getCedb2004ByBanNo(String banNo) {
		return cedb2004Dao.findByBanNo(banNo);
	}

	public DeclaratoryStatutes saveDeclaratoryStatutes(DeclaratoryStatutes obj) {
		return declaratoryStatutesDao.set(obj);
	}

	public int saveWhenPublish(DeclaratoryStatutes obj) {
		return declaratoryStatutesDao.updateWhenPublish(obj);
	}
	
	public com.kangdainfo.tcfi.model.eedb.bo.Eedb1000 findEedb1000ByTelixNo(String telixNo) {
		return eedb1000Dao.findByTelixNo(telixNo);
	}
	
	public Eedb1100 findEedb1100ByTelixNo(String telixNo) {
		return eedb1100Dao.findByTelixNo(telixNo);
	}
	
	public Eedb3300 findEedb3300ByTelixNo(String telixNo) {
		return eedb3300Dao.findByTelixNo(telixNo);
	}
	
	public Eedb5000 findEedb5000ByTelixNo(String telixNo) {
		return eedb5000Dao.findByTelixNo(telixNo);
	}
	
	public void saveCedb1019(Cedb1019 obj) {
		cedb1019Dao.update(obj);
	}
	
	public void saveCedb1017(Cedb1017 obj) {
		cedb1017Dao.update(obj);
	}

	public void updateCedb1000GetDateTime(Cedb1000 cedb1000) {
		if(null!=cedb1000 && !"".equals(Common.get(cedb1000.getPrefixNo())) ) {
			cedb1000Dao.updateGetDateTime(cedb1000);
		}
	}

	public void updateCedb1000Appendix(String prefixNo, String isPrefixForm,
			String docType, String prefixFormNo, String isOtherForm,
			String isSpec, String isOtherSpec, String otherSpecRemark,
			String userId) {
		if (!"".equals(Common.get(prefixNo))) {
			cedb1000Dao.updateAppendix(prefixNo, isPrefixForm, docType,
					prefixFormNo, isOtherForm, isSpec, isOtherSpec,
					otherSpecRemark, userId);
		}
	}

	public void updateCedb1000(Cedb1000 cedb1000) {
		cedb1000Dao.update(cedb1000);
	}

	public DeclaratoryStatutes insertDeclaratoryStatutes(DeclaratoryStatutes obj) {
		return declaratoryStatutesDao.insert(obj);
	}
	
	public void deleteDeclaratoryStatutes(DeclaratoryStatutes obj) {
		declaratoryStatutesDao.delete(obj);
	}
	
	public int findOnlineDocNum() {
		return receiveDao.findOnlineDocNum();
	}
	
	/**
	 * 根據統編尋找有限合夥資料
	 * 113/04/17
	 * @param banNo
	 * @return LmsmBussMain
	 */
	public LmsmBussMain getLmsmBussMainByBanNo(String banNo) {
		return lmsmBussMainDao.queryLmsInfoByBanNo(banNo);
	}
	
	/**
	 * 更新有限合夥資料
	 * 113/04/19
	 * @param banNo
	 * @return LmsmBussMain
	 */
	public LmsmBussMain updateLmsmBussMain(LmsmBussMain lmsmBussMain) {
		return lmsmBussMainDao.update(lmsmBussMain);
	}

	 /**
	   * 利用PREFIX_NO或TELIX_NO取得oracle資料庫中的預查完整資料
	   * 使用readSingleCedbData方法的功能有-->列印預查申請表,補列印回執聯
	   * @return List
	   * <AUTHOR>
	   */
	public List<Form1110> readSingleCedbData(String prefixNo) {

	    Form1110 theForm = new Form1110();
		List<Form1110> oracledata = new ArrayList<Form1110>();
		try {
			
			Cedb1000 cedb1000 = new Cedb1000();

			// 公司名稱及所營事業登記預查申請表時 需要在 審查結果 列出 cedb1011資料: 外商註記 及 合併消滅註記
			if (prefixNo != null
					&& (prefixNo.length() == 11 || prefixNo.length() == 16)) {
				cedb1000 = cedb1000Dao.findByTelixNo(prefixNo, null);
				prefixNo = cedb1000.getPrefixNo();
			} else {
				cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
			}
			List<Cedb1001> vc3000 = new ArrayList<Cedb1001>();
			List<Cedb1002> vc3100 = new ArrayList<Cedb1002>();
			List<Cedb1001> vc1001 = cedb1001Dao.findByPrefixNo(prefixNo);
			List<Cedb1002> vc1002 = cedb1002Dao.findByPrefixNo(prefixNo);
			
			// Vector vcee1002 = (Vector) cedb1000.getEedb1002Collection();
			Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(cedb1000.getPrefixNo());
			Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(cedb1000.getPrefixNo());
			// added by girlie 20080625 從資料庫讀Cedb1011資料given PrefixNo
			Cedb1011 cedb1011 = null;

			PropertyUtils.copyProperties(theForm, cedb1000);
			// 將cedb1000與eedb名稱不同的欄位的數值做搬移動作
			theForm.setContactTel(cedb1000.getApplyTel());
			theForm.setCompanyName(cedb1000.getOldCompanyName());
			theForm.setCompanyAddr(cedb1000.getApplyAddr());
			// getApplyKind傳送回來的資料為1 or 2 必須轉換成z1或z2才方便報表列印時的判斷
			theForm.setCaseCode("Z" + cedb1000.getApplyKind());
			theForm.setContactGetKind(cedb1000.getGetKind());
			// cedb1022與cedb1023有可能無資料
			if (cedb1022 != null) {
				theForm.setOrgCorpName(cedb1022.getApplyLawName());
				theForm.setOrgCorpNo(cedb1022.getApplyBanNo());
			}
			if (cedb1023 != null) {
				theForm.setContactName(cedb1023.getGetName());
				theForm.setContactAddr(cedb1023.getGetAddr());
				// 20050113 因應簡訊功能新增欄位
				theForm.setContactCel(cedb1023.getContactCel());
				theForm.setChangeType(cedb1023.getChangeType());
			}
			// added by girlie 20080625
			// 外商註記 = ForeignMark; 合併消滅註記= CompanyName and BanNo
			// must test if cedb1011 returned a data or not
			try {
				List<Cedb1011> cedb1011s = cedb1011Dao.findByPrefixNo(cedb1000.getPrefixNo());
				
				if(!cedb1011s.isEmpty())
					cedb1011 = (Cedb1011) cedb1011s.get(0);
			} catch (Exception e) {
				e.printStackTrace();
			}
			if (cedb1011 != null) {
				theForm.setC1011ForeignMark(cedb1011.getForeignMark());
				theForm.setC1011CompanyName(cedb1011.getCompanyName());
				theForm.setC1011BanNo(cedb1011.getBanNo());
				theForm.setC1011ChinaMark(cedb1011.getChinaMark());
			} else {
				theForm.setC1011ForeignMark("");
				theForm.setC1011CompanyName("");
				theForm.setC1011BanNo("");
				theForm.setC1011ChinaMark("");
			}

			// 轉換內部資料型態
			chanegObjType(vc3000, vc1001, 1);
			chanegObjType(vc3100, vc1002, 2);

			// 按照SEQ_NO 排序
			java.util.Collections.sort(vc3000, new SeqNoComparatorFor3000());
			java.util.Collections.sort(vc3100, new SeqNoComparatorFor3100());
			theForm.setApplyCompanyNames(vc3000);
			theForm.setBussinessItems(vc3100);

			if ("9".equals(theForm.getPrefixStatus())
					|| "A".equals(theForm.getPrefixStatus())
					|| "E".equals(theForm.getPrefixStatus())) {
				try {
					Cedb1010 cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(
							theForm.getPrefixNo(), theForm.getPrefixStatus());
					if (cedb1010 != null) {
						theForm.setWithdrawDate(StringUtility
								.str2DateTwStr(cedb1010.getProcessDate()));
					}
					
					theForm.setApplyCompanyName(cedb1007Dao.findApproveCmpyName(prefixNo));
				} catch (Exception ex) {
					if(logger.isInfoEnabled()) logger.info("取得案件撤件資料發生錯誤:"
							+ theForm.getPrefixNo());
				}
			}

			oracledata.add(theForm);
		} catch (Exception ex) {
			if(logger.isInfoEnabled()) logger.info("[取得ORACLE預查資料發生錯誤]");
			ex.printStackTrace();
		}
		return oracledata;
	}
	
	// 判斷申請方式.
	public String getApplyWayByTelixNo(String telixNo) {
		if (telixNo == null || telixNo.length() == 0)
			return "紙本送件";
		if (telixNo.startsWith("A"))
			return "線上申辦";
		if (telixNo.startsWith("Z") || telixNo.startsWith("0"))
			return "一維條碼";
		if (telixNo.startsWith("L"))
			return "線上審核";
		if (telixNo.startsWith("O"))
			return "一站式";
		return "無法判斷";
	}
	
	public File printApplyForm(String prefixNo, String searchType) {
		String orgType = "";
		boolean reportContinue = false;
		List<Cedb1002> cedb1002sBefore5 = new ArrayList<Cedb1002>();
	    List<Cedb1002> cedb1002sAfter5 = new ArrayList<Cedb1002>();
		List<Form1110> formList = new ArrayList<Form1110>();
		Collection<Map<String, ?>> list_form = new ArrayList<Map<String, ?>>();
		Map<String, Object> parameters = new HashMap<String, Object>();
		//以電子流水號查詢（線上申辦or一維）
		if(searchType != null && "1".equals(searchType)) {
			List<Form1110> forms = readSingleCedbData(prefixNo);
			Map<String, Object> dataForm = new HashMap<String, Object>();
			if(!forms.isEmpty()) {
				formList.add(forms.get(0));
				list_form.add(dataForm);
				parameters = new HashMap<String, Object>();
			    parameters.put("approveString", getApproveString((Form1110) forms.get(0)));
			} else {
				return null;
			}
		} else {
		    Form1110 theForm = new Form1110();
		    Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		    Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
		    if (cedb1000 == null )
		    	return null;
		    List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(prefixNo); //公司名稱

			//是否要顯示審查結果
			boolean isPrintApprove = false;
			if(!"onLineCnt".equals(searchType)){
				isPrintApprove = true;
			} else {
			    if("onLineCnt".equals(searchType) && (cedb1000.getTelixNo().startsWith("OSC") ||cedb1000.getTelixNo().startsWith("OSS"))) {
					//線上審核案件要有審查結果
			    	OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(cedb1000.getTelixNo());
			    	if(ossmApplMain != null && PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType()))
			    		isPrintApprove = true;
			    }
			}

		    //公司名稱固定為5列，並列印以下空白
		    if(cedb1001s.size() != 5) {
		    	final int totals = cedb1001s.size();
	    		Cedb1001 cedb1001;
		    	for (int i = totals; i < 5; i++) {
		    		if(i == totals) {
		    			cedb1001 = new Cedb1001();
				    	cedb1001.setSeqNo(String.format("%02d", (i+1)));
				    	cedb1001.setCompanyName("以下空白");
					} else {
						cedb1001 = new Cedb1001();
				    	cedb1001.setSeqNo(String.format("%02d", (i+1)));
				    	cedb1001.setCompanyName("");
					}
			    	cedb1001s.add(cedb1001);
				}
		    }

		    List<Cedb1002> cedb1002s = cedb1002Dao.findByPrefixNo(prefixNo); //營業項目
		    for (int i=0;i<cedb1002s.size();i++) {
		    	if ("null".equals(cedb1002s.get(i).getBusiItemNo()))
		    			cedb1002s.get(i).setBusiItemNo("");
		    }

		    List<Cedb1011> cedb1011s = cedb1011Dao.findByPrefixNo(prefixNo);   // 外商註記
		    Cedb1011 cedb1011 = new Cedb1011();
		    try {
				if(!cedb1011s.isEmpty())
					cedb1011 = (Cedb1011) cedb1011s.get(0);
			} catch (Exception e) {
				e.printStackTrace();
			}
			if (cedb1011 != null) {
				theForm.setC1011ForeignMark(cedb1011.getForeignMark());
				theForm.setC1011CompanyName(cedb1011.getCompanyName());
				theForm.setC1011BanNo(cedb1011.getBanNo());
				theForm.setC1011ChinaMark(cedb1011.getChinaMark());
			} else {
				theForm.setC1011ForeignMark("");
				theForm.setC1011CompanyName("");
				theForm.setC1011BanNo("");
				theForm.setC1011ChinaMark("");
			}
		    
		    if ( cedb1002s.size() > 5 )
		    	reportContinue = true;
		    for ( int i=0; i<cedb1002s.size() ;i++ ) {
		    	if ( i <= 4 )
		    		cedb1002sBefore5.add(cedb1002s.get(i));
		    	else 
		    		cedb1002sAfter5.add(cedb1002s.get(i));
		    }
		    
		    if ( cedb1002sBefore5.size() < 5 ) {
		    	Cedb1002 cedb1002 = new Cedb1002();
		    	cedb1002.setSeqNo("");
		    	cedb1002.setBusiItemNo("");
		    	cedb1002.setBusiItem("以下空白");
		    	cedb1002sBefore5.add(cedb1002);
		    	int index = cedb1002sBefore5.size() - 1;
		    	while ( index < 4 ) {
		    		cedb1002 = new Cedb1002();
			    	cedb1002.setSeqNo("");
			    	cedb1002.setBusiItemNo("");
			    	cedb1002.setBusiItem("");
			    	cedb1002sBefore5.add(cedb1002);
			    	index++;
		    	}
		    }
		    
		    try {
				BeanUtils.copyProperties(theForm, cedb1000);
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}

		    theForm.setApplyCompanyNames(cedb1001s);
		    theForm.setBussinessItems(cedb1002sBefore5);
		    theForm.setApplyWay(theForm.getApplyWay());
		    
			theForm.setContactTel(cedb1000.getApplyTel());
			theForm.setCompanyName(cedb1000.getOldCompanyName());
			//if (lengthOfApplyAddr >= 50)
			//	theForm.setCompanyAddr2(cedb1000.getApplyAddr());	
			//else
				theForm.setCompanyAddr(cedb1000.getApplyAddr());
			// getApplyKind傳送回來的資料為1 or 2 必須轉換成z1或z2才方便報表列印時的判斷
			theForm.setCaseCode("Z" + cedb1000.getApplyKind());
			theForm.setContactGetKind(cedb1000.getGetKind());
			
			if (cedb1023 != null) {
				theForm.setContactName(cedb1023.getGetName());
				theForm.setContactAddr(cedb1023.getGetAddr());
				// 20050113 因應簡訊功能新增欄位
				theForm.setContactCel(cedb1023.getContactCel());
				theForm.setChangeType(cedb1023.getChangeType());
			}
			
			if (PrefixConstants.PREFIX_STATUS_9.equals(theForm.getPrefixStatus())
					|| PrefixConstants.PREFIX_STATUS_A.equals(theForm.getPrefixStatus())
					|| PrefixConstants.PREFIX_STATUS_E.equals(theForm.getPrefixStatus())) {
				try {
					Cedb1010 cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(theForm.getPrefixNo(), theForm.getPrefixStatus());
					if (cedb1010 != null) {
						theForm.setWithdrawDate(StringUtility.str2DateTwStr(cedb1010.getProcessDate()));
					}
					String oldApproveCmpyName = cedb1007Dao.findApproveCmpyName(theForm.getPrefixNo());
					if(CommonStringUtils.isNotEmpty(oldApproveCmpyName)) {
					 	theForm.setApplyCompanyName(oldApproveCmpyName);
					}
				} catch (Exception ex) {
					System.out.println("取得案件撤件資料發生錯誤:" + theForm.getPrefixNo());
				}
			}
			
			if(cedb1023 != null && cedb1023.getChangeType() != null) {
				int intChangeType = Integer.parseInt(cedb1023.getChangeType());
				switch (intChangeType) {
					case 0: {
						theForm.setSetup(true);
						theForm.setChangeName(false);
						theForm.setChangeItem(false);
						break;
					}
					case 1: {
						theForm.setSetup(false);
						theForm.setChangeName(true);
						theForm.setChangeItem(false);
						break;
					}
					case 2: {
						theForm.setSetup(false);
						theForm.setChangeName(false);
						theForm.setChangeItem(true);
						break;
					}
					case 3: {
						theForm.setSetup(false);
						theForm.setChangeName(true);
						theForm.setChangeItem(true);
						break;
					}
					default: {
						theForm.setSetup(false);
						theForm.setChangeName(false);
						theForm.setChangeItem(false);
						break;
					}
				}
				// 9/1使用者表示希望"預查公司名稱列表中"核准之公司名稱後加上"(本案核准之公司名稱，詳見審查結果)"
				if(isPrintApprove) {
				    if ( theForm.isChangeName() || theForm.isSetup() ) {
				    	boolean found = false;
				    	for ( int j = 0; j < cedb1001s.size() && !found; j++ ) {
				    		if ("Y".equals(cedb1001s.get(j).getApproveResult())) {
				    			found = true;
				    			cedb1001s.get(j).setCompanyName(cedb1001s.get(j).getCompanyName()+"　（本案核准之"+("05".equals(cedb1023.getOrgType())?"有限合夥":"公司")+"名稱，詳見審查結果）");
				    		}
				    	}
				    }
				}

			    orgType = cedb1023.getOrgType();
			}

			Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(theForm.getPrefixNo());
			if (theForm.getTelixNo() == null || "".equals(theForm.getTelixNo())) {
				theForm.setTempString("（" +theForm.getApplyWay()+"）");
				if ("紙本送件".equals(theForm.getApplyWay())) {
					if ( cedb1022 != null ) {
						theForm.setApplyName("".equals(Common.get(cedb1022.getApplyLawName())) ? Common.get(cedb1000.getApplyName()) :  Common.get(cedb1000.getApplyName()).indexOf(Common.get(cedb1022.getApplyLawName())) < 0 ? Common.get(cedb1022.getApplyLawName()) + "\n代表人：" + Common.get(cedb1000.getApplyName()):Common.get(cedb1000.getApplyName()));
						theForm.setApplyId(theForm.getApplyId()+"\n"+Common.get(cedb1022.getApplyBanNo()));
					}
				}
			} else {
				if ( null != cedb1022) {
					if ("0".equals(cedb1023.getChangeType())) {
						theForm.setApplyName("".equals(Common.get(cedb1022.getApplyLawName())) ? Common.get(cedb1000.getApplyName()) :  Common.get(cedb1000.getApplyName()).indexOf(Common.get(cedb1022.getApplyLawName())) < 0 ? Common.get(cedb1022.getApplyLawName()) + "\n代表人：" + Common.get(cedb1000.getApplyName()):Common.get(cedb1000.getApplyName()));
						theForm.setApplyId(theForm.getApplyId()+"\n"+Common.get(cedb1022.getApplyBanNo()));
					} else {
						theForm.setApplyName(theForm.getApplyName());
						theForm.setApplyId(theForm.getApplyId());
					}
				} else {
					theForm.setApplyName(theForm.getApplyName());
					theForm.setApplyId(theForm.getApplyId());
				}
				theForm.setTempString("（" + theForm.getApplyWay() + "：" + theForm.getTelixNo() + "）");
			}

			// 103/12/09
			// 不論是否要印出審核結果, 都應該要有以下文字:
			// ＊公司名稱如有違反其他法令，而侵害他人在先權利者，仍應依各該法令規定辦理。
		    if(isPrintApprove) {
			    parameters.put("approveString", getApproveString(theForm));
			} else {
		    	if (!"05".equals(orgType)) {
		    		parameters.put("approveString", "＊公司名稱如有違反其他法令，而侵害他人在先權利者，仍應依各該法令規定辦理。");
		    	}
		    }
		    
		    // 2024/03/18 針對新增線上列印跟自取欄位因應服務端來決定使用哪個
		    theForm.setTakeMethod(theForm.getApplyWay().equals("一站式")?"線上列印":"自取");

		    formList.add(theForm);
		    
		    Map<String, Object> dataForm = new HashMap<String, Object>();
		    dataForm.put("banNo", theForm.getBanNo());
		    dataForm.put("setup", theForm.isSetup());
		    dataForm.put("changeName", theForm.isChangeName());
		    dataForm.put("changeItem", theForm.isChangeItem());
		    dataForm.put("applyId", theForm.getApplyId());
		    dataForm.put("applyName", theForm.getApplyName());
		    dataForm.put("attorName", theForm.getAttorName());
		    dataForm.put("attorNo", theForm.getAttorNo());
		    dataForm.put("contactTel", theForm.getContactTel());
		    dataForm.put("attorTel", theForm.getAttorTel());
		    // 103/12/10 結尾加一個空白符號可以解決特定長度的字串應該換行但ireport卻認定它不用換行的問題
		    if (theForm.getCompanyAddr() == null)
		    	dataForm.put("companyAddr",theForm.getCompanyAddr());
		    else
		    	dataForm.put("companyAddr",theForm.getCompanyAddr()+"\t");
		    dataForm.put("attorAddrF", theForm.getAttorAddr());
		    dataForm.put("companyName", theForm.getCompanyName());
		    dataForm.put("contactCel", theForm.getContactCel());
		    dataForm.put("closed", cedb1023.getClosed());
		    list_form.add(dataForm);
		}
		parameters.put("nameOrOrgCorpName", "姓名（或法人名稱及其代表人）");			    
	    parameters.put("idOrOrgCorpNo", "身分證（或法\n人）統一編號");
	    if ("05".equals(orgType)) {//orgType:05 有限合夥
	    	parameters.put("addrOrCompanyAddr", "戶籍地址(或\n有限合夥地址)");
	    	parameters.put("attorAddr", "地址");
	    }  else {
	    	parameters.put("addrOrCompanyAddr", "戶籍地址\n(或公司地址)");
	    	parameters.put("attorAddr", "事 務 所\n所 在 地");
	    }
	    parameters.put("constantString", "簽名或\n蓋章");

		String jasperPath = ""; 
		String jasperPath_approveString = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_subreport2.jasper");
		String jasperPath_mainReport = "";
		String jasperPath_continue = "";
		if ("05".equals(orgType)) {//orgType:05 有限合夥
			jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_limit.jasper");
			jasperPath_mainReport = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_subreport_limit.jasper");
		} else {
			jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1.jasper");
			jasperPath_mainReport = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_subreport_closed.jasper");
		}
		
		parameters.put("SUB_REPORT_APPROVESTRING", jasperPath_approveString);
		parameters.put("SUB_REPORT_MAINREPORT", jasperPath_mainReport);
		Collection<Map<String, ?>> list_approveString = new ArrayList<Map<String, ?>>();
		Map<String, Object> data;
    	for (int i = 0; i < 5; i++) {
    		data = new HashMap<String, Object>();
    		data.put("seqNo", "1");
    		data.put("busiItemNo", "2");
    		data.put("busiItem", "3");
    		list_approveString.add(data);
    	}
    	parameters.put("LIST_MAINREPORT", new JRMapCollectionDataSource(list_form));
    	parameters.put("LIST_APPROVESTRING", new JRMapCollectionDataSource(list_approveString));
	    if ( reportContinue ) {
	    	if ("05".equals(orgType)) {//orgType:05 有限合夥
	    		jasperPath_continue = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_subreport1_limit.jasper");
	    	} else {
	    		jasperPath_continue = ServiceGetter.getInstance().getWebContextInfo().getRealPath("/tcfi/report/pre1004_1_subreport1.jasper");
	    	}
	    	
	    	//補空白列
			Cedb1002 blank = new Cedb1002();
			blank.setSeqNo("");
			blank.setBusiItemNo("");
			blank.setBusiItem("以下空白");
			cedb1002sAfter5.add(blank);
	    	
	    	Collection<Map<String, ?>> list = new ArrayList<Map<String, ?>>();
	    	for (int i = 0; i < cedb1002sAfter5.size(); i++) {
	    		data = new HashMap<String, Object>();
	    		data.put("seqNo", cedb1002sAfter5.get(i).getSeqNo());
	    		data.put("busiItemNo", cedb1002sAfter5.get(i).getBusiItemNo());
	    		data.put("busiItem", cedb1002sAfter5.get(i).getBusiItem());
	    		list.add(data);
	    	}
	    	
	    	// Map<String, Object> parameters = new HashMap<String, Object>();
	    	parameters.put("LIST", new JRMapCollectionDataSource(list));
	    	parameters.put("SUB_REPORT", jasperPath_continue);
		    parameters.put("nameOrOrgCorpNameSUB", "申請人姓名\n（或法人名稱及其代表人）");			    
		    parameters.put("idOrOrgCorpNoSUB", "身分證\n（或法人統一編號）");
	    	parameters.put("", jasperPath_continue);
	    }

	    File pdfReport = null;
	    try {
			pdfReport =  JasperReportMaker.makePdfReport(formList, parameters, jasperPath);
			if(null!=pdfReport) {
				logger.info("[申請表輸出路徑]["+ pdfReport.getAbsolutePath() +"]");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	    return pdfReport;
	}

	public String findMaxPrefixNo(String twYear) {
		return cedb1019Dao.findMaxPrefixNo(twYear);
	}

	public int updateCsmmCmpyInfoByPartName(String banNo, String newPartName) {
		return csmmCmpyInfoDao.updatePartNameByBanNo(banNo, newPartName);
	}

	public List<CsmmCmpyInfo> getCsmmCmpyInfoByBanNo(String banNo) {
		return csmmCmpyInfoDao.query(banNo);
	}

	public List<CsmmCmpyInfo> getCsmmCmpyInfosByCondition(String banNo,
			String companyName, String prefixNo, String specialName,
			String respName, String respIdNo) {
		return csmmCmpyInfoDao.findByCondition(banNo, companyName, prefixNo,
				specialName, respName, respIdNo);
	}
	
	public CsyssUser getStaffInfoById(String idNo) {// 2024/05/20 新增
		return csyssUserDao.getSatffInfoByUserIdAndStaffName(idNo);
	} 

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public void chanegObjType(List vc_add, List vc_source, int objtype)
			throws Exception {
		Object obj = null;
		Object obj2 = null;
		Eedb3000 eedb3000 = null;
		Eedb3100 eedb3100 = null;
		Cedb1001 cedb1001 = null;
		Cedb1002 cedb1002 = null;
		for (int i = 0; i < vc_source.size(); i++) {
			eedb3000 = new Eedb3000();
			eedb3100 = new Eedb3100();
			cedb1001 = new Cedb1001();
			cedb1002 = new Cedb1002();
			obj = null;
			obj2 = null;

			switch (objtype) {
				case 1:
					cedb1001 = (Cedb1001) vc_source.get(i);
					obj = cedb1001;
					obj2 = eedb3000;
					break;
				case 2:
					cedb1002 = (Cedb1002) vc_source.get(i);
					if ("null".equals(cedb1002.getBusiItemNo()));
						cedb1002.setBusiItemNo("");
					obj = cedb1002;
					obj2 = eedb3100;
					break;
			}

			PropertyUtils.copyProperties(obj2, obj);
			vc_add.add(obj2);
		}
	}

	private String getApproveString(Form1110 theForm) {
		// 1. 公司名稱
		// 2. 所營事業
		// 3. 外商, 合併消滅, 陸商註記
		// 4. 檢還撤件註記
		// 5. 其他審查結果
		Cedb1000 cedb1000 = getCedb1000ByPrefixNo(theForm.getPrefixNo());
		List<Cedb1001> cedb1001s = getCedb1001ByPrefixNo(cedb1000.getPrefixNo());
		Cedb1023 cedb1023 = getCedb1023ByPrefixNo(cedb1000.getPrefixNo());
		String orgTypeName = ("05".equals(cedb1023.getOrgType())?"有限合夥":"公司");
		
		boolean isApproveY = PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult());
		boolean isApproveN = PrefixConstants.APPROVE_RESULT_N.equals(cedb1000.getApproveResult());

		List<String> lines = new ArrayList<String>();
        if(isApproveY || isApproveN) {
	        if(theForm.isSetup() || theForm.isChangeName()) {
		        int itemIdx = 0;
	        	for(Cedb1001 cedb1001 : cedb1001s) {
	        		itemIdx++;
	        		if(CommonStringUtils.isNotEmpty(cedb1001.getCompanyName())) {
						if ("Y".equals(cedb1001.getApproveResult())) {
							lines.add("經查" + orgTypeName + "名稱第" + itemIdx + "欄符合規定，應予核准保留。");
							break;
						} else {
							String remark = "";
							if(CommonStringUtils.isNotEmpty(cedb1001.getRemark())) {
								remark = "，" + cedb1001.getRemark();
							}
							lines.add("經查" + orgTypeName + "名稱第" + itemIdx + "欄" + remark + "，不予核准。");
						}
	        		}
	        	}
	        }
			
			if (isApproveY && theForm.isChangeItem() ) {
				lines.add("查本件所營事業符合規定，應予核准保留。");
			}
			
			// 若外商註記 有值 要列出
			if (CommonStringUtils.isNotEmpty(theForm.getC1011ForeignMark())) {
				String closeDate = null;
				if (isApproveY) {
					closeDate = cedb1000.getCloseDate();
				}
				
				if (isApproveN) {
					closeDate = cedb1000.getApproveDate();
				}
				
				// 准駁日為107.11.1前採用舊申請表. HB@20190304
				if (StringUtils.isNotBlank(closeDate) &&
						TaiwanDateUtil.parseToDate(closeDate).before(TaiwanDateUtil.parseToDate("1071101"))) {
					lines.add("非外商認許公司不得使用「" + theForm.getC1011ForeignMark() + "」商字樣。");
				} else {
					lines.add("非外商公司不得使用「" + theForm.getC1011ForeignMark() + "」商字樣。");
				}
			}
			
			// 若合併消滅註記 有值 要列出
			if (CommonStringUtils.isNotEmpty(theForm.getC1011CompanyName()) && CommonStringUtils.isNotEmpty(theForm.getC1011BanNo()) ) {
				lines.add("此件預查表應於"
						+ theForm.getC1011CompanyName() + "【統一編號："
						+ theForm.getC1011BanNo() + "】合併(分割)消滅後始生效力。");
			}

			if ("Y".equals(theForm.getC1011ChinaMark())) {
				lines.add("非大陸商許可公司，不得使用大陸商字樣。");
			}
			
			if (PrefixConstants.PREFIX_STATUS_9.equals(theForm.getPrefixStatus())) {
				lines.add(theForm.getWithdrawDate() + "申請人檢還原件，自該日起本案失其效力。");
			} else if (PrefixConstants.PREFIX_STATUS_A.equals(theForm.getPrefixStatus())) {
				lines.add(theForm.getWithdrawDate() + "申請人撤回申請，自該日起本案失其效力。");
			} else if (PrefixConstants.PREFIX_STATUS_E.equals(theForm.getPrefixStatus())) {
				lines.add(theForm.getWithdrawDate() + "申請人撤回申請。");
			}

			if(CommonStringUtils.isNotEmpty(cedb1000.getApproveRemark())) {
				lines.add(cedb1000.getApproveRemark().replaceAll("\n", ""));
			}

			if ("Y".equals(cedb1000.getOtherReason()) && CommonStringUtils.isEmpty(cedb1000.getRemark())) {
				lines.add(cedb1000.getRemark1());
			}
		}
        String[] codeToChar = {"一","二","三","四","五","六","七","八","九","十","十一","十二","十三","十四","十五"};
        int idx = 0;
    	StringBuffer sb = new StringBuffer("");
    	for(String line : lines) {
    		sb.append(codeToChar[idx]).append("、").append(line).append("\n");
    		idx++;
    	}
    	sb.append("\n");//空一行
    	sb.append("＊").append(orgTypeName).append("名稱如有違反其他法令，而侵害他人在先權利者，仍應依各該法令規定辦理。");
		return sb.toString();
	}
	
	/**
	 * 組合areacode與addr兩各欄位構成完整的地址
	 * @param areacode String
	 * @param addr String
	 * @return String
	 */
	public String areaCodeAddrBulid(String areacode, String addr) {
		String prefect_addr = "";
		//使用AreaCodeOptionHelper轉換代碼變成縣市名稱
		if (areacode != null && areacode.length() > 0 && addr != null && addr.length() > 0) {
			prefect_addr = AreaCodeOptionHelper.getAreaZoneName(areacode) + addr;
		} else if(addr != null && addr.length() > 0)
			return addr;

		return prefect_addr;
	}

	public String getLastPrefixCompanyName(Cedb1000 cedb1000) {
		if (cedb1000 == null) {
			return null;
		}
		// 沒統編也別查了
		if (cedb1000.getBanNo() == null || cedb1000.getBanNo().trim().equals("")) {
			return null;
		}
		List<Cedb1000> lastCedb1000s = cedb1000Dao.getLastPrefix(cedb1000.getPrefixNo(), cedb1000.getBanNo());

		if (!lastCedb1000s.isEmpty()) {
			int reserve, three;
			reserve = parseInt(lastCedb1000s.get(0).getReserveDate());
			// KyLin : 預查案件只顯示核准日期在三個月內，寫在這裡，前次預查名稱與輔助查詢同步
			three = parseInt(DateTimeFormatter.getDate3MonthAgo());
			if (reserve < three) {
				return null;
			} // KyLin : 結束
			return lastCedb1000s.get(0).getCompanyName();
		}

		System.out.println("----ModelUtil , 查無前次預查公司名稱資料");
		return null;
	}
	
	// 字串轉數字
	private static int parseInt(String str) {
		if (str == null)
			return 0;
		int a = 0;
		try {
			a = Integer.parseInt(str);
		} catch (Exception e) {
			// do nothing
		}
		return a;
	}
	
	/**
	 * 審核畫面用; 根據統編取得公司主檔現況名稱(就是CEDB2000的公司名稱
	 * @param banNo String  統一編號
	 * @return String
	 * <AUTHOR>
	 */
	public String getMainFileCompanyNameByBanNo(String banNo) {
		if (banNo == null || banNo.length() < 8)
			return null;
		try {
			Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
			if (cedb2000 != null) {
				return cedb2000.getCompanyName();
			} else {
				// QASC：11005310013 申請有限合夥變更案會找不到資料，若CEDB2000無資料則去lms撈
				LmsmBussMain mainvo = lmsmBussMainDao.queryLmsInfoByBanNo(banNo);
				if (mainvo != null) {
					return mainvo.getLmsName();
				}
				return null;
			}
		} catch(Exception e) {
			//不能找就回傳null
			//commons.util.debugMsgs.println("Find mainFileCompanyName Exception!!");
			return null;
		}
	}

	private void logWithTime(String msg, Long timeStart, Long timeSegement) {
		timeSegement = System.currentTimeMillis()-timeStart;
		findPrefixDataLog.info(msg+timeSegement);
	}

	/**
	 * 藉由prefixNo一次取得相關的預查資訊
	 * @param prefixNo
	 * @return PrefixVo
	 */
	public PrefixVo findPrefixData(String q_prefixNo, String q_approveResult) {
		Long timeStart = System.currentTimeMillis();
		Long timeSegement = new Long(1);
		findPrefixDataLog.info(q_prefixNo+",1.find prefixData business logic start---"+timeStart);
		Cedb1000 c1000 = cedb1000Dao.findByPrefixNo(q_prefixNo, q_approveResult);
		logWithTime(q_prefixNo+",2.find cedb1000 done---",timeStart, timeSegement);
		
		PrefixVo prefixVo = new PrefixVo();
		if(c1000 != null) {
			String prefixNo = c1000.getPrefixNo();
			String companyStus = c1000.getCompanyStus();
			String reserveDate = c1000.getReserveDate();
			String approveResult = c1000.getApproveResult();
			String prefixStatus = c1000.getPrefixStatus();

			prefixVo = new PrefixVo(c1000);
			
			c1000.setApproveResult(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(c1000.getApproveResult()));
			logWithTime(q_prefixNo+",3.set approveResult done---",timeStart, timeSegement);
			try {
				BeanUtils.copyProperties(prefixVo, c1000);
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			} catch (InvocationTargetException e) {
				e.printStackTrace();
			}
			
			prefixVo.setApplyKind(PrefixDescHelper.getPrefixTypeDesc(c1000.getPrefixNo()));
			logWithTime(q_prefixNo+",4.set applyKind done---",timeStart, timeSegement);
			prefixVo.setLastCompanyName(getLastPrefixCompanyName(c1000));
			logWithTime(q_prefixNo+",5.set lastCompanyName done---",timeStart, timeSegement);
			prefixVo.setPrefixStatusDesc(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(c1000.getPrefixStatus()));
			prefixVo.setCedb1001s(cedb1001Dao.findByPrefixNo(prefixNo));
			prefixVo.setCedb1002s(cedb1002Dao.findByPrefixNo(prefixNo));
			prefixVo.setCedb1003s(cedb1003Dao.findByPrefixNo(prefixNo));
			logWithTime(q_prefixNo+",6.set cedb1001~1003 done---",timeStart, timeSegement);
			List<Cedb1004> cedb1004s = cedb1004Dao.findByPrefixNo(prefixNo);
			logWithTime(q_prefixNo+",7.find cedb1004 done---",timeStart, timeSegement);
			for(Cedb1004 cedb1004: cedb1004s) {
				cedb1004.setCmpyStatus(PrefixDescHelper.getPrefixCompanyStusDesc(cedb1004.getCmpyStatus()));
			}
			logWithTime(q_prefixNo+",8.set companyStatus at cedb1004 done---",timeStart, timeSegement);
			
			prefixVo.setCedb1004s(cedb1004s);
			prefixVo.setCedb1009s(cedb1009Dao.findByPrefixNo(prefixNo));
			prefixVo.setCedb1010s(cedb1010Dao.findByPrefixNo(prefixNo));
			
			
			for(Cedb1010 cedb1010 : (List<Cedb1010>) prefixVo.getCedb1010s()) {
				
				if(PrefixConstants.PREFIX_STATUS_7.equals(cedb1010.getProcessStatus())) {
					prefixVo.setLastKeyin(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
				} else if(PrefixConstants.PREFIX_STATUS_2.equals(cedb1010.getProcessStatus())){
					prefixVo.setLastKeyin(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
				}
				
				cedb1010.setProcessStatus(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(cedb1010.getProcessStatus()));
				cedb1010.setProcessDate(StringUtility.str2DateStr(cedb1010.getProcessDate())+"  "+StringUtility.str2TimeStr(cedb1010.getProcessTime()));
				cedb1010.setIdNo(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
				
			}
			
			logWithTime(q_prefixNo+",9.update cedb1010 done---",timeStart, timeSegement);
			prefixVo.setUpdateName(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(c1000.getUpdateIdNo()));
			
			prefixVo.setCedb1009s(cedb1009Dao.findByPrefixNo(prefixNo));
			prefixVo.setCedb1022(cedb1022Dao.findByPrefixNo(prefixNo));
			prefixVo.setCedb1023(cedb1023Dao.findByPrefixNo(prefixNo));
			
			//帶出審核意見歷史資料 1006
			prefixVo.setCedb1006s(getRemark1HistoryByPrefixNo(prefixNo));
			//郵寄資料
			prefixVo.setCedb1027(cedb1027Dao.findLastOneByPrefixNo(prefixNo));
			//帶出前次預查公司名稱
			prefixVo.setLastCompanyName(getLastPrefixCompanyName(c1000));
			//順便帶出公司主檔現況名稱
			String oldCompanyName = c1000.getOldCompanyName();
			
			logWithTime(q_prefixNo+",10.set cedb1006, cedb1027, lastCompanyName done---",timeStart, timeSegement);
			Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(c1000.getBanNo());
			logWithTime(q_prefixNo+",11.find cedb2000 done---",timeStart, timeSegement);
			if (cedb2000 != null && "Y".equals(cedb2000.getClosed())) {
				prefixVo.setMainFileCompanyName(oldCompanyName != null && !oldCompanyName.isEmpty() ? oldCompanyName + "(閉鎖性)"
						: getMainFileCompanyNameByBanNo(c1000.getBanNo()) + "(閉鎖性)");
			} else {
				prefixVo.setMainFileCompanyName(oldCompanyName != null && !oldCompanyName.isEmpty() ? oldCompanyName
					: getMainFileCompanyNameByBanNo(c1000.getBanNo()));
			}
			logWithTime(q_prefixNo+",12.closeCompany issue done---",timeStart, timeSegement);
			//申請方式
			prefixVo.setApplyWay(getApplyWayByTelixNo(c1000.getTelixNo()));

			//掛號號碼(抓1027的最後一筆)，排除平信
			Cedb1027 cedb1027 = cedb1027Dao.findLastOneByPrefixNo(prefixNo);
			logWithTime(q_prefixNo+",13.find cedb1027 done---",timeStart, timeSegement);
			if(cedb1027 != null && !"04".equals(cedb1027.getPostType())) prefixVo.setPostNo( cedb1027.getPostNo() );

			//收件日期時間
			prefixVo.setReceiveDateTime(Datetime.formatRocDate(c1000.getReceiveDate())+" "+Datetime.formatRocTime(c1000.getReceiveTime()));
			//收文登打日期時間
			prefixVo.setReceiveKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_2));
			//分文日期時間
			prefixVo.setAssignDateTime(Datetime.formatRocDate(c1000.getAssignDate())+" "+Datetime.formatRocTime(c1000.getAssignTime()));
			//審核日期時間
			prefixVo.setApproveDateTime(Datetime.formatRocDate(c1000.getApproveDate())+" "+Datetime.formatRocTime(c1000.getApproveTime()));
			//發文登打日期時間
			prefixVo.setIssueKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_7));
			//發文日期時間
			prefixVo.setCloseDateTime(Datetime.formatRocDate(c1000.getCloseDate())+" "+Datetime.formatRocTime(c1000.getCloseTime()));
			//領件日期時間
			prefixVo.setGetDateTime(Datetime.formatRocDate(c1000.getGetDate())+" "+Datetime.formatRocTime(c1000.getGetTime()));
			//公司設立日期
			prefixVo.setCmpySetupDate(Common.get(cedb2000Dao.findSetupDateByBanNo(c1000.getBanNo())));

			//提示文字
			String reserveTip = "";
			if( "03".equals(companyStus) ) {
				reserveTip = "本案件已完成公司登記";
			} else if( "01".equals(companyStus) || "00".equals(companyStus) ) {
				reserveTip = "本案件正在公司登記中";
			} else if( "Y".equals(approveResult) && "".equals(Common.get(reserveDate)) ) {
				reserveTip = "本案件尚無保留期限";
			} else if( "Y".equals(approveResult) && Datetime.getYYYMMDD().compareTo(Datetime.getDateAdd("d",15,reserveDate)) <= 0 ) {
				reserveTip = "預查名稱保留中";
			} else if( "Y".equals(approveResult) && Datetime.getYYYMMDD().compareTo(reserveDate) > 0 ) {
				//保留期限逾期15天
				reserveTip = "本案件保留期限已過期，請檢查保留期限";
			} else if( "B".equals(prefixStatus) ) {
				reserveTip = "本案件曾經辦過「遺失補發」";
			} else if( "C".equals(prefixStatus) ) {
				reserveTip = "本案件曾經辦過「馬上辦」";
			} else {
			}
			prefixVo.setReserveTip(reserveTip);
		}
		logWithTime(q_prefixNo+",14.setReserveTip done---",timeStart, timeSegement);
		findPrefixDataLog.info(q_prefixNo+",15.find PrefixData business logic complete---"+System.currentTimeMillis());
		return prefixVo;
	}
	
	
	/**
	 * 審核畫面用; 根據預查編號取得審核意見的歷史記錄(CEDB1006)
	 * @param prefixNo String  預查編號
	 * @return Vector
	 * <AUTHOR>
	 */
	public List<Cedb1006> getRemark1HistoryByPrefixNo(String prefixNo) {
		List<Cedb1006> vc = new ArrayList<Cedb1006>();
		List<Cedb1006> cedb1006List = new ArrayList<Cedb1006>();
		List<Cedb1006> cedb1006s = null;
		Cedb1006 cedb1006;
		try {
			cedb1006s = cedb1006Dao.findByPrefixNo(prefixNo);
		} catch (Exception ex) {
			System.out.println("查無1006資料");
			return cedb1006List;
		}
		
		if (!cedb1006s.isEmpty()) {
			vc = cedb1006s;
			for (int i = 0; i < vc.size(); i++) {
				cedb1006 = (Cedb1006) vc.get(i);
				// 只顯示非空的審核意見
				if (cedb1006.getRemark1() != null && !"".equals(cedb1006.getRemark1())) {
					String str = cedb1006.getRemark1()
							+ " - "
							+ StringUtility.chgNullToEmpty((String) ServiceGetter.getInstance().getCedbc000CodeLoader()
									.getNameById(cedb1006.getUpdateIdNo())) + " - "
							+ StringUtility.str2DateStr(cedb1006.getUpdateDate());
					cedb1006.setRemark1(str);
					cedb1006List.add(cedb1006);
				}
			}
		}
		
		return cedb1006List;
	}
	

	public List<Cedbc053> queryCedbc053(Cedbc053 bo) {
		return cedbc053Dao.find(bo);
	}

	public List<CsmdCountry> queryCsmdCountry(CsmdCountry bo) {
		return csmdCountryDao.find(bo);
	}
	
	public List<Cedbc055> queryAllCedbc055( String itemCode, String businessItem, String masterCode ) {
		return cedbc055Dao.findAllCondition( itemCode, businessItem, masterCode );
	}
	
	public List<BusiItem> queryAllBusiItem( String itemCode, String businessItem, String masterCode, int length) {
		return busiItemDao.findAllCondition( itemCode, businessItem, masterCode, length);
	}
	
	public Cedbc055 queryCedbc055ByItemCode( String itemCode ) {
		return cedbc055Dao.findByItemCode( itemCode );
	}
	
	public BusiItem queryBusiItemByItemCode( String itemCode ) {
		return busiItemDao.findByItemCode( itemCode );
	}
	
	public int saveCedbc055( Cedbc055 obj ) {
		return cedbc055Dao.update( obj );
	}
	
	public BusiItem saveBusiItem( BusiItem obj ) {
		return busiItemDao.update( obj );
	}
	
	public int insertCedbc055( Cedbc055 obj ) {
		return cedbc055Dao.insert( obj );
	}
	
	public BusiItem insertBusiItem( BusiItem obj ) {
		return busiItemDao.insert( obj );
	}
	
	public int deleteCedbc055( Cedbc055 obj ) {
		return cedbc055Dao.delete(obj) ;
	}
	
	public void deleteBusiItem( BusiItem obj ) {
		busiItemDao.delete(obj) ;
	}

	/** Cedb1028Dao 清算完結資料檔 Start*/
	public Cedb1028 getCedb1028ByBanNo(String banNo) {
		return cedb1028Dao.findByBanNo(banNo);
	}
	public Cedb1028 getCedb1028ByReceiveNo(String receiveNo) {
		return cedb1028Dao.findByReceiveNo(receiveNo);
	}
	public Cedb1028 updateCedb1028(Cedb1028 obj) {
		return cedb1028Dao.update(obj);
	}
	public Cedb1028 insertCedb1028(Cedb1028 obj) {
		return cedb1028Dao.insert(obj);
	}
	public void deleteCedb1028(String banNo){
		cedb1028Dao.delete(banNo);
	}
	/** Cedb1028Dao 清算完結資料檔 End*/
	
	/** CmpyMemoInfoDao 公司名稱管制備忘檔 Start*/
	public CmpyMemoInfo getCmpyMemoInfoByRcvNo(String rcvNo) {
		List<CmpyMemoInfo> list = cmpyMemoInfoDao.query(rcvNo, null, null, null);
		return list.isEmpty()? null: list.get(0);
	}
	public List<CmpyMemoInfo> getCmpyMemoInfoByCondition(String rcvNo, String companyName, String reserveDateS, String reserveDateE) {
		return cmpyMemoInfoDao.query(rcvNo, companyName, reserveDateS, reserveDateE);
	}
	public void updateCmpyMemoInfo(CmpyMemoInfo obj) {
		cmpyMemoInfoDao.update(obj);
	}
	public void insertCmpyMemoInfo(CmpyMemoInfo obj) {
		cmpyMemoInfoDao.insert(obj);
	}
	public void deleteCmpyMemoInfo(String rcvNo) {
		cmpyMemoInfoDao.delete(rcvNo);
	}
	/** CmpyMemoInfoDao 公司名稱管制備忘檔 Start*/
	
	/** FunctionMenuDao 功能選單 Start*/
	public FunctionMenu getFunctionMenuById(int id){
		return functionMenuDao.findById(id);
	}
	public FunctionMenu getFunctionMenuByCode(String code){
		return functionMenuDao.findByCode(code);
	}
	public List<FunctionMenu> getFunctionMenuByPid(int pid){
		return functionMenuDao.findByPid(pid);
	}
	public List<FunctionMenu> getFunctionMenuAll(){
		return functionMenuDao.findAll();
	}
	public List<FunctionMenu> getFunctionMenuByGroupId(String groupId){
		return functionMenuDao.findByGroupId(groupId);
	}
	public List<FunctionMenu> getFunctionMenuByNotExistsAuth(String groupId){
		return functionMenuDao.findByNotExistsAuth(groupId);
	}
	public FunctionMenu insertFunctionMenu(FunctionMenu obj){
		return functionMenuDao.insert(obj);
	}
	public FunctionMenu updateFunctionMenu(FunctionMenu obj){
		return functionMenuDao.update(obj);
	}
	public void deleteFunctionMenu(FunctionMenu obj){
		functionMenuDao.delete(obj);
	}
	/** FunctionMenuDao 功能選單 End*/
	
	/** FunctionMenuAuthDao 功能選單權限檔 */
	public void confirmFunctionMenuAuth(String optype, String groupId, String[] menuId, String editId){
		functionMenuAuthDao.confirmFunctionMenuAuth(optype, groupId, menuId, editId);
	}
	
	/** SystemCodeDao 參數代碼 Start*/
	public SystemCode getSystemCodeById(Integer id) {
		return systemCodeDao.findById(id);
	}
	public List<SystemCode> getSystemCodesByCodeKind(String codeKind) {
		return systemCodeDao.findByCodeKind(codeKind);
	}
	public SystemCode getSystemCodeByCodeKindAndCode(String codeKind, String code) {
		return systemCodeDao.findByPk(codeKind, code);
	}
	public String getSystemCodeNameByCodeKindAndCode(String codeKind, String code) {
		String result = "";
		try{
			SystemCode o = systemCodeDao.findByPk(codeKind, code);
			if(null!=o)	result = o.getCodeName();
		}catch(Exception e) {}
		return result;
	}
	public List<SystemCode> querySystemCode(String codeKind, String codeName){
		return systemCodeDao.findByCondition(codeKind, codeName);
	}
	public SystemCode insertSystemCode(SystemCode obj) {
		return systemCodeDao.insert(obj);
	}
	public SystemCode updateSystemCode(SystemCode obj) {
		return systemCodeDao.update(obj);
	}
	public void deleteSystemCode(SystemCode obj) {
		systemCodeDao.delete(obj);
	}
	public Integer countSystemCode(String codeKind, String codeName) {
		return systemCodeDao.countByCondition(codeKind, codeName);
	}
	/** SystemCodeDao 參數代碼 End*/
	
	/** RestrictionDao 營業項目限制條件資料檔 Start*/
	public Restriction getRestrictionById(Integer id){
		return restrictionDao.findById(id);
	}
	public List<Restriction> getRestriction(){
		return restrictionDao.find();
	}
	public Restriction getRestrictionByCode(String code){
		return restrictionDao.findByCode(code);
	}
	public List<Restriction> getRestrictionByCondition(String code, String name, String enable){
		return restrictionDao.findByCondition(code, name, enable);
	}
	public Restriction insertRestriction(Restriction bo){
		return restrictionDao.insert(bo);
	}
	public Restriction updateRestriction(Restriction bo){
		return restrictionDao.update(bo);
	}
	public void deleteRestriction(Restriction bo){
		restrictionDao.delete(bo);
		restrictionItemDao.deleteByRestrictionId(bo.getId());
	}
	/** RestrictionDao 營業項目限制條件資料檔 End*/
	
	/** RestrictionItemDao 營業項目限制條件營業項目檔 Start*/
	public List<RestrictionItem> getRestrictionItemByRestrictionId(Integer id){
		return restrictionItemDao.findByRestrictionId(id);
	}
	public RestrictionItem getRestrictionItemByCode(Integer id, String code){
		return restrictionItemDao.findByCode(id, code);
	}
	public List<RestrictionItem> getRestrictionItem(){
		return restrictionItemDao.findByRestriction();
	}
	
	public void insertIntoRestrictionItem(RestrictionItem bo) {
		restrictionItemDao.insert(bo);
	}
	
	public void deleteFromRestrictionItem(RestrictionItem bo) {
		restrictionItemDao.delete(bo);
	}
	
	public void confirmRestrictionItem(String optype, String id, String[] item, String editId){
		restrictionItemDao.confirmRestrictionItem(optype, id, item, editId);
	}
	/** RestrictionItemDao 營業項目限制條件營業項目檔 End*/
	
	/** LoginLogDao 登入紀錄檔 Start*/
	public List<LoginLog> getLoginLog(String loginId, String loginName, String startDate, String endDate){
		return loginLogDao.queryByCondition(loginId, loginName, startDate, endDate);
	}
	public void insertLoginLog(LoginLog obj){
		loginLogDao.insert(obj);
	}
	/** LoginLogDao 登入紀錄檔 End*/
	
	/**SameName_Queue*/
	public java.util.List<Queue> getSameNameQueueWithLotsCondition(String prefixNo, String statutas, String dateStart, String dateEnd){
		return sameNameQueueDao.queryWithLotsCondition(prefixNo, statutas, dateStart, dateEnd);
	}
	public Queue getSameNameQueueWithPk(String id){
		return sameNameQueueDao.queryByPk(id);
	}
	public int insertSameNameQueue(Queue obj) {
		return sameNameQueueDao.insert(obj);
	}
	public Queue updateSameNameQueue(Queue obj) {
		return sameNameQueueDao.update(obj);
	}
	public void deleteSameNameQueue(Queue obj) {
		sameNameQueueDao.delete(obj);
	}
	/**SameName_Queue*/
	/**SyncOss_Queue*/ 
	public java.util.List<Queue> getSyncOssQueueWithLotsCondition(String prefixNo, String statutas, String dateStart, String dateEnd){
		return syncOssQueueDao.queryWithLotsCondition(prefixNo, statutas, dateStart, dateEnd);
	}
	public Queue getSyncOssQueueWithPk(String id){
		return syncOssQueueDao.queryByPk(id);
	}
	public int insertSyncOssQueue(Queue obj) {
		return syncOssQueueDao.insert(obj);
	}
	public Queue updateSyncOssQueue(Queue obj) {
		return syncOssQueueDao.update(obj);
	}
	public void deleteSyncOssQueue(Queue obj) {
		syncOssQueueDao.delete(obj);
	}
	/**SyncOss_Queue*/
	
	public SynonymWord querySynonymWordById(String id) {
		return synonymWordDao.queryByPk(id);
	}
	
	public List<SynonymWord> querySynonymWord(SynonymWord obj) {
		return synonymWordDao.find(obj);
	}
	
	public SynonymWord querySynonymWordByCheckWord(String word, String synonymWord) {
		return synonymWordDao.findByCheckWord(word, synonymWord);
	}

	public SynonymWord insertSynonymWord(SynonymWord obj) {
		return synonymWordDao.insert(obj);
		
	}

	public SynonymWord updateSynonymWord(SynonymWord obj) {
		return synonymWordDao.update(obj);
		
	}

	public void deleteSynonymWord(SynonymWord obj) {
		synonymWordDao.delete(obj);
	}
	
	public Eedb1300 getEedb1300ByTelixNo( String telixNo ) {
		return eedb1300Dao.findByTelixNo(telixNo) ;
	}
	
	public boolean checkReserve365(String prefixNo) {
		boolean result = false;
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT COUNT(1) AS C FROM CEDB1002");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.appendSQL("AND busi_item_no in ( select item_code from BUSI_ITEM where RESERVE_365 = 'Y' )");
		sqljob.addParameter(prefixNo);
		List<Map<String,Object>> datas = eicmGeneralQueryDao.queryForList(sqljob.getSQL(), sqljob.getParametersArray());
		if(null!=datas && !datas.isEmpty()) {
			BigDecimal c = (BigDecimal)datas.get(0).get("C");
			if(null!=c && c.intValue() > 0 ) {
				result = true;
			}
		}
		return result;
	}
	
	public void insertPartNameLog(String banNo, String loginIdNo, String oldName, String newName) {
		PartNameLog obj = new PartNameLog();
		obj.setBanNo(banNo);
		obj.setIdNo(loginIdNo);
		obj.setOldName(oldName);
		obj.setNewName(newName);
		obj.setLogDate(Datetime.getYYYMMDD());
		obj.setLogTime(Datetime.getHHMMSS());
		partNameLogDao.insert(obj);
	}
	
	public String countReserveDate(String closeDate, Integer reserveDays, String reserveMark) {
		String result = "";
		try {
			java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
			Date date = sdf.parse( String.valueOf(Integer.parseInt(closeDate)+19110000) );
			Calendar cal = Calendar.getInstance();
			cal.setTime(date);
			if (180 == reserveDays.intValue()) {
				cal.add(Calendar.MONTH, 6);
				cal.add(Calendar.DATE, -1);
			} else if (365 == reserveDays.intValue()) {
				cal.add(Calendar.YEAR, 1);
				cal.add(Calendar.DATE, -1);
			}
			if("Y".equalsIgnoreCase(reserveMark)) {
				cal.add(Calendar.MONTH, 1);
			}
			result = Datetime.getYYYMMDD(cal.getTime());
		} catch (Exception e) {
		}
		return result;
	}
	
	/**
	 * 尚未分文重回收文確認前的狀態
	 */
	@Override
	public void doIsNeedRollBackToReceive(Cedb1000 cedb1000) {
		if( cedb1000.getIdNo() == null && !"N".equals(cedb1000.getRcvCheck()) ) { //已分文時
			cedb1000.setPrefixStatus("2");
			cedb1000.setRcvCheck("N");
			cedb1010Dao.deleteByPrefixNoAndAfterStatus(cedb1000.getPrefixNo(), "1");
		}
	}

	/** 收文確認 */
	public void confirmRcvCheck(String prefixNo, String userId) {
		if(logger.isInfoEnabled()) logger.info("[confirmRcvCheck][prefixNo:"+prefixNo+"]");
		if("".equals(Common.get(prefixNo))) return ;
		//1.備份
		ServiceGetter.getInstance().getBackupService().doBackup(prefixNo, userId);
		//2.更新為已確認(RCV_CHECK=Y)
		cedb1000Dao.updateRcvCheck(prefixNo, "Y", userId);
	}

	public Cedb1010 getCedb1010sByPrefixNoAndStatus(String prefixNo, String status) {
		return cedb1010Dao.findByPrefixNoAndStatus(prefixNo, status);
	}

	public List<Cedb1010> getCedb1010sByPrefixNo(String prefixNo) {
		return cedb1010Dao.findByPrefixNo(prefixNo);
	}

	public Cedb1010 getCedb1010ByUniqueKey(String prefixNo, String idNo, String processStatus) {
		return cedb1010Dao.findByUniqueKey(prefixNo, idNo, processStatus);
	}
	
	public void updateCedb1010(Cedb1010 cedb1010) {
		cedb1010Dao.updateByPrefixNoAndIdNoAndStatus(cedb1010);
	}
	
	public List<Cedb1004> getCedb1004sByPrefixNo(String prefixNo) {
		return cedb1004Dao.findByPrefixNo(prefixNo);
	}

	//同步一站式申辦類型
	public void doSyncOsssApplyType(String prefixNo) {
		if( !"".equals(prefixNo) ) {
			Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1000) {
				String approveResult = Common.get(cedb1000.getApproveResult());
				String telixNo = Common.get(cedb1000.getTelixNo());
				String closeDate = Common.get(cedb1000.getCloseDate());
				//已核准且結案時, 才同步
				if( !"".equals(closeDate) && PrefixConstants.APPROVE_RESULT_Y.equalsIgnoreCase(approveResult) ) {
					if(telixNo.startsWith("OSC") || telixNo.startsWith("OSS") ) {
						OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
						if(null!=ossmApplMain) {
							String oriApplyType = Common.get(ossmApplMain.getApplyType());
							if( PrefixConstants.OSSS_APPLY_TYPE_C1000.equals(oriApplyType) ) {
								//設立案
								String newApplyType = "";
								Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
								if(null!=cedb1023) {
									String changeType = Common.get(cedb1023.getChangeType());
									if( "3".equals(changeType) ) {
										//公司名稱及所營事業變更預查(L1110)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1110;
									} else if( "2".equals(changeType) ) {
										//公司所營事業變更預查(L1010)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1010;
									} else if( "1".equals(changeType) ) {
										//公司名稱變更預查(L1100)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1100;
									} else if( "0".equals(changeType) ) {
										//公司名稱預查(C1000)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_C1000;
									}
								}

								//新舊申辦類型有不一樣的時候, 要同步一下
								if( !oriApplyType.equals(newApplyType) ) {
									//更新申辦類型
									if(logger.isInfoEnabled()) logger.info("預查編號:"+prefixNo+"，網路流水號："+telixNo+"，申辦類型由"+oriApplyType+"變更為"+newApplyType);
									ossmApplMainDao.updateApplyType(telixNo, newApplyType);
								}
							} else if( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(oriApplyType) ) {
								//線上審核, 不管
							} else if( PrefixConstants.OSSS_APPLY_TYPE_L1110.equals(oriApplyType)
								|| PrefixConstants.OSSS_APPLY_TYPE_L1010.equals(oriApplyType)
								|| PrefixConstants.OSSS_APPLY_TYPE_L1100.equals(oriApplyType) ) {
								//變更案
								String newApplyType = "";
								Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
								if(null!=cedb1023) {
									String changeType = Common.get(cedb1023.getChangeType());
									if( "3".equals(changeType) ) {
										//公司名稱及所營事業變更預查(L1110)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1110;
									} else if( "2".equals(changeType) ) {
										//公司所營事業變更預查(L1010)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1010;
									} else if( "1".equals(changeType) ) {
										//公司名稱變更預查(L1100)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_L1100;
									} else if( "0".equals(changeType) ) {
										//公司名稱預查(C1000)
										newApplyType = PrefixConstants.OSSS_APPLY_TYPE_C1000;
									}
								}

								//新舊申辦類型有不一樣的時候, 要同步一下
								if( !oriApplyType.equals(newApplyType) ) {
									//更新申辦類型
									if(logger.isInfoEnabled()) logger.info("預查編號:"+prefixNo+"，網路流水號："+telixNo+"，申辦類型由"+oriApplyType+"變更為"+newApplyType);
									ossmApplMainDao.updateApplyType(telixNo, newApplyType);
									if(PrefixConstants.OSSS_APPLY_TYPE_L1110.equals(newApplyType)) {
										//原本沒勾所營變更, 後來多勾所營變更, 要多同步營業項目資料到一站式
										List<OssmBussItem> ossmBussItems = ossmBussItemDao.findByTelixNo(telixNo);
										if(null==ossmBussItems || ossmBussItems.isEmpty()) {
											List<Cedb1002> cedb1002s = cedb1002Dao.findByPrefixNo(prefixNo);
											if(null!=cedb1002s && !cedb1002s.isEmpty()) {
												for(Cedb1002 cedb1002 : cedb1002s) {
													ossmBussItemDao.insert(telixNo, cedb1002.getSeqNo(), cedb1002.getBusiItemNo(), cedb1002.getBusiItem());
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	public PrefixVo findAjaxCedb1010s(String prefixNo) {
		List <Cedb1010> c1010s = cedb1010Dao.findByPrefixNo(prefixNo);
		PrefixVo prefixVo = new PrefixVo();
		if(c1010s != null && c1010s.size() > 0) {
			prefixVo.setCedb1010s(c1010s);
			for(Cedb1010 cedb1010 : (List<Cedb1010>) prefixVo.getCedb1010s()) {
				cedb1010.setProcessStatus(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(cedb1010.getProcessStatus()));
				cedb1010.setProcessDate(StringUtility.str2DateStr(cedb1010.getProcessDate())+"  "+StringUtility.str2TimeStr(cedb1010.getProcessTime()));
				cedb1010.setIdNo(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
			}
		}
		return prefixVo;
	}

	public Cedb1006 getCedb1006ByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		return cedb1006Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime);
	}

	public List<Cedb1007> getCedb1007sByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		return cedb1007Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime);
	}

	public List<Cedb1008> getCedb1008sByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		return cedb1008Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime);
	}
	
	public List<Cedb1002> getLmsBusiItemByBanNo(String banNo){
		return lmsBusiItemDao.findByBanNo(banNo);
	}

	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}

	public Cedb1007Dao getCedb1007Dao() {return cedb1007Dao;}
	public void setCedb1007Dao(Cedb1007Dao dao) {this.cedb1007Dao = dao;}

	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	
	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}

	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}
	
	public Cedb2004Dao getCedb2004Dao() {return cedb2004Dao;}
	public void setCedb2004Dao(Cedb2004Dao dao) {this.cedb2004Dao = dao;}

	public CmpyMemoInfoDao getCmpyMemoInfoDao() {return cmpyMemoInfoDao;}
	public void setCmpyMemoInfoDao(CmpyMemoInfoDao dao) {this.cmpyMemoInfoDao = dao;}
	
	public DeclaratoryStatutesDao getDeclaratoryStatutesDao() {return declaratoryStatutesDao;}
	public void setDeclaratoryStatutesDao(DeclaratoryStatutesDao dao) {this.declaratoryStatutesDao = dao;}

	public DeclaratoryStatutesRcverDao getDeclaratoryStatutesRcverDao() {return declaratoryStatutesRcverDao;}
	public void setDeclaratoryStatutesRcverDao(DeclaratoryStatutesRcverDao dao) {this.declaratoryStatutesRcverDao = dao;}
	
	public Cedb1019Dao getCedb1019Dao() {return cedb1019Dao;}
	public void setCedb1019Dao(Cedb1019Dao dao) {this.cedb1019Dao = dao;}

	public Cedb1021Dao getCedb1021Dao() {return cedb1021Dao;}
	public void setCedb1021Dao(Cedb1021Dao dao) {this.cedb1021Dao = dao;}
	
	public Cedb1017Dao getCedb1017Dao() {return cedb1017Dao;}
	public void setCedb1017Dao(Cedb1017Dao dao) {this.cedb1017Dao = dao;}

	public ReceiveDao getReceiveDao() {return receiveDao;}
	public void setReceiveDao(ReceiveDao dao) {this.receiveDao = dao;}
	
	public CsmmCmpyInfoDao getCsmmCmpyInfoDao() {return csmmCmpyInfoDao;}
	public void setCsmmCmpyInfoDao(CsmmCmpyInfoDao dao) {this.csmmCmpyInfoDao = dao;}

	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}

	public Cedbc000Dao getCedbc000Dao() {return cedbc000Dao;}
	public void setCedbc000Dao(Cedbc000Dao dao) {this.cedbc000Dao = dao;}
	
	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}

	public OssmApplMainDao getOssmApplMainDao() {return ossmApplMainDao;}
	public void setOssmApplMainDao(OssmApplMainDao dao) {this.ossmApplMainDao = dao;}
	
	public void setOssmOrgNameDao(OssmOrgNameDao ossmOrgNameDao) {this.ossmOrgNameDao = ossmOrgNameDao;}
	public void setOssmBussItemDao(OssmBussItemDao ossmBussItemDao) {this.ossmBussItemDao = ossmBussItemDao;}

	public OssmOrgNameDao getOssmOrgNameDao() {return ossmOrgNameDao;}
	public OssmBussItemDao getOssmBussItemDao() {return ossmBussItemDao;}
	
	public Cedb1009Dao getCedb1009Dao() {return cedb1009Dao;}
	public void setCedb1009Dao(Cedb1009Dao cedb1009Dao) {this.cedb1009Dao = cedb1009Dao;}

	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}
	
	public FunctionMenuDao getFunctionMenuDao() {return functionMenuDao;}
	public void setFunctionMenuDao(FunctionMenuDao functionMenuDao) {this.functionMenuDao = functionMenuDao;}
	
	public FunctionMenuAuthDao getFunctionMenuAuthDao() {return functionMenuAuthDao;}
	public void setFunctionMenuAuthDao(FunctionMenuAuthDao functionMenuAuthDao) {this.functionMenuAuthDao = functionMenuAuthDao;}

	public RestrictionDao getRestrictionDao() {return restrictionDao;}
	public void setRestrictionDao(RestrictionDao restrictionDao) {this.restrictionDao = restrictionDao;}

	public RestrictionItemDao getRestrictionItemDao() {return restrictionItemDao;}
	public void setRestrictionItemDao(RestrictionItemDao restrictionItemDao) {this.restrictionItemDao = restrictionItemDao;}
	
	public LoginLogDao getLoginLogDao() {return loginLogDao;}
	public void setLoginLogDao(LoginLogDao loginLogDao) {this.loginLogDao = loginLogDao;}
	
	public SameNameQueueDao getSameNameQueueDao() {return sameNameQueueDao;}
	public void setSameNameQueueDao(SameNameQueueDao sameNameQueueDao) {this.sameNameQueueDao = sameNameQueueDao;}
	
	public SyncOssQueueDao getSyncOssQueueDao() {return syncOssQueueDao;}
	public void setSyncOssQueueDao(SyncOssQueueDao syncOssQueueDao) {this.syncOssQueueDao = syncOssQueueDao;}

	public Cedbc053Dao getCedbc053Dao() {return cedbc053Dao;}
	public void setCedbc053Dao(Cedbc053Dao cedbc053Dao) {this.cedbc053Dao = cedbc053Dao;}

	public Cedbc055Dao getCedbc055Dao() {return cedbc055Dao;}
	public void setCedbc055Dao(Cedbc055Dao cedbc055Dao) {this.cedbc055Dao = cedbc055Dao;}
	
	public BusiItemDao getBusiItemDao() {return busiItemDao;}
	public void setBusiItemDao(BusiItemDao busiItemDao) {this.busiItemDao = busiItemDao;}

	public SynonymWordDao getSynonymWordDao() {return synonymWordDao;}
	public void setSynonymWordDao(SynonymWordDao dao) {this.synonymWordDao = dao;}

	public Cedbc054Dao getCedbc054Dao() {return cedbc054Dao;}
	public void setCedbc054Dao(Cedbc054Dao dao) {this.cedbc054Dao = dao;}

	public EicmGeneralQueryDao getEicmGeneralQueryDao() {return eicmGeneralQueryDao;}
	public void setEicmGeneralQueryDao(EicmGeneralQueryDao dao) {this.eicmGeneralQueryDao = dao;}

	public PartNameLogDao getPartNameLogDao() {return partNameLogDao;}
	public void setPartNameLogDao(PartNameLogDao dao) {this.partNameLogDao = dao;}

	public Cedb1006Dao getCedb1006Dao() {return cedb1006Dao;}
	public void setCedb1006Dao(Cedb1006Dao dao) {this.cedb1006Dao = dao;}
	
	public com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao getEedb1000DaoEicm() {return eedb1000DaoEicm;}
	public void setEedb1000DaoEicm(com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao dao) {this.eedb1000DaoEicm = dao;}
	
	public com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao getEedb1002Dao() {return eedb1002Dao;}
	public void setEedb1002Dao(com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao dao) {this.eedb1002Dao = dao;}

	public Cedb1027Dao getCedb1027Dao() {return cedb1027Dao;}
	public void setCedb1027Dao(Cedb1027Dao dao) {this.cedb1027Dao = dao;}

	public PostRecordDao getPostRecordDao() {return postRecordDao;}
	public void setPostRecordDao(PostRecordDao dao) {this.postRecordDao = dao;}
	
	public Cedb1003Dao getCedb1003Dao() {return cedb1003Dao;}
	public void setCedb1003Dao(Cedb1003Dao dao) {this.cedb1003Dao = dao;}

	public Cedb1011Dao getCedb1011Dao() {return cedb1011Dao;}
	public void setCedb1011Dao(Cedb1011Dao dao) {this.cedb1011Dao = dao;}

	public Eedb3100Dao getEedb3100Dao() {return eedb3100Dao;}
	public void setEedb3100Dao(Eedb3100Dao dao) {this.eedb3100Dao = dao;}

	public Eedb1300Dao getEedb1300Dao() {return eedb1300Dao;}
	public void setEedb1300Dao(Eedb1300Dao dao) {this.eedb1300Dao = dao;}

	public Cedb1004Dao getCedb1004Dao() {return cedb1004Dao;}
	public void setCedb1004Dao(Cedb1004Dao dao) {this.cedb1004Dao = dao;}

	public Eedb1000Dao getEedb1000Dao() {return eedb1000Dao;}
	public void setEedb1000Dao(Eedb1000Dao dao) {this.eedb1000Dao = dao;}

	public Eedb1100Dao getEedb1100Dao() {return eedb1100Dao;}
	public void setEedb1100Dao(Eedb1100Dao dao) {this.eedb1100Dao = dao;}
	
	public Eedb3000Dao getEedb3000Dao() {return eedb3000Dao;}
	public void setEedb3000Dao(Eedb3000Dao dao) {this.eedb3000Dao = dao;}
	
	public Eedb3300Dao getEedb3300Dao() {return eedb3300Dao;}
	public void setEedb3300Dao(Eedb3300Dao dao) {this.eedb3300Dao = dao;}
	
	public Eedb5000Dao getEedb5000Dao() {return eedb5000Dao;}
	public void setEedb5000Dao(Eedb5000Dao dao) {this.eedb5000Dao = dao;}

	public EedbV8000Dao getEedbV8000Dao() {return eedbV8000Dao;}
	public void setEedbV8000Dao(EedbV8000Dao dao) {this.eedbV8000Dao = dao;}

	public OssmOrgChangeDao getOssmOrgChangeDao() {return ossmOrgChangeDao;}
	public void setOssmOrgChangeDao(OssmOrgChangeDao dao) {this.ossmOrgChangeDao = dao;}

	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}

	public OssmApplFlowDao getOssmApplFlowDao() {return ossmApplFlowDao;}
	public void setOssmApplFlowDao(OssmApplFlowDao dao) {this.ossmApplFlowDao = dao;}

	public OssmFeeMainDao getOssmFeeMainDao() {return ossmFeeMainDao;}
	public void setOssmFeeMainDao(OssmFeeMainDao dao) {this.ossmFeeMainDao = dao;}
	
	public OssmFeeDetailDao getOssmFeeDetailDao() {return ossmFeeDetailDao;}
	public void setOssmFeeDetailDao(OssmFeeDetailDao dao) {this.ossmFeeDetailDao = dao;}
	
	public Cedb1028Dao getCedb1028Dao() {return cedb1028Dao;}
	public void setCedb1028Dao(Cedb1028Dao dao) {this.cedb1028Dao = dao;}

	public Cedb1008Dao getCedb1008Dao() {return cedb1008Dao;}
	public void setCedb1008Dao(Cedb1008Dao dao) {this.cedb1008Dao = dao;}

	public CsmdCountryDao getCsmdCountryDao() {return csmdCountryDao;}
	public void setCsmdCountryDao(CsmdCountryDao dao) {this.csmdCountryDao = dao;}
	
	public LmsmBusiItemDao getLmsBusiItemDao() {return lmsBusiItemDao;}
	public void setLmsBusiItemDao(LmsmBusiItemDao dao) {this.lmsBusiItemDao = dao;}
	
	public LmsmBussMainDao getLmsmBussMainDao() {return lmsmBussMainDao;}
	public void setLmsmBussMainDao(LmsmBussMainDao dao) {this.lmsmBussMainDao = dao;}

	public CsyssUserDao getCsyssUserDao() {//2024/05/20 新增
		return csyssUserDao;
	}
	public void setCsyssUserDao(CsyssUserDao csyssUserDao) {//2024/05/20 新增
		this.csyssUserDao = csyssUserDao;
	}

}