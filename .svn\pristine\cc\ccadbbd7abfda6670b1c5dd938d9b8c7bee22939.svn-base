<!--
程式目的：案件資料查詢 - 備註歷史
程式代號：PRE4001_02
撰寫日期：103.10.22
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4001" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4001_02">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("init".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
function queryOne(v) {
}
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='備註歷史'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg">
	<div id="listContainer">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">備註紀錄</a></th>
    		<th class="listTH" style="text-align:left;width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">異動人員</a></th>
    		<th class="listTH" style="text-align:left;width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">異動日期</a></th>
    		<th class="listTH" style="text-align:left;width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">異動時間</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = { true,false,false,false,false};
  			boolean displayArray[] = {false, true, true, true, true};
  			String[] alignArray = {"left","left","left","left","left"};
  			out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

<tr><td>
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="right">
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開"
				onclick="javascript:window.close();" />&nbsp;
		</td>
	</tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>