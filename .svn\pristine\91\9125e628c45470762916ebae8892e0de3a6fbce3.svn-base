<!--
程式目的：清算完結
程式代號：PRE3006
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3006" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;
var searchType = "";

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkQuery();
		if(form1.q_banNo.value != "" && form1.q_banNo.value.length != 8){
			alertStr += "統一編號請輸入8碼!\n";
		}
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;
	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	searchType = form1.searchType.value;
	var url = getVirtualPath() + "tcfi/pre/pre3006_main.jsp?sendBanNos="+id+"&searchType="+searchType;
	window.open(url,'pre3006',prop);
}



$(document).ready(function() {
	$('#doClear').click(function(){
		form1.q_banNo.value = "";
		form1.q_cmpyOrLmsName.value = "";
		form1.q_receiveNo.value = "";
	});
	$('#doMaintain').click(function(){
		$('#state').val("queryAll");
		$('#q_searchType').val("maintain");
	});
	$('#doSearch').click(function(){
		$('#state').val("queryAll");
		$('#q_searchType').val("search");
	});
	$('#checkAll').click(function(){
		commonUtils.all("selectCases");
	});
	$('#disAll').click(function(){
		commonUtils.unAll("selectCases");
	});
	$('#sendData').click(function(){
		form1.sendBanNos.value = "";
		var $checks = $("input[name=selectCases]").filter(":checked");
		if( $checks.size() > 0){
			var banNos = "";
			for(var i=0; i<$checks.size(); i++) {
				banNos += $checks.eq(i).val() + ",";
			}
			form1.target = "_blank";
			
			form1.sendBanNos.value = banNos;
			form1.state.value = "init";
			searchType = form1.searchType.value;
			form1.action = "pre3006_main.jsp?searchType="+searchType;
			form1.submit();
			form1.action = "";
			form1.target = "";
		}else{
			alert("請至少選取一筆案件!!");	
		}
	});
	
	<% if(objList !=null && objList.size() > 0){ System.out.println(objList.size());%>
		$('#listContainer').show();
	<%}%>	
	
	
	
	$("input[type=checkbox]", "#resultTable").attr("checked", true);
	if( $("#resultTable tr").size() == 2 ) {
		$("#sendData").trigger("click");
	}
	
});
</script>
</head>

<body topmargin="0" >
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3006'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- TOOLBAR AREA -->
<tr><td>
	<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入">&nbsp;
	<input class="toolbar_default" type="submit" followPK="false" id="doMaintain" name="doMaintain" value="登錄清算案件">&nbsp;
	<input class="toolbar_default" type="submit" followPK="false" id="doSearch" name="doSearch" value="查詢清算案件">&nbsp;
	<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
	<input type="hidden" id="sendBanNos" name="sendBanNos" value="<%=obj.getSendBanNos()%>">
	<input type="hidden" id="q_searchType" name="q_searchType" value="<%=obj.getQ_searchType()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<input type="hidden" id="searchType" name="searchType" value="<%=obj.getSearchType()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td nowrap class="bg">
	<table class="table_form" width="100%">
		<tr>
			<td nowrap class="td_form" width="15%">統一編號：</td>
			<td nowrap class="td_form_white">
				<input class="field_Q" type="text" name="q_banNo" size="10" maxlength="10" value="<%=obj.getQ_banNo()%>">
			</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form">公司/有限合夥名稱：</td>
		  	<td nowrap class="td_form_white">
		  		<input class="field_Q cmex" type="text" name="q_cmpyOrLmsName" size="30" maxlength="30" value="<%=obj.getQ_cmpyOrLmsName()%>"><!-- 2024/04/16 配合有限合夥修改名稱 -->
		  	</td>
		</tr>
		<tr>
		  	<td nowrap class="td_form">收文文號：</td>
		  	<td nowrap class="td_form_white">
		  		<input class="field_Q" type="text" name="q_receiveNo" size="15" maxlength="11" value="<%=obj.getQ_receiveNo()%>">
			</td>
		</tr>
	</table>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer" style="display:none;">
	<table class="table_form">
		<tr><td>
		<input id="checkAll" class="toolbar_default" type="button" name="checkAll" value="全部選取">&nbsp;
		<input id="disAll" class="toolbar_default" type="button" name="disAll" value="取消選取">&nbsp;
		<input id="sendData" class="toolbar_default" type="button" name="sendData" value="確認送出">
		</td></tr>
		<tr><td class="tab_line1"></td></tr>
		<tr><td class="tab_line1"></td></tr>
	</table>	
	<TABLE id="resultTable" class="table_form" width="100%" border="0" cellspacing="0" cellpadding="2">
		<TBODY id="listTBODY">
		<tr>
			<td class="listTH" ><a class="text_link_w">選取</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">統一編號</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">公司/有限合夥名稱</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">地址</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">負責人</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">收文文號</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">公司狀態</a></th>
			<td class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">申登機關</a></th>
		</tr>
		<%
		boolean primaryArray[] = {true,false,false,false,false,false,false};
		boolean displayArray[] = {true,true,true,true,true,true,true};
		String[] alignArray = {"center","left","left","left","center","left","left"};
		out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),"selectCases"));
		%>
		</TBODY>
	</TABLE>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>