<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3002" />
</jsp:include>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3002"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
$(window).load(function(){
	var cmpyName = commonUtils.getURLParameter("q_cmpyName");
	if(cmpyName) {
		setTimeout(function() {
			parent.top.window.document.searchFrame1.$('#q_cmpyName').val(decodeURI(cmpyName));
			parent.top.window.document.searchFrame1.$("#doQueryAll").trigger("click");
		}, 100);
	}
});

function changeTab(tabId) {
	if( 'PRE3003' == tabId ) {
		$('#bannerLabel').html("全文檢索(PRE3003)");
		$('#searchFrame1').hide();
		$('#searchFrame2').show();
		document.getElementById("tab3002").className = "tab_border2";
		document.getElementById("tab3003").className = "tab_border1";
		try{
			$('#searchFrame2').contents().find("#q_cmpyName").focus();
			$('#searchFrame2').contents().find("#q_cmpyName").val( $('#searchFrame1').contents().find("#q_cmpyName").val() );
		}catch(e){alert(e.message);};
	} else {
		$('#bannerLabel').html("輔助查詢(PRE3002)");
		$('#searchFrame1').show();
		$('#searchFrame2').hide();
		document.getElementById("tab3002").className = "tab_border1";
		document.getElementById("tab3003").className = "tab_border2";
		try{
			$('#searchFrame1').contents().find("#q_cmpyName").focus();
			$('#searchFrame1').contents().find("#q_cmpyName").val( $('#searchFrame2').contents().find("#q_cmpyName").val() );
		}catch(e){alert(e.message);};
	}
}
function init() {
	changeTab('PRE3002');
	window.moveTo(0,0);
	window.resizeTo(screen.width,screen.height*0.96);
}
</script>
</head>
<body onLoad="init();">
<table width="100%" cellspacing="0" cellpadding="0" >
<!-- BANNER AREA -->
<tr><td>
	<table width="100%">  
		<tr><td class="td_default_banner" width="100%" id="bannerLabel"></td></tr>
	</table>
</td></tr>
<!-- BANNER AREA -->
<!-- TAB AREA -->
<tr><td>
<table cellpadding="0" cellspacing="0" >
	<tr>
		<td>&nbsp;</td>
		<td class="tab_border2" ID="tab3002" onClick="changeTab('PRE3002');">輔助查詢</td>
		<td class="tab_border2" ID="tab3003" onClick="changeTab('PRE3003');">全文檢索</td>
	</tr>
</table>
</td></tr>
<!-- TAB AREA -->
<%
String q_prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
String q_cmpyName = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q_cmpyName")));

String appendUrl = "";
appendUrl += "".equals(q_prefixNo)?"":"&q_prefixNo="+q_prefixNo;
appendUrl += "".equals(q_cmpyName)?"":"&q_cmpyName="+q_cmpyName;
%>
<!-- FRAME AREA -->
<tr><td>
<iframe id="searchFrame1" name="searchFrame1" src='pre3003_00.jsp?tabId=1<%=appendUrl%>' width='100%' height='570' frameborder='0' scrolling='no' ></iframe>
</td></tr>
<tr><td>
<iframe id="searchFrame2" name="searchFrame2" src='pre3003_00.jsp?tabId=2<%=appendUrl%>' width='100%' height='570' frameborder='0' scrolling='no' ></iframe>
</td></tr>
<!-- FRAME AREA -->
</table>
</body>
</html>