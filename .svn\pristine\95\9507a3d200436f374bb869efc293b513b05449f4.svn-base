--DROP TABLE EICM.FUNCTION_MENU_AUTH;
-- Create table
CREATE TABLE EICM.FUNCTION_MENU_AUTH (
	ID INTEGER not null,
	GROUP_ID VARCHAR2(10) not null,
	FUNCTION_MENU_ID INTEGER not null,
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);

-- Add comments to the table 
comment on table EICM.FUNCTION_MENU_AUTH is '功能選單權限檔';
-- Add comments to the columns 
comment on column EICM.FUNCTION_MENU_AUTH.ID is '主鍵值';
comment on column EICM.FUNCTION_MENU_AUTH.GROUP_ID is '群組代號';
comment on column EICM.FUNCTION_MENU_AUTH.FUNCTION_MENU_ID is '功能選單鍵值';
comment on column EICM.FUNCTION_MENU_AUTH.MOD_ID_NO is '異動人員';
comment on column EICM.FUNCTION_MENU_AUTH.MOD_DATE is '異動日期';
comment on column EICM.FUNCTION_MENU_AUTH.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.FUNCTION_MENU_AUTH
  add constraint PK_FUNCTION_MENU_AUTH primary key (ID)
  using index ;

-- Drop sequence
DROP sequence EICM.SEQ_FUNCTION_MENU_AUTH_ID;
-- Create sequence 
create sequence EICM.SEQ_FUNCTION_MENU_AUTH_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_FUNCTION_MENU_AUTH
Before Insert ON EICM.FUNCTION_MENU_AUTH Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_FUNCTION_MENU_AUTH_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.FUNCTION_MENU_AUTH for EICM.FUNCTION_MENU_AUTH;
create or replace synonym EICM4CMPY.FUNCTION_MENU_AUTH for EICM.FUNCTION_MENU_AUTH;
create or replace synonym EICM4PREFIX.FUNCTION_MENU_AUTH for EICM.FUNCTION_MENU_AUTH;

--GRANT
grant all on EICM.FUNCTION_MENU_AUTH to EICM4AP;
grant all on EICM.FUNCTION_MENU_AUTH to EICM4CMPY;
grant all on EICM.FUNCTION_MENU_AUTH to EICM4PREFIX;

--DATA
--GROUP_ID:00 系統管理者
INSERT INTO EICM.FUNCTION_MENU_AUTH (GROUP_ID,FUNCTION_MENU_ID) SELECT '00',ID FROM FUNCTION_MENU;
--GROUP_ID:17 科長主管
INSERT INTO EICM.FUNCTION_MENU_AUTH (GROUP_ID,FUNCTION_MENU_ID) SELECT '17',ID FROM FUNCTION_MENU WHERE CODE not like 'PRE9%';
--GROUP_ID:14 收文
--INSERT INTO FUNCTION_MENU_AUTH (GROUP_ID,FUNCTION_MENU_ID) SELECT '14',ID FROM FUNCTION_MENU WHERE CODE like 'PRE1%';
--GROUP_ID:12 收文登打

--GROUP_ID:11 審核

--GROUP_ID:13 發文

--GROUP_ID:18 檢還撤件

--GROUP_ID:16 馬上辦

--GROUP_ID:19 客服

--GROUP_ID:90 查詢
INSERT INTO EICM.FUNCTION_MENU_AUTH (GROUP_ID,FUNCTION_MENU_ID) SELECT '90',ID FROM FUNCTION_MENU WHERE CODE IN (
	'PRE4','PRE4001'
);


commit;



