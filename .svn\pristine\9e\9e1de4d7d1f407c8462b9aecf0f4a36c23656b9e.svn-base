<%@ page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" %>
<%@ include file="/home/<USER>"%>
<%@ page import="com.kangdainfo.common.util.Common"%>
<%@ taglib uri="http://granule.com/tags" prefix="g" %>

<%
String contextPath = Common.getCurrentContextPath();
%>
<title><%=application.getServletContextName()%></title>
<meta http-equiv="x-ua-compatible" content="IE=Edge" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="Expires" content="-1"/>
<meta http-equiv="pragma" content="no-cache"/>
<meta http-equiv="Cache-control" content="no-cache"/>
<meta name="keywords" content="公司名稱及所營事業預查系統" />

<g:compress>
	<link rel="stylesheet" href="<%=contextPath%>/css/default.css" type="text/css"/>
	<link rel="stylesheet" href="<%=contextPath%>/js/jquery-ui-1.10.4.custom/css/ui-lightness/jquery-ui-1.10.4.custom.min.css" type="text/css">
</g:compress>

<link rel="shortcut icon" href="<c:url value="/images/pre/gcis.ico"/>">

<g:compress>
	<script type="text/javascript" src="<%=contextPath%>/js/tablesoft.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/validate.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/function.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/json.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/jquery/jquery.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/jquery-ui-1.10.4.custom/js/jquery-ui-1.10.4.custom.min.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/jquery/jquery.blockUI.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/jquery/jquery-cookie.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/jquery/jquery.serialize-object.min.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/commonUtils.js"></script>
	<script type="text/javascript" src="<%=contextPath%>/js/md5.js"></script>
	
	<!-- TCFI addon -->
	<script type="text/javascript" src="<%=contextPath%>/js/tcfifunction.js"></script>
</g:compress>

<script>

$(document).ready(function() {
	
	var configs = {
		'pre3001.jsp' : 'pre3001.jsp'
	};
	
	var path = window.location.pathname;
	var functionName = path.substring(path.lastIndexOf("/")+1);
	
	if(configs[functionName]) {
		form1.onkeypress = function(e) {
			if (!e) e = window.event;
			var keyCode = e.keyCode || e.which;
		    if (keyCode == '13') {
		    	doSearch();
		    }
		};
	}
});

</script>
