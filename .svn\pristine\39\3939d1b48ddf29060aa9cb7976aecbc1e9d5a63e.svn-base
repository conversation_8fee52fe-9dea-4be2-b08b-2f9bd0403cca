package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc003;
import com.kangdainfo.util.lang.CommonStringUtils;

public class Cedbc003Dao extends BaseDaoJdbc implements RowMapper<Cedbc003> {

	private static final String SQL_findByPrefixStatus = "SELECT * FROM CEDBC003 WHERE PREFIX_STATUS = ?";
	public Cedbc003 findByPrefixStatus(String prefixStatus) {
		if(null==prefixStatus || "".equals(prefixStatus)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPrefixStatus);
		sqljob.addParameter(prefixStatus);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (Cedbc003) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findAll = "SELECT * FROM CEDBC003";
	public List<Cedbc003> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedbc003>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public Cedbc003 insert(Cedbc003 bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getPrefixStatus())) return null;
		//check exist
		Cedbc003 t = findByPrefixStatus(bo.getPrefixStatus());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO CEDBC003 (");
		sqljob.appendSQL("PREFIX_STATUS");
		sqljob.appendSQL(",PREFIX_STATUS_DESC");
		sqljob.appendSQL(") VALUES (?,?)");
		sqljob.addParameter(bo.getPrefixStatus());
		sqljob.addParameter(bo.getPrefixStatusDesc());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		return findByPrefixStatus(bo.getPrefixStatus());
	}
	
	public Cedbc003 update(Cedbc003 bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getPrefixStatus())) return null;
		//check exist
		Cedbc003 t = findByPrefixStatus(bo.getPrefixStatus());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE CEDBC003 SET");
			sqljob.appendSQL("PREFIX_STATUS_DESC = ?");
			sqljob.appendSQL("WHERE PREFIX_STATUS = ?");
			sqljob.addParameter(bo.getPrefixStatusDesc());
			sqljob.addParameter(bo.getPrefixStatus());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
			return findByPrefixStatus(bo.getPrefixStatus());
		}
	}
	
	public void delete(Cedbc003 bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getPrefixStatus()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM CEDBC003 WHERE PREFIX_STATUS = ?");
			sqljob.addParameter(bo.getPrefixStatus());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public Cedbc003 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc003 obj = null;
		if(null!=rs) {
			obj = new Cedbc003();
			obj.setPrefixStatus(rs.getString("PREFIX_STATUS"));
			obj.setPrefixStatusDesc(rs.getString("PREFIX_STATUS_DESC"));
		}
		return obj;
	}

}