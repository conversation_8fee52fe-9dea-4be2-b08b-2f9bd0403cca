package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 同音同義字對照檔(CEDBC058)
 *
 */
public class Cedbc058 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 同音同義字 */
	private String sameName;
	/** 同音同義字代碼 */
	private String sameName1;
	/** 流水號(新增) */
	private Integer id;
	/** 基本字 */
	private String baseName;
	/** 建立日期  */
	private String createDate;
	private String createTime;
	/** 建立者 */
	private String createUser;
	/** 異動日期 */
	private String updateDate;
	private String updateTime;
	/** 異動者 */
	private String updateUser;
	/** 是否可使用(Y:可使用/N:不可使用(預設Y)) */
	private String canUse;
	/** 是否已使用(Y:已使用/N:未使用(預設N)) */
	private String beUsed;
	/** 狀態(1:新增(預設)/2:修改/3:刪除/9:已更新) */
	private String status;
	/** 字源 */
	private String source;
	/** Y:已生效/N:未生效 */
	private String enabled;
	

	public String getSameName() {return sameName;}
	public void setSameName(String sameName) {this.sameName = sameName;}
	public String getSameName1() {return sameName1;}
	public void setSameName1(String sameName1) {this.sameName1 = sameName1;}
	
	public Integer getId() {return id;}
	public void setId(Integer id) {this.id = id;}
	public String getBaseName() {return baseName;}
	public void setBaseName(String baseName) {this.baseName = baseName;	}
	public String getCreateDate() {return createDate;}
	public void setCreateDate(String createDate) {this.createDate = createDate;}
	public String getCreateUser() {return createUser;}
	public void setCreateUser(String createUser) {this.createUser = createUser;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getCanUse() {return canUse;}
	public void setCanUse(String canUse) {this.canUse = canUse;}
	public String getBeUsed() {return beUsed;}
	public void setBeUsed(String beUsed) {this.beUsed = beUsed;}
	public String getStatus() {return status;}
	public void setStatus(String status) {this.status = status;}
	public String getCreateTime() {return createTime;}
	public void setCreateTime(String createTime) {this.createTime = createTime;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getSource() {return source;}
	public void setSource(String source) {this.source = source;}
	public String getEnabled(){return enabled;}
	public void setEnabled(String enabled) {this.enabled = enabled;}

}