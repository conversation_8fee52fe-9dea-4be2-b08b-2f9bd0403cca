package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;

/**
 * samename_queue 單檔維護
 *
 */
public class PRE9009 extends SuperBean { 
	private int id;
	private String prefixNo;
	private String remark;
	private String processTime;
	private String status;

	private String q_prefixNo;
	private String q_status;
	private String q_dateStart;
	private String q_dateEnd;
  
  // ------------------------------------------------------------------------------------
   
	public int getId() {
		return checkGet(id);
	}

	public void setId(int id) {
		this.id = checkSet(id);
	}

	public String getPrefixNo() {
		return checkGet(prefixNo);
	}

	public void setPrefixNo(String prefixNo) {
		this.prefixNo = checkSet(prefixNo);
	}

	public String getRemark() {
		return checkGet(remark);
	}

	public void setRemark(String remark) {
		this.remark = checkSet(remark);
	}

	public String getProcessTime() {
		return checkGet(processTime);
	}

	public void setProcessTime(String processTime) {
		this.processTime = checkSet(processTime);
	}

	public String getStatus() {
		return checkGet(status);
	}

	public void setStatus(String status) {
		this.status = checkSet(status);
	}

	public String getQ_prefixNo() {
		return checkGet(q_prefixNo);
	}

	public void setQ_prefixNo(String q_prefixNo) {
		this.q_prefixNo = checkSet(q_prefixNo);
	}

	public String getQ_status() {
		return checkGet(q_status);
	}

	public void setQ_status(String q_status) {
		this.q_status = checkSet(q_status);
	}

	public String getQ_dateStart() {
		return checkGet(q_dateStart);
	}

	public void setQ_dateStart(String q_dateStart) {
		this.q_dateStart = checkSet(q_dateStart);
	}

	public String getQ_dateEnd() {
		return checkGet(q_dateEnd);
	}

	public void setQ_dateEnd(String q_dateEnd) {
		this.q_dateEnd = checkSet(q_dateEnd);
	}

	public void doCreate() throws Exception{
		if("".equals(Common.get(getPrefixNo() ) ) ) 
	        throw new MoeaException("預查編號欄位不可空白，請重新輸入!!");

     	Queue obj = new Queue();
     	String prefixNo = getPrefixNo();
     	String status = getStatus();
		obj.setPrefixNo(Common.get(getPrefixNo())) ;
		obj.setRemark(Common.get(getRemark()));
		obj.setProcessTime(Long.parseLong("".equals(Common.get(getProcessTime()))? "0":Common.get(getProcessTime())));
		obj.setStatus("0") ;
		obj.setModId(getLoginUserId());
		obj.setModDate(Datetime.getYYYMMDD());
		obj.setModTime(Datetime.getHHMMSS());
		ServiceGetter.getInstance().getPrefixService().insertSameNameQueue(obj);
		System.out.println(prefixNo+" " +status);
		java.util.List<Queue> QueueList = ServiceGetter.getInstance().getPrefixService().getSameNameQueueWithLotsCondition(prefixNo, status, null, null);
	    Queue queue = QueueList.get(0);
	    this.setId(Integer.parseInt(queue.getId()));
	} // end doCreate()
  
	public void doUpdate() throws Exception{
		if("".equals(Common.get(getId())) ) 
			  throw new MoeaException("修改失敗，請洽系統維運人員！");
		Queue obj = ServiceGetter.getInstance().getPrefixService().getSameNameQueueWithPk(Integer.toString(getId()));
	    if ( obj == null )
	    	  throw new MoeaException( "沒有該案件，請重新輸入" );
	    obj.setId(Common.get(getId()));
	    obj.setPrefixNo(Common.get(getPrefixNo())) ;
		obj.setRemark(Common.get(getRemark()));
		obj.setProcessTime(Long.parseLong(Common.get(getProcessTime())));
		obj.setStatus(Common.get(getStatus())) ;
		obj.setModId(getLoginUserId());
	    ServiceGetter.getInstance().getPrefixService().updateSameNameQueue(obj);
		this.setId(Integer.parseInt(obj.getId()));
	} // end doUpdate()		
  
	public void doDelete() throws Exception{
		Queue queue = ServiceGetter.getInstance().getPrefixService().getSameNameQueueWithPk(Integer.toString(getId()));
		if(queue != null){
			ServiceGetter.getInstance().getPrefixService().deleteSameNameQueue(queue);
		}else{
			throw new MoeaException("查無資料，無法刪除，請重新操作 !");
		}
	} // end doDelete()	
  
	public Object doQueryOne() throws Exception{ 
		PRE9009 pre9009 = this;
		Queue queue = ServiceGetter.getInstance().getPrefixService().getSameNameQueueWithPk(Integer.toString(getId()));
		if ( queue == null ) {
			pre9009.setPrefixNo("");
			pre9009.setRemark("");
			pre9009.setStatus("");
			pre9009.setProcessTime("");
			setErrorMsg( "查無資料" ) ;
		} // end if
		else {
			pre9009.setPrefixNo(queue.getPrefixNo());
			pre9009.setRemark(queue.getRemark());
			pre9009.setStatus(queue.getStatus());
			pre9009.setProcessTime(Long.toString(queue.getProcessTime()));
		} // else
		return pre9009;
	} // end doQueryOne()

	public ArrayList<String[]> doQueryAll() throws Exception {
		List<Queue> tempList = ServiceGetter.getInstance().getPrefixService().getSameNameQueueWithLotsCondition(getQ_prefixNo(), getQ_status(), getQ_dateStart(), getQ_dateEnd());
		java.util.ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>() ;
		if (tempList != null && tempList.size() > 0){
		   for (Queue queue : tempList) {
			 String[] rowArray = new String[6];
			 rowArray[0] = queue.getId();
			 rowArray[1] = queue.getPrefixNo();
			 rowArray[2] = queue.getRemark();
			 rowArray[3] = Long.toString(queue.getProcessTime());
			 if ("0".equals(queue.getStatus())) 
				 rowArray[4] = "待執行";
			 if ("1".equals(queue.getStatus())) 
				 rowArray[4] = "執行中";
			 if ("2".equals(queue.getStatus())) 
				 rowArray[4] = "執行成功";
			 if ("3".equals(queue.getStatus())) 
				 rowArray[4] = "執行失敗";
			 rowArray[5] = queue.getModDate();
			 arrayList.add(rowArray);
			  
		   } // for
		   tempList.clear();
		} // if
		
		return arrayList;
	} // end doQueryAll	
} // PRE9008
