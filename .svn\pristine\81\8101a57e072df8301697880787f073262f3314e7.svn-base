<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="pre1001Service" class="com.kangdainfo.tcfi.service.impl.Pre1001ServiceImpl"
		autowire="byName">
		<property name="systemCodeDao" ref="systemCodeDao"/>
		<property name="ossmApplMainDao" ref="ossmApplMainDao"/>
		<property name="ossmApplFlowDao" ref="ossmApplFlowDao"/>
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="ossmOrgNameDao" ref="ossmOrgNameDao"/>
		<property name="ossmBussItemDao" ref="ossmBussItemDao"/>
		<property name="ossmOrgChangeDao" ref="ossmOrgChangeDao"/>
		<property name="cedbc054Dao" ref="cedbc054Dao"/>
		<property name="cedb1019Dao" ref="cedb1019Dao"/>
		<property name="cedb2000Dao" ref="cedb2000Dao"/>
		<property name="cedb1023Dao" ref="cedb1023Dao"/>
		<property name="cedb1022Dao" ref="cedb1022Dao"/>
		<property name="cedb1002Dao" ref="cedb1002Dao"/>
		<property name="cedb1001Dao" ref="cedb1001Dao"/>
		<property name="cedb2002Dao" ref="cedb2002Dao"/>
		<property name="eedb3100Dao" ref="eedb3100Dao"/>
		<property name="eedb3000Dao" ref="eedb3000Dao"/>
		<property name="eedb3300Dao" ref="eedb3300Dao"/>
		<property name="eedb1000Dao" ref="eedb1000DaoEicm"/>
		<property name="generalityBusitemDao" ref="generalityBusitemDao"/>
		<property name="cedb1010Dao" ref="cedb1010Dao"/>
		<property name="flowLogDao" ref="flowLogDao"/>
		<property name="receiptNoSetupDAO" ref="receiptNoSetupDAO"/>
		<property name="prefixReceiptNoDAO" ref="prefixReceiptNoDAO"/>
	</bean>
    <bean id="pre2001Service" class="com.kangdainfo.tcfi.service.impl.Pre2001ServiceImpl"
		autowire="byName">
	</bean>
    <bean id="pre3005Service" class="com.kangdainfo.tcfi.service.impl.Pre3005ServiceImpl"
		autowire="byName">
		<property name="cedb1001Dao" ref="cedb1001Dao"/>
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="eedb1000DaoEicm" ref="eedb1000DaoEicm"/>
		<property name="cmpyStatusDao" ref="cmpyStatusDao"/>
		<property name="prefixReceiptNoDAO" ref="prefixReceiptNoDAO"/>
    </bean>
    <bean id="pre3007Service" class="com.kangdainfo.tcfi.service.impl.Pre3007ServiceImpl"
		autowire="byName">
		<property name="cedb1006Dao" ref="cedb1006Dao" />
		<property name="cedb1007Dao" ref="cedb1007Dao" />
		<property name="cedb1008Dao" ref="cedb1008Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1023LDao" ref="cedb1023LDao" />
		<property name="cedb1027Dao" ref="cedb1027Dao" />
    </bean>
    <bean id="pre3008Service" class="com.kangdainfo.tcfi.service.impl.Pre3008ServiceImpl"
		autowire="byName">
		<property name="cedb2000Dao" ref="cedb2000Dao" />
		<property name="cedb2002Dao" ref="cedb2002Dao" />
		<property name="cedb2004Dao" ref="cedb2004Dao" />
		<property name="csmdCtrlitemDao" ref="csmdCtrlitemDao" />
    </bean>
    <bean id="pre3013Service" class="com.kangdainfo.tcfi.service.impl.Pre3013ServiceImpl"
		autowire="byName">
		<property name="lmsmBussMainDao" ref="lmsmBussMainDao" />
		<property name="lmsBusiItemDao" ref="lmsBusiItemDao"/>
		<property name="csmdCtrlitemDao" ref="csmdCtrlitemDao" />
    </bean>
    <bean id="pre4001Service" class="com.kangdainfo.tcfi.service.impl.Pre4001ServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1004Dao" ref="cedb1004Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1027Dao" ref="cedb1027Dao" />
		<property name="cedb2000Dao" ref="cedb2000Dao" />
		<property name="caseFlowService" ref="caseFlowService" />
		<property name="trackLogService" ref="trackLogService" />
    </bean>
    <bean id="pre4006Service" class="com.kangdainfo.tcfi.service.impl.Pre4006ServiceImpl"
		autowire="byName">
		<property name="eicmGeneralQueryDao" ref="eicmGeneralQueryDao"/>
    </bean>
    <bean id="pre4013Service" class="com.kangdainfo.tcfi.service.impl.Pre4013ServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedb1011Dao" ref="cedb1011Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
    </bean>
    <bean id="pre4020Service" class="com.kangdainfo.tcfi.service.impl.Pre4020ServiceImpl"
		autowire="byName">
		<property name="ossmApplMainDao" ref="ossmApplMainDao" />
		<property name="ossmApplFlowDao" ref="ossmApplFlowDao" />
		<property name="ossmBussItemDao" ref="ossmBussItemDao" />
		<property name="ossmFeeMainDao" ref="ossmFeeMainDao" />
		<property name="ossmFeeDetailDao" ref="ossmFeeDetailDao" />
		<property name="ossmOrgChangeDao" ref="ossmOrgChangeDao" />
		<property name="ossmOrgNameDao" ref="ossmOrgNameDao" />
		<property name="cedb2000Dao" ref="cedb2000Dao" />
		<property name="cedb2002Dao" ref="cedb2002Dao" />
    </bean>
    <!-- 2024/03/27 新增 -->
    <bean id="pre4022Service" class="com.kangdainfo.tcfi.service.impl.Pre4022ServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="prefixReceiptNoDAO" ref="prefixReceiptNoDAO"/>
		<property name="cedb1023Dao" ref="cedb1023Dao"/>
    </bean>
    <bean id="pre8005Service" class="com.kangdainfo.tcfi.service.impl.Pre8005ServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="cedb1010Dao" ref="cedb1010Dao"/>
		<property name="cedbc000Dao" ref="cedbc000Dao"/>
    </bean>
    <bean id="pre8006Service" class="com.kangdainfo.tcfi.service.impl.Pre8006ServiceImpl"
		autowire="byName">
		<property name="declaratoryStatutesDao" ref="declaratoryStatutesDao"/>
		<property name="declaratoryStatutesRcverDao" ref="declaratoryStatutesRcverDao"/>
    </bean>
    <bean id="pre8010Service" class="com.kangdainfo.tcfi.service.impl.Pre8010ServiceImpl" >
		<property name="cedbc058Dao" ref="cedbc058Dao"/>
    </bean>
    <bean id="pre8011Service" class="com.kangdainfo.tcfi.service.impl.Pre8011ServiceImpl"
		autowire="byName">
		<property name="restrictionDao" ref="restrictionDao"/>
		<property name="restrictionItemDao" ref="restrictionItemDao"/>
    </bean>
    <bean id="pre2003Service" class="com.kangdainfo.tcfi.service.impl.Pre2003ServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="cedb1017Dao" ref="cedb1017Dao"/>
		<property name="cedb1023Dao" ref="cedb1023Dao"/>
		<property name="cedb1027Dao" ref="cedb1027Dao"/>
    </bean>
    <bean id="pre2004Service" class="com.kangdainfo.tcfi.service.impl.Pre2004ServiceImpl"
		autowire="byName">
		<property name="cedb1021Dao" ref="cedb1021Dao"/>
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="cedb1002Dao" ref="cedb1002Dao"/>
		<property name="eedb1000Dao" ref="eedb1000DaoEicm"/>
		<property name="eedb1002Dao" ref="eedb1002Dao"/>
		<property name="eicmGeneralQueryDao" ref="eicmGeneralQueryDao"/>
		<property name="syncOssQueueDao" ref="syncOssQueueDao"/>
    </bean>
    <bean id="pre2008Service" class="com.kangdainfo.tcfi.service.impl.Pre2008ServiceImpl"
		autowire="byName">
		<property name="cedb1017Dao" ref="cedb1017Dao"/>
		<property name="postRecordDao" ref="postRecordDao"/>
    </bean>
    <bean id="pre8012Service" class="com.kangdainfo.tcfi.service.impl.Pre8012ServiceImpl"
		autowire="byName">
		<property name="cedbc004Dao" ref="cedbc004Dao"/>
		<property name="eicmGeneralQueryDao" ref="eicmGeneralQueryDao"/>
    </bean>
    <bean id="pre8018Service" class="com.kangdainfo.tcfi.service.impl.Pre8018ServiceImpl"
		autowire="byName">
		<property name="receiptNoSetupDAO" ref="receiptNoSetupDAO"/>
    </bean>
    <bean id="pre5001Service" class="com.kangdainfo.tcfi.service.impl.Pre5001ServiceImpl"
		autowire="byName">
		<property name="receiptNoSetupDAO" ref="receiptNoSetupDAO"/>
		<property name="prefixReceiptNoDAO" ref="prefixReceiptNoDAO"/>
		<property name="eedb1000Dao" ref="eedb1000Dao"/>
    </bean>
    <bean id="pre5002Service" class="com.kangdainfo.tcfi.service.impl.Pre5002ServiceImpl"
		autowire="byName">
		<property name="prefixReceiptNoDAO" ref="prefixReceiptNoDAO"/>
    </bean>
    <bean id="pre9005Service" class="com.kangdainfo.tcfi.service.impl.Pre9005ServiceImpl" >
		<property name="systemNewsDao" ref="systemNewsDao"/>
    </bean>
	<bean id="prefixService" class="com.kangdainfo.tcfi.service.impl.PrefixServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="eedb1000DaoEicm" ref="eedb1000DaoEicm" />
		<property name="eedb1002Dao" ref="eedb1002Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1003Dao" ref="cedb1003Dao" />
		<property name="cedb1004Dao" ref="cedb1004Dao" />
		<property name="cedb1006Dao" ref="cedb1006Dao" />
		<property name="cedb1007Dao" ref="cedb1007Dao" />
		<property name="cedb1009Dao" ref="cedb1009Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedb1017Dao" ref="cedb1017Dao" />
		<property name="cedb1019Dao" ref="cedb1019Dao" />
		<property name="cedb1021Dao" ref="cedb1021Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1027Dao" ref="cedb1027Dao" />
		<property name="cedb1028Dao" ref="cedb1028Dao" />
		<property name="cedb2000Dao" ref="cedb2000Dao" />
		<property name="cedb2002Dao" ref="cedb2002Dao" />
		<property name="cedb2004Dao" ref="cedb2004Dao" />
		<property name="cedbc000Dao" ref="cedbc000Dao" />
		<property name="cedbc055Dao" ref="cedbc055Dao" />
		<property name="postRecordDao" ref="postRecordDao" />
		<property name="busiItemDao" ref="busiItemDao" />
		<property name="cmpyMemoInfoDao" ref="cmpyMemoInfoDao" />
		<property name="receiveDao" ref="receiveDao" />
		<property name="declaratoryStatutesDao" ref="declaratoryStatutesDao" />
		<property name="declaratoryStatutesRcverDao" ref="declaratoryStatutesRcverDao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
		<property name="functionMenuDao" ref="functionMenuDao" />
		<property name="functionMenuAuthDao" ref="functionMenuAuthDao" />
		<property name="synonymWordDao" ref="synonymWordDao" />
		<property name="restrictionDao" ref="restrictionDao" />
		<property name="restrictionItemDao" ref="restrictionItemDao" />
		<property name="loginLogDao" ref="loginLogDao" />
		<property name="sameNameQueueDao" ref="sameNameQueueDao" />
		<property name="csmmCmpyInfoDao" ref="csmmCmpyInfoDao" />
		<property name="ossmApplMainDao" ref="ossmApplMainDao" />
		<property name="ossmApplFlowDao" ref="ossmApplFlowDao" />
		<property name="ossmOrgChangeDao" ref="ossmOrgChangeDao" />
		<property name="ossmOrgNameDao" ref="ossmOrgNameDao" />
		<property name="ossmBussItemDao" ref="ossmBussItemDao" />
		<property name="eedb1000Dao" ref="eedb1000Dao" />
		<property name="eedb1300Dao" ref="eedb1300Dao"/>
		<property name="eedb3100Dao" ref="eedb3100Dao" />
		<property name="eedb5000Dao" ref="eedb5000Dao" />
		<property name="eedbV8000Dao" ref="eedbV8000Dao" />
		<property name="cedbc053Dao" ref="cedbc053Dao" />
		<property name="partNameLogDao" ref="partNameLogDao" />
		<property name="csmdCountryDao" ref="csmdCountryDao" />
		<property name="lmsBusiItemDao" ref="lmsBusiItemDao" />
		<property name="csyssUserDao" ref="csyssUserDao"/>
	</bean>

	<bean id="approveService" class="com.kangdainfo.tcfi.service.impl.ApproveServiceImpl"
		autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1004Dao" ref="cedb1004Dao" />
		<property name="cedb2000Dao" ref="cedb2000Dao" />
		<property name="cedb2004Dao" ref="cedb2004Dao" />
		<property name="receiveDao" ref="receiveDao" />
		<property name="cedb1019Dao" ref="cedb1019Dao" />
		<property name="cedb1017Dao" ref="cedb1017Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedbc000Dao" ref="cedbc000Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="ossmApplMainDao" ref="ossmApplMainDao" />
		<property name="ossmApplFlowDao" ref="ossmApplFlowDao" />
		<property name="cedb2002Dao" ref="cedb2002Dao" />
		<property name="ossmOrgChangeDao" ref="ossmOrgChangeDao" />
		<property name="eedbV8000Dao" ref="eedbV8000Dao" />
		<property name="eedb1000DaoEicm" ref="eedb1000DaoEicm" />
		<property name="ossmOrgNameDao" ref="ossmOrgNameDao" />
		<property name="ossmBussItemDao" ref="ossmBussItemDao" />
		<property name="eedb3100Dao" ref="eedb3100Dao" />
		<property name="cedb1003Dao" ref="cedb1003Dao" />
		<property name="cedb1009Dao" ref="cedb1009Dao" />
		<property name="cedbc055Dao" ref="cedbc055Dao" />
		<property name="eedb1002Dao" ref="eedb1002Dao" />
		<property name="cedbc004Dao" ref="cedbc004Dao" />
		<property name="cedbc053Dao" ref="cedbc053Dao" />
		<property name="cedb1100Dao" ref="cedb1100Dao" />
		<property name="cedb1006Dao" ref="cedb1006Dao" />
		<property name="cedb1007Dao" ref="cedb1007Dao" />
		<property name="cedb1008Dao" ref="cedb1008Dao" />
	</bean>
	<!-- 備份 -->
	<bean id="backupService" class="com.kangdainfo.tcfi.service.impl.BackupServiceImpl" autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1006Dao" ref="cedb1006Dao" />
		<property name="cedb1007Dao" ref="cedb1007Dao" />
		<property name="cedb1008Dao" ref="cedb1008Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1023LDao" ref="cedb1023LDao" />
	</bean>
	<!-- 封裝 -->
	<bean id="encapsulateService" class="com.kangdainfo.tcfi.service.impl.EncapsulateServiceImpl" autowire="byName">
		<property name="backupService" ref="backupService" />
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1002Dao" ref="cedb1002Dao" />
		<property name="cedb1010Dao" ref="cedb1010Dao" />
		<property name="cedb1019Dao" ref="cedb1019Dao" />
		<property name="cedb1022Dao" ref="cedb1022Dao" />
		<property name="cedb1023Dao" ref="cedb1023Dao" />
		<property name="cedb1027Dao" ref="cedb1027Dao" />
		<property name="cedb1100Dao" ref="cedb1100Dao" />
		<property name="cedb1101Dao" ref="cedb1101Dao" />
		<property name="cedb1102Dao" ref="cedb1102Dao" />
		<property name="cedb1110Dao" ref="cedb1110Dao" />
		<property name="cedb1122Dao" ref="cedb1122Dao" />
		<property name="cedb1123Dao" ref="cedb1123Dao" />
	</bean>

	<bean id="com0001Service" class="com.kangdainfo.tcfi.service.impl.Com0001ServiceImpl" autowire="byName" >
		<property name="indexLogDao" ref="indexLogDao" />
	</bean>
	<bean id="pre0004Service" class="com.kangdainfo.tcfi.service.impl.Pre0004ServiceImpl" autowire="byName" >
		<property name="indexLogDao" ref="indexLogDao" />
		<property name="cedbc058Dao" ref="cedbc058Dao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 同名檢查 -->
	<bean id="sameNameCompareService"
		class="com.kangdainfo.tcfi.service.impl.SameNameCompareServiceImpl" autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="cedb1001Dao" ref="cedb1001Dao" />
		<property name="cedb1004Dao" ref="cedb1004Dao" />
		<property name="sameNameQueueDao" ref="sameNameQueueDao" />
	</bean>
	<!-- 一站式狀態同步 -->
	<bean id="updateOsssStatusService"
		class="com.kangdainfo.tcfi.service.impl.UpdateOsssStatusServiceImpl"
		autowire="byName">
		<property name="syncOssQueueDao" ref="syncOssQueueDao" />
		<property name="cedb1000Dao" ref="cedb1000Dao" />
		<property name="ossmApplMainDao" ref="ossmApplMainDao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
		<property name="ossmOrgRegisterDao" ref="ossmOrgRegisterDao" />
	</bean>
	<!-- 個資軌跡 -->
	<bean id="trackLogService" class="com.kangdainfo.tcfi.service.impl.TrackLogServiceImpl" autowire="byName">
		<property name="trackLogDao" ref="trackLogDao"/>
	</bean>
	<!-- 案件歷程 -->
	<bean id="caseFlowService" class="com.kangdainfo.tcfi.service.impl.CaseFlowServiceImpl" autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="cedb1010Dao" ref="cedb1010Dao"/>
		<property name="csmdWorkDayDao" ref="csmdWorkDayDao"/>
		<property name="flowLogDao" ref="flowLogDao"/>
	</bean>
	<!-- 免繳註記 -->
	<bean id="noPayMarkService" class="com.kangdainfo.tcfi.service.impl.NoPayMarkServiceImpl" autowire="byName">
		<property name="generalityBusitemDao" ref="generalityBusitemDao"/>
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
		<property name="cedb1023Dao" ref="cedb1023Dao"/>
		<property name="ossmFeeMainDao" ref="ossmFeeMainDao" />
		<property name="cedb2000Dao" ref="cedb2000Dao"/>
	</bean>
	<!-- 介接投審會 -->
	<bean id="moeaicApproveService" class="com.kangdainfo.tcfi.service.impl.MoeaicApproveServiceImpl" autowire="byName">
		<property name="csmmMoeaicApproveDao" ref="csmmMoeaicApproveDao"/>
		<property name="csmlCmpyTranMoeaicDao" ref="csmlCmpyTranMoeaicDao"/>
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
	</bean>
	<!-- 測試介接 -->
	<bean id="testService" class="com.kangdainfo.tcfi.service.impl.TestServiceImpl" autowire="byName" >
		<property name="umsMtDao" ref="umsMtDao"/>
	</bean>
	<!-- 資料釐正 -->
	<bean id="correctDataService" class="com.kangdainfo.tcfi.service.impl.CorrectDataServiceImpl" autowire="byName">
		<property name="cedb1000Dao" ref="cedb1000Dao"/>
	</bean>

</beans>