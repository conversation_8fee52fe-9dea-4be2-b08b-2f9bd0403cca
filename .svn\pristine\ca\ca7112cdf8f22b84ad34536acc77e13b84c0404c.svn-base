<%@page import="com.kangdainfo.tcfi.view.pre.PRE3012"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	
	Gson gson = new GsonBuilder().create();

	PRE3012 pre3012 = new PRE3012();
	out.write(gson.toJson(pre3012.getWorkDayForPre3011()));
	
} catch (Exception e) {
	e.printStackTrace();
}
%>