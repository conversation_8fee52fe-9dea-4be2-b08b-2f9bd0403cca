<%
/**
程式目的：清算完結報表
程式代號：pre4009
程式日期：1030508
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4009">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4009" />
</jsp:include>
<%
System.out.println(obj.getState());
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功！");
	} else {
		obj.setErrorMsg("查無資料！");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(form1.q_regDateStart.value == "" && form1.q_clearDateStart.value == "" && form1.q_clearUnit.value == ""){
		alertStr += "查詢欄位至少輸入一個";
	}else{
		if(form1.q_regDateStart.value != "")	alertStr += checkEmpty(form1.q_regDateEnd,"登錄日期(迄)");
		if(form1.q_regDateEnd.value != "")	alertStr += checkEmpty(form1.q_regDateStart,"登錄日期(起)");
		if(form1.q_clearDateStart.value != "")	alertStr += checkEmpty(form1.q_clearDateEnd,"核備清算日期(迄)");
		if(form1.q_clearDateEnd.value != "")	alertStr += checkEmpty(form1.q_clearDateStart,"核備清算日期(起)");	
		alertStr += checkDate(form1.q_regDateStart,"登錄日期(起)");
		alertStr += checkDate(form1.q_regDateEnd,"登錄日期(迄)");
		alertStr += checkDate(form1.q_clearDateStart,"核備清算日期(起)");
		alertStr += checkDate(form1.q_clearDateEnd,"核備清算日期(迄)");
	}	
	if(alertStr.length!=0){ alert(alertStr); return false; }
	return true;
}

function init() {
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function queryOne(id) {
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doClear":
				form1.q_regDateStart.value = "";
				form1.q_regDateEnd.value = "";
				form1.q_clearDateStart.value = "";
				form1.q_clearDateEnd.value = "";
				form1.q_clearUnit.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

</script>
</head>
<!-- Form area -->
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4009'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
        <tr>
			<td class="td_form" width="15%">登錄日期：</td>
		  	<td class="td_form_white" width="85%">
				<%=View.getPopCalendar("field_Q","q_regDateStart",obj.getQ_regDateStart()) %>
				 ~ <%=View.getPopCalendar("field_Q","q_regDateEnd",obj.getQ_regDateEnd()) %>
		  	</td>
		</tr>
		<tr>
		  	<td class="td_form">核備清算日期：</td>
		  	<td class="td_form_white">
				<%=View.getPopCalendar("field_Q","q_clearDateStart",obj.getQ_clearDateStart()) %>
				 ~ <%=View.getPopCalendar("field_Q","q_clearDateEnd",obj.getQ_clearDateEnd()) %>
		  	</td>
		</tr>
		<tr>  
		  	<td class="td_form" >核備清算單位：</td>
          	<td class="td_form_white" align="left">
             	<select name="q_clearUnit" id="q_clearUnit" value="<%=obj.getQ_clearUnit()%>" >
             		<%=View.getOptionCourts(obj.getQ_clearUnit(),false,1) %>
            	</select>
            	
            	&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
            	&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
		  </td>
		</tr>				
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
  	  <td class="td_form" colspan="8" style="text-align:left">
  		  總筆數 :<%=obj.getTotal()%>
  	  </td>
  </tr>
  <tr>
  	<th class="listTH" style="text-align:center"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">序號</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">統一編號</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">公司/有限合夥名稱</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">狀況</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">核備清算日期</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">核備清算-字</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">核備清算-號</a></th>
    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">核備清算單位</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false,false,false,false,false,false};
  boolean displayArray[] = {true,true,true,true,true,true,true};
  String[] alignArray = {"left","left","left","left","left","left","left"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",true));
  %>
  </tbody>
</table>
</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>