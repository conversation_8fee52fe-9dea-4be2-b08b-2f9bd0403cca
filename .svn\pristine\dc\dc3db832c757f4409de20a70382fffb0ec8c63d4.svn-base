package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgName;

public class OssmOrgNameDao extends BaseDaoJdbc implements RowMapper<OssmOrgName> {

	public List<OssmOrgName> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob("SELECT");
		sqljob.appendSQL(" A.TELIX_NO");
		sqljob.appendSQL(",A.SEQ_NO");
		sqljob.appendSQL(",(A.ORG_NAME||DECODE(B.ORG_TYPE,'01','股份有限公司','02','有限公司','03','無限公司','04','兩合公司','18','有限合夥','')) as ORG_NAME");
		sqljob.appendSQL("FROM OSSM_ORG_NAME A");
		sqljob.appendSQL("INNER JOIN OSSM_APPL_MAIN B ON B.TELIX_NO=A.TELIX_NO");
		sqljob.appendSQL("WHERE A.TELIX_NO=?");
		sqljob.addParameter(telixNo);
		sqljob.appendSQL("ORDER BY TELIX_NO, SEQ_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmOrgName>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public OssmOrgName mapRow(ResultSet rs, int idx) throws SQLException {
		OssmOrgName obj = null;
		if(null!=rs) {
			obj = new OssmOrgName();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getInt("SEQ_NO"));
			obj.setOrgName(rs.getString("ORG_NAME"));
		}
		return obj;
	}

}
