package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;

public class Cedb1004Dao extends BaseDaoJdbc implements RowMapper<Cedb1004> {

	private static final String SQL_defaultOrder = "ORDER BY PREFIX_NO, SEQ_NO, SAME_SEQ_NO";

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1004 WHERE PREFIX_NO = ?";
	public List<Cedb1004> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNoAndSeqNo = "SELECT * FROM CEDB1004 WHERE PREFIX_NO = ? AND SEQ_NO = ?";
	public List<Cedb1004> findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndSeqNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(seqNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPk = "SELECT * FROM CEDB1004 WHERE PREFIX_NO = ? AND SEQ_NO = ? AND SAME_SEQ_NO = ?";
	public Cedb1004 findByPk(String prefixNo, String seqNo, String sameSeqNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPk);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(seqNo);
		sqljob.addParameter(sameSeqNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (Cedb1004) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_deleteByPrefixNo = "DELETE CEDB1004 WHERE PREFIX_NO = ?";
	public int deleteByPrefixNo(String prefixNo) {
		if("".equals(Common.get(prefixNo))) return 0;
		SQLJob sqljob = new SQLJob(SQL_deleteByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private static final String SQL_insert = "INSERT INTO CEDB1004(PREFIX_NO, SEQ_NO, COMPANY_NAME, SAME_SEQ_NO, SAME_PREFIX_NO, SAME_BAN_NO, SAME_COMPANY_NAME, REVOKE_APP_DATE, CMPY_STATUS, APPLY_NAME, RESERVE_DATE) "
			+ "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
	public Cedb1004 insert(Cedb1004 obj) {
		SQLJob sqljob = new SQLJob(SQL_insert);
		sqljob.addParameter(Common.get(obj.getPrefixNo()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getSeqNo()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getCompanyName()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getSameSeqNo()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getSamePrefixNo()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getSameBanNo()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getSameCompanyName()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getRevokeAppDate()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getCmpyStatus()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getApplyName()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getReserveDate()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		return findByPk(obj.getPrefixNo(), obj.getSeqNo(), obj.getSameSeqNo());
	}

	@Override
	public Cedb1004 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1004 obj = null;
		if(null!=rs) {
			obj = new Cedb1004();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setSameSeqNo(rs.getString("SAME_SEQ_NO"));
			obj.setSamePrefixNo(rs.getString("SAME_PREFIX_NO"));
			obj.setSameBanNo(rs.getString("SAME_BAN_NO"));
			obj.setSameCompanyName(rs.getString("SAME_COMPANY_NAME"));
			obj.setRevokeAppDate(rs.getString("REVOKE_APP_DATE"));
			obj.setCmpyStatus(rs.getString("CMPY_STATUS"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setReserveDate(rs.getString("RESERVE_DATE"));
		}
		return obj;
	}

}