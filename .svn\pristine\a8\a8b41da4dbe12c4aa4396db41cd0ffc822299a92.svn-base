package com.kangdainfo.util.di;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.util.ArrayList;
import org.jdom.Document;
import org.jdom.Element;
import org.jdom.output.Format;
import org.jdom.output.XMLOutputter;
import org.jdom.CDATA;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * di電子交換檔功能
 * <AUTHOR> huang
 * 113/05/10
 */
public class DiFileMaker {
	
	public static class DiSetting extends SuperBean{
		private int amount;// 退還金
		private String staffName;// 承辦人姓名
		private String staffBranchNo;// 承辦人分機
		private String staffEmail;// 承辦人mail
		private String companyName;// 公司名稱
		private String applyName;// 申請人名稱
		private String sendDate;// 發文日期
		private String returnDate; // 退費日期
		private String returnChNo; // 支票號
		private String attorName; // 代理人
		
		public int getAmount() {
			return checkGet(amount);
		}
		
		public void setAmount(int amount) {
			this.amount = checkSet(amount);
		}

		public String getStaffName() {
			return checkGet(staffName);
		}

		public void setStaffName(String staffName) {
			this.staffName = checkSet(staffName);
		}

		public String getStaffBranchNo() {
			return checkGet(staffBranchNo);
		}

		public void setStaffBranchNo(String staffBranchNo) {
			this.staffBranchNo = checkSet(staffBranchNo);
		}

		public String getStaffEmail() {
			return checkGet(staffEmail);
		}

		public void setStaffEmail(String staffEmail) {
			this.staffEmail = checkSet(staffEmail);
		}

		public String getCompanyName() {
			return checkGet(companyName);
		}

		public void setCompanyName(String companyName) {
			this.companyName = checkSet(companyName);
		}

		public String getApplyName() {
			return checkGet(applyName);
		}

		public void setApplyName(String applyName) {
			this.applyName = checkSet(applyName);
		}

		public String getSendDate() {
			return checkGet(sendDate);
		}

		public void setSendDate(String sendDate) {
			this.sendDate = checkSet(sendDate);
		}

		public String getReturnDate() {
			return checkGet(returnDate);
		}

		public void setReturnDate(String returnDate) {
			this.returnDate = checkSet(returnDate);
		}

		public String getReturnChNo() {
			return checkGet(returnChNo);
		}

		public void setReturnChNo(String returnChNo) {
			this.returnChNo = checkSet(returnChNo);
		}

		
		public String getAttorName() {
			return checkGet(attorName);
		}

		public void setAttorName(String attorName) {
			this.attorName = checkSet(attorName);
		}

		@Override
		public Object doQueryOne() throws Exception {
			// TODO Auto-generated method stub
			return null;
		}

		@Override
		public ArrayList doQueryAll() throws Exception {
			// TODO Auto-generated method stub
			return null;
		}

		@Override
		public void doCreate() throws Exception {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void doUpdate() throws Exception {
			// TODO Auto-generated method stub
			
		}

		@Override
		public void doDelete() throws Exception {
			// TODO Auto-generated method stub
			
		}
	}
	
	public DiSetting diSetting;
	
	public DiSetting getDiSetting() {
		return diSetting;
	}

	public void setDiSetting(DiSetting diSetting) {
		this.diSetting = diSetting;
	}
	
	public DiFileMaker(DiSetting diSetting) {
		super();
		this.diSetting = diSetting;
	}
	
	/**
	 * 建置di檔並下載
	 * @param flagPlusDTD
	 * @param regUnitCode
	 * @param uservo
	 * @param docInfoCol
	 * @param response
	 * @throws MoeaException 
	 * @throws Exception
	 */
	public static File generateDIFile(boolean flagPlusDTD, CommonUser user, Object obj, String fileName) throws MoeaException{
		DiSetting diSet = copyToDiSetting(obj);
		String diContent;
		File file = null;
		
		try {
			diContent = docGenerDI(true, user, diSet);
			file = saveAsDIFile(diContent, fileName);
			if (file == null) throw new MoeaException("產製公文發生異常");
		} catch (MoeaException e) {
			throw e; // 顯示用
		} catch (Exception e) {
			e.printStackTrace();
		} 
		
		return file;
	}

    private static File saveAsDIFile(String diContent, String fileName) throws IOException {
    	File file = new File(fileName);
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            writer.write(diContent);
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        }
        return file;
    }
	
	/**
	 * 產生DI檔.
	 *
	 * @param flag_plusDTD
	 * @param regUnitCode
	 * @param uservo
	 * @param docInfoCol
	 * @return String
	 */
	private static String docGenerDI(final boolean flagPlusDTD, final CommonUser user, final DiSetting diSet) throws Exception {
		String returnString = "";
		Document doc = new Document();
		Element field1 = null; // 第一層
		Element field2 = null; // 第二層
		Element field3 = null; // 第三層

		Element root = new Element("函");
		doc.addContent(root);

		field1 = new Element("發文機關");
		root.addContent(field1);

		field2 = new Element("全銜");
		field2.setText(Common.get(PrefixConstants.DI_REG_UNIT));
		field1.addContent(field2);

		field2 = new Element("機關代碼"); 
		field2.setText(Common.get(PrefixConstants.DI_REG_UNIT_CODE));
		field1.addContent(field2);

		field1 = new Element("函類別");
		field1.setAttribute("代碼", Common.get("書函").toString());
		root.addContent(field1);

		field1 = new Element("地址");
		field1.setText(Common.get(PrefixConstants.DI_REG_UNIT_ADDR));
		root.addContent(field1);
		
		field1 = new Element("聯絡方式");
		field1.setText("承辦人: " + Common.get(diSet.getStaffName()));
		root.addContent(field1);
		
		field1 = new Element("聯絡方式");
		field1.setText("電話：(049)2359－171 分機：" + Common.get(diSet.getStaffBranchNo()));
		root.addContent(field1);
		
		field1 = new Element("聯絡方式");
		field1.setText("電子信箱: " + Common.get(diSet.getStaffEmail()));
		root.addContent(field1);
		
		field1 = new Element("受文者");
		root.addContent(field1);
		
		field2 = new Element("交換表");
		field2.setAttribute("交換表單", Common.get("表單").toString());
		field2.setText("如正副本行文單位");
		field1.addContent(field2);
		
		field1 = new Element("發文日期");
		root.addContent(field1);
		
		field2 = new Element("年月日");
		field2.setText(Common.get(diSet.getSendDate()));
		field1.addContent(field2);
		
		field1 = new Element("發文字號");
		root.addContent(field1);
		
		field2 = new Element("字");
		field2.setText(Common.get(PrefixConstants.DI_BUSS_TYPE));
		field1.addContent(field2);
		
		field2 = new Element("文號");
		field1.addContent(field2);
		
		field3 = new Element("年度");
		field3.setText("");
		field2.addContent(field3);
		
		field3 = new Element("流水號");
		field3.setText("");
		field2.addContent(field3);

		field3 = new Element("支號");
		field3.setText("");
		field2.addContent(field3);
		
		field1 = new Element("速別");
		field1.setAttribute("代碼", Common.get("普通件").toString());
		root.addContent(field1);
		
		field1 = new Element("密等及解密條件或保密期限");
		root.addContent(field1);
		
		field2 = new Element("密等");
		field1.addContent(field2);
		
		field2 = new Element("解密條件或保密期限");
		field1.addContent(field2);
		
		field1 = new Element("附件");
		root.addContent(field1);
		
		field2 = new Element("文字");
		field2.setText("如文");
		field1.addContent(field2);
		
		field1 = new Element("主旨");
		root.addContent(field1);
		
		field2 = new Element("文字");
		if (diSet.getCompanyName() != null || "".equals(diSet.getCompanyName())) {
			field2.setText("貴公司申請預查撤回退費一案，准予所請，請查照。");
		}else {
			field2.setText("臺端申請預查撤回退費一案，准予所請，請查照。");
		}
		field1.addContent(field2);
		
		field1 = new Element("段落");
		field1.setAttribute("段名", "說明：");
		root.addContent(field1);
		
		// 方案1
		field2 = new Element("條列");
		field2.setAttribute("序號" , "一、");
		field1.addContent(field2);
		
		field3 = new Element("文字");
		field3.setText("依據貴公司" + Common.get(diSet.getReturnDate()) + "公司預查案件撤件申請書辦理。");
		field2.addContent(field3);
		
		field2 = new Element("條列");
		field2.setAttribute("序號" , "二、");
		field1.addContent(field2);
		
		field3 = new Element("文字");
		field3.setText("檢送規費新台幣" + Common.get(diSet.getAmount()) + "元整之支票（號碼：" + Common.get(diSet.getReturnChNo()) + 
				"）；依據國庫支票管理辦法規定，自111年1月1日起，本署所核發之國庫機關專戶存款支票，加印雙平行線及「禁止背書轉讓」標識。");
		field2.addContent(field3);
		
		// 方案2	
//		field2 = new Element("文字");
//		String textContent = "\n\t\t\t一、\n\t\t\t依據貴公司" + Common.get(diSet.getReturnDate()) + "公司預查案件撤件申請書辦理。\n\t\t" + 
//                "二、\n\t\t\t檢送規費新台幣" + Common.get(diSet.getAmount()) + "元整之支票（號碼：" + Common.get(diSet.getReturnChNo()) + 
//                "）；依據國庫支票管理辦法規定，自111年1月1日起，本署所核發之國庫機關專戶存款支票，加印雙平行線及「禁止背書轉讓」標識。\n";
//		CDATA cdata = new CDATA(textContent);
//		field2.addContent(cdata);
//		field1.addContent(field2);
		
		// 方案3
//		field2 = new Element("文字");
//		String textContent = "\n\t\t\t一、\n\t\t\t依據貴公司" + Common.get(diSet.getReturnDate()) + "公司預查案件撤件申請書辦理。\n";
//		CDATA cdata = new CDATA(textContent);
//		field2.addContent(cdata);
//		field1.addContent(field2);
//		
//		field2 = new Element("文字");
//		textContent = "\n\t\t\t二、\n\t\t\t檢送規費新台幣" + Common.get(diSet.getAmount()) + "元整之支票（號碼：" + Common.get(diSet.getReturnChNo()) + 
//                "）；依據國庫支票管理辦法規定，自111年1月1日起，本署所核發之國庫機關專戶存款支票，加印雙平行線及「禁止背書轉讓」標識。\n";
//		cdata = new CDATA(textContent);
//		field2.addContent(cdata);
//		field1.addContent(field2);
		
		// 方案4
//		String textContent = "\n\t\t\t" + "一、" + "\n\t\t\t" + "依據貴公司" + Common.get(diSet.getReturnDate()) + "公司預查案件撤件申請書辦理。" + 
//			       "\n\t\t\t" + "二、" + "\n\t\t\t" + "檢送規費新台幣" + Common.get(diSet.getAmount()) + "元整之支票（號碼：" + Common.get(diSet.getReturnChNo()) + 
//				   "）；依據國庫支票管理辦法規定，自111年1月1日起，本署所核發之國庫機關專戶存款支票，加印雙平行線及「禁止背書轉讓」標識。";
//		CDATA cdata = new CDATA(textContent);
//		field1.addContent(cdata);
		
		field1 = new Element("正本");
		root.addContent(field1);
		
		field2 = new Element("全銜");
		if (diSet.getApplyName() != null && !"".equals(diSet.getApplyName()) && 
				diSet.getAttorName() != null && !"".equals(diSet.getAttorName()) &&
				diSet.getApplyName() != null && "".equals(diSet.getApplyName())) {
			field2.setText(Common.get(diSet.getApplyName()) + " 君-" + Common.get(diSet.getAttorName()) + "代轉");
		}else if (diSet.getCompanyName() != null && "".equals(diSet.getCompanyName()) && 
				diSet.getAttorName() != null && !"".equals(diSet.getAttorName()) &&
				diSet.getApplyName() == null && "".equals(diSet.getApplyName())){
			field2.setText(Common.get(diSet.getCompanyName()) + "-" + Common.get(diSet.getAttorName()) + "代轉");
		}else {
			field2.setText(Common.get(diSet.getApplyName()) + " 君");
		}
		field1 .addContent(field2);
		
		field1 = new Element("署名");
		root.addContent(field1);

		// -----------------------------------------------------
		StringWriter writer = new StringWriter();
	    XMLOutputter xmlOutputter = new XMLOutputter();
	    // pretty print
	    xmlOutputter.setFormat(Format.getRawFormat().setEncoding(PrefixConstants.XML_ENCODING));
	    xmlOutputter.output(doc, writer);
		String tmpString = writer.toString();

		tmpString = tmpString.substring(tmpString.indexOf("\n") + 1);
		if (flagPlusDTD) {
			//20140710 全國99版DI、SW。
			String dtdVer = "<!DOCTYPE 函 SYSTEM \"@DTD\" [";
			tmpString = "<?xml version=\"1.0\"  encoding=\"@ENCODING\"?> " + dtdVer + "<!NOTATION DI SYSTEM \"\">"
					+ "<!NOTATION _X SYSTEM \"\">" + "]>" + tmpString;
			tmpString = tmpString.replaceFirst("@ENCODING", PrefixConstants.DI_DTD_VERSION_104_2_UTF8)
					.replaceFirst("@DTD", PrefixConstants.DI_DTD_VERSION_104_2_UTF8);
		}
		returnString = returnString + tmpString;

		return returnString;
	}
	
	/**
	 * 根據不同業務對象轉換成統一di設置格式
	 * @param obj
	 * @return DiSetting
	 */
	private static DiSetting copyToDiSetting(Object obj) {
		DiSetting diSet = new DiSetting();
		Field[] fields = obj.getClass().getDeclaredFields();
		
		for(Field f : fields) {
			String f_name = f.getName();
			switch (f_name){
				case "amount":// 金額
					diSet.setAmount((int)getFieldValue(obj, "amount"));
					break;
				case "staffName":// 承辦人
					diSet.setStaffName((String)getFieldValue(obj, "staffName"));
					break;
				case "staffBranchNo":// 分機
					diSet.setStaffBranchNo((String)getFieldValue(obj, "staffBranchNo"));
					break;
				case "staffEmail":// email
					diSet.setStaffEmail((String)getFieldValue(obj, "staffEmail"));
					break;
				case "companyName":// 公司名
					diSet.setCompanyName((String)getFieldValue(obj, "companyName"));
					break;
				case "applyName":// 申請人
					diSet.setApplyName((String)getFieldValue(obj, "applyName"));
					break;
				case "sendDate":// 發文日期
					diSet.setSendDate((String)getFieldValue(obj, "sendDate"));
					break;
				case "returnDate":// 申請日期
					diSet.setReturnDate((String)getFieldValue(obj, "returnDate"));
					break;
				case "returnChNo":// 支票號 
					diSet.setReturnChNo((String)getFieldValue(obj, "returnChNo"));
					break;
				case "attorName":// 代理人
					diSet.setAttorName((String)getFieldValue(obj, "attorName"));
					break;
			}
		}
		
		return diSet;
	}
	
	/**
	 * 根據業務對象及其屬性名獲取內部值
	 * @param obj
	 * @param fieldName
	 * @return Object
	 */
	private static Object getFieldValue(Object obj, String fieldName) {
        if (obj == null || fieldName == null || fieldName.isEmpty()) {
            throw new IllegalArgumentException("需輸入正確類型與屬性名");
        }

        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }
}
