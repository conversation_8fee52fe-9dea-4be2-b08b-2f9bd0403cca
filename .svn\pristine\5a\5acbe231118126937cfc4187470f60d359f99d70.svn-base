package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;

public interface Pre2003Service {

	public int doSave( Cedb1023 cedb1023, Cedb1027 cedb1027, Cedb1000 cedb1000, String cedb1023Flag, String funCode) throws Exception ;
	public int doUpdate( Cedb1023 cedb1023, Cedb1027 cedb1027, Cedb1000 cedb1000, String cedb1023Flag, String funCode) throws Exception ;
	public int doAssign( Cedb1017 cedb1017 ) throws Exception ;
	public int rollBack(Cedb1000 cedb1000, Cedb1027 cedb1027) throws Exception;
	
	public List<Cedb1027> getCedb1027List(String prefixNo);
	
	public Cedb1027 getCedb1027ByPrefixNo(String prefixNo);
	
} 