@charset "UTF-8"; 

/* 前端中推會字型ttf */
@font-face {
    font-family: EUDC;
    src: url('../cmexfont/eudc_kai.ttf');
}

/*===========文字樣式============*/
body {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	color: #000000;
	margin-top: 0px;
	margin-left: 5px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-color : #F3EFEF;
}

input {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
}

.text_w {
	color: #000000;
	text-decoration: none;
}

.text_w:hover {
	color: #FF0000;
	text-decoration: none;
}

.text_link_w {
	color: #000000;
	font-weight: bolder;
	text-decoration: none;
}

.text_link_w:hover {
	color: #FF0000;
	font-weight: bold;
	text-decoration: none;
}

.text_link_dg {
	color: #25861f;
	text-decoration: none;
}

.text_link_dg:hover {
	color: #ff0000;
	text-decoration: none;
}

.text_link_b {
	color: #0000ff;
	text-decoration: none;
}

.text_link_b:hover {
	color: #ff0000;
	text-decoration: none;
}
/*===========分頁文字樣式============*/
.pageText {
	font-size: 15px;
}
/*============End of 分頁文字樣式 =======*/
.copyRight {
	font-size: 10px;
	color: #b8b8b8;
}
/*===========主畫面樣式============*/
.background1 {
	background: #008B8B;
	background: #A0522D;
	background: #3366cc;
}

.background2 {
	background: #00CED1;
	background: #E9967A;
	background: #1E90FF;
}

.background3 {
	background: #FBFFFD;
	background: #FDF5E6;
	background: #EFF9FF;
	scrollbar-face-color: #E5E5E5;
	scrollbar-shadow-color: #aaaaaa;
	scrollbar-highlight-color: #aaaaaa;
	scrollbar-3dlight-color: #E5E5E5;
	scrollbar-darkshadow-color: #E5E5E5;
	scrollbar-track-color: #E5E5E5;
	scrollbar-arrow-color: #aaaaaa;
}

.titleCFont {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 10px;
	font-weight: bolder;
	color: #008B8B;
	color: #A0522D;
	color: #0000FF;
}

.titleEFont {
	font-size: 10px;
	color: #008B8B;
	color: #A0522D;
	color: #0000FF;
}

/*===========欄位樣式============*/
.field_P,.field_Q,.field_QC,.field_PQ,.field,.field_PC {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	/** font-weight: bold; */
	border: 1px #ABABAB solid;
	font-size: 15px;
}

.field_C,.field_EP,.field_E,.field_QE {
	border: 1px #ABABAB solid;
	font-size: 15px;
	ime-mode: disabled;
}

.field_PRO,.field_QRO,.field_CRO,.field_QCRO,.field_RO {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	background-color: #F3EFEF;
	color: #333333;
	border: 1px #f3efef solid;
}

.field_NRO,.fieldQNRO {
	font-size: 15px;
	color: #333333;
	text-align: right;
	background-color: transparent;
	border: 1px #FFFFFF solid;
	border-bottom-color: black;
	border-bottom-style: dashed;
	ime-mode: disabled;
}

.field_N,.field_QN {
	font-size: 15px;
	COLOR: #333333;
	border: 1px #ABABAB solid;
	text-align: right;
	ime-mode: disabled;
}

.field_SN,.field_QSN {
	font-size: 15px;
	COLOR: #333333;
	border: 1px #ABABAB solid;
	text-align: right;
	ime-mode: disabled;
}

.toolbar_default {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-weight: bold;
	font-size: 14pt;
	-moz-border-radius: 8px;
	-webkit-border-radius: 8px;
	border-radius: 5px;
	border: 1px solid #585858;
	padding: 1px 1px 1px 1px;
	text-decoration: none;
	background-color: #dcc8f0;
	color: #8b0000;
	display: inline-block;
	-webkit-box-shadow: inset 1px 1px 0px 0px #ffffff;
	-moz-box-shadow: inset 1px 1px 0px 0px #ffffff;
	box-shadow: inset 0px 1px 0px #ffffff, 0px 0px 0px 0px #0000CD, 0px 3px
		3px #999;
	cursor: hand;
	height: 27px;
}

.toolbar_default:disabled {
	color: #949494;
}

.field_MSG {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	font-weight: bold;
	background-color: transparent;
	color: #000000;
	border-bottom: 1px #000000 dashed;
}

.field_MSGERR {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	font-weight: bold;
	background-color: transparent;
  	color: #FF0000;
	border-bottom: 1px #000000 dashed;
}

.field_REMARK {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	background-color: #F3EFEF;
	color: #FF0000;
	border: 1px #f3efef solid;
}

/**對外連結使用的按鈕設定**/
.toolbar_shortcut {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-weight: bold;
	font-size: 14pt;
	-moz-border-radius: 8px;
	-webkit-border-radius: 8px;
	border-radius: 5px;
	border: 1px solid #585858;
	padding: 1px 1px 1px 1px;
	text-decoration: none;
	/** background-color:#dcc8f0; **/
	background-color: #21a67c;
	/** color:#8b0000; **/
	color: #ffffff;
	display: inline-block;
	-webkit-box-shadow: inset 1px 1px 0px 0px #ffffff;
	-moz-box-shadow: inset 1px 1px 0px 0px #ffffff;
	box-shadow: inset 0px 1px 0px #ffffff, 0px 0px 0px 0px #0000CD, 0px 3px
		3px #999;
	cursor: hand;
	height: 27px;
}

.budgetgrid_option {
	width: 100%;
	height: 130px;
	color: #000000;
	background-color: #fffafa;
	white-space: nowrap;
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
}

/*===========輸入FORM樣式============ height: 220px; */
div#formContainer {
	width: 100%;
	height: 220px;
	overflow: auto;
	scrollbar-base-color: #eeeeee;
}

.bg {
	border: 2px solid #F3EFEF;
	padding: 2px 2px 2px 2px;
}

.bgToolbar {
	border: 2px solid #F3EFEF;
	padding: 2px 2px 2px 2px;
}

.bgList {
	border: 0px solid #dddddd;
	padding: 2px 2px 2px 2px;
}

.table_normal {
	font-size: 15px;
}

.table_form {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	border: 0px solid white;
	padding: 2px 2px 2px 2px;
	border-collapse: collapse;
	background-color: #F3EFEF;
}

.td_form {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	text-align: right;
	font-weight: bold;
	background-color: #626393;
	color: #FFFFFF;
	border-top: 1px solid silver;
	padding: 2px 6px 2px 2px;
}

.thead {
	font-size: 15px;
	font-weight: bold;
	background-color: #dddddd;
	color: #000000;
	border-top: 1px solid silver;
}

.td_form_left {
	text-align: left;
	font-size: 15px;
	background-color: #f3efef;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
	color: #000000;
}

.td_form_white {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	text-align: left;
	font-size: 15px;
	background-color: #F3EFEF;
	border-left: 0px solid silver;
	border-bottom: 0px solid silver;
	border-right: 0px solid silver;
	border-top: 1px solid silver;
	padding-top: 2px;
	padding-right: 5px;
	padding-bottom: 2px;
	padding-left: 2px;
	color: #000000;
}

.tr_odd {
	font-size: 15px;
	background-color: #f3efef;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
	padding-top: 2px;
	padding-right: 5px;
	padding-bottom: 2px;
	padding-left: 2px;
	color: #000000;
}

.tr_even {
	font-size: 15px;
	background-color: #dddddd;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
	padding-top: 2px;
	padding-right: 5px;
	padding-bottom: 2px;
	padding-left: 2px;
	color: #000000;
}

.td_lable {
	text-align: center;
	background-color: #ffffd2;
	padding: 2px 5px 2px 2px;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
}

.td_lable_white {
	text-align: center;
	background-color: #ffffff;
	padding: 2px 5px 2px 2px;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
}

.td_normal {
	font-size: 15px;
}

.td_default_banner {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	width: 100%;
	font-size: 17px;
	text-align: center;
	font-weight: bold;
	color: #ffffff;
	border-top: 1px solid silver;
	background: #666699;
	margin: 0px 0px 0px 0px;
	padding: 0px 0px 0px 0px;
	height: 25px;
}

/*===========查詢樣式============*/
div#queryContainer {
	position: absolute;
	z-index: 30;
	left: 0;
	top: 0;
}

iframe#queryContainerFrame {
	position: absolute;
	z-index: -1;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

.queryTitle {
	padding: 5px 5px 5px 5px;
	background-color: #3366cc;
	background-color: #84ABE2;
	color: white;
	border-top: 2px solid #84ABE2;
	border-left: 2px solid #84ABE2;
	border-right: 2px solid #84ABE2;
}

.queryTable {
	border: 2px solid #84ABE2;
	background-color: #EBEBEB;
	border-collapse: collapse;
	width: 100%;
	height: 100%;
}

.queryTDLable {
	color: #000000;
	background-color: #dddddd;
	padding: 2px 2px 2px 2px;
	height: 25px;
	text-align: right;
	text-valign: bottom;
}

.queryTDInput {
	background-color: #f3efef;
	padding: 2px 2px 2px 2px;
	height: 25px;
	text-align: left;
	text-valign: bottom;
}

/*===========列表樣式============ 180px*/
div#listContainer {
	width: 100%;
	height: auto;
	overflow: auto;
	scrollbar-base-color: #eeeeee;
	position: relative;
	top: 0;
	left: 0;
}

div#listContainer thead th {
	top: expression(document.getElementById ( "listContainer") .scrollTop-2);
	/* IE5+ only */
	z-index: 20;
}

div#listContainer thead th.locked {
	z-index: 30;
}

div#listContainer td.locked,div#listContainer th.locked {
	left: expression(document.getElementById ( "listContainer") .scrollLeft);
	/* IE5+ only */
	position: relative;
	z-index: 10;
}

.listTH {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	font-weight:bold;
	z-index: 20;
	color: #000000;
	text-align:center;
	height:24px;
	position:relative;
	border: 0px solid silver;
	background-color: #dddddd;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
}

.listTR {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	background-color: #F3F3F3;
	text-align: center;
	height:24px;
	border: 0px solid #FFFFFF;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
}

.listTREven {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	background-color: #F3F3F3;
	text-align: center;
	height:24px;
	border-left: 0px solid #FFFFFF;
	border-top: 0px solid #FFFFFF;
	border-right: 0px solid #FFFFFF;
	border-bottom: 1px solid #FFFFFF;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	color:#000000;
}

.listTROdd {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	text-align: center;
	height:24px;
	background-color: #EEEEEE;
	border-left: 0px solid #FFFFFF;
	border-top: 0px solid #FFFFFF;
	border-right: 0px solid #FFFFFF;
	border-bottom: 1px solid #FFFFFF;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	color:#000000;
}

.listTRMouseover {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 15px;
	/** font-weight: bold; **/
	text-align: center;
	height:24px;
	background-color: #D8FBB6;	
	border: 1px solid #FFFFFF;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	/** cursor: hand; */
}

.listTD {
	text-align: center;
	height:24px;
	border: 0px solid #FFFFFF;
	color: #000000;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	/** cursor: hand; */
}

.listTDEven {
	border: 0px solid #FFFFFF;
	color: #000000;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	/** cursor: hand; */
}

.listTDOdd {
	border: 0px solid #FFFFFF;
	color: #000000;
	padding-top:2px;
	padding-right:2px;
	padding-bottom:1px;
	padding-left:2px;
	/** cursor: hand; */
}

/*===========頁籤樣式============*/
.tab_line1 {
	height: 0px;
	/**
		background-color: #A6A6A6;
		border-right: thin #A6A6A6;
		border-left: thin #A6A6A6;
		**/
}

.tab_line2 {
	height: 0px;
	/**
		background-color: #A6A6A6;
		border-right: thin #A6A6A6;
		border-left: thin #A6A6A6;
		**/
}

.tab_border1 {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	text-align: center;
	width: 203;
	height: 27;
	font-size: 11pt;
	color: #FFFFFF;
	background-image: url(../images/pre/tab_selected_v3.gif);
	font-weight: bold;
}

.tab_border2 {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	text-align: center;
	width: 203;
	height: 27;
	font-size: 11pt;
	color: #000000;
	background-image: url(../images/pre/tab_unselect_v3.gif);
	font-weight: bold;
}

/** padding = top right bottom left **/
.tab_bg1 {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	text-align: center;
	font-size: 10pt;
	height: 27;
	color: #FFFFFF;
	background-color: #1E9CB7;
	border-left: solid thin #A6A6A6;
	border-top: solid thin #A6A6A6;
	border-right: solid thin #A6A6A6;
	border: 1px outset #EDEDED;
	padding: 5px 5px 5px 5px;
}

.tab_bg2 {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 10pt;
	height: 27;
	text-align: center;
	background-color: #gray;
	padding: 5px 5px 5px 5px;
	border-left: solid 1px #E0E0E0;
	border-left: solid 1px silver;
	border-top: solid 1px #E0E0E0;
	border-right: solid 1px gray;
	font-weight: normal
}

/*===========苻合舊程式之樣式,可取消============*/
/* list樣式=>深灰 */
.list_head {
	padding: 4px 20px 2px 2px;
	background-color: #666666;
	color: #ffffff;
	text-align: left;
	height: 25;
}

.list_title {
	padding: 4px 2px 2px 2px;
	background-color: #eeeeee;
	color: darkblue;
	text-align: center;
	height: 25;
}

.list_d1 {
	padding: 4px 2px 2px 2px;
	background-color: #ffffff;
	color: #000000;
	text-align: center;
	height: 25;
	/** cursor: hand; */
}

.list_d2 {
	padding: 4px 2px 2px 2px;
	background-color: #f9f9f9;
	color: #000000;
	text-align: center;
	height: 25;
	/** cursor: hand; */
}

.TDLable {
	background-color: #FDF5E6;
	padding: 2px 2px 2px 2px;
	height: 25px;
	text-valign: bottom;
}

/*===========列表樣式============*/
div#listMsg {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	font-size: 13px;
	color: yellow;
	width: 100%;
	height: 32px;
	overflow: hidden;
	vertical-align: top;
}

a {
	color: #156F82;
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

.authBg {
	background-color: #AAFFAA;
	font-size: 15px;
}

.highLight {
	padding: 2px 5px 2px 2px;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
	background-color: #ffffff;
	/*text-align:center;*/
	/**cursor: hand; **/
}

.highLights {
	/*text-align:center;*/
	background-color: #ffffd2;
	padding: 2px 5px 2px 2px;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	border-top: 1px solid silver;
	/** cursor: hand; **/
}

.toolbar_image_insert {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_add_over.gif)
}

.toolbar_image_insert_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_add.gif)
}

.toolbar_image_update {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_mod_over.gif)
}

.toolbar_image_update_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_mod.gif)
}

.toolbar_image_delete {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_del_over.gif)
}

.toolbar_image_delete_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_del.gif)
}

.toolbar_image_confirm {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sto_over.gif)
}

.toolbar_image_confirm_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sto.gif)
}

.toolbar_image_cancel {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_can_over.gif)
}

.toolbar_image_cancel_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_can.gif)
}

.toolbar_image_query {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sea_over.gif)
}

.toolbar_image_query_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sea.gif)
}

.toolbar_image_save {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sto_over.gif)
}

.toolbar_image_save_disable {
	background-color: transparent;
	border: 0px;
	width: 72;
	height: 26;
	background-image: url(../images/btn_sto.gif)
}

.toolbar_image_listshow {
	background-color: transparent;
	color: #FCFBF8;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri03_over.gif)
}

.toolbar_image_listshow_disable {
	background-color: transparent;
	color: #FCFBF8;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri03.gif)
}

.toolbar_image_listhide {
	background-color: transparent;
	color: #FCFBF8;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri02_over.gif)
}

.toolbar_image_listhide_disable {
	background-color: transparent;
	color: #FCFBF8;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri02.gif)
}

.toolbar_image_listprint {
	background-color: transparent;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri03_over.gif)
}

.toolbar_image_listprint_disable {
	background-color: transparent;
	border: 0px;
	width: 80;
	height: 26;
	background-image: url(../images/btn_pri03.gif)
}

.btn_assist {
	background-color: transparent;
	border: 0px;
	width: 26;
	height: 26;
	background-image: url(../images/btn_question01.gif)
}

.activeRow {
	font-family: "新細明體;細明體", "微軟正黑體", Verdana, Arial, Helvetica, sans-serif, EUDC;
	text-align: center;
	height: 18px;
	background-color: #D8FBB6;
	border-left: solid 1px #FFFFFF;
	border-top: solid 1px #FFFFFF;
	border-right: solid 1px #FFFFFF;
	border-bottom: :solid 1px #FFFFFF;
	padding: 4px 2px 2px 2px;
}

.trTop {
	vertical-align: top;
}

.field_btnAdd {
	background-color: transparent;
	border: 0px;
	width: 16;
	height: 16;
	background-image: url(../images/plus_icon.gif)
}

.field_btnRemove {
	background-color: transparent;
	border: 0px;
	width: 16;
	height: 16;
	background-image: url(../images/minus_icon.gif)
}

.btnDefault {
	border: 0px;
	height: 21;
	background-image: url(../images/icon001.gif);
	font-size: 15px;
	color: #ffffff;
}

#msgList div {
	width: 211px;
	height: 120px;
	background-color: #FFFFFF;
	color: #333;
	text-align: left;
	font-size: 10px;
}

[name^="showSame"] {
	cursor: pointer;
	cursor: hand;
}

td.fielddarkTitle {
	text-indent: 3px;
	font-weight: bold;
	text-align: left;
	background-color: #9D9DBD;
	font-size: 15px;
	color: #000000;
}

td.fieldlight {
	text-indent: 3px;
	font-weight: bold;
	background-color: #DDDDDD;
	font-size: 15px;
	color: #000000;
}

td.fielddark {
	text-indent: 3px;
	font-weight: bold;
	background-color: #CCCCFF;
	font-size: 15px;
	color: #000000;
}

.inputCenter {
	text-align: center;
	width:100% !important;
}

.inputNoBorder{
    float: left;
    border: none !important;
    margin-top: 7px;
    background-color: White;
    background-repeat: no-repeat;
    background-position: center center;
}

.field_CB {
	border: none !important;
	zoom: 1.5;
	vertical-align: top;
	padding: 0px !important;
	margin: 0px !important;
	outline: none !important;
}

/*=========== tab css ============*/
.ui-state-default a,.ui-state-default a:hover,.ui-state-default a:link {
	background: #dcc8f0 !important;
	color: #000000 !important;
}
.ui-state-hover a,.ui-state-hover a:hover,.ui-state-hover a:link {
	color: #000000 !important;
}
.ui-state-active a, .ui-state-active a:link, .ui-state-active a:visited {
	background: #626393 !important;
	color: #ffffff !important;
}
.ui-widget {
	background: #F3EFEF !important;
}
.ui-widget-header {
	border: none !important;
	background: #F3EFEF !important;
    font-weight: bold !important;
}
.ui-tabs .ui-tabs-nav li {
    font-size:15px !important;
    font-weight: bold !important;
}
.ui-tabs .ui-tabs-nav li a {
    outline: none !important;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active{
	background: #626393 !important;
	color: #FF0000 !important;
}

/*=========== title_form css ============*/
.title_form {
	border: 0px solid white;
	background-color: #F3EFEF;
	color: #000000;
}

.title_form_label {
	text-align: right;
	color:#0000FF;
}

.title_form_value {
	text-align: left;
	color:#000000;
}
