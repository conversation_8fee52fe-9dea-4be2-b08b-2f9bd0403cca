package com.kangdainfo.tcfi.view.pre;

import java.awt.GraphicsEnvironment;
/*
程式目的：預查線上申辦電子核定書
程式代號：pre4013
撰寫日期：103.05.15
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.lang.CommonStringUtils;
import com.kangdainfo.util.pdf.CommonPdfUtils;
import com.kangdainfo.util.report.JasperReportMaker;

import net.sf.jasperreports.engine.fonts.FontUtil;
import net.sf.jasperreports.engine.DefaultJasperReportsContext;
import net.sf.jasperreports.engine.fonts.FontFamily;

public class PRE4013 extends SuperBean {

	private String q_type;
	private String q_telixNo;
	private String q_prefixNo;
	private String q_prefixNoEnd;

	/** 標題 */
	private String title;
	/** 預查編號 */
	private String prefixNo;
	/** 申請項目 */
	private String applyItem;
	/** 公司說明 */
	private String companyDesc;
	/** 公司屬性-閉鎖性 */
	private String closedDesc;
	/** 審查結果 */
	private String approveResult;
	/** 審查結果說明 */
	private String approveResultDesc;
	/** 審查理由 */
	private List<Map<String, String>> approveReasons;
	/** 申請人-姓名 */
	private String applyName;
	/** 申請人-身分證字號 */
	private String applyId;
	/** 申請人-戶籍地址-標題 */
	private String applyAddrCont;
	/** 申請人-戶籍地址 */
	private String applyAddr;
	/** 申請人-聯絡電話 */
	private String applyTel;
	/** 馬上辦項目 */
	private String atonce;
	/** 組織型態 */
	private String orgType;
	/** 核准(定)日期 */
	private String closeDate;
	/** 核准保留期限 */
	private String reserveDate;
	/** 收件日期 */
	private String receiveDate;
	/** 國外匯款使用英文名稱 */
	private String extRemitEname;

	// --------------------------------------getter and setter of variables
	// bellow ---------------------------------------

	public String getQ_type() {
		return checkGet(q_type);
	}

	public void setQ_type(String s) {
		q_type = checkSet(s);
	}

	public String getQ_prefixNo() {
		return checkGet(q_prefixNo);
	}

	public void setQ_prefixNo(String s) {
		q_prefixNo = checkSet(s);
	}

	public String getQ_prefixNoEnd() {
		return checkGet(q_prefixNoEnd);
	}

	public void setQ_prefixNoEnd(String s) {
		q_prefixNoEnd = checkSet(s);
	}

	public String getQ_telixNo() {
		return checkGet(q_telixNo);
	}

	public void setQ_telixNo(String s) {
		q_telixNo = checkSet(s);
	}

	public String getReserveDate() {
		return checkGet(reserveDate);
	}

	public void setReserveDate(String s) {
		reserveDate = checkSet(s);
	}

	public String getCloseDate() {
		return checkGet(closeDate);
	}

	public void setCloseDate(String s) {
		closeDate = checkSet(s);
	}

	public String getPrefixNo() {
		return checkGet(prefixNo);
	}

	public void setPrefixNo(String s) {
		this.prefixNo = checkSet(s);
	}

	public String getApplyName() {
		return checkGet(applyName);
	}

	public void setApplyName(String s) {
		this.applyName = checkSet(s);
	}

	public String getApplyId() {
		return checkGet(applyId);
	}

	public void setApplyId(String s) {
		this.applyId = checkSet(s);
	}

	public String getApplyTel() {
		return checkGet(applyTel);
	}

	public void setApplyTel(String s) {
		this.applyTel = checkSet(s);
	}

	public String getApplyAddr() {
		return checkGet(applyAddr);
	}

	public void setApplyAddr(String s) {
		this.applyAddr = checkSet(s);
	}

	public String getAtonce() {
		return checkGet(atonce);
	}

	public void setAtonce(String s) {
		this.atonce = checkSet(s);
	}

	public String getOrgType() {
		return checkGet(orgType);
	}

	public void setOrgType(String s) {
		this.orgType = checkSet(s);
	}

	public String getTitle() {
		return checkGet(title);
	}

	public void setTitle(String s) {
		this.title = checkSet(s);
	}

	public String getApplyAddrCont() {
		return checkGet(applyAddrCont);
	}

	public void setApplyAddrCont(String s) {
		this.applyAddrCont = checkSet(s);
	}

	public String getApproveResult() {
		return checkGet(approveResult);
	}

	public void setApproveResult(String s) {
		this.approveResult = checkSet(s);
	}

	public String getApplyItem() {
		return checkGet(applyItem);
	}

	public void setApplyItem(String s) {
		this.applyItem = checkSet(s);
	}

	public String getApproveResultDesc() {
		return checkGet(approveResultDesc);
	}

	public void setApproveResultDesc(String s) {
		this.approveResultDesc = checkSet(s);
	}

	public String getCompanyDesc() {
		return checkGet(companyDesc);
	}

	public void setCompanyDesc(String s) {
		this.companyDesc = checkSet(s);
	}

	public String getClosedDesc() {
		return checkGet(closedDesc);
	}

	public void setClosedDesc(String s) {
		this.closedDesc = checkSet(s);
	}

	public List<Map<String, String>> getApproveReasons() {
		return approveReasons;
	}

	public void setApproveReasons(List<Map<String, String>> l) {
		this.approveReasons = l;
	}

	public String getReceiveDate() {
		return checkGet(receiveDate);
	}

	public void setReceiveDate(String s) {
		receiveDate = checkSet(s);
	}

	public String getExtRemitEname() {
		return checkGet(extRemitEname);
	}

	public void setExtRemitEname(String s) {
		extRemitEname = checkSet(s);
	}

	// --------------------------------------------------------------------------------------------------------------------

	public void doCreate() throws Exception {
	}

	public void doUpdate() throws Exception {
	}

	public void doDelete() throws Exception {
	}

	public Object doQueryOne() throws Exception {
		return null;
	}

	public ArrayList<?> doQueryAll() throws Exception {
		return null;
	}

	public boolean check() throws Exception {
		Cedb1000 cedb1000 = new Cedb1000();
		if ("".equals(getQ_telixNo()) && "".equals(this.getQ_prefixNo()) && "".equals(this.getPrefixNo())) {
			setErrorMsg("請輸入至少一項查詢條件");
			return false;
		}

		if ("telix".equals(q_type)) {
			cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByTelixNo(getQ_telixNo());
		} else {
			if (this.getQ_prefixNo().equals(this.getQ_prefixNoEnd()) || StringUtils.isBlank(this.getQ_prefixNoEnd())) {
				this.setPrefixNo(this.getQ_prefixNo());
			}

			cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(getPrefixNo());
		}

		if (cedb1000 == null) {
			setErrorMsg("查無資料，請變更查詢條件");
			return false;
		} else {
			setPrefixNo(cedb1000.getPrefixNo());
			List<Cedb1001> cedb1001List = ServiceGetter.getInstance().getPrefixService()
					.getCedb1001ByPrefixNo(cedb1000.getPrefixNo());
			if (null == cedb1001List || cedb1001List.isEmpty()) {
				setErrorMsg("查無公司名稱變更預查資料");
				return false;
			}
			List<Cedb1002> cedb1002List = ServiceGetter.getInstance().getPrefixService()
					.getCedb1002ByPrefixNo(cedb1000.getPrefixNo());
			if (null == cedb1002List || cedb1002List.isEmpty()) {
				setErrorMsg("查無所營事業變更預查資料");
				return false;
			}
			return true;
		} // end if
	} // check()

	/**
	 * 產製電子核定書
	 * 
	 * @return File
	 */
	public File doPrintPdf() {
		File report = null;
		try {
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo()
					.getRealPath("tcfi/report/pre4013.jasper");
			Map<String, Object> parameters = new HashMap<String, Object>();
			// 列印時間
			parameters.put("printDate", ServiceGetter.getInstance().getPre4013Service().getPrintDate());
			parameters.put("printTime", ServiceGetter.getInstance().getPre4013Service().getPrintTime());

			// TODO 2024/10/25 待移除 測試用
			// GraphicsEnvironment ge =
			// GraphicsEnvironment.getLocalGraphicsEnvironment();
			// String[] fontNames = ge.getAvailableFontFamilyNames();
			// for (String fontName : fontNames) {
			// System.out.println("驗證" + fontName);
			// }

			if (!check()) {
				return null;
			} else {
				PRE4013 obj = ServiceGetter.getInstance().getPre4013Service().queryPrintDataByPrefixNo(getPrefixNo());

//				System.out.println(
//						getClass().getClassLoader().getResource("fonts/中推會楷體0字面/CMEX-Kai-Subset-only-Plane0.ttf"));
//
//				InputStream is = getClass().getClassLoader().getResourceAsStream("jasperreports_extension.properties");
//				if (is != null) {
//					System.out.println("jasperreports_extension.properties loaded successfully!");
//				} else {
//					System.err.println("jasperreports_extension.properties not found in classpath.");
//				}

				// 寫入[電子核定書查閱時間]
				try {
					ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(obj.getPrefixNo(), getLoginUserId(),
							PrefixConstants.PREFIX_STATUS_Y);
				} catch (Exception e) {
					System.out.println("錯誤: " + e.getMessage());
				}

				List<Object> dataList = new ArrayList<Object>();
				dataList.add(obj);

				// 新增個資軌跡
				ServiceGetter.getInstance().getTrackLogService().insertApplyPerson("PRE4013",
						PrefixConstants.TRACK_LOG_SEARCH, getPrefixNo(), getApplyId(), getApplyName(), getApplyTel(),
						getApplyAddr());
				report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
				return report;
			} // end else
		} // try
		catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 200) {
				setErrorMsg(getErrorMsg());
			} // end if
			else
				setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch
	} // printPdf

	/**
	 * 批次產製電子核定書
	 * 
	 * @return File
	 */
	public File makeMutiReportByPrefixNo() throws Exception {
		List<String> prefixNos = new ArrayList<String>();
		String startNo = String.format("%09d", Integer.parseInt(getQ_prefixNo()));
		if (CommonStringUtils.isNotEmpty(getQ_prefixNoEnd())) {
			String endNo = String.format("%09d", Integer.parseInt(getQ_prefixNoEnd()) + 1);
			while (!startNo.equals(endNo)) {
				prefixNos.add(startNo);
				startNo = String.format("%09d", Integer.parseInt((startNo)) + 1);
			}
		} else {
			prefixNos.add(startNo);
		}

		List<File> pdfFiles = new ArrayList<File>();
		for (String prefixNo : prefixNos) {
			this.setPrefixNo(prefixNo); // setting for check().
			pdfFiles.add(doPrintPdf());
		}

		File mergeFile = null;
		if (null != pdfFiles && !pdfFiles.isEmpty()) {
			mergeFile = CommonPdfUtils.concatPdfFiles(pdfFiles, true);
		}
		if (mergeFile != null) {
			this.setErrorMsg("製作成功");
		} else {
			this.setErrorMsg("製作失敗");
		}
		return mergeFile;
	}
}
