<!-- 
程式目的：個人主頁
程式代號：pre3012
程式日期：1030630
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3012">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList3010"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="objList3011"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3012" />
</jsp:include>
<%
objList3010 = obj.doQueryAll3010();
objList3011 = obj.doQueryAll3011();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
$(document).ready(function() {
	
	$.post( getVirtualPath() + "tcfi/ajax/jsonPre3010ChkWorkday.jsp", function( json ) {
		if(null!=json) {
			var workdays;
			$.each(json, function(k, v) {
				if(v) {
					workdays = v.split("-");
					if(workdays.length == 2)
						$("#listContainerRow" + k).find("td").eq(5).text(workdays[0]).next().next().next().text(workdays[1]);
				}
			});
		}
	});
	
	$.post( getVirtualPath() + "tcfi/ajax/jsonPre3011ChkWorkday.jsp", function( json ) {
		if(null!=json) {
			var workdays;
			$.each(json, function(k, v) {
				if(v) {
					workdays = v.split("-");
					if(workdays.length == 2)
						$("#listContainer2 #listContainerRow" + k).find("td").eq(5).text(workdays[0]).next().next().next().text(workdays[1]);
				}
			});
		}
	});
	
	
});

function queryOne(prefixNo) {
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	var url = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo="+prefixNo+"&hiddenPrefixNos="+prefixNo;
	window.open(url,'pre3001',prop).focus();
}

function init(){
	changeTab(1);
}

function changeTab(tabId) {
	if ( tabId == "1" ) {
	  	document.getElementById("t1").className = "tab_border1";
	  	document.getElementById("t2").className = "tab_border2";
	  	document.getElementById("listContainer1").style.display = '';
      	document.getElementById("listContainer2").style.display = 'none';
	} // end if
	else  {
		document.getElementById("t1").className = "tab_border2";
		document.getElementById("t2").className = "tab_border1";
		document.getElementById("listContainer1").style.display = 'none';
		document.getElementById("listContainer2").style.display = '';	
	} // end else if
} 
</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="">
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE3012'/>
</c:import>

<table cellpadding="0" cellspacing="0" valign="top" width="100%">
      <tr>
	    <td class="tab_border2" id="t1" onClick="changeTab(1)">待辦案件清單</td>
		<td class="tab_border2" id="t2" onClick="changeTab(2)">今日案件清單</td>
		<td style="text-align:right;"><font color="#0000"><%=obj.getStaffName()%></font></td>
	  </tr>
  </table> 
  <div id="listContainer1" height="200">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" width="5%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
    		<th class="listTH" width="13%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查名稱</a></th>
    		<th class="listTH" width="8%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查種類</a></th>
    		<th class="listTH" width="9%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">申請人</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦工作<br>天數</a></th>
    		<th class="listTH" width="18%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">分文時間</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">展期註記</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">審核工作<br>時數</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray3010[] = {true,false,false,false,false,false,false,false};
  			boolean displayArray3010[] = {true,true,true,true,true,true,true,true};
  			String[] alignArray3010 = {"center", "left","center","left","center","center","center","center"};
  			out.write(obj.getQuerylist(primaryArray3010,displayArray3010,alignArray3010,objList3010,"true",true,false,null,null,"",true,true,""));
  			%>
  		</tbody>
	</table>
  </div>
   
  <div id="listContainer2" height = "200">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" width="5%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
    		<th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查名稱</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查種類</a></th>
    		<th class="listTH" width="15%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">申請人</a></th>
    		<th class="listTH" width="15%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">分文時間</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">覆核結果</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">展期原因</a></th>
    		<th class="listTH" width="9%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">審核時數</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray3011[] = {true,false,false,false,false,false,false,false};
  			boolean displayArray3011[] = {true,true,true,true,true,true,true,true};
  			String[] alignArray3011 = {"center", "left","center","center","center","center","center","center"};
  			out.write(obj.getQuerylist(primaryArray3011,displayArray3011,alignArray3011,objList3011,"true",true,false,null,null,"",true,true,""));
  			%>
  		</tbody>
	</table>
  </div>

</form>
</body>
</html>