package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;

/**
 * 排程(PRE0012)
 * 定時檢查重複辦理馬上辦延期一個月的案件, 將資料釐正
 */
public class Pre0012QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		ServiceGetter.getInstance().getCorrectDataService().doCorrectReserveDate();
	}

}