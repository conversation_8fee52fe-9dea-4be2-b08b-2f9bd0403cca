<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
if(!"".equals(obj.getOnlineFilePath())) {
	System.out.println((new java.util.Date())+" onlineFilePath:"+obj.getOnlineFilePath());
	String realFilePath = "";
	try {
		org.apache.commons.codec.binary.Base64 base64encoder = new org.apache.commons.codec.binary.Base64();
		realFilePath = new String(base64encoder.decode(obj.getOnlineFilePath().getBytes()));
	} catch(Exception e) {
		e.printStackTrace();
	}
	System.out.println((new java.util.Date())+" onlineFilePath:"+realFilePath);
	java.io.File report = new java.io.File(realFilePath);
	if(null!=report)
	{
		obj.outputFile(response, report, report.getName(), true);
		out.clear();
		out = pageContext.pushBody();
	}
	System.out.println((new java.util.Date())+" complete");
}
%>