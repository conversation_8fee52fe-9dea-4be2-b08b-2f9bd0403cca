package com.kangdainfo.tcfi.service;

import java.util.List;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;

public interface Pre2008Service {

	public int doSave( PostRecord postRecord, String cedb1023Flag, String funCode) throws Exception ;
	public int doUpdate( PostRecord postRecord, String cedb1023Flag, String funCode) throws Exception ;
	public int doAssign( Cedb1017 cedb1017 ) throws Exception ;
	public int rollBack( PostRecord postRecord ) throws Exception;
	
	public List<PostRecord> getPostRecordList(String prefixNo);
	
	public PostRecord getPostRecordByPrefixNo(String prefixNo);
	
} 