<!DOCTYPE html>
<!--
程式目的：補印預查收據
程式代號：pre5002
撰寫日期：105.12.28
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE5002">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>    
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE5002" />
</jsp:include>
<%

if("doSomeCheck".equals(obj.getState())) {
	//空白表單收文 或 一維條碼$("#state").val("init");
	obj.doSomeCheck();
} else if ("checkSuccess".equals(obj.getState())) {
	java.io.File report = obj.doPrintPdf();
	String fileName = "pre5002.pdf";
	if(null!=report)
	{
		obj.outputFile(response, report, fileName);
		out.clear();
		out = pageContext.pushBody();
	}
	obj.setState("init");
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
$( document ).ready(function() {
	var msg ="";
	if ( $("#state").val() == "checkSuccess" ) {
		var target = 'PRE5002_'+randomUUID().replace(/\-/g,"");
		window.open("",target);
		form1.target = target;
		form1.submit();
		$("#state").val("init");
		form1.target = '';
	}
	$("#doPrintPdf").click(function() {
		if (!$("input[name=queryType]").is(':checked') ) {
    		msg = "請選擇查詢條件";
    		showMsgBar(msg);
    		return;
		} else {
			if ($("input[name=queryField]").val() == '') {
				msg = "請輸入查詢條件";
				showMsgBar(msg);
				return;
			}
		}
		form1.action = 'pre5002.jsp';
		form1.state.value = 'doSomeCheck';
   		form1.submit();
	});
});
</script>
</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE5002'/>
</c:import>

<!--Toolbar區============================================================-->
<table>
	<tr><td style="text-align:left">
	   	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
		<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	</td></tr>
</table>

<!--Form區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg">
	<table class="table_form">
		<tr>
			<td class="td_form">查詢條件</td>
			<td class="td_form_white">
				<input type="radio" class="field_Q" name="queryType" value="0" <%="0".equals(obj.getQueryType())?"checked":""%>>收據編號&nbsp;&nbsp;
				<input type="radio" class="field_Q" name="queryType" value="2" <%="2".equals(obj.getQueryType())?"checked":""%>>預查編號&nbsp;
				<input type="text" class="field_Q" id="queryField" name="queryField" size="20" maxLength="11" value="<%=obj.getQueryField()==null?"":obj.getQueryField()%>" />&nbsp;
				<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="列印">
            </td>
        </tr>
	</table>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
</table>
</form>
</body>
</html>



