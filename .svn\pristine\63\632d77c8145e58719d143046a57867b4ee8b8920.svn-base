package com.kangdainfo.tcfi.service;

import java.io.File;

import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;

/**
 * 預查收費作業
 *
 */
public interface Pre5001Service {
	public ReceiptNoSetup selectReceiptNoSetupByReceiptType(String receiptType) throws Exception;
	public boolean insertPrefixReceiptNoAndUpdateReceiptNoSetup(PrefixReceiptNo vo1, ReceiptNoSetup vo2) throws Exception;
	public Eedb1000 selectEedb1000ByTelixNo(String telixNo)throws Exception;
	public Eedb3300 selectEedb3300ByTelixNo(String telixNo)throws Exception;
	public PrefixReceiptNo selectPrefixReceiptNo(String receiptNo) throws Exception;
	public PrefixReceiptNo selectPrefixReceiptNoByTelixNo(String telixNo) throws Exception;
	
	public File createPdfFile(PrefixReceiptNo prefixReceiptNo) throws Exception;
		
}