package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.view.pre.PRE4022;

/**
 * 公司名稱預查規費日報表
 * 113/03/27
 */
public interface Pre4022Service {
	
	/** 
	 * 根據收文時間區間檢核 
	 * @param dateStart
	 * @param dateEnd
	 * @param timeStart
	 * @param timeEnd
	 * @return List<Cedb1000>
	 **/
		public List<Cedb1000> selectCEDB1000ByTimeInterval(String dateStart, String dateEnd, String timeStart, String timeEnd) throws Exception;
		
	/** 
	 * 根據繳款時間區間檢核
	 * @param dateStart
	 * @param dateEnd
	 * @return List<PrefixReceiptNo>
	 **/
	public List<PrefixReceiptNo> selectPrefixReceiptNoByTimeInterval(String dateStart, String dateEnd) throws Exception;
	
	/**
	 * 取得 列印日期
	 * @return String
	 */
	public String getPrintDate();

	/**
	 * 取得 列印時間
	 * @return String
	 */
	public String getPrintTime();
	
	/**
	 * 根據勾選收文日期範圍取得相應資料
	 * @param dateStart
	 * @param dateEnd
	 * @param timeStart
	 * @param timeEnd
	 * @param idNo
	 * @param changeTypeCode
	 * @param payTypeCode
	 * @return com.kangdainfo.tcfi.view.pre.PRE4022
	 */
	public List<PRE4022> selectPre4022ByTypeEqualReceive(String dateStart, String dateEnd, String timeStart, String timeEnd, String idNo, String changeTypeCode, String payTypeCode);
	
	/**
	 * 根據勾選繳款日期範圍取得相應資料
	 * @param dateStart
	 * @param dateEnd
	 * @param idNo
	 * @param changeTypeCode
	 * @param payType
	 */
	public List<PRE4022> selectPre4022ByTypeEqualPay(String dateStart, String dateEnd, String idNo, String changeTypeCode, String payTypeCode);
}
