package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc004;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao;
import com.kangdainfo.tcfi.service.Pre8012Service;
import com.kangdainfo.tcfi.view.pre.PRE8012;

public class Pre8012ServiceImpl implements Pre8012Service {

	@SuppressWarnings("unchecked")
	public List<Cedbc004> queryAll(PRE8012 condition) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDBC004");
		if ( !"".equals(Common.get(condition.getQ_seqNo())) ) {
			sqljob.appendSQLCondition("SEQ_NO = ?");
			sqljob.addParameter(condition.getQ_seqNo());
		}
		if ( !"".equals(Common.get(condition.getQ_banWord())) ) {
			sqljob.appendSQLCondition("BAN_WORD LIKE ?");
			sqljob.addLikeParameter(condition.getQ_banWord());
		}
		if ( !"".equals(Common.get(condition.getQ_letterNo())) ) {
			sqljob.appendSQLCondition("LETTER_NO LIKE ?");
			sqljob.addSuffixLikeParameter(condition.getQ_letterNo());
		}
		if ( !"".equals(Common.get(condition.getQ_letterDesc())) ) {
			sqljob.appendSQLCondition("LETTER_DESC LIKE ?");
			sqljob.addLikeParameter(condition.getQ_letterDesc());
		}
		if ( !"".equals(Common.get(condition.getQ_modifier())) ) {
			sqljob.appendSQLCondition("MOD_ID_NO = ?");
			sqljob.addParameter(condition.getQ_modifier());
		}
		if ( !"".equals(Common.get(condition.getQ_modDate())) ) {
			sqljob.appendSQLCondition("MOD_DATE = ?");
			sqljob.addParameter(condition.getQ_modDate());
		}
		sqljob.appendSQL("ORDER BY nvl(mod_date,'0000000')||nvl(mod_time,'000000') DESC");
		System.out.println(sqljob);
		return eicmGeneralQueryDao.query(sqljob, BeanPropertyRowMapper.newInstance(Cedbc004.class));
	}

	/**
	 * 檢查禁用名稱是否存在
	 * @param banWord 禁用名稱
	 * @return 存在（true） / 不存在（false）
	 */
	public boolean isBanWordExits(String banWord) {
		boolean isExits = false;
		if(!"".equals(Common.get(banWord))) {
			Cedbc004 obj = cedbc004Dao.findByBanWord(banWord);
			if(null!=obj)
				isExits = true;
		}
		return isExits;
	}

	public Cedbc004 queryByBanWord(String banWord) {
		if(!"".equals(Common.get(banWord))) {
			return cedbc004Dao.findByBanWord(banWord);
		}
		return null;
	}

	public Cedbc004 queryBySeqNo(String seqNo) {
		if(!"".equals(Common.get(seqNo))) {
			return cedbc004Dao.findBySeqNo(seqNo);
		}
		return null;
	}

	public void insertCedbc004(Cedbc004 obj) {
		if(null!=obj) {
			obj.setModDate(Datetime.getYYYMMDD());
			obj.setModTime(Datetime.getHHMMSS());
			cedbc004Dao.insert(obj);
		}
	}
	
	public Cedbc004 updateCedbc004(Cedbc004 obj) {
		if(null!=obj) {
			obj.setModDate(Datetime.getYYYMMDD());
			obj.setModTime(Datetime.getHHMMSS());
			cedbc004Dao.update(obj);
		}
		return cedbc004Dao.update(obj);
	}
	
	public void deleteCedbc004(Cedbc004 obj) {
		cedbc004Dao.delete(obj);
	}

	private Cedbc004Dao cedbc004Dao;
	private EicmGeneralQueryDao eicmGeneralQueryDao;

	public Cedbc004Dao getCedbc004Dao() {return cedbc004Dao;}
	public void setCedbc004Dao(Cedbc004Dao dao) {this.cedbc004Dao = dao;}
	public EicmGeneralQueryDao getEicmGeneralQueryDao() {return eicmGeneralQueryDao;}
	public void setEicmGeneralQueryDao(EicmGeneralQueryDao dao) {this.eicmGeneralQueryDao = dao;}

}