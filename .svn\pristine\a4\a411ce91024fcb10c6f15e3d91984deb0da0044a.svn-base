package com.kangdainfo.tcfi.loader;

import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.tcfi.model.eicm.dao.FunctionMenuDao;

public class FunctionMenuLoader extends BaseLoader {
	private static final String CACHE_NAME = "CACHE_NAME_FUNCTION_MENU";
	//singleton
	private static FunctionMenuLoader instance;
	public FunctionMenuLoader() {
		if (FunctionMenuLoader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ FunctionMenuLoader.instance);
		}
		FunctionMenuLoader.instance = this;
	}
	public static FunctionMenuLoader getInstance() {return instance;}

	/**
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<FunctionMenu> loadFunctionMenus() {
		if(getServletContext().getAttribute(CACHE_NAME) == null)
			reload();
		return (List<FunctionMenu>) getServletContext().getAttribute(CACHE_NAME);
	}

	public FunctionMenu getFunctionMenuByCode(String code) {
		List<FunctionMenu> datas = loadFunctionMenus();
		for(FunctionMenu data : datas)
		{
			if( null!=data.getCode() && data.getCode().equals(code) )
				return data;
		}
		return null;
	}
	
	public String getFunctionMenuNameByCode(String code) {
		List<FunctionMenu> datas = loadFunctionMenus();
		for(FunctionMenu data : datas)
		{
			if( null!=data.getCode() && data.getCode().equals(code) )
				return data.getTitle();
		}
		return null;
	}

	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	/** 重新載入 */
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(CACHE_NAME, functionMenuDao.findAll());
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}

	private FunctionMenuDao functionMenuDao;
	public FunctionMenuDao getFunctionMenuDao() {return functionMenuDao;}
	public void setFunctionMenuDao(FunctionMenuDao dao) {this.functionMenuDao = dao;}

}