package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.util.lang.CommonStringUtils;

public class FunctionMenuDao extends BaseDaoJdbc implements RowMapper<FunctionMenu> {

	private static final String SQL_find = "SELECT * FROM FUNCTION_MENU";
	public FunctionMenu findById(int id) {
		//check pk
		if(Common.get(id) == null) return null;
		SQLJob sqljob = new SQLJob(SQL_find + " WHERE ID = ? ");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<FunctionMenu> list = (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}
	
	public FunctionMenu findByCode(String code) {
		//check pk
		if(Common.get(code) == null) return null;
		SQLJob sqljob = new SQLJob(SQL_find + " WHERE CODE = ? ");
		sqljob.addParameter(code);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<FunctionMenu> list = (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}

	private static final String SQL_findByPid = "SELECT * FROM FUNCTION_MENU WHERE PID = ? AND ENABLE = 'Y' ORDER BY SORTED, CODE";
	public List<FunctionMenu> findByPid(Integer pid) {
		if(null==pid) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPid);
		sqljob.addParameter(pid);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findAll = "SELECT * FROM FUNCTION_MENU WHERE ENABLE = 'Y' ORDER BY SORTED, CODE";
	public List<FunctionMenu> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public List<FunctionMenu> findByGroupId(String groupId) {
		if("".equals(Common.get(groupId)))	return null;
		SQLJob sqljob = new SQLJob("select m.* from function_menu_auth a, function_menu m where a.function_menu_id = m.id and group_id = ? ");
		sqljob.addParameter(groupId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public List<FunctionMenu> findByNotExistsAuth(String groupId) {
		if("".equals(Common.get(groupId)))	return null;
		SQLJob sqljob = new SQLJob("select * from function_menu m");
		sqljob.appendSQL("where enable = 'Y'");
		sqljob.appendSQL("AND (");
		sqljob.appendSQL("  not exists (select 1 from function_menu_auth where function_menu_id=m.id and group_id=?)");
		sqljob.appendSQL("or");
		sqljob.appendSQL("  id in (");
		sqljob.appendSQL("    select pid from function_menu n");
		sqljob.appendSQL("    where enable = 'Y'");
		sqljob.appendSQL("    and not exists (select 1 from function_menu_auth where function_menu_id=n.id and group_id=?)");
		sqljob.appendSQL("    and pid is not null");
		sqljob.appendSQL("  )");
		sqljob.appendSQL(")");
		sqljob.appendSQL("order by nvl(m.pid,-1), m.sorted, m.code");
		sqljob.addParameter(groupId);
		sqljob.addParameter(groupId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<FunctionMenu>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public FunctionMenu insert(FunctionMenu bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getCode())) return null;
		//check exist
		FunctionMenu t = findByCode(bo.getCode());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO FUNCTION_MENU (");
		sqljob.appendSQL(" PID");
		sqljob.appendSQL(",CODE");
		sqljob.appendSQL(",URL");
		sqljob.appendSQL(",TITLE");
		sqljob.appendSQL(",TARGET");
		sqljob.appendSQL(",SORTED");
		sqljob.appendSQL(",ENABLE");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getPid());
		sqljob.addParameter(bo.getCode());
		sqljob.addParameter(bo.getUrl());
		sqljob.addParameter(bo.getTitle());
		sqljob.addParameter(bo.getTarget());
		sqljob.addParameter(bo.getSorted());
		sqljob.addParameter(bo.getEnable());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.NUMERIC,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.NUMERIC,
					java.sql.Types.VARCHAR}
		);
		return findByCode(bo.getCode());
	}

	public FunctionMenu update(FunctionMenu bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getCode())) return null;
		//check exist
		FunctionMenu t = findByCode(bo.getCode());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE FUNCTION_MENU SET");
			sqljob.appendSQL("PID = ?");
			sqljob.appendSQL(",URL = ?");
			sqljob.appendSQL(",TITLE = ?");
			sqljob.appendSQL(",TARGET = ?");
			sqljob.appendSQL(",SORTED = ?");
			sqljob.appendSQL(",ENABLE = ?");
			sqljob.appendSQL("WHERE CODE = ?");
			sqljob.addParameter(bo.getPid());
			sqljob.addParameter(bo.getUrl());
			sqljob.addParameter(bo.getTitle());
			sqljob.addParameter(bo.getTarget());
			sqljob.addParameter(bo.getSorted());
			sqljob.addParameter(bo.getEnable());
			sqljob.addParameter(bo.getCode());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
					,new int[]{
						java.sql.Types.NUMERIC,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.NUMERIC,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR}
			);
			return findByCode(bo.getCode());
		}
	}
	
	public void delete(FunctionMenu bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getCode()) ) {
			//delete - 只設定停用, 不刪除
			SQLJob sqljob = new SQLJob("UPDATE FUNCTION_MENU SET");
			sqljob.appendSQL("ENABLE = 'N'");
			sqljob.appendSQL("WHERE CODE = ?");
			sqljob.addParameter(bo.getCode());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public FunctionMenu mapRow(ResultSet rs, int idx) throws SQLException {
		FunctionMenu obj = null;
		if(null!=rs) {
			obj = new FunctionMenu();
			obj.setId(rs.getInt("ID"));
			obj.setPid(rs.getInt("PID"));
			obj.setCode(rs.getString("CODE"));
			//obj.setName(rs.getString("NAME"));
			obj.setUrl(rs.getString("URL"));
			obj.setTitle(rs.getString("TITLE"));
			obj.setTarget(rs.getString("TARGET"));
			obj.setSorted(rs.getInt("SORTED"));
			obj.setEnable(rs.getString("ENABLE"));
		}
		return obj;
	}

}