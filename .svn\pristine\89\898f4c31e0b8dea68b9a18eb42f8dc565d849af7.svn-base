package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司預查申請案收件人封存資料檔(CEDB1123)
 *
 */
public class Cedb1123 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 收件人地址 */
	private String getAddr;
	/** 收件人姓名 */
	private String getName;
	/** 是否接受簡訊服務 */
	private String sms;
	/** 接收簡訊手機號碼 */
	private String contactCel;
	/** 變更類別 */
	private String changeType;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getGetAddr() {return getAddr;}
	public void setGetAddr(String getAddr) {this.getAddr = getAddr;}
	public String getGetName() {return getName;}
	public void setGetName(String getName) {this.getName = getName;}
	public String getSms() {return sms;}
	public void setSms(String sms) {this.sms = sms;}
	public String getContactCel() {return contactCel;}
	public void setContactCel(String contactCel) {this.contactCel = contactCel;}
	public String getChangeType() {return changeType;}
	public void setChangeType(String changeType) {this.changeType = changeType;}

}