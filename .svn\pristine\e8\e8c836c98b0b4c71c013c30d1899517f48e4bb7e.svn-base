package com.kangdainfo.web.listener;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;
import org.springframework.web.context.request.RequestContextListener;

import com.kangdainfo.common.model.bo.CommonDtree;

public class MyServletContextListener extends RequestContextListener implements ServletContextListener {

	public static final String NL = "\r\n";
	
	private Logger logger = Logger.getLogger(this.getClass());
    
	public void contextInitialized(ServletContextEvent sce) {
		ServletContext servletContext = sce.getServletContext();
		MyServletContext.getInstance().setServletContext(servletContext);
		MyServletContext.getInstance().setFunctionMap(new java.util.concurrent.ConcurrentHashMap<String, CommonDtree>());
		StringBuffer message = new StringBuffer("\r\n");
		message.append("##############################################################").append("\r\n");
		message.append("############### ").append(servletContext.getServletContextName()).append(" Started! ##############").append("\r\n");
		message.append("##############################################################").append("\r\n");
		if(logger.isInfoEnabled()) logger.info(message.toString());
	}

	public void contextDestroyed(ServletContextEvent sce) {
		StringBuffer message = new StringBuffer("\r\n");
		message.append("##############################################################").append("\r\n");
		message.append("############### ").append(MyServletContext.getInstance().getServletContext().getServletContextName()).append(" Shutdown! #############").append("\r\n");
		message.append("##############################################################").append("\r\n");
		if(logger.isInfoEnabled()) logger.info(message.toString());
		MyServletContext.getInstance().setFunctionMap(null);
		MyServletContext.getInstance().setServletContext(null);
	}	 

}