package com.kangdainfo.tcfi.loader;

/**
 * 展期原因
 *
 */
public class SystemCode14Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_14";
	private static final String CODE_KIND = "14";//14:展期原因

	//singleton
	private static SystemCode14Loader instance;
	public SystemCode14Loader() {
		if (SystemCode14Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode14Loader.instance);
		}
		SystemCode14Loader.instance = this;
	}
	public static SystemCode14Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}

}