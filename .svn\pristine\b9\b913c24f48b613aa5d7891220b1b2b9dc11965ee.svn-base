package com.kangdainfo.common.model.bo;

import com.kangdainfo.common.util.Common;


public class CommonUser implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	/** 帳號 */
	private String userId;
	/** 密碼 */
	private String userPwd;
	/** 姓名 */
	private String userName;
	/** 單位別 */
	private String userDept;
	/** 使用權責類別 */
	private String userGroup;
	/**  */
	private String sessionId;
	/**  */
	private String loginDate;
	/**  */
	private String loginTime;
	/**  */
	private String userIP;
	/** 權限 */
	private java.util.List<String> permissions;
	/** 是否為憑證登入 */
	private String isSSO;
	
	public String getUserId() {return Common.get(userId);}
	public void setUserId(String s) {this.userId = Common.set(s);}
	public String getUserPwd() {return Common.get(userPwd);}
	public void setUserPwd(String s) {this.userPwd = Common.set(s);}
	public String getUserName() {return Common.get(userName);}
	public void setUserName(String s) {this.userName = Common.set(s);}
	public String getUserDept() {return Common.get(userDept);}
	public void setUserDept(String s) {this.userDept = Common.set(s);}
	public String getUserGroup() {return Common.get(userGroup);}
	public void setUserGroup(String s) {this.userGroup = Common.set(s);}
	public String getSessionId() {return Common.get(sessionId);}
	public void setSessionId(String s) {this.sessionId = Common.set(s);}
	public String getLoginDate() {return Common.get(loginDate);}
	public void setLoginDate(String s) {this.loginDate = Common.set(s);}
	public String getLoginTime() {return Common.get(loginTime);}
	public void setLoginTime(String s) {this.loginTime = Common.set(s);}
	public String getUserIP() {return Common.get(userIP);}
	public void setUserIP(String s) {this.userIP = Common.set(s);}
	public java.util.List<String> getPermissions() {return permissions;}
	public void setPermissions(java.util.List<String> permissions) {this.permissions = permissions;}
	public String getIsSSO() {return isSSO;}
	public void setIsSSO(String isSSO) {this.isSSO = isSSO;}
	
	@Override
	public String toString() {
		return "CommonUser [userId=" + userId + ", userPwd=" + userPwd
				+ ", userName=" + userName + ", userDept=" + userDept
				+ ", userGroup=" + userGroup + ", sessionId=" + sessionId
				+ ", loginDate=" + loginDate + ", loginTime=" + loginTime
				+ ", userIP=" + userIP + ", permissions=" + permissions + ", isSSO=" + isSSO + "]";
	}

}