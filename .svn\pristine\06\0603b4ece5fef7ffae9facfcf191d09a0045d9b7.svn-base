package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;

public class OssmApplMainDao extends BaseDaoJdbc implements RowMapper<OssmApplMain> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSM_APPL_MAIN WHERE TELIX_NO = ?";
	public OssmApplMain findByTelixNo(String telixNo) {
		if("".equals(Common.get(telixNo))) return null;
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<OssmApplMain> results = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=results && !results.isEmpty())
			return results.get(0);
		return null;
	}

	public List<OssmApplMain> findByUserid(String userid) {
		if(!"".equals(Common.get(userid))) {
			SQLJob sqljob = new SQLJob("SELECT * FROM OSSM_APPL_MAIN WHERE USERID = ?");
			sqljob.addParameter(userid);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		}
		return null;
	}
	
	private static final String applMainSql = "SELECT * FROM OSSS.OSSM_APPL_MAIN A WHERE TELIX_NO IN ("
			+ " SELECT TELIX_NO FROM OSSS.OSSM_APPL_FLOW WHERE (TELIX_NO LIKE 'OSC%' OR TELIX_NO LIKE 'OSS%')"
			+ " AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I')"
			+ " AND PROCESS_STATUS = '003') "
			+ "ORDER BY A.UPDATE_TIME, A.CREATE_TIME";
	
	public List<OssmApplMain> findOnline() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(applMainSql);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Map<String, java.util.Date> doReadAllCaseTelixDate(int limit) {
		OssmApplFlowDao ossmApplFlowDao = ServiceGetter.getInstance().getPrefixService().getOssmApplFlowDao();
		
		List<OssmApplFlow> flows = ossmApplFlowDao.doReadCaseByRownum(limit); //所有線上申辦文
		
		Map<String, java.util.Date> Result = new HashMap<String, java.util.Date>();
		//排序用的時間 使用 OssmApplFlowTmp 資料中的付款完成時間(003：資料已傳送)
		OssmApplFlow flowTmp;
		OssmApplMain result;
		for(int i=0; i<flows.size(); i++){
			flowTmp = flows.get(i);
			result = findByTelixNo(flowTmp.getTelixNo());
			java.util.Date RefDate = flowTmp.getUpdateTime();
			if(RefDate == null)
				RefDate = flowTmp.getCreateTime();
			Result.put(result.getTelixNo(), RefDate);
		}
		return Result;
	}

	/** 更新一站式申辦類型 */
	public int updateApplyType(String telixNo, String applyType) {
		if("".equals(Common.get(telixNo)) || "".equals(Common.get(applyType))) return -1;
		SQLJob sqljob = new SQLJob("UPDATE OSSS.OSSM_APPL_MAIN SET APPLY_TYPE = ? WHERE TELIX_NO = ?");
		sqljob.addParameter(applyType);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(telixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	@Override
	public OssmApplMain mapRow(ResultSet rs, int idx) throws SQLException {
		OssmApplMain obj = null;
		if(null!=rs) {
			obj = new OssmApplMain();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setApplyType(rs.getString("APPLY_TYPE"));
			obj.setWriterType(rs.getString("WRITER_TYPE"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setApplyId(rs.getString("APPLY_ID"));
			obj.setApplyAreaCode(rs.getString("APPLY_AREA_CODE"));
			obj.setApplyNeiborCode(rs.getString("APPLY_NEIBOR_CODE"));
			obj.setApplyAddr(rs.getString("APPLY_ADDR"));
			obj.setApplyZipCode(rs.getString("APPLY_ZIP_CODE"));
			obj.setApplyBirthday(rs.getDate("APPLY_BIRTHDAY"));
			obj.setAttorName(rs.getString("ATTOR_NAME"));
			obj.setAttorId(rs.getString("ATTOR_ID"));
			obj.setAttorNo(rs.getString("ATTOR_NO"));
			obj.setAttorAreaCode(rs.getString("ATTOR_AREA_CODE"));
			obj.setAttorNeiborCode(rs.getString("ATTOR_NEIBOR_CODE"));
			obj.setAttorAddr(rs.getString("ATTOR_ADDR"));
			obj.setAttorZipCode(rs.getString("ATTOR_ZIP_CODE"));
			obj.setAttorTel(rs.getString("ATTOR_TEL"));
			obj.setContactName(rs.getString("CONTACT_NAME"));
			obj.setContactAreaCode(rs.getString("CONTACT_AREA_CODE"));
			obj.setContactNeiborCode(rs.getString("CONTACT_NEIBOR_CODE"));
			obj.setContactAddr(rs.getString("CONTACT_ADDR"));
			obj.setContactZipCode(rs.getString("CONTACT_ZIP_CODE"));
			obj.setContactTel(rs.getString("CONTACT_TEL"));
			obj.setContactCel(rs.getString("CONTACT_CEL"));
			obj.setContactFax(rs.getString("CONTACT_FAX"));
			obj.setContactEmail(rs.getString("CONTACT_EMAIL"));
			obj.setUserid(rs.getString("USERID"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setCreateTime(rs.getDate("CREATE_TIME"));
			obj.setUpdateTime(rs.getDate("UPDATE_TIME"));
			obj.setApplyAddrComb(rs.getString("APPLY_ADDR_COMB"));
			obj.setAttorAddrComb(rs.getString("ATTOR_ADDR_COMB"));
			obj.setContactAddrComb(rs.getString("CONTACT_ADDR_COMB"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setApplyDate(rs.getDate("APPLY_DATE"));
			obj.setOrgName(rs.getString("ORG_NAME"));
			obj.setIformTelixNo(rs.getString("IFORM_TELIX_NO"));
			obj.setCaseType(rs.getString("CASE_TYPE"));
			obj.setApplyEngAddr(rs.getString("APPLY_ENG_ADDR"));
			obj.setTradeApplyType(rs.getString("TRADE_APPLY_TYPE"));
			obj.setTradeOrgName(rs.getString("TRADE_ORG_NAME"));
			obj.setIfClosed(rs.getString("IF_CLOSED"));
		}
		return obj;
	}

}
