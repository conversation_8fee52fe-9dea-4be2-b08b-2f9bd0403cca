package com.kangdainfo.tcfi.view.ajax;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.report.JasperReportMaker;

public class PopBusiItem extends SuperBean {

	public java.util.ArrayList<String[]> doQueryAll() throws Exception
	{
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
    	java.util.List<Cedb1002> objList = null;
    	if( !"".equals(id) ) {
    		if(PrefixConstants.INDEX_TYPE_1.equals(indexType)) {
    			//(1)公司
    			objList = ServiceGetter.getInstance().getPrefixService().getCedb1002ByBanNo(id);
    		}
    		else if(PrefixConstants.INDEX_TYPE_2.equals(indexType)) {
    			//(2)已收文
    			String prefixNo = id.split("_")[0];
    			objList = ServiceGetter.getInstance().getPrefixService().getCedb1002ByPrefixNo(prefixNo);
    		}
    		else if(PrefixConstants.INDEX_TYPE_3.equals(indexType)) {
    			//(3)預查
    			objList = ServiceGetter.getInstance().getPrefixService().getCedb1002ByPrefixNo(id);
    		}
    		else if(PrefixConstants.INDEX_TYPE_5.equals(indexType)) {
    			//(5)有限合夥
    			objList = ServiceGetter.getInstance().getPrefixService().getLmsBusiItemByBanNo(id);
    		}
    	}

		if (null!=objList && !objList.isEmpty()) {
			String[] rows = null;
			for(Cedb1002 dtl : objList){
				rows = new String[3];
				rows[0] = Common.get(dtl.getSeqNo());
				rows[1] = Common.get(dtl.getBusiItemNo());
				rows[2] = Common.get(dtl.getBusiItem());
				arrList.add(rows);
			}
		}
		return arrList;
	}
	
	public File doPrint() throws Exception {
		File report = null;
		try{
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/PRE3003_2R.jasper");
			Map<String, Object> parameters = new HashMap<String,Object>();
			parameters.put("prefixNo", getPrefixNo());
			parameters.put("banNo", getBanNo());
			parameters.put("companyName", getCompanyName());
			java.util.ArrayList<String[]> arrList = doQueryAll();
			if(arrList != null && arrList.size() > 0){
				java.util.ArrayList<Map<String,String>> datas = new java.util.ArrayList<Map<String,String>>();
				Map<String,String> map = null;
				for(String[] dtl:arrList){
					map = new HashMap<String,String>(); 
					map.put("seqNo", dtl[0]);
					map.put("busiItemNo", dtl[1]);
					map.put("busiItemName", dtl[2]);
					datas.add(map);
				}
				report = JasperReportMaker.makeXlsReport(datas, parameters, jasperPath);
			}else{
				setErrorMsg("無營業項目資料！");
			}
			return report;
		}catch( Exception e ) {
	    	e.printStackTrace();
	        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	    }
		return report;
	}

	private String seq;
    private String indexType;
    private String id;
    private String companyName;
    private String prefixNo;
    private String banNo;

	public String getSeq() {return checkGet(seq);}
	public void setSeq(String s) {this.seq = checkSet(s);}
	public String getIndexType() {return checkGet(indexType);}
	public void setIndexType(String s) {this.indexType = checkSet(s);}
	public String getId() {return checkGet(id);}
	public void setId(String s) {this.id = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String prefixNo) {this.prefixNo = checkSet(prefixNo);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String banNo) {this.banNo = checkSet(banNo);}

	public Object doQueryOne() throws Exception {return null;}
	public void doCreate() throws Exception {}
	public void doUpdate() throws Exception {}
	public void doDelete() throws Exception {}

}