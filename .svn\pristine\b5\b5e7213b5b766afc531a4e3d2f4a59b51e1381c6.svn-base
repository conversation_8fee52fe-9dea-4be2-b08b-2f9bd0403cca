package com.kangdainfo.tcfi.view.test;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;

/**
 * 同名比對
 *
 */
public class SameNameCompare extends SuperBean {

	/** 查詢條件 */
	private String q_prefixNo;
	/** 資料欄位 */
	private String prefixNo;
	private String seqNo;
	private String companyName;
	private String sameSeqNo;
	private String samePrefixNo;
	private String sameBanNo;
	private String sameCompanyName;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		ServiceGetter.getInstance().getSameNameCompareService().doCheckCmpySameName(q_prefixNo);

		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<Cedb1004> objList = ServiceGetter.getInstance().getPrefixService().getCedb1004sByPrefixNo(q_prefixNo);			
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[7];
			for(Cedb1004 o : objList) {
				rowArray = new String[7];
				rowArray[0] = Common.get(o.getPrefixNo());
				rowArray[1] = Common.get(o.getSeqNo());
				rowArray[2] = Common.get(o.getCompanyName());
				rowArray[3] = Common.get(o.getSameSeqNo());
				rowArray[4] = Common.get(o.getSamePrefixNo());
				rowArray[5] = Common.get(o.getSameBanNo());
				rowArray[6] = Common.get(o.getSameCompanyName());
				arrList.add(rowArray);
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return this;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getSeqNo() {return checkGet(seqNo);}
	public void setSeqNo(String s) {this.seqNo = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}
	public String getSameSeqNo() {return checkGet(sameSeqNo);}
	public void setSameSeqNo(String s) {this.sameSeqNo = checkSet(s);}
	public String getSamePrefixNo() {return checkGet(samePrefixNo);}
	public void setSamePrefixNo(String s) {this.samePrefixNo = checkSet(s);}
	public String getSameBanNo() {return checkGet(sameBanNo);}
	public void setSameBanNo(String s) {this.sameBanNo = checkSet(s);}
	public String getSameCompanyName() {return checkGet(sameCompanyName);}
	public void setSameCompanyName(String s) {this.sameCompanyName = checkSet(s);}

}