package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;

public class ReceiptNoSetupDAO extends BaseDaoJdbc  implements RowMapper<ReceiptNoSetup> {
	
	private static final String SQL_SELECT_ALL = 
			"SELECT * FROM RECEIPT_NO_SETUP WHERE 1=1";
	public List<ReceiptNoSetup> selectAll() {
		SQLJob sqljob = new SQLJob(SQL_SELECT_ALL);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<ReceiptNoSetup> list = getJdbcTemplate().query(sqljob.getSQL(), this);
    	return list;
	}
	
	private static final String SQL_SELECT_BY_RECEIPT_TYPE = 
			"SELECT * FROM RECEIPT_NO_SETUP WHERE RECEIPT_TYPE = ?";
	public ReceiptNoSetup selectByReceiptType(String receiptType) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_RECEIPT_TYPE);
    	sqljob.addParameter(receiptType);
    	sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<ReceiptNoSetup> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list.isEmpty()? null:list.get(0);
	}
	
	private static final String SQL_INSERT = 
			"INSERT INTO RECEIPT_NO_SETUP (RECEIPT_TYPE, START_RECEIPT_NO, END_RECEIPT_NO, USED_RECEIPT_NO"
			+ " UPDATE_DATE, UPDATE_TIME ) VALUES (?,?,?,?,?,?)";
	public void insert(ReceiptNoSetup vo) { 
		SQLJob sqljob = new SQLJob(SQL_INSERT);
		sqljob.addParameter(vo.getReceiptType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getStartReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getEndReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getUsedReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.DATE);
		sqljob.addParameter(vo.getUpdateUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	private static final String SQL_UPDATE =
			" UPDATE RECEIPT_NO_SETUP SET START_RECEIPT_NO = ?, END_RECEIPT_NO = ?, USED_RECEIPT_NO = ?, UPDATE_DATE = ?, UPDATE_USER = ? "
			+ " WHERE RECEIPT_TYPE = ?";
	public int update(ReceiptNoSetup vo) {
		try {
			SQLJob sqljob = new SQLJob(SQL_UPDATE);
			sqljob.addParameter(vo.getStartReceiptNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getEndReceiptNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getUsedReceiptNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getUpdateDate());
			sqljob.addSqltypes(java.sql.Types.DATE);
			sqljob.addParameter(vo.getUpdateUser());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReceiptType());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		} catch (Exception e) {
			e.printStackTrace();
			return 0;
		}
	}
	
	private static final String SQL_DELETE = 
			"DELETE FROM RECEIPT_NO_SETUP WHERE RECEIPT_TYPE = ?";
	public void delete(String receiptType) {
		SQLJob sqljob = new SQLJob(SQL_DELETE);
		sqljob.addParameter(receiptType);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	@Override
	public ReceiptNoSetup mapRow(ResultSet rs, int idx) throws SQLException {
		ReceiptNoSetup obj = null;
		if (null != rs) {
			obj = new ReceiptNoSetup();
			obj.setReceiptType(rs.getString("RECEIPT_TYPE"));
			obj.setStartReceiptNo(rs.getString("START_RECEIPT_NO"));
			obj.setEndReceiptNo(rs.getString("END_RECEIPT_NO"));
			obj.setUsedReceiptNo(rs.getString("USED_RECEIPT_NO"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
		}
		return obj;
	}
}
