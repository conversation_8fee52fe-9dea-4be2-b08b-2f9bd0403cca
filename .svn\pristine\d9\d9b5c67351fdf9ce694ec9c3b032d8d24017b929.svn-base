package com.kangdainfo.tcfi.service.impl;

import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO;
import com.kangdainfo.tcfi.service.Pre5002Service;


public class Pre5002ServiceImpl implements Pre5002Service {
	PrefixReceiptNoDAO prefixReceiptNoDAO;
	public PrefixReceiptNoDAO getPrefixReceiptNoDAO() {return prefixReceiptNoDAO;}
	public void setPrefixReceiptNoDAO(PrefixReceiptNoDAO prefixReceiptNoDAO) {
		this.prefixReceiptNoDAO = prefixReceiptNoDAO;
	}
	
	public PrefixReceiptNo selectPrefixReceiptNoByReceiptNo(String receiptNo) throws Exception {
		return prefixReceiptNoDAO.selectByReceiptNo(receiptNo);
	}
	
	public PrefixReceiptNo selectPrefixReceiptNoByTelixNo(String telixNo) throws Exception {
		return prefixReceiptNoDAO.selectByTelixNo(telixNo);
	}
	
	public PrefixReceiptNo selectPrefixReceiptNoByPrefixNo(String prefixNo) throws Exception {
		return prefixReceiptNoDAO.selectByPrefixNo(prefixNo);
	}
	
	
}