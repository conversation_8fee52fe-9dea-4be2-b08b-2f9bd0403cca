--DROP TABLE EICM.SYNONYM_WORD;
-- Create table
CREATE TABLE EICM.SYNONYM_WORD (
	ID NUMBER(15) not null,
	WORD VARCHAR2(200),
	SYNONYM_WORD VARCHAR2(200),
	SOURCE VARCHAR2(400),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.SYNONYM_WORD is '同義詞資料檔';
-- Add comments to the columns 
comment on column EICM.SYNONYM_WORD.ID is '主鍵值';
comment on column EICM.SYNONYM_WORD.WORD is '字詞';
comment on column EICM.SYNONYM_WORD.SYNONYM_WORD is '同義詞';
comment on column EICM.SYNONYM_WORD.SOURCE is '詞源依據';
comment on column EICM.SYNONYM_WORD.MOD_ID_NO is '異動人員';
comment on column EICM.SYNONYM_WORD.MOD_DATE is '異動日期';
comment on column EICM.SYNONYM_WORD.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.SYNONYM_WORD
  add constraint PK_SYNONYM_WORD primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_SYNONYM_WORD_ID;
-- Create sequence 
create sequence EICM.SEQ_SYNONYM_WORD_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_SYNONYM_WORD
Before Insert ON EICM.SYNONYM_WORD Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_SYNONYM_WORD_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.SYNONYM_WORD for EICM.SYNONYM_WORD;

--GRANT
grant all on EICM.SYNONYM_WORD to EICM4AP;

