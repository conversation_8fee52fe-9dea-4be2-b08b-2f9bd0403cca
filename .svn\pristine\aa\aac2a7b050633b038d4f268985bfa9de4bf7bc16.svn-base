<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8016_02">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8016" />
</jsp:include>
<%
	obj = (com.kangdainfo.tcfi.view.pre.PRE8016_02)obj.queryOne();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<style type="text/css">
	.td_form {
		font-size: 15px;
		text-align:center;
		font-weight:bold;
		background-color: #dddddd;
		color: #000000;
		border-top: 1px solid silver;
	}
</style>

<script src="./../../js/jquery/highcharts.src.js"></script>
<script src="./../../js/jquery/highcharts-more.src.js"></script>
<!-- 
<script type='text/javascript' src='http://code.jquery.com/jquery-git.js'></script>
<script src="http://code.highcharts.com/highcharts.js"></script>
<script src="http://code.highcharts.com/modules/exporting.js"></script>
 -->
<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){}

function init() {
	window.moveTo(0,0);
	window.resizeTo(screen.width,screen.height*0.96);
}

<% if(!"".equals(obj.getDataTitle()) && obj.getDataTitle() != null){ %>
	$(function () {
		$('#paint').highcharts({
			chart: {
	            type: 'column'
	        },
	        title: {
	            text: <%=Datetime.getYYY()%> + '年度' + '<%=obj.getTitle()%>' + '收文量'
	        },
	        //subtitle: {
	        //    text: '<input class="toolbar_default" followPK="false" type="button" name="doPrint" value="列印">'
	        //},
	        xAxis: {
	            categories: [<%=obj.getDataTitle()%>]
	        },
	        yAxis: {
	            min: 0,
	            title: {
	                align: 'high',
	                offset: 0,
	                text: '案件量',
	                rotation: 0,
					x: 20,
	                y: -27
	            },
	            labels: {
	                overflow: 'justify',
	                style: {
			            fontSize: '16px'
			        },
					x: 10
	            }
	        },
            plotOptions: {
				bar: {
					dataLabels: {enabled: true}
	            },
	            column: {
	                pointPadding: 0.2,
	                borderWidth: 0
	            },
				series: {
					cursor: 'pointer'
					<%if("season".equals(obj.getSearchType())){%>
					//event事件
					,point: {
						events: {
							click: function() {
								if(this.series.name == "總收文量")	form1.searchType.value = "monthAll";
								else if(this.series.name == "未辦結案件量")	form1.searchType.value = "monthRcv";
								else return;
								
								beforeSubmit();
								form1.submit();
							}
						}
					}
					<%}%>
					//值顯示在畫面上
	                ,dataLabels: {
	                    enabled: true,
	                    style: {
	                        fontWeight: 'bold'
	                    },
	                    formatter: function() {
	                        return this.y;
	                    }
	                }
				}   
	        },
	        legend: {
	            layout: 'vertical',
	            align: 'right',
	            verticalAlign: 'top',
	            x: 0,
	            y: 0,
	            floating: true,
	            borderWidth: 1,
	            backgroundColor: '#FFFFFF',
	            shadow: true
	        },
	        credits: {
	            enabled: false
	        },
	      	//滑鼠游標
	        tooltip: {
	            formatter: function() {
	            	form1.season.value = this.x.substring(0,2);
	                return '<b>'+ this.series.name +'</b><br/>'+
	                    this.x +': '+ this.y;
	            }
	        },
	      	//Data
	        series: [
				<%if("season".equals(obj.getSearchType())){%>
		            {name: '總收文量',
		            data: [<%=obj.getDataAllNum()%>],
		            pointWidth: 40,
		            fontSize: '16px'
		        	},{
					name: '未辦結案件量',color:'#FF6347',
		            data: [<%=obj.getDataRcvNum()%>],
		            pointWidth: 40,
		            fontSize: '16px'}
		        <%}else if("monthAll".equals(obj.getSearchType())){%>
	            	{name: '總收文量',
		            data: [<%=obj.getDataAllNum()%>],
		            pointWidth: 15,
		            fontSize: '16px'}
	            <%}else if("monthRcv".equals(obj.getSearchType())){%>
	            	{name: '未辦結案件量',color:'#FF6347',
		            data: [<%=obj.getDataRcvNum()%>],
		            pointWidth: 15,
		            fontSize: '16px'}
	            <%}%>
			]
	    });
	});
<% } %>

function printsetup(){ 
	//if( $.browser.msie ) {
	//	wb.execwb(7,1); 
	//} else {
	//	window.print();
	//}
	$('#doPrint').hide();
	window.print();
	//$('#doPrint').show();
} 

$(document).ready(function() {
	$('#doPrint').click(function(){
		printsetup();
	});
});
</script>
</head>

<body topmargin="0" onLoad="init();showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" action="pre8016_01.jsp" name="form1" method="post" autocomplete="off" >
<table width="100%" cellspacing="0" cellpadding="0" >
	<tr><td>
		<div id="buttonPrint" style="width:90%" align="right">
			<br>
			<br>
			<input class="toolbar_default" style="text-align:right" followPK="false" type="button" id="doPrint" name="doPrint" value="列　印"></input>
			<br>
			<br>
		</div>
		<div id="paint" style="width:90%"></div>
	</td></tr>
	<tr><td>
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>"/>
		<input type="hidden" id="season" name="season" value="<%=obj.getSeason()%>"/>
		<input type="hidden" id="searchType" name="searchType" value="<%=obj.getSearchType()%>"/>
	</td></tr>
</table>
</form>
<!-- 
<object classid="CLSID:8856F961-340A-11D0-A96B-00C04FD705A2" height=0 id=wb name=wb width=0></object> 
 -->
</body>
</html>