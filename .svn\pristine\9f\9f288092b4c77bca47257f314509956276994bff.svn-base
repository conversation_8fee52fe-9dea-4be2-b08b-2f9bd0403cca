package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;

public class OssmFeeMainDao extends BaseDaoJdbc implements RowMapper<OssmFeeMain> {

	private static final String SQL_findByTelixNoAndProcessNo = "SELECT * FROM OSSM_FEE_MAIN WHERE TELIX_NO = ? AND PROCESS_NO = ?";
	public OssmFeeMain findByTelixNoAndProcessNo(String telixNo, String processNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNoAndProcessNo);
		sqljob.addParameter(telixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(processNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<OssmFeeMain> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), this);
		return (null==list || list.isEmpty()?null:list.get(0)); 
	}
	
	private static final String SQL_saveByTelixNo = "UPDATE OSSM_FEE_MAIN SET RETURN_FLAG = 'Y' WHERE TELIX_NO = ?";
	public void saveByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_saveByTelixNo);
		sqljob.addParameter(telixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	
	@Override
	public OssmFeeMain mapRow(ResultSet rs, int idx) throws SQLException {
		OssmFeeMain obj = null;
		if(null!=rs) {
			obj = new OssmFeeMain();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setProcessNo(rs.getString("PROCESS_NO"));
			obj.setReceiptNo(rs.getString("RECEIPT_NO"));
			obj.setAmount(rs.getInt("AMOUNT"));
			obj.setAccountDate(rs.getString("ACCOUNT_DATE"));
			obj.setPayDate(rs.getString("PAY_DATE"));
			obj.setPayTime(rs.getString("PAY_TIME"));
			obj.setPayType(rs.getInt("PAY_TYPE"));
			obj.setReceiptPrintDate(rs.getString("RECEIPT_PRINT_DATE"));
			obj.setReturnFlag(rs.getString("RETURN_FLAG"));
		}
		return obj;
	}

}
