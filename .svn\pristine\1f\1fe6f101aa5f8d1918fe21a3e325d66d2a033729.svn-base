package com.kangdainfo.tcfi.service;

import java.util.List;
import com.kangdainfo.tcfi.model.eicm.bo.TrackLog;

/**
 * 個資軌跡紀錄
 */
public interface TrackLogService {
	
	/** 新增 個資軌跡紀錄檔(異動類)
	 *  @param funcCode [程式代碼]
	 *  @param obj [異動的table bo]
	 */
	public void doUpateTrack(String funcCode, Object obj);
	
	/** 新增 個資軌跡紀錄檔(查詢類)
	 *  @param funcCode [程式代碼]
	 *  @param obj [查詢的table bo]
	 */
	public void doSearchTrack(String funcCode, Object obj);
	
	/** 查詢 個資軌跡紀錄檔 */
	public List<TrackLog> getTrackLog(String funcCode, String type, String idNo, String dateS, String dateE);
	
	/** 新增 個資軌跡紀錄檔(查詢類) Cedb1000申請人資訊 */
	public void insertApplyPerson(String funcCode, String opType, String prefixNo, String applyId, String applyName, String applyTel, String applyAddr);

	/** 新增 個資軌跡紀錄檔(查詢類) Cedb1023收件人資訊 */
	public void insertGetPerson(String funcCode, String opType, String prefixNo, String getName, String getTel, String getAddr);
}
