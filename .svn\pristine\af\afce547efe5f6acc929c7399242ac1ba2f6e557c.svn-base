package com.kangdainfo.tcfi.loader;

/**
 * 案件狀態
 *
 */
public class SystemCode06Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_06";
	private static final String CODE_KIND = "06";//06:案件狀態
	//singleton
	private static SystemCode06Loader instance;
	public SystemCode06Loader() {
		if (SystemCode06Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode06Loader.instance);
		}
		SystemCode06Loader.instance = this;
	}
	public static SystemCode06Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}