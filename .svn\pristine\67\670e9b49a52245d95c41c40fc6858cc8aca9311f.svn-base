package com.kangdainfo.tcfi.lucene.service.impl;

import java.io.IOException;
import java.util.List;

import org.apache.log4j.Logger;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig.OpenMode;
import org.apache.lucene.queryparser.classic.QueryParser;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.lucene.bo.IndexData;
import com.kangdainfo.tcfi.lucene.bo.IndexId;
import com.kangdainfo.tcfi.lucene.dao.IndexDataDao;
import com.kangdainfo.tcfi.lucene.dao.IndexIdDao;
import com.kangdainfo.tcfi.lucene.service.IndexUpdateService;
import com.kangdainfo.tcfi.lucene.util.IndexDataConverter;
import com.kangdainfo.tcfi.lucene.util.LuceneManager;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.IndexLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.IndexLogHDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.lang.CommonStringUtils;

public class IndexUpdateServiceImpl implements IndexUpdateService {
	private Logger logger = Logger.getLogger(this.getClass());

	//檢索鍵值
	private static final String DEFAULT_SEARCH_ID = "ID";

	public synchronized IndexLog doStartUpdate(String wsId) {
		if( "".equals(Common.get(wsId)) ) return null;
		IndexLog indexLog = indexLogDao.getWaitForProcess(wsId);
		if(null!=indexLog) {
			if(logger.isInfoEnabled()) logger.info("[doStartUpdate][WS_ID:"+wsId+"][Param1:"+indexLog.getParam1()+"]");
			if("".equals(Common.get(indexLog.getParam1()))){
				//未帶參數
				indexLog.setStartTime(Datetime.getYYYMMDDHHMISS());
				indexLog.setFinishTime(Datetime.getYYYMMDDHHMISS());
				indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_3);//執行失敗
				indexLog.setRemark(CommonStringUtils.limitLength("執行失敗，未帶參數!",1000));
				indexLog = indexLogDao.update(indexLog);
				return null;
			} else {
				indexLog.setStartTime(Datetime.getYYYMMDDHHMISS());
				indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_1);//執行中
				indexLog.setRemark(CommonStringUtils.limitLength("執行中",1000));
				indexLog = indexLogDao.update(indexLog);
				return indexLog;
			}
		} else {
			return null;
		}
	}

	public IndexLog doUpdateIndex(IndexLog indexLog) {
		if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Start]");

		IndexWriter writer = null;
		try{
			//檢查-索引路徑設定
			String indexPath = getRepositoryPath();
			if(null==indexPath) throw new Exception("系統路徑未設定!");
			//檢查-同音同義字設定
			List<Cedbc058> c058s = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058Codes();
			if(null==c058s || c058s.isEmpty()) throw new Exception("無法讀取CEDBC058");
			//get writer
			writer = LuceneManager.getIndexWriter(indexPath, OpenMode.APPEND);
			if(null==writer) {
				throw new Exception("Index Writer IS NULL");
			}

			Analyzer analyzer = new StandardAnalyzer(LuceneManager.LUCENE_VERSION);
			QueryParser parser = new QueryParser(LuceneManager.LUCENE_VERSION, DEFAULT_SEARCH_ID, analyzer);

			String key = indexLog.getParam1();
			String wsId = indexLog.getWsId();
			
			List<IndexId> indexIds = null;
			
			if( PrefixConstants.JOB_WS10001.equals(wsId) ) {
				String banNo = key;
				//用統編刪除公司索引
				if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+banNo+"]");
				writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + banNo));
				//用統編刪除預查索引
				indexIds = indexIdDao.queryKind2ByBanNo(banNo);
				if(null!=indexIds && !indexIds.isEmpty()) {
					for(IndexId indexId : indexIds) {
						if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+indexId+"]");
						writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + indexId.getId()));
					}
				}
				indexIds = indexIdDao.queryKind3ByBanNo(banNo);
				if(null!=indexIds && !indexIds.isEmpty()) {
					for(IndexId indexId : indexIds) {
						if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+indexId+"]");
						writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + indexId.getId()));
					}
				}
			} else if( PrefixConstants.JOB_WS10002.equals(wsId) ) {
				String prefixNo = key;
				//用預查編號刪除索引
				if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+prefixNo+"]");
				writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + prefixNo));
				indexIds = indexIdDao.queryKind2ByPrefixNo(prefixNo);
				if(null!=indexIds && !indexIds.isEmpty()) {
					for(IndexId indexId : indexIds) {
						if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+indexId+"]");
						writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + indexId.getId()));
					}
				}
			} else if( PrefixConstants.JOB_WS10004.equals(wsId) ) {
				String banNo = key;
				//用統編刪除有限合夥索引
				if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+banNo+"]");
				writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + banNo));
				//用統編刪除預查索引
				indexIds = indexIdDao.queryKind2ByBanNo(banNo);
				if(null!=indexIds && !indexIds.isEmpty()) {
					for(IndexId indexId : indexIds) {
						if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+indexId+"]");
						writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + indexId.getId()));
					}
				}
				indexIds = indexIdDao.queryKind3ByBanNo(banNo);
				if(null!=indexIds && !indexIds.isEmpty()) {
					for(IndexId indexId : indexIds) {
						if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Delete Id:"+indexId+"]");
						writer.deleteDocuments(parser.parse(DEFAULT_SEARCH_ID + ":" + indexId.getId()));
					}
				}
			}
			writer.commit();

			if( PrefixConstants.JOB_WS10001.equals(wsId) ) {
				//新增公司索引
				buildIndex(writer, PrefixConstants.INDEX_TYPE_1, key);
				//用統編刪除預查索引後, 要更新重建預查索引
				indexIds = indexIdDao.queryKind3ByBanNo(key);
				if(null!=indexIds && !indexIds.isEmpty()) {
					String prefixNo = "";
					for(IndexId indexId : indexIds) {
						prefixNo = indexId.getId();
						buildIndex(writer, PrefixConstants.INDEX_TYPE_2, prefixNo);
						buildIndex(writer, PrefixConstants.INDEX_TYPE_3, prefixNo);
						buildIndex(writer, PrefixConstants.INDEX_TYPE_4, prefixNo);
					}
				}
			} else if( PrefixConstants.JOB_WS10002.equals(wsId) ) {
				//新增預查索引
				buildIndex(writer, PrefixConstants.INDEX_TYPE_2, key);
				buildIndex(writer, PrefixConstants.INDEX_TYPE_3, key);
				buildIndex(writer, PrefixConstants.INDEX_TYPE_4, key);
			} else if( PrefixConstants.JOB_WS10004.equals(wsId) ) {
				//新增有限合夥索引
				buildIndex(writer, PrefixConstants.INDEX_TYPE_5, key);
				//用統編刪除預查索引後, 要更新重建預查索引
				indexIds = indexIdDao.queryKind3ByBanNo(key);
				if(null!=indexIds && !indexIds.isEmpty()) {
					String prefixNo = "";
					for(IndexId indexId : indexIds) {
						prefixNo = indexId.getId();
						buildIndex(writer, PrefixConstants.INDEX_TYPE_2, prefixNo);
						buildIndex(writer, PrefixConstants.INDEX_TYPE_3, prefixNo);
						buildIndex(writer, PrefixConstants.INDEX_TYPE_4, prefixNo);
					}
				}
			}
			writer.commit();

			//write indexLog
			indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_2);//執行成功
			indexLog.setRemark(CommonStringUtils.limitLength(CommonStringUtils.append("Success, Update:"+key),1000));
			if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Success]");
		} catch (Exception e) {
			//e.printStackTrace();
			indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_3);//執行失敗
			indexLog.setRemark(CommonStringUtils.limitLength(CommonStringUtils.append("Failure, errorMsg:", e.getMessage() ),1000));
			if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][Failure][Exception:"+ e.getMessage() + "]");
		} finally {
			//close writer
			try {
				if(writer != null) writer.close();
			} catch (IOException e) {
				//e.printStackTrace();
			}
		}

		if(logger.isInfoEnabled()) logger.info("[doUpdateIndex][End]");
		return indexLog;
	}

	public void doEndUpdate(IndexLog indexLog) {
		indexLog.setFinishTime(Datetime.getYYYMMDDHHMISS());
		indexLogDao.update(indexLog);
		indexLogDao.updateSameIndexLog(indexLog);
	}

	public void doRetryError() {
		if(logger.isInfoEnabled()) logger.info("[doRetryError][START]");
		indexLogDao.updateRetryError();
		if(logger.isInfoEnabled()) logger.info("[doRetryError][END]");
	}

	public void doBackupIndexLog() {
		if(logger.isInfoEnabled()) logger.info("[doBackupIndexLog][START]");
		indexLogHDao.backupIndexLog();
		if(logger.isInfoEnabled()) logger.info("[doBackupIndexLog][END]");
	}

	private String getRepositoryPath() {
		SystemCode s = null;
		if(System.getProperty("os.name").toLowerCase().indexOf("win") >= 0)
			s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathWindows");
		else 
			s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathCentOS");
		
		String result = null;
		if(null!=s) {
			result = s.getCodeParam2();
		}
		return result;
	}
	
	private void buildIndex(IndexWriter writer, String kind, String key) throws Exception {
		List<IndexData> objList = indexDataDao.query(kind, key);
		if (objList != null && objList.size() > 0) {
			Document doc = null;
			String id;
			for(IndexData o : objList) {
				id = o.getId();
				if (id != null && !"".equals(id)){
					doc = IndexDataConverter.convertMapToDoc(o);
					if(null!=doc) {
						writer.addDocument(doc);
					}
				}
				id = null;
				doc = null;
			}
		}
	}

	private IndexDataDao indexDataDao;
	private SystemCodeDao systemCodeDao;
	private IndexLogDao indexLogDao;
	private IndexLogHDao indexLogHDao;
	private IndexIdDao indexIdDao;

	public IndexDataDao getIndexDataDao() {return indexDataDao;}
	public void setIndexDataDao(IndexDataDao dao) {this.indexDataDao = dao;}

	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}

	public IndexLogDao getIndexLogDao() {return indexLogDao;}
	public void setIndexLogDao(IndexLogDao dao) {this.indexLogDao = dao;}

	public IndexIdDao getIndexIdDao() {return indexIdDao;}
	public void setIndexIdDao(IndexIdDao dao) {this.indexIdDao = dao;}

	public IndexLogHDao getIndexLogHDao() {return indexLogHDao;}
	public void setIndexLogHDao(IndexLogHDao dao) {this.indexLogHDao = dao;}

}