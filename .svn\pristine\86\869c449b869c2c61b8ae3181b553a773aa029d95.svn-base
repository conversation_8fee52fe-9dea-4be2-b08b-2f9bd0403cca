package com.kangdainfo.tcfi.scheduling;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashSet;
import java.util.Set;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.common.util.CommonCompressUtils;
import com.kangdainfo.util.io.CommonFileUtils;

/**
 * 排程
 * 壓縮系統紀錄檔
 */
public class CompressLogFileQuartzJobBean extends BaseQuartzJobBean {

	private static final String LOG_PATH = "/opt/temp/prefix";
	
	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		if(log.isInfoEnabled()) log.info("[START]"+getDateTime());
		File tempFolder = new File(LOG_PATH);
		if(null!=tempFolder && tempFolder.isDirectory() && tempFolder.canRead()) {
			//1.move to date folder
			move(tempFolder);
			//2.compress
			compress(tempFolder);
			//3.clean
			clean(tempFolder);
		}
		if(log.isInfoEnabled()) log.info("[END]"+getDateTime());
	}

	private void clean(File root) {
		String lastWeekDate = getLastWeekDate();
		
		Set<String> fileNames = new HashSet<String>();
		Set<String> folderNames = new HashSet<String>();
		if(null!=root && root.isDirectory()) {
			for(File f : root.listFiles()) {
				if( f.isFile() ) {
					fileNames.add(f.getName().substring(0,f.getName().indexOf(".")));
				} else if( f.isDirectory() ) {
					folderNames.add(f.getAbsolutePath());
				}
			}
		}

		for(String folderName : folderNames) {
			if( lastWeekDate.compareTo(folderName) > 0 ) {
				for(String fileName : fileNames) {
					if( folderName.indexOf(fileName) > -1 ) {
						try {
							if(log.isInfoEnabled()) log.info("[DELETE]folder:"+folderName);
							CommonFileUtils.deleteDirectory(new File(folderName));
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
			}
		}
	}

	private long getLastDateTime() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -1);
		return cal.getTimeInMillis();
	}

	private void compress(File root) {
		for(File f : root.listFiles()) {
			if(f.isDirectory()) {
				CommonCompressUtils.targzip(f);
			}
		}
	}

	private void move(File root) {
		long lastDateTime = getLastDateTime();

		if(root.isDirectory()) {
			for(File f : root.listFiles()) {
				if( f.isFile() && f.getName().indexOf(".tar.gz") < 0 && f.lastModified() < lastDateTime ) {
					//1.不處理目錄
					//2.超過一天的才處理
					move(f);
				}
			}
		} else if( root.isFile() ) {
			String yyyyMMdd = getYYYYMMDD(root.lastModified());
			
			File parent = root.getParentFile();
			File dateFolder = new File(parent, yyyyMMdd);
			if( !dateFolder.exists() ) dateFolder.mkdir();

			root.renameTo(new File(dateFolder, root.getName()));
		}
	}

	private static DateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
	private String getYYYYMMDD(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		return yyyyMMdd.format(cal.getTime());
	}

	private String getLastWeekDate() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -7);
		return yyyyMMdd.format(cal.getTime());
	}

}