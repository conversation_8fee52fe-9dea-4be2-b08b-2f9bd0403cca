package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdUnitCode;

public class CsmdUnitCodeDao extends BaseDaoJdbc implements RowMapper<CsmdUnitCode> {

	private static final String SQL_defaultOrder = "order by UNIT_CODE";
	
	private static final String SQL_findAll = "select * from icms.CSMD_UNIT_CODE where enable = 'Y'";
	public List<CsmdUnitCode> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdUnitCode mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdUnitCode obj = null;
		if(null!=rs) {
			obj = new CsmdUnitCode();
			obj.setUnitCode(rs.getString("UNIT_CODE"));
			obj.setAreaCode(rs.getString("AREA_CODE"));
			obj.setOid(rs.getString("OID"));
			obj.setSimpleCode(rs.getString("SIMPLE_CODE"));
			obj.setUnitName(rs.getString("UNIT_NAME"));
			obj.setZipCode(rs.getString("ZIP_CODE"));
			obj.setUnitAdd(rs.getString("UNIT_ADD"));
			obj.setUnitTelNo(rs.getString("UNIT_TEL_NO"));
			obj.setUnitFaxNo(rs.getString("UNIT_FAX_NO"));
			obj.setDispatchWd(rs.getString("DISPATCH_WD"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
		}
		return obj;
	}

}