package com.kangdainfo.util.collections;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.ListUtils;

/**
 * Provides utility methods and decorators for List instances.
 *
 */
public class CommonListUtils extends ListUtils
{
	/**
	 * 反轉List
	 * @param list
	 * @return
	 */
	public static List<?> reverse(List<Object> list)
	{
		List<Object> reversedList = new ArrayList<Object>();
		for(int i=list.size()-1;i>=0;i--)
		{
			reversedList.add(list.get(i));
		}
		return reversedList;
	}

}
