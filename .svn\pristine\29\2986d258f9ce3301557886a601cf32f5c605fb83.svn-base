package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO;
import com.kangdainfo.tcfi.service.Pre4022Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.view.pre.PRE4022;

/**
 * 公司名稱預查規費日報表 113/03/27
 */
public class Pre4022ServiceImpl implements Pre4022Service {

	PrefixReceiptNoDAO prefixReceiptNoDAO;
	Cedb1000Dao cedb1000Dao;
	Cedb1023Dao cedb1023Dao;

	public PrefixReceiptNoDAO getPrefixReceiptNoDAO() {
		return prefixReceiptNoDAO;
	}

	public void setPrefixReceiptNoDAO(PrefixReceiptNoDAO prefixReceiptNoDAO) {
		this.prefixReceiptNoDAO = prefixReceiptNoDAO;
	}

	public Cedb1000Dao getCedb1000Dao() {
		return cedb1000Dao;
	}

	public void setCedb1000Dao(Cedb1000Dao cedb1000Dao) {
		this.cedb1000Dao = cedb1000Dao;
	}
	
	public Cedb1023Dao getCedb1023Dao() {
		return cedb1023Dao;
	}

	public void setCedb1023Dao(Cedb1023Dao cedb1023Dao) {
		this.cedb1023Dao = cedb1023Dao;
	}

	/**
	 * 產製公司名稱預查規費日報表前的資料檢查 - 根據繳款日期
	 * 
	 * @param dateStart
	 *            - String
	 * @param dateEnd
	 *            - String
	 * @param timeStart
	 *            - String
	 * @param timeEnd
	 *            - String
	 * @return String
	 */
	@Override
	public List<PrefixReceiptNo> selectPrefixReceiptNoByTimeInterval(String dateStart, String dateEnd)
			throws Exception {
		return prefixReceiptNoDAO.selectByPayDateInterval(dateStart, dateEnd);
	}

	/**
	 * 產製公司名稱預查規費日報表前的資料檢查 - 根據收文日期
	 * 
	 * @param dateStart
	 *            - String
	 * @param dateEnd
	 *            - String
	 * @param timeStart
	 *            - String
	 * @param timeEnd
	 *            - String
	 * @return String
	 */
	@Override
	public List<Cedb1000> selectCEDB1000ByTimeInterval(String dateStart, String dateEnd, String timeStart,
			String timeEnd) throws Exception {
		return cedb1000Dao.selectCedb1000ByReceiveDateAndTimeInterval(dateStart, dateEnd, timeStart, timeEnd);
	}

	/**
	 * 產製報表用報表日期
	 * 
	 * @return String
	 */
	@Override
	public String getPrintDate() {
		String curr = Datetime.getCurrentDate("yyyy/MM/dd");
		String[] parts = curr.split("/");
		int year = Integer.parseInt(parts[0]);
		int month = Integer.parseInt(parts[1]);
		int day = Integer.parseInt(parts[2]);

		StringBuffer result = new StringBuffer("");
		result.append(year - 1911).append("年");
		result.append(month).append("月");
		result.append(day).append("日");
		return result.toString();
	}

	/**
	 * 產製報表用報表時間
	 * 
	 * @return String
	 */
	@Override
	public String getPrintTime() {
		// 留著日後評估看是否需要用
		return null;
	}

	/**
	 * 根據勾選收文日期範圍取得相應資料
	 * 
	 * @param dateStart
	 * @param dateEnd
	 * @param timeStart
	 * @param timeEnd
	 * @param idNo
	 * @param changeTypeCode
	 * @param payTypeCode
	 * @return List<PRE4022>
	 */
	@Override
	public List<PRE4022> selectPre4022ByTypeEqualReceive(String dateStart, String dateEnd, String timeStart,
			String timeEnd, String idNo, String changeTypeCode, String payTypeCode) {
		List<String> prefixNos = new ArrayList<>();
		List<PrefixReceiptNo> receiptList = new ArrayList<PrefixReceiptNo>();
		Map<String, Integer> prefixNoToAmountMap = new HashMap<>();
		List<PRE4022> pre4022s = new ArrayList<>();
		
//		System.out.println("收文input 承辦人: " + idNo + " 預查種類: " + changeTypeCode + " 付款方式: " + payTypeCode);

		try {
			List<Cedb1000> cedb1000s = selectCEDB1000ByTimeInterval(dateStart, dateEnd, timeStart, timeEnd);
			
//			System.out.println("根據時間得到初步收文確認數量:" + cedb1000s.size());
//			for (Cedb1000 c: cedb1000s) {
//				System.out.println("收文預查編號:" + c.getPrefixNo() + ", idNo: " + c.getIdNo());
//			}

			// 根據承辦人過濾
			List<Cedb1000> filteredCedb1000s = fliterCedb1000sByIdNo(idNo, cedb1000s);
			
//			for(Cedb1000 c : filteredCedb1000s) {
//				System.out.println("承辦人: " + c.getIdNo() + ", PREFIX_NO: " + c.getPrefixNo());
//			}
			
			// 取得預查編號列表
			if(!filteredCedb1000s.isEmpty() || filteredCedb1000s != null) {
				for (Cedb1000 cedb1000 : filteredCedb1000s) {
				    prefixNos.add(cedb1000.getPrefixNo());
				}
			}
			
			// 取得變更類別
			List<Cedb1023> cedb1023s = selectCedb1023ByChangeType(changeTypeCode, prefixNos);
			
//			if (cedb1023s.size() > 0) {
//				System.out.println("1023預查種類數量:" + cedb1023s.size());
//				System.out.println("1023預查種類:" + cedb1023s.get(0).getChangeType());
//				for (Cedb1023 c : cedb1023s) {
//					System.out.println("1023預查編號:" + c.getPrefixNo());
//				}
//			} else {
//				System.out.println("沒有1023資料");
//			}
			
			// 找到對應付款收據資料
			if (!"".equals(payTypeCode)) {
				receiptList = selectReceiptNoByPayTypeAndPrefixNos(payTypeCode, prefixNos);
			} else {
				receiptList = Collections.emptyList();
			}
			
//			System.out.println("收文>>收據數量:" + receiptList.size());
//			for(PrefixReceiptNo r : receiptList) {
//				System.out.println("收文>>收據編號:" + r.getPrefixNo() + ", 付款方式: " + r.getPayType());
//			}
			
			for (PrefixReceiptNo receipt : receiptList) {
			    prefixNoToAmountMap.put(receipt.getPrefixNo(), receipt.getAmount());
			}
			
			// 繳款金額取得
			pre4022s = outputWithCedb1000sAndReceipts(filteredCedb1000s, prefixNoToAmountMap);
			
			// 變更類型取得
			pre4022s = outputWithCedb1023s(pre4022s, cedb1023s);

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return pre4022s;
	}

	/**
	 * 根據勾選繳款日期範圍取得相應資料
	 * 
	 * @param dateStart
	 * @param dateEnd
	 * @param idNo
	 * @param changeTypeCode
	 * @param payType
	 * @return List<PRE4022>
	 */
	@Override
	public List<PRE4022> selectPre4022ByTypeEqualPay(String dateStart, String dateEnd, String idNo,
			String changeTypeCode, String payTypeCode) {
		List<PrefixReceiptNo> filteredReceiptList = new ArrayList<>();
		Map<String, Integer> prefixNoToAmountMap = new HashMap<>();
		List<PRE4022> pre4022s = new ArrayList<>();
		
//		System.out.println("收文input 承辦人: " + idNo + " 預查種類: " + changeTypeCode + " 付款方式: " + payTypeCode);
		
		try {
			// 先篩選付費時間
			List<PrefixReceiptNo> receipts = selectPrefixReceiptNoByTimeInterval(dateStart, dateEnd);
			
//			System.out.println("根據時間得到初步收據數量:" + receipts.size());
			
			// 先付費方式過濾
			if (!"".equals(payTypeCode)) {
				filteredReceiptList = selectReceiptNoByPayTypeAndPrefixNos(payTypeCode, toPrefixNos(receipts));
			} else {
				filteredReceiptList = Collections.emptyList();
			}
			
//			System.out.println("根據付款方式過濾後收據數量:" + filteredReceiptList.size());
//			for(PrefixReceiptNo r : filteredReceiptList) {
//				System.out.println("繳款>>收據編號:" + r.getPrefixNo() + ", 付款方式: " + r.getPayType());
//			}
			
			for (PrefixReceiptNo receipt : filteredReceiptList) {
			    prefixNoToAmountMap.put(receipt.getPrefixNo(), receipt.getAmount());
			}
			
			List<String> prefixNos = toPrefixNos(filteredReceiptList);
			
			List<Cedb1000> cedb1000s = cedb1000Dao.selectCedb1000ByPrefixNos(prefixNos);
			
//			for(Cedb1000 c : cedb1000s) {
//				System.out.println("是否收文確認: " + c.getRcvCheck() + ", PREFIX_NO: " + c.getPrefixNo() + ", idNo: " + c.getIdNo());
//			}
			
//			System.out.println("繳款>>主表數量:" + cedb1000s.size());
			
			// 取得變更類別
			List<Cedb1023> cedb1023s = selectCedb1023ByChangeType(changeTypeCode, prefixNos);
			
//			if (cedb1023s.size() > 0) {
//				System.out.println("1023預查種類數量:" + cedb1023s.size());
//				for (Cedb1023 c : cedb1023s) {
//					System.out.println("1023預查編號:" + c.getPrefixNo());
//				}
//			} else {
//				System.out.println("沒有1023資料");
//			}
			
			// 根據承辦人過濾
			List<Cedb1000> filteredCedb1000s = fliterCedb1000sByIdNo(idNo, cedb1000s);
			
//			for(Cedb1000 c : filteredCedb1000s) {
//				System.out.println("承辦人: " + c.getIdNo() + ", PREFIX_NO: " + c.getPrefixNo());
//			}
		    
			// 繳款金額取得
		    pre4022s = outputWithCedb1000sAndReceipts(filteredCedb1000s, prefixNoToAmountMap);
		    
		    // 變更類型取得
		    pre4022s = outputWithCedb1023s(pre4022s, cedb1023s);
		    
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return pre4022s;
	}

	/**
	 * 根據主表提供的預查編號列表來查詢收據表裡面對應付款方式的資料
	 * 
	 * @param prefixNo
	 * @return List<PrefixReceiptNo>
	 */
	private List<PrefixReceiptNo> selectReceiptNoByPayTypeAndPrefixNos(String payType, List<String> prefixNos) {
		if (prefixNos.isEmpty() || "".equals(payType)) {
			return new ArrayList<PrefixReceiptNo>();
		} else {
			List<PrefixReceiptNo> receiptList = new ArrayList<PrefixReceiptNo>();
			if ("A".equals(payType)) {
				receiptList = prefixReceiptNoDAO.selectByPrefixNos(prefixNos);
			} else {
				receiptList = prefixReceiptNoDAO.selectByPayTypeAndPrefixNos(payType, prefixNos);
			}

			return receiptList;
		}
	}
	
	/**
	 * 根據預查變更類型，執行對應sql
	 * @param changeTypeCode
	 * @param prefixNos
	 * @return List<Cedb1023>
	 */
	private List<Cedb1023> selectCedb1023ByChangeType(String changeTypeCode, List<String> prefixNos) {
		if (prefixNos.isEmpty()) {
			return new ArrayList<Cedb1023>();
		}else {
			List<Cedb1023> cedb1023s = new ArrayList<Cedb1023>(); 
			if ("".equals(changeTypeCode) || changeTypeCode == null) {
				System.out.println("無預查種類");
				cedb1023s = cedb1023Dao.selectByPrefixNos(prefixNos);
			} else {
				System.out.println("有預查種類，預查種類是: " + changeTypeCode);
				cedb1023s = cedb1023Dao.selectByChangeTypeAndPrefixNos(changeTypeCode, prefixNos);
			}
			return cedb1023s;
		}
	}

	/**
	 * 根據收據資料轉換為對應預查編號列表
	 * @param receipts
	 * @return List<String>
	 */
	private List<String> toPrefixNos(List<PrefixReceiptNo> receipts) {
		if (receipts.isEmpty() || receipts == null) {
			return new ArrayList<String>();
		} else {
			List<String> prefxNos = new ArrayList<String>();
			for(PrefixReceiptNo receipt: receipts) {
				prefxNos.add(receipt.getPrefixNo());
			}
			return prefxNos;
		}
	}
	
	/**
	 * 根據承辦人進行過濾
	 * @param idNo
	 * @param changeTypeCode
	 * @param cedb1000s
	 * @return List<Cedb1000>
	 */
	private List<Cedb1000> fliterCedb1000sByIdNo(String idNo, List<Cedb1000> cedb1000s) {
		List<Cedb1000> filteredCedb1000s = new ArrayList<>();
		
		if (!"".equals(idNo)) { // 承辦人有值
		    for (Cedb1000 cedb1000 : cedb1000s) {
		        if (idNo.equals(cedb1000.getIdNo())) {
		            filteredCedb1000s.add(cedb1000);
		        }
		    }
		} else {
			filteredCedb1000s = cedb1000s;
		}
		
		return filteredCedb1000s;
	}
	
	/**
	 * 整合匯出
	 * @param cedb1000s
	 * @param receipts
	 * @param changeTypeCode
	 * @param prefixNoToAmountMap
	 * @return List<PRE4022>
	 */
	private List<PRE4022> outputWithCedb1000sAndReceipts(List<Cedb1000> cedb1000s, Map<String, Integer> prefixNoToAmountMap) {
		List<PRE4022> pre4022s = new ArrayList<PRE4022>();
		
		for (Cedb1000 cedb1000 : cedb1000s) {
					    
			if (prefixNoToAmountMap.containsKey(Common.get(cedb1000.getPrefixNo()))) {
                PRE4022 pre4022 = new PRE4022();
                pre4022.setPrefixNo(cedb1000.getPrefixNo());
                pre4022.setAmount(prefixNoToAmountMap.get(Common.get(cedb1000.getPrefixNo())));
                
                if (Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS")) {
                    pre4022.setApplyway("一站式");
                } else if (Common.get(cedb1000.getTelixNo()).startsWith("Z")) {
                    pre4022.setApplyway("一維條碼");
                }
                
                pre4022s.add(pre4022);
            }
		}
		
		return pre4022s;
	}
	
	private List<PRE4022> outputWithCedb1023s(List<PRE4022> pre4022s, List<Cedb1023> cedb1023s) {
		if (cedb1023s == null || cedb1023s.isEmpty()) {
			List<PRE4022> result = new ArrayList<PRE4022>();
			result.add(new PRE4022());
	        return result; // 回傳至少一個資料，確保JASPER REPORT產出文件內容
	    }
	    String changeType = "";
	    boolean matchFound = false; // 新增標誌用於檢查是否找到匹配
	    
	    Iterator<PRE4022> iterator = pre4022s.iterator();
	    while (iterator.hasNext()) {
	        PRE4022 pre4022 = iterator.next();
	        boolean match = false;
	        
	        for (Cedb1023 cedb1023 : cedb1023s) {
	            if (Common.get(cedb1023.getPrefixNo()).equals(Common.get(pre4022.getPrefixNo()))) {
	                match = true;
	                changeType = cedb1023.getChangeType();
	                matchFound = true;
	                break;
	            }
	        }
	        if (match) {
	            if (PrefixConstants.CHANGE_TYPE_0.equals(changeType)) {
	                pre4022.setChangeType("設立");
	            } else if (PrefixConstants.CHANGE_TYPE_1.equals(changeType)) {
	                pre4022.setChangeType("名稱變更");
	            } else if (PrefixConstants.CHANGE_TYPE_2.equals(changeType)) {
	                pre4022.setChangeType("所營變更");
	            } else if (PrefixConstants.CHANGE_TYPE_3.equals(changeType)) {
	                pre4022.setChangeType("名稱及所營變更");
	            }
	        } else {
	            iterator.remove(); // 使用Iterator的remove方法刪除不滿足條件的元素，避免操作報ConcurrentModificationException
	        }
	    }
	    
	    if (!matchFound) { // 沒比對到一樣回傳空數據
	    	List<PRE4022> result = new ArrayList<PRE4022>();
			result.add(new PRE4022());
	        return result;
	    }
	    
	    return pre4022s;
	}
}
