<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
String approveResult = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q2")));

try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(prefixNo)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDB1001 WHERE PREFIX_NO=? AND APPROVE_RESULT LIKE ?");
		sqljob.addParameter(prefixNo);
		sqljob.addLikeParameter(approveResult);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>