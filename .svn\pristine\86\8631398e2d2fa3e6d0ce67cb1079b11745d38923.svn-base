package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司免繳註記(GENERALITY_BUSITEM)
 *
 */
public class GeneralityBusitem extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 公司統編 */
	private String banNo;
	/** 線上申辦電子流水號 */
	private String telixNo;
	/** 預查編號 */
	private String prefixNo;
	/** 公司系統是否免費過 */
	private String cmpyMark;

	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String telixNo) {this.telixNo = telixNo;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getCmpyMark() {return cmpyMark;}
	public void setCmpyMark(String cmpyMark) {this.cmpyMark = cmpyMark;}

}