<%@page import="com.kangdainfo.tcfi.model.eicm.bo.Cedb1000"%>
<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDB1000 WHERE BAN_NO=?");
		sqljob.addParameter(q);
		List datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(sqljob, new BeanPropertyRowMapper(Cedb1000.class));
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas.get(0)));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>