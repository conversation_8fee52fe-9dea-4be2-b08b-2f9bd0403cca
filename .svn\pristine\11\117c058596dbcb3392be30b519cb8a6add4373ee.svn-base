<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3005">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3005" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState()))
	obj = (com.kangdainfo.tcfi.view.pre.PRE3005)obj.printDetail();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_dateStart,"收件日期起");
	alertStr += checkEmpty(form1.q_dateEnd,"收件日期迄");
	if(form1.q_dateStart.value != "")	alertStr += checkDate(form1.q_dateStart,"收件日期起") ;
	if(form1.q_dateEnd.value != "")	alertStr += checkDate(form1.q_dateEnd,"收件日期迄") ;
	
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function init(){
	if ( form1.state.value == "queryAll" )
		document.getElementById("formContainer1").style.display = '';
	else
	  document.getElementById("formContainer1").style.display = 'none';
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;
			case "doClear":
				form1.q_dateStart.value = "";
				form1.q_dateEnd.value = "";
				break;	
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>
<body topmargin="5" onload="showErrorMsg('<%=obj.getErrorMsg()%>');init();" >
<form id="form1" name="form1" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3005'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0" border="0">

<!-- TOOLBAR AREA -->
<tr><td>
	<table width="100%">
		<tr>
			<td style="text-align:left">
				<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)">&nbsp;
				<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)">
				<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
				<jsp:include page="../../home/<USER>" >
					<jsp:param name="btnInsert" value="N" />
					<jsp:param name="btnQueryAll" value="N" />
					<jsp:param name="btnUpdate" value="N" />
					<jsp:param name="btnDelete" value="N" />
					<jsp:param name="btnClear" value="N" />
					<jsp:param name="btnConfirm" value="N" />
					<jsp:param name="btnListHidden" value="N" />
					<jsp:param name="btnPreview" value="N" />
					<jsp:param name="btnCancel" value="N" />
					<jsp:param name="btnListPrint" value="N" />
				</jsp:include>
    		</td>
    	</tr>
    </table>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td nowrap class="bg">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="20%">查詢日期：</td>
			<td class="td_form_white"> 
				(起)<%=View.getPopCalendar("field_Q","q_dateStart",obj.getQ_dateStart()) %>~(迄)<%=View.getPopCalendar("field_Q","q_dateEnd",obj.getQ_dateEnd()) %>
			</td>
		</tr>
	</table>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td nowrap class="bg">
		<table id="formContainer1" class="table_form" width="100%" height="100%" style="height:auto">  
			<tr>
				<td class="td_form" width="20%">撤件案件數：</td>
				<td class="td_form_white"> 
					<%=obj.getQueryResult1()%>
				</td>
			</tr>
			<tr>
				<td class="td_form" width="20%">檢還案件數：</td>
				<td class="td_form_white"> 
					<%=obj.getQueryResult3()%>
				</td>
			</tr>
            <tr>
				<td class="td_form">撤回退費案件數：</td>
				<td class="td_form_white">
					<%=obj.getQueryResult2()%>
				</td>
			</tr>
		</table>
</td></tr> 

</table>	
</form>
</body>
</html>
