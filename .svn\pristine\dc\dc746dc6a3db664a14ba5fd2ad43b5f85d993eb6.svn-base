--DROP TABLE EICM.FLOW_LOG;
-- Create table
CREATE TABLE EICM.FLOW_LOG (
	ID NUMBER(15) not null,
	PREFIX_NO NVARCHAR2(18),
	ID_NO NVARCHAR2(20),
	PROCESS_DATE NVARCHAR2(14),
	PROCESS_TIME NVARCHAR2(14),
	PROCESS_STATUS NVARCHAR2(4),
	WORK_DAY NUMBER(5,2),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.FLOW_LOG is '案件歷程檔';
-- Add comments to the columns 
comment on column EICM.FLOW_LOG.ID is '主鍵值';
comment on column EICM.FLOW_LOG.PREFIX_NO is '預查編號';
comment on column EICM.FLOW_LOG.ID_NO is '承辦人員帳號';
comment on column EICM.FLOW_LOG.PROCESS_DATE is '處理日期';
comment on column EICM.FLOW_LOG.PROCESS_TIME is '處理時間';
comment on column EICM.FLOW_LOG.PROCESS_STATUS is '流程代碼';
comment on column EICM.FLOW_LOG.WORK_DAY is '工作日數';
comment on column EICM.FLOW_LOG.MOD_ID_NO is '異動人員';
comment on column EICM.FLOW_LOG.MOD_DATE is '異動日期';
comment on column EICM.FLOW_LOG.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.FLOW_LOG
  add constraint PK_FLOW_LOG primary key (ID)
  using index ;

-- Drop sequence
DROP sequence EICM.SEQ_FLOW_LOG_ID;
-- Create sequence 
create sequence EICM.SEQ_FLOW_LOG_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_FLOW_LOG
Before Insert ON EICM.FLOW_LOG Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_FLOW_LOG_ID.NextVal into :nu.id From Dual;
End;
   
-- SYNONYM 
create or replace synonym EICM4AP.FLOW_LOG for EICM.FLOW_LOG;
create or replace synonym EICM4CMPY.FLOW_LOG for EICM.FLOW_LOG;
create or replace synonym EICM4PREFIX.FLOW_LOG for EICM.FLOW_LOG;

-- Grant/Revoke object privileges 
grant all on EICM.FLOW_LOG to EICM4AP;
grant all on EICM.FLOW_LOG to EICM4CMPY;
grant all on EICM.FLOW_LOG to EICM4PREFIX;

