<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="indexDataDao" class="com.kangdainfo.tcfi.lucene.dao.IndexDataDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="indexIdDao" class="com.kangdainfo.tcfi.lucene.dao.IndexIdDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>

	<bean id="indexSearchService" class="com.kangdainfo.tcfi.lucene.service.impl.IndexSearchServiceImpl">
		<property name="indexDataDao" ref="indexDataDao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
		<property name="searchLogDao" ref="searchLogDao" />
	</bean>

	<bean id="indexCreateService" class="com.kangdainfo.tcfi.lucene.service.impl.IndexCreateServiceImpl">
		<property name="indexDataDao" ref="indexDataDao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
		<property name="indexLogDao" ref="indexLogDao" />
	</bean>

	<bean id="indexUpdateService" class="com.kangdainfo.tcfi.lucene.service.impl.IndexUpdateServiceImpl">
		<property name="indexDataDao" ref="indexDataDao" />
		<property name="indexIdDao" ref="indexIdDao" />
		<property name="systemCodeDao" ref="systemCodeDao" />
		<property name="indexLogDao" ref="indexLogDao" />
		<property name="indexLogHDao" ref="indexLogHDao" />
	</bean>

</beans>