package com.kangdainfo.common.util;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.kangdainfo.util.pdf.CommonPdfUtils;

public abstract class DefaultBean {
	//logger
	protected final Log logger = LogFactory.getLog(getClass());

	/**
	 * 取得系統日
	 * @return Date
	 */
	public Date getDate() {
		return new Date();
	}

	/**
  	 * <pre>
   	 * 目的：撰寫JavaBean get方法時所需套用的函數
   	 * 參數：資料字串
     * 傳回：檢查資料為null,是則傳回空字串
     * </pre>
	 * @param s
	 * @return String
	 */
	public static String get(String s){	return (null==s)?"":s.trim();	}
  	/**
  	 * <br>
  	 * <br>目的：撰寫JavaBean set方法時所需套用的函數
  	 * <br>參數：資料字串
  	 * <br>傳回：將資料前後空白去除
  	*/
	public static String set(String s){return (null==s)?"":s.trim();}

	public Boolean checkGet(Boolean b){	return (null==b)?false:b;	}
	public Boolean checkSet(Boolean b){	return (null==b)?false:b;	}

	public Long checkGet(Long l){	return (null==l)?0l:l;	}
	public Long checkSet(Long l){	return (null==l)?0l:l;	}

	public Integer checkGet(Integer i){	return (null==i)?0:i;	}
	public Integer checkSet(Integer i){	return (null==i)?0:i;	}

	public Double checkGet(Double d){	return (null==d)?0d:d;	}
	public Double checkSet(Double d){	return (null==d)?0d:d;	}

	public Float checkGet(Float f){	return (null==f)?0f:f;	}
	public Float checkSet(Float f){	return (null==f)?0f:f;	}

	public Date checkGet(Date d){	return d;	}
	public Date checkSet(Date d){	return d;	}

	public BigDecimal checkGet(BigDecimal bd){	return bd;	}
	public BigDecimal checkSet(BigDecimal bd){	return bd;	}

  	/**
  	 * <br>
   	 * <br>目的：撰寫JavaBean chect get方法時所需套用的函數
   	 * <br>參數：資料字串
     * <br>傳回：檢查資料為null,是則傳回空字串
  	*/
	public String checkGet(String s){
		return Common.checkGet(s);
	}
  	/**
  	 * <br>
  	 * <br>目的：撰寫JavaBean check set方法時所需套用的函數
  	 * <br>參數：資料字串
  	 * <br>傳回：將資料前後空白去除
  	*/
	public String checkSet(String s){
		return Common.checkSet(s);
	}		
	
	public void outputFile(HttpServletResponse response, File file, String fileName){
		outputFile(response, file, fileName, false);
	}
	
	/**
	 * 輸出 檔案 到client端
	 * @param response - HttpServletResponse
	 * @param report - File 輸出檔案
	 * @param fileName - String 輸出檔名
	 * @param autoPrint - boolean 是否要設定自動列印
	 */
	public void outputFile(HttpServletResponse response, File file, String fileName, boolean autoPrint)
	{
		java.io.InputStream is = null;
		java.io.OutputStream os = null;
		try{
			if(null!=file)
			{
			    if(null==fileName || "".equals(fileName))
					fileName = file.getName();
				fileName = java.net.URLEncoder.encode(fileName, "UTF-8");

				String contentType = new javax.activation.MimetypesFileTypeMap().getContentType(file);
				//for ie8 download when use ssl
				//need to add header ( Cache-control='', Pragma='' )
				response.setHeader("Cache-control", "");
				response.setHeader("Pragma", "");
			    response.setCharacterEncoding("UTF-8");
				if( fileName.indexOf(".pdf") > -1 ) {
					//pdf 改用嵌入視窗方式
					response.setHeader("Content-disposition", "inline;filename="+fileName);
				    response.setContentType("application/pdf");
				    //設定為自動列印
				    if(autoPrint)
				    	file = CommonPdfUtils.setAutoShowPrintUI(file);
				} else {
					response.setHeader("Content-disposition", "attachment; filename="+fileName);
				    response.setContentType(contentType);
				}

			    int length = 0;
				byte[] buffer = new byte[512];
				os = response.getOutputStream();
			    is = new java.io.FileInputStream(file);
			    while ( (length = is.read(buffer)) != -1) {
			    	os.write(buffer, 0, length);
			    }
			    os.flush();
			}
		} catch (Exception e) {
		} finally {
			try{
				if(null!=os) os.close();
			}catch(Exception e){
			}
			try{
				if(null!=is) is.close();
			}catch(Exception e){
			}
		}
	}

	/** TO STRING */
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
