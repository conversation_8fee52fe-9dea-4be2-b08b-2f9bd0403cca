package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1017Dao;
import com.kangdainfo.tcfi.model.eicm.dao.PostRecordDao;
import com.kangdainfo.tcfi.service.Pre2008Service;

public class Pre2008ServiceImpl implements Pre2008Service {
	
	PostRecordDao postRecordDao;
	Cedb1017Dao cedb1017Dao;
	
	public PostRecordDao getPostRecordDao() {return postRecordDao;}
	public void setPostRecordDao(PostRecordDao dao) {this.postRecordDao = dao;}
	public Cedb1017Dao getCedb1017Dao() {return cedb1017Dao;}
	public void setCedb1017Dao(Cedb1017Dao dao) {this.cedb1017Dao = dao;}
	
	public int doSave( PostRecord postRecord, String cedb1023Flag, String funCode) throws Exception {
		if ( "insert".equals( cedb1023Flag ) ) {
			if (postRecordDao.insert(postRecord) == 0) {
				throw new MoeaException("公文掛號重複!請重新輸入");
			} // end if	
			//寫入個資軌跡TrackLog(CEDB1023)
			//ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end if
		else if ( "update".equals( cedb1023Flag )) {
			postRecordDao.update(postRecord) ;
			//寫入個資軌跡TrackLog(CEDB1023)
			//ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end else if
		
		
		//cedb1000Dao.setWhenPre2003(cedb1000) ;
				
	    return 0 ;
	}
	
	public int doUpdate( PostRecord postRecord, String cedb1023Flag, String funCode) throws Exception {
		postRecordDao.update(postRecord); 
	    return 0 ;
	}
	
	public int doAssign( Cedb1017 cedb1017 ) throws Exception{
		// 給過的號碼寫回cedb1017
		cedb1017Dao.update(cedb1017) ;
		return 0 ;	
	}
	
	public int rollBack(PostRecord postRecord) throws Exception {
		List<PostRecord> list = postRecordDao.findByPrefixNo(postRecord.getPrefixNo());
		if (list.size() == 1) {
			postRecordDao.setWhenPre2003(postRecord);
		}
		postRecordDao.delete(postRecord);
		return 0;
	}
	
	public List<PostRecord> getPostRecordList(String prefixNo) {
		return postRecordDao.findByPrefixNo(prefixNo);
	}

	public PostRecord getPostRecordByPrefixNo(String prefixNo) {
		return postRecordDao.findLastOneByPrefixNo(prefixNo);
	}
}
