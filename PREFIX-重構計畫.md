# PREFIX 專案現代化重構計畫

## 專案概述

本文件詳述 PREFIX Java Web 應用程式的現代化重構計畫，目標是將現有的 JSP + Spring MVC 單體應用轉換為前後端分離的現代化架構。

## 現有架構分析與評估

### 現狀分析

**技術債務評估：**

-   **高耦合度**: JSP 頁面混合展示層與業務邏輯
-   **維護困難**: 內嵌 Java 程式碼難以測試和重構
-   **技術老舊**: 使用傳統 Spring XML 配置和 JSP 技術
-   **安全複雜**: ESAPI 框架增加系統複雜度

**核心業務模組分析：**

```
PRE1xxx (收文模組)
├── pre1001.jsp - 預查申請資料收文
├── pre1003.jsp - 預查案件收文確認
├── pre1004.jsp - 列印預查申請表
├── pre1005.jsp - 補列印回執聯
├── pre1006.jsp - 收文登打作業
├── pre1007.jsp - 收文預查編號年度統計表
├── pre1008.jsp - 收文預查編號月份統計表
└── pre1009.jsp - 民眾申請案件統計表

PRE2xxx (發文模組)
├── pre2001.jsp - 發文登打作業
├── pre2002.jsp - 自取案件處理
├── pre2003.jsp - 郵寄掛號登錄及維護
├── pre2004.jsp - 預查核准領件編號維護
├── pre2005.jsp - 申請案郵寄資料列印
├── pre2006.jsp - 當月發文統計資料查詢
└── pre2007.jsp - 當月所有承辦人員馬上辦及檢還件數統計表

PRE3xxx (審核模組)
├── pre3001.jsp - 預查審核登錄及維護
├── pre3002.jsp - 輔助查詢
├── pre3003.jsp - 全文檢索查詢
├── pre3004.jsp - 馬上辦
├── pre3005.jsp - 檢還、撤件、撤回退費
├── pre3006.jsp - 清算完結
├── pre3007.jsp - 案件異動紀錄查詢
├── pre3010.jsp - 待辦案件清單
├── pre3011.jsp - 今日案件清單
└── pre3012.jsp - 個人主頁

PRE4xxx (查詢模組)
├── pre4015.jsp - 已審核未結案清單
└── pre4022.jsp - 公司名稱預查規費日報表

PRE5xxx (收費模組)
├── pre5001.jsp - 預查收費作業
└── pre5002.jsp - 補印預查收據

PRE8xxx (系統管理模組)
└── pre8009.jsp - 掛號編號維護
```

**資料庫連線評估：**

-   **EEDB**: 主要業務資料庫 (核心交易資料)
-   **EICM**: 電子化資料庫 (文件管理)
-   **OSSS**: 線上服務系統 (外部整合)
-   **ICMS**: 整合管理系統 (系統管理)

## 目標架構設計

### 整體架構圖

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   React 18+     │ ◄─────────────────► │  Spring Boot    │
│   Frontend      │                     │   Backend       │
│                 │                     │                 │
│ ├── Components  │                     │ ├── Controllers │
│ ├── Redux Store │                     │ ├── Services    │
│ ├── Services    │                     │ ├── Mock Data   │
│ └── Routing     │                     │ └── Entities    │
└─────────────────┘                     └─────────────────┘
                                                   │
                                        ┌─────────────────┐
                                        │  Multi-Database │
                                        │   Configuration │
                                        │   (Mock Data)   │
                                        │ ├── EEDB        │
                                        │ ├── EICM        │
                                        │ ├── OSSS        │
                                        │ └── ICMS        │
                                        └─────────────────┘
```

### 技術選型

**前端技術堆疊：**

-   **核心框架**: React 18.2+
-   **語言**: JavaScript ES6+ (不使用 TypeScript)
-   **建置工具**: Webpack 5.x
-   **狀態管理**: Redux Toolkit
-   **路由**: React Router 6.x
-   **HTTP 客戶端**: Axios
-   **UI 框架**: Ant Design 或 Material-UI
-   **開發工具**: ESLint, Prettier

**後端技術堆疊：**

-   **核心框架**: Spring Boot 3.1.x
-   **Java 版本**: Java 17 LTS
-   **建置工具**: Maven 3.9+
-   **資料存取**: 模擬假資料 (保留多資料源配置)
-   **資料庫**: Oracle Database 配置 (使用模擬資料)
-   **API 文件**: OpenAPI 3.0 (Swagger)
-   **測試框架**: JUnit 5, Mockito

## 詳細分階段遷移計畫

### 基礎架構設置

#### 後端基礎建設

**Spring Boot 專案結構**

```xml
<!-- pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.acer</groupId>
    <artifactId>prefix-backend</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.5</version>
        <relativePath/>
    </parent>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.2.0</version>
        </dependency>
    </dependencies>
</project>
```

**多資料源配置 (保留配置，使用模擬資料)**

```yaml
# application.yml
spring:
    datasource:
        eedb:
            jdbc-url: *************************************
            username: ${EEDB_USERNAME:eedb_user}
            password: ${EEDB_PASSWORD:password}
            driver-class-name: oracle.jdbc.OracleDriver
        eicm:
            jdbc-url: *************************************
            username: ${EICM_USERNAME:eicm_user}
            password: ${EICM_PASSWORD:password}
            driver-class-name: oracle.jdbc.OracleDriver
        osss:
            jdbc-url: *************************************
            username: ${OSSS_USERNAME:osss_user}
            password: ${OSSS_PASSWORD:password}
            driver-class-name: oracle.jdbc.OracleDriver
        icms:
            jdbc-url: *************************************
            username: ${ICMS_USERNAME:icms_user}
            password: ${ICMS_PASSWORD:password}
            driver-class-name: oracle.jdbc.OracleDriver

server:
    port: 8080
    servlet:
        context-path: /api

app:
    mock-data: true

logging:
    level:
        com.acer: DEBUG
        org.springframework: INFO
```

**模擬資料服務基礎類別**

```java
@Component
public class MockDataService {

    private final Random random = new Random();

    public String generatePrefixNo() {
        return String.format("PRE%06d", random.nextInt(999999));
    }

    public String generateReceiptNo() {
        return String.format("REC%06d", random.nextInt(999999));
    }

    public LocalDateTime generateRandomDate() {
        LocalDateTime now = LocalDateTime.now();
        return now.minusDays(random.nextInt(365));
    }

    public String generateStaffUnit() {
        String[] units = {"UNIT001", "UNIT002", "UNIT003", "UNIT004"};
        return units[random.nextInt(units.length)];
    }

    public String generateStatus() {
        String[] statuses = {"PENDING", "APPROVED", "REJECTED", "PROCESSING"};
        return statuses[random.nextInt(statuses.length)];
    }
}
```

#### 前端基礎建設

**React 專案初始化**

```bash
# 建立前端專案目錄
mkdir prefix-frontend
cd prefix-frontend

# 初始化 npm 專案
npm init -y

# 安裝核心依賴
npm install react react-dom react-router-dom axios
npm install @reduxjs/toolkit react-redux
npm install antd
npm install --save-dev webpack webpack-cli webpack-dev-server
npm install --save-dev babel-loader @babel/core @babel/preset-react @babel/preset-env
npm install --save-dev html-webpack-plugin css-loader style-loader
npm install --save-dev eslint eslint-plugin-react prettier
```

**Redux Store 配置**

```javascript
// src/store/index.js
import { configureStore } from '@reduxjs/toolkit';
import receiptReducer from './slices/receiptSlice';
import dispatchReducer from './slices/dispatchSlice';
import reviewReducer from './slices/reviewSlice';

export const store = configureStore({
    reducer: {
        receipt: receiptReducer,
        dispatch: dispatchReducer,
        review: reviewReducer,
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                ignoredActions: ['persist/PERSIST'],
            },
        }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

**Redux Slice 範例**

```javascript
// src/store/slices/receiptSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { receiptApi } from '../../services/apiService';

// 異步 thunk
export const fetchReceipts = createAsyncThunk('receipt/fetchReceipts', async (params, { rejectWithValue }) => {
    try {
        const response = await receiptApi.getAllReceipts(params);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response.data);
    }
});

export const createReceipt = createAsyncThunk('receipt/createReceipt', async (receiptData, { rejectWithValue }) => {
    try {
        const response = await receiptApi.createReceipt(receiptData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response.data);
    }
});

const receiptSlice = createSlice({
    name: 'receipt',
    initialState: {
        receipts: [],
        currentReceipt: null,
        loading: false,
        error: null,
    },
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
        setCurrentReceipt: (state, action) => {
            state.currentReceipt = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchReceipts.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchReceipts.fulfilled, (state, action) => {
                state.loading = false;
                state.receipts = action.payload;
            })
            .addCase(fetchReceipts.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(createReceipt.fulfilled, (state, action) => {
                state.receipts.push(action.payload);
            });
    },
});

export const { clearError, setCurrentReceipt } = receiptSlice.actions;
export default receiptSlice.reducer;
```

## 步驟 1：預查收文管理模組開發 (8-10 週)

### 週次 1-2：PRE5001 預查收費作業 & PRE1001 預查申請資料收文

#### PRE5001 後端實作

**實體類別定義**

```java
@Entity
@Table(name = "PREFIX_FEE", schema = "EEDB")
public class PrefixFee {

    @Id
    @Column(name = "FEE_ID")
    private String feeId;

    @Column(name = "PREFIX_NO")
    private String prefixNo;

    @Column(name = "FEE_AMOUNT")
    private BigDecimal feeAmount;

    @Column(name = "FEE_DATE")
    private LocalDateTime feeDate;

    @Column(name = "PAYMENT_STATUS")
    private String paymentStatus;

    @Column(name = "RECEIPT_NO")
    private String receiptNo;

    // Constructors, getters, setters
    public PrefixFee() {}

    public String getFeeId() { return feeId; }
    public void setFeeId(String feeId) { this.feeId = feeId; }

    public String getPrefixNo() { return prefixNo; }
    public void setPrefixNo(String prefixNo) { this.prefixNo = prefixNo; }

    public BigDecimal getFeeAmount() { return feeAmount; }
    public void setFeeAmount(BigDecimal feeAmount) { this.feeAmount = feeAmount; }

    // ... 其他 getter/setter 方法
}
```

**DTO 類別**

```java
public class PrefixFeeDto {
    private String feeId;
    private String prefixNo;
    private BigDecimal feeAmount;
    private LocalDateTime feeDate;
    private String paymentStatus;
    private String receiptNo;
    private String applicantName;
    private String companyName;

    // Constructors
    public PrefixFeeDto() {}

    // Getters and Setters
    public String getFeeId() { return feeId; }
    public void setFeeId(String feeId) { this.feeId = feeId; }

    // ... 其他 getter/setter 方法
}
```

**服務層實作 (使用模擬資料)**

```java
@Service
public class PrefixFeeService {

    private final MockDataService mockDataService;

    public PrefixFeeService(MockDataService mockDataService) {
        this.mockDataService = mockDataService;
    }

    public List<PrefixFeeDto> getAllFees() {
        List<PrefixFeeDto> fees = new ArrayList<>();

        for (int i = 1; i <= 20; i++) {
            PrefixFeeDto fee = new PrefixFeeDto();
            fee.setFeeId("FEE" + String.format("%06d", i));
            fee.setPrefixNo(mockDataService.generatePrefixNo());
            fee.setFeeAmount(new BigDecimal("300.00"));
            fee.setFeeDate(mockDataService.generateRandomDate());
            fee.setPaymentStatus(i % 3 == 0 ? "PAID" : "PENDING");
            fee.setReceiptNo(mockDataService.generateReceiptNo());
            fee.setApplicantName("申請人" + i);
            fee.setCompanyName("測試公司" + i);

            fees.add(fee);
        }

        return fees;
    }

    public PrefixFeeDto createFee(PrefixFeeDto dto) {
        // 模擬建立收費記錄
        dto.setFeeId("FEE" + System.currentTimeMillis());
        dto.setFeeDate(LocalDateTime.now());
        dto.setPaymentStatus("PENDING");
        dto.setReceiptNo(mockDataService.generateReceiptNo());

        return dto;
    }

    public PrefixFeeDto updatePaymentStatus(String feeId, String status) {
        // 模擬更新付款狀態
        PrefixFeeDto fee = new PrefixFeeDto();
        fee.setFeeId(feeId);
        fee.setPaymentStatus(status);
        fee.setFeeDate(LocalDateTime.now());

        return fee;
    }

    public List<PrefixFeeDto> getFeesByDateRange(LocalDate startDate, LocalDate endDate) {
        // 模擬日期範圍查詢
        return getAllFees().stream()
                .filter(fee -> {
                    LocalDate feeDate = fee.getFeeDate().toLocalDate();
                    return !feeDate.isBefore(startDate) && !feeDate.isAfter(endDate);
                })
                .collect(Collectors.toList());
    }
}
```

**REST 控制器**

```java
@RestController
@RequestMapping("/api/prefix/fees")
@CrossOrigin(origins = "http://localhost:3000")
public class PrefixFeeController {

    private final PrefixFeeService service;

    public PrefixFeeController(PrefixFeeService service) {
        this.service = service;
    }

    @GetMapping
    public ResponseEntity<List<PrefixFeeDto>> getAllFees() {
        List<PrefixFeeDto> fees = service.getAllFees();
        return ResponseEntity.ok(fees);
    }

    @PostMapping
    public ResponseEntity<PrefixFeeDto> createFee(@RequestBody PrefixFeeDto dto) {
        PrefixFeeDto created = service.createFee(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    @PutMapping("/{feeId}/payment-status")
    public ResponseEntity<PrefixFeeDto> updatePaymentStatus(
            @PathVariable String feeId,
            @RequestBody Map<String, String> request) {
        String status = request.get("status");
        PrefixFeeDto updated = service.updatePaymentStatus(feeId, status);
        return ResponseEntity.ok(updated);
    }

    @GetMapping("/date-range")
    public ResponseEntity<List<PrefixFeeDto>> getFeesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        List<PrefixFeeDto> fees = service.getFeesByDateRange(startDate, endDate);
        return ResponseEntity.ok(fees);
    }
}
```

#### PRE5001 前端實作

**Redux Slice**

```javascript
// src/store/slices/feeSlice.js
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { feeApi } from '../../services/apiService';

export const fetchFees = createAsyncThunk('fee/fetchFees', async (params, { rejectWithValue }) => {
    try {
        const response = await feeApi.getAllFees(params);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response.data);
    }
});

export const createFee = createAsyncThunk('fee/createFee', async (feeData, { rejectWithValue }) => {
    try {
        const response = await feeApi.createFee(feeData);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response.data);
    }
});

export const updatePaymentStatus = createAsyncThunk('fee/updatePaymentStatus', async ({ feeId, status }, { rejectWithValue }) => {
    try {
        const response = await feeApi.updatePaymentStatus(feeId, status);
        return response.data;
    } catch (error) {
        return rejectWithValue(error.response.data);
    }
});

const feeSlice = createSlice({
    name: 'fee',
    initialState: {
        fees: [],
        currentFee: null,
        loading: false,
        error: null,
    },
    reducers: {
        clearError: (state) => {
            state.error = null;
        },
        setCurrentFee: (state, action) => {
            state.currentFee = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchFees.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchFees.fulfilled, (state, action) => {
                state.loading = false;
                state.fees = action.payload;
            })
            .addCase(fetchFees.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(createFee.fulfilled, (state, action) => {
                state.fees.push(action.payload);
            })
            .addCase(updatePaymentStatus.fulfilled, (state, action) => {
                const index = state.fees.findIndex((fee) => fee.feeId === action.payload.feeId);
                if (index !== -1) {
                    state.fees[index] = action.payload;
                }
            });
    },
});

export const { clearError, setCurrentFee } = feeSlice.actions;
export default feeSlice.reducer;
```

**API 服務**

```javascript
// src/services/feeService.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080/api';

const apiClient = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

export const feeApi = {
    getAllFees: (params) => apiClient.get('/prefix/fees', { params }),
    createFee: (data) => apiClient.post('/prefix/fees', data),
    updatePaymentStatus: (feeId, status) => apiClient.put(`/prefix/fees/${feeId}/payment-status`, { status }),
    getFeesByDateRange: (startDate, endDate) =>
        apiClient.get('/prefix/fees/date-range', {
            params: { startDate, endDate },
        }),
};
```

**PRE5001 收費作業元件**

```javascript
// src/pages/PRE5001/FeeManagement.js
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Table, Button, Space, message, Modal, Form, Input, Select, DatePicker, Card, Row, Col } from 'antd';
import { fetchFees, createFee, updatePaymentStatus } from '../../store/slices/feeSlice';

const { Option } = Select;
const { RangePicker } = DatePicker;

const FeeManagement = () => {
    const dispatch = useDispatch();
    const { fees, loading, error } = useSelector((state) => state.fee);
    const [form] = Form.useForm();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedFee, setSelectedFee] = useState(null);

    useEffect(() => {
        dispatch(fetchFees());
    }, [dispatch]);

    const handleCreateFee = async (values) => {
        try {
            await dispatch(createFee(values)).unwrap();
            message.success('收費記錄建立成功');
            setIsModalVisible(false);
            form.resetFields();
        } catch (error) {
            message.error('建立失敗');
        }
    };

    const handleUpdatePaymentStatus = async (feeId, status) => {
        try {
            await dispatch(updatePaymentStatus({ feeId, status })).unwrap();
            message.success('付款狀態更新成功');
        } catch (error) {
            message.error('更新失敗');
        }
    };

    const columns = [
        {
            title: '收費編號',
            dataIndex: 'feeId',
            key: 'feeId',
            width: 120,
        },
        {
            title: '預查編號',
            dataIndex: 'prefixNo',
            key: 'prefixNo',
            width: 120,
        },
        {
            title: '申請人',
            dataIndex: 'applicantName',
            key: 'applicantName',
            width: 100,
        },
        {
            title: '公司名稱',
            dataIndex: 'companyName',
            key: 'companyName',
            width: 150,
        },
        {
            title: '收費金額',
            dataIndex: 'feeAmount',
            key: 'feeAmount',
            width: 100,
            render: (amount) => `NT$ ${amount}`,
        },
        {
            title: '收費日期',
            dataIndex: 'feeDate',
            key: 'feeDate',
            width: 120,
            render: (date) => new Date(date).toLocaleDateString('zh-TW'),
        },
        {
            title: '付款狀態',
            dataIndex: 'paymentStatus',
            key: 'paymentStatus',
            width: 100,
            render: (status) => {
                const statusMap = {
                    PENDING: { text: '待付款', color: 'orange' },
                    PAID: { text: '已付款', color: 'green' },
                    CANCELLED: { text: '已取消', color: 'red' },
                };
                const statusInfo = statusMap[status] || { text: status, color: 'default' };
                return <span style={{ color: statusInfo.color }}>{statusInfo.text}</span>;
            },
        },
        {
            title: '收據編號',
            dataIndex: 'receiptNo',
            key: 'receiptNo',
            width: 120,
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size='small'>
                    {record.paymentStatus === 'PENDING' && (
                        <Button type='link' size='small' onClick={() => handleUpdatePaymentStatus(record.feeId, 'PAID')}>
                            確認付款
                        </Button>
                    )}
                    <Button
                        type='link'
                        size='small'
                        onClick={() => {
                            setSelectedFee(record);
                            // 開啟收據列印功能
                        }}
                    >
                        列印收據
                    </Button>
                    <Button type='link' size='small' danger onClick={() => handleUpdatePaymentStatus(record.feeId, 'CANCELLED')}>
                        取消
                    </Button>
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Card title='PRE5001 - 預查收費作業' style={{ marginBottom: 16 }}>
                <Row gutter={16} style={{ marginBottom: 16 }}>
                    <Col span={6}>
                        <Button type='primary' onClick={() => setIsModalVisible(true)}>
                            新增收費記錄
                        </Button>
                    </Col>
                    <Col span={8}>
                        <RangePicker
                            placeholder={['開始日期', '結束日期']}
                            onChange={(dates) => {
                                if (dates) {
                                    // 實作日期範圍查詢
                                }
                            }}
                        />
                    </Col>
                </Row>

                <Table
                    columns={columns}
                    dataSource={fees}
                    rowKey='feeId'
                    loading={loading}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 筆資料`,
                    }}
                    scroll={{ x: 1200 }}
                />
            </Card>

            <Modal
                title='新增收費記錄'
                visible={isModalVisible}
                onCancel={() => {
                    setIsModalVisible(false);
                    form.resetFields();
                }}
                footer={null}
                width={600}
            >
                <Form form={form} layout='vertical' onFinish={handleCreateFee}>
                    <Form.Item label='預查編號' name='prefixNo' rules={[{ required: true, message: '請輸入預查編號' }]}>
                        <Input placeholder='請輸入預查編號' />
                    </Form.Item>

                    <Form.Item label='申請人姓名' name='applicantName' rules={[{ required: true, message: '請輸入申請人姓名' }]}>
                        <Input placeholder='請輸入申請人姓名' />
                    </Form.Item>

                    <Form.Item label='公司名稱' name='companyName' rules={[{ required: true, message: '請輸入公司名稱' }]}>
                        <Input placeholder='請輸入公司名稱' />
                    </Form.Item>

                    <Form.Item label='收費金額' name='feeAmount' rules={[{ required: true, message: '請輸入收費金額' }]}>
                        <Input type='number' placeholder='請輸入收費金額' addonBefore='NT$' />
                    </Form.Item>

                    <Form.Item>
                        <Space>
                            <Button type='primary' htmlType='submit'>
                                建立
                            </Button>
                            <Button
                                onClick={() => {
                                    setIsModalVisible(false);
                                    form.resetFields();
                                }}
                            >
                                取消
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default FeeManagement;
```

### 週次 3-4：PRE1003 預查案件收文確認 & PRE1004 列印預查申請表

#### PRE1003 後端實作

**收文確認服務**

```java
@Service
public class ReceiptConfirmationService {

    private final MockDataService mockDataService;

    public ReceiptConfirmationService(MockDataService mockDataService) {
        this.mockDataService = mockDataService;
    }

    public List<ReceiptConfirmationDto> getPendingConfirmations() {
        List<ReceiptConfirmationDto> confirmations = new ArrayList<>();

        for (int i = 1; i <= 15; i++) {
            ReceiptConfirmationDto confirmation = new ReceiptConfirmationDto();
            confirmation.setConfirmationId("CONF" + String.format("%06d", i));
            confirmation.setPrefixNo(mockDataService.generatePrefixNo());
            confirmation.setReceiptNo(mockDataService.generateReceiptNo());
            confirmation.setApplicantName("申請人" + i);
            confirmation.setCompanyName("測試公司" + i);
            confirmation.setReceiptDate(mockDataService.generateRandomDate());
            confirmation.setStatus("PENDING_CONFIRMATION");
            confirmation.setStaffUnit(mockDataService.generateStaffUnit());

            confirmations.add(confirmation);
        }

        return confirmations;
    }

    public ReceiptConfirmationDto confirmReceipt(String confirmationId, ReceiptConfirmationDto dto) {
        // 模擬確認收文
        dto.setConfirmationId(confirmationId);
        dto.setStatus("CONFIRMED");
        dto.setConfirmationDate(LocalDateTime.now());
        dto.setConfirmedBy("SYSTEM_USER");

        return dto;
    }

    public ReceiptConfirmationDto rejectReceipt(String confirmationId, String reason) {
        // 模擬駁回收文
        ReceiptConfirmationDto dto = new ReceiptConfirmationDto();
        dto.setConfirmationId(confirmationId);
        dto.setStatus("REJECTED");
        dto.setRejectionReason(reason);
        dto.setConfirmationDate(LocalDateTime.now());

        return dto;
    }
}
```

**REST 控制器**

```java
@RestController
@RequestMapping("/api/prefix/receipt-confirmations")
@CrossOrigin(origins = "http://localhost:3000")
public class ReceiptConfirmationController {

    private final ReceiptConfirmationService service;

    public ReceiptConfirmationController(ReceiptConfirmationService service) {
        this.service = service;
    }

    @GetMapping("/pending")
    public ResponseEntity<List<ReceiptConfirmationDto>> getPendingConfirmations() {
        List<ReceiptConfirmationDto> confirmations = service.getPendingConfirmations();
        return ResponseEntity.ok(confirmations);
    }

    @PostMapping("/{confirmationId}/confirm")
    public ResponseEntity<ReceiptConfirmationDto> confirmReceipt(
            @PathVariable String confirmationId,
            @RequestBody ReceiptConfirmationDto dto) {
        ReceiptConfirmationDto confirmed = service.confirmReceipt(confirmationId, dto);
        return ResponseEntity.ok(confirmed);
    }

    @PostMapping("/{confirmationId}/reject")
    public ResponseEntity<ReceiptConfirmationDto> rejectReceipt(
            @PathVariable String confirmationId,
            @RequestBody Map<String, String> request) {
        String reason = request.get("reason");
        ReceiptConfirmationDto rejected = service.rejectReceipt(confirmationId, reason);
        return ResponseEntity.ok(rejected);
    }
}
```

#### PRE1003 前端實作

**收文確認元件**

```javascript
// src/pages/PRE1003/ReceiptConfirmation.js
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Table, Button, Space, message, Modal, Form, Input, Card, Tag, Descriptions } from 'antd';
import { fetchPendingConfirmations, confirmReceipt, rejectReceipt } from '../../store/slices/receiptConfirmationSlice';

const ReceiptConfirmation = () => {
    const dispatch = useDispatch();
    const { confirmations, loading } = useSelector((state) => state.receiptConfirmation);
    const [selectedRecord, setSelectedRecord] = useState(null);
    const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
    const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
    const [rejectForm] = Form.useForm();

    useEffect(() => {
        dispatch(fetchPendingConfirmations());
    }, [dispatch]);

    const handleConfirm = async (record) => {
        try {
            await dispatch(
                confirmReceipt({
                    confirmationId: record.confirmationId,
                    data: record,
                })
            ).unwrap();
            message.success('收文確認成功');
            dispatch(fetchPendingConfirmations());
        } catch (error) {
            message.error('確認失敗');
        }
    };

    const handleReject = async (values) => {
        try {
            await dispatch(
                rejectReceipt({
                    confirmationId: selectedRecord.confirmationId,
                    reason: values.reason,
                })
            ).unwrap();
            message.success('收文駁回成功');
            setIsRejectModalVisible(false);
            rejectForm.resetFields();
            dispatch(fetchPendingConfirmations());
        } catch (error) {
            message.error('駁回失敗');
        }
    };

    const columns = [
        {
            title: '確認編號',
            dataIndex: 'confirmationId',
            key: 'confirmationId',
            width: 120,
        },
        {
            title: '預查編號',
            dataIndex: 'prefixNo',
            key: 'prefixNo',
            width: 120,
        },
        {
            title: '收文編號',
            dataIndex: 'receiptNo',
            key: 'receiptNo',
            width: 120,
        },
        {
            title: '申請人',
            dataIndex: 'applicantName',
            key: 'applicantName',
            width: 100,
        },
        {
            title: '公司名稱',
            dataIndex: 'companyName',
            key: 'companyName',
            width: 150,
        },
        {
            title: '收文日期',
            dataIndex: 'receiptDate',
            key: 'receiptDate',
            width: 120,
            render: (date) => new Date(date).toLocaleDateString('zh-TW'),
        },
        {
            title: '承辦單位',
            dataIndex: 'staffUnit',
            key: 'staffUnit',
            width: 100,
        },
        {
            title: '狀態',
            dataIndex: 'status',
            key: 'status',
            width: 120,
            render: (status) => {
                const statusMap = {
                    PENDING_CONFIRMATION: { text: '待確認', color: 'orange' },
                    CONFIRMED: { text: '已確認', color: 'green' },
                    REJECTED: { text: '已駁回', color: 'red' },
                };
                const statusInfo = statusMap[status] || { text: status, color: 'default' };
                return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
            },
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size='small'>
                    <Button
                        type='link'
                        size='small'
                        onClick={() => {
                            setSelectedRecord(record);
                            setIsDetailModalVisible(true);
                        }}
                    >
                        檢視詳情
                    </Button>
                    {record.status === 'PENDING_CONFIRMATION' && (
                        <>
                            <Button type='link' size='small' onClick={() => handleConfirm(record)}>
                                確認
                            </Button>
                            <Button
                                type='link'
                                size='small'
                                danger
                                onClick={() => {
                                    setSelectedRecord(record);
                                    setIsRejectModalVisible(true);
                                }}
                            >
                                駁回
                            </Button>
                        </>
                    )}
                </Space>
            ),
        },
    ];

    return (
        <div>
            <Card title='PRE1003 - 預查案件收文確認'>
                <Table
                    columns={columns}
                    dataSource={confirmations}
                    rowKey='confirmationId'
                    loading={loading}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 筆待確認資料`,
                    }}
                    scroll={{ x: 1200 }}
                />
            </Card>

            {/* 詳情檢視 Modal */}
            <Modal
                title='收文詳情'
                visible={isDetailModalVisible}
                onCancel={() => setIsDetailModalVisible(false)}
                footer={[
                    <Button key='close' onClick={() => setIsDetailModalVisible(false)}>
                        關閉
                    </Button>,
                ]}
                width={600}
            >
                {selectedRecord && (
                    <Descriptions column={2} bordered>
                        <Descriptions.Item label='確認編號'>{selectedRecord.confirmationId}</Descriptions.Item>
                        <Descriptions.Item label='預查編號'>{selectedRecord.prefixNo}</Descriptions.Item>
                        <Descriptions.Item label='收文編號'>{selectedRecord.receiptNo}</Descriptions.Item>
                        <Descriptions.Item label='申請人'>{selectedRecord.applicantName}</Descriptions.Item>
                        <Descriptions.Item label='公司名稱' span={2}>
                            {selectedRecord.companyName}
                        </Descriptions.Item>
                        <Descriptions.Item label='收文日期'>{new Date(selectedRecord.receiptDate).toLocaleDateString('zh-TW')}</Descriptions.Item>
                        <Descriptions.Item label='承辦單位'>{selectedRecord.staffUnit}</Descriptions.Item>
                        <Descriptions.Item label='狀態' span={2}>
                            <Tag color={selectedRecord.status === 'PENDING_CONFIRMATION' ? 'orange' : 'green'}>
                                {selectedRecord.status === 'PENDING_CONFIRMATION' ? '待確認' : '已確認'}
                            </Tag>
                        </Descriptions.Item>
                    </Descriptions>
                )}
            </Modal>

            {/* 駁回原因 Modal */}
            <Modal
                title='駁回收文'
                visible={isRejectModalVisible}
                onCancel={() => {
                    setIsRejectModalVisible(false);
                    rejectForm.resetFields();
                }}
                footer={null}
            >
                <Form form={rejectForm} layout='vertical' onFinish={handleReject}>
                    <Form.Item label='駁回原因' name='reason' rules={[{ required: true, message: '請輸入駁回原因' }]}>
                        <Input.TextArea rows={4} placeholder='請詳細說明駁回原因' />
                    </Form.Item>

                    <Form.Item>
                        <Space>
                            <Button type='primary' htmlType='submit' danger>
                                確認駁回
                            </Button>
                            <Button
                                onClick={() => {
                                    setIsRejectModalVisible(false);
                                    rejectForm.resetFields();
                                }}
                            >
                                取消
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default ReceiptConfirmation;
```

### 週次 5-6：PRE1005 補列印回執聯 & PRE1006 收文登打作業

### 週次 7-8：PRE1007-PRE1009 統計報表功能

### 週次 9-10：PRE5002 補印預查收據 & PRE4022 規費日報表

## 步驟 2：預查發文管理模組開發 (8-10 週)

### 週次 1-2：PRE4015 已審核未結案清單 & PRE2001 發文登打作業

### 週次 3-4：PRE2004 預查核准領件編號維護 & PRE2002 自取案件處理

### 週次 5-6：PRE8009 掛號編號維護 & PRE2003 郵寄掛號登錄及維護

### 週次 7-8：PRE2005 申請案郵寄資料列印 & PRE2006 當月發文統計資料查詢

### 週次 9-10：PRE2007 當月所有承辦人員馬上辦及檢還件數統計表

## 步驟 3：預查審核管理模組開發 (8-10 週)

### 週次 1-2：PRE3012 個人主頁 & PRE3010 待辦案件清單

### 週次 3-4：PRE3011 今日案件清單 & PRE3001 預查審核登錄及維護

### 週次 5-6：PRE3002 輔助查詢 & PRE3003 全文檢索查詢

### 週次 7-8：PRE3004 馬上辦 & PRE3005 檢還、撤件、撤回退費

### 週次 9-10：PRE3006 清算完結 & PRE3007 案件異動紀錄查詢

## 步驟 4：系統整合測試 (4-6 週)

### 週次 1-2：前後端 API 整合測試

**整合測試配置**

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
class PrefixIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void shouldHandleCompleteReceiptWorkflow() {
        // 測試完整的收文流程
        // 1. 建立收費記錄
        // 2. 收文確認
        // 3. 列印申請表
        // 4. 補列印回執聯
    }

    @Test
    void shouldHandleCompleteDispatchWorkflow() {
        // 測試完整的發文流程
    }

    @Test
    void shouldHandleCompleteReviewWorkflow() {
        // 測試完整的審核流程
    }
}
```

### 週次 3-4：前端元件整合測試

**前端整合測試**

```javascript
// src/tests/integration/WorkflowIntegration.test.js
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from '../../store';
import App from '../../App';

describe('Complete Workflow Integration', () => {
    test('should complete receipt workflow', async () => {
        render(
            <Provider store={store}>
                <BrowserRouter>
                    <App />
                </BrowserRouter>
            </Provider>
        );

        // 測試收文流程
        // 1. 導航到收費作業
        // 2. 建立收費記錄
        // 3. 確認收文
        // 4. 列印相關文件
    });
});
```

### 週次 5-6：效能測試與優化

**效能測試配置**

```java
@Test
void shouldHandleHighVolumeRequests() {
    // 模擬高併發請求測試
    int numberOfThreads = 100;
    int numberOfRequestsPerThread = 10;

    ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
    CountDownLatch latch = new CountDownLatch(numberOfThreads);

    for (int i = 0; i < numberOfThreads; i++) {
        executor.submit(() -> {
            try {
                for (int j = 0; j < numberOfRequestsPerThread; j++) {
                    // 執行 API 請求
                    ResponseEntity<List<PrefixFeeDto>> response =
                        restTemplate.getForEntity("/api/prefix/fees", List.class);
                    assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
                }
            } finally {
                latch.countDown();
            }
        });
    }

    latch.await(30, TimeUnit.SECONDS);
}
```

## 時程規劃與里程碑

### 整體時程表

| 階段       | 期程         | 主要里程碑           | 交付項目                 |
| ---------- | ------------ | -------------------- | ------------------------ |
| **步驟 1** | 8-10 週      | 預查收文管理模組完成 | 11 個收文相關功能頁面    |
| **步驟 2** | 8-10 週      | 預查發文管理模組完成 | 9 個發文相關功能頁面     |
| **步驟 3** | 8-10 週      | 預查審核管理模組完成 | 10 個審核相關功能頁面    |
| **步驟 4** | 4-6 週       | 系統整合測試完成     | 完整測試套件與效能報告   |
| **總計**   | **28-36 週** | **完整現代化系統**   | **30 個功能頁面 + 測試** |

### 詳細里程碑

**步驟 1 里程碑：**

-   週次 2：PRE5001、PRE1001 完成
-   週次 4：PRE1003、PRE1004 完成
-   週次 6：PRE1005、PRE1006 完成
-   週次 8：PRE1007、PRE1008、PRE1009 完成
-   週次 10：PRE5002、PRE4022 完成

**步驟 2 里程碑：**

-   週次 2：PRE4015、PRE2001 完成
-   週次 4：PRE2004、PRE2002 完成
-   週次 6：PRE8009、PRE2003 完成
-   週次 8：PRE2005、PRE2006 完成
-   週次 10：PRE2007 完成

**步驟 3 里程碑：**

-   週次 2：PRE3012、PRE3010 完成
-   週次 4：PRE3011、PRE3001 完成
-   週次 6：PRE3002、PRE3003 完成
-   週次 8：PRE3004、PRE3005 完成
-   週次 10：PRE3006、PRE3007 完成

**步驟 4 里程碑：**

-   週次 2：API 整合測試完成
-   週次 4：前端整合測試完成
-   週次 6：效能測試與優化完成

## 風險評估與緩解策略

### 高風險項目

**1. 功能複雜度風險**

-   **風險描述**: 30 個功能頁面的業務邏輯複雜，可能遺漏重要功能
-   **影響程度**: 高
-   **緩解策略**:
    -   每個功能完成後進行詳細的功能對照檢查
    -   建立功能清單與驗收標準
    -   分模組進行功能驗證

**2. 模擬資料一致性風險**

-   **風險描述**: 使用模擬資料可能導致資料關聯性問題
-   **影響程度**: 中
-   **緩解策略**:
    -   建立統一的模擬資料生成規則
    -   確保跨模組資料的一致性
    -   建立資料關聯性檢查機制

### 中風險項目

**3. Redux 狀態管理複雜度風險**

-   **風險描述**: 多個模組的狀態管理可能變得複雜
-   **影響程度**: 中
-   **緩解策略**:
    -   建立清晰的 Redux 架構規範
    -   每個模組使用獨立的 slice
    -   建立狀態管理最佳實踐文件

## 成功指標

### 技術指標

-   **API 回應時間**: < 500ms
-   **前端載入時間**: < 3 秒
-   **功能完整性**: 100% 功能頁面實作
-   **程式碼品質**: ESLint 零錯誤

### 業務指標

-   **功能對照率**: 100% 原有功能覆蓋
-   **使用者介面一致性**: 統一的 UI/UX 設計
-   **系統穩定性**: 0 重大錯誤

## 結論

本重構計畫採用功能模組導向的開發方式，將 PREFIX 系統的 30 個功能頁面分為三個主要模組進行現代化改造。透過使用 Redux Toolkit 進行狀態管理、模擬資料進行開發，以及完整的整合測試，確保系統的穩定性和功能完整性。

重構完成後，系統將具備現代化的前後端分離架構，為未來的功能擴展和維護提供良好的基礎。
onClick={() => navigate(`/applications/${record.prefixNo}/edit`)} >
編輯
</Button>
<Button
type="link"
danger
onClick={() => handleDelete(record.prefixNo)} >
刪除
</Button>
</Space>
)
}
];

return (

<div>
<div style={{ marginBottom: 16 }}>
<Button
type="primary"
onClick={() => navigate('/applications/new')} >
新增申請案件
</Button>
</div>
<Table
columns={columns}
dataSource={applications}
rowKey="prefixNo"
loading={loading}
pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆資料`
        }}
/>
</div>
);
};

export default ApplicationList;

````

**5.2 申請表單元件**
```javascript
// src/pages/ApplicationForm.js
import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, message, Card } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import { prefixApi } from '../services/apiService';

const { Option } = Select;

const ApplicationForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const navigate = useNavigate();
  const { prefixNo } = useParams();

  useEffect(() => {
    if (prefixNo) {
      setIsEdit(true);
      fetchApplication();
    }
  }, [prefixNo]);

  const fetchApplication = async () => {
    setLoading(true);
    try {
      const response = await prefixApi.getApplication(prefixNo);
      form.setFieldsValue(response.data);
    } catch (error) {
      message.error('載入申請案件失敗');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await prefixApi.updateApplication(prefixNo, values);
        message.success('更新成功');
      } else {
        await prefixApi.createApplication(values);
        message.success('建立成功');
      }
      navigate('/applications');
    } catch (error) {
      message.error(isEdit ? '更新失敗' : '建立失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title={isEdit ? '編輯申請案件' : '新增申請案件'}>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        style={{ maxWidth: 600 }}
      >
        <Form.Item
          label="申請編號"
          name="prefixNo"
          rules={[{ required: true, message: '請輸入申請編號' }]}
        >
          <Input disabled={isEdit} placeholder="請輸入申請編號" />
        </Form.Item>

        <Form.Item
          label="收文編號"
          name="receiptNo"
          rules={[{ required: true, message: '請輸入收文編號' }]}
        >
          <Input placeholder="請輸入收文編號" />
        </Form.Item>

        <Form.Item
          label="承辦單位"
          name="staffUnit"
          rules={[{ required: true, message: '請選擇承辦單位' }]}
        >
          <Select placeholder="請選擇承辦單位">
            <Option value="UNIT001">第一組</Option>
            <Option value="UNIT002">第二組</Option>
            <Option value="UNIT003">第三組</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="狀態"
          name="status"
          rules={[{ required: true, message: '請選擇狀態' }]}
        >
          <Select placeholder="請選擇狀態">
            <Option value="PENDING">待處理</Option>
            <Option value="APPROVED">已核准</Option>
            <Option value="REJECTED">已駁回</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="備註"
          name="remark"
        >
          <Input.TextArea rows={4} placeholder="請輸入備註" />
        </Form.Item>

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新' : '建立'}
            </Button>
            <Button onClick={() => navigate('/applications')}>
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ApplicationForm;
````

### 第三階段：業務邏輯遷移 (8-10 週)

#### 週次 1-3：PRE1xxx 收文模組遷移

**1.1 收文服務後端實作**

```java
@Service
@Transactional
public class DocumentReceiptService {

    private final DocumentReceiptRepository repository;
    private final SequenceService sequenceService;

    public DocumentReceiptService(DocumentReceiptRepository repository,
                                SequenceService sequenceService) {
        this.repository = repository;
        this.sequenceService = sequenceService;
    }

    public DocumentReceiptDto processReceipt(DocumentReceiptDto dto) {
        // 產生收文編號
        String receiptNo = sequenceService.generateReceiptNumber();

        DocumentReceipt entity = new DocumentReceipt();
        entity.setReceiptNo(receiptNo);
        entity.setDocumentType(dto.getDocumentType());
        entity.setReceiptDate(LocalDateTime.now());
        entity.setStaffUnit(dto.getStaffUnit());
        entity.setStatus("RECEIVED");

        DocumentReceipt saved = repository.save(entity);
        return convertToDto(saved);
    }

    public List<DocumentReceiptDto> getReceiptsByDateRange(LocalDate startDate, LocalDate endDate) {
        LocalDateTime start = startDate.atStartOfDay();
        LocalDateTime end = endDate.atTime(23, 59, 59);

        return repository.findByReceiptDateBetween(start, end)
                .stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    private DocumentReceiptDto convertToDto(DocumentReceipt entity) {
        DocumentReceiptDto dto = new DocumentReceiptDto();
        dto.setReceiptNo(entity.getReceiptNo());
        dto.setDocumentType(entity.getDocumentType());
        dto.setReceiptDate(entity.getReceiptDate());
        dto.setStaffUnit(entity.getStaffUnit());
        dto.setStatus(entity.getStatus());
        return dto;
    }
}
```

**1.2 收文前端元件**

```javascript
// src/components/DocumentReceipt.js
import React, { useState } from 'react';
import { Form, Input, Select, Button, DatePicker, message, Card } from 'antd';
import { documentApi } from '../services/apiService';

const { Option } = Select;

const DocumentReceipt = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    const onFinish = async (values) => {
        setLoading(true);
        try {
            const response = await documentApi.processReceipt(values);
            message.success(`收文成功，收文編號：${response.data.receiptNo}`);
            form.resetFields();
        } catch (error) {
            message.error('收文處理失敗');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card title='文件收文作業'>
            <Form form={form} layout='vertical' onFinish={onFinish} style={{ maxWidth: 600 }}>
                <Form.Item label='文件類型' name='documentType' rules={[{ required: true, message: '請選擇文件類型' }]}>
                    <Select placeholder='請選擇文件類型'>
                        <Option value='APPLICATION'>申請書</Option>
                        <Option value='CERTIFICATE'>證明文件</Option>
                        <Option value='OTHER'>其他</Option>
                    </Select>
                </Form.Item>

                <Form.Item label='承辦單位' name='staffUnit' rules={[{ required: true, message: '請選擇承辦單位' }]}>
                    <Select placeholder='請選擇承辦單位'>
                        <Option value='UNIT001'>第一組</Option>
                        <Option value='UNIT002'>第二組</Option>
                        <Option value='UNIT003'>第三組</Option>
                    </Select>
                </Form.Item>

                <Form.Item label='備註' name='remark'>
                    <Input.TextArea rows={3} placeholder='請輸入備註' />
                </Form.Item>

                <Form.Item>
                    <Button type='primary' htmlType='submit' loading={loading}>
                        確認收文
                    </Button>
                </Form.Item>
            </Form>
        </Card>
    );
};

export default DocumentReceipt;
```

#### 週次 4-6：PRE2xxx 登打模組遷移

**4.1 資料登打服務**

```java
@Service
@Transactional
public class DataEntryService {

    private final ApplicationDataRepository repository;
    private final ValidationService validationService;

    public DataEntryService(ApplicationDataRepository repository,
                          ValidationService validationService) {
        this.repository = repository;
        this.validationService = validationService;
    }

    public ApplicationDataDto saveApplicationData(ApplicationDataDto dto) {
        // 資料驗證
        ValidationResult validation = validationService.validateApplicationData(dto);
        if (!validation.isValid()) {
            throw new ValidationException(validation.getErrors());
        }

        ApplicationData entity = convertToEntity(dto);
        entity.setLastModified(LocalDateTime.now());
        entity.setStatus("DRAFT");

        ApplicationData saved = repository.save(entity);
        return convertToDto(saved);
    }

    public ApplicationDataDto submitApplicationData(String applicationId) {
        ApplicationData entity = repository.findById(applicationId)
                .orElseThrow(() -> new EntityNotFoundException("Application not found"));

        // 最終驗證
        ApplicationDataDto dto = convertToDto(entity);
        ValidationResult validation = validationService.validateForSubmission(dto);
        if (!validation.isValid()) {
            throw new ValidationException(validation.getErrors());
        }

        entity.setStatus("SUBMITTED");
        entity.setSubmittedDate(LocalDateTime.now());

        ApplicationData updated = repository.save(entity);
        return convertToDto(updated);
    }

    private ApplicationDataDto convertToDto(ApplicationData entity) {
        // 轉換邏輯
        return new ApplicationDataDto();
    }

    private ApplicationData convertToEntity(ApplicationDataDto dto) {
        // 轉換邏輯
        return new ApplicationData();
    }
}
```

#### 週次 7-8：PRE3xxx 審核模組遷移

**7.1 審核服務實作**

```java
@Service
@Transactional
public class ReviewService {

    private final ReviewRepository repository;
    private final NotificationService notificationService;

    public ReviewService(ReviewRepository repository,
                        NotificationService notificationService) {
        this.repository = repository;
        this.notificationService = notificationService;
    }

    public ReviewDto assignReviewer(String applicationId, String reviewerId) {
        Review review = new Review();
        review.setApplicationId(applicationId);
        review.setReviewerId(reviewerId);
        review.setAssignedDate(LocalDateTime.now());
        review.setStatus("ASSIGNED");

        Review saved = repository.save(review);

        // 發送通知
        notificationService.notifyReviewerAssignment(reviewerId, applicationId);

        return convertToDto(saved);
    }

    public ReviewDto submitReview(String reviewId, ReviewSubmissionDto submission) {
        Review review = repository.findById(reviewId)
                .orElseThrow(() -> new EntityNotFoundException("Review not found"));

        review.setReviewResult(submission.getResult());
        review.setReviewComment(submission.getComment());
        review.setReviewDate(LocalDateTime.now());
        review.setStatus("COMPLETED");

        Review updated = repository.save(review);

        // 發送審核完成通知
        notificationService.notifyReviewCompletion(review.getApplicationId(), submission.getResult());

        return convertToDto(updated);
    }

    private ReviewDto convertToDto(Review entity) {
        // 轉換邏輯
        return new ReviewDto();
    }
}
```

### 第四階段：整合測試與部署 (4-6 週)

#### 週次 1-2：API 整合測試

**1.1 整合測試配置**

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
class PrefixApplicationIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private PrefixApplicationRepository repository;

    @Test
    void shouldCreateAndRetrieveApplication() {
        // Given
        PrefixApplicationDto dto = new PrefixApplicationDto();
        dto.setPrefixNo("TEST001");
        dto.setReceiptNo("REC001");
        dto.setStaffUnit("UNIT001");
        dto.setRemark("Test application");

        // When
        ResponseEntity<PrefixApplicationDto> createResponse = restTemplate.postForEntity(
                "/api/prefix/applications", dto, PrefixApplicationDto.class);

        // Then
        assertThat(createResponse.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(createResponse.getBody().getPrefixNo()).isEqualTo("TEST001");

        // Verify retrieval
        ResponseEntity<PrefixApplicationDto> getResponse = restTemplate.getForEntity(
                "/api/prefix/applications/TEST001", PrefixApplicationDto.class);

        assertThat(getResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(getResponse.getBody().getReceiptNo()).isEqualTo("REC001");
    }
}
```

#### 週次 3-4：前端整合測試

**3.1 前端測試配置**

```javascript
// src/tests/ApplicationList.test.js
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ApplicationList from '../pages/ApplicationList';
import { prefixApi } from '../services/apiService';

// Mock API
jest.mock('../services/apiService');

const mockApplications = [
    {
        prefixNo: 'TEST001',
        receiptNo: 'REC001',
        staffUnit: 'UNIT001',
        applicationDate: '2024-01-01T00:00:00',
        status: 'PENDING',
    },
];

describe('ApplicationList', () => {
    beforeEach(() => {
        prefixApi.getAllApplications.mockResolvedValue({ data: mockApplications });
    });

    test('renders application list', async () => {
        render(
            <BrowserRouter>
                <ApplicationList />
            </BrowserRouter>
        );

        await waitFor(() => {
            expect(screen.getByText('TEST001')).toBeInTheDocument();
            expect(screen.getByText('REC001')).toBeInTheDocument();
        });
    });
});
```

#### 週次 5-6：部署配置與文件

**5.1 Docker 配置**

```dockerfile
# Dockerfile.backend
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/prefix-backend-1.0.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```dockerfile
# Dockerfile.frontend
FROM node:18-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

**5.2 Docker Compose 配置**

```yaml
# docker-compose.yml
version: '3.8'

services:
    backend:
        build:
            context: ./prefix-backend
            dockerfile: Dockerfile
        ports:
            - '8080:8080'
        environment:
            - SPRING_PROFILES_ACTIVE=docker
            - EEDB_USERNAME=${EEDB_USERNAME}
            - EEDB_PASSWORD=${EEDB_PASSWORD}
        depends_on:
            - oracle-db

    frontend:
        build:
            context: ./prefix-frontend
            dockerfile: Dockerfile
        ports:
            - '80:80'
        depends_on:
            - backend

    oracle-db:
        image: oracle/database:19.3.0-ee
        ports:
            - '1521:1521'
        environment:
            - ORACLE_SID=ORCL
            - ORACLE_PWD=${ORACLE_PASSWORD}
        volumes:
            - oracle-data:/opt/oracle/oradata

volumes:
    oracle-data:
```

## 時程規劃與里程碑

### 整體時程表

| 階段         | 期程         | 主要里程碑         | 交付項目                                     |
| ------------ | ------------ | ------------------ | -------------------------------------------- |
| **第一階段** | 6-8 週       | 後端基礎建設完成   | Spring Boot 應用程式、多資料源配置、基礎 API |
| **第二階段** | 6-8 週       | 前端基礎建設完成   | React 應用程式、核心元件、API 整合           |
| **第三階段** | 8-10 週      | 業務邏輯遷移完成   | 所有 PRE 模組功能、完整業務流程              |
| **第四階段** | 4-6 週       | 整合測試與部署     | 測試套件、部署配置、文件                     |
| **總計**     | **24-32 週** | **完整現代化系統** | **生產就緒的前後端分離應用**                 |

### 詳細里程碑

**第一階段里程碑：**

-   週次 2：Spring Boot 專案建立與多資料源配置
-   週次 4：核心實體類別與 Repository 完成
-   週次 6：基礎服務層與 REST API 完成
-   週次 8：API 文件與基礎測試完成

**第二階段里程碑：**

-   週次 2：React 專案建立與 Webpack 配置
-   週次 4：核心元件與路由配置完成
-   週次 6：API 整合與狀態管理完成
-   週次 8：基礎 UI 元件與樣式完成

**第三階段里程碑：**

-   週次 3：PRE1xxx 收文模組遷移完成
-   週次 6：PRE2xxx 登打模組遷移完成
-   週次 8：PRE3xxx 審核模組遷移完成
-   週次 10：PRE4xxx、PRE5xxx 查詢與收費模組完成

**第四階段里程碑：**

-   週次 2：API 整合測試完成
-   週次 4：前端整合測試完成
-   週次 6：部署配置與文件完成

## 風險評估與緩解策略

### 高風險項目

**1. 業務邏輯複雜度風險**

-   **風險描述**: JSP 頁面內嵌複雜業務邏輯難以完整提取
-   **影響程度**: 高
-   **緩解策略**:
    -   建立詳細的業務邏輯文件
    -   逐頁分析現有 JSP 程式碼
    -   建立對照測試確保功能一致性
    -   分階段遷移，保持舊系統並行運作

**2. 多資料庫整合風險**

-   **風險描述**: 多個資料庫連線配置複雜，可能影響資料一致性
-   **影響程度**: 高
-   **緩解策略**:
    -   建立完整的資料庫連線測試
    -   實作分散式交易管理
    -   建立資料同步監控機制
    -   準備資料回滾策略

### 中風險項目

**3. 效能降低風險**

-   **風險描述**: 前後端分離可能增加網路延遲
-   **影響程度**: 中
-   **緩解策略**:
    -   實作 API 快取機制
    -   優化資料庫查詢效能
    -   建立效能監控與警報
    -   實作前端資料快取

**4. 使用者介面變更風險**

-   **風險描述**: 新 UI 可能影響使用者操作習慣
-   **影響程度**: 中
-   **緩解策略**:
    -   保持相似的操作流程
    -   提供使用者訓練
    -   建立使用者回饋機制
    -   準備 UI 調整計畫

### 低風險項目

**5. 技術學習曲線風險**

-   **風險描述**: 團隊需要學習新技術
-   **影響程度**: 低
-   **緩解策略**:
    -   提供技術訓練課程
    -   建立技術文件與範例
    -   安排技術分享會議
    -   建立技術支援機制

## 具體實作步驟

### 步驟 1：環境準備

```bash
# 1. 建立專案目錄結構
mkdir prefix-modernization
cd prefix-modernization
mkdir prefix-backend prefix-frontend

# 2. 後端專案初始化
cd prefix-backend
mvn archetype:generate -DgroupId=com.kangdainfo -DartifactId=prefix-backend

# 3. 前端專案初始化
cd ../prefix-frontend
npm init -y
npm install react react-dom react-router-dom axios antd
```

### 步驟 2：後端開發

```bash
# 1. 建立 Spring Boot 主類別
# 2. 配置多資料源
# 3. 建立實體類別
# 4. 實作 Repository 介面
# 5. 開發服務層
# 6. 建立 REST 控制器
```

### 步驟 3：前端開發

```bash
# 1. 配置 Webpack
# 2. 建立 React 應用程式結構
# 3. 開發核心元件
# 4. 實作 API 服務層
# 5. 建立業務頁面
# 6. 整合 UI 框架
```

### 步驟 4：整合測試

```bash
# 1. 建立後端整合測試
# 2. 建立前端單元測試
# 3. 實作端對端測試
# 4. 效能測試
# 5. 安全性測試
```

### 步驟 5：部署準備

```bash
# 1. 建立 Docker 映像檔
# 2. 配置 Docker Compose
# 3. 準備部署腳本
# 4. 建立監控配置
# 5. 準備備份策略
```

## 成功指標

### 技術指標

-   **API 回應時間**: < 500ms
-   **前端載入時間**: < 3 秒
-   **測試覆蓋率**: > 80%
-   **系統可用性**: > 99.5%

### 業務指標

-   **功能完整性**: 100% 現有功能遷移
-   **資料一致性**: 0 資料遺失
-   **使用者滿意度**: > 85%
-   **系統穩定性**: 0 重大錯誤

## 結論

本重構計畫提供了將 PREFIX 系統從傳統 JSP + Spring MVC 架構遷移到現代化前後端分離架構的完整路徑。透過分階段實施、風險控制和持續測試，可以確保遷移過程的順利進行，同時保持系統的穩定性和業務連續性。

重構完成後，系統將具備更好的可維護性、擴展性和使用者體驗，為未來的功能擴展和技術升級奠定堅實基礎。
