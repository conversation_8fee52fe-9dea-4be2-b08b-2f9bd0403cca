package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class IndexLogDao  extends BaseDaoJdbc implements RowMapper<IndexLog>{

	private static final String SQL_query = "SELECT * FROM INDEX_LOG ";
	public IndexLog query(IndexLog obj){
		if(obj == null) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		if(!"".equals(Common.get(obj.getId()))){
			sqljob.appendSQLCondition(" ID = ? ");
			sqljob.addParameter(obj.getId());	
		}
		if(!"".equals(Common.get(obj.getWsId()))){
			sqljob.appendSQLCondition(" WS_ID = ? ");
			sqljob.addParameter(obj.getWsId());
		}
		if(!"".equals(Common.get(obj.getParam1()))){
			sqljob.appendSQLCondition(" PARAM1 = ? ");
			sqljob.addParameter(obj.getParam1());
		}
		if(!"".equals(Common.get(obj.getParam2()))){
			sqljob.appendSQLCondition(" PARAM2 = ? ");
			sqljob.addParameter(obj.getParam2());
		}
		if(!"".equals(Common.get(obj.getParam3()))){
			sqljob.appendSQLCondition(" PARAM3 = ? ");
			sqljob.addParameter(obj.getParam3());
		}
		if(!"".equals(Common.get(obj.getExecuteDate()))){
			sqljob.appendSQLCondition(" EXECUTE_DATE = ? ");
			sqljob.addParameter(obj.getExecuteDate());
		}
		if(!"".equals(Common.get(obj.getStatus()))){
			sqljob.appendSQLCondition(" STATUS = ? ");
			sqljob.addParameter(obj.getStatus());
		}
		sqljob.appendSQLCondition(" ROWNUM = 1 ");
		sqljob.appendSQL(" ORDER BY CREATE_DATE ");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<IndexLog> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}

	public IndexLog getWaitForProcess(String wsId){
		if(null==wsId || "".equals(wsId)) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition(" WS_ID = ? ");
		sqljob.addParameter(wsId);
		sqljob.appendSQLCondition(" STATUS = ? ");
		sqljob.addParameter(PrefixConstants.INDEX_LOG_STATUS_0);
		sqljob.appendSQL(" ORDER BY CREATE_DATE ");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<IndexLog> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}

	public IndexLog queryByPk(Long id){
		if("".equals(Common.get(id))) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition(" ID = ? ");
		sqljob.addParameter(id);
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<IndexLog> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}
	
	public Integer queryCount(){
		SQLJob sqljob = new SQLJob("SELECT COUNT(1) FROM INDEX_LOG WHERE STATUS = '0' ");
		return getJdbcTemplate().queryForObject(sqljob.getSQL(), Integer.class);
	}
	
	private static final String SQL_insert = "INSERT INTO INDEX_LOG(WS_ID, PARAM1, PARAM2, PARAM3, EXECUTE_DATE, CREATE_DATE, CREATE_USER, START_TIME, FINISH_TIME, STATUS, REMARK) "
			+ "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	public void insert(IndexLog obj){
		if(obj != null){
			SQLJob sqljob = new SQLJob(SQL_insert);
			sqljob.addParameter(obj.getWsId());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getParam1());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getParam2());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getParam3());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getExecuteDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getCreateDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getCreateUser());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getStartTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getFinishTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getStatus());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getRemark());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}

	public void updateSameIndexLog(IndexLog obj) {
		SQLJob sqljob = new SQLJob("UPDATE INDEX_LOG SET ");
		sqljob.appendSQL(" START_TIME=?");
		sqljob.addParameter(Common.get(obj.getStartTime()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",FINISH_TIME=?");
		sqljob.addParameter(Common.get(obj.getFinishTime()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",STATUS=?");
		sqljob.addParameter(Common.get(obj.getStatus()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",REMARK=?");
		sqljob.addParameter(Common.get(obj.getRemark()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(" WHERE WS_ID=?");
		sqljob.addParameter(Common.get(obj.getWsId()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(" AND PARAM1=?");
		sqljob.addParameter(Common.get(obj.getParam1()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(" AND STATUS='0'");
		sqljob.appendSQL(" AND CREATE_DATE < ? ");
		sqljob.addParameter(Common.get(obj.getStartTime()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public IndexLog update(IndexLog obj){
		if(obj == null || "".equals(Common.get(obj.getId()))) return null;
		
		IndexLog t = queryByPk(obj.getId());
		if(null==t) return null;
		
		SQLJob sqljob = new SQLJob("UPDATE INDEX_LOG SET ");
		sqljob.appendSQL(" START_TIME=?");
		sqljob.appendSQL(",FINISH_TIME=?");
		sqljob.appendSQL(",STATUS=?");
		sqljob.appendSQL(",REMARK=?");
		sqljob.appendSQL(" WHERE ID = ? ");
		sqljob.addParameter(Common.get(obj.getStartTime()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getFinishTime()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getStatus()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getRemark()));
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(Common.get(obj.getId()));
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		return queryByPk(obj.getId());
	}
	
	public void updateRetryError() {
		SQLJob sqljob = new SQLJob("UPDATE INDEX_LOG SET STATUS='0', PARAM3='ERROR' WHERE STATUS='3' ");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL());
	}
	
	@Override
	public IndexLog mapRow(ResultSet rs, int arg1) throws SQLException {
		IndexLog obj = null;
		if(null!=rs) {
			obj = new IndexLog();
			obj.setId(rs.getLong("ID"));
			obj.setWsId(rs.getString("WS_ID"));
			obj.setParam1(rs.getString("PARAM1"));
			obj.setParam2(rs.getString("PARAM2"));
			obj.setParam3(rs.getString("PARAM3"));
			obj.setExecuteDate(rs.getString("EXECUTE_DATE"));
			obj.setCreateDate(rs.getString("CREATE_DATE"));
			obj.setCreateUser(rs.getString("CREATE_USER"));
			obj.setStartTime(rs.getString("START_TIME"));
			obj.setFinishTime(rs.getString("FINISH_TIME"));
			obj.setStatus(rs.getString("STATUS"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}