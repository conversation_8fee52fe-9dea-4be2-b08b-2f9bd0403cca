var banNationalNames = [];
var banNouns = [];
var allBusinessItems = {};
var itemRests; //專業經營項目
var itemCmpyOrgs; //限制公司型態經營的營業項目
var busItemComporgs; //限制公司型態經營的營業項目，網頁提示後面的說明。
var canResetKeyin = false; //是否可重設發文登打
var oldBusiItems;
var jsonBusItemReserve365s;

//for IE8 Array indexOf
if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = Array.prototype.indexOf || function(obj, start) {
		for (var i = (start || 0), j = this.length; i < j; i++) {
			if (this[i] === obj) {
				return i;
			}
		}
		return -1;
	};
}

if (!Array.prototype.forEach) {
    Array.prototype.forEach = function (fn, scope) {
        'use strict';
        var i, len;
        for (i = 0, len = this.length; i < len; ++i) {
            if (i in this) {
                fn.call(scope, this, i, this);
            }
        }
    };
}

$(document).ready(function() {
	commonUtils.pressToSearch();
	
	 var alertFallback = true;
	 if (typeof console === "undefined" || typeof console.log === "undefined") {
	 		console = {};
	 	if (alertFallback) {
	 		console.log = function(msg) {
	 			//alert(msg);
	 		};
	 	} else {
	 		console.log = function() {};
	 	}
    }
	 
	$('#save, #tempSave, #assignBtn').prop('disabled', true);
	
	$("div[id^=fragment]").css("padding", 0);
	$("div[id^=tabs2]").css("padding", 0);
	$("img[src$=gif]").css({'height' : '20px'});
	
	$("input[value=列印申請表]").attr('disabled', false).click( function() {
		var prefixNo = $("#prefixNo").val();
		var path = window.location.pathname;
		var functionName = path.substring(path.lastIndexOf("/")+1).replace('_00', '');
		var notAutoPrintConfig = ['pre2001.jsp']; // 欲取消自動列印的程式代號
		var isAutoPrint = '';
		
		if( notAutoPrintConfig.indexOf(functionName) != -1 ) {
			isAutoPrint = '&isAutoPrint=false';
		} else {
			isAutoPrint = '&isAutoPrint=true';
		}
		
		window.open(getVirtualPath() + "tcfi/pre/pre1004.jsp?from=" + functionName + "&prefixNo="+ prefixNo +"&prefixNoEnd=" + prefixNo + isAutoPrint, "printApplyForm" );
	});
	
	if($("input[name=state]").val().indexOf('success') != -1) {
		$('input[name="ERRMSG"]').val('存檔成功');
	}
	
	getAllItemCode();
	initBanDatas();
	initRestDatas();
	hiddenBeforeSearch();
	ajaxStopInit();
	approveResultListener();
	commonUtils.removeHotkeyStr();
	
	$(document).tooltip({
		track: false
	});
	
	$("#backToPrev").click(function() {
		var path = window.location.pathname;
		window.location = path.replace("_00", '');
	});
	
	$(".openAddresser").hide();
	
	//當查詢的結果只有一筆時，自動選取送出
	if( $(".tabinnerTable tr, #resultTable tr").size() == 2 ) {
		$("input[type=checkbox]").attr("checked", true);
		$("#doSubmit,input[name=btnQuery6],input[name=btnQuery2]").trigger("click");
	} else {
		$("input[type=checkbox]", "#resultTable").attr("checked", true);
	}
	
	if( $(".tabinnerTable tr, #resultTable tr").size() ) {
		if( $(".tabinnerTable tr, #resultTable tr").size() >= 2 ) {
			addSearchMsg("查詢成功");
		} else {
			addSearchMsg("無資料");
		}
	}

	//set up css for buttons
	$(".openAddresser,.toolbar_form[type=button],.toolbar_default2[type=button],#copyToReceiver,#copyToReceiver2").addClass('toolbar_form toolbar_default');
	$("[name=attorAddr]").prop("size", 70);
	
	$("#cedb1001s,#cedb1101s").on("mouseup", "input[name=companyName]", function(e) {
		$(this).next().val(commonUtils.getSelectionText());
	});
	
	$("#cedb1001s").on("click", "input[name=approveResult]", function() {
		var companyName = $(this).parents('tr').find("[name=companyName]").val();
		checkBans(companyName);
	});
	
	$(document).ajaxComplete(function() {
		checkControlWord();
		
	});
	
	$("#buItemUnSelectAll").click(function() {
		commonUtils.unAll("cedb1002Chk");
	});
	
	
	$("#resetClose").click(function() {
		if( canResetKeyin ) { //已審核之前才能操作
			if(document.forms[0].resetCloseDateFlag) {
				document.forms[0].resetCloseDateFlag.value = "Y";
			} else {
				$('<input>').attr({
				    type: 'hidden',
				    id: 'resetCloseDateFlag',
				    name: 'resetCloseDateFlag',
				    value: 'Y'
				}).appendTo('form');
			}
			$("input[name=reserveDate]").val('');
			$("#issueKeyinDateTime").html('');
			document.forms[0].state.value = 'resetCloseDate';
			document.forms[0].submit();
			//document.forms[0].issueKeyinDateTime.value = '';
			//$("#tempSave").click();
		} else {
			alert("案件狀態不符「發文登打完成」，不可重設");
		}
	});
	
	$("#previewApproveForm").click(function() {
		var telixNo = $("input[name=telixNo]").val();
		var prefixNo = $("#prefixNo").val();
		if(telixNo) {
			var url = 'pre4013.jsp?validate=false&q_telixNo='+ telixNo;
			window.open(url,'gcis','height=768,width=1024,left=0,top=0,toolbars=no,scrollbars=auto,location=no');
		} else {
			var url = 'pre4013.jsp?validate=false&q_prefixNo=' + prefixNo;
			window.open(url,'gcis','height=768,width=1024,left=0,top=0,toolbars=no,scrollbars=auto,location=no');
			//alert("沒有網路收文號，無法列印");
		}
	});
	
	$("input[name=busiItemNo]").live('focusout', '#cedb1002s', function() {
		var value = $.trim(this.value);
		var itemValue = checkItemCode(value);
		if(itemValue) {
			$(this).closest('td').next().find('input[name=busiItem]').val(itemValue);
			$(this).closest('tr').find('input[name=restMark]').val(searchRest(value));
		}
	});
	
	$.post(getVirtualPath() + "tcfi/ajax/jsonBusItemReserve365.jsp", function(data) {
		jsonBusItemReserve365s = data;
	});
	
});

function initBanDatas() {
	$("select[class='td_form_white']").eq(0).find("option").map(function(i, v) {
		banNouns.push($(v).text());
	});
	
	$("select[class='td_form_white']").eq(1).find("option").map(function(i, v) {
		banNationalNames.push($(v).text());
	});
}

function initRestDatas() {
	//專業經營項目
	$.post(getVirtualPath() + "tcfi/ajax/jsonItemRest.jsp", function(data) {
		itemRests = data;
	});
	
	//限制公司型態經營的營業項目
	$.post(getVirtualPath() + "tcfi/ajax/jsonItemCmpyOrg.jsp", function(data) {
		itemCmpyOrgs = data;
	});
	
	//限制公司型態經營的營業項目
	$.post(getVirtualPath() + "tcfi/ajax/jsonBusItemComporg.jsp", function(data) {
		busItemComporgs = data;
	});
}

/**
 * 檢核禁用名詞和國家名稱
 * @param companyName
 */
function checkBans(companyName) {
	for(var i in banNationalNames) {
		if(companyName.indexOf(banNationalNames[i]) != -1) {
			alert("國家名稱不能做為公司名稱：" + banNationalNames[i]);
			return;
		}
	}
	
	for(var i in banNouns) {
		if(companyName.indexOf(banNouns[i]) != -1) {
			alert("禁用名詞不能做為公司名稱：" + banNouns[i]);
			return;
		}
	}
}

function isDataChanged() {
	$("form").change(function() {
		return true;
	});
}

//跳出專業經營項目提示的對話方塊
function alertRest(item_code) {
	for (var code in itemRests) {
		if (code == item_code) {
			alert(item_code + "為「限專業經營」之營業項目：\n本案" + itemRests[code]);
		}
	}
	return "";
}

//存檔時，檢查專業經營項目
function isExitRest() {
	var num = 0;
	var size = document.getElementsByName("cedb1002Chk").length;
	
	// 計算有多少筆限專業經營的
	$("input[name=restMark]").map(function(i, v) {
		if ($(v).val() == "限專業經營")
			num++;
	});
	
	// 營業項目包含專業經營，且超過一個營業項目，強制不給存檔
	if (num >= 1 && size > 1) {
		alert("有「限專業經營」之營業項目，不可再包含其他營業項目，請檢查之後再存檔。");
		if (!confirm("是否存檔？"))
			return "no";
		return "yes";
	}
	// 跳出提示訊息
	$("input[name=restMark]").map(function(i, v) {
		if ($(v).val() == "限專業經營") {
			var code = $(v).closest("tr").find("input[name=busiItemNo]").val();
			alertRest(code);
        }
	});
	
	if (num > 0)
		if (!confirm("是否存檔？"))
			return "no";
	return "yes";
}

function hiddenBeforeSearch() {
	var url = window.location.href;
	var table = $("table[class=table_form],table[class=tabinnerTable]");
	
	if(url.indexOf("00") != -1 && table.find("tr").length <= 1) {
		table.hide();
		$("#spanQuery4,#spanQuery5,#spanQuery6").hide();
	}
}

function searchRest(item_code) {
	for (var i in itemRests) {
		if (itemRests[item_code]) {
			itemRests[item_code];
			//return "限專業經營"; 舊系統寫法
		}
	}

	for (var i in itemCmpyOrgs) {
		if (itemCmpyOrgs[item_code]) {
			return itemCmpyOrgs[item_code];
		}
	}
	return "";
}

/**
 * 檢查營業項資料正確性
 */
function checkItem() {
	var array = [];
	var validateResult = true;
	$("input[name=busiItemNo]").map(function(i, v) {
		var code = $(v).val();
		if(code.length != 7) {
			alert("營業項目代碼長度錯誤！");
			validateResult = false;
			return;
		}
		
		if(array.indexOf(code) != -1) {
			alert(code + "營業項目重覆！");
			validateResult = false;
			return;
		}
		array.push(code);
		
		if(code == "ZZ99999" && $("input[name=busiItemNo]").length <= 1) {
			alert("營業項目不能只有一項ZZ99999");
			validateResult = false;
			return;
		}
		
	});
	
	return validateResult;
}

//檢查公司名稱是否有管制文字
function checkControlWord() {
	var controlWords = ["華億傳媒","保利華億","耀華玻璃","香港商鳳凰","鳳凰衛視","臺灣港務","台灣港務"];
	
	$("input[name='companyName']").map(function(i, v) {
		var companyName = $(this).val();
		for(var i in controlWords) {
			if(companyName.indexOf(controlWords[i]) != -1) {
				$(this).parents('tr').find("[name=remark]").val("本名稱為管制文字，須經長官同意始得核准");
			}
		}
	});
}

//輔助查詢
function helpSearch(e) {
	var q = encodeURI( $(e).prev().val() );	
	var prop = "scrollbars=0,resizable=1,toolbar=0,menubar=0,directories=0,status=0,location=0";
	closeReturnWindow();
	returnWindow = window.open(getVirtualPath() + "tcfi/pre/pre3002.jsp?q_cmpyName=" + q + "&tabId=1","helpSearch",prop);
}

/**
 * 同名公司相關功能
 * @param cedb1001s
 * @param cedb1004s
 */
function showSames(cedb1001s, cedb1004s) {
	var html;
	var c1001TrId;
	var c1004SameSeqNo;
	var sameBanNo;
	var sameCompanyName; 
	var samePrefixNo;
	var c1001SeqNo;
	var revokeAppDate;
	var warningWording;
	var c1004SeqNo;
	
	for(var i = 0; i < cedb1001s.length; i++) {
		c1001TrId = (i+1);
		html = '<div id="showSame0' + c1001TrId + '" style="display: none;">' +
		'<table border=1>' +
		'<tr>' +
	    '<td class="thead" width="20px">&nbsp;</td>' +
	    '<td class="thead" width="80px">預查編號</td>' +
	    '<td class="thead" width="80px">統編</td>' +
	    '<td class="thead">同名公司名稱</td>' +
	    '<td class="thead" width="80px">公司狀態</td>' +
	    '<td class="thead" width="76px">申請人</td>' +
	    '<td class="thead" width="80px">保留期限</td>' +
	    '<td class="thead" width="80px">解撤廢日</td>' +
	    '<td class="thead" />' +
	    '</tr>' +
	    '</table></div>';

	    $("#cedb1001s").after(html);
	    
		c1001SeqNo = cedb1001s[i].seqNo;
		for(var j = 0; j < cedb1004s.length; j++) {
			c1004SeqNo = cedb1004s[j].seqNo;
			c1004SameSeqNo = '0' + cedb1004s[j].sameSeqNo;
			sameBanNo = cedb1004s[j].sameBanNo;
			sameCompanyName = cedb1004s[j].sameCompanyName;
			samePrefixNo = cedb1004s[j].samePrefixNo;
			
			if(c1001SeqNo == c1004SeqNo) {
				if(sameBanNo) {
					$.ajax({
					    type: 'GET',
					    url: getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + sameBanNo,
					    async: false
					}).done(function (data) {
						var showSameCompanyName = sameBanNo + " " + (data ? data.STATUS_CODE : '') + " " + sameCompanyName;
					    $("input[name=showSame0"+ c1001TrId +"]").val(showSameCompanyName);
					});
				} else {
					var showSameCompanyName = samePrefixNo +  " " + sameCompanyName;
				    $("input[name=showSame0"+ c1001TrId +"]").val(showSameCompanyName);
				}
			}
		}
	}
	var state = '';
	var postUrl = '';
	var applyName = '';
	var reserveDate = '';
	var prefixNo = '';
	var banNo = '';
	var tr = '';
	for(var k = 0; k < cedb1004s.length; k++) {
		//reset
		state = '';
		postUrl = '';
		applyName = '';
		reserveDate = '';
		prefixNo = '';
		banNo = '';
		tr = '';

		c1001TrId = cedb1004s[k].seqNo;
		revokeAppDate = commonUtils.trimUndefined(cedb1004s[k].revokeAppDate);
		
		if(revokeAppDate == '9999999') {
			revokeAppDate = '';
		}
		
		var isCompany = true;
		var qBanNoUrl = "pre3008_00.jsp";
		if(cedb1004s[k].sameCompanyName.indexOf("有限合夥") >= 0){
			isCompany = false;
			qBanNoUrl = "pre3013_00.jsp";
		}
		if(isCompany && cedb1004s[k].sameBanNo) {
			warningWording = '涉及同名公司(統編：' + cedb1004s[k].sameBanNo + cedb1004s[k].sameCompanyName + ')，違反公司法第18條規定';
			postUrl = getVirtualPath() + 'tcfi/ajax/jsonCedb2000.jsp?q=' + cedb1004s[k].sameBanNo;
		} else {
			warningWording = '涉及同名公司(預查編號：' + cedb1004s[k].samePrefixNo + cedb1004s[k].sameCompanyName + ')，違反公司法第18條規定';
			state = commonUtils.trimUndefined(cedb1004s[k].cmpyStatus);//公司狀態
			applyName = cedb1004s[k].applyName;
			reserveDate = cedb1004s[k].reserveDate;
			//postUrl = '/prefix_new/tcfi/ajax/jsonCedb1000.jsp?q=' + cedb1004s[k].samePrefixNo;
		}
		if(postUrl) {
			$.ajax({
				type: "POST",
				url: postUrl,
				async: false,
				success: function(data) {
					if(!!data && data.STATUS_CODE) {
						state = data.STATUS_CODE;
					} else {
						state = '';
					}

					if(!!data && data.APPLY_NAME) {
						applyName = data.APPLY_NAME;
					} else {
						applyName = '';
					}
					
					if(!!data && data.RESERVE_DATE) {
						reserveDate = data.RESERVE_DATE;
					} else {
						reserveDate = '';
					}
				},
				error: function(jqXHR, textStatus, errorThrown) {
					console.log("Error: status = " + textStatus + ", " + "error thrown: " + errorThrown);
				}
			});
		}
		
		prefixNo = commonUtils.trimUndefined(cedb1004s[k].samePrefixNo);
		banNo = commonUtils.trimUndefined(cedb1004s[k].sameBanNo);
		
		if (prefixNo != '' ) {
			if( banNo == '' ) {
				$.ajax({
					type: "POST",
					url: getVirtualPath() + 'tcfi/ajax/jsonCedb1000.jsp?q=' + prefixNo,
					async: false,
					success: function(data) {
						if(!!data && data.BAN_NO) {
							banNo = data.BAN_NO;
						} else {
							banNo = '';
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						console.log("Error: status = " + textStatus + ", " + "error thrown: " + errorThrown);
					}
				});
			}
		}
		
		tr += "<tr>" +
				"<td align='center'>" + cedb1004s[k].sameSeqNo + "</td>" +
				"<td><a href='pre4001_00.jsp?prefixNos="+ prefixNo +"' target='_blank'>" + prefixNo +"</a>&nbsp;</td>" +
				"<td><a href='" + qBanNoUrl + "?banNos="+ banNo +"' target='_blank'>" + banNo + "</a></td>" +
				"<td>" + cedb1004s[k].sameCompanyName + "&nbsp;</td>" + 
				"<td>" + commonUtils.trimUndefined(state) + "&nbsp;</td>" +
				"<td>" + commonUtils.trimUndefined(applyName) + "&nbsp;</td>" +
				"<td>" + commonUtils.trimUndefined(reserveDate) +"&nbsp;</td>" +
				"<td>" + revokeAppDate + "&nbsp;</td>" +
				"<td> <button data-src=" + c1001TrId +" class='copyToRemark' value='" + warningWording + "' onclick=copyToRemark(this) >複製</button> </td>" +		
				"</tr>";
		
		try {
			if($("#showSame" + c1001TrId)) {
				$("#showSame" + c1001TrId).find("table").append(tr);
			}
		} catch(e) {}
	}
	
	var newDiv = $('<div />');
	$( document ).ajaxComplete(function( event,request, settings ) {
		$("input[name^='showSame']").click(function() {
			var name = $(this).attr("name");
			var content = $("#" + name).html();
			if(!content)
				return;
			
			$(newDiv).html(content);
			$(newDiv).dialog({
				title: '同名公司詳細清單', 
				height: 280, 
				width: 'auto'
			});
		});
	});
	//如果approveResult都是N的時候就自動勾選不予核准
	if(!$("input[name='approveResult']:checked").val() && $("select[name=approveResult]").val() == 'N') {
		$("input[name='approveResultAllNo']").prop("checked", true);
	}
}

/**
 * 智慧型預查相關功能
 * @param prefixNo
 */
function showPreSearchs (prefixNo) {
	$.ajax({
	    type: 'GET',
	    url: getVirtualPath() + "tcfi/ajax/jsonPreSearch.jsp?q=" + prefixNo,
	    async: false
	}).done(function (res) {
		console.log("結果: ", res);
		let resultsObj = (new DOMParser()).parseFromString(res, "text/xml");
		if (resultsObj.getElementsByTagName("errorcode")[0] == null) {	
			let results = resultsObj.getElementsByTagName("results")[0].getElementsByTagName("result");
			let i = 0;
			for(i = 0; i < results.length; i++){
				let result = results[i];
				$("input[name=showPreSearch" + result.getAttribute("SeqNO") +"]").val(result.getElementsByTagName("examineDescr")[0].textContent);
			}
		} 
	});
}

function showSamesNew(cedb1001s, cedb1004s) {
	//$("#cedb1001s input[name=remark]").hide();
	for(var i = 0; i < cedb1001s.length; i++) {
		//舊的作法，點擊後同名公司公司後所跳出來的視窗用
		var html = "<div id=showSame0" + (i+1) + " style='display: none'><table border=1><TR>" +
	    '<td class="thead" width="20px">&nbsp;</td>' +
	    '<td class="thead" width="80px">預查編號</td>' +
	    '<td class="thead" width="80px">統編</td>' +
	    '<td class="thead">同名公司名稱</td>' +
	    '<td class="thead" width="80px">公司狀態</td>' +
	    '<td class="thead" width="76px">申請人</td>' +
	    '<td class="thead" width="80px">保留期限</td><TR>' +
	    "</table></div>";

	    $("#cedb1001s").after(html);
	    
		var c1001SeqNo = cedb1001s[i].seqNo;
		var isDone = false;
		for(var j = 0; j < cedb1004s.length; j++) {
			var c1004SeqNo = cedb1004s[j].seqNo;
			var sameBanNo = cedb1004s[j].sameBanNo;
			var prefixNo = cedb1004s[j].prefixNo;
			var sameCompanyName = cedb1004s[j].sameCompanyName;
			var revokeAppDate = cedb1004s[j].revokeAppDate || '';

			if(c1001SeqNo == c1004SeqNo) {
				//先以統編為主去抓2000的資料
				$.ajax({
				    type: 'GET',
				    url: getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + sameBanNo,
				    async: false
				}).done(function (data) {
					if(data) {
						var showSameCompanyName = sameBanNo + " " + data.STATUS_CODE + " " + sameCompanyName + " " + revokeAppDate + " ";
						var warningWording = '(統編：' + sameBanNo + ' ' + sameCompanyName + ')，違反公司法第18條規定';
						$("input[name=showSame0"+ (i+1) +"]").after(showSameCompanyName + "<button class='copyToRemark' value='" + warningWording + "' onclick=copyToRemark(this) >複製</button><br/>");
						//$("input[name=showSame0"+ (i+1) +"]").val(showSameCompanyName).after("<button class='copyToRemark' value='" + warningWording + "' onclick=copyToRemark(this) >複製</button>");
					    isDone = true;
					}
				});
				
				if(!isDone) {
					var showSameCompanyName = sameBanNo + " " + " " + sameCompanyName + " " + revokeAppDate + " ";
					var warningWording = '(統編：' + sameBanNo + ' ' + sameCompanyName + ')，違反公司法第18條規定';
				    $("input[name=showSame0"+ (i+1) +"]").val(showSameCompanyName).after("<button class='copyToRemark' value='" + warningWording + "' onclick=copyToRemark(this) >複製</button><br/>");
				}
				
				//沒有統編時
				if(!sameBanNo) {
					var showSameCompanyName =  " " + revokeAppDate + " ";
					var warningWording = '(預查編號：' + prefixNo + ' ' + sameCompanyName + ')，違反公司法第18條規定';
					$("input[name=showSame0"+ (i+1) +"]").after(showSameCompanyName + " <button class='copyToRemark' value='" + warningWording + "' onclick=copyToRemark(this) >複製</button><br/>");
				}
			}
		}
	}
	
	var samePrefixNo, sameBanNo, sameCompanyName;
	for(var k = 0; k < cedb1004s.length; k++) {
		var tr = "";
		samePrefixNo = cedb1004s[k].samePrefixNo;
		if(cedb1004s[k].sameBanNo) {
			tr += "<tr><td>" + cedb1004s[k].sameSeqNo + "</td>" +
				   "<td>" + commonUtils.trimUndefined(cedb1004s[k].samePrefixNo) +"</td>" +
				   "<td>" + cedb1004s[k].sameBanNo + "</td>" +
				   "<td>" + cedb1004s[k].sameCompanyName + "</td>" + 
				   "<td>" + "" + "</td><td /><td /></tr>" ;
		} else if(samePrefixNo) {
			sameBanNo = cedb1004s[k].sameBanNo || '';
			sameCompanyName = cedb1004s[k].sameCompanyName;
			tr = commonUtils.trimUndefined(cedb1004s[k].samePrefixNo) + " " + sameBanNo + " " + sameCompanyName;
			$("input[name=showSame"+ cedb1004s[k].seqNo +"]").val(tr);
		}
		$("#showSame" + cedb1004s[k].seqNo +" tr:last").after(tr);
	}
	
	var newDiv = $('<div />');
	$( document ).ajaxComplete(function( event,request, settings ) {
		$("input[name^='showSame']").click(function() {
			var name = $(this).attr("name");
			var content = $("#" + name).html();
			
			if(!content)
				return;
			
			$(newDiv).html(content);
			$(newDiv).dialog({height: 280, width: 'auto'});
		});
	});
	
	//如果approveResult都是N的時候就自動勾選不予核准
	if($("input[name='approveResult']:checked").length == 0 && $("select[name=approveResult]").val() == 'N') {
		$("input[name='approveResultAllNo']").prop("checked", true);
	}
}

//TODO refactor addRow below.
function addRow() {
	var trSize = $("#cedb1002s tr").size() - 1;

	var cedb1002Html = '<tr class="cedb1002 listTR">'+
	'<td style="text-align: center; width:40px;"><input name="cedb1002Chk" type="checkbox" value=""></td>' +
	'<td style="text-align: center; width:60px"><input class="field_RO inputCenter" name="itemSeqNo" type="text" size="3" value="'+ commonUtils.padZero(trSize, 3) +'" readonly></td>' +
	'<td style="text-align: center; width:100px"><input class="field_Q inputCenter" name="busiItemNo" type="text" size="8" value=""></td>' +
	'<td style="text-align:   left; width:400px"><input class="field_Q" name="busiItem" type="text" size="45" title="" value="">&nbsp;&nbsp;&nbsp;&nbsp;</td>' +
	'<td style="text-align:   left; width:50px"><input class="toolbar_default" type="button" value="  +  "></td>' +
	'<td><input class="field_RO" name="restMark" type="text" size="50" value=""></td>'+
	'</tr>';

	$("#cedb1002s").append(cedb1002Html);
}


//add one row at specific index
function addRowAtIndex(index) {
	var cedb1002Html = '<tr class="cedb1002 listTR">'+
	'<td style="text-align: center; width:40px;"><input name="cedb1002Chk" type="checkbox" value=""></td>' +
	'<td style="text-align: center; width:60px"><input class="field_RO inputCenter" name="itemSeqNo" type="text" size="3" value="" readonly></td>' +
	'<td style="text-align: center; width:100px"><input class="field_Q inputCenter" name="busiItemNo" type="text" size="8" value=""></td>' +
	'<td style="text-align:   left; width:400px"><input class="field_Q" name="busiItem" type="text" size="45" title="" value="">&nbsp;&nbsp;&nbsp;&nbsp;</td>' +
	'<td style="text-align:   left; width:50px"><input class="toolbar_default" type="button" value="  +  "></td>' +
	'<td><input class="field_RO" name="restMark" type="text" size="50" value=""></td>'+
	'</tr>';

	$("#cedb1002s tr").eq(index).after(cedb1002Html);
	resetCedb1002SeqNo();
}

function cedb1002AddRow(dom) {
	
	var cedb1002Html = '<tr class="cedb1002 listTR">'+
	'<td style="text-align: center; width:40px;"><input name="cedb1002Chk" type="checkbox" value=""></td>' +
	'<td style="text-align: center; width:60px"><input class="field_RO inputCenter" name="itemSeqNo" type="text" size="3" value="" readonly></td>' +
	'<td style="text-align: center; width:100px"><input class="field_Q inputCenter" name="busiItemNo" type="text" size="8" value=""></td>' +
	'<td style="text-align:   left; width:400px"><input class="field_Q" name="busiItem" type="text" size="45" title="" value=""><font color="red">(新增)</font></td>' +
	'<td style="text-align:   left; width:50px"><input class="toolbar_default" type="button"  value="  +  "></td>' +
	'<td><input class="field_RO" name="restMark" type="text" size="50" value=""></td>'+
	'</tr>';
	
	$(dom).closest('tr').after(cedb1002Html).next().find("input[name='busiItemNo']").focus();
	resetCedb1002SeqNo();
	commonUtils.changeInputToSpan('input[name=restMark]', "#cedb1002s");
	
}

function ajaxStopInit() {
	
	$( document ).ajaxStop(function() {
		var setupIsChked = $("input[name=setup]").prop("checked");
		if(setupIsChked == true) {
			$("input[name=banNo]").prop("readonly", true);
		}
		//addToolTip();
		addReadOnly();
		commonUtils.changeInputToSpan('input[name=restMark]', "#cedb1002s");
		//commonUtils.changeInputToSpan('input[name^=showSame]', "#cedb1001s");
		
		$("input[type=button][value='+']", "#cedb1002s").attr('value', '  +  ');
		addRestMarkInfo();
		
		$("#cedb1002s").on('blur', 'input[name=busiItemNo]', function() {
			checkDuplicateItemCode();
			var restrictionWording = itemRests[$(this).val()] || '';
			$(this).closest('tr').find('td').eq(5).find('span').text( restrictionWording );
			$(this).val(this.value.toUpperCase());
		});
		
		addTabindexToCedb1002();
		resetKeyin();
		changeOrgnType();
		setSpecialName();
		//setAtLeastCompanyName();
		disableApproveResult();
		
		$("#cedb1001s input[name=seqNo]").prop("size", 1);
		
		//如果approveResult都是N的時候就自動勾選不予核准
		if($("input[name='approveResult']:checked").length == 0 && $("select[name=approveResult]").val() == 'N') {
			$("input[name='approveResultAllNo']").prop("checked", true);
		}
		
		$('#save, #tempSave, #assignBtn').prop('disabled', false);
		
		//只有所營變更不顯示本次預查名稱   103/10/1優仲要求改為不論選擇何種案由均顯示本次預查名稱, 因此將以下兩行註解
		/*
		if( $("input[name=changeItem]").is(':checked') && !($("input[name=changeName]").is(':checked') || $("input[name=setup]").is(':checked'))) {
			$("input[name=companyName]", '#fragment-1').val('');
		}
		*/
	
	});
	
	$("input[name=setup]").change(function() {
		var setupIsChked = $("input[name=setup]").is(":checked");
		if(setupIsChked) {
			$("input[name=changeName],input[name=changeItem]").prop("checked", false);
			$("input[name=banNo]").val('').prop("readonly", true).addClass("field_RO");
		} else {
			$("input[name=banNo]").prop("readonly", false).removeClass("field_RO");
			$("input[name=specialName]").val('');
		}
	});
	
	$("input[name=changeName],input[name=changeItem]").change(function() {
		var isChecked = $("input[name=changeName]").is(":checked") || $("input[name=changeItem]").is(":checked");
		if (!$("input[name=changeName]").is("checked")) {
			$("input[name=specialName]").val('');
		}
		
		if(isChecked) {
			$("input[name=banNo]").prop("readonly", false).removeClass("field_RO");
			$("input[name=setup]").prop("checked", false);
		} else {
			$("input[name=banNo]").prop("readonly", true).addClass("field_RO");
		}
	});
	
	busiItemListener();
	unbindShowSameifEmpty();
	bindAddRowEvt();
}

function addSearchMsg(msg) {
	$("#ERRMSG").text(msg);
}

function addToolTip() {
	commonUtils.addToolTip("input[name^=showSame],input[name=remark],input[name=busiItem]");
}

function addReadOnly() {
	$("input[name^=showSame]")
		.attr({"readonly" : true}).addClass("inputNoBorder").css("background-color", "#F3EFEF");
	$("input[name^=showPreSearch]")
		.attr({"readonly" : true}).addClass("inputNoBorder").css("background-color", "#F3EFEF");
}

function setCedb1022(cedb1022) {
	if(cedb1022) {
		$('input[name=applyLawName]').val(cedb1022.applyLawName);
		$("input[name=applyBanNo]").val(cedb1022.applyBanNo);
	}
}


function getAllItemCode() {
	$.post(getVirtualPath() + "tcfi/ajax/jsonCedbc055s.jsp", function(data) {
		
		if(data) {
			$.each(data, function(i, v) {
				allBusinessItems[data[i].itemCode] = data[i].businessItem;
			});
		}
	});
}

function checkItemCode(itemCode) {
	var itemValue = allBusinessItems[itemCode];
	if(!itemValue && itemCode != '') {
		alert("沒有這項營業項目代碼！");
		return;
	} else {
		return itemValue;
	}
}

function addRestMarkInfo() {
	var $busiItemNo, info, restInfo;
	$("input[name=busiItemNo]").each(function(i, v) {
		$busiItemNo = $(v);
		info = busItemComporgs[$busiItemNo.val()];    // from Constants
		restInfo = itemRests[$busiItemNo.val()];      // from DB
		if (info && restInfo) {
			$busiItemNo.closest("tr").find("input[name=restMark]").next().text(restInfo+"，"+info);
		}else if(info) {
			//$busiItemNo.closest("tr").find("input[name=restMark]").val(info);
			$busiItemNo.closest("tr").find("input[name=restMark]").next().text(info);
		}else if(restInfo) {
			$busiItemNo.closest("tr").find("input[name=restMark]").next().text(restInfo);
		}
	});
}

function checkCedb1002s() {
	
	if(!checkOnlyOneZZ99999()) {
		return false;
	}
	
	$("input[name=busiItemNo]", "#cedb1002s").each(function(i, v) {
		//console.log(i +  $(v).closest("tr").find("input[name=busiItem]").val() );
		if( $(v).closest("tr").find("input[name=busiItem]").val() == '') {
			alert("有營業代碼必須輸入營業項目中文");
			return false;
			//throw '';
		}
	});
	if ($("input[name=orgType]").val() == "05") {
		if (!checkBusiItemLimitCannotApply()) {
			return false;
		}
	}
	return true;
}

function checkOnlyOneZZ99999() {
	if($("input[name=busiItemNo]", "#cedb1002s").size() == 1 && $("input[name=busiItemNo]", "#cedb1002s").val() == 'ZZ99999') {
		if( $('#cmpySetupDate') ) {
			if( $('#cmpySetupDate').val() < '0980701' ) {
				return true;
			}
		}
		alert("營業項目不能只有一項ZZ99999");
		return false;
	}
	return true;
}

function checkBusiItemLimitCannotApply() {
	var count = 0;
	$("input[name=busiItemNo]", "#cedb1002s").each(function(i, v) {
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'IZ17010') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'I701021') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'H203011') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'E801060') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'G101061') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'J701010') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'GA01010') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'H106010') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
		if( $(v).closest("tr").find("input[name=busiItemNo]").val() == 'H501040') {
			alert("有限合夥營業項目不能申請" + $(v).closest("tr").find("input[name=busiItemNo]").val() + " " + $(v).closest("tr").find("input[name=busiItem]").val() );
			count++;
		}
	});
	
	if (count == 0)
		return true;
	else
		return false;
}

function bindAddRowEvt() {
	$('#cedb1002s').on('click', 'input[value="  +  "]', function() {
		//var index = $addBtn.index( this );
		cedb1002AddRow(this);
	});
}

function resetCedb1002SeqNo() {
	var base = 0;
	$(".cedb1002").each(function(i, v) {
		$(this).find("input[name=itemSeqNo]").val( commonUtils.padZero(i+1, 3) );
		$(this).find("input[name=busiItemNo]").attr('tabindex', base+1);
		$(this).find("input[name=busiItem]").attr('tabindex', base+=2);
	});
	diffOldBusiItem();
}

function addTabindexToCedb1002() {
	var base = 0;
	$(".cedb1002").each(function(i, v) {
		$(this).find("input[name=busiItemNo]").attr('tabindex', base+1);
		$(this).find("input[name=busiItem]").attr('tabindex', base+=2);
	});
}

/**
 * 檢查是否有重複的營業項目
 * 有重複時回傳true
 * @returns {Boolean}
 */
function checkDuplicateItemCode() {
	var codes = {};
	var hasDuplicate = false;
	var itemCode;
	$("input[name=busiItemNo]").each(function(i, v) {
		itemCode = $(v).val();
		if( !codes[itemCode] ) {
			codes[itemCode] = itemCode;
		} else {
			alert(itemCode + "營業項目重複輸入，請檢查");
			hasDuplicate = true;
			return false;
		}
	});
	
	return hasDuplicate;
}

function init() {
	var prefixNo = commonUtils.getURLParameter("prefixNo");
	var selectCases = commonUtils.getURLParameter("selectCases");
	if(prefixNo)
		prefixNos = prefixNo.match(/[^-]+/g) || [];
	else {
		prefixNo = $('input[name="prefixNos"]').map(function() {
			return $(this).val();
		}).get().join('-');
		prefixNos = prefixNo.match(/[^-]+/g) || [];
	}
	
	if(prefixNo.indexOf("-") != -1) {
		$.cookie("prefixNos", JSON.stringify(prefixNos));
	} else if(selectCases) {
		prefixNos.push(prefixNo);
		$.cookie("prefixNos", JSON.stringify(prefixNos));
	}

	if(prefixNos[0]) {
		$("#prefixNo").val(prefixNos[0]);
	}
		
	setDisplayItem("spanClear,spanUpdate,spanDelete,spanListPrint,spanListHidden", "H");
}

function insertItem(itemCode, item) {
	var $tr;
	if(ilocation == -1) {
		addRow(); //add row at last one.
		$tr = $("#cedb1002s tr").eq(ilocation);
	} else {
		addRowAtIndex(ilocation); //add row at specific index
		$tr = $("#cedb1002s tr").eq(ilocation+1);
	}
	
	$tr.find("input[name='busiItemNo']").val(itemCode);
    $tr.find("input[name='busiItem']").val(item).attr("title", item);
    $tr.find("input[name='restMark']").val(searchRest(itemCode));
    commonUtils.changeInputToSpan('input[name=restMark]', "#cedb1002s");
}

/**
 * 重設發文登打
 */
function resetKeyin() {
	$("input[name=div4status8]", "#cedb1010s").each(function(i, v) {
		if(v.value.indexOf('發文登打') != -1) {
			canResetKeyin = true;
			return;
		}
	});
	
	if(!canResetKeyin) {
		$("#resetClose").prop('disabled', true);
	}
	else {
		$("#resetClose").prop('disabled', false);
	}
}

function copyToRemark(dom) {
	var tempRemark = $(dom).attr('value');
	var trId = $(dom).data("src");
	$("#cedb1001s [name=seqNo][value="+ trId +"]")
		.closest("tr")
		.find("[name=remark]")
		.val(tempRemark)
		.prop('title', tempRemark);
	//for IE8 issue
	event.preventDefault ? event.preventDefault() : event.returnValue = false;
}

function changeOrgnType() {

	$(".orgnType").change(function() {
		var type = this.value;
		
		if(type=="XX")
		 	return;
		if (type == "股份有限公司") {
			$("input[name=orgType]").val("01");
		} else if (type == "有限公司") {
			$("input[name=orgType]").val("02");
		} else if (type == "無限公司") {
			$("input[name=orgType]").val("03");
		} else if (type == "兩合公司") {
			$("input[name=orgType]").val("04");
		} else if (type == "有限合夥") {
			$("input[name=orgType]").val("05");
		}
		
		var cmpyName = $(this).prev().val();
		if (cmpyName.indexOf("股份有限公司") > 0) {
			rootCmpyName = cmpyName.substring(0, cmpyName.indexOf("股份有限公司"));
			cmpyName = rootCmpyName + type;
		} else if (cmpyName.indexOf("有限公司") > 0) {
			rootCmpyName = cmpyName.substring(0, cmpyName.indexOf("有限公司"));
			cmpyName = rootCmpyName + type;
		} else if (cmpyName.indexOf("無限公司") > 0) {
			rootCmpyName = cmpyName.substring(0, cmpyName.indexOf("無限公司"));
			cmpyName = rootCmpyName + type;
		} else if (cmpyName.indexOf("兩合公司") > 0) {
			rootCmpyName = cmpyName.substring(0, cmpyName.indexOf("兩合公司"));
			cmpyName = rootCmpyName + type;
		} else if (cmpyName.indexOf("有限合夥") > 0) {
			rootCmpyName = cmpyName.substring(0, cmpyName.indexOf("有限合夥"));
			cmpyName = rootCmpyName + type;
		}
		$(this).prev().val(cmpyName);
	});
}

function syncRremark1() {
	var $syncRemark = $("[name=sync-remark1]");
	var $remark = $("[name=remark1]");
	
	$syncRemark.val($remark.val());
	
	$syncRemark.blur(function() {
		$remark.val($(this).val());
	});
	
	$remark.blur(function() {
		$syncRemark.val($(this).val());
	});
}

function syncApproveRemark() {
	var $syncApproveRemark = $("[name=sync-approveRemark]");
	var $approveRemark = $("[name=approveRemark]");
	
	$syncApproveRemark.val($approveRemark.val());
	
	$syncApproveRemark.blur(function() {
		$approveRemark.val($(this).val());
	});
	
	$approveRemark.blur(function() {
		$syncApproveRemark.val($(this).val());
	});
}

/**
 * 公司名稱tab focusOut 後可能觸發的相關事件
 */
function setSpecialName() {
	var $approveResult = $("input[name=approveResult]");
	// TCFI-UT-PRE3001-29 預查審核中案件不應出現本次預查名稱  103/10/20
	// 這兩段判斷放在下面那個if則不會進去
	// 詳細的原因還要再研究一下，因此暫且將這兩段拉出來
	//如果都沒選就是審查中
	if(!$("input[name=approveResult]").is(":checked") && !$("input[name=approveResultAllNo]").is(":checked")) {
		$("select[name=approveResult] option[value='A']").prop('selected', true);
		//$("input[name=specialName]").val('');
		$("input[name=companyName]", "#fragment-1").val('');
		$("#xCompanyName").html('');
	}
	
	// 不予核准
	if( $("input[name=approveResultAllNo]").is(':checked') ) {
		//$("input[name=specialName]").val('');
		$("select[name=approveResult] option[value='N']").prop('selected', true);
		$("input[name=companyName]", "#fragment-1").val('');
		$("#xCompanyName").html('');
	}
	
	
	
	$("input", "#cedb1001s").on('click, blur',function() {
//		if(!this.checked)
//			return;
		
		var specialName = $(this).closest("tr").find('[name=companyName]').next().val();
		var isApproveChecked = $(this).closest("tr").find('[name=approveResult]').is(':checked');
		if(specialName && isApproveChecked && ($("input[name=changeName]").is(':checked') || $("input[name=setup]").is(':checked'))) {
			$("input[name=specialName]").val(specialName);
		}
		else {
			$("input[name=specialName]").val('');
		}
		
		var companyName = $(this).closest("tr").find('[name=companyName]').val();
		if(companyName && isApproveChecked) {
			$("#fragment-1 input[name=companyName]").val(companyName);
			$("#xCompanyName").html(companyName);
		}
		
		$("select[name=approveResult] option[value='Y']").prop('selected', true);
		
		//如果都沒選就是審查中
		if(!$("input[name=approveResult]").is(":checked") && !$("input[name=approveResultAllNo]").is(":checked")) {
			$("select[name=approveResult] option[value='A']").prop('selected', true);
			$("input[name=specialName]").val('');
			$("input[name=companyName]", "#fragment-1").val('');
			$("#xCompanyName").html('');
		}
		
		// 不予核准
		if( $("input[name=approveResultAllNo]").is(':checked') ) {
			$("input[name=specialName]").val('');
			$("select[name=approveResult] option[value='N']").prop('selected', true);
			$("input[name=companyName]", "#fragment-1").val('');
			$("#xCompanyName").html('');
		}
		
	});
	
}

function selectApplyKind(applyKind) {

	$input = $('<input>').attr({
	    type: 'hidden',
	    name: 'applyKind'
	}).appendTo('form');
	
	if(applyKind == '設立預查') {
		$('input[name=setup]').attr('checked', true);
		$input.val(1);
	} else if(applyKind == '名稱變更') {
		$('input[name=changeName]').attr('checked', true);
		$input.val(2);
	} else if(applyKind == '所營變更') {
		$('input[name=changeItem]').attr('checked', true);
		$input.val(3);
	} else if(applyKind == '名稱及所營變更') {
		$('input[name=changeName]').attr('checked', true);
		$('input[name=changeItem]').attr('checked', true);
		$input.val(4);
	}
}

/**
 * 申請人id檢核
 * @returns {Boolean}
 */
function checkApplyId() {
	if (/^[A-Z]{1}/.test($("input[name=applyId]").val()) && $("input[name=applyId]").val().length > 10 ) {
		alert('身分ID不可超過10碼!');
		return false;
	} else if ($("input[name=applyId]").val().length > 20) { //大陸人為18碼數字
		alert('身分ID不可超過20碼!');
		return false;
	}
	return true;
}

/**
 * 附件檢核
 * @returns {Boolean}
 */
function chkAttachment() {
	if( !!$("input[name=isPrefixForm]:checked").val() ) {
		if( $("input[name=prefixFormNo]").val().length != 9 ) {
			alert('附件之預查編號必填且須符合規範');
			return false;
		}
	}
	if( $("input[name=prefixFormNo]").val() != '' ) {
		if( !$("input[name=isPrefixForm]").prop("checked") ) {
			alert('有填預查編號時附件必須勾選');
			return false;
		}
	}
	
	if( !!$("input[name=isOtherSpec]:checked").val() ) {
		if( $("input[name=otherSpecRemark]").val() == '' ) {
			alert('附件之其他欄位勾選時註記必須填寫');
			return false;
		}
	}
	if ($("input[name=otherSpecRemark]").val() != '' ) {
		if( !$("input[name=isOtherSpec]").prop("checked") ) {
			alert('有填寫註記時需勾選附件之其他欄位');
			return false;
		}
	}
	
	return true;
}

function checkGetKind() {
	if( $("input[name=getKind]:checked").val() == 2 && $("input[name=getKindRemark]").val() == '' ) {
		alert("未填寫郵寄註記");
    	return false;
	} else {
		return true;
	}
}

/**
 * 所營變更時比較舊的營業項目(diff加上新增字樣)
 */
function diffOldBusiItem() {
	
	if(!oldBusiItems) {
		var banNo = $("input[name=banNo]").val();
		if( !banNo ) return;
		
		$.ajax({
		    type: 'POST',
		    url: getVirtualPath() + "tcfi/ajax/jsonOldBusItem.jsp?q=" + banNo ,
		    async: false
		}).done(function(data) {
			oldBusiItems = data;
		});
	}
		
	var $target;
	if(oldBusiItems) {
		$.each($("[name=busiItemNo]", "#cedb1002s"), function(i, v) {
			if( $(v).val() != '' ) {
				$target = $(v).closest('tr').find('td').eq(3);
				if(!oldBusiItems[$(v).val()] && $target.find('font').size() == 0) {
					$target.append("<font color='red'>(新增)</font>");
				}
			}
		});
	}
}

function checkReserve365() {
	
	var itemCode;
	var isValidate = true;
	
	//此次案件不為設立時不需檢核此規則
	if($('input[name=setup]').is(':checked')) {
		return isValidate;
	}
	
	
	$("input[name=busiItemNo]", "#cedb1002s").each(function(i, v) {
		itemCode = $(v).val();
		if(jsonBusItemReserve365s[itemCode] && !$("[name=reserveDays][value=365]").is(":checked")  && oldBusiItems && !oldBusiItems[itemCode]) {
			alert("營業項目" + itemCode + "保留期限為一年，建議修改保留期限為保留一年");
		}
	});
	
	return isValidate;
}

function getApplyKind() {
	if( $("input[name=setup]").is(":checked") ) {
		return 0; //設立
	} else if ($("input[name=changeName]").is(":checked") && $("input[name=changeItem]").is(":checked") ) {
		return 3; //名稱變更 + 所營變更
	} else if( $("input[name=changeName]").is(":checked") ) {
		return 1; //名稱變更
	} else if( $("input[name=changeItem]").is(":checked") ) {
		return 2; //所營變更
	} else {
		return "";
	}
}

function getApplyKindSave() {
	if( $("input[name=setup]").is(":checked") ) {
		return 1; //設立
	} else if ($("input[name=changeName]").is(":checked") || $("input[name=changeItem]").is(":checked") ) {
		return 2; //變更
	} else {
		return "";
	}
}

function approveResultListener() {
	$("select[name=approveResult]", "#fragment-1").change(function() {
		if(this.value == 'N') {
			$("input[name=specialName]").val('');
			$("input[name=companyName]", "#fragment-1").val('');
			$("#xCompanyName").html('');
			$("input[name=approveResult]").prop('checked', false);
			$("input[name=approveResultAllNo]").prop('checked', true);
		} else if(this.value == 'Y' && !$("input[name=companyName]").val() ) {
			alert('請注意，您選擇核准保留，但公司沒有勾選欲核准的公司名稱');
		} else if(this.value == 'A') {
			$("input[name=specialName]").val('');
			$("input[name=companyName]", "#fragment-1").val('');
			$("#xCompanyName").html('');
		}
	});
}

function setAtLeastCompanyName() {
	if(!$("input[name=companyName]", "#cedb1001s").eq(0).val() && !$('input[name="companyName"]', '#cedb1001s').eq(0).val() && $("input[name=changeItem]").is(":checked")) {
		$.post(getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + $("input[name=banNo]").val(), function(data) {
			if(!data) return;
			$("input[name=companyName]", "#fragment-1").val('');
			$("input[name=companyName]", "#cedb1001s").eq(0).val( data.COMPANY_NAME );
		});
	}
}

/**
 * 監聽busiItem 是否觸發tab key，按下時會新增一列並重新focus
 */
function busiItemListener() {
	$("#cedb1002s").on("keydown", "input[name=busiItem]" , function(e) {
		
		var keyCode = e.keyCode || e.which;
		var $tr = $(this).closest('tr');
		var tabIndex = this.tabIndex;
		
	    if(keyCode == 9) { //tab keycode
	    	$tr.find('input[type=button]').click();
	    	$("input[tabindex="+ tabIndex + "]", "#cedb1002s").focus();
	    }
	});
}

function unbindShowSameifEmpty() {
	$(document).ajaxStop(function() {
		$("input[name^=showSame]").each(function(i, v) {
			// if(!$(v).val() || $("input[name=changeItem]").is(':checked') ) //空值, 所營變更時不用顯示同名
			// 103/10/02 設立與名稱變更如果有打勾還是應該要顯示同名公司名稱
			if ( $("input[name=changeItem]").is(':checked') && !$("input[name=changeName]").is(':checked') && !$("input[name=setUp]").is(':checked') )
				$(v).remove();
		});
	});
}

/**
 * 設立或名稱變更且為核准保留時特取為必填
 */
function checkSpecialName() {
	if ($("select[name=approveResult]", "#fragment-1").val() == 'Y' &&
		 ( $("input[name=setup]").is(':checked') || $("input[name=changeName]").is(':checked')) ) {
					
		if(!$("input[name=specialName]").val()) {
			alert('請注意，設立或名稱變更且狀態為核准保留時，特取名稱為必填欄位。');
			return false;
		}
	}
	
	return true;
}

/**
 * 發文登打完成後不可再修改核覆結果
 */
function disableApproveResult() {
	if($("input[name=prefixStatus]").val() == 7 || $("input[name=prefixStatus]").val() == 8) {
		$("select[name=approveResult]").prop('disabled', true);
	}
}

/**
 * 組合營業項目CEDB1002
 */
function setCedb1002Row(cedb1002s){
	if(!cedb1002s)	return;
	if($("#cedb1002s tr").length == 2) {
		var cedb1002Html = "";
		var seqNo = "";
		var busiItem = "";
		var busiItemNo = "";
		var tr_class = "";
		for(var i=0; i<cedb1002s.length; i++) {
			if(i%2==0) tr_class = "listTREven";
			else tr_class = "listTROdd";
			seqNo = cedb1002s[i].seqNo;
			busiItem = cedb1002s[i].busiItem;
			busiItemNo = cedb1002s[i].busiItemNo||'';
			cedb1002Html += 
				'<tr class="cedb1002 '+tr_class+'">'+
				'	<td style="text-align:center;width:40px;">' +
				'		<input name="cedb1002Chk" type="checkbox" value="'+ busiItemNo +'">' +
				'	</td>' +
				'	<td style="text-align:center;width:60px">' +
				'		<input class="field_RO inputCenter" name="itemSeqNo" type="text" size="3" value="' + seqNo +'" readonly>' +
				'	</td>' +
				'	<td style="text-align:center;width:100px">' +
				'		<input class="field_Q inputCenter" name="busiItemNo" type="text" size="8" value="'+ busiItemNo +'">' +
				'	</td>' +
				'	<td style="text-align:left;width:400px">' +
				'		<input class="field_Q" name="busiItem" type="text" size="45" title="" value="'+ busiItem +'">' +
				'	</td>' +
				'	<td style="text-align:left;width:50px">' +
				'		<input class="toolbar_default" type="button"  value="  +  ">' + 
				'	</td>' +
				'	<td>' +
				'		<input class="field_REMARK" name="restMark" type="text" size="50" value="">' + 
				'	</td>' +
				'</tr>';
		
		}
		$("#cedb1002s tr").eq(1).after(cedb1002Html);
		diffOldBusiItem();
	}
}

/**
 * 組合案件流程CEDB1010
 */
function setCedb1010Row(cedb1010s){
	if(!cedb1010s)	return;
	if($("#cedb1010s tr").length == 1) {
		var cedb1010Html = "";
		var processDateTime = "";
		var processStatus = "";
		var workDay = "";
		var processor = "";
		var tr_class = "";
		for(var i=0; i<cedb1010s.length; i++) {
			if(i%2==0) tr_class = "listTREven";
			else tr_class = "listTROdd";
			processDateTime = cedb1010s[i].processDate;
			workDay = cedb1010s[i].workDay;
			processStatus = cedb1010s[i].processStatus;
			processor = cedb1010s[i].idNo;
			
			cedb1010Html += '<tr class="'+tr_class+'">' +
			'<td style="text-align:left">'+ (i+1) +'</td>' +
			// 103/10/21 優仲問為何重設發文按鈕是被disabled的狀態; 言下之意為案件審核及登錄應該也可以重設發文
			// 			 因此將以下這行改寫
			'<td style="text-align:left"><input class="field_RO" readonly name="div4status8" type="hidden" size="25" value="'+ processStatus +'">'+ processStatus +'</td>' +
			'<td style="text-align:left">'+ processDateTime +'</td>' +
			'<td style="text-align:left">'+ processor +'</td>' +
			'<td style="text-align:left">'+ workDay +'</td>' +
			'</tr>';
		}
		$("#cedb1010s tr").eq(0).after(cedb1010Html);
	}
}

/**
 * 存檔時將附件的值放進prefixVo
 * PRE1006, PRE3001
 */
function setAttachment(data){
	data.isPrefixForm = $("input[name=isPrefixForm]").is(":checked")?"Y":null;
	data.isOtherForm = $("input[name=isOtherForm]").is(":checked")?"Y":null;
	data.isSpec = $("input[name=isSpec]").is(":checked")?"Y":null;
	data.isOtherSpec = $("input[name=isOtherSpec]").is(":checked")?"Y":null;
	return data;
}