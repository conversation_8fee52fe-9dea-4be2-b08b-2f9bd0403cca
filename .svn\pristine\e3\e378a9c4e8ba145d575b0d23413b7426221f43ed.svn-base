package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.Cedbc004;
import com.kangdainfo.tcfi.view.pre.PRE8012;

/**
 * 禁用字詞維護(PRE8012)
 *
 */
public interface Pre8012Service {

	/**
	 * 查詢
	 * @param condition
	 * @return
	 */
	public List<Cedbc004> queryAll(PRE8012 condition);

	/**
	 * 查詢單筆
	 * @param seqNo
	 * @return
	 */
	public Cedbc004 queryBySeqNo(String seqNo);
	
	/**
	 * 查詢單筆
	 * @param banWord
	 * @return
	 */
	public Cedbc004 queryByBanWord(String banWord);

	/**
	 * 新增
	 * @param obj
	 * @return
	 */
	public void insertCedbc004(Cedbc004 obj);

	/**
	 * 修改
	 * @param obj
	 * @return
	 */
	public Cedbc004 updateCedbc004(Cedbc004 obj);

	/**
	 * 刪除
	 * @param obj
	 */
	public void deleteCedbc004(Cedbc004 obj);

	/**
	 * 檢查禁用名稱是否存在
	 * @param banWord 禁用名稱
	 * @return 存在（true） / 不存在（false）
	 */
	public boolean isBanWordExits(String banWord);

}