package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;

public class PostRecordDao extends BaseDaoJdbc implements RowMapper<PostRecord> {

	private static final String SQL_defaultOrder = "ORDER BY GET_DATE, GET_TIME";

	private static final String SQL_findAll = "SELECT * FROM POST_RECORD";
	public List<PostRecord> SQL_findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByPrefixNo = "SELECT * FROM POST_RECORD WHERE PREFIX_NO = ? and ( post_no is not null )";
	public List<PostRecord> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findLastOneByPrefixNo = "SELECT * FROM POST_RECORD WHERE PREFIX_NO = ? ";
	public PostRecord findLastOneByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findLastOneByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(" and post_no is not null ");
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PostRecord> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(),this);
		
		return list.isEmpty() ? null : list.get(list.size()-1);
	}
	
	public PostRecord findMainByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findLastOneByPrefixNo);
		sqljob.addParameter(prefixNo);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PostRecord> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(),this);
		
		return list.isEmpty() ? null : list.get(list.size()-1);
	}

	private static final String SQL_findByPrefixNoAndPostNo = "SELECT * FROM POST_RECORD WHERE PREFIX_NO=? AND POST_NO=?";
	public PostRecord findByPrefixNoAndPostNo(String prefixNo, String postNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndPostNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(postNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PostRecord> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public List<PostRecord> findBetween( String numStart, String numEnd, String type, String postType ) {
		SQLJob sqljob = new SQLJob("SELECT * FROM POST_RECORD") ;
		if ("prefix".equals(type))
			sqljob.appendSQL(" WHERE PREFIX_NO BETWEEN ? AND ? ");
		else
			sqljob.appendSQL(" WHERE POST_NO BETWEEN ? AND ? ");
        sqljob.appendSQL(" AND POST_TYPE = ? ");
        if ( "03".equals(postType) ) {
        	sqljob.appendSQL(" AND POST_NO IS NOT NULL");
        } // if
        sqljob.appendSQL("ORDER BY Post_NO asc");
		sqljob.addParameter(numStart);
		sqljob.addParameter(numEnd);
		sqljob.addParameter(postType);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this) ;
	}

	public int insert(PostRecord obj) {
		if ( obj == null )
			return 0;
		PostRecord pr = findByPrefixNoAndPostNo(obj.getPrefixNo(), obj.getPostNo());
		
		if(pr != null) {
	        return 0;
		}
		
		SQLJob sqljob = new SQLJob("INSERT INTO POST_RECORD (") ;
		sqljob.appendSQL(" PREFIX_NO");
		sqljob.appendSQL(",POST_NO");
		sqljob.appendSQL(",POST_TYPE");
		sqljob.appendSQL(",GET_NAME");
		sqljob.appendSQL(",GET_ADDR");
		sqljob.appendSQL(",GET_DATE");
		sqljob.appendSQL(",GET_TIME");
		sqljob.appendSQL(",GET_KIND");
		sqljob.appendSQL(",GET_KIND_REMARK");
		sqljob.appendSQL(",BACK_DATE");
		sqljob.appendSQL(",BACK_TIME");
		sqljob.appendSQL(",BACK_REASON");
		sqljob.appendSQL(",BACK_REASON_REMARK");
		sqljob.appendSQL(",OTHER_METHOD");
		sqljob.appendSQL(",OTHER_METHOD_REMARK");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(",APPLY_NAME");
		sqljob.appendSQL(",APPLY_ID");
		sqljob.appendSQL(",APPLY_TEL");
		sqljob.appendSQL(",APPLY_ADDR");
		sqljob.appendSQL(",COMPANY_NAME");
		sqljob.appendSQL(") VALUES (");
		sqljob.appendSQL("?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?");
		sqljob.appendSQL(")");
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPostNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPostType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKindRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		
		sqljob.addParameter(obj.getBackDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBackReasonRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getOtherMethod());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getOtherMethodRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getModIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getModDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getModTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyId());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApplyAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	} 

	public int update(PostRecord obj) {
		if ( obj == null )
			return 0;
		SQLJob sqljob = new SQLJob("UPDATE POST_RECORD SET") ;
		sqljob.appendSQL(" POST_NO=?");
		sqljob.addParameter(obj.getPostNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(" ,POST_TYPE=?");
		sqljob.addParameter(obj.getPostType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_NAME=?");
		sqljob.addParameter(obj.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_ADDR=?");
		sqljob.addParameter(obj.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_DATE=?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_TIME=?");
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_KIND=?");
		sqljob.addParameter(obj.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",GET_KIND_REMARK=?");
		sqljob.addParameter(obj.getGetKindRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_DATE=?");
		sqljob.addParameter(obj.getBackDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_TIME=?");
		sqljob.addParameter(obj.getBackTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_REASON=?");
		sqljob.addParameter(obj.getBackReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",BACK_REASON_REMARK=?");
		sqljob.addParameter(obj.getBackReasonRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",OTHER_METHOD=?");
		sqljob.addParameter(obj.getOtherMethod());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",OTHER_METHOD_REMARK=?");
		sqljob.addParameter(obj.getOtherMethodRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",MOD_ID_NO=?");
		sqljob.addParameter(obj.getModIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",MOD_DATE=?");
		sqljob.addParameter(obj.getModDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",MOD_TIME=?");
		sqljob.addParameter(obj.getModTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",APPLY_NAME=?");
		sqljob.addParameter(obj.getApplyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",APPLY_ID=?");
		sqljob.addParameter(obj.getApplyId());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",APPLY_TEL=?");
		sqljob.addParameter(obj.getApplyTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL(",APPLY_ADDR=?");
		sqljob.addParameter(obj.getApplyAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		
		sqljob.appendSQL(",COMPANY_NAME=?");
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		
		sqljob.appendSQL("WHERE PREFIX_NO=?");
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL("AND GET_DATE=?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.appendSQL("AND GET_TIME=?");
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public int delete(PostRecord postRecord) throws Exception {
		
		SQLJob sqljob = new SQLJob("DELETE FROM POST_RECORD") ;
		sqljob.appendSQL("WHERE PREFIX_NO=?");
		sqljob.appendSQL("and get_date=?");
		sqljob.appendSQL("and get_time=?");
		sqljob.addParameter(postRecord.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(postRecord.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(postRecord.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public PostRecord mapRow(ResultSet rs, int idx) throws SQLException {
		PostRecord obj = null;
		if(null!=rs) {
			obj = new PostRecord();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setPostNo(rs.getString("POST_NO"));
			obj.setPostType(rs.getString("POST_TYPE"));
			obj.setGetName(rs.getString("GET_NAME"));
			obj.setGetAddr(rs.getString("GET_ADDR"));
			obj.setGetDate(rs.getString("GET_DATE"));
			obj.setGetTime(rs.getString("GET_TIME"));
			obj.setGetKind(rs.getString("GET_KIND"));
			obj.setGetKindRemark(rs.getString("GET_KIND_REMARK"));
			obj.setBackDate(rs.getString("BACK_DATE"));
			obj.setBackTime(rs.getString("BACK_TIME"));
			obj.setBackReason(rs.getString("BACK_REASON"));
			obj.setBackReasonRemark(rs.getString("BACK_REASON_REMARK"));
			obj.setOtherMethod(rs.getString("OTHER_METHOD"));
			obj.setOtherMethodRemark(rs.getString("OTHER_METHOD_REMARK"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setApplyId(rs.getString("APPLY_ID"));
			obj.setApplyTel(rs.getString("APPLY_TEL"));
			obj.setApplyAddr(rs.getString("APPLY_ADDR"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
		}
		return obj;
	}
	
	public int setWhenPre2003(PostRecord obj) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("UPDATE POST_RECORD SET");
		sqljob.appendSQL(" GET_DATE=?");
		sqljob.appendSQL(",GET_TIME=?");
		sqljob.appendSQL(",GET_KIND=?");
		sqljob.appendSQL(",GET_KIND_REMARK=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getGetKindRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

}