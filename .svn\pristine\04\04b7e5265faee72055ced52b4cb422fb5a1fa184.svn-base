<!DOCTYPE html>
<!-- 
程式目的：營業項目代碼維護
程式代號：pre8011
程式日期：1030611
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8011">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8011" />
</jsp:include>

<%
if ("update".equals(obj.getState())) {
	obj.doSaveRestriction();
}
objList = (java.util.ArrayList) obj.queryAllRestriction();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;

function init() {
	window.moveTo(0,0);
	window.resizeTo(screen.width,screen.height*0.96);
}

function oneClickCheckAll(obj,cName) { 
    var checkboxs = document.getElementsByName(cName); 
    for(var i=0;i<checkboxs.length;i++){checkboxs[i].checked = obj.checked;} 
}

$(document).ready(function() {
	$('#doSave').click(function(){
		if(!confirm("確定存檔?"))	return false;
		$('#state').val("update");
		form1.submit();
	});

});
</script>
</head>
<body topmargin="5" onLoad="init();">
<form id="form1" name="form1" method="post" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8011'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- 新增按鈕區 -->
<tr><td>
	<input class="toolbar_default" type="button" followPK="false" id="doSave" name="doSave" value="存　檔">
	<input type="hidden" id="itemCode" name="itemCode" value="<%=obj.getItemCode()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
</td></tr>

<tr><td class="bg" >
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="20%">營業項目代碼：</td>
	        <td class="td_form_white" width="20%"> 
				<%=obj.getItemCode()%>
	        </td>
	        <td class="td_form" width="20%">營業項目：</td>
	        <td class="td_form_white" width="40%"> 
				<%=obj.getBusinessItem()%>
			</td>
	   </tr>
	</table>
	<c:import url="../common/msgbar.jsp">
  		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bg">	
	<div id="listContainer" height = "200">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  		<thead id="listTHEAD">
  		<tr>
  			<th class="listTH" width="5%">
    			<input type="checkbox" id="checkAll" name="checkAll" value="Y" onClick="oneClickCheckAll(this,'idListWillChange');">
    		</th>
    		<th width="50%" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">規則名稱</a></th>
    		<th width="10%" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">標明專業</a></th>
    		<th width="10%" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">營業項目專業</a></th>
    		<th width="15%" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">組織別</a></th>
    		<th width="10%" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">狀態</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  		<%
  			boolean primaryArray[] = {true,false,false,false,false,false,false,false};
  			boolean displayArray[] = {false,false,true,true,true,true,true,true};
  			String[] alignArray = {"center", "center", "left", "center", "center", "left", "center","center"};
  			out.write(obj.getQuerylist(primaryArray,displayArray,alignArray,objList,"idListWillChange"));
  		%>
  		</tbody>
	</table>
	</div>
</td></tr>
</table>
</form>
</body>
</html>