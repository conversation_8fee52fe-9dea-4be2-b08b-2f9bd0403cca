package com.kangdainfo.tcfi.model.osss.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;


/**
 * 申辦案件流程進度(OSSM_APPL_FLOW)
 *
 */
public class OssmApplFlow extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子案號 */
	private String telixNo;
	/** 申辦流程序號 */
	private Integer seqNo;
	/** 申辦流程代碼 */
	private String processNo;
	/** 案件狀態 */
	private String processStatus;
	/** 審查結果 */
	private String approveResult;
	/** 審查時間 */
	private Date approveTime;
	/** 建立時間 */
	private Date createTime;
	/** 修改時間 */
	private Date updateTime;
	/** 修改人員 */
	private String updateUser;
	/** 收文字號 */
	private String rcvCaseWd;
	/** 收文文號 */
	private String rcvCaseNo;
	/** 發文字號 */
	private String postCaseWd;
	/** 發文文號 */
	private String postCaseNo;
	/** 領件方式 */
	private String getKind;

	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String telixNo) {this.telixNo = telixNo;}
	public Integer getSeqNo() {return seqNo;}
	public void setSeqNo(Integer seqNo) {this.seqNo = seqNo;}
	public String getProcessNo() {return processNo;}
	public void setProcessNo(String processNo) {this.processNo = processNo;}
	public String getProcessStatus() {return processStatus;}
	public void setProcessStatus(String processStatus) {this.processStatus = processStatus;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String approveResult) {this.approveResult = approveResult;}
	public Date getApproveTime() {return approveTime;}
	public void setApproveTime(Date approveTime) {this.approveTime = approveTime;}
	public Date getCreateTime() {return createTime;}
	public void setCreateTime(Date createTime) {this.createTime = createTime;}
	public Date getUpdateTime() {return updateTime;}
	public void setUpdateTime(Date updateTime) {this.updateTime = updateTime;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getRcvCaseWd() {return rcvCaseWd;}
	public void setRcvCaseWd(String rcvCaseWd) {this.rcvCaseWd = rcvCaseWd;}
	public String getRcvCaseNo() {return rcvCaseNo;}
	public void setRcvCaseNo(String rcvCaseNo) {this.rcvCaseNo = rcvCaseNo;}
	public String getPostCaseWd() {return postCaseWd;}
	public void setPostCaseWd(String postCaseWd) {this.postCaseWd = postCaseWd;}
	public String getPostCaseNo() {return postCaseNo;}
	public void setPostCaseNo(String postCaseNo) {this.postCaseNo = postCaseNo;}
	public String getGetKind() {return getKind;}
	public void setGetKind(String getKind) {this.getKind = getKind;}

}