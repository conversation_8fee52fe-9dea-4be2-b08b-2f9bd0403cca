package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 排程(PRE0004)
 * 因同音同義字異動，同步異動索引資料
 */
public class Pre0004QuartzJobBean extends BaseQuartzJobBean
{
	/**
	 * 異動同音同義字資料程序
	 * 1. 讀取 INDEX_LOG 執行WS10003
	 *    1-1. 判斷 INDEX_LOG.STATUS = '0' 待執行的筆數
	 *    1-2. 筆數『小於 』SYSTEM_CODE 設定的 PRE0004_QueueNum 時才繼續往下執行重建索引，
	 *         反之此筆 WS10003 什麼都不做，必須等待下一次排程再判斷一次
	 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
	 * 3. 查詢檢索檔中公司名稱與特許名稱符合的資料
	 *    3-1. 若查出的檢索檔筆數『小於』SYSTEM_CODE 設定的 PRE0004_SearchMax 時才繼續往下執行重建索引，
	 *         反之此筆 WS10003 直接更新成執行完成，並註記remark = '檢索結果xxx', 不需往下執行重建索引
	 * 4. 新增異動公司/預查檢索資料程序至 INDEX_LOG 
	 * 5. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
	 */
	protected void executeJob(JobExecutionContext context) throws JobExecutionException{
		
		IndexLog obj =  new IndexLog();
		obj.setWsId(PrefixConstants.JOB_WS10003);			//異動同音同義字資料
		obj.setStatus(PrefixConstants.INDEX_LOG_STATUS_0);	//待執行
		
		obj = ServiceGetter.getInstance().getPre0004Service().getIndexData(obj);
		if(obj != null)
			ServiceGetter.getInstance().getPre0004Service().doBuildIndex(obj);
	}
}