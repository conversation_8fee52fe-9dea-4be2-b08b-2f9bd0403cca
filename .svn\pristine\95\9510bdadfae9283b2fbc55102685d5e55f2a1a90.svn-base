package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.StringUtility;
import com.kangdainfo.tcfi.loader.SystemCode09Loader;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1011;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1011Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.service.Pre4013Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.view.pre.PRE1004;
import com.kangdainfo.tcfi.view.pre.PRE4013;
import com.kangdainfo.util.TaiwanDateUtil;

/**
 * 預查線上申辦電子核定書
 *
 */
public class Pre4013ServiceImpl implements Pre4013Service {
	private Logger logger = Logger.getLogger(this.getClass());

	/**
	 * 產製電子核定書前的資料檢查
	 * @param prefixNo - String
	 * @param telixNo - String
	 * @param type - String
	 * @return String
	 */
	public String precheck(String prefixNo, String prefixNoEnd, String telixNo, String type) {
		if("telix".equals(type)) {
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByTelixNo(telixNo);
			if ( cedb1000 == null ) { 
				return "查無資料，請變更查詢條件" ;
			} else {
				return "ok";
			}
		} else {
			return PRE1004.checkForjsp(prefixNo, prefixNoEnd);
		}
    }

	/**
	 * 產製 列印資料
	 * @param prefixNo - String
	 * @return PRE4013
	 */
	public PRE4013 queryPrintDataByPrefixNo(String prefixNo) {
		if(logger.isInfoEnabled()) logger.info("[queryPrintDataByPrefixNo][prefixNo:"+prefixNo+"]");
		PRE4013 obj = new PRE4013();
		
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo);
		if(null==cedb1000) {
			obj.setErrorMsg("查無資料，請變更查詢條件");
			return obj;
		}
		Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
		if(null==cedb1023) {
			obj.setErrorMsg("查無資料，請變更查詢條件");
			return obj;
		}
		Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(prefixNo);
		String applyLawName = "";
		if (cedb1022 != null && cedb1022.getApplyLawName()!=null && !"".equals(cedb1022.getApplyLawName())) {
			applyLawName = cedb1022.getApplyLawName();
		}

		obj.setPrefixNo(cedb1000.getPrefixNo());
		obj.setReceiveDate(cedb1000.getReceiveDate());
		obj.setApplyId(cedb1000.getApplyId());
		obj.setApplyAddr(cedb1000.getApplyAddr());
		obj.setApplyTel(cedb1000.getApplyTel());
		obj.setApplyName(cedb1000.getApplyName());
		// Mantis0044629:問題需求單-1120510002_預查(中辦)
		// 變更案也顯示法人
//		if ("0".equals(cedb1023.getChangeType())) {
//		}
//      2024/02/23 修改新增"所代表法人"
		if(!"".equals(applyLawName)) {
			if(-1 == obj.getApplyName().indexOf(applyLawName)) {
				obj.setApplyName(cedb1000.getApplyName() + "　所代表法人：" + applyLawName);
			}
		}
		obj.setExtRemitEname(cedb1000.getExtRemitEname());

		if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
			//核准時印出核准日期跟保留日期
			obj.setCloseDate("核准日期：" + Common.get(cedb1000.getCloseDate()));
			obj.setReserveDate("核准保留期限："+Common.get(cedb1000.getReserveDate()));
		} else if (PrefixConstants.APPROVE_RESULT_N.equals(cedb1000.getApproveResult())) {
			//否准時印出核定日期
			obj.setCloseDate("核定日期：" + Common.get(cedb1000.getApproveDate()));
		}
		//組織別
		String orgType = ("05".equals(cedb1023.getOrgType())?"有限合夥":"公司");
		obj.setOrgType(orgType);
		//標題
		obj.setTitle(orgType+"名稱及所營事業登記預查核定書");
		//地址欄標題
		obj.setApplyAddrCont("戶籍地址(或"+orgType+"地址)");
		//馬上辦
		if ( PrefixConstants.PREFIX_STATUS_C.equals(cedb1000.getPrefixStatus())) {
			obj.setAtonce("（馬上辦："+generateAtonce(cedb1000.getAtonceType())+"）");
		}
		//申請項目
		obj.setApplyItem("申請項目：");
		//公司說明
		obj.setCompanyDesc("");
		if(PrefixConstants.CHANGE_TYPE_0.equals(cedb1023.getChangeType())) {
			obj.setApplyItem("申請項目：設立預查");
			obj.setCompanyDesc("");
		} else if(PrefixConstants.CHANGE_TYPE_1.equals(cedb1023.getChangeType())) {
			obj.setApplyItem("申請項目："+orgType+"名稱變更預查");
			if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+"原"+orgType+"名稱："+Common.get(cedb1000.getOldCompanyName()));
			} else {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+orgType+"名稱："+Common.get(cedb1000.getOldCompanyName()));
			}
		} else if(PrefixConstants.CHANGE_TYPE_2.equals(cedb1023.getChangeType())) {
			obj.setApplyItem("申請項目：所營事業變更預查");
			if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+orgType+"名稱："+Common.get(cedb1000.getCompanyName()));
			} else {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+orgType+"名稱："+Common.get(cedb1000.getOldCompanyName()));
			}
		} else if(PrefixConstants.CHANGE_TYPE_3.equals(cedb1023.getChangeType())) {
			obj.setApplyItem("申請項目："+orgType+"名稱及所營事業變更預查");
			if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+"原"+orgType+"名稱："+Common.get(cedb1000.getOldCompanyName()));
			} else {
				obj.setCompanyDesc(orgType+"統一編號："+Common.get(cedb1000.getBanNo())+"　　　　　　　　　　　"+orgType+"名稱："+Common.get(cedb1000.getOldCompanyName()));
			}
		}
		//公司屬性-閉鎖性
		obj.setClosedDesc("");
		if ("Y".equals(cedb1023.getClosed())) {
			obj.setClosedDesc("公司屬性：閉鎖性");
		}
		//公司名稱
		List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(cedb1000.getPrefixNo());
		//營業項目
		List<Cedb1002> cedb1002s = cedb1002Dao.findByPrefixNo(cedb1000.getPrefixNo());
		//審查結果
		obj.setApproveResult("審查結果：");
		if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
			obj.setApproveResult("審查結果：核准保留");
			//核准的公司名稱
			String approvedCompanyName = "";
			if(null!=cedb1001s && !cedb1001s.isEmpty()) {
				for(Cedb1001 cedb1001 : cedb1001s) {
					if ( PrefixConstants.APPROVE_RESULT_Y.equals( cedb1001.getApproveResult() )) {
						approvedCompanyName = cedb1001.getCompanyName();
					}
				}
			}
			//審查結果說明
			StringBuffer approveResultDesc = new StringBuffer("");
			if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
				if(PrefixConstants.CHANGE_TYPE_0.equals(cedb1023.getChangeType())) {
					approveResultDesc.append("　　　一、核准之").append(orgType).append("名稱：").append(approvedCompanyName).append("\n");
					approveResultDesc.append("　　　二、核准之所營事業項目:").append("\n");
					if(null!=cedb1002s && !cedb1002s.isEmpty()) {
						for(Cedb1002 cedb1002 : cedb1002s) {
							approveResultDesc.append("　　　　　").append(cedb1002.getSeqNo()).append("　").append(Common.get(cedb1002.getBusiItemNo())).append("　").append(cedb1002.getBusiItem()).append("\n");
						}
					}
				} else if(PrefixConstants.CHANGE_TYPE_1.equals(cedb1023.getChangeType())) {
					approveResultDesc.append("　　　　　核准之").append(orgType).append("名稱：").append(approvedCompanyName);
				} else if(PrefixConstants.CHANGE_TYPE_2.equals(cedb1023.getChangeType())) {
					approveResultDesc.append("　　　　　核准之所營事業項目：").append("\n");
					if(null!=cedb1002s && !cedb1002s.isEmpty()) {
						for(Cedb1002 cedb1002 : cedb1002s) {
							approveResultDesc.append("　　　　　").append(cedb1002.getSeqNo()).append("　").append(Common.get(cedb1002.getBusiItemNo())).append("　").append(cedb1002.getBusiItem()).append("\n");
						}
					}
				} else if(PrefixConstants.CHANGE_TYPE_3.equals(cedb1023.getChangeType())) {
					approveResultDesc.append("　　　一、核准之").append(orgType).append("名稱：").append(approvedCompanyName).append("\n");
					approveResultDesc.append("　　　二、核准之所營事業項目:").append("\n");
					if(null!=cedb1002s && !cedb1002s.isEmpty()) {
						for(Cedb1002 cedb1002 : cedb1002s) {
							approveResultDesc.append("　　　　　").append(cedb1002.getSeqNo()).append("　").append(Common.get(cedb1002.getBusiItemNo())).append("　").append(cedb1002.getBusiItem()).append("\n");
						}
					}
					
				}
			}
			obj.setApproveResultDesc(approveResultDesc.toString());
		} else {
			obj.setApproveResult("審查結果：不予核准");
		}
		//審查理由
		List<Map<String,String>> reasons = new ArrayList<Map<String,String>>();
		Map<String,String> reason = null;
		Integer reasonIdx = 1;
		//公司名稱
		if(PrefixConstants.CHANGE_TYPE_0.equals(cedb1023.getChangeType())
			|| PrefixConstants.CHANGE_TYPE_1.equals(cedb1023.getChangeType())
			|| PrefixConstants.CHANGE_TYPE_3.equals(cedb1023.getChangeType()) ) {
			if(null!=cedb1001s && !cedb1001s.isEmpty()) {
				for(Cedb1001 cedb1001 : cedb1001s) {
					if ( PrefixConstants.APPROVE_RESULT_Y.equals( cedb1001.getApproveResult() )) {
						reason = new HashMap<String,String>();
						reason.put("index", getIndexCharacter(reasonIdx++));
						reason.put("content","經查"+orgType+"名稱第" + Integer.parseInt(cedb1001.getSeqNo()) + "欄(" + Common.get(cedb1001.getCompanyName()) + ")符合規定，應予核准保留。");
						reasons.add(reason);
						break;
					} else {
						if(!"".equals(Common.get(cedb1001.getRemark()))) {
							reason = new HashMap<String,String>();
							reason.put("index", getIndexCharacter(reasonIdx++));
							reason.put("content","經查"+orgType+"名稱第" + Integer.parseInt(cedb1001.getSeqNo()) + "欄(" + Common.get(cedb1001.getCompanyName()) + ")，" + Common.get(cedb1001.getRemark()) + "，不予核准。");
							reasons.add(reason);
						} else {
							reason = new HashMap<String,String>();
							reason.put("index", getIndexCharacter(reasonIdx++));
							reason.put("content","經查"+orgType+"名稱第" + Integer.parseInt(cedb1001.getSeqNo()) + "欄(" + Common.get(cedb1001.getCompanyName()) + ")，不予核准。");
							reasons.add(reason);
						}
					}
				}
			}
		}
		//所營事業
		if(PrefixConstants.CHANGE_TYPE_0.equals(cedb1023.getChangeType())
			|| PrefixConstants.CHANGE_TYPE_2.equals(cedb1023.getChangeType())
			|| PrefixConstants.CHANGE_TYPE_3.equals(cedb1023.getChangeType()) ) {
			if(PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","查本件所營事業符合規定，應予核准保留。");
				reasons.add(reason);
			}
		}
		Cedb1011 cedb1011 = cedb1011Dao.queryByPrefixNo(cedb1000.getPrefixNo());
		if(null!=cedb1011) {
			//外商
			if(!"".equals(Common.get(cedb1011.getForeignMark()))) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","非外國公司不得使用「" + Common.get(cedb1011.getForeignMark()) + "商」字樣。");
				// 申請日為107.11.1前採用舊核定書. HB@20190227
				if (TaiwanDateUtil.parseToDate(obj.getReceiveDate()).before(TaiwanDateUtil.parseToDate("1071101"))) {
					reason.put("content","非外商認許公司不得使用「" + Common.get(cedb1011.getForeignMark()) + "」商字樣。");
				}
				reasons.add(reason);
			}
			//合併消滅
			if(!"".equals(Common.get(cedb1011.getCompanyName())) && !"".equals(Common.get(cedb1011.getBanNo()))) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","此件預查表應於" + Common.get(cedb1011.getCompanyName()) + "【統一編號：" + Common.get(cedb1011.getBanNo()) + "】合併(分割)消滅後始生效力。");
				reasons.add(reason);
			}
			//大陸商
			if(!"".equals(Common.get(cedb1011.getChinaMark()))) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","非大陸商許可公司不得使用大陸商字樣。");
				reasons.add(reason);
			}
		}
		if(PrefixConstants.PREFIX_STATUS_9.equals(cedb1000.getPrefixStatus())) {
			//預查狀態:檢還(9)時顯示
			Cedb1010 flow9 = cedb1010Dao.findByPrefixNoAndStatus(cedb1000.getPrefixNo(), PrefixConstants.PREFIX_STATUS_9);
			if(null!=flow9) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content",StringUtility.str2DateTwStr(flow9.getProcessDate())+"申請人檢還原件，自該日起本案失其效力。");
				reasons.add(reason);
			} else {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","申請人檢還原件，自該日起本案失其效力。");
				reasons.add(reason);
			}
		} else if(PrefixConstants.PREFIX_STATUS_A.equals(cedb1000.getPrefixStatus())) {
			//預查狀態:撤件(A)時顯示
			Cedb1010 flowA = cedb1010Dao.findByPrefixNoAndStatus(cedb1000.getPrefixNo(), PrefixConstants.PREFIX_STATUS_A);
			if(null!=flowA) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content",StringUtility.str2DateTwStr(flowA.getProcessDate())+"申請人撤回申請，自該日起本案失其效力。");
				reasons.add(reason);
			} else {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","申請人撤回申請，自該日起本案失其效力。");
				reasons.add(reason);
			}
		} else if(PrefixConstants.PREFIX_STATUS_E.equals(cedb1000.getPrefixStatus())) {
			//預查狀態:撤回退費(E)時顯示
			Cedb1010 flowE = cedb1010Dao.findByPrefixNoAndStatus(cedb1000.getPrefixNo(), PrefixConstants.PREFIX_STATUS_E);
			if(null!=flowE) {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content",StringUtility.str2DateTwStr(flowE.getProcessDate())+"申請人撤回申請。");
				reasons.add(reason);
			} else {
				reason = new HashMap<String,String>();
				reason.put("index", getIndexCharacter(reasonIdx++));
				reason.put("content","申請人撤回申請。");
				reasons.add(reason);
			}
		}
		//有[審核備註]時，要顯示
		if(!"".equals(Common.get(cedb1000.getApproveRemark()))) {
			reason = new HashMap<String,String>();
			reason.put("index", getIndexCharacter(reasonIdx++));
			reason.put("content",Common.get(cedb1000.getApproveRemark()));
			reasons.add(reason);
		}
		obj.setApproveReasons(reasons);
		return obj;
	}

	/**
	 * 取得 列印日期
	 * @return String
	 */
	public String getPrintDate() {
		StringBuffer result = new StringBuffer("");
		String curr = Datetime.getCurrentDate("yyyy/MM/dd");
		result.append(curr.substring(0, 3)).append("年");
		result.append(curr.substring(4, 6)).append("月");
		result.append(curr.substring(7)).append("日");
		return result.toString();
	}

	/**
	 * 取得 列印時間
	 * @return String
	 */
	public String getPrintTime() {
		StringBuffer result = new StringBuffer("");
		String curr = Datetime.getHHMMSS();
		result.append(curr.substring(0, 2)).append("點");
		result.append(curr.substring(2, 4)).append("分");
		result.append(curr.substring(4)).append("秒");
		return result.toString();
	}

	/**
	 * 馬上辦
	 * @return String
	 */
	private String generateAtonce(String atonceType) {
		StringBuffer sb = new StringBuffer("");
		if(!"".equals(Common.get(atonceType))) {
			String[] atonceTypesBefore = atonceType.split(",");
			String atonceDesc = "";
			for(String at : atonceTypesBefore) {
				if( !"".equals(Common.get(at)) ) {
					at = Common.get(at);
					if(!"".equals(sb.toString())) sb.append("、");
					atonceDesc = SystemCode09Loader.getInstance().getCodeDescByCode(at);
					if(!"".equals(Common.get(atonceDesc))) {
						sb.append(Common.get(atonceDesc));
					}
				}
			}
		}
		return sb.toString();
	}

	private String getIndexCharacter(Integer num) {
		String character = "";
		if( 1 == num ) character = "一、";
		else if( 2 == num ) character = "二、";
		else if( 3 == num ) character = "三、";
		else if( 4 == num ) character = "四、";
		else if( 5 == num ) character = "五、";
		else if( 6 == num ) character = "六、";
		else if( 7 == num ) character = "七、";
		else if( 8 == num ) character = "八、";
		else if( 9 == num ) character = "九、";
		else if( 10 == num ) character = "十、";
		return character;
	}

	private Cedb1000Dao cedb1000Dao;
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	private Cedb1002Dao cedb1002Dao;
	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}
	private Cedb1001Dao cedb1001Dao;
	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}
	private Cedb1010Dao cedb1010Dao;
	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	private Cedb1011Dao cedb1011Dao;
	public Cedb1011Dao getCedb1011Dao() {return cedb1011Dao;}
	public void setCedb1011Dao(Cedb1011Dao dao) {this.cedb1011Dao = dao;}
	private Cedb1022Dao cedb1022Dao;
	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}
	private Cedb1023Dao cedb1023Dao;
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}

}