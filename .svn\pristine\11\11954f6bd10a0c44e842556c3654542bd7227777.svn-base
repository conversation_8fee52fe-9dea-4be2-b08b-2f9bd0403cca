<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.EncapsulationVo"%>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		EncapsulationVo encap = ServiceGetter.getInstance().getEncapsulateService().findEncapsulation(q);
		out.write(gson.toJson(encap));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>