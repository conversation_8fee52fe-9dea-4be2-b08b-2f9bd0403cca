package com.kangdainfo.tcfi.service.impl;

import java.util.Calendar;
import java.util.List;

import javax.xml.ws.BindingProvider;

import org.apache.log4j.Logger;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.SyncOssQueueDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgRegisterDao;
import com.kangdainfo.tcfi.service.UpdateOsssStatusService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.lang.CommonStringUtils;

public class UpdateOsssStatusServiceImpl
	implements UpdateOsssStatusService
{
	private Logger logger = Logger.getLogger(this.getClass());

	public void insertQueue(String prefixNo, String modIdNo) {
		if(logger.isInfoEnabled()) logger.info("[insertQueue][prefixNo:"+prefixNo+"]");
		syncOssQueueDao.insert(prefixNo, modIdNo);
	}
	
	public Queue getToDoQueue() {
		if(logger.isDebugEnabled()) logger.debug("[getToDoQueue]");
		Queue q = syncOssQueueDao.query();
		if(q != null)
			q.setStatus(PrefixConstants.INDEX_LOG_STATUS_1);
		return syncOssQueueDao.update(q);
	}

	public void updateResetErrorQueue() {
		syncOssQueueDao.updateResetErrorQueue();
	}

	public List<Queue> queryErrorQueues() {
		return syncOssQueueDao.queryErrorQueues();
	}

	public void doSyncOsss(Queue queue) {
		if(logger.isDebugEnabled()) logger.debug("[doSyncOsss]");
		if(null!=queue) {
			long start = Calendar.getInstance().getTimeInMillis();
			String prefixNo = queue.getPrefixNo();
			String modIdNo = queue.getModId();
			
			if( null==prefixNo || "".equals(prefixNo) ) {
				queue.setRemark("無預查編號");
				updateQueue(queue, start);
			} else {
				Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
				if(null==cedb1000) {
					queue.setRemark("查無cedb1000資料");
					updateQueue(queue, start);
				} else {
					String telixNo = Common.get(cedb1000.getTelixNo());
					if( null==telixNo || "".equals(telixNo) ) {
						queue.setRemark("非一站式案件");
						updateQueue(queue, start);
					} else {
						if( !telixNo.startsWith("O") ) {
							queue.setRemark("非一站式案件");
							updateQueue(queue, start);
						} else {
							OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
							if(null==ossmApplMain) {
								queue.setRemark("非一站式案件");
								updateQueue(queue, start);
							} else {
								//預查核覆結果
								String approveResult = Common.get(cedb1000.getApproveResult());
								//預查案件狀態
								String prefixStatus = Common.get(cedb1000.getPrefixStatus());
								//已發文結案, 才同步案件處理完成
								String closeDate = Common.get(cedb1000.getCloseDate());

								if( !"".equals(closeDate) && PrefixConstants.APPROVE_RESULT_Y.equalsIgnoreCase(approveResult) ) {
									/* 處理「馬上辦-人工審核」: 表示在馬上辦畫面執行過【存檔】動作。
									 * 之所以不使用PrefixConstants.OSSS_APPLY_TYPE_XXXX來判斷, 是因為在一站式系統裡, 案件申請為馬上辦後, 
									 * 並不會去修改OSSM_APPL_MAIN.APPLY_TYPE, 所以僅能以在預查系統中的狀態來判斷是否為馬上辦案件
									 */
									if(PrefixConstants.PREFIX_STATUS_C.equalsIgnoreCase(prefixStatus)) {
										String remark = syncOsss(cedb1000, "J", PrefixConstants.OSSS_STATUS_103, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									//案件已核准保留, 傳送狀態(103)案件處理完成
									}else if( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType()) ) {
										//若一站式申請類別為(L0100)線上審核時, 傳送狀態(103)案件處理完成
										String remark = syncOsss(cedb1000, "I", PrefixConstants.OSSS_STATUS_103, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									} else {
										String remark = syncOsss(cedb1000, "B", PrefixConstants.OSSS_STATUS_103, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									}
								} else if ( PrefixConstants.APPROVE_RESULT_N.equalsIgnoreCase(approveResult) ) {
									if( PrefixConstants.PREFIX_STATUS_E.equals(prefixStatus) ) {
										//案件已不予核准 且 撤件退費(E), 傳送狀態(B,105)
										String remark = syncOsss(cedb1000, "B", PrefixConstants.OSSS_STATUS_105, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									} else if ( PrefixConstants.PREFIX_STATUS_A.equals(prefixStatus) ) {
										//案件已不予核准 且 撤件(A), 傳送狀態(H,103)
										String remark = syncOsss(cedb1000, "H", PrefixConstants.OSSS_STATUS_103, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									} else if ( !"".equals(closeDate) ) {
										//案件已不予核准, 傳送狀態(B,103)案件處理完成
										String remark = syncOsss(cedb1000, "B", PrefixConstants.OSSS_STATUS_103, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									} else {
										queue.setRemark("狀態錯誤");
										updateQueue(queue, start);
									}
								} else {
									String rcvCheck = Common.get(cedb1000.getRcvCheck());
									if( "Y".equalsIgnoreCase(rcvCheck) ) {
										//已收文確認, 傳送狀態(102)案件審理中
										String remark = syncOsss(cedb1000, "B", PrefixConstants.OSSS_STATUS_102, modIdNo);
										queue.setRemark(remark);
										updateQueue(queue, start);
									} else {
										if( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType()) ) {
											//若一站式申請類別為(L0100)線上審核時, 傳送狀態(103)案件處理完成
											String remark = syncOsss(cedb1000, "I", PrefixConstants.OSSS_STATUS_103, modIdNo);
											queue.setRemark(remark);
											updateQueue(queue, start);
										} else {
											//已收文尚未收文確認, 傳送狀態(101)已接收資料
											String remark = syncOsss(cedb1000, "B", PrefixConstants.OSSS_STATUS_101, modIdNo);
											queue.setRemark(remark);
											updateQueue(queue, start);
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	
	private void updateQueue(Queue queue, long start) {
		if(queue == null)	return;
		// 000 介接成功
		if("000".equals(Common.get(queue.getRemark()))){
			queue.setStatus(PrefixConstants.INDEX_LOG_STATUS_2);
		} else if ("非一站式案件".equals(Common.get(queue.getRemark()))) {
			queue.setStatus(PrefixConstants.INDEX_LOG_STATUS_2);
		} else {
			queue.setStatus(PrefixConstants.INDEX_LOG_STATUS_3);
		}
		queue.setRemark( CommonStringUtils.limitLength(queue.getRemark(), 125) );
		queue.setProcessTime(Calendar.getInstance().getTimeInMillis() - start);
		syncOssQueueDao.update(queue);
	}

	private String syncOsss(Cedb1000 cedb1000, String osssType, String status, String modIdNo) {
		if(logger.isInfoEnabled()) logger.info("[syncOsss]");
		String result = "error";
		try {
			tw.org.moea.online.oss.UpdateCaseStatusBEAN msg = new tw.org.moea.online.oss.UpdateCaseStatusBEAN();
			msg.setTicketNo(cedb1000.getTelixNo());
			msg.setCaseNo(cedb1000.getPrefixNo());
			msg.setUpdateUser(modIdNo);
			msg.setStatusTime((new java.text.SimpleDateFormat("yyyyMMddHHmmss")).format(new java.util.Date()));
			msg.setStatusCode(status);
			// msg.setOid("2.16.886.101.20003.20007.20028.20006_"+osssType);
			// 配合預查業務移交中辦, 調整oid
			msg.setOid("2.16.886.101.20003.20007.20029_"+osssType);
			if(PrefixConstants.OSSS_STATUS_103.equals(status)) {
				//已結案(003)
				msg.setResultCode(cedb1000.getApproveResult());
				msg.setIsClosed("Y");
				StringBuffer caseInfo = new StringBuffer();
				caseInfo.append(cedb1000.getPrefixNo());
				if(null!=cedb1000.getCompanyName() && !"".equals(cedb1000.getCompanyName())) {
					caseInfo.append("_");
					caseInfo.append(cedb1000.getCompanyName());
				}
				msg.setCaseInfo(caseInfo.toString());
			} else if (PrefixConstants.OSSS_STATUS_105.equals(status)) {
				//已撤件(105)
				msg.setResultCode("A");
				msg.setIsClosed("Y");
			}
			
			String osssWsdl = getOsssWsdl();
			if(null!=osssWsdl && !"".equals(osssWsdl)) {
				java.net.URL wsdlURL = new java.net.URL(osssWsdl);
		        tw.org.moea.online.oss.CaseStatusService ss =
		        		new tw.org.moea.online.oss.CaseStatusService(wsdlURL,
		        				new javax.xml.namespace.QName("http://tw/org/moea/online/oss", "CaseStatusService"));
		        tw.org.moea.online.oss.CaseStatus port = ss.getCaseStatusPort();
		        ((BindingProvider) port).getRequestContext().put("com.sun.xml.internal.ws.connect.timeout",60000);
		        ((BindingProvider) port).getRequestContext().put("com.sun.xml.internal.ws.request.timeout",60000);
		        if(logger.isInfoEnabled()) logger.info(msg);
		        if(logger.isInfoEnabled()) logger.info("Invoking updateCaseStatus...");
		        result = port.updateCaseStatus(msg);
		        if(logger.isInfoEnabled()) logger.info("updateCaseStatus.result=" + result);
				result = CommonStringUtils.limitLength(result,125);
			} else {
				result = "一站式同步路徑(WSDL)未設定";
			}
		} catch (Exception e) {
			e.printStackTrace();
			result = CommonStringUtils.limitLength(e.getMessage(),125);
		}
		return result;
	}
	
	/**
	 * 正式機: http://onestop.nat.gov.tw/oss/ossws/UpdateCaseStatusService?wsdl	<br/>
	 * 測試環境: http://125.227.131.127/ossWithdraw/ossws/UpdateCaseStatusService?wsdl
	 */
	private String getOsssWsdl() {
		SystemCode s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "OSSS_WSDL");
		String result = null;
		if(null!=s) {
			result = s.getCodeName();
		}
		return result;
	}
	
	public void updateOrgCapitalAmt(String prefixNo) {
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null!=cedb1000) {
			String telixNo = Common.get(cedb1000.getTelixNo());
			if(telixNo.startsWith("O")) {
				ossmOrgRegisterDao.updateOrgCapitalAmtByTelixNo(telixNo);
				ossmOrgRegisterDao.updateOrgRealCapitalAmtByTelixNo(telixNo);
			}
		}
	}

	private SyncOssQueueDao syncOssQueueDao;
	private Cedb1000Dao cedb1000Dao;
	private Cedb1023Dao cedb1023Dao;
	private OssmApplMainDao ossmApplMainDao;
	private SystemCodeDao systemCodeDao;
	private OssmOrgRegisterDao ossmOrgRegisterDao;

	public SyncOssQueueDao getSyncOssQueueDao() {return syncOssQueueDao;}
	public void setSyncOssQueueDao(SyncOssQueueDao dao) {this.syncOssQueueDao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public OssmApplMainDao getOssmApplMainDao() {return ossmApplMainDao;}
	public void setOssmApplMainDao(OssmApplMainDao dao) {this.ossmApplMainDao = dao;}
	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	public OssmOrgRegisterDao getOssmOrgRegisterDao() {return ossmOrgRegisterDao;}
	public void setOssmOrgRegisterDao(OssmOrgRegisterDao dao) {this.ossmOrgRegisterDao = dao;}

}