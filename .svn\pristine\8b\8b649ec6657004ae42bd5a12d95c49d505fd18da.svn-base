package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司名稱管制備忘檔(CMPY_MEMO_INFO)
 *
 */
public class CmpyMemoInfo extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String id;
	/** 收文文號 */
	private String rcvNo;
	/** 公司名稱 */
	private String companyName;
	/** 保留日期 */
	private String reserveDate;
	/** 啟用 */
	private String enable;
	/** 更新日期 */
	private String updateDate;
	/** 更新時間 */
	private String updateTime;
	/** 更新人員 */
	private String updateUser;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getRcvNo() {return rcvNo;}
	public void setRcvNo(String rcvNo) {this.rcvNo = rcvNo;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String reserveDate) {this.reserveDate = reserveDate;}
	public String getEnable() {return enable;}
	public void setEnable(String enable) {this.enable = enable;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}

}