package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 排程(PRE0003)
 * 異動預查索引資料
 */
public class Pre0003QuartzJobBean extends BaseQuartzJobBean
{
	/**
	 * 異動預查檢索資料程序
	 * 1. 讀取 INDEX_LOG 執行WS10002
	 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
	 * 3. 檢查是『預查』還是『已收文』資料
	 * 4. 呼叫 delIndex.delIndexByPrefixNo() 刪除檢索檔資料
	 * 5. 呼叫 IndexDatabase.batchAppendIndex() 重建檢索檔 (含同音同義字)
	 * 6. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
	 */
	protected void executeJob(JobExecutionContext context)
			throws JobExecutionException {
		//1.start
		IndexLog indexLog = ServiceGetter.getInstance().getIndexUpdateService().doStartUpdate(PrefixConstants.JOB_WS10002);
		if(null!=indexLog) {
			//2.execute
			indexLog = ServiceGetter.getInstance().getIndexUpdateService().doUpdateIndex(indexLog);
			//3.end
			ServiceGetter.getInstance().getIndexUpdateService().doEndUpdate(indexLog);
		}
	}
}