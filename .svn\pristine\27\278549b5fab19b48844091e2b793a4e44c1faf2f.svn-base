package com.kangdainfo.tcfi.view.common;

import java.util.ArrayList;

import com.kangdainfo.common.util.SuperBean;

/**
 * 快捷列
 *
 */
public class Msgbar extends SuperBean {

	private String errorMsg;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getErrorMsg() {return checkGet(errorMsg);}
	public void setErrorMsg(String s) {this.errorMsg = checkSet(s);}

}