package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc055;

public class Cedbc055Dao extends BaseDaoJdbc implements RowMapper<Cedbc055> {

	private static final String SQL_findAll = "SELECT * FROM CEDBC055 ORDER BY ITEM_CODE ";
	public List<Cedbc055> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedbc055>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public List<Cedbc055> findAllCondition( String itemCode, String businessItem, String masterCode ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL( "select * from cedbc055 where 1=1" ) ;
		if ( itemCode != null && !"".equals( itemCode ) )
			sqljob.appendSQL( "and ITEM_CODE LIKE '" + itemCode + "%'" ) ;
		if ( businessItem != null && !"".equals( businessItem ) )
			sqljob.appendSQL( "and BUSINESS_ITEM LIKE '%" + businessItem + "%'" ) ;
		if ( masterCode != null && !"".equals( masterCode ) )
			sqljob.appendSQL( "and MASTER_CODE LIKE '" + masterCode + "%'" ) ;
		sqljob.appendSQL( "order by item_code" ) ;
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedbc055> list =  (List<Cedbc055>) getJdbcTemplate().query(sqljob.getSQL(), this);
		return list.isEmpty() ? null : list ; 
	}
	
	public Cedbc055 findByItemCode( String itemCode ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL( "select * from cedbc055 where 1=1" ) ;
		if ( itemCode != null && !"".equals( itemCode ) )
			sqljob.appendSQL( "and ITEM_CODE LIKE '" + itemCode + "'" ) ;
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedbc055> list =  (List<Cedbc055>) getJdbcTemplate().query(sqljob.getSQL(), this);
		return list.isEmpty() ? null : list.get(0) ; 
	}
	
	public int update( Cedbc055 obj ) {
		if ( obj == null )
			return 0 ;
		StringBuffer sql = new StringBuffer();
		sql.append( "update cedbc055 set " ) ;
		sql.append( "business_item = ? " ) ;
		sql.append( ",master_code = ? " ) ;
		sql.append( "where item_code = ?" ) ;
		Object[] parameters = {
				 obj.getBusinessItem()
				,obj.getMasterCode()
				,obj.getItemCode()
				};
		return getJdbcTemplate().update(sql.toString(), parameters); 
	}
	
	public int insert( Cedbc055 obj ) {
		if ( obj == null )
			return 0 ;
		StringBuffer sql = new StringBuffer();
		sql.append( " insert into cedbc055 " ) ;
		sql.append( "( master_code" );
		sql.append( ",item_code " ) ; 
		sql.append( ",business_item ) values (?,?,?) " ) ;
		Object[] parameters = {
				obj.getMasterCode() 
				,obj.getItemCode()
				,obj.getBusinessItem()
				};
		return getJdbcTemplate().update(sql.toString(), parameters); 
	}
	
	public int delete( Cedbc055 obj ) {
		if ( obj == null )
			return 0 ;
	    StringBuffer sql = new StringBuffer() ;
	    sql.append( "delete from cedbc055 " ) ;
	    sql.append( "where item_code = ?" ) ;
	    Object[] parameters = { obj.getItemCode() };
	    return getJdbcTemplate().update(sql.toString(), parameters);
	}

	public Cedbc055 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc055 obj = null;
		if(null!=rs) {
			obj = new Cedbc055();
			obj.setMasterCode(rs.getString("MASTER_CODE"));
			obj.setItemCode(rs.getString("ITEM_CODE"));
			obj.setBusinessItem(rs.getString("BUSINESS_ITEM"));
		}
		return obj;
	}

	public List<Cedbc055> findItemCodeByLength(int length) {
		SQLJob sqljob = new SQLJob("SELECT ITEM_CODE, BUSINESS_ITEM, MASTER_CODE FROM CEDBC055 WHERE (LENGTH(ITEM_CODE) = ?) ORDER BY ITEM_CODE");
		sqljob.addParameter(length);
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public List<Cedbc055> findItemByLengthAndKeyword(int length, String keyword) {
		SQLJob sqljob = new SQLJob("SELECT ITEM_CODE, BUSINESS_ITEM, MASTER_CODE FROM CEDBC055 WHERE ((LENGTH(ITEM_CODE) = ?) AND (BUSINESS_ITEM LIKE ?)) ORDER BY ITEM_CODE");
		sqljob.addParameter(length);
		sqljob.addLikeParameter(keyword);
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

}