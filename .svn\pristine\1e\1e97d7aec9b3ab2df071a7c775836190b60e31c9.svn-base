package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3100;

public class Eedb3100Dao extends BaseDaoJdbc implements RowMapper<Eedb3100> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB3100 WHERE TELIX_NO = ? ORDER BY TELIX_NO, SEQ_NO";
	public List<Eedb3100> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Eedb3100>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Eedb3100 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb3100 obj = null;
		if(null!=rs) {
			obj = new Eedb3100();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBusiItemNo(rs.getString("BUSI_ITEM_NO"));
			obj.setBusiItem(rs.getString("BUSI_ITEM"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
		}
		return obj;
	}

}
