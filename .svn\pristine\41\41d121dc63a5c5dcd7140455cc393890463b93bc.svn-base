package com.kangdainfo.tcfi.view.pre;

/*
程式目的：核准公司名稱列表
程式代號：pre4008
撰寫日期：103.04.30
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.util.report.JasperReportMaker;


public class PRE4008 extends SuperBean {	
  
 
  private String q_approveDateStart ;  // 核覆日期起
  private String q_approveDateEnd ;    // 核覆日期迄
  private String q_idNo ;              // 承辦人員識別碼
  private String q_companyName;

  
  
  
  private String prefixNo ;         // 預查編號
  private String staffName ;        // 承辦人員姓名
  private String companyName ;      // 公司名稱
  private String specialName ;      // 特取名稱
  private String closeDate ;        // 結案時間
  private String approveDate ;      // 核覆日期
  private String idNo ;             // 承辦人員識別碼
  private String approveDateStart ;
  private String approveDateEnd ;
  private String total;            // 總筆數
  // ----------------------------------getters and setters of local variable bellow ---------------------------
  
  public String getQ_approveDateStart() {return checkGet(q_approveDateStart);}
  public void setQ_approveDateStart(String s) {q_approveDateStart = checkSet(s);}
  public String getQ_approveDateEnd() {return checkGet(q_approveDateEnd);}
  public void setQ_approveDateEnd(String s) {q_approveDateEnd = checkSet(s);}
  public String getQ_idNo() {return checkGet(q_idNo);}
  public void setQ_idNo(String s) {q_idNo = checkSet(s);}
  public String getQ_companyName() {return checkGet(q_companyName);}
  public void setQ_companyName(String s) {q_companyName = checkSet(s);}
  
  
  public String getPrefixNo() {return checkGet(prefixNo);}
  public void setPrefixNo(String s) {prefixNo = checkSet(s);}
  public String getStaffName() {return checkGet(staffName);}
  public void setStaffName(String s) {staffName = checkSet(s);}
  public String getCompanyName() {return checkGet(companyName);}
  public void setCompanyName(String s) {companyName = checkSet(s);}
  public String getSpecialName() {return checkGet(specialName);}
  public void setSpecialName(String s) {specialName = checkSet(s);}
  public String getCloseDate() {return checkGet(closeDate);}
  public void setCloseDate(String s) {closeDate = checkSet(s);}
  public String getApproveDate() {return checkGet(approveDate);}
  public void setApproveDate(String s) {approveDate = checkSet(s);}
  public String getIdNo() {return checkGet(idNo);}
  public void setIdNo(String s) {idNo = checkSet(s);}
  public String getApproveDateStart() {return checkGet(approveDateStart);}
  public void setApproveDateStart(String s) {approveDateStart = checkSet(s);}
  public String getApproveDateEnd() {return checkGet(approveDateEnd);}
  public void setApproveDateEnd(String s) {approveDateEnd = checkSet(s);}
  public String getTotal() {return checkGet(total);}
  public void setTotal(String s) {total = checkSet(s);}
  // ----------------------------------------------------------------------------------------------------------
  
  
  
  // ----------------------------------function never used bellow----------------------------------------------
  
  public void doCreate() throws Exception{	  
  } // end doCreate()
	  
  public void doUpdate() throws Exception{
  } // end doUpdate()		
	  
  public void doDelete() throws Exception{			
  } // end doDelete()	
	  
  public Object doQueryOne() throws Exception{ 
    return null ;
  } // end doQueryOne() 
  
  public static SQLJob doAppendSqljob(String dateStart, String dateEnd, String staff, String companyName) {
	  SQLJob sqljob = new SQLJob();
	  sqljob.appendSQL(" SELECT A.PREFIX_NO, A.STAFF_NAME, A.COMPANY_NAME, A.SPECIAL_NAME, A.APPROVE_DATE, A.CLOSE_DATE, A.ID_NO ");
      sqljob.appendSQL(" FROM CEDB1000 A, CEDB1023 B ");
      sqljob.appendSQL(" WHERE A.PREFIX_NO = B.PREFIX_NO AND (B.CHANGE_TYPE IS NULL OR B.CHANGE_TYPE <> '2') AND ");
      sqljob.appendSQL("       A.APPROVE_RESULT = 'Y' AND ");
      sqljob.appendSQL("       A.APPROVE_DATE BETWEEN ? AND ?" );
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      if ( staff != null && staff.length() == 10) {
    	  sqljob.appendSQL(" AND A.ID_NO = ?") ;
    	  sqljob.addParameter(staff);
      } // if
      if (companyName != null && !"".equals(companyName)) {
    	  sqljob.appendSQL(" AND A.company_name like ?") ;
    	  companyName = "%"+companyName + "%";
    	  sqljob.addParameter(companyName);
      }
      sqljob.appendSQL(" ORDER BY A.PREFIX_NO ");  
	  
      return sqljob ;
	  
  } // doAppendSqljob()

  
  
  
  @SuppressWarnings("rawtypes")
  public ArrayList doQueryAll() throws Exception {
	  try {
		  setTotal("0") ;
		  int num = 0;
		  if ( "".equals( getQ_approveDateEnd() ) )
			  setQ_approveDateEnd( getQ_approveDateStart() ) ;    // 若只輸入起始日期則查詢當天的資料
     	     
   	      if(logger.isInfoEnabled()) logger.info(doAppendSqljob(getQ_approveDateStart(), getQ_approveDateEnd(), getQ_idNo(), getQ_companyName()));	
   	      List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_approveDateStart(), getQ_approveDateEnd(), getQ_idNo(), getQ_companyName() ));
   	      if ( rs == null || rs.size() <= 0 ) {
   	    	  setErrorMsg("查無資料，請變更查詢日期或承辦人姓名");
   		      throw new MoeaException( "查無資料，請變更查詢日期或承辦人姓名" ) ;
   	      } // end if
   	      else { 
   	    	  ArrayList<String[]> dataList = new ArrayList<String[]>();
   	    	  int i = 0;
   	    	  while( i < rs.size()){
   	    		  String[] rowArray = new String[6] ;
   	    		  Map<String,Object> temp = rs.get(i);
   	    		  PRE4008 data = new PRE4008();
   	    		  data.setPrefixNo(Common.get(temp.get("PREFIX_NO"))) ;
   	    		  data.setStaffName(Common.get(temp.get("STAFF_NAME")));
   	    		  data.setCompanyName(Common.get(temp.get("COMPANY_NAME")));
   	    		  data.setSpecialName(Common.get(temp.get("SPECIAL_NAME")));
   	    		  data.setApproveDate(Common.get(temp.get("APPROVE_DATE")));
   	    		  data.setCloseDate(Common.get(temp.get("CLOSE_DATE")));
   	    		  data.setIdNo(Common.get(temp.get("ID_NO")));
   	    		  rowArray[0] = data.getPrefixNo();
   	    		  rowArray[1] = data.getStaffName();
   	    		  rowArray[2] = data.getCompanyName();
   	    		  rowArray[3] = data.getSpecialName();
   	    		  rowArray[4] = data.getApproveDate();
   	    		  rowArray[5] = data.getCloseDate();
   	    		  dataList.add(rowArray);
   	    		  num++ ;
   	    		  i++;
   	    	  } // end while
		
   	    	  setTotal(Integer.toString(num)) ;
   	    	  setErrorMsg("查詢成功！");
   	    	  return dataList;
   	      } // end else 
	  } // try 
	  catch( MoeaException e ) {
		  e.printStackTrace();
		  setErrorMsg(e.getMessage());
		  return null;
	  } // catch
  } // doQueryAll()

  // -----------------------------------------------------------------------------------------------------------
    
  public String DateFormat( String inputDate ) {
	  String tempDate = "" ;
	  String year = inputDate.substring(0, 3) ;
	  String month = inputDate.substring(4, 6) ;
	  String day = inputDate.substring(7) ;
	  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
	  return tempDate ;
  } // DateFormat()
  
  public String TimeFormat( String inputTime ) {
	  String tempTime = "" ;
	  String hour = inputTime.substring(0, 2) ;
	  String minute = inputTime.substring(2, 4) ;
	  String second = inputTime.substring(4) ;
	  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
	  return tempTime ;
  } // TimeFormat()
  
  
  
  public File doPrintPdf() throws Exception {
	File report = null ;  
	try {       
	  if ( "".equals( getQ_approveDateEnd() ) ) 
		  setQ_approveDateEnd( getQ_approveDateStart() ) ;     // 若只輸入起始日則查詢當天的資料
	
          String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4008.jasper");
      
          Map<String, Object> parameters = new HashMap<String,Object>();

          String printDate = Datetime.getRocDateFromYYYYMMDD(Datetime.getCurrentDate("yyyy/MM/dd")) ;//列印時間年月日
          String printTime = Datetime.getHHMMSS() ;    // 列印時間時分秒                                                    
       
          printDate = DateFormat( printDate ) ;
          printTime = TimeFormat( printTime ) ;
      
          parameters.put("printDate", printDate);//列印時間
	      parameters.put("printTime", printTime);//列印時間
	  
	      String year = getQ_approveDateStart().substring(0, 3) ;
	      String month = getQ_approveDateStart().substring(3, 5) ;
	      String day = getQ_approveDateStart().substring(5) ;
	      String yearE = getQ_approveDateEnd().substring(0, 3) ;
	      String monthE = getQ_approveDateEnd().substring(3, 5) ;
	      String dayE = getQ_approveDateEnd().substring(5) ;
	  
	      parameters.put("dateStart", year + "年" + month +"月" + day + "日")  ;
	      parameters.put("dateEnd", yearE + "年" + monthE + "月" + dayE + "日"  ) ;
	  	  
	      
   	      if(logger.isInfoEnabled()) logger.info(doAppendSqljob(getQ_approveDateStart(), getQ_approveDateEnd(), getQ_idNo(), getQ_companyName()));	
   	      List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(getQ_approveDateStart(), getQ_approveDateEnd(), getQ_idNo(), getQ_companyName()));

   	      if ( rs == null || rs.size() <= 0 )
   		      throw new Exception( "查無資料，請變更查詢日期或承辦人姓名" ) ;
   	      else {
            ArrayList<PRE4008> dataList = new ArrayList<PRE4008>();
	        int i = 0;
	        while( i < rs.size()){
		      Map<String,Object> temp = rs.get(i);
		      PRE4008 data = new PRE4008();
		      data.setPrefixNo(Common.get(temp.get("PREFIX_NO"))) ;
		      data.setStaffName(Common.get(temp.get("STAFF_NAME")));
		      data.setCompanyName(Common.get(temp.get("COMPANY_NAME")));
		      data.setSpecialName(Common.get(temp.get("SPECIAL_NAME")));
		      data.setApproveDate(Common.get(temp.get("APPROVE_DATE")));
		      data.setCloseDate(Common.get(temp.get("CLOSE_DATE")));
		      data.setIdNo(Common.get(temp.get("ID_NO")));

		      dataList.add(data);
		      i++;
	        } // end while
	  	 
	        report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	        setErrorMsg("報表印製成功");
	        return report;
   	      } // end else
   	   
    } // end try
	catch ( MoeaException e ) {
		e.printStackTrace();
		setErrorMsg(e.getMessage());
		return null;
	} // catch
    catch( Exception e ) {
    	e.printStackTrace();
        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
		else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
        return null;
	} // end catch
  } // doPrintfPdf()	
  
  public static String checkForjsp( String dateStart, String dateEnd, String staff, String companyName ) {
	  List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(dateStart, dateEnd, staff, companyName));
	  if ( rs == null || rs.size() <= 0 )
		  return "查無資料，請變更查詢條件！";
	  else
		  return "ok";
  } 


  
} // PPE4008()