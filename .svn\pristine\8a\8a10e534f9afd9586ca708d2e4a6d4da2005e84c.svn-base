package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCountry;

/**
 * 國家代碼查詢(PRE8014)
 *
 */
public class PRE8014 extends SuperBean {

	private String code;	//國家代碼
	private String name;	//國家名稱

	private String q_code;
	private String q_name;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		CsmdCountry obj = new CsmdCountry();
		if(!"".equals(getQ_code()))
			obj.setCnCode(getQ_code());		
		if (!"".equals(getQ_name()))
			obj.setCn(getQ_name());

		java.util.ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>();
		java.util.List<CsmdCountry> tempList = ServiceGetter.getInstance().getPrefixService().queryCsmdCountry(obj);
		//this.processCurrentPageAttribute(tempList.size());
		if (tempList != null && tempList.size() > 0) {
			CsmdCountry dtl;
			String[] rowArray = new String[4];
			for (CsmdCountry dtlObj : tempList) {
				dtl = (CsmdCountry) dtlObj;
				rowArray = new String[4];
				rowArray[0] = Common.get(dtl.getCnCode());
				rowArray[1] = Common.get(dtl.getCn());
				rowArray[2] = Datetime.getYYYMMDD(dtl.getUpdateDate(), "/");
				rowArray[3] = Common.get(dtl.getUpdateUser());
				arrayList.add(rowArray);
			} 
			tempList.clear();
			this.setErrorMsg("查詢成功!");
		} else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件!");
		}
		return arrayList;
	}

	
	/*
	 * getters and setters
	 */
	public String getCode() {return checkGet(code);}
	public void setCode(String s) {this.code = checkSet(s);}
	public String getName() {return checkGet(name);}
	public void setName(String s) {this.name = checkSet(s);}
	public String getQ_code() {return checkGet(q_code);}
	public void setQ_code(String s) {this.q_code = checkSet(s);}
	public String getQ_name() {return checkGet(q_name);}
	public void setQ_name(String s) {this.q_name = checkSet(s);}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

} 