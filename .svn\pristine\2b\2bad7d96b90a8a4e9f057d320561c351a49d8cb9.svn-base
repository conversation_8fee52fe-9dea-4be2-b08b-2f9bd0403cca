<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ page import="com.kangdainfo.*"%>
<%@ page import="com.kangdainfo.common.util.*"%>
<%@ page import="com.kangdainfo.common.model.bo.*"%>
<%
String p_uid = "";
CommonUser commonUser = null;
String errorFlg = "1";
boolean isPass = ServiceGetter.getInstance().getAuthenticationService().authenticate(request);
if(!isPass) {
	//登入失敗
	session.invalidate();
	response.sendRedirect("index.jsp?error="+errorFlg);
} else {
	//登入成功
	response.sendRedirect("home/frame.jsp");
}
%>
<head>
<script>
function setCookie(c_name,value,exHours)
{
	var exdate=new Date();
	exdate.setHours(exdate.getHours() + exHours);
	var c_value=escape(value) + ((exHours==null) ? "" : "; expires="+exdate.toUTCString());
	document.cookie=c_name + "=" + c_value;
}
</script>
</head>
<body onload="setCookie('UID','<%=p_uid%>',1);">
</body>
</html>