package com.kangdainfo.tcfi.lucene.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.lucene.bo.IndexData;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class IndexDataDao extends BaseDaoJdbc implements RowMapper<IndexData> {

	/** 公司資料 */
	private static final String sql_getIndexDataKind1 = ""
			+ "SELECT "
			+ " BAN_NO id"
			+ " ,PREFIX_NO"
			+ " ,BAN_NO"
			+ " ,'' as APPLY_KIND"
			+ " ,COMPANY_NAME"
			+ " ,PART_NAME as SPECIAL_NAME"
			+ "	,'' as APPROVE_RESULT"
			+ " ,STATUS_CODE as CMPY_STATUS"
			//--沒有撤銷日期時,帶9999999
			//公司狀態存活時(01,02,03),不顯示解撤廢日
			+ "	,nvl(decode(STATUS_CODE,'01',null,'02',null,'03',null,END_DATE), '9999999') as REVOKE_APP_DATE"
			+ " ,SETUP_DATE"
			+ " ,'' as RECEIVE_DATE"
			+ " ,'' as RESERVE_DATE"
			+ " ,RESP_NAME as APPLY_NAME"
			+ " ,ORGN_TYPE"
			+ " ,'3' as SORTBY"
			+ " ,'1' as INDEX_TYPE"
			+ " ,'' as PREFIX_NO_SORT"
			+ " ,DECODE(STATUS_CODE,'04','y','05','y','06','y','09','y','10','y','11','y','12','y','13','y','n') as CMPY_REVOKE" // 2025/06/11 新增10~13 (還有許多廢棄狀態需考慮，待更新)
			+ " FROM CEDB2000"
			+ " WHERE"
			+ "	("
			+ "		(STATUS_CODE NOT IN ('07','11','12','13','14','15','16','24','29','30','32')"
			+ "		AND NOT (STATUS_CODE in ('04', '05', '06', '09')"
			+ "		AND add_months(NVL(TO_DATE(substr(END_DATE, 1, 3) + 1911 || substr(END_DATE, 4, 4),'yyyymmdd'),sysdate),12 * 10) < sysdate))"
			+ "		OR STATUS_CODE IS NULL"
			+ "	) AND ("
			+ "		(STATUS_CODE NOT IN ('07','11','12','13','14','15','16','24','29','30','32')"
			+ "		AND NOT ((STATUS_CODE in ('04', '05', '06', '09')"
			+ " 	AND add_months(NVL(TO_DATE(substr(END_DATE, 1, 3) + 1911 || substr(END_DATE, 4, 4),'yyyymmdd'),sysdate),12 * 10) < sysdate)"
			+ "		AND NOT (trim(END_DATE) is null AND END_NO in ('110503', '*********'))))"
			+ "		OR STATUS_CODE IS NULL"
			+ "	)"
//固定條件FOR Test
//			+ " AND ( COMPANY_NAME LIKE '%管理顧問%' OR PART_NAME LIKE '%管理顧問%' ) "
			+ "";

	/** 已收文資料 */
	private static final String sql_getIndexDataKind2 = ""
			+ "SELECT /*+parallel*/ "
			+ "	B.PREFIX_NO||'_'||A.SEQ_NO as ID"
			+ " ,B.PREFIX_NO"
			+ " ,B.BAN_NO"
			+ " ,B.APPLY_KIND"
			+ " ,A.COMPANY_NAME"
			+ " ,B.SPECIAL_NAME"
			+ "	,B.APPROVE_RESULT"
			+ "	,B.COMPANY_STUS as CMPY_STATUS"
			+ " ,'' as REVOKE_APP_DATE"
			+ "	,'' as SETUP_DATE"
			+ " ,B.RECEIVE_DATE"
			+ " ,B.RESERVE_DATE"

			//申請人資料增加代表人
			//+ " ,B.APPLY_NAME"
			+ " ,("
			+ " ( case when B.APPLY_NAME not like '%代表人%' then"
			+ " nvl((select (case when apply_law_name is not null then apply_law_name||' 代表人:' else null end) from cedb1022 where prefix_no = B.prefix_no),'')"
			+ " else null end )"
			+ " ||B.APPLY_NAME"
			+ " ) AS APPLY_NAME"

			+ " ,'' as ORGN_TYPE"
			+ "	,'1' as SORTBY"
			+ "	,'2' as INDEX_TYPE"
			+ "	,B.PREFIX_NO as PREFIX_NO_SORT"
			+ " ,'n' as CMPY_REVOKE"
			+ " FROM CEDB1001 A INNER JOIN CEDB1000 B on B.PREFIX_NO = A.PREFIX_NO"
			//(A)審查中
			+ " WHERE B.APPROVE_RESULT = 'A'"
			//收文日期要在一年內
			+ "	AND B.RECEIVE_DATE > LPAD(TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE),-12),'yyyymmdd')-19110000,7,'0')"
//固定條件FOR Test
//			+ " AND ( A.COMPANY_NAME LIKE '%管理顧問%' OR B.SPECIAL_NAME LIKE '%管理顧問%' ) "
			+ "";
	
	/** 預查資料 */
	private static final String sql_getIndexDataKind3 = ""
			+ "SELECT /*+parallel*/ "
			+ "	B.PREFIX_NO as ID"
			+ " ,B.PREFIX_NO"
			+ " ,B.BAN_NO"
			+ " ,B.APPLY_KIND"
			+ " ,B.COMPANY_NAME"
			+ " ,B.SPECIAL_NAME"
			+ "	,B.APPROVE_RESULT"
			+ "	,B.COMPANY_STUS as CMPY_STATUS"
			+ " ,'' as REVOKE_APP_DATE"
			+ "	,'' as SETUP_DATE"
			+ "	,B.RECEIVE_DATE"
			+ "	,nvl(B.RESERVE_DATE,'9999999') as RESERVE_DATE"

			//申請人資料增加代表人
			//+ " ,B.APPLY_NAME"
			+ " ,("
			+ " ( case when B.APPLY_NAME not like '%代表人%' then"
			+ " nvl((select (case when apply_law_name is not null then apply_law_name||' 代表人:' else null end) from cedb1022 where prefix_no = B.prefix_no),'')"
			+ " else null end )"
			+ " ||B.APPLY_NAME"
			+ " ) AS APPLY_NAME"
			
			+ " ,'' as ORGN_TYPE"
			+ "	,'2' as SORTBY"
			+ "	,'3' as INDEX_TYPE"
			+ "	,B.PREFIX_NO as PREFIX_NO_SORT"
			+ " ,'n' as CMPY_REVOKE"
			+ " FROM CEDB1000 B"
			//(Y)已核准保留
			+ " WHERE B.APPROVE_RESULT = 'Y'"
			//保留期限要超過(系統日期 -15天) 或 沒有保留期限(已核准未結案) 或 公司系統申請中(00,01)
			+ "	AND (B.RESERVE_DATE >= LPAD(TO_CHAR(TRUNC(SYSDATE-15),'yyyymmdd')-19110000,7,'0') OR B.RESERVE_DATE IS NULL OR (B.COMPANY_STUS='00' OR B.COMPANY_STUS='01'))"
			//公司系統已核准(03)的不包含, 沒有公司系統狀態的也不包含
			+ "	AND (B.COMPANY_STUS <> '03' OR B.COMPANY_STUS IS NULL)"
//固定條件FOR Test
//			+ " AND (B.COMPANY_NAME LIKE '%管理顧問%' OR B.SPECIAL_NAME LIKE '%管理顧問%' ) "
			+ "";
	
	/** 預查否准資料 */
	private static final String sql_getIndexDataKind4 = ""
			+ "SELECT /*+parallel*/ "
			+ "	B.PREFIX_NO||'_'||A.SEQ_NO as ID"
			+ " ,B.PREFIX_NO"
			+ " ,B.BAN_NO"
			+ " ,B.APPLY_KIND"
			+ " ,A.COMPANY_NAME"
			+ " ,B.SPECIAL_NAME"
			+ "	,B.APPROVE_RESULT"
			+ "	,B.COMPANY_STUS as CMPY_STATUS"
			+ "	,'' as REVOKE_APP_DATE"
			+ "	,'' as SETUP_DATE"
			+ " ,B.RECEIVE_DATE"
			+ "	,B.RESERVE_DATE"
			
			//申請人資料增加代表人
			//+ " ,B.APPLY_NAME"
			+ " ,("
			+ " ( case when B.APPLY_NAME not like '%代表人%' then"
			+ " nvl((select (case when apply_law_name is not null then apply_law_name||' 代表人:' else null end) from cedb1022 where prefix_no = B.prefix_no),'')"
			+ " else null end )"
			+ " ||B.APPLY_NAME"
			+ " ) AS APPLY_NAME"

			+ " ,'' as ORGN_TYPE"
			+ "	,'4' as SORTBY"
			+ "	,'4' as INDEX_TYPE"
			+ "	,B.PREFIX_NO as PREFIX_NO_SORT"
			+ " ,'n' as CMPY_REVOKE"
			+ " FROM CEDB1001 A INNER JOIN CEDB1000 B on B.PREFIX_NO = A.PREFIX_NO"
			//(N)否准
			+ " WHERE B.APPROVE_RESULT = 'N'"
			//收文日期要在一年內
			+ "	AND B.RECEIVE_DATE > LPAD(TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE),-12),'yyyymmdd')-19110000,7,'0')"
//固定條件FOR Test
//			+ " AND ( A.COMPANY_NAME LIKE '%管理顧問%' OR B.SPECIAL_NAME LIKE '%管理顧問%' ) "
			+ "";
	
	/** 有限合夥資料 */
	private static final String sql_getIndexDataKind5 = ""
			+ "SELECT "
			+ "	BAN_NO as ID"
			+ "	,'' as PREFIX_NO"	//--預查編號
			+ "	,BAN_NO"
			+ "	,'' as APPLY_KIND"
			+ "	,BUSS_NAME as COMPANY_NAME"
			+ " ,'' as SPECIAL_NAME"	//--特許名稱?
			+ "	,'' as APPROVE_RESULT"
			+ "	,(SELECT DESCRIPTION FROM LMS.LMSD_CODEMAPPING WHERE KIND = 'STAT' AND CODE = M.CURR_STATUS AND REG_UNIT_CODE = M.REG_UNIT_CODE AND ENABLE = 'Y') as CMPY_STATUS"
			//--沒有撤銷日期時,帶9999999
			//公司狀態存活時(01,02,03),不顯示解撤廢日
			+ "	,NVL(DECODE(CURR_STATUS, '01', NULL, SUBSTRB(LPAD(TO_CHAR(CLOSE_DATE, 'yyyymmdd') - 19110000, 7, '0'), 1, 7)), '9999999') as REVOKE_APP_DATE"
			+ "	,SUBSTRB(LPAD(TO_CHAR(SET_APP_DATE, 'yyyymmdd') - 19110000, 7, '0'), 1, 7) as SETUP_DATE"
			+ "	,'' as RECEIVE_DATE"
			+ "	,'' as RESERVE_DATE"
			+ "	,RES_NAME as APPLY_NAME"
			+ "	,'有限合夥' as ORGN_TYPE"
			+ "	,'5' as SORTBY"
			+ "	,'5' as INDEX_TYPE"
			+ "	,'' as PREFIX_NO_SORT"
			+ "	,'n' as CMPY_REVOKE"
			+ "	FROM LMS.LMSM_BUSS_MAIN M WHERE IS_NEWEST = 'Y' AND ORG_CODE = '18' ";
	
	public java.util.List<IndexData> query(String kind, String id) {
		if(null==kind || "".equals(kind)) return null;
		SQLJob sqljob = new SQLJob();
    	if(PrefixConstants.INDEX_TYPE_1.equals(kind))
    		sqljob.appendSQL(sql_getIndexDataKind1);
    	else if(PrefixConstants.INDEX_TYPE_2.equals(kind))
    		sqljob.appendSQL(sql_getIndexDataKind2);
    	else if(PrefixConstants.INDEX_TYPE_3.equals(kind))
    		sqljob.appendSQL(sql_getIndexDataKind3);
    	else if(PrefixConstants.INDEX_TYPE_4.equals(kind))
    		sqljob.appendSQL(sql_getIndexDataKind4);
    	else if(PrefixConstants.INDEX_TYPE_5.equals(kind))
    		sqljob.appendSQL(sql_getIndexDataKind5);
    	
    	if(null!=id && !"".equals(id)){
    		if(PrefixConstants.INDEX_TYPE_1.equals(kind) || PrefixConstants.INDEX_TYPE_5.equals(kind))
        		sqljob.appendSQL(" AND BAN_NO = ? ");
        	else if(PrefixConstants.INDEX_TYPE_2.equals(kind))
        		sqljob.appendSQL(" AND B.PREFIX_NO = ? ");
        	else if(PrefixConstants.INDEX_TYPE_3.equals(kind))
        		sqljob.appendSQL(" AND PREFIX_NO = ? ");
        	else if(PrefixConstants.INDEX_TYPE_4.equals(kind))
        		sqljob.appendSQL(" AND B.PREFIX_NO = ? ");
        	sqljob.addParameter(id);
    	}
    	if(logger.isDebugEnabled()) logger.debug(sqljob);
    	return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public IndexData mapRow(ResultSet rs, int arg1) throws SQLException {
		IndexData obj = null;
		if(null!=rs) {
			obj = new IndexData();
			obj.setId(Common.get(rs.getString("ID")));
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setApplyKind(Common.get(rs.getString("APPLY_KIND")));
			obj.setCompanyName(Common.get(rs.getString("COMPANY_NAME")));
			obj.setSpecialName(Common.get(rs.getString("SPECIAL_NAME")));
			obj.setApproveResult(Common.get(rs.getString("APPROVE_RESULT")));
			obj.setCmpyStatus(Common.get(rs.getString("CMPY_STATUS")));
			obj.setRevokeAppDate(Common.get(rs.getString("REVOKE_APP_DATE")));
			obj.setSetupDate(Common.get(rs.getString("SETUP_DATE")));
			obj.setReceiveDate(Common.get(rs.getString("RECEIVE_DATE")));
			obj.setReserveDate(Common.get(rs.getString("RESERVE_DATE")));
			obj.setApplyName(Common.get(rs.getString("APPLY_NAME")));
			obj.setOrgnType(Common.get(rs.getString("ORGN_TYPE")));
			obj.setSortby(Common.get(rs.getString("SORTBY")));
			obj.setIndexType(Common.get(rs.getString("INDEX_TYPE")));
			obj.setPrefixNoSort(Common.get(rs.getString("PREFIX_NO_SORT")));
			obj.setCmpyRevoke(Common.get(rs.getString("CMPY_REVOKE")));
		}
		return obj;
	}

}