package com.kangdainfo.tcfi.model.icms.bo;

import com.kangdainfo.persistence.BaseModel;


/**
 * 投審會核准案件檔(CSMM_MOEAIC_APPROVE)
 *
 */
public class CsmmMoeaicApprove extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 核准文號(投審會核准文號) */
	private String receiveNo;
	/** 預查文號 */
	private String prefixNo;
	/** 公司名稱 */
	private String cmpyName;
	/** 核准日期 */
	private String respDate;
	/** 案由(案由可能有多筆,以逗點隔開[設立0201,更名0601,所營變更0602]) */
	private String caseCode;
	/** 公司系統核准文號(公司系統核准文號註記) */
	private String rcvNo;
	/** 公司系統核准日期(公司系統核准日期註記) */
	private String recDate;
	/** 公司系統核准機關(公司系統核准申登機關註記) */
	private String regUnitCode;
	/** 案由說明 */
	private String caseDesc;
	/** 公司類別(1：僑 ；2：外；3：陸 ； 4：陸分) */
	private String cmpyType;
	/** 統一編號 */
	private String banNo;
	/** 營運資金 */
	private Integer plnAmount;
	/** 審定期限 */
	private String impDate;
	/** 鍵值 */
	private String tbpk;
	/** 發文字號 */
	private String sendword;

	public String getReceiveNo() {
		return receiveNo;
	}
	public void setReceiveNo(String receiveNo) {
		this.receiveNo = receiveNo;
	}
	public String getPrefixNo() {
		return prefixNo;
	}
	public void setPrefixNo(String prefixNo) {
		this.prefixNo = prefixNo;
	}
	public String getCmpyName() {
		return cmpyName;
	}
	public void setCmpyName(String cmpyName) {
		this.cmpyName = cmpyName;
	}
	public String getRespDate() {
		return respDate;
	}
	public void setRespDate(String respDate) {
		this.respDate = respDate;
	}
	public String getCaseCode() {
		return caseCode;
	}
	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}
	public String getRcvNo() {
		return rcvNo;
	}
	public void setRcvNo(String rcvNo) {
		this.rcvNo = rcvNo;
	}
	public String getRecDate() {
		return recDate;
	}
	public void setRecDate(String recDate) {
		this.recDate = recDate;
	}
	public String getRegUnitCode() {
		return regUnitCode;
	}
	public void setRegUnitCode(String regUnitCode) {
		this.regUnitCode = regUnitCode;
	}
	public String getCaseDesc() {
		return caseDesc;
	}
	public void setCaseDesc(String caseDesc) {
		this.caseDesc = caseDesc;
	}
	public String getCmpyType() {
		return cmpyType;
	}
	public void setCmpyType(String cmpyType) {
		this.cmpyType = cmpyType;
	}
	public String getBanNo() {
		return banNo;
	}
	public void setBanNo(String banNo) {
		this.banNo = banNo;
	}
	public Integer getPlnAmount() {
		return plnAmount;
	}
	public void setPlnAmount(Integer plnAmount) {
		this.plnAmount = plnAmount;
	}
	public String getImpDate() {
		return impDate;
	}
	public void setImpDate(String impDate) {
		this.impDate = impDate;
	}
	public String getTbpk() {
		return tbpk;
	}
	public void setTbpk(String tbpk) {
		this.tbpk = tbpk;
	}
	public String getSendword() {
		return sendword;
	}
	public void setSendword(String sendword) {
		this.sendword = sendword;
	}

}
