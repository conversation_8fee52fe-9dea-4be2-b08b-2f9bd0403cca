<%--
程式目的：收文-列印預查申請表
程式代號：PRE1004
撰寫日期：103.04.25
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
--%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1004">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1004" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())){
	java.io.File report = obj.makeMutiReportByPrefixNo();
	if(null!=report)
	{
		if("true".equals(obj.getIsAutoPrint())) {
			obj.outputFile(response, report, report.getName(),true);
		} else {
			obj.outputFile(response, report, report.getName(),false);
		}
		out.clear();
		out = pageContext.pushBody();
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
var child = null;
$(function() {
	var state = form1.state.value;
	if(state == "printFail") {
		alert("查無資料，無法列印");
	}
	var $prefixNo = $("input[name=prefixNo]");
	if( $prefixNo.val() && $prefixNo.val() ) {
		$prefixNo.focus();
	}
	if( $prefixNo.val() && $prefixNo.val() == $("input[name=prefixNoEnd]").val() ) {
		$("input[name=btnQuery2]").trigger('click');
	}
});
	
function doSearch(print) {
	// 103/10/06 優仲表示承辦人希望申請表視窗pop出時,可固定大小為1024*768
	var x = form1.prefixNo.value;
	var xEnd = form1.prefixNoEnd.value;
	var prop="";
	var width = window.screen.width;
	width = 1024;
	var height = window.screen.height;
	height = 700;
	var top = (window.screen.height - height);
	top = 0;
	var left = (window.screen.width - width);
	left = 0;
	prop=prop+"width="+width+"px,";
	prop=prop+"height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=1,resizable=1,location=0";
	
	if(x.length < 9 ){
		$("#ERRMSG").text("預查編號太短或未填寫(PREFIX NO ERROR)");
		return;
	}
	
	if(xEnd != '' && xEnd.length < 9 ){
		$("#ERRMSG").text("預查編號太短或未填寫(PREFIX NO ERROR)");
		return;
	}
	
	if ((xEnd - x) > 99) {
		$("#ERRMSG").text("請勿超過100筆！");
		return;
	}
	
	if (!doSomeCheck()) {
		if (x == xEnd)
			$("#ERRMSG").text("沒有這筆預查編號");
		else
			$("#ERRMSG").text("沒有這些預查編號");
		return;
	}	
	
	document.forms[0].state.value = "queryAll";
	document.forms[0].print.value = "print";
    var target = 'PRE1004_'+randomUUID().replace(/\-/g,"");
    if (child!=null)
    	child.close();
    child = window.open("",target, prop);
	form1.target = target;
	$("#ERRMSG").text("　");
    document.forms[0].submit();

    if(commonUtils.getURLParameter("from")) {
   		window.close();
    }
}
  
function reset(){
	form1.reset();
}

function init(){
	//setDisplayItem("spanDoConfirm,spanQueryAll,spanInsert,spanClear,spanConfirm,spanUpdate,spanDelete,spanListPrint,spanListHidden","H");
}
  
function doSomeCheck(){
	var prefixNo = form1.prefixNo.value;
	var prefixNoEnd = form1.prefixNoEnd.value;
	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre1004.jsp?prefixNo='+prefixNo+'&prefixNoEnd='+prefixNoEnd);
	return (x=='ok');
}
  
function keyDown() {
	if (event.keyCode==13) {
		doSearch();
	}
}

function changeSearchType() {
	if ( document.getElementById('onlyReceive').checked ) 
		document.forms[0].searchType.value = "onLineCnt";
	else
		document.forms[0].searchType.value = "2";

}
</script>
</head>
<body topmargin="5" onLoad="init();">
<form id="form1" name="form1" method="post" >

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1004'/>
</c:import>

<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="left">
			<input class="toolbar_default" type="button" name="btnQuery1" value="重新輸入" onClick="reset();">&nbsp;
			<input class="toolbar_default" type="button" name="btnQuery2" value="報表產製" onClick="doSearch();">&nbsp;
		</td>
		<td align="right">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
			</c:import>
		</td>
	</tr>
</table>

<table class="table_form" width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td>
	<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
		<tr>
			<td class="td_form" width="100px">預查編號：</td>
			<td class="td_form_white">
				<input type="text" class="field" id="prefixNo" name="prefixNo" maxlength="9" size="10" value="<%=obj.getPrefixNo() %>" onKeyDown="keyDown()">
				~
				<input type="text" class="field" id="prefixNoEnd" name="prefixNoEnd" maxlength="9" size="10" value="<%=obj.getPrefixNoEnd() %>" onKeyDown="keyDown()">
				(欲列印一張只需輸入前面那一欄亦或輸入相同預查編號)
			</td>
		</tr>
<% if( "local".equals(com.kangdainfo.ServiceGetter.getInstance().getEnv()) ) { %>
		<tr>
			<td class="td_form">&nbsp;</td>
			<td class="td_form_white">
				<input type="checkbox" id="onlyReceive" name="onlyReceive" value="Y" onclick="changeSearchType();">列印原始收文資料
			</td>
		</tr>
<% } %>
	</table>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" width="100%" style="text-align:left">
	<input type="hidden" name="print" value="" />
	<input type="hidden" name="searchType" value="2">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
</td></tr>

</table>	
</form>
</body>
</html>