package com.kangdainfo.util.io;

import java.io.File;
import java.io.IOException;
import java.util.Properties;
import java.util.Set;
import java.util.TreeSet;

import com.kangdainfo.util.io.configFile.ConfigFileParser;


public class ConfigFile {

	private Input input;
	private Output output;
	
	public Input getInput() { return input; }
	public void setInput(Input input) { this.input = input; }
	public Output getOutput() { return output; }
	public void setOutput(Output output) { this.output = output; }
	
	public class Input {
		private File files[];
		private File file;
		private String rawContent;		
		private boolean ignoreHashLine;
		
		private Input() {
		}
		
		public File getFile() {return file;}
		public void setFile(File f) {this.file = f;}
		public File[] getFiles() {return files;}
		public void setFiles(File[] fs) {this.files = fs;}
		public boolean isIgnoreHashLine() {return ignoreHashLine;}
		public void setIgnoreHashLine(boolean b) {this.ignoreHashLine = b;}
		public String getRawContent() {return rawContent;}
		public void setRawContent(String s) {this.rawContent = s;}
	}

	public class Output {
		private String parsedContent;
		private Properties properties;
		private Set<String> duplicateKeys;

		private Output () {
			properties = new Properties();
			duplicateKeys = new TreeSet<String>();
		}

		public Set<String> getDuplicateKeys() { return duplicateKeys; }
		public void setDuplicateKeys(Set<String> s) { this.duplicateKeys = s; }
		public String getParsedContent() { return parsedContent; }
		public void setParsedContent(String s) { this.parsedContent = s; }
		public Properties getProperties() { return properties; }
		public void setProperties(Properties p) { this.properties = p; }		
	}

	private ConfigFile() {
		input = new Input();
		output = new Output();
	}
	
	public static ConfigFile getInstance() {
		ConfigFile configFile = new ConfigFile();
		return configFile;
	}
	
	public void parse() throws IOException {
		ConfigFileParser.parse(this);	
	}

}