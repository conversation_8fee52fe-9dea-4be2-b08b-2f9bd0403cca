package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;

public class Eedb3300Dao extends BaseDaoJdbc implements RowMapper<Eedb3300> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB3300 WHERE TELIX_NO = ?";
	public Eedb3300 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb3300> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public Eedb3300 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb3300 obj = null;
		if(null!=rs) {
			obj = new Eedb3300();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setOrgCorpNo(rs.getString("ORG_CORP_NO"));
			obj.setOrgCorpName(rs.getString("ORG_CORP_NAME"));
			obj.setOrgApplyName(rs.getString("ORG_APPLY_NAME"));
			obj.setOrgApplyId(rs.getString("ORG_APPLY_ID"));
		}
		return obj;
	}

}
