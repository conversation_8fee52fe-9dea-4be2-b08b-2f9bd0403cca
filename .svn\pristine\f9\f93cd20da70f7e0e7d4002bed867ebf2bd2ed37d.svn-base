package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgBranch;

public class OssmOrgBranchDao extends BaseDaoJdbc implements RowMapper<OssmOrgBranch> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSS.OSSM_ORG_BRANCH WHERE TELIX_NO = ? ORDER BY TELIX_NO, SEQ_NO";
	public List<OssmOrgBranch> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmOrgBranch>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public OssmOrgBranch mapRow(ResultSet rs, int idx) throws SQLException {
		OssmOrgBranch obj = null;
		if(null!=rs) {
			obj = new OssmOrgBranch();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getInt("SEQ_NO"));
			obj.setCaseCode(rs.getString("CASE_CODE"));
			obj.setBrBanNo(rs.getString("BR_BAN_NO"));
			obj.setBrName(rs.getString("BR_NAME"));
			obj.setCaseType(rs.getString("CASE_TYPE"));
		}
		return obj;
	}

}