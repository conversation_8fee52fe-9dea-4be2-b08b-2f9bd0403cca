/*
 *--------------------------------------------------------------------------------------------
 * Copyright (c) 2007 Acer, Inc. All Rights Reserved.
 *
 * This software is proprietary to and embodies the confidential technology
 * of Acer Internet Services, Inc.. Possession, use or copying of this software
 * and media is authorized only pursuant to a valid written license from Acer
 * Internet Services, Inc. or an authorized sublicensor.
 *---------------------------------------------------------------------------------------------
 */

/*
 *--------------------------------------------------------------------------------------------
 *
 * Author : Unknow
 *
 * Modification Log :
 * Vers	Date			By				Notes
 * ----	---------------	---------------	------------------------------------------------
 * V1.0                                 Create
 * v2.0 2008/11/12      Zion            Modify
 *------------------------------------------------------------------------------------------
 */

package com.kangdainfo.sys.common;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import org.apache.commons.lang.StringUtils;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;

/**
 * <p>Title: 試辦系統</p>
 * <p>Description: 共用常數</p>
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: III</p>
 * <AUTHOR>
 * @version 1.0
 */

public final class Constants {
	
	public static final boolean IF_OS_WINDOWS = checkIfOsWindows();// 是否部屬之機器作業系統為windows
	public static final String MACHINE_NAME = (IF_OS_WINDOWS) ? System.getenv("COMPUTERNAME") : checkLinuMachineName();// 系統機器名
//	public static final 
	
    /**
     * 同音同義字對照表
     */
    public static HashMap<String, HashSet<String>> C_cedbc058Map;
    public static void load_C_cedbc058Map() {
        //載入cedbc058 同音同義字對照表
        List<Cedbc058> cedbc058s = new ArrayList<Cedbc058>();
        C_cedbc058Map = new HashMap<String, HashSet<String>>();
        try {
            cedbc058s = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058Codes();
            Cedbc058 cedbc058;
            for (int i = 0; i < cedbc058s.size(); i++) {
                cedbc058 = (Cedbc058) (cedbc058s.get(i));
                addC_cedbc058Map(C_cedbc058Map,cedbc058.getSameName(),cedbc058.getSameName1());
                addC_cedbc058Map(C_cedbc058Map,cedbc058.getSameName1(),cedbc058.getSameName());
            }
        }
        catch (Exception ex) {
            System.out.println("讀取talbe Cedbc058失敗");
        }
    }
    private static void addC_cedbc058Map(HashMap<String, HashSet<String>> C_cedbc058Map,String key,String value)
    {
        if(!C_cedbc058Map.containsKey(key))
            C_cedbc058Map.put(key,new HashSet<String>());
        C_cedbc058Map.get(key).add(value);
    }

    //專業經營項目，後面的說明沒有用到，網頁均提示「限專業經營」。
    public static final HashMap<String, String> C_BUS_ITEM_REST = new HashMap<String, String>();
    static {
    	
		String sql = "SELECT ITEM_CODE, NAME FROM RESTRICTION_ITEM A LEFT JOIN RESTRICTION B ON A.RESTRICTION_ID = B.ID";
		List<Map<String, Object>> list = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql);
		
		if (list != null && list.size() > 0) {
			java.util.Map<String, Object> obj;
			String code, name;
			String[] splits;
			for (int i = 0; i < list.size(); i++) {
				obj = (java.util.Map<String, Object>) list.get(i);
				code = Common.get(obj.get("ITEM_CODE"));
				splits = Common.get(obj.get("NAME")).split("、");
				if(splits != null && splits.length > 1) {
					name = splits[1];
				} else {
					name = Common.get(obj.get("NAME"));
				}
				C_BUS_ITEM_REST.put(code, name);
			}
		}
    	
    	/**
    	//一、名稱須標明專業、營業項目限專業經營、並以「股份有限公司」型態經營為限
    	C_BUS_ITEM_REST.put("H101021", "限專業經營，並以「股份有限公司」型態經營");//H101021:商業銀行業
    	C_BUS_ITEM_REST.put("H101041", "限專業經營，並以「股份有限公司」型態經營");//H101041:信託投資公司業
        C_BUS_ITEM_REST.put("H101051", "限專業經營，並以「股份有限公司」型態經營");//H101051:工業銀行業
        C_BUS_ITEM_REST.put("H101061", "限專業經營，並以「股份有限公司」型態經營");//H101061:農業銀行業
        C_BUS_ITEM_REST.put("H101081", "限專業經營，並以「股份有限公司」型態經營");//H101081:中小企業銀行業
        C_BUS_ITEM_REST.put("H101091", "限專業經營，並以「股份有限公司」型態經營");//H101091:不動產信用銀行業
        C_BUS_ITEM_REST.put("H102011", "限專業經營，並以「股份有限公司」型態經營");//H102011:票券金融業
        C_BUS_ITEM_REST.put("H104011", "限專業經營，並以「股份有限公司」型態經營");//H104011:信用卡業
        C_BUS_ITEM_REST.put("H105011", "限專業經營，並以「股份有限公司」型態經營");//H105011:信託業
        C_BUS_ITEM_REST.put("H108011", "限專業經營，並以「股份有限公司」型態經營");//H108011:農業金庫業
        C_BUS_ITEM_REST.put("H201020", "限專業經營，並以「股份有限公司」型態經營");//H201020:中小企業開發業
        C_BUS_ITEM_REST.put("H501011", "限專業經營，並以「股份有限公司」型態經營");//H501011:人身保險業
        C_BUS_ITEM_REST.put("H501021", "限專業經營，並以「股份有限公司」型態經營");//H501021:財產保險業
        C_BUS_ITEM_REST.put("H501031", "限專業經營，並以「股份有限公司」型態經營");//H501031:再保險業
        C_BUS_ITEM_REST.put("H801011", "限專業經營，並以「股份有限公司」型態經營");//H801011:金融控股公司業
        C_BUS_ITEM_REST.put("I901011", "限專業經營，並以「股份有限公司」型態經營");//I901011:保全業
        //二、名稱標明專業、營業項目限專業經營、並以「公司」組織型態經營為限
        C_BUS_ITEM_REST.put("H601011", "限專業經營");//H601011:人身保險代理人
        C_BUS_ITEM_REST.put("H601021", "限專業經營");//H601021:財產保險代理人
        C_BUS_ITEM_REST.put("H602011", "限專業經營");//H602011:人身保險經紀人
        C_BUS_ITEM_REST.put("H602021", "限專業經營");//H602021:財產保險經紀人
        C_BUS_ITEM_REST.put("J902011", "限專業經營，公司名稱應標明「旅行社」字樣");//J902011:旅行業
        //四、營業項目限專業經營、並以「股份有限公司」型態經營
        C_BUS_ITEM_REST.put("G104021", "限專業經營，並以「股份有限公司」型態經營");//G104021:森林鐵路經營業（非經主管機關核准不得兼營其他附屬事業）
        C_BUS_ITEM_REST.put("G606011", "限專業經營，並以「股份有限公司」型態經營");//G606011:機場經營業（經營其他事項須經主管機關核准）
    	C_BUS_ITEM_REST.put("H309011", "限專業經營，並以「股份有限公司」型態經營");//H309011:櫃檯買賣業
    	C_BUS_ITEM_REST.put("H901011", "限專業經營，並以「股份有限公司」型態經營");//H901011:證券期貨控股業
    	C_BUS_ITEM_REST.put("HZ05011", "限專業經營，並以「股份有限公司」型態經營");//HZ05011:電子票證業
		//九、名稱須標明專業、營業項目限專業經營(但不限組織別)
        C_BUS_ITEM_REST.put("H203011", "限專業經營");//H203011:當舖業
        //十三、營業項目限專業經營(但名稱不須標明專業)
        C_BUS_ITEM_REST.put("D101011", "限專業經營");//D101011:發電業（非經主管機關核准不得兼營其他非電事業）
        C_BUS_ITEM_REST.put("D101021", "限專業經營");//D101021:輸電業（非經主管機關核准不得兼營其他非電事業）
        C_BUS_ITEM_REST.put("D101031", "限專業經營");//D101031:配電業（非經主管機關核准不得兼營其他非電事業）
        **/
    }

    //限制公司型態經營的營業項目，網頁提示後面的說明。
    public static final HashMap<String, String> C_BUS_ITEM_COMPORG = new HashMap<String, String>();
    static {
    	//二、名稱標明專業、營業項目限專業經營、並以「公司」組織型態經營為限　「」
    	C_BUS_ITEM_COMPORG.put("H602011", "同時具備資格者，得同時經營「財產保險經紀人」");//H602011:人身保險經紀人
    	C_BUS_ITEM_COMPORG.put("H602021", "同時具備資格者，得同時經營「人身保險經紀人」");//H602021:財產保險經紀人
    	C_BUS_ITEM_COMPORG.put("J902011", "公司名稱應標明「旅行社」字樣");//J902011:旅行業
    	C_BUS_ITEM_COMPORG.put("G203011", "公司名稱應標明「救護車」字樣");//G203011:救護車經營業
        //三、名稱標明專業、且限以「股份有限公司」型態經營
        C_BUS_ITEM_COMPORG.put("H305011", "經核准兼營短期票券集中保管結算業務者，公司名稱應標示「集中保管結算所」字樣");//H305011:證券集中保管業
        C_BUS_ITEM_COMPORG.put("H406011", "兼營其他業務者可不標明專業");//H406011:期貨信託事業（兼營其他業務者可不標明專業）
    	C_BUS_ITEM_COMPORG.put("HZ03011", "註：公司名稱應標明「特殊目的公司」字樣");//HZ03011:金融資產證券化特殊目的公司業(註：公司名稱應標明「特殊目的公司」字樣)
    	//四、營業項目限專業經營、並以「股份有限公司」型態經營
    	C_BUS_ITEM_COMPORG.put("G104021", "非經主管機關核准不得兼營其他附屬事業");//G104021:森林鐵路經營業
    	C_BUS_ITEM_COMPORG.put("G606011", "經營其他事項須經主管機關核准");//G606011:機場經營業
    	C_BUS_ITEM_COMPORG.put("HZ05011", "惟電子票證發行管理條例公布施行前已發行電子票證之發行機構經主管機關核准者，不受專業經營之限制");//HZ05011電子票證業
    	//五、限以「股份有限公司」型態經營
		C_BUS_ITEM_COMPORG.put("C112011", "限「股份有限公司」型態經營");//C112011:製菸業
		C_BUS_ITEM_COMPORG.put("C801081", "限「股份有限公司」型態經營");//C801081:食用酒精製造業
		C_BUS_ITEM_COMPORG.put("C801091", "限「股份有限公司」型態經營");//C801091:非食用酒精製造業
		C_BUS_ITEM_COMPORG.put("C803011", "限「股份有限公司」型態經營");//C803011:石油煉製業
		C_BUS_ITEM_COMPORG.put("D201011", "限「股份有限公司」型態經營（不得為「閉鎖性股份有限公司」）");//D201011:公用煤氣業
		C_BUS_ITEM_COMPORG.put("F112010", "限「股份有限公司」型態經營");//F112010:汽油、柴油批發業
		C_BUS_ITEM_COMPORG.put("F401151", "限「股份有限公司」型態經營");//F401151:石油輸入業
		C_BUS_ITEM_COMPORG.put("G102011", "限「股份有限公司」型態經營");//G102011:大眾捷運系統運輸業
		C_BUS_ITEM_COMPORG.put("G104011", "限「股份有限公司」型態經營");//G104011:高速鐵路經營業
		C_BUS_ITEM_COMPORG.put("G301011", "限「股份有限公司」型態經營");//G301011:船舶運送業
		C_BUS_ITEM_COMPORG.put("G901011", "限「股份有限公司」型態經營");//G901011:第一類電信事業
		C_BUS_ITEM_COMPORG.put("H103011", "限「股份有限公司」型態經營");//H103011:外匯經紀商
		C_BUS_ITEM_COMPORG.put("H107011", "限「股份有限公司」型態經營");//H107011:短期票券集中保管結算機構
		C_BUS_ITEM_COMPORG.put("H307011", "限「股份有限公司」型態經營");//H307011:信用評等業
		C_BUS_ITEM_COMPORG.put("H308011", "限「股份有限公司」型態經營");//H308011:都市更新投資信託業
		C_BUS_ITEM_COMPORG.put("H402011", "限「股份有限公司」型態經營");//H402011:期貨交易所
		C_BUS_ITEM_COMPORG.put("H403011", "限「股份有限公司」型態經營");//H403011:期貨結算機構
		C_BUS_ITEM_COMPORG.put("H404011", "限「股份有限公司」型態經營");//H404011:槓桿交易商
		C_BUS_ITEM_COMPORG.put("H408011", "限「股份有限公司」型態經營");//H408011:期貨交易輔助人
		C_BUS_ITEM_COMPORG.put("H701040", "限「股份有限公司」型態經營");//H701040:特定專業區開發業
		C_BUS_ITEM_COMPORG.put("H701080", "限「股份有限公司」型態經營");//H701080:都市更新重建業
		C_BUS_ITEM_COMPORG.put("HZ02031", "限「股份有限公司」型態經營");//HZ02031:公正第三人資產拍賣業務
		C_BUS_ITEM_COMPORG.put("J501011", "限「股份有限公司」型態經營");//J501011:廣播業
		C_BUS_ITEM_COMPORG.put("J502011", "限「股份有限公司」型態經營");//J502011:電視業
		C_BUS_ITEM_COMPORG.put("J504011", "限「股份有限公司」型態經營");//J504011:有線電視系統經營業
		C_BUS_ITEM_COMPORG.put("J506011", "限「股份有限公司」型態經營");//J506011:直播衛星廣播電視服務經營業
		C_BUS_ITEM_COMPORG.put("J506021", "限「股份有限公司」型態經營");//J506021:衛星廣播電視節目供應業
        //六、名稱標明專業、且限以「公司」型態經營
		C_BUS_ITEM_COMPORG.put("I801011","公司名稱應標明「公寓大廈管理維護」字樣");//I801011:公寓大廈管理服務業（註：公司名稱應標明「公寓大廈管理維護」字樣）
        // C_BUS_ITEM_COMPORG.put("G203010","公司名稱應標明「救護車」字樣");//G203010:救護車經營業(註：公司名稱應標明「救護車」字樣)
        C_BUS_ITEM_COMPORG.put("H603011","公司名稱應標明「保險公證」字樣");//H603011:一般保險公證人(公司名稱應標明「保險公證」字樣)
        C_BUS_ITEM_COMPORG.put("H603021","公司名稱應標明「保險公證」字樣");//H603021:海事保險公證人(公司名稱應標明「保險公證」字樣)
        //七、限以「公司」型態經營「」
        C_BUS_ITEM_COMPORG.put("G101041", "甲種：限以「公司」組織經營；乙種、丙種：無限制");//G101041:小客車租賃業
        C_BUS_ITEM_COMPORG.put("H704031", "經營國外不動產仲介業務者，以「公司」為限");//H704031:不動產仲介經紀業
        C_BUS_ITEM_COMPORG.put("H704041", "經營國外不動產代銷業務者，以「公司」為限");//H704041:不動產代銷經紀業
        C_BUS_ITEM_COMPORG.put("I701011", "辦理下列業務，應以「公司」組織型態經營：1. 仲介外國人至中華民國境內工作；2. 仲介香港或澳門居民、大陸地區人民至台灣地區工作；" +
        		"3. 仲介本國人至台灣地區以外之地區工作");
        //八、限本國公司經營
		C_BUS_ITEM_COMPORG.put("IZ17010", "限本國公司經營");//IZ17010:遊說業
		C_BUS_ITEM_COMPORG.put("I701021", "限本國公司經營");//I701021:境外大陸船員仲介服務業
		//十、名稱須標明專業(但不限專業經營亦不限組織別)
		C_BUS_ITEM_COMPORG.put("E801060", "經營本項業務，名稱應標明「室內裝修」字樣");//E801060:室內裝修業(經營本項業務，名稱應標明「室內裝修」字樣)
		C_BUS_ITEM_COMPORG.put("G101061", "專辦搬家業務者，名稱應標明「搬家」字樣");//G101061:汽車貨運業（註：專辦搬家業務者，名稱應標明「搬家」字樣。）
		C_BUS_ITEM_COMPORG.put("J701010", "經營本項業務，名稱應標明「電子遊戲場業」字樣");//J701010:電子遊戲場業(經營本項業務，名稱應標明「電子遊戲場業」字樣)
		//十一、限「中華郵政股份有限公司」經營
		C_BUS_ITEM_COMPORG.put("GA01010", "限「中華郵政股份有限公司」經營");//GA01010:郵政業
		C_BUS_ITEM_COMPORG.put("H106010", "限「中華郵政股份有限公司」經營");//H106010:郵政儲金匯兌業
		C_BUS_ITEM_COMPORG.put("H501040", "限「中華郵政股份有限公司」經營");//H501040:簡易人壽保險業
		//十二、限「中央存款保險股份有限公司」經營
		C_BUS_ITEM_COMPORG.put("HZ04010", "限「中央存款保險股份有限公司」經營");//HZ04010:存款保險業
		//十三、營業項目限專業經營(但名稱不須標明專業)
		//C_BUS_ITEM_COMPORG.put("D101011", "非經主管機關核准不得經營其他非電事業");//D101011:發電業
		//C_BUS_ITEM_COMPORG.put("D101021", "非經主管機關核准不得經營其他非電事業");//D101021:輸電業
		//C_BUS_ITEM_COMPORG.put("D101031", "非經主管機關核准不得經營其他非電事業");//D101031:配電業
    }

    // Zion : 若有下列營業項目，保留期限應預設為一年
    /* 查無程式參考到該變數.HB@20190308
    public static final Vector C_BUS_ITEM_365 = new Vector();
    static {
        C_BUS_ITEM_365.add("H101021"); //商業銀行業
        C_BUS_ITEM_365.add("H101041"); //信託投資公司業
        C_BUS_ITEM_365.add("H101051"); //工業銀行業
        C_BUS_ITEM_365.add("H101061"); //農業銀行業
        C_BUS_ITEM_365.add("H101081"); //中小企業銀行業
        C_BUS_ITEM_365.add("H101091"); //不動產信用銀行業
        C_BUS_ITEM_365.add("H101991"); //其他銀行業
        C_BUS_ITEM_365.add("H102011"); //票券金融業
        C_BUS_ITEM_365.add("H301011"); //證券商
        C_BUS_ITEM_365.add("H302011"); //證券金融業
        C_BUS_ITEM_365.add("H401011"); //期貨商
        C_BUS_ITEM_365.add("H501011"); //人身保險業
        C_BUS_ITEM_365.add("H501021"); //財產保險業
        C_BUS_ITEM_365.add("H501031"); //再保險業
        C_BUS_ITEM_365.add("J504011"); //有線電視系統經營業
    }
    */

    // Zion 大陸商可使用營業項目
    public static final Vector<String> C_CHINA_BUS_ITEM = new Vector<String>();
    static {
        C_CHINA_BUS_ITEM.add("A102060");
        C_CHINA_BUS_ITEM.add("C301010");
        C_CHINA_BUS_ITEM.add("C302010");
        C_CHINA_BUS_ITEM.add("C303010");
        C_CHINA_BUS_ITEM.add("C305010");
        C_CHINA_BUS_ITEM.add("C306010");
        C_CHINA_BUS_ITEM.add("C307010");
        C_CHINA_BUS_ITEM.add("C399990");
        C_CHINA_BUS_ITEM.add("C804010");
        C_CHINA_BUS_ITEM.add("C804020");
        C_CHINA_BUS_ITEM.add("C804990");
        C_CHINA_BUS_ITEM.add("C805010");
        C_CHINA_BUS_ITEM.add("C805020");
        C_CHINA_BUS_ITEM.add("C805030");
        C_CHINA_BUS_ITEM.add("C805050");
        C_CHINA_BUS_ITEM.add("C805990");
        C_CHINA_BUS_ITEM.add("CB01010");
        C_CHINA_BUS_ITEM.add("CB01071");
        C_CHINA_BUS_ITEM.add("CC01010");
        C_CHINA_BUS_ITEM.add("CC01020");
        C_CHINA_BUS_ITEM.add("CC01030");
        C_CHINA_BUS_ITEM.add("CC01060");
        C_CHINA_BUS_ITEM.add("CC01070");
        C_CHINA_BUS_ITEM.add("CC01080");
        C_CHINA_BUS_ITEM.add("CC01101");
        C_CHINA_BUS_ITEM.add("CC01110");
        C_CHINA_BUS_ITEM.add("CC01120");
        C_CHINA_BUS_ITEM.add("CC01990");
        C_CHINA_BUS_ITEM.add("CD01010");
        C_CHINA_BUS_ITEM.add("CD01030");
        C_CHINA_BUS_ITEM.add("CD01050");
        C_CHINA_BUS_ITEM.add("CE01010");
        C_CHINA_BUS_ITEM.add("CE01030");
        C_CHINA_BUS_ITEM.add("CE01990");
        C_CHINA_BUS_ITEM.add("CF01011");
        C_CHINA_BUS_ITEM.add("CH01010");
        C_CHINA_BUS_ITEM.add("CI01010");
        C_CHINA_BUS_ITEM.add("CJ01010");
        C_CHINA_BUS_ITEM.add("CN01010");
        C_CHINA_BUS_ITEM.add("F101040");
        C_CHINA_BUS_ITEM.add("F101050");
        C_CHINA_BUS_ITEM.add("F101100");
        C_CHINA_BUS_ITEM.add("F101130");
        C_CHINA_BUS_ITEM.add("F101990");
        C_CHINA_BUS_ITEM.add("F102030");
        C_CHINA_BUS_ITEM.add("F102040");
        C_CHINA_BUS_ITEM.add("F102050");
        C_CHINA_BUS_ITEM.add("F102170");
        C_CHINA_BUS_ITEM.add("F104110");
        C_CHINA_BUS_ITEM.add("F105050");
        C_CHINA_BUS_ITEM.add("F106020");
        C_CHINA_BUS_ITEM.add("F107010");
        C_CHINA_BUS_ITEM.add("F107020");
        C_CHINA_BUS_ITEM.add("F107030");
        C_CHINA_BUS_ITEM.add("F107041");
        C_CHINA_BUS_ITEM.add("F107170");
        C_CHINA_BUS_ITEM.add("F107200");
        C_CHINA_BUS_ITEM.add("F107990");
        C_CHINA_BUS_ITEM.add("F108011");
        C_CHINA_BUS_ITEM.add("F108021");
        C_CHINA_BUS_ITEM.add("F108031");
        C_CHINA_BUS_ITEM.add("F108040");
        C_CHINA_BUS_ITEM.add("F109070");
        C_CHINA_BUS_ITEM.add("F110010");
        C_CHINA_BUS_ITEM.add("F110020");
        C_CHINA_BUS_ITEM.add("F111090");
        C_CHINA_BUS_ITEM.add("F112010");
        C_CHINA_BUS_ITEM.add("F112020");
        C_CHINA_BUS_ITEM.add("F112030");
        C_CHINA_BUS_ITEM.add("F112040");
        C_CHINA_BUS_ITEM.add("F113010");
        C_CHINA_BUS_ITEM.add("F113020");
        C_CHINA_BUS_ITEM.add("F113030");
        C_CHINA_BUS_ITEM.add("F113050");
        C_CHINA_BUS_ITEM.add("F113070");
        C_CHINA_BUS_ITEM.add("F113990");
        C_CHINA_BUS_ITEM.add("F114010");
        C_CHINA_BUS_ITEM.add("F114020");
        C_CHINA_BUS_ITEM.add("F114030");
        C_CHINA_BUS_ITEM.add("F114060");
        C_CHINA_BUS_ITEM.add("F114070");
        C_CHINA_BUS_ITEM.add("F114080");
        C_CHINA_BUS_ITEM.add("F114990");
        C_CHINA_BUS_ITEM.add("F115010");
        C_CHINA_BUS_ITEM.add("F116010");
        C_CHINA_BUS_ITEM.add("F117010");
        C_CHINA_BUS_ITEM.add("F118010");
        C_CHINA_BUS_ITEM.add("F119010");
        C_CHINA_BUS_ITEM.add("F199010");
        C_CHINA_BUS_ITEM.add("F199990");
        C_CHINA_BUS_ITEM.add("F201010");
        C_CHINA_BUS_ITEM.add("F201020");
        C_CHINA_BUS_ITEM.add("F201030");
        C_CHINA_BUS_ITEM.add("F201070");
        C_CHINA_BUS_ITEM.add("F203010");
        C_CHINA_BUS_ITEM.add("F203010");
        C_CHINA_BUS_ITEM.add("F203020");
        C_CHINA_BUS_ITEM.add("F204110");
        C_CHINA_BUS_ITEM.add("F205040");
        C_CHINA_BUS_ITEM.add("F206020");
        C_CHINA_BUS_ITEM.add("F209060");
        C_CHINA_BUS_ITEM.add("F208031");
        C_CHINA_BUS_ITEM.add("F210010");
        C_CHINA_BUS_ITEM.add("F210020");
        C_CHINA_BUS_ITEM.add("F211010");
        C_CHINA_BUS_ITEM.add("F212011");
        C_CHINA_BUS_ITEM.add("F212030");
        C_CHINA_BUS_ITEM.add("F212040");
        C_CHINA_BUS_ITEM.add("F212050");
        C_CHINA_BUS_ITEM.add("F213010");
        C_CHINA_BUS_ITEM.add("F213030");
        C_CHINA_BUS_ITEM.add("F213060");
        C_CHINA_BUS_ITEM.add("F214010");
        C_CHINA_BUS_ITEM.add("F214020");
        C_CHINA_BUS_ITEM.add("F214030");
        C_CHINA_BUS_ITEM.add("F215010");
        C_CHINA_BUS_ITEM.add("F218010");
        C_CHINA_BUS_ITEM.add("F219010");
        C_CHINA_BUS_ITEM.add("F299990");
        C_CHINA_BUS_ITEM.add("F301010");
        C_CHINA_BUS_ITEM.add("F301020");
        C_CHINA_BUS_ITEM.add("F399010");
        C_CHINA_BUS_ITEM.add("F399040");
        C_CHINA_BUS_ITEM.add("F399990");
        C_CHINA_BUS_ITEM.add("F501060");
        C_CHINA_BUS_ITEM.add("G101041");
        C_CHINA_BUS_ITEM.add("G101061");
        C_CHINA_BUS_ITEM.add("G101071");
        C_CHINA_BUS_ITEM.add("G101081");
        C_CHINA_BUS_ITEM.add("G101091");
        C_CHINA_BUS_ITEM.add("G301011");
        C_CHINA_BUS_ITEM.add("G501011");
        C_CHINA_BUS_ITEM.add("G902011");
        C_CHINA_BUS_ITEM.add("I101070");
        C_CHINA_BUS_ITEM.add("I101080");
        C_CHINA_BUS_ITEM.add("I301010");
        C_CHINA_BUS_ITEM.add("I301020");
        C_CHINA_BUS_ITEM.add("I301030");
        C_CHINA_BUS_ITEM.add("I501010");
        C_CHINA_BUS_ITEM.add("I502010");
        C_CHINA_BUS_ITEM.add("I504010");
        C_CHINA_BUS_ITEM.add("I599990");
        C_CHINA_BUS_ITEM.add("IF04010");
        C_CHINA_BUS_ITEM.add("IG02010");
        C_CHINA_BUS_ITEM.add("J101030");
        C_CHINA_BUS_ITEM.add("J101040");
        C_CHINA_BUS_ITEM.add("J101050");
        C_CHINA_BUS_ITEM.add("J101060");
        C_CHINA_BUS_ITEM.add("J101080");
        C_CHINA_BUS_ITEM.add("J901011");
        C_CHINA_BUS_ITEM.add("JA02990");
        C_CHINA_BUS_ITEM.add("JB01010");
        C_CHINA_BUS_ITEM.add("JZ99050");
    }
    
    // Linux機器名稱
 	public static String checkLinuMachineName() {
 		String result = StringUtils.EMPTY;
 		try {
 			result = InetAddress.getLocalHost().getHostName();
 			System.out.println("checkLinuMachineName：" + result);
 		} catch(Exception e) {
 			e.printStackTrace();
 		}
 		return result;
 	}
 	
 // 是否部屬之機器作業系統為windows
 	public static boolean checkIfOsWindows() {
 		boolean result = false;
 		try {
 			String osName = System.getProperty("os.name");
 			System.out.println("OS_NAME：" + osName);
 			if (StringUtils.isNotBlank(osName)) {
 				result = osName.toLowerCase().contains("win");
 			}
 		} catch(Exception e) {
 			System.out.println(e.getMessage());
 		}
 		return result; 
 	}
 	
}
