<!DOCTYPE html>
<!--
程式目的：收據編號維護
程式代號：pre8018
撰寫日期：105.12.6
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8018">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>    
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8018" />
</jsp:include>

<%
if ("update".equals(obj.getState())) {
	obj.update();
}
obj = (com.kangdainfo.tcfi.view.pre.PRE8018)obj.queryOne();	
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){

	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.startReceiptNo0, "一站式收據編號 起號");  
	   alertStr += checkEmpty(form1.endReceiptNo0, "一站式收據編號 訖號");  
	   alertStr += checkEmpty(form1.startReceiptNo1, "郵寄/現場收據 起號");
	   alertStr += checkEmpty(form1.endReceiptNo1, "郵寄/現場收據 訖號");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;	
}

function init(){
}
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8018'/>
</c:import>

<!--Toolbar區============================================================-->
<table>
	<tr><td style="text-align:left">
	   	<input type="hidden" name="state" value="<%=obj.getState()%>">
		<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
		<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	</td></tr>
</table>

<!--Form區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg" >
	<div id="formContainer" style="height:110">
	<table class="table_form" width="100%" height="100%">
        <tr>
            <td nowrap class="td_form" >退還書編號&nbsp;&nbsp;起號：</td>
            <td nowrap class="td_form_white">
            	<input type="hidden" name="receiptType0" value="0">
				<input class="field" type="text" name="startReceiptNo0" size="12" maxlength="10" value="<%=obj.getStartReceiptNo0()==null?"":obj.getStartReceiptNo0()%>">
            </td>
            <td nowrap class="td_form">迄號：</td>
            <td nowrap  class="td_form_white">
				<input class="field" type="text" name="endReceiptNo0" size="12" maxlength="10" value="<%=obj.getEndReceiptNo0()==null?"":obj.getEndReceiptNo0()%>">
            </td>  
            <td nowrap class="td_form">已用過的編號：</td>
            <td nowrap class="td_form_white">
				<input class="field_RO" type="text" name="usedReceiptNo0" size="12" maxlength="10" value="<%=obj.getUsedReceiptNo0()==null?"":obj.getUsedReceiptNo0()%>">
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="15%">郵寄及現場收據編號&nbsp;&nbsp;起號：</td>
            <td nowrap class="td_form_white" width="15%">
            	<input type="hidden" name="receiptType1" value="1">
				<input class="field" type="text" name="startReceiptNo1" size="12" maxlength="10" value="<%=obj.getStartReceiptNo1()==null?"":obj.getStartReceiptNo1()%>">
            </td>  
            <td nowrap class="td_form" width="15%">迄號：</td>
            <td nowrap class="td_form_white" width="15%">
				<input class="field" type="text" name="endReceiptNo1" size="12" maxlength="10" value="<%=obj.getEndReceiptNo1()==null?"":obj.getEndReceiptNo1()%>">
            </td>  
            <td nowrap class="td_form" width="20%">已用過的編號：</td>
            <td nowrap class="td_form_white" width="20%">
				<input class="field_RO" type="text" name="usedReceiptNo1" size="12" maxlength="10" value="<%=obj.getUsedReceiptNo1()==null?"":obj.getUsedReceiptNo1()%>">
            </td>  
        </tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

</table>
</form>
</body>
</html>



