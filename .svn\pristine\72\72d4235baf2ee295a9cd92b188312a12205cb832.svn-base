package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.TreeMap;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.ekera.presearch.Examine;
import com.google.gson.Gson;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc055;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.service.ApproveService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.lang.CommonArrayUtils;

public class PRE3001 extends SuperBean implements IApprove {
	private Log saveToDBLog = LogFactory.getLog("saveToDBLog");
	private String requestTime1;
	private String responseTime1;
	private String requestTime2;
	private String responseTime2;
	private String id;
	private String errmsg;
	private int onLineCnt;
	private String prefixNo;
	private String[] cbPrefixType; // setup, changeName, changeItem (checkbox)
	private String prefixType;
	private String elixNo;
	private String oldCompanyName;
	private String banNo;
	private String contactGetKind; // radio
	private String getKindRemark;
	private String applyType;
	private String receiveDate;
	private String checkPrefixForm;
	private String docType; // select
	private String prefixFormNo;
	private String[] attachment; // checkOtherForm, checkSpec, checkOtherSpec
									// (checkbox)
	private String otherSpecRemark;
	private String applyName;
	private String applyId;
	private String applyAddr;
	private String applyTel;
	private String attorName;
	private String attorId;
	private String attorNo;
	private String attorAddr;
	private String receiveName;
	private String receiveId;
	private String receiveAddr;
	private String receiveTel;
	private String caseCode;
	private String changeType;
	private String telixNo;
	private String contactName;
	private String contactAddr;
	private String companyAddr;
	private String companyName; // PRE3001_00.jsp
	private String assignDate;
	private String[] cedb1002Chk; // checkbox

	private String[] busiItem;
	private String[] busiItemNo;
	private String[] seqNo; // 公司名稱用
	private String[] itemSeqNo; // 營業項目用
	private String approveResultAllNo; // checkbox, only one
	private String cases;

	private String setup;
	private String changeName;
	private String changeItem;

	/* 營業項目查詢 */
	private String[] businessItem;
	private String[] itemCode;
	private List<Cedbc055> items;
	private String keyword;

	// private List<Cedbc004> banWords; //禁用名詞
	// private List<Cedbc053> countrys; //國家

	private String verify;
	private String functionName;

	/* 營業項目匯入 */
	private List<Cedb1002> importItems;

	private String json;
	private boolean autoApprove;

	private String showSame01;
	private String showSame02;
	private String showSame03;
	private String showSame04;
	private String showSame05;

	private String assignPrefixNo; // for 分文用
	private String chinaBusitemMark; // 檢查陸商營業項目用
	
	//外商\大陸商或合併(分割)消滅註記 (審核-額外註記)
	private String foreignMark;
	private String chinaMark;
	
	private String distributedCountByDay; //本日分文數
	private String resetCloseDateFlag;
	
	private String hRemark;
	
	private String isPrefixForm;
	private String isOtherForm;
	private String isSpec;
	private String isOtherSpec;
	
	private String applyWay;
	private String otherReason;
	
	private boolean isNeedWithdraw; //判斷是否需要跳出檢還
	private String withdrawCompanyName; // 存檔時承辦勾選的那一筆companyName
	private String[] prefixNos;
	private String withdrawType;
	
	private String hiddenPrefixNos;
	private String currentPrefixNo;
	
	private String extendMark;
	private String extendReason;
	private String extendOther;
	
	private String chooseSyncOss;	//QA10906100033 如果已經發文結案, 則跳出視窗由承辦人決定是否同步案件狀態至一站式系統
	
	public void initPrefixNos() {
		PRE3001 obj = this;
		if (this.hiddenPrefixNos != null ) {
			String[] prefixNos = this.hiddenPrefixNos.split("-");
			if(prefixNos != null && prefixNos.length > 0){
				String strPrefixNo = "";
				for(int i=0; i< prefixNos.length; i++){
					if(!"".equals(Common.get(prefixNos[i]))){
						if(i == 0){
							// obj.setBanNo(prefixNos[i]);
							obj.setCurrentPrefixNo(String.valueOf(i));
						}else{
							strPrefixNo += ",";
						}
						strPrefixNo += prefixNos[i];
					}
				}
				obj.setHiddenPrefixNos(strPrefixNo);
				
			}
		} // if
	}
	
	public void init() {
		initPrefixNos();
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		this.setDistributedCountByDay(ServiceGetter.getInstance().getApproveService().getDistributedCountByDayForUser(user.getUserId()).toString());
		
		ApproveService approveService = ServiceGetter.getInstance().getApproveService();

		String prefixNo = (this.prefixNo != null && this.prefixNo.indexOf("-") != -1)  ? this.prefixNo.split("-")[0] : this.prefixNo;
		Cedb1000 c1000 = approveService.getCedb1000Dao().findByPrefixNo(prefixNo, null);
		
		if(c1000==null)
			return;
		this.setApplyWay(TcfiView.getApplyWayByTelixNo(c1000.getTelixNo()));
	}
	
	
	public TreeMap<String, PrefixVo> doSearch(PRE3001 pre3001) {
		TreeMap<String, PrefixVo> treeMap = ServiceGetter.getInstance().getApproveService().getCaseSelection(pre3001, functionName, false, null);
		this.setState("doSearch");
		this.setErrorMsg("查詢成功");
		return treeMap;
	}

	public String tempSave() throws Exception {
		// int index = theForm.getIndex();
		ApproveService approveService = ServiceGetter.getInstance().getApproveService();
		Gson gson = new Gson();

		PrefixVo prefixVo = gson.fromJson(json, PrefixVo.class);
		
		Long timeStart = System.currentTimeMillis();
		saveToDBLog.info(prefixVo.getPrefixNo()+",0.審查作業存檔開始---"+timeStart);
		
		prefixVo.setPrefixStatus("5");
		approveService.convertJsonToObject(prefixVo);

		// kylin 檢查公司名稱格式, 2005.04.26新增
		if (this.functionName.startsWith("approve") || this.functionName.startsWith("keyin")) {
			if (!approveService.doVerifyCmpyNames(prefixVo)) {
				this.setState("error.COMPANY_NAME.format");
				this.setErrmsg("公司名稱格式不對，不可有空白，或重複公司型態");
			}
		}

		// zion 檢查營業項目, 2009.06.11新增
		if (!approveService.doVerifyBusiItem(prefixVo, this.getFunctionName())) {
			this.setState("error.busiitem.onlyGenerality");
			this.setErrmsg("營業項目不可空白或僅登記概括條款");
		}

		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		String errors = saveToDb(prefixVo, this, approveService, user.getUserId());
		if (errors.isEmpty() && this.getState().equals("save")) {
			System.out.println("*** MultiFormIteratorAction 存檔成功 ***");
			// 存檔成功訊息
			this.setState("msg.doSave.success");
		} else {
			// 存檔失敗
			System.out.println("*** MultiFormIteratorAction 存檔失敗或存檔成功但仍為審查中 ***");
		}
		init();
		saveToDBLog.info(prefixVo.getPrefixNo()+",22.審核作業存檔結束---"+System.currentTimeMillis());
		return "";
	}

	private String saveToDb(PrefixVo prefixVo, PRE3001 pre3001, ApproveService approveService, String userId) {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		String error = "";
		if (pre3001.isAutoApprove()) {
			if (approveService.saveToPrefixDataBase(prefixVo, pre3001, user.getUserId(), PrefixConstants.FUN_CODE_3001) == null) {
				System.out.println("error.save.Exception");
				error = "存檔失敗";
			}
		} else {
			if (approveService.saveToPrefixDataBaseNoApprove(prefixVo, pre3001, user.getUserId(), PrefixConstants.FUN_CODE_3001) == null) {
				System.out.println("error.save.Exception");
				error = "存檔失敗";
			}
		}
		//同步一站式案件狀態(不是'N'的都要同步)
		if(!"N".equals(pre3001.getChooseSyncOss())) {
			ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(prefixVo.getPrefixNo(), getLoginUserId());
		}
		return error;
	}

	public void assignNewOne() throws Exception {
		try{
			CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
			if (user == null) {
				if (logger.isInfoEnabled())	logger.info("[無法取得登入資料錯誤.............]");
				throw new Exception("無法取得使用者資料");
			}
			prefixNo = assign(user.getUserId(), user.getUserName());
			if("".equals(Common.get(prefixNo)))	
				throw new Exception("已經沒有可供分文的案件");
			setAssignPrefixNo(prefixNo);
			setState("assignSuccess");
		}catch(Exception e){
			e.printStackTrace();
	        setAssignPrefixNo("");
	        setState("assignError");
			setErrorMsg("分文失敗!!" + e.getMessage());
			setState("assignSuccess");
		}
		init();
	}
	
	public HashMap<String, String> assignNewOneAjax() throws Exception {
		HashMap<String, String> map = new HashMap<String, String>();
		try{
			CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
			if (user == null) {
				if (logger.isInfoEnabled())	logger.info("[無法取得登入資料錯誤.............]");
				map.put("result", "無法取得使用者資料" );
				return map;
			}
			prefixNo = assign(user.getUserId(), user.getUserName());
			if("".equals(Common.get(prefixNo)))	{
				map.put("result", "已經沒有可供分文的案件");
				return map;
			}
			
			map.put("result", "assignSuccess");
			map.put("assignPrefixNo", prefixNo);
			return map;
		}catch(Exception e){
			e.printStackTrace();
	        setAssignPrefixNo("");
	        setState("assignError");
	        map.put("result", "分文失敗"+e.getMessage());
	        return map;
		}
	}

	public void delBuItem() throws Exception {
		if (cedb1002Chk != null) {
			List<String> cedb1002s = CommonArrayUtils.arrayToList(cedb1002Chk);
			for (String busiItemNo : cedb1002s) {
				if (!prefixNo.isEmpty() && !busiItemNo.isEmpty())
					ServiceGetter.getInstance().getPrefixService().deleteCedb1002ByPrefixNoAndBusiItemNo(prefixNo, busiItemNo);
			}
		}
	}

	public void saveBusiItem() throws Exception {
		List<Cedb1002> cedb1002s = new ArrayList<Cedb1002>();
		if (busiItem != null && busiItemNo != null && itemSeqNo != null) {
			if (busiItem.length == busiItemNo.length && busiItem.length == itemSeqNo.length) {
				for (int i = 0; i < busiItem.length; i++) {
					if (busiItemNo[i].isEmpty() || busiItem[i].isEmpty())
						continue;
					Cedb1002 cedb1002 = new Cedb1002();
					cedb1002.setPrefixNo(prefixNo.replaceAll("-", ""));
					cedb1002.setSeqNo(itemSeqNo[i]);
					cedb1002.setBusiItemNo(busiItemNo[i]);
					cedb1002.setBusiItem(busiItem[i]);
					cedb1002s.add(cedb1002);
				}
			}
		}
		ServiceGetter.getInstance().getApproveService().insertCedb1002s(cedb1002s);
	}

	// 103/10/30 重新定義重設發文功能
	public void resetCloseDate() {
		if(logger.isInfoEnabled()) logger.info("[resetCloseDate]"+getPrefixNo());
		String currentNo = getPrefixNo();
		//備份
		ServiceGetter.getInstance().getBackupService().doBackup(currentNo, getLoginUserId());
		//重設發文
		ServiceGetter.getInstance().getApproveService().resetCloseDate(getPrefixNo());
	}
	
	/** 分文*/
	public synchronized static String assign(String userId, String userName) throws Exception {
		return ServiceGetter.getInstance().getApproveService().doAssign(userId, userName);
	}
	
	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getCaseCode() {return caseCode;}
	public void setCaseCode(String s) {this.caseCode = s;}

	public String getChangeType() {return changeType;}
	public void setChangeType(String s) {this.changeType = s;}

	public String[] getCedb1002Chk() {return cedb1002Chk;}
	public void setCedb1002Chk(String[] a) {this.cedb1002Chk = a;}

	public String getId() {return id;}
	public void setId(String s) {this.id = s;}

	public String getErrmsg() {return errmsg;}
	public void setErrmsg(String s) {this.errmsg = s;}

	public int getOnLineCnt() {return onLineCnt;}
	public void setOnLineCnt(int i) {this.onLineCnt = i;}

	public String getPrefixNo() {
		return prefixNo == null ? checkGet(prefixNo) : prefixNo.replaceAll("\\D+", "");
	}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public String[] getCbPrefixType() {return cbPrefixType;}
	public void setCbPrefixType(String[] a) {this.cbPrefixType = a;}

	public String getPrefixType() {return prefixType;}
	public void setPrefixType(String s) {this.prefixType = s;}

	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}

	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}

	public String getContactGetKind() {return contactGetKind;}
	public void setContactGetKind(String s) {this.contactGetKind = s;}

	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String s) {this.getKindRemark = s;}

	public String getApplyType() {return applyType;}
	public void setApplyType(String s) {this.applyType = s;}

	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}

	public String getCheckPrefixForm() {return checkPrefixForm;}
	public void setCheckPrefixForm(String s) {this.checkPrefixForm = s;}

	public String getDocType() {return docType;}
	public void setDocType(String s) {this.docType = s;}

	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String s) {this.prefixFormNo = s;}

	public String[] getAttachment() {return attachment;}
	public void setAttachment(String[] a) {this.attachment = a;}

	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = s;}

	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {this.applyName = checkSet(s);}

	public String getApplyId() {return checkGet(applyId);}
	public void setApplyId(String s) {this.applyId = checkSet(s);}

	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String s) {this.applyAddr = s;}

	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}

	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}

	public String getAttorId() {return attorId;}
	public void setAttorId(String s) {this.attorId = s;}

	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}

	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}

	public String getReceiveName() {return receiveName;}
	public void setReceiveName(String s) {this.receiveName = s;}

	public String getReceiveId() {return receiveId;}
	public void setReceiveId(String s) {this.receiveId = s;}

	public String getReceiveAddr() {return receiveAddr;}
	public void setReceiveAddr(String s) {this.receiveAddr = s;}

	public String getReceiveTel() {return receiveTel;}
	public void setReceiveTel(String s) {this.receiveTel = s;}

	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}

	public String getContactName() {return contactName;}
	public void setContactName(String s) {this.contactName = s;}

	public String getContactAddr() {return contactAddr;}
	public void setContactAddr(String s) {this.contactAddr = s;}

	public String getCompanyAddr() {return companyAddr;}
	public void setCompanyAddr(String s) {this.companyAddr = s;}

	public String[] getBusiItem() {return busiItem;}
	public void setBusiItem(String[] a) {this.busiItem = a;}

	public String[] getBusiItemNo() {return busiItemNo;}
	public void setBusiItemNo(String[] a) {this.busiItemNo = a;}

	public String[] getSeqNo() {return seqNo;}
	public void setSeqNo(String[] a) {this.seqNo = a;}

	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}

	public String getAssignDate() {return checkGet(assignDate);}
	public void setAssignDate(String s) {this.assignDate = checkSet(s);}

	public String getApproveResultAllNo() {return approveResultAllNo;}
	public void setApproveResultAllNo(String s) {this.approveResultAllNo = s;}

	public String getSetup() {return setup;}
	public void setSetup(String s) {this.setup = s;}

	public String getChangeName() {return changeName;}
	public void setChangeName(String s) {this.changeName = s;}

	public String getChangeItem() {return changeItem;}
	public void setChangeItem(String s) {this.changeItem = s;}

	public String[] getBusinessItem() {return businessItem;}
	public void setBusinessItem(String[] a) {this.businessItem = a;}

	public String[] getItemCode() {return itemCode;}
	public void setItemCode(String[] a) {this.itemCode = a;}

	public List<Cedbc055> getItems() {return items;}
	public void setItems(List<Cedbc055> l) {this.items = l;}

	public String getKeyWord() {return checkGet(keyword);}
	public void setKeyWord(String s) {this.keyword = checkSet(s);}

	public String getKeyword() {return keyword;}
	public void setKeyword(String s) {this.keyword = s;}

	public String getCases() {return cases;}
	public void setCases(String s) {this.cases = s;}

	public List<Cedb1002> getImportItems() {return importItems;}
	public void setImportItems(List<Cedb1002> l) {this.importItems = l;}

	public String[] getItemSeqNo() {return itemSeqNo;}
	public void setItemSeqNo(String[] a) {this.itemSeqNo = a;}

	public String getVerify() {return verify;}
	public void setVerify(String s) {this.verify = s;}

	public String getFunctionName() {return functionName;}
	public void setFunctionName(String s) {this.functionName = s;}

	public String getJson() {return json;}
	public void setJson(String s) {this.json = s;}

	public boolean isAutoApprove() {return autoApprove;}
	public void setAutoApprove(boolean b) {this.autoApprove = b;}

	public String getShowSame01() {return showSame01;}
	public void setShowSame01(String s) {this.showSame01 = s;}

	public String getShowSame02() {return showSame02;}
	public void setShowSame02(String s) {this.showSame02 = s;}

	public String getShowSame03() {return showSame03;}
	public void setShowSame03(String s) {this.showSame03 = s;}

	public String getShowSame04() {return showSame04;}
	public void setShowSame04(String s) {this.showSame04 = s;}

	public String getShowSame05() {return showSame05;}
	public void setShowSame05(String s) {this.showSame05 = s;}

	public String getAssignPrefixNo() {return assignPrefixNo;}
	public void setAssignPrefixNo(String s) {this.assignPrefixNo = s;}

	public String getChinaBusitemMark() {return checkGet(chinaBusitemMark);}
	public void setChinaBusitemMark(String s) {this.chinaBusitemMark = checkSet(s);}

	public String getForeignMark() {return checkGet(foreignMark);}
	public void setForeignMark(String s) {this.foreignMark = checkSet(s);}

	public String getChinaMark() {return checkGet(chinaMark);}
	public void setChinaMark(String s) {this.chinaMark = checkSet(s);}
	
	public String getDistributedCountByDay() {return checkGet(distributedCountByDay);}
	public void setDistributedCountByDay(String s) {this.distributedCountByDay = checkSet(s);}
	
	public String getResetCloseDateFlag() {return resetCloseDateFlag;}
	public void setResetCloseDateFlag(String s) {this.resetCloseDateFlag = s;}

	public String gethRemark() {return hRemark;}
	public void sethRemark(String s) {this.hRemark = s;}

	public String getElixNo() {return elixNo;}
	public void setElixNo(String s) {this.elixNo = s;}

	public String getIsPrefixForm() {return isPrefixForm;}
	public void setIsPrefixForm(String s) {this.isPrefixForm = s;}

	public String getIsOtherForm() {return isOtherForm;}
	public void setIsOtherForm(String s) {this.isOtherForm = s;}

	public String getIsSpec() {return isSpec;}
	public void setIsSpec(String s) {this.isSpec = s;}

	public String getIsOtherSpec() {return isOtherSpec;}
	public void setIsOtherSpec(String s) {this.isOtherSpec = s;}

	public String getApplyWay() {return applyWay;}
	public void setApplyWay(String s) {this.applyWay = s;}

	public String getOtherReason() {return otherReason;}
	public void setOtherReason(String s) {this.otherReason = s;}

	public boolean isNeedWithdraw() {
		ApproveService approveService = ServiceGetter.getInstance().getApproveService();
		Cedb1000 cedb1000 = approveService.getCedb1000ByPrefixNo(this.prefixNo);
		
		if( cedb1000 == null) return false;
		List<Cedb1000> cedb1000s = approveService.findWithdrawWithIdAndCompanyName(cedb1000.getApplyId(), Common.get(getWithdrawCompanyName()), this.prefixNo);
		if(!cedb1000s.isEmpty()) {
			this.setNeedWithdraw(true);
			this.setApplyId(this.applyId);
		}

		return this.isNeedWithdraw;
	}

	public void setNeedWithdraw(boolean isNeedWithdraw) {
		this.isNeedWithdraw = isNeedWithdraw;
	}

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] a) {this.prefixNos = a;}

	public String getWithdrawType() {return withdrawType;}
	public void setWithdrawType(String s) {this.withdrawType = s;}
	
	public String getHiddenPrefixNos() {return hiddenPrefixNos;}
	public void setHiddenPrefixNos(String s) {this.hiddenPrefixNos = s;}
	
	public String getCurrentPrefixNo() {return currentPrefixNo;}
	public void setCurrentPrefixNo(String s) {this.currentPrefixNo = s;}
	
	public String getExtendMark() {return extendMark;}
	public void setExtendMark(String s) {this.extendMark = s;}
	
	public String getExtendReason() {return extendReason;}
	public void setExtendReason(String s) {this.extendReason = s;}
	
	public String getExtendOther() {return extendOther;}
	public void setExtendOther(String s) {this.extendOther = s;}
	
	public String getWithdrawCompanyName() {return withdrawCompanyName;}
	public void setWithdrawCompanyName(String s) {this.withdrawCompanyName = s;}
	
	public String getRequestTime1() {return requestTime1;}
	public void setRequestTime1(String s) {this.requestTime1 = s;}
	
	public String getResponseTime1() {return responseTime1;}
	public void setResponseTime1(String s) {this.responseTime1 = s;}
	
	public String getRequestTime2() {return requestTime2;}
	public void setRequestTime2(String s) {this.requestTime2 = s;}
	
	public String getResponseTime2() {return responseTime2;}
	public void setResponseTime2(String s) {this.responseTime2 = s;}

	public String getChooseSyncOss() {return chooseSyncOss;}

	public void setChooseSyncOss(String chooseSyncOss) {this.chooseSyncOss = chooseSyncOss;}

}