package com.kangdainfo.tcfi.lucene.bo;

import org.apache.lucene.document.Document;
import org.apache.lucene.search.ScoreDoc;

public class Hit {

	private ScoreDoc scoreDoc;
	private Document doc;
	private String html;

	public ScoreDoc getScoreDoc() {return scoreDoc;}
	public void setScoreDoc(ScoreDoc scoreDoc) {this.scoreDoc = scoreDoc;}

	public Document getDoc() {return doc;}
	public void setDoc(Document doc) {this.doc = doc;}

	public String getHtml() {return html;}
	public void setHtml(String html) {this.html = html;}

	@Override
	public String toString() {
		return "Hit [scoreDoc=" + scoreDoc + ", doc=" + doc + "]";
	}
}