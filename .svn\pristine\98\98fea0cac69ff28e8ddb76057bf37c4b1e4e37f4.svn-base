package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;

public class PRE5002 extends SuperBean {
	private String queryType;
	private String queryField;
	
	public String getQueryType() {
		return queryType;
	}
	public void setQueryType(String queryType) {
		this.queryType = queryType;
	}
	public String getQueryField() {
		return queryField;
	}
	public void setQueryField(String queryField) {
		this.queryField = queryField;
	}
	
	public void doSomeCheck() {
		PRE5002 pre5002 = this;
		try {
			String queryType = pre5002.getQueryType();
			String queryField = pre5002.getQueryField();
			PrefixReceiptNo prefixReceiptNo = null;
			if ("0".equals(queryType)) {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByReceiptNo(queryField);
			} else if ("1".equals(queryType)) {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByTelixNo(queryField);
			} else {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByPrefixNo(queryField);
			}
			
			if (prefixReceiptNo == null) {
				pre5002.setState("checkError");
				pre5002.setErrorMsg("查無該收據資料");
			} else {
				pre5002.setState("checkSuccess");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	public Object doQueryOne() throws Exception{ 
		return null;
	} // end doQueryOne()
	
	
	public File doPrintPdf() throws Exception {
		File report = null;
		try {
			PrefixReceiptNo prefixReceiptNo = null;
			if ("0".equals(queryType)) {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByReceiptNo(getQueryField());
			} else if ("1".equals(queryType)) {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByTelixNo(getQueryField());
			} else {
				prefixReceiptNo = ServiceGetter.getInstance().getPre5002Service().selectPrefixReceiptNoByPrefixNo(getQueryField());
			}
			report = ServiceGetter.getInstance().getPre5001Service().createPdfFile(prefixReceiptNo);
		} catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<300) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return report;
	}
	
	public ArrayList<?> doQueryAll() throws Exception{
		return null;
	} // doQueryAll()
		
	public void doUpdate() throws Exception {   
		
	} // end doUpdate()
	
	public void doCreate() throws Exception{
		
	} // end doCreate() 
	  
    public void doDelete() throws Exception{			
		   
    } // end doDelete()	
}