--DROP TABLE EICM.CEDB1123;
-- Create table
CREATE TABLE EICM.CEDB1123 (
	PREFIX_NO NVARCHAR2(18) not null,
	GET_ADDR NVARCHAR2(300),
	GET_NAME NVARCHAR2(120),
	SMS NVARCHAR2(2),
	CONTACT_CEL NVARCHAR2(34),
	CHANGE_TYPE VARCHAR2(1)
);
-- Add comments to the table 
comment on table EICM.CEDB1123 is '公司預查封存申請案收件人資料檔';
-- Add comments to the columns 
comment on column EICM.CEDB1123.PREFIX_NO is '預查編號';
comment on column EICM.CEDB1123.GET_ADDR is '收件人地址';
comment on column EICM.CEDB1123.GET_NAME is '收件人姓名';
comment on column EICM.CEDB1123.SMS is '是否接受簡訊服務';
comment on column EICM.CEDB1123.CONTACT_CEL is '接收簡訊手機號碼';
comment on column EICM.CEDB1123.CHANGE_TYPE is '變更類別';

--Synonymn(use dba to run)
CREATE OR REPLACE SYNONYM "EICM4AP"."CEDB1123" FOR "EICM"."CEDB1123";
--GRANT(use dba to run)
grant all on EICM.CEDB1123 to EICM4AP;
