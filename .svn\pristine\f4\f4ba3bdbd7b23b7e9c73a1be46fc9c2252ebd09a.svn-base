package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmBussItem;

public class OssmBussItemDao extends BaseDaoJdbc implements RowMapper<OssmBussItem> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSM_BUSS_ITEM WHERE TELIX_NO = ? ORDER BY TELIX_NO, SEQ_NO";
	public List<OssmBussItem> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<OssmBussItem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public void insert(String telixNo, String seqNo, String busiItemNo, String busiItem) {
		if("".equals(Common.get(telixNo))) return;
		if("".equals(Common.get(seqNo))) return;
		if("".equals(Common.get(busiItem))) return;

		SQLJob sqljob = new SQLJob("INSERT INTO OSSM_BUSS_ITEM (TELIX_NO,SEQ_NO,BUSI_ITEM_NO,BUSI_ITEM) VALUES (?,?,?,?)");
		sqljob.addParameter(telixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(seqNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(busiItemNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(busiItem);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public OssmBussItem mapRow(ResultSet rs, int idx) throws SQLException {
		OssmBussItem obj = null;
		if(null!=rs) {
			obj = new OssmBussItem();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBusiItemNo(rs.getString("BUSI_ITEM_NO"));
			obj.setBusiItem(rs.getString("BUSI_ITEM"));
		}
		return obj;
	}

}
