package com.kangdainfo.tcfi.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SessionDataBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1100;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixQueryVo;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.model.eicm.bo.TrackLog;
import com.kangdainfo.tcfi.model.eicm.dao.TrackLogDao;
import com.kangdainfo.tcfi.service.TrackLogService;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class TrackLogServiceImpl extends SessionDataBean implements TrackLogService{

	private TrackLogDao trackLogDao;
	public TrackLogDao getTrackLogDao() {return trackLogDao;}
	public void setTrackLogDao(TrackLogDao trackLogDao) {this.trackLogDao = trackLogDao;}
	
	@Override
	public void doUpateTrack(String funcCode, Object obj){
		if(obj == null)	return;
		String remark = getRemark(obj);
		insertTrackLog(funcCode, PrefixConstants.TRACK_LOG_UPDATE, remark);
	}
	
	@Override
	public void doSearchTrack(String funcCode, Object obj){
		if(obj == null)	return;
		String remark = "";
		remark = getRemark(obj);
		insertTrackLog(funcCode, PrefixConstants.TRACK_LOG_SEARCH, remark);
		if(obj instanceof PrefixVo){
			PrefixVo vo = (PrefixVo) obj;
			if(vo.getCedb1023() != null){
				remark = getRemark(vo.getCedb1023());
				insertTrackLog(funcCode, PrefixConstants.TRACK_LOG_SEARCH, remark);
			}
		} else if(obj instanceof PrefixQueryVo){
			PrefixQueryVo vo = (PrefixQueryVo) obj;
			remark = getRemark("CEDB1023", getTitleName("CEDB1023"), vo.getPrefixNo(), 
					vo.getReceiveName(), vo.getReceiveAddr(), vo.getContactCel());
			insertTrackLog(funcCode, PrefixConstants.TRACK_LOG_SEARCH, remark);
		}
	}
	
	/** 查詢 個資軌跡紀錄檔 */
	public List<TrackLog> getTrackLog(String funcCode, String type, String idNo, String dateS, String dateE){
		return trackLogDao.query(funcCode, type, idNo, dateS, dateE);
	}
	
	/** 新增 個資軌跡紀錄檔 */
	public void insertTrackLog(String funcCode, String type, String remark){
		TrackLog bo = new TrackLog();
		bo.setFuncCode(funcCode);
		bo.setFuncName(ServiceGetter.getInstance().getFunctionMenuLoader().getFunctionMenuNameByCode(funcCode));
		bo.setType(type);
		bo.setIp("");	//IP ADDRESS
		bo.setStatus(null);
		bo.setIdNo(getLoginUserId());
		bo.setName(getLoginUserName());
		bo.setDate(Datetime.getYYYMMDD());
		bo.setTime(Datetime.getHHMMSS());
		bo.setRemark(remark);
		trackLogDao.insert(bo);
	}
	
	public String getRemark(Object obj){
		String remark = "", tableName = "";
		if(obj instanceof Cedb1000){
			Cedb1000 bo = (Cedb1000)obj;
			tableName = "CEDB1000";
			remark = getRemark(tableName, getTitleName(tableName), bo.getPrefixNo(), 
					bo.getApplyId(), bo.getApplyName(), bo.getApplyAddr(), bo.getApplyTel(), 
					bo.getAttorName(), bo.getAttorAddr(), bo.getAttorTel(), bo.getAttorNo());
		}else if(obj instanceof PrefixQueryVo){
			PrefixQueryVo bo = (PrefixQueryVo)obj;
			tableName = "CEDB1000";
			remark = getRemark(tableName, getTitleName(tableName), bo.getPrefixNo(), 
					bo.getApplyId(), bo.getApplyName(), bo.getApplyAddr(), bo.getApplyTel(), 
					bo.getAttorName(), bo.getAttorAddr(), bo.getAttorTel(), bo.getAttorNo());
		}else if(obj instanceof Cedb1006){
			Cedb1006 bo = (Cedb1006)obj;
			tableName = "CEDB1006";
			remark = getRemark(tableName, getTitleName(tableName), bo.getPrefixNo(), 
					bo.getApplyId(), bo.getApplyName(), bo.getApplyAddr(), bo.getApplyTel(), 
					bo.getAttorName(), bo.getAttorAddr(), bo.getAttorTel(), bo.getAttorNo());
		}else if(obj instanceof Cedb1100){
			Cedb1100 bo = (Cedb1100)obj;
			tableName = "CEDB1100";
			remark = getRemark(tableName, getTitleName(tableName), bo.getPrefixNo(), 
					bo.getApplyId(), bo.getApplyName(), bo.getApplyAddr(), bo.getApplyTel(), 
					bo.getAttorName(), bo.getAttorAddr(), bo.getAttorTel(), bo.getAttorNo());
		}else if(obj instanceof Cedb1023){
			Cedb1023 bo = (Cedb1023)obj;
			tableName = "CEDB1023";
			remark = getRemark(tableName, getTitleName(tableName), bo.getPrefixNo(), 
					bo.getGetName(), bo.getGetAddr(), bo.getContactCel());
		}
		return remark;
	}
	
	/** 組合 個資軌跡 查詢條件/個資欄位 
	 *  @param tableName [來源資料表]
	 *  @param datas[] 異動的欄位
	 *  CEDB1000, CEDB1006, CEDB1100 = PREFIX_NO, APPLY_ID, APPLY_NAME, APPLY_ADDR, APPLY_TEL, ATTOR_NAME, ATTOR_ADDR, ATTOR_TEL, ATTOR_NO
	 *  CEDB1023 = PREFIX_NO, GET_NAME, GET_ADDR, CONTACT_CEL
	 * */
	public String getRemark(String tableName, Map<Integer, String> title, String... datas){
		if(datas == null || datas.length < 1)	return "";
		StringBuffer sb = new StringBuffer("");
		if("CEDB1000".equals(tableName))	sb.append("預查案件基本資料記錄檔：");
		else if("CEDB1006".equals(tableName))	sb.append("預查案件基本資料異動記錄檔：");
		else if("CEDB1100".equals(tableName))	sb.append("預查封存申請案基本資料記錄檔：");
		else if("CEDB1023".equals(tableName))	sb.append("預查申請案收件人資料檔：");
		
		for(int i=0; i< datas.length; i++){
			//空白不寫入
			if("".equals(Common.get(datas[i])))	continue;
			sb.append(title.get(i)).append("=").append(datas[i]);
			sb.append(",");
		}
		return sb.toString();
	}
	
	public Map<Integer, String> getTitleName(String tableName){
		Map<Integer, String> map = new HashMap<Integer, String>();
		int i=0;
		map.put(i++, "預查編號");
		if("CEDB1000".equals(tableName) || "CEDB1006".equals(tableName) || "CEDB1100".equals(tableName)){
			map.put(i++, "申請人身分證統一編號");
			map.put(i++, "申請人姓名");
			map.put(i++, "申請人地址");
			map.put(i++, "申請人連絡電話");
			map.put(i++, "代理人姓名");
			map.put(i++, "代理人地址");
			map.put(i++, "代理人連絡電話");
			map.put(i++, "代理人證書號碼");
		}else if("CEDB1023".equals(tableName)){
			map.put(i++, "收件人姓名");
			map.put(i++, "收件人地址");
			map.put(i++, "接收簡訊手機號碼");
		}
		return map;
	}
	
	@Override
	public void insertApplyPerson(String funcCode, String opType, String prefixNo, String applyId, String applyName, String applyTel, String applyAddr){
		if("".equals(Common.get(prefixNo)))	return;
		Cedb1000 cedb1000 = new Cedb1000();
		cedb1000.setPrefixNo(prefixNo);
		cedb1000.setApplyId(applyId);
		cedb1000.setApplyName(applyName);
		cedb1000.setApplyTel(applyTel);
		cedb1000.setApplyAddr(applyAddr);
		doSearchTrack(funcCode, cedb1000);
	}
	
	@Override
	public void insertGetPerson(String funcCode, String opType, String prefixNo, String getName, String getTel, String getAddr){
		if("".equals(Common.get(prefixNo)))	return;
		Cedb1023 cedb1023 = new Cedb1023();
		cedb1023.setPrefixNo(prefixNo);
		cedb1023.setGetName(getName);
		cedb1023.setContactCel(getTel);
		cedb1023.setGetAddr(getAddr);
		doSearchTrack(funcCode, cedb1023);
	}
}
