package com.kangdainfo.common.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;

public interface AuthenticationService {

	public boolean authenticate(HttpServletRequest request);
    public void passAuthentication(String userName, HttpServletRequest request) throws Exception;
    public void passAuthentication(String userName,HttpSession session)  throws Exception;
    public boolean isAlreadyAuthenticated(HttpSession session) throws Exception;
    public boolean isAlreadyAuthenticated(HttpServletRequest request) throws Exception;

    public CommonUser getCurrentUser();
    public CommonUser getCurrentUser(HttpSession session);
    public CommonUser getCurrentUser(HttpServletRequest request);

	public int checkAuthentication(HttpServletRequest request, HttpServletResponse response);
	public void invalidateSession(HttpServletRequest request);
	
	public CommonUser getCommonUserByUserId(String userid);
	public CommonUser getCommonUser(String userid, String pwd);

	/** 登出 */
	public void logout(javax.servlet.ServletRequest req);

	/** 產生選單Script */
	public String buildAuthorizeMenu(String treeID, String treeName, String groupId);
	
	public FunctionMenu getCurrentDtree();

	public void setCurrentDtree(String progCode);

	/** 判斷是否使用Single Sign On */
	public boolean isUseSSO();
	
	/** 紀錄使用者登入Log */
	public void setLoginLog(HttpServletRequest request);

}