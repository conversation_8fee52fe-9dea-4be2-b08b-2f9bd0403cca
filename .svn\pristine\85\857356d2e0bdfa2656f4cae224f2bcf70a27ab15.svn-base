<%@ page import="java.util.*" %>
<%@ page import="com.kangdainfo.*" %>
<%@ page import="com.kangdainfo.web.util.*" %>
<%@ page import="com.kangdainfo.common.util.*" %>
<%@ page import="com.kangdainfo.common.util.report.*" %>
<%@ page import="com.kangdainfo.common.model.bo.*" %>
<%@ page import="com.kangdainfo.persistence.*" %>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*" %>
<%@ page import="org.owasp.esapi.*" %>
<jsp:useBean id="User" scope="session" class="com.kangdainfo.common.model.bo.CommonUser"/>
<%
if (User==null){
	out.println("<script type=\"text/javascript\">");
	out.println("var prop='';");
	out.println("prop=prop+'width=300px,height=120,scrollbars=no'");
	out.println("window.open('" + Common.getCurrentContextPath() + "/home/<USER>','sessionTimeout',prop);");
	out.println("top.top.location.href='" + Common.getCurrentContextPath() + "/index.jsp';");
	out.println("</script>");
	return;
}
%>