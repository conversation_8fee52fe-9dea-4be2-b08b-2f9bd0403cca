package com.kangdainfo.common.view.home;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;

public class PopBoard extends SuperBean {

	/** 訊息公告 */
	public Object doQueryOne() throws Exception {
		PopBoard obj = new PopBoard();
		SystemNews o = ServiceGetter.getInstance().getPre9005Service().getSystemNewsById(Common.getInt(getId()));
		if (null!=o) {
	        obj.setId(o.getId().toString());
	        obj.setSubject(o.getSubject());
	        obj.setContent(o.getContent());
	        obj.setStartDate(o.getStartDate());
	        obj.setEndDate(o.getEndDate());
	        obj.setIsImportant(("Y".equalsIgnoreCase(o.getIsImportant())?"Y":"N"));
	        obj.setEnable(("Y".equalsIgnoreCase(o.getEnable())?"Y":"N"));
		} else throw new Exception("查無該筆資料！");
		return obj;
	}

	public java.util.ArrayList<String[]> doQueryAll() throws Exception {
		return null;
	}

	public void doCreate() throws Exception {
	}

	public void doUpdate() throws Exception {
	}

	public void doDelete() throws Exception {
	}

	private String id;//主鍵值
	private String subject;//主旨
	private String content;//內容
	private String startDate;//開始日期
	private String endDate;//結束日期
	private String isImportant;//重要公告註記(Y:重要,N:一般)
	private String enable;//是否啟用(Y:啟動,N:停用 )

	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getSubject() {return checkGet(subject);}
	public void setSubject(String subject) {this.subject = checkSet(subject);}
	public String getContent() {return checkGet(content);}
	public void setContent(String content) {this.content = checkSet(content);}
	public String getStartDate() {return checkGet(startDate);}
	public void setStartDate(String startDate) {this.startDate = checkSet(startDate);}
	public String getEndDate() {return checkGet(endDate);}
	public void setEndDate(String endDate) {this.endDate = checkSet(endDate);}
	public String getIsImportant() {return checkGet(isImportant);}
	public void setIsImportant(String isImportant) {this.isImportant = checkSet(isImportant);}
	public String getEnable() {return checkGet(enable);}
	public void setEnable(String enable) {this.enable = checkSet(enable);}

}