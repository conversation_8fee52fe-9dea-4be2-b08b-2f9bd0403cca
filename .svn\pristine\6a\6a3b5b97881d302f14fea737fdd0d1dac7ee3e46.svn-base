package com.kangdainfo.web.listener;

import java.io.File;

import javax.servlet.ServletContextEvent;

import org.apache.log4j.Logger;
import org.springframework.web.context.ContextLoaderListener;
import org.springframework.web.context.support.WebApplicationContextUtils;

import com.kangdainfo.web.util.MySpringWebApplicationContext;

public class MySpringContextLoaderListener extends ContextLoaderListener {

	private Logger logger = Logger.getLogger(this.getClass());
	
	public void contextInitialized(ServletContextEvent event) {
		super.contextInitialized(event);
		MyServletContext.getInstance().setServletContext(event.getServletContext());
		MyServletContext.getInstance().setContextPath(event.getServletContext().getContextPath());		
		
		MySpringWebApplicationContext.defaultWebApplicationContext = 
			WebApplicationContextUtils.getWebApplicationContext(event.getServletContext());

		//clear TEMP Folder
		clearTempFolder();
	}

	private void clearTempFolder() {
		//clear tmpdir
		java.io.File dir = new java.io.File(System.getProperty("java.io.tmpdir"));
		if (dir.isDirectory() && dir.getParentFile()!=null)
		{
			String[] children = dir.list();
			for (int i=0; i<children.length; i++) {
				File f = new File(dir, children[i]);
				f.delete();
			}
			String msg = "Clear " + System.getProperty("java.io.tmpdir") + " : " + children.length + " files deleted.";
			if(logger.isInfoEnabled()) logger.info(msg);
		}
	}
}