package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;

public class Eedb5000Dao extends BaseDaoJdbc implements RowMapper<Eedb5000> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB5000 WHERE TELIX_NO = ?";
	public Eedb5000 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb5000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public Eedb5000 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb5000 obj = null;
		if(null!=rs) {
			obj = new Eedb5000();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setReceiptNo(rs.getString("RECEIPT_NO"));
			obj.setAmount(rs.getString("AMOUNT"));
			obj.setId(rs.getString("ID"));
			obj.setSignCaNo(rs.getString("SIGN_CA_NO"));
			obj.setSignCaName(rs.getString("SIGN_CA_NAME"));
			obj.setReceiptDate(rs.getString("RECEIPT_DATE"));
			obj.setPayDate(rs.getString("PAY_DATE"));
			obj.setPayTime(rs.getString("PAY_TIME"));
			obj.setTransDate(rs.getString("TRANS_DATE"));
			obj.setReturnDate(rs.getString("RETURN_DATE"));
			obj.setReturnAmount(rs.getString("RETURN_AMOUNT"));
			obj.setAccountDate(rs.getString("ACCOUNT_DATE"));
			obj.setReturnFlag(rs.getString("RETURN_FLAG"));
		}
		return obj;
	}

}