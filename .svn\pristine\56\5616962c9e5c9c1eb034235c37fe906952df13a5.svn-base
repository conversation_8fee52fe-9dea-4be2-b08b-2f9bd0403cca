<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE1004"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
String prefixNoEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNoEnd")));

try {
	if ( !"".equals(prefixNo) ) {
		String checkResult = PRE1004.checkForjsp(prefixNo, prefixNoEnd);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>