package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;

public class DeclaratoryStatutesRcverDao extends BaseDaoJdbc implements RowMapper<DeclaratoryStatutesRcver> {
	
	private static final String SQL_find = "SELECT * FROM EICM.DECLARATORY_STATUTES_RCVER";
	
	public java.util.List<DeclaratoryStatutesRcver> find(DeclaratoryStatutes bo) {
		SQLJob sqljob = new SQLJob(SQL_find);
		if(!"".equals(Common.get(bo.getRcvNo()))){
			sqljob.appendSQLCondition(" RCV_NO = ?");
			sqljob.addParameter(bo.getRcvNo());
		}
		sqljob.appendSQL(" ORDER BY RCV_NO, RCVER_TYPE, SEQ_NO ");
		System.out.println(sqljob.getSQL());
		System.out.println(bo.getRcvNo());
		return (java.util.List<DeclaratoryStatutesRcver>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public int insert( DeclaratoryStatutesRcver bo ) {
		//insert
		if (bo==null)
			return 0;
		SQLJob sqljob = new SQLJob("insert into EICM.Declaratory_Statutes_Rcver (rcv_no, rcver_type, seq_no, rcver_org) values(?,?,?,?)");
		sqljob.addParameter(bo.getRcvNo());
		sqljob.addParameter(bo.getRcverType());
		sqljob.addParameter(bo.getSeqNo());
		sqljob.addParameter(bo.getRcverOrg());
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	} // set
	
	public int delete (DeclaratoryStatutes bo) {
		if ( bo == null )
			return 0;
		SQLJob sqljob = new SQLJob("delete from EICM.Declaratory_statutes_rcver where rcv_no = ?");
		sqljob.addParameter(bo.getRcvNo());
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	} // publishUpdate
	
	public DeclaratoryStatutesRcver mapRow(ResultSet rs, int idx) throws SQLException {
		DeclaratoryStatutesRcver obj = null;
		if(null!=rs) {
			obj = new DeclaratoryStatutesRcver();
			obj.setRcvNo(rs.getString("RCV_NO"));
			obj.setRcverType(rs.getString("RCVER_TYPE"));
			obj.setSeqNo(rs.getInt("SEQ_NO"));
			obj.setRcverOrg(rs.getString("RCVER_ORG"));
		}
		return obj;
	}
}
