<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- USE JNDI CONNECTION -->
	<!--
	<bean id="osssDataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiName">
			<value>java:comp/env/jdbc/osss</value>
		</property>
	</bean>
	-->
	<bean id="osssDataSource"
		class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="${osss.dataSource.driverClassName}" />
		<property name="url" value="${osss.dataSource.url}" />
		<property name="username" value="${osss.dataSource.username}" />
		<property name="password" value="${osss.dataSource.password}" />
		<property name="validationQuery" value="${osss.dataSource.validationQuery}" />
		<property name="poolPreparedStatements" value="${osss.dataSource.poolPreparedStatements}" />
		<property name="maxOpenPreparedStatements" value="${osss.dataSource.maxOpenPreparedStatements}" />
		<property name="maxActive" value="${osss.dataSource.maxActive}" />
		<property name="maxIdle" value="${osss.dataSource.maxIdle}" />
	</bean>	

	<bean id="osssGeneralQueryDao" class="com.kangdainfo.tcfi.model.osss.dao.OsssGeneralQueryDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>

	<bean id="ossmApplFlowDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmApplMainDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmBussItemDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmFeeDetailDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmFeeDetailDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmFeeMainDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmOrgBranchDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmOrgBranchDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmOrgChangeDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmOrgNameDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="ossmOrgRegisterDao" class="com.kangdainfo.tcfi.model.osss.dao.OssmOrgRegisterDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="receiveDao" class="com.kangdainfo.tcfi.model.eicm.dao.ReceiveDao">
		<property name="dataSource" ref="osssDataSource" />
	</bean>

</beans>