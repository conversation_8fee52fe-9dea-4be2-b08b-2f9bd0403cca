package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 清算完結資料檔(CEDB1028)
 *
 */
public class Cedb1028 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 統一編號 */
	private String banNo;
	/** 收文號 */
	private String receiveNo;
	/** 核備清算完結日期 */
	private String clearDate;
	/** 清算完結單位 */
	private String clearUnit;
	/** 公司名稱 */
	private String companyName;
	/** 公司裝況 */
	private String statusCode;
	/** 登錄日期 */
	private String regDate;
	/** 清算完結字 */
	private String clearWord;
	/** 清算完結號 */
	private String clearNo;
	/** 備註 */
	private String remark;

	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getReceiveNo() {return receiveNo;}
	public void setReceiveNo(String receiveNo) {this.receiveNo = receiveNo;}
	public String getClearDate() {return clearDate;}
	public void setClearDate(String clearDate) {this.clearDate = clearDate;}
	public String getClearUnit() {return clearUnit;}
	public void setClearUnit(String clearUnit) {this.clearUnit = clearUnit;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getStatusCode() {return statusCode;}
	public void setStatusCode(String statusCode) {this.statusCode = statusCode;}
	public String getRegDate() {return regDate;}
	public void setRegDate(String regDate) {this.regDate = regDate;}
	public String getClearWord() {return clearWord;}
	public void setClearWord(String clearWord) {this.clearWord = clearWord;}
	public String getClearNo() {return clearNo;}
	public void setClearNo(String clearNo) {this.clearNo = clearNo;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}

}