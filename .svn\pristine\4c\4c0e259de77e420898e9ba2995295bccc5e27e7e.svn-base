package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;

public interface Pre0004Service {
	
	/**
	 * 異動同音同義字資料程序
	 * 1. 讀取 INDEX_LOG 執行WS10003
	 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
	 * 3. 查詢檢索檔中公司名稱與特許名稱符合的資料
	 * 4. 新增異動公司/預查檢索資料程序至 INDEX_LOG 
	 * 5. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
	 */
	public IndexLog getIndexData(IndexLog obj);

	/**
	 * 執行
	 * @param obj
	 */
	public void doBuildIndex(IndexLog obj);
}