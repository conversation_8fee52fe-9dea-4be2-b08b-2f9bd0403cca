package com.kangdainfo.tcfi.loader;

/**
 * 馬上辦案由
 *
 */
public class SystemCode09Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_09";
	private static final String CODE_KIND = "09";//09:馬上辦案由

	//singleton
	private static SystemCode09Loader instance;
	public SystemCode09Loader() {
		if (SystemCode09Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode09Loader.instance);
		}
		SystemCode09Loader.instance = this;
	}
	public static SystemCode09Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}