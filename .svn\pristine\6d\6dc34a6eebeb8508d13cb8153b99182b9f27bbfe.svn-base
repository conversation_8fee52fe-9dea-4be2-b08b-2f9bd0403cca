package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2004;

public class Cedb2004Dao extends BaseDaoJdbc implements RowMapper<Cedb2004> {

	private static String sql_findByBanNo = "SELECT * FROM CEDB2004 ";
	public List<Cedb2004> findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.appendSQLCondition(" BAN_NO = ? ");
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedb2004>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	@Override
	public Cedb2004 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb2004 obj = null;
		if(null!=rs) {
			obj = new Cedb2004();
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setApproveDate(Common.get(rs.getString("APPROVE_DATE")));
			obj.setSeqNo(Common.get(rs.getString("SEQ_NO")));
			obj.setIdNo(Common.get(rs.getString("ID_NO")));
			obj.setPositionCode(Common.get(rs.getString("POSITION_CODE")));
			obj.setPositionName(Common.get(rs.getString("POSITION_NAME")));
			obj.setManagerCode(Common.get(rs.getString("MANAGER_CODE")));
			obj.setName(Common.get(rs.getString("NAME"))) ;
			obj.setForName(Common.get(rs.getString("FOR_NAME")));
			obj.setNativeCode(Common.get(rs.getString("NATIVE_CODE")));
			obj.setNativeName(Common.get(rs.getString("NATIVE_NAME")));
			obj.setZoneCode(Common.get(rs.getString("ZONE_CODE")));
			obj.setAddress(Common.get(rs.getString("ADDRESS")));
			obj.setForAddress(Common.get(rs.getString("FOR_ADDRESS")));
			obj.setArriveDate(Common.get(rs.getString("ARRIVE_DATE")));
			obj.setInvestAmt(Common.get(rs.getString("INVEST_AMT")));
			obj.setRespType(Common.get(rs.getString("RESP_TYPE")));
			obj.setUpdateUser(Common.get(rs.getString("UPDATE_USER")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateTime(Common.get(rs.getString("UPDATE_TIME")));
			obj.setHypothecStock(Common.get(rs.getString("HYPOTHEC_STOCK")));
			obj.setHypothecDate(Common.get(rs.getString("HYPOTHEC_DATE")));
			obj.setReleaseStock(Common.get(rs.getString("RELEASE_STOCK")));
			obj.setReleaseDate(Common.get(rs.getString("RELEASE_DATE")));
			obj.setTotalHypothecStock(Common.get(rs.getString("TOTAL_HYPOTHEC_STOCK")));
			obj.setRemoveDate(Common.get(rs.getString("REMOVE_DATE")));
			obj.setRemoveWord(Common.get(rs.getString("REMOVE_WORD")));
			obj.setRemoveNo(Common.get(rs.getString("REMOVE_NO")));
			obj.setRemoveEffectiveDate(Common.get(rs.getString("REMOVE_EFFECTIVE_DATE")));
			obj.setHypothecGrantDate(Common.get(rs.getString("HYPOTHEC_GRANT_DATE")));
			obj.setHypothecGrantWord(Common.get(rs.getString("HYPOTHEC_GRANT_WORD")));
			obj.setHypothecGrantNo(Common.get(rs.getString("HYPOTHEC_GRANT_NO")));
			obj.setCancelDate(Common.get(rs.getString("CANCEL_DATE")));
			obj.setCancelWord(Common.get(rs.getString("CANCEL_WORD")));
			obj.setCancelNo(Common.get(rs.getString("CANCEL_NO")));
			obj.setCancelLaw(Common.get(rs.getString("CANCEL_LAW")));
			
		}
		return obj;
	}
}
