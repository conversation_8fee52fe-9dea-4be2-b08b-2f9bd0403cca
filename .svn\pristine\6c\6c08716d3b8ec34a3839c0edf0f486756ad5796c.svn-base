package com.kangdainfo.tcfi.service.impl;

import java.text.DecimalFormat;
import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1100;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1101;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1102;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1110;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1122;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1123;
import com.kangdainfo.tcfi.model.eicm.bo.EncapsulationVo;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1019Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1100Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1101Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1102Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1110Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1122Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1123Dao;
import com.kangdainfo.tcfi.service.BackupService;
import com.kangdainfo.tcfi.service.EncapsulateService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;

public class EncapsulateServiceImpl implements EncapsulateService {

	public void doEncapsulateByYear(String year, String idNo) throws Exception {
		DecimalFormat df = new DecimalFormat("000000");
		Cedb1019 cedb1019 = cedb1019Dao.findByYearNo(year);
		if(null!=cedb1019) {
			String maxPrefixNo = cedb1019.getPrefixNo();
			if( null!=maxPrefixNo && !"".equals(maxPrefixNo) ) {
				int end = Integer.parseInt(maxPrefixNo.replaceFirst(year, ""));
				String prefixNo = "";
				for(int i=1;i<=end;i++) {
					prefixNo = year + df.format(i);
					doEncapsulateByPrefixNo(prefixNo, idNo);
				}
			}
		}
	}

	public void doEncapsulateByPrefixNo(String prefixNo, String idNo) throws Exception {
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null!=cedb1000) {
			//複製 - 基本資料
			doCopy1000To1100(cedb1000.getPrefixNo(), idNo);
			//複製 - 公司名稱資料
			doCopy1001To1101(cedb1000.getPrefixNo(), idNo);
			//複製 - 營業項目資料
			doCopy1002To1102(cedb1000.getPrefixNo(), idNo);
			//複製 - 案件過程記錄
			doCopy1010To1110(cedb1000.getPrefixNo(), idNo);
			//複製 - 法人資料
			doCopy1022To1122(cedb1000.getPrefixNo(), idNo);
			//複製 - 收件人資料
			doCopy1023To1123(cedb1000.getPrefixNo(), idNo);
			//備份
			backupService.doBackup(prefixNo, idNo);
			//刪除現況 - 基本資料
			cedb1000Dao.deleteByPrefixNo(prefixNo);
			//刪除現況 - 公司名稱資料
			cedb1001Dao.deleteByPrefixNo(prefixNo);
			//刪除現況 - 營業項目資料
			cedb1002Dao.deleteByPrefixNo(prefixNo);
			//刪除現況 - 案件過程記錄
			cedb1010Dao.deleteByPrefixNo(prefixNo);
			//刪除現況 - 法人資料
			cedb1022Dao.deleteByPrefixNo(prefixNo);
			//刪除現況 - 收件人資料
			cedb1023Dao.deleteByPrefixNo(prefixNo);
		}
	}

	private void doCopy1000To1100(String prefixNo, String idNo) throws Exception {
		// 由資料庫中取得所有符合條件的資料
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null!=cedb1000) {
			Cedb1100 cedb1100 = new Cedb1100();
			cedb1100.setPrefixNo(cedb1000.getPrefixNo());
			cedb1100.setBanNo(cedb1000.getBanNo());
			cedb1100.setApplyKind(cedb1000.getApplyKind());
			cedb1100.setApplyName(cedb1000.getApplyName());
			cedb1100.setApplyId(cedb1000.getApplyId());
			cedb1100.setApplyAddr(cedb1000.getApplyAddr());
			cedb1100.setApplyTel(cedb1000.getApplyTel());
			cedb1100.setAttorName(cedb1000.getAttorName());
			cedb1100.setAttorNo(cedb1000.getAttorNo());
			cedb1100.setAttorAddr(cedb1000.getAttorAddr());
			cedb1100.setAttorTel(cedb1000.getAttorTel());
			cedb1100.setGetKind(cedb1000.getGetKind());
			cedb1100.setApplyType(cedb1000.getApplyType());
			cedb1100.setCompanyName(cedb1000.getCompanyName());
			cedb1100.setTelixNo(cedb1000.getTelixNo());
			cedb1100.setReceiveDate(cedb1000.getReceiveDate());
			cedb1100.setReceiveTime(cedb1000.getReceiveTime());
			cedb1100.setApproveDate(cedb1000.getApproveDate());
			cedb1100.setApproveTime(cedb1000.getApproveTime());
			cedb1100.setApproveResult(cedb1000.getApproveResult());
			cedb1100.setReserveMark(cedb1000.getReserveMark());
			cedb1100.setReserveDate(cedb1000.getReserveDate());
			cedb1100.setGetDate(cedb1000.getGetDate());
			cedb1100.setGetTime(cedb1000.getGetTime());
			cedb1100.setSpecialName(cedb1000.getSpecialName());
			cedb1100.setCompanyStus(cedb1000.getCompanyStus());
			cedb1100.setRegUnit(cedb1000.getRegUnit());
			cedb1100.setRemark(cedb1000.getRemark());
			cedb1100.setAssignDate(cedb1000.getAssignDate());
			cedb1100.setAssignTime(cedb1000.getAssignTime());
			cedb1100.setIdNo(cedb1000.getIdNo());
			cedb1100.setStaffName(cedb1000.getStaffName());
			cedb1100.setUpdateCode(cedb1000.getUpdateCode());
			cedb1100.setCodeNo(cedb1000.getCodeNo());
			cedb1100.setCodeName(cedb1000.getCodeName());
			cedb1100.setUpdateIdNo(cedb1000.getUpdateIdNo());
			cedb1100.setUpdateDate(cedb1000.getUpdateDate());
			cedb1100.setUpdateTime(cedb1000.getUpdateTime());
			cedb1100.setRegDate(cedb1000.getRegDate());
			cedb1100.setControlCd1(cedb1000.getControlCd1());
			cedb1100.setControlCd2(cedb1000.getControlCd2());
			cedb1100.setZoneCode(cedb1000.getZoneCode());
			cedb1100.setApproveMark(cedb1000.getApproveMark());
			cedb1100.setCloseDate(cedb1000.getCloseDate());
			cedb1100.setCloseTime(cedb1000.getCloseTime());
			cedb1100.setWorkDay(cedb1000.getWorkDay());
			cedb1100.setRemark1(cedb1000.getRemark1());
			cedb1100.setOldCompanyName(cedb1000.getOldCompanyName());
			cedb1100.setPrefixStatus(cedb1000.getPrefixStatus());
			cedb1100.setReserveDays(cedb1000.getReserveDays());
			cedb1100.setRefundNo(cedb1000.getRefundNo());
			cedb1100.setExtendReason(cedb1000.getExtendReason());
			cedb1100.setExtendOther(cedb1000.getExtendOther());
			cedb1100.setExtendDate(cedb1000.getExtendDate());
			cedb1100.setOtherReason(cedb1000.getOtherReason());
			cedb1100Dao.insert(cedb1100);
		}
	}

	private void doCopy1001To1101(String prefixNo, String idNo) throws Exception {
		// 由資料庫中取得所有符合條件的資料
		List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(prefixNo);
		if (cedb1001s.size() < 1)
			return;

		Cedb1101 cedb1101 = null;
		for (Cedb1001 cedb1001 : cedb1001s) {
			cedb1101 = new Cedb1101();
			cedb1101.setApproveResult(cedb1001.getApproveResult());
			cedb1101.setCompanyName(cedb1001.getCompanyName());
			cedb1101.setPrefixNo(cedb1001.getPrefixNo());
			cedb1101.setRemark(cedb1001.getRemark());
			cedb1101.setSeqNo(cedb1001.getSeqNo());
			cedb1101Dao.insert(cedb1101);
		}
	}

	private void doCopy1002To1102(String prefixNo, String idNo) throws Exception {
		Cedb1102 cedb1102 = null;
		// 由資料庫中取得所有符合條件的資料
		List<Cedb1002> cedb1002s = cedb1002Dao.findByPrefixNo(prefixNo);
		if (cedb1002s.size() < 1)
			return;

		for (Cedb1002 cedb1002 : cedb1002s) {
			cedb1102 = new Cedb1102();
			cedb1102.setBusiItem(cedb1002.getBusiItem());
			cedb1102.setBusiItemNo(cedb1002.getBusiItemNo());
			cedb1102.setPrefixNo(cedb1002.getPrefixNo());
			cedb1102.setSeqNo(cedb1002.getSeqNo());
			cedb1102Dao.insert(cedb1102);
		}
	}

	private void doCopy1010To1110(String prefixNo, String idNo) throws Exception {
		Cedb1110 cedb1110 = null;
		// 由資料庫中取得所有符合條件的資料
		List<Cedb1010> cedb1010s = cedb1010Dao.findByPrefixNo(prefixNo);
		if (cedb1010s.size() < 1)
			return;

		for (Cedb1010 cedb1010 : cedb1010s) {
			cedb1110 = new Cedb1110();
			cedb1110.setPrefixNo(cedb1010.getPrefixNo());
			cedb1110.setIdNo(cedb1010.getIdNo());
			cedb1110.setProcessDate(cedb1010.getProcessDate());
			cedb1110.setProcessTime(cedb1010.getProcessTime());
			cedb1110.setProcessStatus(cedb1010.getProcessStatus());
			cedb1110.setWorkDay(cedb1010.getWorkDay());
			cedb1110Dao.insert(cedb1110);
		}
	}

	private void doCopy1022To1122(String prefixNo, String idNo) throws Exception {
		Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(prefixNo);
		if(null!=cedb1022) {
			Cedb1122 o = new Cedb1122();
			o.setPrefixNo(cedb1022.getPrefixNo());
			o.setApplyBanNo(cedb1022.getApplyBanNo());
			o.setApplyLawName(cedb1022.getApplyLawName());
			cedb1122Dao.insert(o);
		}
	}

	private void doCopy1023To1123(String prefixNo, String idNo) throws Exception {
		Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
		if(null!=cedb1023) {
			Cedb1123 o = new Cedb1123();
			o.setPrefixNo(cedb1023.getPrefixNo());
			o.setGetAddr(cedb1023.getGetAddr());
			o.setGetName(cedb1023.getGetName());
			o.setSms(cedb1023.getSms());
			o.setContactCel(cedb1023.getContactCel());
			o.setChangeType(cedb1023.getChangeType());
			cedb1123Dao.insert(o);
		}
	}

	@Override
	public EncapsulationVo findEncapsulation(String prefixNo) {
		Cedb1100 m = cedb1100Dao.findByPrefixNo(prefixNo, null);
		if (m != null) {
			String companyStus = m.getCompanyStus();
			String reserveDate = m.getReserveDate();
			String approveResult = m.getApproveResult();
			String prefixStatus = m.getPrefixStatus();

			EncapsulationVo encap = new EncapsulationVo();
			encap.setCedb1101s(cedb1101Dao.findByPrefixNo(prefixNo));
			encap.setCedb1102s(cedb1102Dao.findByPrefixNo(prefixNo));

			encap.setCedb1110s(cedb1110Dao.findByPrefixNo(prefixNo));
			for(Cedb1110 cedb1110 : encap.getCedb1110s()) {
				cedb1110.setProcessStatus(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(cedb1110.getProcessStatus()));
				cedb1110.setProcessDate(Datetime.formatRocDate(cedb1110.getProcessDate())+"  "+Datetime.formatRocTime(cedb1110.getProcessTime()));
				cedb1110.setIdNo(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1110.getIdNo()));
			}

			String receiverAddr = "";//收件人地址
			String receiverName = "";//收件人姓名
			String contactCel = "";//接收簡訊手機號碼
			String applyKindDesc = "";
			Cedb1123 cedb1123 = cedb1123Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1123) {
				receiverAddr = cedb1123.getGetAddr();
				receiverName = cedb1123.getGetName();
				contactCel = cedb1123.getContactCel();
				applyKindDesc = ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(cedb1123.getChangeType());
			} else {
				Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
				if(null!=cedb1023) {
					receiverAddr = cedb1023.getGetAddr();
					receiverName = cedb1023.getGetName();
					contactCel = cedb1023.getContactCel();
					applyKindDesc = ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(cedb1023.getChangeType());
				}
			}
			encap.setReceiverAddr(receiverAddr);
			encap.setReceiverName(receiverName);
			encap.setContactCel(contactCel);
			encap.setApplyKindDesc(("".equals(Common.get(applyKindDesc))?("1".equals(m.getApplyKind())?"設立":"變更"):applyKindDesc));
			
			String applyLawName = "";
			String applyBanNo = "";
			Cedb1122 cedb1122 = cedb1122Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1122) {
				applyLawName = Common.get(cedb1122.getApplyLawName());
				applyBanNo = Common.get(cedb1122.getApplyBanNo());
			} else {
				Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(prefixNo);
				if(null!=cedb1022) {
					applyLawName = Common.get(cedb1022.getApplyLawName());
					applyBanNo = Common.get(cedb1022.getApplyBanNo());
				}
			}
			encap.setApplyLawName(applyLawName);
			encap.setApplyBanNo(applyBanNo);

			String getKindDesc = "";
			if( "1".equals(m.getGetKind()) ) {
				getKindDesc = "自取";
			} else {
				String postNo = "";
				Cedb1027 cedb1027 = cedb1027Dao.findLastOneByPrefixNo(prefixNo);
				if(null!=cedb1027) {
					postNo = cedb1027.getPostNo();
				}
				getKindDesc = "郵寄　掛號號碼"+Common.get(postNo);
			}
			encap.setGetKindDesc(getKindDesc);

			encap.setPrefixNo(m.getPrefixNo());
			encap.setBanNo(m.getBanNo());
			encap.setApplyName(m.getApplyName());
			encap.setApplyId(m.getApplyId());
			encap.setApplyAddr(m.getApplyAddr());
			encap.setApplyTel(m.getApplyTel());
			encap.setAttorName(m.getAttorName());
			encap.setAttorNo(m.getAttorNo());
			encap.setAttorAddr(m.getAttorAddr());
			encap.setAttorTel(m.getAttorTel());
			encap.setApplyType(m.getApplyType());
			encap.setCompanyName(m.getCompanyName());
			encap.setTelixNo(m.getTelixNo());
			encap.setReceiveDate(m.getReceiveDate());
			encap.setReceiveTime(m.getReceiveTime());
			encap.setApproveDate(m.getApproveDate());
			encap.setApproveTime(m.getApproveTime());
			encap.setApproveResult(m.getApproveResult());
			encap.setReserveMark(m.getReserveMark());
			encap.setReserveDate(m.getReserveDate());
			encap.setGetDate(m.getGetDate());
			encap.setGetTime(m.getGetTime());
			encap.setSpecialName(m.getSpecialName());
			encap.setCompanyStus(m.getCompanyStus());
			encap.setRegUnit(m.getRegUnit());
			encap.setRemark(m.getRemark());
			encap.setAssignDate(m.getAssignDate());
			encap.setAssignTime(m.getAssignTime());
			encap.setIdNo(m.getIdNo());
			encap.setStaffName(m.getStaffName());
			encap.setUpdateIdNo(m.getUpdateIdNo());
			encap.setUpdateDate(m.getUpdateDate());
			encap.setUpdateTime(m.getUpdateTime());
			encap.setRegDate(m.getRegDate());
			encap.setZoneCode(m.getZoneCode());
			encap.setApproveMark(m.getApproveMark());
			encap.setCloseDate(m.getCloseDate());
			encap.setCloseTime(m.getCloseTime());
			encap.setWorkDay(m.getWorkDay());
			encap.setRemark1(m.getRemark1());
			encap.setOldCompanyName(m.getOldCompanyName());
			encap.setPrefixStatus(m.getPrefixStatus());
			encap.setReserveDays(m.getReserveDays());

			encap.setApproveResultDesc(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(approveResult));
			encap.setPrefixStatusDesc(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(prefixStatus));
			encap.setApplyWay(TcfiView.getApplyWayByTelixNo(m.getTelixNo()));
			encap.setUpdateName(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(m.getUpdateIdNo()));
			encap.setReceiveDateTime(Datetime.formatRocDate(m.getReceiveDate())+" "+Datetime.formatRocTime(m.getReceiveTime()));
			encap.setAssignDateTime(Datetime.formatRocDate(m.getAssignDate())+" "+Datetime.formatRocTime(m.getAssignTime()));
			encap.setApproveDateTime(Datetime.formatRocDate(m.getApproveDate())+" "+Datetime.formatRocTime(m.getApproveTime()));
			encap.setCloseDateTime(Datetime.formatRocDate(m.getCloseDate())+" "+Datetime.formatRocTime(m.getCloseTime()));
			encap.setGetDateTime(Datetime.formatRocDate(m.getGetDate())+" "+Datetime.formatRocTime(m.getGetTime()));
			encap.setReceiveKeyinDateTime(cedb1110Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_2));
			encap.setIssueKeyinDateTime(cedb1110Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_7));
			//提示文字
			encap.setReserveTip(TcfiView.getReserveTip(approveResult, reserveDate, prefixStatus, companyStus));
			return encap;
		}
		return null;
	}

	private BackupService backupService;
	private Cedb1000Dao cedb1000Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1010Dao cedb1010Dao;
	private Cedb1019Dao cedb1019Dao;
	private Cedb1022Dao cedb1022Dao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1027Dao cedb1027Dao;
	private Cedb1100Dao cedb1100Dao;
	private Cedb1101Dao cedb1101Dao;
	private Cedb1102Dao cedb1102Dao;
	private Cedb1110Dao cedb1110Dao;
	private Cedb1122Dao cedb1122Dao;
	private Cedb1123Dao cedb1123Dao;

	public BackupService getBackupService() {return backupService;}
	public void setBackupService(BackupService service) {this.backupService = service;}

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}

	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}

	public Cedb1100Dao getCedb1100Dao() {return cedb1100Dao;}
	public void setCedb1100Dao(Cedb1100Dao dao) {this.cedb1100Dao = dao;}

	public Cedb1101Dao getCedb1101Dao() {return cedb1101Dao;}
	public void setCedb1101Dao(Cedb1101Dao dao) {this.cedb1101Dao = dao;}

	public Cedb1102Dao getCedb1102Dao() {return cedb1102Dao;}
	public void setCedb1102Dao(Cedb1102Dao dao) {this.cedb1102Dao = dao;}

	public Cedb1019Dao getCedb1019Dao() {return cedb1019Dao;}
	public void setCedb1019Dao(Cedb1019Dao dao) {this.cedb1019Dao = dao;}

	public Cedb1123Dao getCedb1123Dao() {return cedb1123Dao;}
	public void setCedb1123Dao(Cedb1123Dao dao) {this.cedb1123Dao = dao;}

	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}

	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}

	public Cedb1027Dao getCedb1027Dao() {return cedb1027Dao;}
	public void setCedb1027Dao(Cedb1027Dao dao) {this.cedb1027Dao = dao;}

	public Cedb1110Dao getCedb1110Dao() {return cedb1110Dao;}
	public void setCedb1110Dao(Cedb1110Dao dao) {this.cedb1110Dao = dao;}

	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}

	public Cedb1122Dao getCedb1122Dao() {return cedb1122Dao;}
	public void setCedb1122Dao(Cedb1122Dao dao) {this.cedb1122Dao = dao;}

}