package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;

public class Eedb1000Dao extends BaseDaoJdbc implements RowMapper<Eedb1000> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB1000 WHERE TELIX_NO = ?";
	public Eedb1000 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}
	
	public Eedb1000 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb1000 obj = null;
		if(null!=rs) {
			obj = new Eedb1000();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setWriterType(rs.getString("WRITER_TYPE"));
			obj.setRoleType(rs.getString("ROLE_TYPE"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApplyType(rs.getString("APPLY_TYPE"));
			obj.setPrintFlag(rs.getString("PRINT_FLAG"));
			obj.setTotalFee(rs.getInt("TOTAL_FEE"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setHouseTaxNo(rs.getString("HOUSE_TAX_NO"));
			obj.setOrgnType(rs.getString("ORGN_TYPE"));
			obj.setClosed(rs.getString("CLOSED"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setCompanyZipCode(rs.getString("COMPANY_ZIP_CODE"));
			obj.setCompanyAreaCode(rs.getString("COMPANY_AREA_CODE"));
			obj.setCompanyAddr(rs.getString("COMPANY_ADDR"));
			obj.setCompanyVillage(rs.getString("COMPANY_VILLAGE"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setApplyId(rs.getString("APPLY_ID"));
			obj.setContactName(rs.getString("CONTACT_NAME"));
			obj.setContactAddr(rs.getString("CONTACT_ADDR"));
			obj.setContactVillage(rs.getString("CONTACT_VILLAGE"));
			obj.setContactAreaCode(rs.getString("CONTACT_AREA_CODE"));
			obj.setContactZipCode(rs.getString("CONTACT_ZIP_CODE"));
			obj.setContactTel(rs.getString("CONTACT_TEL"));
			obj.setContactCel(rs.getString("CONTACT_CEL"));
			obj.setContactFax(rs.getString("CONTACT_FAX"));
			obj.setContactGetKind(rs.getString("CONTACT_GET_KIND"));
			obj.setContactEmail(rs.getString("CONTACT_EMAIL"));
			obj.setAttorName(rs.getString("ATTOR_NAME"));
			obj.setAttorId(rs.getString("ATTOR_ID"));
			obj.setAttorAddr(rs.getString("ATTOR_ADDR"));
			obj.setAttorVillage(rs.getString("ATTOR_VILLAGE"));
			obj.setAttorAreaCode(rs.getString("ATTOR_AREA_CODE"));
			obj.setAttorZipCode(rs.getString("ATTOR_ZIP_CODE"));
			obj.setAttorTel(rs.getString("ATTOR_TEL"));
			obj.setAttorNo(rs.getString("ATTOR_NO"));
			obj.setOrderDate(rs.getString("ORDER_DATE"));
			obj.setOrderTime(rs.getString("ORDER_TIME"));
			obj.setOpenDate(rs.getString("OPEN_DATE"));
			obj.setReceiveDate(rs.getString("RECEIVE_DATE"));
			obj.setReceiveTime(rs.getString("RECEIVE_TIME"));
			obj.setApproveDate(rs.getString("APPROVE_DATE"));
			obj.setApproveTime(rs.getString("APPROVE_TIME"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setProcessStatus(rs.getString("PROCESS_STATUS"));
			obj.setPaymentStatus(rs.getString("PAYMENT_STATUS"));
			obj.setReserveDate(rs.getString("RESERVE_DATE"));
			obj.setWriterMemo(rs.getString("WRITER_MEMO"));
			obj.setEnrolmentXml(rs.getString("ENROLMENT_XML"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setUid(rs.getString("UID"));
			obj.setSms(rs.getString("SMS"));
			obj.setIsRcv(rs.getString("IS_RCV"));
			obj.setCmpyRemitEname(rs.getString("CMPY_REMIT_ENAME"));
		}
		return obj;
	}
}
