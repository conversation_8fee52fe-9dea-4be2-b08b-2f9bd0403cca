package com.kangdainfo.tcfi.lucene.service;

import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;

/**
 * 重建檢索檔
 * 1. 新增 INDEX_LOG(執行WS10000)
 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
 * 3. 備份舊的檢索檔案
 * 4. 呼叫 IndexDatabase.createIndex() 重建檢索檔 (含同音同義字)
 * 5. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
 */
public interface IndexCreateService {

	/** 開始重建 */
	public IndexLog doStartBuildIndex();

	/** 執行重建 */
	public IndexLog doBuildIndex(IndexLog indexLog);

	/** 結束重建 */
	public void doEndBuildIndex(IndexLog indexLog);

}
