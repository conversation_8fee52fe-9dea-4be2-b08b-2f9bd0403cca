<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- USE JNDI CONNECTION -->
	<!--
	<bean id="icmsDataSource" class="org.springframework.jndi.JndiObjectFactoryBean"> 
		<property name="jndiName"> <value>java:comp/env/jdbc/icms</value> </property> 
	</bean>
	-->
	<bean id="icmsDataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${icms.dataSource.driverClassName}" />
		<property name="url" value="${icms.dataSource.url}" />
		<property name="username" value="${icms.dataSource.username}" />
		<property name="password" value="${icms.dataSource.password}" />
		<property name="validationQuery" value="${icms.dataSource.validationQuery}" />
		<property name="poolPreparedStatements" value="${icms.dataSource.poolPreparedStatements}" />
		<property name="maxOpenPreparedStatements" value="${icms.dataSource.maxOpenPreparedStatements}" />
		<property name="maxActive" value="${icms.dataSource.maxActive}" />
		<property name="maxIdle" value="${icms.dataSource.maxIdle}" />
	</bean>

	<!-- ICMS -->
	<bean id="csmdAreaDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdAreaDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdBusItemCodeDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdBusItemCodeDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdCountryDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdCountryDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdCtrlitemDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdCtrlitemDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdRegunitDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdRegunitDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdUnitCodeDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdUnitCodeDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmdWorkDayDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmdWorkDayDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmmCmpyInfoDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmmCmpyInfoDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmmMoeaicApproveDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmmMoeaicApproveDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="csmlCmpyTranMoeaicDao" class="com.kangdainfo.tcfi.model.icms.dao.CsmlCmpyTranMoeaicDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="zsmxEmptyWordDao" class="com.kangdainfo.tcfi.model.icms.dao.ZsmxEmptyWordDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="icmsGeneralQueryDao" class="com.kangdainfo.tcfi.model.icms.dao.IcmsGeneralQueryDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<bean id="lmsmBussMainDao" class="com.kangdainfo.tcfi.model.lms.dao.LmsmBussMainDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	
		<bean id="lmsBusiItemDao" class="com.kangdainfo.tcfi.model.lms.dao.LmsmBusiItemDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<!-- 2024/04/16 新增 -->
	<bean id="lmsdCodemappingDao" class="com.kangdainfo.tcfi.model.lms.dao.LmsdCodemappingDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<!-- 2024/04/17 新增 -->
	<bean id="lmsdRegUnitDao" class="com.kangdainfo.tcfi.model.lms.dao.LmsdRegUnitDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<!-- 2024/05/17 新增 -->
	<bean id="csyssUserDao" class="com.kangdainfo.tcfi.model.crmsmoea.dao.CsyssUserDao">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
</beans>