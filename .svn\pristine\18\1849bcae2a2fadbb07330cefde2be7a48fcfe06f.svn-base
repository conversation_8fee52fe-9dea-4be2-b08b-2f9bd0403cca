package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdBusItemCode;

public class CsmdBusItemCodeDao extends BaseDaoJdbc implements RowMapper<CsmdBusItemCode> {

	private static final String SQL_defaultOrder = "order by BUS_ITEM_TYPE";

	private static final String SQL_findAll = "select * from icms.CSMD_BUS_ITEM_CODE where enable = 'Y'";
	public List<CsmdBusItemCode> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdBusItemCode mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdBusItemCode obj = null;
		if(null!=rs) {
			obj = new CsmdBusItemCode();
			obj.setBusItemType(rs.getString("BUS_ITEM_TYPE"));
			obj.setTypeName(rs.getString("TYPE_NAME"));
			obj.setAdminUnit(rs.getString("ADMIN_UNIT"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setChinaPermit(rs.getString("CHINA_PERMIT"));
		}
		return obj;
	}

}