<!--
程式目的：預查案件收文確認 
程式代號：PRE1003
撰寫日期：103.04.25
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1003" />
</jsp:include>
<%
if("save".equals(obj.getState())) {
	obj.save();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE1003"/></jsp:include>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/>
<%@ include file="../../home/<USER>" %>
<script>
$( document ).ready(function() {
    //事件註冊
    $("#refresh").click(function(){
    	document.getElementById("refresh").disabled = true;
    	document.getElementById("save").disabled = true;
    	document.getElementById("selectAll").disabled = true;
    	document.getElementById("unSelectAll").disabled = true;
		list();
    });
	$("#selectAll").click(function() {
		commonUtils.all("prefixNos");
	});
	$("#selectTop10").click(function() {
		commonUtils.top10("prefixNos");
	});
	$("#unSelectAll").click(function() {
		commonUtils.unAll("prefixNos");
	});
	$("#save").click(function() {
		document.getElementById("refresh").disabled = true;
    	document.getElementById("save").disabled = true;
    	document.getElementById("selectAll").disabled = true;
    	document.getElementById("unSelectAll").disabled = true;
    	document.getElementById("selectTop10").disabled = true;
		form1.state.value = "save";
	    if(isFirstChecked() && isSequentialChecked()) {
	    	form1.submit();
	    } else {
			alert("不可跳號，無法存檔");
			document.getElementById("refresh").disabled = false;
	    	document.getElementById("save").disabled = false;
	    	document.getElementById("selectAll").disabled = false;
	    	document.getElementById("unSelectAll").disabled = false;
	    	document.getElementById("selectTop10").disabled = false;
		}
	});

    //畫面載入
    $.blockUI({ message: '<h1>資料載入中，請稍後...</h1>' });
	$.post( getVirtualPath() + "tcfi/ajax/jsonPre1003Cedb1000.jsp?q=N", function( json ) {
		$.unblockUI();
		var trClass = '';
		for (var i = 0 ; i < json.length ; i++) {
			trClass = (i % 2 == 0) ? 'listTREven' : 'listTROdd';

			$("#form2 tbody").append('<tr class="'+ trClass +'">' +
					'<td>' + (i+1) +'</td>' +
					'<td id=' +i +' ><input type="checkbox" name="prefixNos" value="'+ json[i].PREFIX_NO +'"/></td>'+
					'<td><div onclick="openPre4001(\''+ json[i].PREFIX_NO +'\')">'+ json[i].PREFIX_NO +'</div></td>' +
					'<td><div onclick="openPre3008(\''+ json[i].BAN_NO +'\');">'+ json[i].BAN_NO +'</div></td>' +
					//'<td style="text-align:left">'+ json[i].APPLY_KIND +'</td>' +
					'<td style="text-align:left">'+ json[i].APPLY_NAME +'</td>' +
					'<td style="text-align:left"><input class="toolbar_default" type="button" name="accessory" value="附件" onclick="openAccessory(\''+ json[i].PREFIX_NO +'\');"></td>' +
					'<td style="text-align:left">'+ json[i].APPLY_WAY +'</td>' +
					'<td>'+ json[i].RECEIVE_DATE +'</td>' +
				 	'<td><span id="noNeedPay_'+json[i].PREFIX_NO+'" style="color:#FF0000"></span></td>'+
				 	'<td><span id="payOrNot_'+json[i].PREFIX_NO+'" style="color:#FF0000"></span></td>'+
				 	'</tr>');
			if( '2' == json[i].CHANGE_TYPE || '3' == json[i].CHANGE_TYPE ) {
				$('#noNeedPay_'+json[i].PREFIX_NO).html("查詢中...");
				$.post( getVirtualPath() + "tcfi/ajax/jsonPre1003ChkNoNeedPay.jsp?q=" + json[i].PREFIX_NO, function( data ) {
					if ( data.noNeedPay != null ) {
						var isNoNeedPay = data.noNeedPay == "Y" ? "免繳審查費" : "";
						$('#noNeedPay_'+data.prefixNo).html(isNoNeedPay);
					} // if
				});
			}
			if( json[i].APPLY_WAY == "一站式" && json[i].CHANGE_TYPE != 0 ) {
				$('#payOrNot_'+json[i].PREFIX_NO).html("查詢中...");
				$.post( getVirtualPath() + "tcfi/ajax/jsonPre1003PayOrNot.jsp?q=" + json[i].PREFIX_NO, function( data ) {
					if ( data.payOrNot != null ) {
						var isPayOrNot = data.payOrNot == "未繳費" ? "未繳費" : "";
						$('#payOrNot_'+data.prefixNo).html(isPayOrNot);
					} // if
				});
			}
			
		};

	});
});

function openPre4001(prefixNo) {
	var prop="";
	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	var url = getVirtualPath() + "tcfi/pre/pre4001_00.jsp?prefixNos="+prefixNo;
	window.open(url,'pre4001',prop).focus();
}

function openPre3008(banNo) {
	var prop="";
	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	var url = getVirtualPath() + "tcfi/pre/pre3008_00.jsp?banNos="+banNo;
	window.open(url,'pre3008',prop).focus();
}

function openAccessory(prefixNo) {
	var prop="";
	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	var url = getVirtualPath() + "tcfi/pre/pre1003_00.jsp?prefixNo="+prefixNo;
	window.open(url,'pre1003_00',prop).focus();
}

function isFirstChecked() {
	return $("input[name=prefixNos]").eq(0).prop('checked');
}

function isSequentialChecked() {
	// 103/10/30 依使用者要求加入判斷勾選的預查編號必須是連號的功能
 	var count = 0;
 	var size = $("input[name=prefixNos]").length;
 	var index = 0;
 	while ( index < size ) {
 		if (index != 0) {
 			if ( $("input[name=prefixNos]")[index].checked && !$("input[name=prefixNos]")[index-1].checked  ) {
 				count++;
 			}
 			if ( !$("input[name=prefixNos]")[index].checked && $("input[name=prefixNos]")[index-1].checked  ) {
 				count++;
 			}
 		} // if
 		index++;
 	} // end while
 	if ( count >= 3 ) {
 		return false;
 	}	
 	else {
 		return true;
 	}
}

function list() {
	window.location = getVirtualPath() + "tcfi/pre/pre1003.jsp";
}
</script>
</head>
<body>
<form id="form1" name="rcvCheckForm" method="post">
<input type="hidden" name="state" value="<%=obj.getState()%>">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1003'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td>
				<input class="toolbar_default" type="button" id="refresh" name="btnQuery1" value="更新清單" >&nbsp;
				<input class="toolbar_default" type="button" id="selectAll" name="btnQuery2" value="全部選取" >&nbsp;
				<!--  103/10/30 依使用者要求加入選前10筆的按鈕 -->
				<input class="toolbar_default" type="button" id="selectTop10" name="btnQuery2.5" value="選前十筆" >&nbsp;
				<input class="toolbar_default" type="button" id="unSelectAll" name="btnQuery3" value="取消選取">&nbsp;
				<input class="toolbar_default" type="button" name="btnQuery4" value="儲存" id="save">&nbsp;
			</td>
			<td style="text-align:right">
				<c:import url="../common/shortcut.jsp">
					<c:param name="functions" value='PRE4001,PRE3002,PRE3008,PRE3013'/>
				</c:import>
			</td>
		</tr>     
	</table>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- LIST AREA -->
<tr><td class="bgList">
	<div id="listContainer">
	<table id="form2" class="table_form" width="100%" cellpadding="2" cellspacing="0">
		<thead id="listTHEAD">
			<tr>
				<th class="listTH" width="5%"><a class="text_link_w" href="#">序號</a></th>
				<th class="listTH" width="5%"><a class="text_link_w" href="#">選取</a></th>
				<th class="listTH" width="10%"><a class="text_link_w" href="#">預查編號</a></th>
				<th class="listTH" width="12%"><a class="text_link_w" href="#">統一編號</a></th>
				<!-- <th style="text-align:left" class="listTH" width="10%"><a class="text_link_w" href="#">預查種類</a></th> -->
				<th style="text-align:left" class="listTH" width="16%"><a class="text_link_w" href="#">申請人</a></th>
				<th style="text-align:left" class="listTH" width="10%"><a class="text_link_w" href="#">附件</a></th>
				<th style="text-align:left" class="listTH" width="12%"><a class="text_link_w" href="#">申請方式</a></th>
				<th class="listTH" width="10%"><a class="text_link_w" href="#">收文日期</a></th>
				<th class="listTH" width="10%"><a class="text_link_w" href="#">審查費</a></th>
				<th class="listTH" width="10%"><a class="text_link_w" href="#">是否繳費</a></th>
			</tr>
		</thead>
		<tbody></tbody>
	</table>
	</div>
</td></tr>
<!-- LIST AREA -->

</table>
</form>
</body>
</html>