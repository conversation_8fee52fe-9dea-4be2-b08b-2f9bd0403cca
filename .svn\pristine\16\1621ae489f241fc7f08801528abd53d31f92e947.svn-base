--DROP TABLE EICM.LOGIN_LOG;
-- Create table
CREATE TABLE EICM.LOGIN_LOG (
	ID NUMBER(15) not null,
	LOING_ID_NO VARCHAR2(20),
	LOING_NAME VARCHAR2(50),
	LOGIN_DATE VARCHAR2(20),
	LOGIN_TIME VARCHAR2(20),
	LOGIN_IP VARCHAR2(50),
	LOGIN_STATUS VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.LOGIN_LOG is '登入紀錄檔';
-- Add comments to the columns 
comment on column EICM.LOGIN_LOG.ID is '主鍵值';
comment on column EICM.LOGIN_LOG.LOING_ID_NO is '使用者帳號';
comment on column EICM.LOGIN_LOG.LOING_NAME is '使用者名稱';
comment on column EICM.LOGIN_LOG.LOGIN_DATE is '登入日期';
comment on column EICM.LOGIN_LOG.LOGIN_TIME is '登入時間';
comment on column EICM.LOGIN_LOG.LOGIN_IP is '連線IP';
comment on column EICM.LOGIN_LOG.LOGIN_STATUS is '登入狀態';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.LOGIN_LOG
  add constraint PK_LOGIN_LOG primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_LOGIN_LOG_ID;
-- Create sequence 
create sequence EICM.SEQ_LOGIN_LOG_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_LOGIN_LOG
Before Insert ON EICM.LOGIN_LOG Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_LOGIN_LOG_ID.NextVal into :nu.id From Dual;
End;
   
-- SYNONYM 
create or replace synonym EICM4AP.LOGIN_LOG for EICM.LOGIN_LOG;
create or replace synonym EICM4CMPY.LOGIN_LOG for EICM.LOGIN_LOG;
create or replace synonym EICM4PREFIX.LOGIN_LOG for EICM.LOGIN_LOG;

-- Grant/Revoke object privileges 
grant all on EICM.LOGIN_LOG to EICM4AP;
grant all on EICM.LOGIN_LOG to EICM4CMPY;
grant all on EICM.LOGIN_LOG to EICM4PREFIX;

