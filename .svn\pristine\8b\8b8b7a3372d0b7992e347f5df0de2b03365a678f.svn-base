package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class Cedb1010Dao extends BaseDaoJdbc implements RowMapper<Cedb1010> {

	public String findProcessDateTime(String prefixNo, String processStatus) {
		String result = "";
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL("(");
		sqljob.appendSQL("case");
		sqljob.appendSQL("when nvl(process_date,' ') = ' ' then process_date");
		sqljob.appendSQL("when nvl(process_time,' ') = ' ' then process_time");
		sqljob.appendSQL("else");
		sqljob.appendSQL("substr(process_date,1,3)||'/'||substr(process_date,4,2)||'/'||substr(process_date,6,2)||'  '||substr(process_time,1,2)||':'||substr(process_time,3,2)||':'||substr(process_time,5,2)");
		sqljob.appendSQL("end");
		sqljob.appendSQL(") as RESULT");
		sqljob.appendSQL("FROM cedb1010");
		sqljob.appendSQL("WHERE prefix_no = ?");
		sqljob.appendSQL("AND process_status = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(processStatus);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Map<String,Object>> list = getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray());
		if(null!=list && !list.isEmpty()) {
			Map<String,Object> map = list.get(0);
			if(null!=map){
				result = (String)map.get("RESULT");
			}
		}
		if(logger.isDebugEnabled()) logger.debug("[findProcessDateTime][prefixNo:"+prefixNo+"][processStatus:"+processStatus+"][result:"+result+"]");
		return result;
	}

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1010 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, PROCESS_DATE, PROCESS_TIME, PROCESS_STATUS";
	public List<Cedb1010> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByUniqueKey = "SELECT * FROM CEDB1010 WHERE PREFIX_NO=? AND ID_NO=? AND PROCESS_STATUS=?";
	public Cedb1010 findByUniqueKey(String prefixNo, String idNo, String processStatus) {
		SQLJob sqljob = new SQLJob(SQL_findByUniqueKey);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(idNo);
		sqljob.addParameter(processStatus);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1010> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	private static final String SQL_findByPrefixNoAndStatus = "SELECT * FROM CEDB1010 WHERE PREFIX_NO=? AND PROCESS_STATUS=?";
	public Cedb1010 findByPrefixNoAndStatus(String prefixNo, String processStatus) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndStatus);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(processStatus);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1010> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	public Cedb1010 findLastOne(Cedb1010 cedb1010) {
		Cedb1010 result = null;
		if(null!=cedb1010 && null!=cedb1010.getPrefixNo()) {
			SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1010");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.addParameter(cedb1010.getPrefixNo());
			if(!"".equals(Common.get(cedb1010.getProcessDate())) && !"".equals(Common.get(cedb1010.getProcessTime()))) {
				sqljob.appendSQL("AND PROCESS_DATE||PROCESS_TIME < ?");
				sqljob.addParameter(cedb1010.getProcessDate()+cedb1010.getProcessTime());
			}
			if(!"".equals(Common.get(cedb1010.getProcessStatus()))) {
				if( PrefixConstants.PREFIX_STATUS_2.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1')");
				} else if( PrefixConstants.PREFIX_STATUS_3.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2')");
				} else if( PrefixConstants.PREFIX_STATUS_4.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2','3')");
				} else if( PrefixConstants.PREFIX_STATUS_5.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2','3','4')");
				} else if( PrefixConstants.PREFIX_STATUS_6.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2','3','4','5')");
				} else if( PrefixConstants.PREFIX_STATUS_7.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2','3','4','5','6')");
				} else if( PrefixConstants.PREFIX_STATUS_8.equals(cedb1010.getProcessStatus()) ) {
					sqljob.appendSQL("AND PROCESS_STATUS in ('1','2','3','4','5','6','7')");
				}
			}
			sqljob.appendSQL("ORDER BY PREFIX_NO, PROCESS_DATE, PROCESS_TIME, PROCESS_STATUS");
			if (logger.isDebugEnabled()) logger.debug(sqljob);
			List<Cedb1010> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
			if(null!=list && !list.isEmpty())
				result = list.get(list.size() - 1);
		}
		return result;
	}

	private static String SQL_insert = "INSERT INTO Cedb1010 (PREFIX_NO,ID_NO,PROCESS_DATE,PROCESS_TIME,PROCESS_STATUS,WORK_DAY) "
			+ "VALUES (?, ?, ?, ?, ?, ?) ";
	public void insert(Cedb1010 cedb1010) {
		if(null!=cedb1010) {
			SQLJob sqljob = new SQLJob(SQL_insert);
			sqljob.addParameter(cedb1010.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1010.getIdNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1010.getProcessDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1010.getProcessTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1010.getProcessStatus());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(cedb1010.getWorkDay());
			sqljob.addSqltypes(java.sql.Types.FLOAT);
			if (logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}

	public void updateByPrefixNoAndStatus(Cedb1010 obj) {
		SQLJob sqljob = new SQLJob("UPDATE CEDB1010 SET");
		sqljob.appendSQL(" ID_NO=?");
		sqljob.appendSQL(",PROCESS_DATE=?");
		sqljob.appendSQL(",PROCESS_TIME=?");
		sqljob.appendSQL(",WORK_DAY=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.appendSQL("AND PROCESS_STATUS = ?");
		sqljob.addParameter(obj.getIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getWorkDay());
		sqljob.addSqltypes(java.sql.Types.FLOAT);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public void updateByPrefixNoAndIdNoAndStatus(Cedb1010 obj) {
		SQLJob sqljob = new SQLJob("UPDATE CEDB1010 SET");
		sqljob.appendSQL(" PROCESS_DATE=?");
		sqljob.appendSQL(",PROCESS_TIME=?");
		sqljob.appendSQL(",WORK_DAY=?");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.appendSQL("AND ID_NO = ?");
		sqljob.appendSQL("AND PROCESS_STATUS = ?");
		sqljob.addParameter(obj.getProcessDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getWorkDay());
		sqljob.addSqltypes(java.sql.Types.FLOAT);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public void deleteByPrefixNoAndStatus(String prefixNo, String processStatus) {
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1010");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.appendSQL("AND PROCESS_STATUS = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(processStatus);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public void deleteByPrefixNoAndAfterStatus(String prefixNo, String processStatus) {
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1010");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.appendSQL("AND PROCESS_STATUS > ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(processStatus);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public void deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1010");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1010 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1010 obj = null;
		if (null != rs) {
			obj = new Cedb1010();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setProcessDate(rs.getString("PROCESS_DATE"));
			obj.setProcessTime(rs.getString("PROCESS_TIME"));
			obj.setProcessStatus(rs.getString("PROCESS_STATUS"));
			obj.setWorkDay(rs.getFloat("WORK_DAY"));
		}
		return obj;
	}

}