package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 發文登打
 *
 */
public class PRE2001 extends SuperBean {
	//查詢條件
	/** 預查編號 */
	private String q_prefixNo;
	/** 電子流水號 */
	private String q_telixNo;
	/** 申請人身分ID */
	private String q_applyId;
	/** 申請人姓名 */
	private String q_applyName;
	/** 統一編號 */
	private String q_banNo;
	/** 公司名稱 */
	private String q_companyName;
	/** 預設查詢待發文登打 */
	private String q_queryDefault;
	//預查編號
	private String[] prefixNos;
	//目前的預查編號
	private String current;

	private String functionName;

	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(",nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL(" ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE");
		sqljob.appendSQL(",A.APPROVE_RESULT");
		sqljob.appendSQL(",A.COMPANY_NAME");
		sqljob.appendSQL(",A.RESERVE_DATE");
		sqljob.appendSQL(",A.COMPANY_STUS");
		sqljob.appendSQL(",A.RECEIVE_DATE");
		sqljob.appendSQL(",A.STAFF_NAME");
		sqljob.appendSQL("FROM CEDB1000 A");
		sqljob.appendSQL("WHERE (A.APPROVE_RESULT='Y' OR A.APPROVE_RESULT='N')");//已審核(APPROVE_RESULT IN 'Y','N')
		sqljob.appendSQL("AND (A.APPROVE_DATE IS NOT NULL)");//已審核
		sqljob.appendSQL("AND (A.PREFIX_STATUS NOT IN ('A','E'))");//不為撤回(A)或撤回退費(E)
		if( "Y".equals(getQ_queryDefault()) ) {
			String currYYY = Datetime.getYYY();
			String lastTwoYYY = String.valueOf(Integer.parseInt(currYYY)-2);
			String lastYYY = String.valueOf(Integer.parseInt(currYYY)-1);
			sqljob.appendSQL("AND (A.PREFIX_NO LIKE "+Common.sqlChar(lastYYY+"%")+" OR A.PREFIX_NO LIKE "+Common.sqlChar(currYYY+"%")+" OR A.PREFIX_NO LIKE "+Common.sqlChar(lastTwoYYY+"%")+")");
			sqljob.appendSQL("AND (A.CLOSE_DATE IS NULL OR A.CLOSE_DATE= '')");//未結案
			sqljob.appendSQL("AND (0=(SELECT COUNT(1) FROM CEDB1010 WHERE PREFIX_NO=A.PREFIX_NO AND PROCESS_STATUS='7'))");//流程紀錄不含發文登打完成(7)
			//sqljob.appendSQL("AND (NOT EXISTS (SELECT PREFIX_NO FROM CEDB1010 WHERE PREFIX_NO=A.PREFIX_NO AND PROCESS_STATUS='7'))");//流程紀錄不含發文登打完成(7)
		} else {
			if( !"".equals(getQ_prefixNo()) ) {
				sqljob.appendSQL("AND (A.PREFIX_NO LIKE ?)");
				sqljob.addSuffixLikeParameter(getQ_prefixNo());
			}
			if( !"".equals(getQ_telixNo()) ) {
				sqljob.appendSQL("AND (A.TELIX_NO = ?)");
				sqljob.addParameter(getQ_telixNo().toUpperCase());
			}
			if( !"".equals(getQ_applyId()) ) {
				sqljob.appendSQL("AND (A.APPLY_ID = ?)");
				sqljob.addParameter(getQ_applyId().toUpperCase());
			}
			if( !"".equals(getQ_applyName()) ) {
				sqljob.appendSQL("AND (A.APPLY_NAME = ?)");
				sqljob.addParameter(getQ_applyName());
			}
			if( !"".equals(getQ_banNo()) ) {
				sqljob.appendSQL("AND (A.BAN_NO = ?)");
				sqljob.addParameter(getQ_banNo());
			}
			if( !"".equals(getQ_companyName()) ) {
				sqljob.appendSQL("AND ( A.COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%"));
				//需同步查詢CEDB1001
				//sqljob.appendSQL("OR A.PREFIX_NO IN ( SELECT PREFIX_NO FROM CEDB1001 WHERE COMPANY_NAME LIKE ? )");
				//sqljob.addLikeParameter(getQ_companyName());
				//改用Lucene查詢
				List<String> tempPrefixNos = ServiceGetter.getInstance().getIndexSearchService().searchPrefixNos(getQ_companyName());
				if(null!=tempPrefixNos && !tempPrefixNos.isEmpty()) {
					sqljob.appendSQL("OR A.PREFIX_NO IN (");
					boolean isFirst = true;
					for(String tempPrefixNo : tempPrefixNos) {
						if(!isFirst) sqljob.appendSQL(",");
						sqljob.appendSQL("'"+tempPrefixNo+"'");
						isFirst = false;
					}
					sqljob.appendSQL(")");
				}
				sqljob.appendSQL(")");
			}
		}
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");
		if(logger.isInfoEnabled()) logger.info(sqljob);
		java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[9];
			for(Map<String,Object> o : objList) {
				rowArray = new String[9];
				rowArray[0] = Common.get(o.get("PREFIX_NO"));
				rowArray[1] = Common.get(o.get("APPLY_NAME"));
				rowArray[2] = Common.get(o.get("CHANGE_TYPE"));
				rowArray[3] = ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get(o.get("APPROVE_RESULT")));
				rowArray[4] = Common.get(o.get("COMPANY_NAME"));
				rowArray[5] = Common.get(o.get("RESERVE_DATE"));
				rowArray[6] = ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(Common.get(o.get("COMPANY_STUS")));
				rowArray[7] = Common.get(o.get("RECEIVE_DATE"));
				rowArray[8] = Common.get(o.get("STAFF_NAME"));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}

	public String getQ_telixNo() {return checkGet(q_telixNo);}
	public void setQ_telixNo(String s) {this.q_telixNo = checkSet(s);}

	public String getQ_applyId() {return checkGet(q_applyId);}
	public void setQ_applyId(String s) {this.q_applyId = checkSet(s);}

	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {this.q_applyName = checkSet(s);}

	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {this.q_banNo = checkSet(s);}

	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {this.q_companyName = checkSet(s);}

	public String getQ_queryDefault() {return checkGet(q_queryDefault);}
	public void setQ_queryDefault(String s) {this.q_queryDefault = checkSet(s);}

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] prefixNos) {this.prefixNos = prefixNos;}

	public String getCurrent() {return checkGet(current);}
	public void setCurrent(String s) {this.current = checkSet(s);}

	public String getFunctionName() {return functionName;}
	public void setFunctionName(String s) {this.functionName = s;}

}