<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="pre1004_1_subreport2" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="73b3bd05-a07d-40c9-a209-6f12660e20f2">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="approveString" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="15" splitType="Stretch">
			<staticText>
				<reportElement uuid="e4e310c3-0489-4b4c-a491-f39f95aaeb57" x="0" y="0" width="555" height="15"/>
				<box leftPadding="2">
					<topPen lineWidth="1.75"/>
					<leftPen lineWidth="1.75"/>
					<rightPen lineWidth="1.75"/>
				</box>
				<textElement textAlignment="Left">
					<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[審查結果]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement uuid="73cc7429-c8ab-4b48-ba5a-85457bbd493c" stretchType="RelativeToTallestObject" x="0" y="0" width="555" height="15"/>
				<box leftPadding="2" bottomPadding="3">
					<leftPen lineWidth="1.75"/>
					<bottomPen lineWidth="1.75"/>
					<rightPen lineWidth="1.75"/>
				</box>
				<textElement>
					<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{approveString}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="1" splitType="Stretch"/>
	</summary>
</jasperReport>
