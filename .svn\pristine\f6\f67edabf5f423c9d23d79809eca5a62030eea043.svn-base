package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.LoginLog;

public class LoginLogDao  extends BaseDaoJdbc implements RowMapper<LoginLog>{

	private static final String SQL_query = "SELECT * FROM LOGIN_LOG ";
	public List<LoginLog> queryByCondition(String loginId, String loginName, String startDate, String endDate){
		SQLJob sqljob = new SQLJob(SQL_query);
		if(!"".equals(Common.get(loginId))){
			sqljob.appendSQLCondition(" LOING_ID_NO = ? ");
			sqljob.addParameter(loginId);
		}
		if(!"".equals(Common.get(loginName))){
			sqljob.appendSQLCondition(" LOING_NAME = ? ");
			sqljob.addParameter(loginName);
		}
		if(!"".equals(Common.get(startDate))){
			sqljob.appendSQLCondition(" LOGIN_DATE >= ? ");
			sqljob.addParameter(startDate);
		}
		if(!"".equals(Common.get(endDate))){
			sqljob.appendSQLCondition(" LOGIN_DATE <= ? ");
			sqljob.addParameter(endDate);
		}
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<LoginLog>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(),  this);
	}
	
	private static final String SQL_insert = "INSERT INTO LOGIN_LOG(LOING_ID_NO, LOING_NAME, LOGIN_DATE, LOGIN_TIME, LOGIN_IP, LOGIN_STATUS) "
			+ "VALUES(?, ?, ?, ?, ?, ?)";
	public void insert(LoginLog obj){
		if(obj != null){
			SQLJob sqljob = new SQLJob(SQL_insert);
			sqljob.addParameter(obj.getLoginId());
			sqljob.addParameter(obj.getLoginName());
			sqljob.addParameter(obj.getLoginDate());
			sqljob.addParameter(obj.getLoginTime());
			sqljob.addParameter(obj.getLoginIp());
			sqljob.addParameter(obj.getLoginStatus());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
					,new int[]{
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR
				});
		}
	}
	
	@Override
	public LoginLog mapRow(ResultSet rs, int arg1) throws SQLException {
		LoginLog obj = null;
		if(null!=rs) {
			obj = new LoginLog();
			obj.setId(rs.getInt("ID"));
			obj.setLoginId(rs.getString("LOING_ID_NO"));
			obj.setLoginName(rs.getString("LOING_NAME"));
			obj.setLoginDate(rs.getString("LOGIN_DATE"));
			obj.setLoginTime(rs.getString("LOGIN_TIME"));
			obj.setLoginIp(rs.getString("LOGIN_IP"));
			obj.setLoginStatus(rs.getString("LOGIN_STATUS"));
		}
		return obj;
	}
}
