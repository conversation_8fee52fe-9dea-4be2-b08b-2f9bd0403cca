package com.kangdainfo.tcfi.model.osss.bo;

import com.kangdainfo.persistence.BaseModel;


/**
 * 變更案件資訊檔(OSSM_ORG_CHANGE)
 *
 */
public class OssmOrgChange extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子案號 */
	private String telixNo;
	/** 申請人身分 */
	private String applicantType;
	/** 法人公司名稱 */
	private String corpName;
	/** 法人公司統編 */
	private String corpBanNo;
	/** 法人代表人姓名 */
	private String corpApplyName;
	/** 法人代表人身分證字號 */
	private String corpApplyId;
	/** 公司地址縣市鄉鎮代碼 */
	private String cmpyAreaCode;
	/** 申請人地址地址-鄰 */
	private String cmpyNeiborCode;
	/** 申請人地址 */
	private String cmpyAddr;
	/** 申請人郵遞區號 */
	private String cmpyZipCode;
	/** 申請人全地址 */
	private String cmpyAddrComb;
	/** 登記機關 */
	private String regUnitName;
	/** 登記機關代碼 */
	private String regUnitCode;
	/** 國外匯款使用英文名稱 */
	private String cmpyRemitEname;

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getApplicantType() {
		return applicantType;
	}
	public void setApplicantType(String applicantType) {
		this.applicantType = applicantType;
	}
	public String getCorpName() {
		return corpName;
	}
	public void setCorpName(String corpName) {
		this.corpName = corpName;
	}
	public String getCorpBanNo() {
		return corpBanNo;
	}
	public void setCorpBanNo(String corpBanNo) {
		this.corpBanNo = corpBanNo;
	}
	public String getCorpApplyName() {
		return corpApplyName;
	}
	public void setCorpApplyName(String corpApplyName) {
		this.corpApplyName = corpApplyName;
	}
	public String getCorpApplyId() {
		return corpApplyId;
	}
	public void setCorpApplyId(String corpApplyId) {
		this.corpApplyId = corpApplyId;
	}
	public String getCmpyAreaCode() {
		return cmpyAreaCode;
	}
	public void setCmpyAreaCode(String cmpyAreaCode) {
		this.cmpyAreaCode = cmpyAreaCode;
	}
	public String getCmpyNeiborCode() {
		return cmpyNeiborCode;
	}
	public void setCmpyNeiborCode(String cmpyNeiborCode) {
		this.cmpyNeiborCode = cmpyNeiborCode;
	}
	public String getCmpyAddr() {
		return cmpyAddr;
	}
	public void setCmpyAddr(String cmpyAddr) {
		this.cmpyAddr = cmpyAddr;
	}
	public String getCmpyZipCode() {
		return cmpyZipCode;
	}
	public void setCmpyZipCode(String cmpyZipCode) {
		this.cmpyZipCode = cmpyZipCode;
	}
	public String getCmpyAddrComb() {
		return cmpyAddrComb;
	}
	public void setCmpyAddrComb(String cmpyAddrComb) {
		this.cmpyAddrComb = cmpyAddrComb;
	}
	public String getRegUnitName() {
		return regUnitName;
	}
	public void setRegUnitName(String regUnitName) {
		this.regUnitName = regUnitName;
	}
	public String getRegUnitCode() {
		return regUnitCode;
	}
	public void setRegUnitCode(String regUnitCode) {
		this.regUnitCode = regUnitCode;
	}
	public String getCmpyRemitEname() {
		return cmpyRemitEname;
	}
	public void setCmpyRemitEname(String cmpyRemitEname) {
		this.cmpyRemitEname = cmpyRemitEname;
	}
}
