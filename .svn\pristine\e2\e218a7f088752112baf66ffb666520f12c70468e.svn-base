package com.kangdainfo.tcfi.lucene.service.impl;

import java.io.IOException;
import java.util.Calendar;
import java.util.List;

import org.apache.log4j.Logger;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig.OpenMode;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.lucene.bo.IndexData;
import com.kangdainfo.tcfi.lucene.dao.IndexDataDao;
import com.kangdainfo.tcfi.lucene.service.IndexCreateService;
import com.kangdainfo.tcfi.lucene.util.IndexDataConverter;
import com.kangdainfo.tcfi.lucene.util.LuceneManager;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.IndexLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.io.CommonFileUtils;
import com.kangdainfo.util.lang.CommonStringUtils;

public class IndexCreateServiceImpl implements IndexCreateService {
	private Logger logger = Logger.getLogger(this.getClass());

	public IndexLog doStartBuildIndex() {
		IndexLog indexLog = new IndexLog();
		indexLog.setWsId(PrefixConstants.JOB_WS10000);
		indexLog.setExecuteDate(Datetime.getYYYMMDDHHMISS());
		indexLog.setCreateDate(Datetime.getYYYMMDDHHMISS());
		indexLog.setStartTime(Datetime.getYYYMMDDHHMISS());
		indexLog.setCreateUser(PrefixConstants.SYS);
		indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_1);//執行中
		indexLog.setParam1(Datetime.getYYYMMDD());
		indexLogDao.insert(indexLog);
		return indexLogDao.query(indexLog);
	}

	public IndexLog doBuildIndex(IndexLog indexLog) {
		if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start]");
		long start = Calendar.getInstance().getTimeInMillis();
		IndexWriter writer = null;
		try{
			//檢查-索引路徑設定
			SystemCode setting = null;
			if(System.getProperty("os.name").toLowerCase().indexOf("win") >= 0)
				setting = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathWindows");
			else 
				setting = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathCentOS");
			if(null==setting) throw new Exception("系統路徑未設定!");
			//檢查-同音同義字設定
			List<Cedbc058> c058s = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058Codes();
			if(null==c058s || c058s.isEmpty()) throw new Exception("無法讀取CEDBC058");
			//今天建置的路徑
			String indexPath = CommonStringUtils.append(setting.getCodeParam1(),"_", Datetime.getYYYMMDD());
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][New Path:"+indexPath+"]");
			CommonFileUtils.forceDeleteOnExit(new java.io.File(indexPath));
			CommonFileUtils.forceMkdir(new java.io.File(indexPath));
			//get writer
			writer = LuceneManager.getIndexWriter(indexPath, OpenMode.CREATE);
			//build 公司
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start][INDEX_TYPE:1]");
			buildIndex(writer, PrefixConstants.INDEX_TYPE_1);
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End][INDEX_TYPE:1]");
			//build 已收文
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start][INDEX_TYPE:2]");
			buildIndex(writer, PrefixConstants.INDEX_TYPE_2);
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End][INDEX_TYPE:2]");
			//build 預查
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start][INDEX_TYPE:3]");
			buildIndex(writer, PrefixConstants.INDEX_TYPE_3);
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End][INDEX_TYPE:3]");
			//build 預查否准
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start][INDEX_TYPE:4]");
			buildIndex(writer, PrefixConstants.INDEX_TYPE_4);
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End][INDEX_TYPE:4]");
			//build 有限合夥
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Start][INDEX_TYPE:5]");
			buildIndex(writer, PrefixConstants.INDEX_TYPE_5);
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End][INDEX_TYPE:5]");
			//執行成功
			setting.setCodeParam2(indexPath);
			setting.setModIdNo(PrefixConstants.SYS);
			setting.setModDate(Datetime.getYYYMMDD());
			setting.setModTime(Datetime.getHHMMSS());
			systemCodeDao.update(setting);
			//write indexLog
			indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_2);//執行成功
			indexLog.setRemark(CommonStringUtils.limitLength(CommonStringUtils.append("Success, indexPath:", indexPath),1000));
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Success]");
		} catch (Exception e) {
			e.printStackTrace();
			indexLog.setStatus(PrefixConstants.INDEX_LOG_STATUS_3);//執行失敗
			indexLog.setRemark(CommonStringUtils.limitLength(CommonStringUtils.append("Failure, errorMsg:", e.getMessage() ),1000));
			if(logger.isInfoEnabled()) logger.info("[Rebuild Index][Failure][Exception:"+ e.getMessage() + "]");
		} finally {
			//close writer
			try {
				if(writer != null)
					writer.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		if(logger.isInfoEnabled()) logger.info("[Rebuild Index][End]");
		long end = Calendar.getInstance().getTimeInMillis();
		long diff = end - start;
		long h = diff / ( 60* 60 * 1000);
		long m = (diff - (h * 60* 60 * 1000)) / ( 60 * 1000);
		long s = (diff - (h * 60* 60 * 1000) - (m * 60 * 1000)) / (1000);
		if(logger.isInfoEnabled()) logger.info("[time]"+h+" hour "+m+" minute "+s+" second ");
		return indexLog;
	}

	public void doEndBuildIndex(IndexLog indexLog) {
		indexLog.setFinishTime(Datetime.getYYYMMDDHHMISS());
		indexLogDao.update(indexLog);
	}
	
	private void buildIndex(IndexWriter writer, String kind) throws Exception {
		List<IndexData> objList = indexDataDao.query(kind, null);
		if (objList != null && objList.size() > 0) {
			Document doc = null;
			String id;
			for(IndexData o : objList) {
				id = o.getId();
				if (id != null && !"".equals(id)){
					doc = IndexDataConverter.convertMapToDoc(o);
					if(null!=doc) {
						writer.addDocument(doc);
					}
				}
				id = null;
				doc = null;
			}
		}
	}

	private IndexDataDao indexDataDao;
	private SystemCodeDao systemCodeDao;
	private IndexLogDao indexLogDao;

	public IndexDataDao getIndexDataDao() {return indexDataDao;}
	public void setIndexDataDao(IndexDataDao dao) {this.indexDataDao = dao;}

	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}

	public IndexLogDao getIndexLogDao() {return indexLogDao;}
	public void setIndexLogDao(IndexLogDao dao) {this.indexLogDao = dao;}

}