<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4022"%>
<%
//2024/03/28 新增
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String dateStart = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateStart")));
String dateEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateEnd")));
String timeStart = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("timeStart")));
String timeEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("timeEnd")));
String type = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("type")));

try {
	if ( ( dateStart != null && !"".equals(dateStart) && dateEnd != null && !"".equals(dateEnd) )  &&// 時間部分僅用前端審核
	     ( type != null && !"".equals(type) ) ) 
	{
		String checkResult = PRE4022.checkForJsp(dateStart, dateEnd, timeStart, timeEnd, type);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>