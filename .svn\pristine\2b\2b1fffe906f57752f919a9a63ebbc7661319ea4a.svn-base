package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class PRE8016 extends SuperBean{
	
	private String tabId;
	private String season1;
	private String season2;
	private String season3;
	private String season4;
	private String season1Close;
	private String season2Close;
	private String season3Close;
	private String season4Close;
	
	public String getTabId() {return checkGet(tabId);}
	public void setTabId(String s) {this.tabId = checkSet(s);}
	public String getSeason1() {return checkGet(season1);}
	public void setSeason1(String s) {this.season1 = checkSet(s);}
	public String getSeason2() {return checkGet(season2);}
	public void setSeason2(String s) {this.season2 = checkSet(s);}
	public String getSeason3() {return checkGet(season3);}
	public void setSeason3(String s) {this.season3 = checkSet(s);}
	public String getSeason4() {return checkGet(season4);}
	public void setSeason4(String s) {this.season4 = checkSet(s);}
	public String getSeason1Close() {return checkGet(season1Close);}
	public void setSeason1Close(String s) {this.season1Close = checkSet(s);}
	public String getSeason2Close() {return checkGet(season2Close);}
	public void setSeason2Close(String s) {this.season2Close = checkSet(s);}
	public String getSeason3Close() {return checkGet(season3Close);}
	public void setSeason3Close(String s) {this.season3Close = checkSet(s);}
	public String getSeason4Close() {return checkGet(season4Close);}
	public void setSeason4Close(String s) {this.season4Close = checkSet(s);}
	
	private String prefixNoReceive;
	private String prefixNoClose;
	private String prefixNoDifferent;
	private String[] prefixNoCount;
	
	public String getPrefixNoReceive() {return checkGet(prefixNoReceive);}
	public void setPrefixNoReceive(String s) {this.prefixNoReceive = checkSet(s);}
	public String getPrefixNoClose() {return checkGet(prefixNoClose);}
	public void setPrefixNoClose(String s) {this.prefixNoClose = checkSet(s);}
	public String getPrefixNoDifferent() {return checkGet(prefixNoDifferent);}
	public void setPrefixNoDifferent(String s) {this.prefixNoDifferent = checkSet(s);}
	public String[] getPrefixNoCount() {return prefixNoCount;}
	public void setPrefixNoCount(String[] prefixNoCount) {this.prefixNoCount = prefixNoCount;}
	
	@Override
	public Object doQueryOne() throws Exception {
		PRE8016 obj = this;
		List<Map<String,Object>> dataList = null;
		Map<String,Object> data = null;
		if("1".equals(Common.get(getTabId()))){
			//收文量統計
			dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(getSql(getTabId()));
			if(dataList != null && dataList.size() > 0){
				data = dataList.get(0);
				obj.setSeason1(Common.get(data.get("season1")));
				obj.setSeason2(Common.get(data.get("season2")));
				obj.setSeason3(Common.get(data.get("season3")));
				obj.setSeason4(Common.get(data.get("season4")));
				obj.setSeason1Close(Common.get(data.get("season1_close")));
				obj.setSeason2Close(Common.get(data.get("season2_close")));
				obj.setSeason3Close(Common.get(data.get("season3_close")));
				obj.setSeason4Close(Common.get(data.get("season4_close")));
			}else{
				
			}
		}else if("2".equals(Common.get(getTabId()))){
			//案件分派及辦理進度
			prefixNoCount = new String[5];
			dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(getSql(getTabId()));
			if(dataList != null && dataList.size() > 0){
				obj.setPrefixNoReceive(Common.get(dataList.get(0).get("prefix_no")));
				obj.setPrefixNoClose(Common.get(dataList.get(1).get("prefix_no")));
				//int i = Integer.valueOf(obj.getPrefixNoReceive()) - Integer.valueOf(obj.getPrefixNoClose());
				//obj.setPrefixNoDifferent(Common.get(i));
				int receiveI = Common.getInt(obj.getPrefixNoReceive());
				int closeI = Common.getInt(obj.getPrefixNoClose());
				if(receiveI > 0 && closeI > 0)
					obj.setPrefixNoDifferent(Common.get(receiveI-closeI));
			}
			
			dataList.clear();
			dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(getSql("3"));
			if(dataList != null && dataList.size() > 0){
				for(int i=0;i<prefixNoCount.length; i++)	
					prefixNoCount[i] = Common.get(dataList.get(0).get("count"+Common.get(i)));
			}
		}
		return obj;
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}
	
	public String getBuildPrefixNotData(String[] datas, String... code) {
		StringBuffer sb = new StringBuffer("");
		SystemCode obj = null;
		String color = "";
		for(int i=0; i<datas.length;i++){
			color = "";
			obj = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, code[i]);
			
			sb.append("<tr>");
			sb.append("<td class=\"td_form_white\" width=\"30%\">");
			if(obj != null){
				sb.append(Common.get(obj.getCodeName()));
			}
			sb.append("</td>");
			sb.append("<td class=\"td_form_white\" width=\"10%\">");
			
			sb.append("<font color=");
			if(obj != null){
				if(!"".equals(Common.get(obj.getCodeParam1())) && Integer.valueOf(datas[i]) < Integer.valueOf(obj.getCodeParam1()))
					color = "green";
				else if(!"".equals(Common.get(obj.getCodeParam3())) && Integer.valueOf(datas[i]) > Integer.valueOf(obj.getCodeParam3()))
					color = "red";
				else if(!"".equals(Common.get(obj.getCodeParam2())))
					color = "yellow";
			}
			sb.append("'").append("".equals(color)?"black":color).append("'>●</font>");
			sb.append(datas[i]);
			
			sb.append("</td>");
			sb.append("<td class=\"td_form_white\" width=\"60%\">");
			
			if(obj != null){
				sb.append(Common.get(obj.getRemark().replace("@CODEPARAM1", obj.getCodeParam1()).replace("@CODEPARAM2", obj.getCodeParam2()).replace("@CODEPARAM3", obj.getCodeParam3())));
			}
			sb.append("</td>").append("</tr>");
		}
		return sb.toString();
	}
	
	
	public SQLJob getSql(String typeId){
		SQLJob sqljob = new SQLJob();
		String YYY = Datetime.getYYY();
		String prefixNoStart = YYY + "000000";
		String prefixNoEnd = YYY + "999999";
		if("1".equals(typeId)){
			sqljob.appendSQL("select ");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('01','02','03') then 1 else 0 end) season1,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('01','02','03') and close_date is null then 1 else 0 end) season1_close,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('04','05','06') then 1 else 0 end) season2,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('04','05','06') and close_date is null then 1 else 0 end) season2_close,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('07','08','09') then 1 else 0 end) season3,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('07','08','09') and close_date is null then 1 else 0 end) season3_close,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('10','11','12') then 1 else 0 end) season4,");
			sqljob.appendSQL("		sum(case when substr(receive_date, 4, 2) in ('10','11','12') and close_date is null then 1 else 0 end) season4_close ");
			sqljob.appendSQL("from cedb1000 ");
			sqljob.appendSQL("where prefix_no between ");
			sqljob.appendSQL(Common.sqlChar(prefixNoStart));
			sqljob.appendSQL(" and ");
			sqljob.appendSQL(Common.sqlChar(prefixNoEnd));
		}else if("2".equals(typeId)){
			sqljob.appendSQL("select nvl(max(prefix_no),'0') as prefix_no ");
			sqljob.appendSQL("from cedb1000 where receive_date is not null ");
			sqljob.appendSQL(" and prefix_no >= ");
			sqljob.appendSQL(Common.sqlChar(prefixNoStart));
			//sqljob.appendSQL(" and ");
			//sqljob.appendSQL(Common.sqlChar(prefixNoEnd));
			sqljob.appendSQL(" union all ");
			sqljob.appendSQL("SELECT MAX(PREFIX_NO) AS PREFIX_NO FROM CEDB1021");
		}else if("3".equals(typeId)){
			sqljob.appendSQL("select ");
			sqljob.appendSQL("		sum(case when receive_date is not null and rcv_check = 'N' then 1 else 0 end) as count0,");
			sqljob.appendSQL("		sum(case when assign_date is null and id_no is null and rcv_check = 'Y' then 1 else 0 end) as count1,");
			sqljob.appendSQL("		sum(case when assign_date is not null and approve_result = 'A' then 1 else 0 end) as count2,");
			sqljob.appendSQL("		sum(case when approve_date is not null and close_date is null then 1 else 0 end) as count3,");
			sqljob.appendSQL("		sum(case when extend_mark = 'Y' and close_date is null then 1 else 0 end) as count4 ");
			sqljob.appendSQL("from cedb1000 ");
			sqljob.appendSQL("where prefix_no between ");
			sqljob.appendSQL(Common.sqlChar(prefixNoStart));
			sqljob.appendSQL(" and ");
			sqljob.appendSQL(Common.sqlChar(prefixNoEnd));
		}
		if(logger.isInfoEnabled()) logger.info(sqljob);	
		return sqljob;
	}
}
