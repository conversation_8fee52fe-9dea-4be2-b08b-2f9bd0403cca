package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

/**
 * 排程(PRE0007)
 * 重載代碼資料
 */
public class Pre0007QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		com.kangdainfo.ServiceGetter.getInstance().getCedbc000CodeLoader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getCedbc058CodeLoader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getFunctionMenuLoader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode01Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode02Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode03Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode04Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode05Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode06Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode07Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode08Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode09Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode10Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode11Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode12Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode13Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemCode14Loader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getSystemNewsLoader().reload();
		com.kangdainfo.ServiceGetter.getInstance().getLmsdCodemappingOrgLoader().reload(); //2024/04/16新增
		com.kangdainfo.ServiceGetter.getInstance().getLmsdCodemappingStatLoader().reload(); //2024/04/17新增
		com.kangdainfo.ServiceGetter.getInstance().getLmsdRegUnitLoader().reload(); //2024/04/17新增
	}

}