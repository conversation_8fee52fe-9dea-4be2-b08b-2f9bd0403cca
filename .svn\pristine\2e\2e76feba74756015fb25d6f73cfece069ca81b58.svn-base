<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.math.BigDecimal" %>
<%@ page import="com.kangdainfo.common.util.SQLJob" %>
<%@ page import="com.kangdainfo.ServiceGetter" %>
<%
//查詢發文待登打件數
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL(" SELECT COUNT(1) AS COUNT");
	sqljob.appendSQL(" FROM CEDB1000 A");
	sqljob.appendSQL(" WHERE A.APPROVE_RESULT <> 'A'");//已審核(APPROVE_RESULT IN 'Y','N')
	sqljob.appendSQL(" AND (A.CLOSE_DATE IS NULL OR A.CLOSE_DATE='')");//未結案
	sqljob.appendSQL(" AND A.APPROVE_DATE IS NOT NULL");//已審核
	sqljob.appendSQL(" AND A.PREFIX_STATUS NOT IN ('A','E')");//不為撤回(A)或撤回退費(E)
	sqljob.appendSQL(" AND 0=(SELECT COUNT(1) FROM CEDB1010 WHERE PREFIX_NO=A.PREFIX_NO AND PROCESS_STATUS='7')");//流程紀錄不含發文登打完成(7)
	sqljob.appendSQL(" ORDER BY A.PREFIX_NO");
	//System.out.println(sqljob);
	List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	if (null!=datas && !datas.isEmpty()) {
		out.write(com.kangdainfo.common.util.Common.get(datas.get(0).get("COUNT")));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>