package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.icms.bo.CsmmMoeaicApprove;
import com.kangdainfo.tcfi.model.icms.dao.CsmlCmpyTranMoeaicDao;
import com.kangdainfo.tcfi.model.icms.dao.CsmmMoeaicApproveDao;
import com.kangdainfo.tcfi.service.MoeaicApproveService;

public class MoeaicApproveServiceImpl implements MoeaicApproveService {

	public Logger logger = Logger.getLogger(getClass());

	public void notifyMoeaic(String prefixNo) {
		if(logger.isInfoEnabled()) logger.info("[notifyMoeaic][prefixNo:"+prefixNo+"]");
		List<CsmmMoeaicApprove> approves = csmmMoeaicApproveDao.findByPrefixNo(prefixNo);
		//投審會有核准案件的資料才處理
		if(null!=approves && !approves.isEmpty()) {
			try {
				Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
				if(null!=cedb1000) {
					csmlCmpyTranMoeaicDao.insert(cedb1000.getPrefixNo(), cedb1000.getBanNo());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private CsmmMoeaicApproveDao csmmMoeaicApproveDao;
	private CsmlCmpyTranMoeaicDao csmlCmpyTranMoeaicDao;
	private Cedb1000Dao cedb1000Dao;

	public CsmmMoeaicApproveDao getCsmmMoeaicApproveDao() {return csmmMoeaicApproveDao;}
	public void setCsmmMoeaicApproveDao(CsmmMoeaicApproveDao dao) {this.csmmMoeaicApproveDao = dao;}
	public CsmlCmpyTranMoeaicDao getCsmlCmpyTranMoeaicDao() {return csmlCmpyTranMoeaicDao;}
	public void setCsmlCmpyTranMoeaicDao(CsmlCmpyTranMoeaicDao dao) {this.csmlCmpyTranMoeaicDao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

}