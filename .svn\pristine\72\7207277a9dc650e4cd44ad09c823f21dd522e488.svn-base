<!DOCTYPE html>
<!--
程式目的：群組資料及權限維護
程式代號：PRE9002
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9002">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9002" />
</jsp:include>
<%
	obj.setQueryAllFlag("true");	
	objList = obj.queryAll();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}
function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	form1.action = "pre9002_auth.jsp";
	beforeSubmit();
	form1.submit();
}

function init() {}
</script>
</head>

<body topmargin="0" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9002'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td>
<table cellpadding=0 cellspacing=0 valign="top">
	<tr>
		<td nowrap ID=t1 CLASS="tab_border1" width="100" height="25">身分別資料</td>
		<td nowrap ID=t2 CLASS="tab_border2" width="100"><a href="#" onClick="alert('請點選一筆資料!!');">身分別權限</a></td>
	</tr>
	<tr>
		<td nowrap class="tab_line1"></td>
		<td nowrap class="tab_line2"></td>	
	</tr>
</table>
</td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" width="15%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">代碼</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">角色權限</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true,false};
	boolean displayArray[] = {true,true};
	String[] alignArray = {"center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

<tr><td nowrap class="bgToolbar" style="text-align:center">
	<input type="hidden" name="id" value="<%=obj.getId()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td></tr>

</table>
</form>
</body>
</html>