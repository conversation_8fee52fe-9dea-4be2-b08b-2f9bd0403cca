package com.kangdainfo.tcfi.view.pre;
/*
程式目的：承辦案件列表
程式代號：pre4004
撰寫日期：103.06.12
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.util.report.JasperReportMaker;


public class PRE4004 extends SuperBean {

	private String q_idNo ;
	private String q_date ;
	
	private String prefixNo ;
	private String applyName ;
	private String approveResult ;
	private String changeType;
	private String companyName ;
	private String companyStus;
	private String reserveDate ;
	//private String assignDate ;
	//private String staffName ;
	private String resultAssignDate;
	private String resultStaffName;
	private String[] rcvNoListWillChange ; 
	public String[] getRcvNoListWillChange() {return rcvNoListWillChange;}
	public void setRcvNoListWillChange(String s[]) {rcvNoListWillChange = s;}
	public String getResultStaffName() {return checkGet(resultStaffName);}
	public void setResultStaffName(String s) {this.resultStaffName = checkSet(s);}
	public String getResultAssignDate() {return checkGet(resultAssignDate);}
	public void setResultAssignDate(String s) {this.resultAssignDate = checkSet(s);}
	public String getQ_idNo() {return checkGet(q_idNo);}
	public void setQ_idNo(String s) {q_idNo = checkSet(s);}
	public String getQ_date() {return checkGet(q_date);}
	public void setQ_date(String s) {q_date = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {approveResult = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	public String getReserveDate() {return checkGet(reserveDate);}
	public void setReserveDate(String s) {reserveDate = checkSet(s);}
	public String getChangeType() {return checkGet(changeType);}
	public void setChangeType(String s) {changeType = checkSet(s);}
	public String getCompanyStus() {return checkGet(companyStus);}
	public void setCompanyStus(String s) {companyStus = checkSet(s);}
	//public String getAssignDate() {return checkGet(assignDate);}
	//public void setAssignDate(String s) {assignDate = checkSet(s);}
	//public String getStaffName() {return checkGet(staffName);}
	//public void setStaffName(String s) {staffName = checkSet(s);}
	
	public static SQLJob doAppendSqljob( String date, String idNo ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL( "select" ) ;
		sqljob.appendSQL( "a.prefix_no" ) ;
		sqljob.appendSQL( ",a.apply_name" ) ;
		sqljob.appendSQL( ",a.approve_result" ) ;
		sqljob.appendSQL( ",a.company_name" ) ;
		sqljob.appendSQL( ",a.reserve_date" ) ;
		sqljob.appendSQL( ",a.assign_date" ) ;
		sqljob.appendSQL( ",a.staff_name" ) ;
		sqljob.appendSQL( ",a.ban_no" ) ;
		sqljob.appendSQL( ",b.change_type" ) ;
		sqljob.appendSQL( "from eicm.cedb1000 a, eicm.cedb1023 b" ) ;
		sqljob.appendSQL( "where a.prefix_no = b.prefix_no" ) ;
		sqljob.appendSQL( "and assign_date = ?" ) ;
		sqljob.addParameter(date);
		if ( !"".equals(idNo) ) {
			sqljob.appendSQL( "and id_no = ?" ) ;
			sqljob.addParameter(idNo);
		}
        return sqljob ;
	}  // doAppendSqljob()
	
		
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		return null ;
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
	    try {
	    	System.out.println(Datetime.getHHMMSS());
	    	/*
	    	 * 分文日期改為前端檢核  
	    	 */
	    	if(logger.isInfoEnabled()) logger.info(doAppendSqljob(getQ_date(), getQ_idNo()));
	    	List<Map<String, Object>> pre4004List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_date(), getQ_idNo() ));

	    	if ( pre4004List == null || pre4004List.size() <= 0 ) {	
	    		setErrorMsg( "查無資料，請變更查詢條件" ) ;
	    		throw new MoeaException( "查無資料，請變更查詢條件" ) ;
	    	} // end if
	  
	    	ArrayList<String[]> dataList = new ArrayList<String[]>() ;
	    	int i = 0 ;
	    	String[] rowArray ;
	    	System.out.println(Datetime.getHHMMSS());
	    	while ( i < pre4004List.size() ) {
	    		// Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo(Common.get(pre4004List.get(i).get("prefix_no")));
	    		Cedb2000 cedb2000 = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(Common.get(pre4004List.get(i).get("ban_no")));
	    		rowArray = new String[9] ; 
	    		rowArray[0] = Common.get( pre4004List.get(i).get("prefix_no") )  ;
	    		rowArray[1] = Common.get( pre4004List.get(i).get("apply_name") )  ;
	    		rowArray[2] = ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(Common.get( pre4004List.get(i).get("change_type") ));
	    		rowArray[3] = ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get( pre4004List.get(i).get("approve_result")))  ;
	    		rowArray[4] = Common.get( pre4004List.get(i).get("company_name") )  ;
	    		rowArray[5] = Common.get( pre4004List.get(i).get("reserve_date") )  ;
	    		if ( cedb2000 == null)
	    			rowArray[6] = "";
	    		else
	    			rowArray[6] = ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(Common.get( cedb2000.getStatusCode() ));
	    		rowArray[7] = Common.get( pre4004List.get(i).get("assign_date") )  ;
	    		rowArray[8] = Common.get( pre4004List.get(i).get("staff_name") )  ;
	    		i++;
	    		dataList.add(rowArray) ;
	    	} // end while
	    	System.out.println(Datetime.getHHMMSS());
	    	return dataList ;
	    } // try
	    catch ( MoeaException e ) {
	    	e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
	    } // catch
    } // doQueryAll()
	
	public String DateFormat( String inputDate ) {
		  String tempDate = "" ;
		  String year = inputDate.substring(0, 3) ;
		  String month = inputDate.substring(4, 6) ;
		  String day = inputDate.substring(7) ;
		  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
		  return tempDate ;
	  } // DateFormat()
	  
	  public String TimeFormat( String inputTime ) {
		  String tempTime = "" ;
		  String hour = inputTime.substring(0, 2) ;
		  String minute = inputTime.substring(2, 4) ;
		  String second = inputTime.substring(4) ;
		  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
		  return tempTime ;
	  } // TimeFormat()
	
	public File doPrintPdf() throws Exception{
		try {
			System.out.println(Datetime.getHHMMSS());
			File report = null ;
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4004.jasper");
	        Map<String, Object> parameters = new HashMap<String,Object>();
	        	      
	        parameters.put("printDate", DateFormat( Datetime.getRocDateFromYYYYMMDD(Datetime.getCurrentDate("yyyy/MM/dd")) ));//列印時間
		    parameters.put("printTime", TimeFormat( Datetime.getHHMMSS()));//列印時間
		    parameters.put("assignDate", getQ_date());
			/*
			 * 分文日期改為前端檢核  
		     */
			List<Map<String, Object>> pre4004List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(getQ_date(), getQ_idNo() ));

			if ( pre4004List == null || pre4004List.size() <= 0 ) {
			    setErrorMsg( "查無資料，請變更查詢條件" ) ;
				throw new MoeaException( "查無資料，請變更查詢條件" ) ;
			} // end if
			
			parameters.put("staffName" , Common.get(pre4004List.get(0).get("staff_name")));//列印時間
			ArrayList<PRE4004> dataList = new ArrayList<PRE4004>() ;
			PRE4004 data ;
			int i = 0;
			while ( i < pre4004List.size() ) {
				Cedb1023 cedb1023 = ServiceGetter.getInstance().getPrefixService().getCedb1023ByPrefixNo(Common.get(pre4004List.get(i).get("prefix_no")));
	    		Cedb2000 cedb2000 = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(Common.get(pre4004List.get(i).get("ban_no")));
				data = new PRE4004() ;
				data.setPrefixNo( Common.get( pre4004List.get(i).get("prefix_no") ) ) ;		
				data.setApplyName( Common.get( pre4004List.get(i).get("apply_name") ) ) ;
				data.setApproveResult( ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get( pre4004List.get(i).get("approve_result") )) ) ;
				data.setChangeType( ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(Common.get( cedb1023.getChangeType() )) ) ;
				data.setCompanyName( Common.get( pre4004List.get(i).get("company_name") ) ) ;
				data.setReserveDate( Common.get( pre4004List.get(i).get("reserve_date") ) ) ;
				if ( cedb2000 == null )
					data.setCompanyStus("");
				else
					data.setCompanyStus(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(Common.get( cedb2000.getStatusCode() )));
				data.setResultAssignDate( Common.get( pre4004List.get(i).get("assign_date") ) ) ;
				data.setResultStaffName( Common.get( pre4004List.get(i).get("staff_name") ) ) ;

				dataList.add(data) ;
				i++ ;
			} // end while
			
			report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
			System.out.println(Datetime.getHHMMSS());
			return report ;
		} //try
		catch ( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		} // catch
		catch( Exception e ) {
			e.printStackTrace();
			setStateInsertError();
			if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表產製失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null ;
		} // catch()
	} // doPrintPdf()
	
	public static String checkForjsp( String date, String idNo) {
		  List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(date, idNo));
		  System.out.println(rs.size());
		  if ( rs == null || rs.size() <= 0 )
			  return "查無資料，請變更查詢條件！";
		  else
			  return "ok";
	} 
	
} // PRE8003()