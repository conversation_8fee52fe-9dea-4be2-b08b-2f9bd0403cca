package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司登記申登機關代碼檔(CSMD_REGUNIT)
 *
 */
public class CsmdRegunit extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 申登機關代碼 */
	private String regUnitCode;
	/** 申登機關名稱 */
	private String regUnitName;
	/** 機關代碼 */
	private String unitCode;
	/** 啟用 */
	private String enable;
	/** 資料異動日期 */
	private Date updateDate;
	/** 資料異動人員 */
	private String updateUser;
	/** 管制項目代碼 */
	private String ctrlItem;
	/** 機關OID (一站式介接有使用) */
	private String oid;

	public String getRegUnitCode() {return regUnitCode;}
	public void setRegUnitCode(String regUnitCode) {this.regUnitCode = regUnitCode;}
	public String getRegUnitName() {return regUnitName;}
	public void setRegUnitName(String regUnitName) {this.regUnitName = regUnitName;}
	public String getUnitCode() {return unitCode;}
	public void setUnitCode(String unitCode) {this.unitCode = unitCode;}
	public String getEnable() {return enable;}
	public void setEnable(String enable) {this.enable = enable;}
	public Date getUpdateDate() {return updateDate;}
	public void setUpdateDate(Date updateDate) {this.updateDate = updateDate;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getCtrlItem() {return ctrlItem;}
	public void setCtrlItem(String ctrlItem) {this.ctrlItem = ctrlItem;}
	public String getOid() {return oid;}
	public void setOid(String oid) {this.oid = oid;}

}