<!DOCTYPE html>
<!--
程式目的：公司名稱管制備忘
程式代號：PRE8007
撰寫日期：103.03.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8007">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8007" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8007)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}
if ("true".equals(obj.getQueryAllFlag())) {
	objList = (java.util.ArrayList) obj.queryAll();
}
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkQuery();
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.rcvNo, "預查文號");  
	   alertStr += checkEmpty(form1.reserveDate, "保留日期");  
	   alertStr += checkEmpty(form1.companyName, "公司/有限合夥名稱");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function init(){
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<!--Query區============================================================-->
<div id="queryContainer" style="width:600px;height:200px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form">預查文號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_rcvNo" size="12" maxlength="12" value="<%=obj.getQ_rcvNo()%>">
        </td>
    </tr>
    <tr>
    	<td class="td_form">公司/有限合夥名稱：</td>
        <td class="td_form_white"> 
           <input class="field_Q cmex" type="text" name="q_companyName" size="50" maxlength="50" value="<%=obj.getQ_companyName()%>">
        </td>
    </tr> 
    <tr>
    	<td class="td_form">保留日期(起迄)：</td>
        <td class="td_form_white"> 
           <%=View.getPopCalendar("field_Q", "q_reserveDateS", obj.getQ_reserveDateS())%>
           ~
           <%=View.getPopCalendar("field_Q", "q_reserveDateE", obj.getQ_reserveDateE())%>
        </td>
    </tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8007'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<!--Toolbar區============================================================-->
<tr>
<td class="bgToolbar" style="text-align:left">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">	
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td>
</tr>

<!--Form區============================================================-->
<tr>
<td class="bg" >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
       <tr>
            <td nowrap class="td_form" width="20%"><font color="red">*</font>預查文號：</td>
            <td nowrap class="td_form_white" width="30%">
              <input class="field" type="text" name="rcvNo" size="12" maxlength="10" value="<%=obj.getRcvNo()%>">
            </td>  
            <td nowrap class="td_form" width="20%"><font color="red">*</font>保留日期：</td>
            <td nowrap class="td_form_white" width="30%">
              <%=View.getPopCalendar("field", "reserveDate", obj.getReserveDate())%>
            </td> 
        </tr>
        <tr>
            <td nowrap class="td_form" width="20%"><font color="red">*</font>公司/有限合夥名稱：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field cmex" type="text" name="companyName" size="50" maxlength="50" value="<%=obj.getCompanyName()%>">
            </td>  
        </tr>        
  </table>
  </div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td>
</tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w">NO.</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查文號</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">保留日期</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">公司名稱</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	  <%
	  boolean primaryArray[] = {true, false, false, false};
	  boolean displayArray[] = {false, true, true, true};
	  String[] alignArray = {"center", "center", "center", "left"};
	  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag()));
	  %>
	</tbody>
</table>
</div>
</td></tr>

</table>	
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>