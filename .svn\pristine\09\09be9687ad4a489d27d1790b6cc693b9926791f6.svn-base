package com.kangdainfo.tcfi.model.crmsmoea.bo;

import com.kangdainfo.persistence.BaseModel;
/**
 * 公司登記系統使用者
 * <AUTHOR>
 * 2024/05/17
 */
public class CsyssUser extends BaseModel {
	
	public CsyssUser() {
		super();
	}
	
	private static final long serialVersionUID = 1L;
	
	private String userId;
	private String userName;
	private String password;
	private String email;
	private String workzone;
	private String userstatus;
	private String resetpass;
	private String staffCode;
	private String birthday;
	private String orgizationalcode;
	private String title;
	private String postalcode;
	private String postaladdress;
	private String telephonenumber;
	private String mobile;
	private String pwddate;
	private String regUnitCode;
	
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getWorkzone() {
		return workzone;
	}
	public void setWorkzone(String workzone) {
		this.workzone = workzone;
	}
	public String getUserstatus() {
		return userstatus;
	}
	public void setUserstatus(String userstatus) {
		this.userstatus = userstatus;
	}
	public String getResetpass() {
		return resetpass;
	}
	public void setResetpass(String resetpass) {
		this.resetpass = resetpass;
	}
	public String getStaffCode() {
		return staffCode;
	}
	public void setStaffCode(String staffCode) {
		this.staffCode = staffCode;
	}
	public String getBirthday() {
		return birthday;
	}
	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}
	public String getOrgizationalcode() {
		return orgizationalcode;
	}
	public void setOrgizationalcode(String orgizationalcode) {
		this.orgizationalcode = orgizationalcode;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getPostalcode() {
		return postalcode;
	}
	public void setPostalcode(String postalcode) {
		this.postalcode = postalcode;
	}
	public String getPostaladdress() {
		return postaladdress;
	}
	public void setPostaladdress(String postaladdress) {
		this.postaladdress = postaladdress;
	}
	public String getTelephonenumber() {
		return telephonenumber;
	}
	public void setTelephonenumber(String telephonenumber) {
		this.telephonenumber = telephonenumber;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getPwddate() {
		return pwddate;
	}
	public void setPwddate(String pwddate) {
		this.pwddate = pwddate;
	}
	public String getRegUnitCode() {
		return regUnitCode;
	}
	public void setRegUnitCode(String regUnitCode) {
		this.regUnitCode = regUnitCode;
	}
	
}
