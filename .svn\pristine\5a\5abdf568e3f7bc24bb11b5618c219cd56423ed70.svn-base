package com.kangdainfo.tcfi.model.osss.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;


/**
 * 公司(商登)登記申請主檔(OSSM_ORG_REGISTER)
 *
 */
public class OssmOrgRegister extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子案號 */
	private String telixNo;
	/** 公司(商登)名稱 */
	private String orgName;
	/** 公司縣市代碼 */
	private String orgAreaCode;
	/** 公司(商登)地址-鄰 */
	private String orgNeiborCode;
	/** 公司(商登)地址 */
	private String orgAddr;
	/** 公司(商登)郵遞區號 */
	private String orgZipCode;
	/** 公司(商登)資本總額 */
	private Integer orgCapitalAmt;
	/** 股份有限公司實收資本額 */
	private Integer orgRealCapitalAmt;
	/** 營業地址縣市代碼 */
	private String bussAreaCode;
	/** 營業地址-鄰 */
	private String bussNeiborCode;
	/** 營業地址 */
	private String bussAddr;
	/** 營業地址郵遞區號 */
	private String bussZipCode;
	/** 公司(商登)電話 */
	private String orgTel;
	/** 公司(商登)合併地址 */
	private String orgAddrComb;
	/** 營業合併地址 */
	private String bussAddrComb;
	/** 是否有申請工商憑證 */
	private String ifBussCard;
	/** 設籍課稅統編 */
	private String ntaxBanNo;
	/** 分公司縣市代碼 */
	private String branchAreaCode;
	/** 分公司地址-鄰 */
	private String branchNeiborCode;
	/** 分公司地址 */
	private String branchAddr;
	/** 分公司郵遞區號 */
	private String branchZipCode;
	/** 分公司合併地址 */
	private String branchAddrComb;
	/** 在中華民國境內訴訟及非訴訟代理人姓名 */
	private String lawApplyName;
	/** 分公司設立家數 */
	private Integer branchSetupNum;
	/** 分公司變更登記家數 */
	private Integer branchChangeNum;
	/** 分公司撤銷登記家數 */
	private Integer branchDeregisterNum;
	/** 外國公司分公司撤銷登記家數 */
	private Integer forinBranchDeregstrNum;
	/** 分支機構聯絡電話 */
	private String branchTel;
	/** 分支機構傳真電話 */
	private String branchFax;
	/** 分支機構名稱 */
	private String branchName;
	/** 分支機構統一編號 */
	private String branchBanNo;
	/** 分支機構廢止日期 */
	private Date branchAbolishDate;
	/** 變更之新組織別 */
	private String newOrgType;
	/** 預查編號(提供給商業變更名稱使用) */
	private String prefixNoName;
	/** 預查編號(提供給商業變更所營業務使用) */
	private String prefixNoItem;
	/** 商業名稱(英) */
	private String engOrgName;
	/** 負責人姓名(英) */
	private String engApplyName;
	/** 合夥人姓名(英) */
	private String engPartnerName;
	/** 文字敘述之所營業務(英) */
	private String engBussItem;

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgAreaCode() {
		return orgAreaCode;
	}
	public void setOrgAreaCode(String orgAreaCode) {
		this.orgAreaCode = orgAreaCode;
	}
	public String getOrgNeiborCode() {
		return orgNeiborCode;
	}
	public void setOrgNeiborCode(String orgNeiborCode) {
		this.orgNeiborCode = orgNeiborCode;
	}
	public String getOrgAddr() {
		return orgAddr;
	}
	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}
	public String getOrgZipCode() {
		return orgZipCode;
	}
	public void setOrgZipCode(String orgZipCode) {
		this.orgZipCode = orgZipCode;
	}
	public Integer getOrgCapitalAmt() {
		return orgCapitalAmt;
	}
	public void setOrgCapitalAmt(Integer orgCapitalAmt) {
		this.orgCapitalAmt = orgCapitalAmt;
	}
	public Integer getOrgRealCapitalAmt() {
		return orgRealCapitalAmt;
	}
	public void setOrgRealCapitalAmt(Integer orgRealCapitalAmt) {
		this.orgRealCapitalAmt = orgRealCapitalAmt;
	}
	public String getBussAreaCode() {
		return bussAreaCode;
	}
	public void setBussAreaCode(String bussAreaCode) {
		this.bussAreaCode = bussAreaCode;
	}
	public String getBussNeiborCode() {
		return bussNeiborCode;
	}
	public void setBussNeiborCode(String bussNeiborCode) {
		this.bussNeiborCode = bussNeiborCode;
	}
	public String getBussAddr() {
		return bussAddr;
	}
	public void setBussAddr(String bussAddr) {
		this.bussAddr = bussAddr;
	}
	public String getBussZipCode() {
		return bussZipCode;
	}
	public void setBussZipCode(String bussZipCode) {
		this.bussZipCode = bussZipCode;
	}
	public String getOrgTel() {
		return orgTel;
	}
	public void setOrgTel(String orgTel) {
		this.orgTel = orgTel;
	}
	public String getOrgAddrComb() {
		return orgAddrComb;
	}
	public void setOrgAddrComb(String orgAddrComb) {
		this.orgAddrComb = orgAddrComb;
	}
	public String getBussAddrComb() {
		return bussAddrComb;
	}
	public void setBussAddrComb(String bussAddrComb) {
		this.bussAddrComb = bussAddrComb;
	}
	public String getIfBussCard() {
		return ifBussCard;
	}
	public void setIfBussCard(String ifBussCard) {
		this.ifBussCard = ifBussCard;
	}
	public String getNtaxBanNo() {
		return ntaxBanNo;
	}
	public void setNtaxBanNo(String ntaxBanNo) {
		this.ntaxBanNo = ntaxBanNo;
	}
	public String getBranchAreaCode() {
		return branchAreaCode;
	}
	public void setBranchAreaCode(String branchAreaCode) {
		this.branchAreaCode = branchAreaCode;
	}
	public String getBranchNeiborCode() {
		return branchNeiborCode;
	}
	public void setBranchNeiborCode(String branchNeiborCode) {
		this.branchNeiborCode = branchNeiborCode;
	}
	public String getBranchAddr() {
		return branchAddr;
	}
	public void setBranchAddr(String branchAddr) {
		this.branchAddr = branchAddr;
	}
	public String getBranchZipCode() {
		return branchZipCode;
	}
	public void setBranchZipCode(String branchZipCode) {
		this.branchZipCode = branchZipCode;
	}
	public String getBranchAddrComb() {
		return branchAddrComb;
	}
	public void setBranchAddrComb(String branchAddrComb) {
		this.branchAddrComb = branchAddrComb;
	}
	public String getLawApplyName() {
		return lawApplyName;
	}
	public void setLawApplyName(String lawApplyName) {
		this.lawApplyName = lawApplyName;
	}
	public Integer getBranchSetupNum() {
		return branchSetupNum;
	}
	public void setBranchSetupNum(Integer branchSetupNum) {
		this.branchSetupNum = branchSetupNum;
	}
	public Integer getBranchChangeNum() {
		return branchChangeNum;
	}
	public void setBranchChangeNum(Integer branchChangeNum) {
		this.branchChangeNum = branchChangeNum;
	}
	public Integer getBranchDeregisterNum() {
		return branchDeregisterNum;
	}
	public void setBranchDeregisterNum(Integer branchDeregisterNum) {
		this.branchDeregisterNum = branchDeregisterNum;
	}
	public Integer getForinBranchDeregstrNum() {
		return forinBranchDeregstrNum;
	}
	public void setForinBranchDeregstrNum(Integer forinBranchDeregstrNum) {
		this.forinBranchDeregstrNum = forinBranchDeregstrNum;
	}
	public String getBranchTel() {
		return branchTel;
	}
	public void setBranchTel(String branchTel) {
		this.branchTel = branchTel;
	}
	public String getBranchFax() {
		return branchFax;
	}
	public void setBranchFax(String branchFax) {
		this.branchFax = branchFax;
	}
	public String getBranchName() {
		return branchName;
	}
	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}
	public String getBranchBanNo() {
		return branchBanNo;
	}
	public void setBranchBanNo(String branchBanNo) {
		this.branchBanNo = branchBanNo;
	}
	public Date getBranchAbolishDate() {
		return branchAbolishDate;
	}
	public void setBranchAbolishDate(Date branchAbolishDate) {
		this.branchAbolishDate = branchAbolishDate;
	}
	public String getNewOrgType() {
		return newOrgType;
	}
	public void setNewOrgType(String newOrgType) {
		this.newOrgType = newOrgType;
	}
	public String getPrefixNoName() {
		return prefixNoName;
	}
	public void setPrefixNoName(String prefixNoName) {
		this.prefixNoName = prefixNoName;
	}
	public String getPrefixNoItem() {
		return prefixNoItem;
	}
	public void setPrefixNoItem(String prefixNoItem) {
		this.prefixNoItem = prefixNoItem;
	}
	public String getEngOrgName() {
		return engOrgName;
	}
	public void setEngOrgName(String engOrgName) {
		this.engOrgName = engOrgName;
	}
	public String getEngApplyName() {
		return engApplyName;
	}
	public void setEngApplyName(String engApplyName) {
		this.engApplyName = engApplyName;
	}
	public String getEngPartnerName() {
		return engPartnerName;
	}
	public void setEngPartnerName(String engPartnerName) {
		this.engPartnerName = engPartnerName;
	}
	public String getEngBussItem() {
		return engBussItem;
	}
	public void setEngBussItem(String engBussItem) {
		this.engBussItem = engBussItem;
	}

}