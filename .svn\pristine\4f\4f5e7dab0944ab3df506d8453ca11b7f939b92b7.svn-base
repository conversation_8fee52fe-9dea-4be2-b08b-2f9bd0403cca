package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import org.apache.commons.logging.Log;
import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;

public class PRE5001 extends SuperBean {
	private String telixNo;
	private String payType;
	private String chNo;
	private String payName;
	
	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getPayType() {
		return payType;
	}
	public void setPayType(String payType) {
		this.payType = payType;
	}
	public String getChNo() {
		return chNo;
	}
	public void setChNo(String chNo) {
		this.chNo = chNo;
	}
	public String getPayName() {
		return payName;
	}
	public void setPayName(String payName) {
		this.payName = payName;
	}
	private String receiptNo;
	public String getReceiptNo() {
		return receiptNo;
	}
	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	
	private String printPayType;
	public String getPrintPayType() {
		return printPayType;
	}
	public void setPrintPayType(String printPayType) {
		this.printPayType = printPayType;
	}
	
	private String printChNo;
	public String getPrintChNo() {
		return printChNo;
	}
	public void setPrintChNo(String printChNo) {
		this.printChNo = printChNo;
	}
	
	public void save() {
		PRE5001 pre5001 = this;
		try {
			String result = savePrefixReceiptNo(pre5001.getTelixNo(), pre5001.getPayType(), pre5001.getChNo(), pre5001.getPayName(), logger);
			String[] msg = result.split(";");
			if (msg[0].equals("0")) {
				pre5001.setReceiptNo(msg[1]);
				pre5001.setState("saveSuccess");
				pre5001.setErrorMsg("新增成功!");
			} else  {
				pre5001.setErrorMsg(msg[1]);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private synchronized static String savePrefixReceiptNo(String telixNo, String payType, String chNo, String payName, Log logger) throws Exception{
		int receiptNo = 0;
		ReceiptNoSetup receiptNoSetup = ServiceGetter.getInstance().getPre5001Service().selectReceiptNoSetupByReceiptType("1");
		int subtract = Integer.parseInt(receiptNoSetup.getEndReceiptNo())-Integer.parseInt(receiptNoSetup.getUsedReceiptNo());
		if (subtract==0) {
			return "1;收據編號已用罄, 請使用收據編號維護作業更新可用的收據號";
		} else {
			if (telixNo != null && !"".equals(telixNo)) {
				Eedb1000 eedb1000 = ServiceGetter.getInstance().getPre5001Service().selectEedb1000ByTelixNo(telixNo);
				if (eedb1000 == null) {
					return "1;輸入的電子案號錯誤, 資料庫裡沒有該筆資料";
				}
				
				PrefixReceiptNo prefixReceiptNo = ServiceGetter.getInstance().getPre5001Service().selectPrefixReceiptNoByTelixNo(telixNo);
				if (prefixReceiptNo != null) {
					return "1;該電子案號已收文, 請重新輸入!!";
				}
			}
			receiptNo = Integer.parseInt(receiptNoSetup.getUsedReceiptNo()) + 1;
			PrefixReceiptNo prefixReceiptNo = new PrefixReceiptNo();
			prefixReceiptNo.setTelixNo(telixNo);
			prefixReceiptNo.setReceiptNo(String.valueOf(receiptNo));
			prefixReceiptNo.setReceiptType("1");
			prefixReceiptNo.setPayType(payType);
			prefixReceiptNo.setPayDate(Datetime.getYYYMMDD());
			prefixReceiptNo.setChNo(chNo);
			prefixReceiptNo.setAmount(300);
			prefixReceiptNo.setPayName(payName);
			receiptNoSetup.setUsedReceiptNo(String.valueOf(receiptNo));
			if(logger.isInfoEnabled()) logger.info("[新增收據]收據編號:"+receiptNo);
			if (ServiceGetter.getInstance().getPre5001Service().insertPrefixReceiptNoAndUpdateReceiptNoSetup(prefixReceiptNo, receiptNoSetup)) {
				if(logger.isInfoEnabled()) logger.info("[新增收據]新增成功");
				return "0;"+String.valueOf(receiptNo);
			}
			else {
				if(logger.isInfoEnabled()) logger.info("[新增收據]新增失敗");
				return "1;新增收據失敗";
			}
		}
	}
	
	public File doPrintPdf() throws Exception {
		PRE5001 pre5001 = this;
		File report = null;
		try {
			PrefixReceiptNo prefixReceiptNo = ServiceGetter.getInstance().getPre5001Service().selectPrefixReceiptNo(pre5001.getReceiptNo());
			report = ServiceGetter.getInstance().getPre5001Service().createPdfFile(prefixReceiptNo);
		} catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<300) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return report;
	}
	
	public Object doQueryOne() throws Exception{ 
		return null;
	} // end doQueryOne()
	
	public ArrayList<?> doQueryAll() throws Exception{
		return null;
	} // doQueryAll()
		
	public void doUpdate() throws Exception {   
		
	} // end doUpdate()
	
	public void doCreate() throws Exception{
		
	} // end doCreate() 
	  
    public void doDelete() throws Exception{			
		   
    } // end doDelete()	
}