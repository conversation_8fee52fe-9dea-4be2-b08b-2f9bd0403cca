package com.kangdainfo.tcfi.model.eedb.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * (EEDB1300)
 *
 */
public class Eedb1300 extends BaseModel {
	private static final long serialVersionUID = 1L;

	private String telixNo;
	private String payType;
	private String amount;
	private String signCaName;
	private String payDate;
	private String payTime;
	private String bankCode;
	private String bankName;
	private String caseCode;
	private String caseShortName;
	private String companyName;
	private String applyName;
	private String receiptDate;
	private String receiptNo;
	private String receiptTitle;
	private String regCost;
	private String scriptCost;
	private String manCost;
	private String examineCost;
	private String transDate;
	private String returnDate;
	private String returnAmount;
	private String accountDate;
	

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getPayType() {
		return payType;
	}
	public void setPayType(String payType) {
		this.payType = payType;
	}
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getSignCaName() {
		return signCaName;
	}
	public void setSignCaName(String signCaName) {
		this.signCaName = signCaName;
	}
	public String getPayDate() {
		return payDate;
	}
	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
	public String getBankCode() {
		return bankCode;
	}
	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getCaseCode() {
		return caseCode;
	}
	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}
	public String getCaseShortName() {
		return caseShortName;
	}
	public void setCaseShortName(String caseShortName) {
		this.caseShortName = caseShortName;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getReceiptDate() {
		return receiptDate;
	}
	public void setReceiptDate(String receiptDate) {
		this.receiptDate = receiptDate;
	}
	public String getReceiptNo() {
		return receiptNo;
	}
	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	public String getReceiptTitle() {
		return receiptTitle;
	}
	public void setReceiptTitle(String receiptTitle) {
		this.receiptTitle = receiptTitle;
	}
	public String getRegCost() {
		return regCost;
	}
	public void setRegCost(String regCost) {
		this.regCost = regCost;
	}
	public String getScriptCost() {
		return scriptCost;
	}
	public void setScriptCost(String scriptCost) {
		this.scriptCost = scriptCost;
	}
	public String getManCost() {
		return manCost;
	}
	public void setManCost(String manCost) {
		this.manCost = manCost;
	}
	public String getExamineCost() {
		return examineCost;
	}
	public void setExamineCost(String examineCost) {
		this.examineCost = examineCost;
	}
	public String getTransDate() {
		return transDate;
	}
	public void setTransDate(String transDate) {
		this.transDate = transDate;
	}
	public String getReturnDate() {
		return returnDate;
	}
	public void setReturnDate(String returnDate) {
		this.returnDate = returnDate;
	}
	public String getReturnAmount() {
		return returnAmount;
	}
	public void setReturnAmount(String returnAmount) {
		this.returnAmount = returnAmount;
	}
	public String getAccountDate() {
		return accountDate;
	}
	public void setAccountDate(String accountDate) {
		this.accountDate = accountDate;
	}
}