package com.kangdainfo.tcfi.view.pre;

/*
 程式目的：個人主頁
 程式代號：pre3012
 撰寫日期：103.06.30
 程式作者：
 --------------------------------------------------------
 修改作者　　修改日期　　　修改目的
 --------------------------------------------------------
 */
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections.map.HashedMap;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;

public class PRE3012 extends SuperBean {

	private String staffName;
	private String[] checkedPrefixNo; // 被勾選的預查編號清單 + 他們在清單中的位置
	public String getStaffName() {
		return checkGet(staffName);
	}

	public void setStaffName(String s) {
		staffName = checkSet(s);
	}

	public String[] getCheckedPrefixNo() {
		return checkedPrefixNo;
	}

	public void setCheckedPrefixNo(String[] s) {
		checkedPrefixNo = s;
	}

	public SQLJob doAppendSqljob3010() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" SELECT");
		sqljob.appendSQL("  A.PREFIX_NO");
		sqljob.appendSQL(" ,A.COMPANY_NAME");
		sqljob.appendSQL(" ,A.APPLY_NAME");
		sqljob.appendSQL(" ,A.WORK_DAY");
		sqljob.appendSQL(" ,nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL("  ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE_NAME");
		sqljob.appendSQL(" ,decode(A.EXTEND_MARK,'Y',(decode(a.extend_reason,'90',a.extend_other,c14.code_name)),'') AS EXTEND_REASON");
		sqljob.appendSQL(" ,A.ASSIGN_DATE");
		sqljob.appendSQL(" ,A.ASSIGN_TIME");
		sqljob.appendSQL(" ,A.APPROVE_DATE");
		sqljob.appendSQL(" ,A.APPROVE_TIME");
		sqljob.appendSQL(" ,A.EXTEND_MARK");
		sqljob.appendSQL(" FROM CEDB1000 a");
		sqljob.appendSQL("   left outer join system_code c14 on c14.code_kind='14' and c14.code=a.extend_reason");
		sqljob.appendSQL(" WHERE A.APPROVE_RESULT = 'A'"); // --審查中
		sqljob.appendSQL(" AND A.ASSIGN_DATE IS NOT NULL"); // --已分文
		sqljob.appendSQL(" AND A.CLOSE_DATE IS NULL"); // --未結案
		sqljob.appendSQL(" AND ID_NO = ?");
		sqljob.appendSQL(" ORDER BY A.PREFIX_NO");
		sqljob.addParameter(getLoginUserId());
		return sqljob;
	} // doAppendSqljob()

	public SQLJob doAppendSqljob3011() { // 依此可以查出今日案件清單(PRE3011)
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" SELECT");
		sqljob.appendSQL("  A.PREFIX_NO");
		sqljob.appendSQL(" ,A.COMPANY_NAME");
		sqljob.appendSQL(" ,A.APPLY_NAME");
		sqljob.appendSQL(" ,A.WORK_DAY");
		sqljob.appendSQL(" ,nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL("  ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE_NAME");
		sqljob.appendSQL(" ,decode(A.EXTEND_MARK,'Y',(decode(a.extend_reason,'90',a.extend_other,c14.code_name)),'') AS EXTEND_REASON");
		sqljob.appendSQL(" ,A.ASSIGN_DATE");
		sqljob.appendSQL(" ,A.APPROVE_RESULT");
		sqljob.appendSQL(" ,A.ASSIGN_DATE");
		sqljob.appendSQL(" ,A.ASSIGN_TIME");
		sqljob.appendSQL(" ,A.APPROVE_DATE");
		sqljob.appendSQL(" ,A.APPROVE_TIME");
		sqljob.appendSQL(" ,A.EXTEND_MARK");
		sqljob.appendSQL(" FROM CEDB1000 a");
		sqljob.appendSQL("   left outer join system_code c14 on c14.code_kind='14' and c14.code=a.extend_reason");
		sqljob.appendSQL(" WHERE A.ASSIGN_DATE = ?");
		sqljob.appendSQL(" AND ID_NO = ?");
		sqljob.appendSQL(" ORDER BY A.PREFIX_NO");
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addParameter(getLoginUserId());
		return sqljob;
	} // doAppendSqljob()

	public String abandonFloatPoint(float workDay) {
		int div = 1;
		String s = Integer.toString((int) workDay / div);
		return s;
	} // abandonFloatPoint

	public String abandonFloatPointFirst(float workDay) {
		float x = workDay;
		DecimalFormat df = new DecimalFormat("#.##");
		String s = df.format(x);
		return s;
	} // abandonFloatPoint

	public ArrayList<?> doQueryAll3010() throws Exception {
		try {
			List<Map<String, Object>> cedb1000List = ServiceGetter.getInstance().getEicmGeneralQueryDao()
					.queryForList(doAppendSqljob3010());
			;
			int i = 0;
			ArrayList<String[]> dataList = new ArrayList<String[]>();
			String[] rowArray;
			setStaffName("承辦人：" + getLoginUserName());
			if (cedb1000List == null || cedb1000List.size() == 0) {
				setErrorMsg("查無資料！");
				throw new MoeaException("查無資料！");
			} // if
			Map<String, Object> temp;
			String assignDate, assignTime;
			while (i < cedb1000List.size()) {
				rowArray = new String[8];
				temp = cedb1000List.get(i);

				assignDate = Common.get(temp.get("ASSIGN_DATE"));
				assignTime = Common.get(temp.get("ASSIGN_TIME"));

				rowArray[0] = Common.get(temp.get("PREFIX_NO"));
				rowArray[1] = Common.get(temp.get("COMPANY_NAME"));
				rowArray[2] = Common.get(temp.get("CHANGE_TYPE_NAME"));
				rowArray[3] = Common.get(temp.get("APPLY_NAME"));
				rowArray[4] = ""; // abandonFloatPoint(workDay)+"天";
				rowArray[5] = Datetime.formatRocDateTime(assignDate, assignTime);
				rowArray[6] = Common.get(temp.get("EXTEND_REASON"));
				rowArray[7] = ""; // abandonFloatPointFirst(approveWorkDay * 8);

				dataList.add(rowArray);
				i = i + 1;
			} // end while
			return dataList;
		} // try
		catch (MoeaException e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 300)
				setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else
				setErrorMsg("查詢失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch
	} // doQueryAll()

	public HashedMap getWorkDay() {
		List<Map<String, Object>> cedb1000List = ServiceGetter.getInstance().getEicmGeneralQueryDao()
				.queryForList(doAppendSqljob3010());

		int i = 0;
		if (cedb1000List == null || cedb1000List.size() == 0) {
			setErrorMsg("查無資料！");
		}
		
		HashedMap workDatas = new HashedMap();
		
		Map<String, Object> temp;
		Float workDay;
		Float approveWorkDay;// 審核工作天數
		String assignDate, assignTime, approveDate, approveTime, extendMark;
		while (i < cedb1000List.size()) {
			temp = cedb1000List.get(i);

			assignDate = Common.get(temp.get("ASSIGN_DATE"));
			assignTime = Common.get(temp.get("ASSIGN_TIME"));
			approveDate = Common.get(temp.get("APPROVE_DATE"));
			approveTime = Common.get(temp.get("APPROVE_TIME"));
			extendMark = Common.get(temp.get("EXTEND_MARK"));
			workDay = ServiceGetter.getInstance().getCaseFlowService()
					.countWorkDay(assignDate, assignTime, Datetime.getYYYMMDD(), Datetime.getHHMMSS());

			approveWorkDay = 0.08f;
			if ("Y".equals(extendMark)) {
				approveWorkDay = 0.08f;
			} // if
			else if (!"".equals(approveDate) && !"".equals(approveTime)) {
				approveWorkDay = ServiceGetter.getInstance().getCaseFlowService()
						.countWorkDay(assignDate, assignTime, approveDate, approveTime);
			} // else if
			else {
				approveWorkDay = workDay;
			} // else

			workDatas.put(Common.get(temp.get("PREFIX_NO")), abandonFloatPoint(workDay) + "天" + "-"
					+ abandonFloatPointFirst(approveWorkDay * 8));

			i = i + 1;
		} // end while
		return workDatas;
	}

	public HashedMap getWorkDayForPre3011() throws Exception {
		HashedMap workDatas = new HashedMap();

		try {
			setStaffName("承辦人：" + getLoginUserName());
			List<Map<String, Object>> cedb1000List;
			cedb1000List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob3011());
			int i = 0;
			if (cedb1000List == null || cedb1000List.size() == 0) {
				setErrorMsg("查無資料！");
				throw new MoeaException("查無資料！");
			} // if

			Map<String, Object> temp;
			Float approveWorkDay;
			String assignDate, assignTime, approveDate, approveTime, extendMark;
			while (i < cedb1000List.size()) {
				temp = cedb1000List.get(i);

				assignDate = Common.get(temp.get("ASSIGN_DATE"));
				assignTime = Common.get(temp.get("ASSIGN_TIME"));
				approveDate = Common.get(temp.get("APPROVE_DATE"));
				approveTime = Common.get(temp.get("APPROVE_TIME"));
				extendMark = Common.get(temp.get("EXTEND_MARK"));

				approveWorkDay = 0.08f;
				if ("Y".equals(extendMark)) {
					approveWorkDay = 0.08f;
				} // if
				else if (!"".equals(approveDate) && !"".equals(approveTime)) {
					approveWorkDay = ServiceGetter.getInstance().getCaseFlowService()
							.countWorkDay(assignDate, assignTime, approveDate, approveTime);
				} // else if
				else {
					approveWorkDay = ServiceGetter.getInstance().getCaseFlowService()
							.countWorkDay(assignDate, assignTime, Datetime.getYYYMMDD(), Datetime.getHHMMSS());
				} // else

				workDatas.put(Common.get(temp.get("PREFIX_NO")), Datetime.formatRocDateTime(assignDate, assignTime) + "-" + abandonFloatPointFirst(approveWorkDay * 8));
				
				i++;
			} // end while
		} // try
		catch (MoeaException e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 300)
				setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else
				setErrorMsg("查詢失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch
		
		return workDatas;
		
	} // doQueryAll()
	
	public ArrayList<?> doQueryAll3011() throws Exception {
		try {
			setStaffName("承辦人：" + getLoginUserName());
			List<Map<String, Object>> cedb1000List;
			cedb1000List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob3011());
			int i = 0;
			ArrayList<String[]> dataList = new ArrayList<String[]>();
			String[] rowArray;
			if (cedb1000List == null || cedb1000List.size() == 0) {
				setErrorMsg("查無資料！");
				throw new MoeaException("查無資料！");
			} // if

			Map<String, Object> temp;
			while (i < cedb1000List.size()) {
				rowArray = new String[8];
				temp = cedb1000List.get(i);

				rowArray[0] = Common.get(temp.get("PREFIX_NO"));
				rowArray[1] = Common.get(temp.get("COMPANY_NAME"));
				rowArray[2] = Common.get(temp.get("CHANGE_TYPE_NAME"));
				rowArray[3] = Common.get(temp.get("APPLY_NAME"));
				rowArray[4] = ""; //Datetime.formatRocDateTime(assignDate, assignTime);
				rowArray[5] = ServiceGetter.getInstance().getSystemCode05Loader()
						.getCodeNameByCode(Common.get(temp.get("APPROVE_RESULT")));
				rowArray[6] = Common.get(temp.get("EXTEND_REASON"));
				rowArray[7] = ""; //abandonFloatPointFirst(approveWorkDay * 8);
				dataList.add(rowArray);
				i++;
			} // end while
			return dataList;
		} // try
		catch (MoeaException e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 300)
				setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else
				setErrorMsg("查詢失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch
	} // doQueryAll()

	/** 分文 */
	public void doAssignToMe() throws Exception{
		try{
			CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
			if (user == null)
				throw new Exception("無法取得使用者資料");
			if (Integer.parseInt(Datetime.getYYYMMDD().substring(0,3)) >= 106 ) {
				Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
				if (cedbc000!=null && "六科".equals(cedbc000.getStaffUnit())) {
					throw new Exception("預查業務已移交經濟部商業發展署名稱預查科, 其他科的同仁毋須使用分文功能");
				}
			}
			//統一由PRE3001來分文
			String result = PRE3001.assign(user.getUserId(), user.getUserName());
			if("".equals(Common.get(result)))	
				throw new Exception("已經沒有可供分文的案件");
			setErrorMsg("分文成功！");
			setState("assignSuccess");
		}catch(Exception e){
			e.printStackTrace();
			setState("assignError");
			setErrorMsg("分文失敗!!" + e.getMessage());
		}
	} // doAssignToMe

	public static String getQuerylist(boolean primaryArray[], boolean displayArray[], String[] arrAlign, ArrayList<String[]> objList,
			String queryAllFlag, boolean withListNo, boolean withCheckbox, boolean linkArray[], String target, String noDataMsg,
			boolean checkHtml, boolean defaultRow, String checkboxName) {
		int i, counter = 0;
		boolean trFlag = false, targetFlag = false, even = false;
		String pk = "";

		StringBuilder sb = new StringBuilder();
		if (objList != null && objList.size() > 0) {
			String rowArray[] = new String[primaryArray.length];
			java.util.Iterator<String[]> it = objList.iterator();

			// boolean defaultRow = true;
			String defaultKey = "null";
			for (i = 0; i < primaryArray.length; i++) {
				if (primaryArray[i])
					defaultKey = "";
			}

			while (it.hasNext()) {
				rowArray = it.next();
				counter++;
				String classTR = "tr_odd";
				if (even) {
					classTR = "tr_even";
				}

				pk = "";
				for (i = 0; i < primaryArray.length; i++) {
					if (primaryArray[i])
						pk += Common.escapeReturnChar(rowArray[i]);
				}
				StringBuilder v = new StringBuilder().append(defaultKey);
				for (i = 0; i < primaryArray.length; i++) {
					if (primaryArray[i]) {
						if (trFlag) {
							v.append(",'").append(Common.escapeReturnChar(rowArray[i])).append("'");
						} else {
							v.append("'").append(Common.escapeReturnChar(rowArray[i])).append("'");
							trFlag = true;
						}
					}
				}
				if (targetFlag == false && target != null && !"".equals(Common.get(target))) {
					v.append(",'").append(target).append("'");
					targetFlag = true;
				}

				// 顯示TR
				if (linkArray != null) {
					sb.append(" <tr class='highLight' >");
				} else {
					sb.append(" <tr id=\"").append("listContainerRow").append(pk).append("\"");
					sb.append(" class=\"").append(classTR).append("\"");
					sb.append(">\n");
				}
				// 顯示TD
				if (withListNo)
					sb.append(" <td style=\"text-align:center\" onClick=\"queryOne(").append(v).append(")\">").append(counter)
							.append("</td>\n");
				if (withCheckbox) {
					sb.append("<td class='listTD'>");
					sb.append("<input type='checkbox' ");
					// sb.append(" class='field_Q'");
					sb.append(" id=\"").append(checkboxName).append("\"");
					sb.append(" name=\"").append(checkboxName).append("\"");
					sb.append(" value=\"").append(v.toString().replaceAll("'", "")).append("\"");
					sb.append("/>");
					sb.append("</td>\n");
				} // if
				targetFlag = false;
				for (i = 0; i < displayArray.length; i++) {
					if (displayArray[i]) {
						if (arrAlign != null && arrAlign.length > 0) {
							sb.append(" <td style=\"text-align:").append(arrAlign[i]).append("\"  onClick=\"queryOne(").append(v)
									.append(")\">"); // .append(Common.get(rowArray[i])).append("</td>\n");
						} else {
							sb.append(" <td>");
						}
						if (linkArray != null && linkArray[i]) {
							sb.append("<a href='#' class='sLink2' onClick=\"listContainerRowClick('").append(pk)
									.append("');queryOne(").append(v).append(",").append(i).append(")\"");
							sb.append(">").append(checkHtml ? Common.checkGet(rowArray[i]) : Common.get(rowArray[i]))
									.append("</a>");
						} else
							sb.append(checkHtml ? Common.checkGet(rowArray[i]) : Common.get(rowArray[i]));
						if (defaultRow) { // 預設選取欄位
							sb.append("<script type=\"text/javascript\">");
							sb.append("if(typeof queryOne == 'function') {");
							sb.append("	if (isObj(document.all.item('state')) && document.all.item('state').value=='queryAllSuccess') {");
							sb.append(" listContainerRowClick('").append(pk).append("');");
							sb.append("	queryOne(").append(v).append(",-1);");
							sb.append("	}");
							sb.append("}");
							sb.append("</script>");
							// sb.append("<input type='hidden' name='listContainerRowDefault' id='listContainerRowDefault' value=\""
							// ).append( v ).append( "\">");
							defaultRow = false;
						}
						sb.append("</td>\n");
					}
				}
				sb.append("</tr>\n");
				trFlag = false;
				even = !even;
			}
		} else {
			if ("true".equals(queryAllFlag))
				sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>")
						.append("".equals(Common.get(noDataMsg)) ? "查無資料，請您重新輸入查詢條件！" : noDataMsg).append("</td></tr>");
		}
		return sb.toString();
	}

	public void doCreate() throws Exception {
	}

	public void doUpdate() throws Exception {
	}

	public void doDelete() throws Exception {
	}

	public Object doQueryOne() throws Exception {
		return this;
	}

	public ArrayList<?> doQueryAll() throws Exception {
		return null;
	}

} // PRE3005