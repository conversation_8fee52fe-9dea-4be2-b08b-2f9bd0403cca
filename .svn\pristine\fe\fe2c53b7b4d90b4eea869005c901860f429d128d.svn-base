package com.kangdainfo.tcfi.view.pre;
/*
程式目的：申請案郵寄資料列印
程式代號：pre2005
撰寫日期：103.05.26
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE2005 extends SuperBean {

	private String q_type ;
	private String q_prefixStart ;
	private String q_prefixEnd ;
	private String q_postStart ;
	private String q_postEnd ;
	private String q_printType ;
	
	private String seqNo ;
	private String postNo ;
	private String name ;
	private String addr ;
	private String prefixNo ;
	
	
	//------------------------------ getter and setter of variables bellow ------------------------------
	public String getQ_type() {return checkGet(q_type);}
	public void setQ_type(String s) {q_type = checkSet(s);}
	public String getQ_prefixStart() {return checkGet(q_prefixStart);}
	public void setQ_prefixStart(String s) {q_prefixStart = checkSet(s);}
	public String getQ_prefixEnd() {return checkGet(q_prefixEnd);}
	public void setQ_prefixEnd(String s) {q_prefixEnd = checkSet(s);}
	public String getQ_postStart() {return checkGet(q_postStart);}
	public void setQ_postStart(String s) {q_postStart = checkSet(s);}
	public String getQ_postEnd() {return checkGet(q_postEnd);}
	public void setQ_postEnd(String s) {q_postEnd = checkSet(s);}
	public String getQ_printType() {return checkGet(q_printType);}
	public void setQ_printType(String s) {q_printType = checkSet(s);}
	
	public String getSeqNo() {return checkGet(seqNo);}
	public void setSeqNo(String s) {seqNo = checkSet(s);}
	public String getPostNo() {return checkGet(postNo);}
	public void setPostNo(String s) {postNo = checkSet(s);}
	public String getName() {return checkGet(name);}
	public void setName(String s) {name = checkSet(s);}
	public String getAddr() {return checkGet(addr);}
	public void setAddr(String s) {addr = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	
	// ------------------------------ the 4 basic function bellow ------------------------------
	

	public void doCreate() throws Exception{	  
	} // end doCreate()
		  
	public void doUpdate() throws Exception{
	} // end doUpdate()		
		  
	public void doDelete() throws Exception{			
	} // end doDelete()	
		  
	public Object doQueryOne() throws Exception{ 
	    return null ;
	} // end doQueryOne()
	  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
	    return null ;	      
	} // doQueryAll()
	
	// ----------------------------------- extra functions -------------------------------------------------
	
	public String DateFormat( String inputDate ) {
	  String tempDate = "" ;
	  String year = inputDate.substring(0, 3) ;
	  String month = inputDate.substring(4, 6) ;
	  String day = inputDate.substring(7) ;
	  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
	  return tempDate ;
	} // DateFormat()
	  
	public String TimeFormat( String inputTime ) {
	  String tempTime = "" ;
	  String hour = inputTime.substring(0, 2) ;
	  String minute = inputTime.substring(2, 4) ;
	  String second = inputTime.substring(4) ;
	  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
	  return tempTime ;
	} // TimeFormat()
	
	public static SQLJob doAppendSQLJobWithPrefix(String noStart, String noEnd, String postType) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select * from ( ");
		sqljob.appendSQL("    select post_no, prefix_no ");
		sqljob.appendSQL("    ,(select get_name from cedb1023 where prefix_no = a.prefix_no) as get_name ");
		sqljob.appendSQL("    ,(select get_addr from cedb1023 where prefix_no = a.prefix_no) as get_addr ");
		sqljob.appendSQL("    from cedb1027 a");
		sqljob.appendSQL("    where prefix_no between ? and ? ");
		sqljob.appendSQL("    and post_type = ?");
		sqljob.appendSQL(") order by prefix_no");
		sqljob.addParameter(noStart);
		sqljob.addParameter(noEnd);
		sqljob.addParameter(postType);
		return sqljob;
	} 
	
	public static SQLJob doAppendSQLJobWithPost(String noStart, String noEnd, String postType) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select * from ( ");
		sqljob.appendSQL("    select post_no, prefix_no ");
		sqljob.appendSQL("           ,(select get_name from cedb1023 b where a.prefix_no = b.prefix_no) get_name");
		sqljob.appendSQL("           ,(select get_addr from cedb1023 b where a.prefix_no = b.prefix_no) get_addr");
		sqljob.appendSQL("    from cedb1027 a ");
		sqljob.appendSQL("    where post_no between ? and ? ");
		sqljob.appendSQL("    and post_type = ?");
		sqljob.appendSQL("    union ");
		sqljob.appendSQL("    select post_no, prefix_no, get_name, get_addr ");
		sqljob.appendSQL("    from post_record ");
		sqljob.appendSQL("    where post_no between ? and ?");
		sqljob.appendSQL("    and post_type = ?");
		sqljob.appendSQL(") order by post_no");
		sqljob.addParameter(noStart);
		sqljob.addParameter(noEnd);
		sqljob.addParameter(postType);
		sqljob.addParameter(noStart);
		sqljob.addParameter(noEnd);
		sqljob.addParameter(postType);
		return sqljob;
	}
	
	
	public File doPrintPdf() {
	  File report = null ;
      try {
    	  String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre2005.jasper");  
    	  List<Map<String, Object>> tempList = new ArrayList<Map<String, Object>>();
    	  if ( "prefix".equals( getQ_type() ) ) {	
    		  tempList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobWithPrefix(getQ_prefixStart(), getQ_prefixEnd(), getQ_printType()));
  		  } // end if
  		  else if ( "post".equals( getQ_type() ) ) {
  		      tempList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobWithPost(getQ_postStart(), getQ_postEnd(), getQ_printType()));
  		  } // end if
    	  Map<String, Object> parameters = new HashMap<String,Object>();
    	  String printDate = DateFormat( Datetime.getRocDateFromYYYYMMDD(Datetime.getCurrentDate("yyyy/MM/dd"))) ;//列印時間年月日
    	  String printTime = TimeFormat( Datetime.getHHMMSS()) ;    // 列印時間時分秒                                                    
    	  parameters.put("printDate", printDate);//列印時間
    	  parameters.put("printTime", printTime);//列印時間
    	  CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
    	  Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
    	  parameters.put("staffUnit", cedbc000.getStaffUnit());//列印時間
    	  parameters.put("reportGenerator", ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(user.getUserId()));
    	  List<PRE2005> dataList = new ArrayList<PRE2005>();
    	  PRE2005 pre2005 ;
    	  int i = 0;
    	  if (tempList == null || tempList.size() == 0) {
    		  pre2005 = new PRE2005();
    		  dataList.add(pre2005);
    	  }
    	  else {
    		  for ( i = 0; i<tempList.size();i++ ) {
    			  pre2005 = new PRE2005();
    			  Map<String, Object> temp = tempList.get(i);
    			  pre2005.setSeqNo(Integer.toString(i+1));
    			  pre2005.setPostNo(Common.get(temp.get("post_no")));
    			  pre2005.setPrefixNo(Common.get(temp.get("prefix_no")));
    			  pre2005.setName(Common.get(temp.get("get_name")));
    			  pre2005.setAddr(Common.get(temp.get("get_addr")));
    			  dataList.add(pre2005);
    		  } //for
    	  } // else

          parameters.put("total", Integer.toString( tempList.size() ) );
          report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
          setState("printSuccess");
          setErrorMsg( "查詢成功" ) ;
          return report ;
      } // try
      catch( Exception e ) {
          e.printStackTrace();
          if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
    	  else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！"); 
          return null ;
      } // catch
	} // doPrintPdf()
	
	public static String checkForjsp( String start, String end, String type, String printType ) {		
		ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobWithPost(start, end, printType));
		return "ok";
	}
} // PRE2005
