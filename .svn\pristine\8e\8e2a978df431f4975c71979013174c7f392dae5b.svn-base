package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1009;

public class Cedb1009Dao extends BaseDaoJdbc implements RowMapper<Cedb1009> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1009 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1009> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedb1009>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByComposite = "SELECT * FROM CEDB1009 WHERE PREFIX_NO = ? AND UPDATE_ID_NO = ? AND UPDATE_DATE = ? AND UPDATE_TIME = ?";
	public List<Cedb1009> findByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		SQLJob sqljob = new SQLJob(SQL_findByComposite);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(updateIdNo);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
	}

	@Override
	public Cedb1009 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1009 obj = null;
		if(null!=rs) {
			obj = new Cedb1009();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApproveOpinionNo(rs.getString("APPROVE_OPINION_NO"));
			obj.setApproveOpinion(rs.getString("APPROVE_OPINION"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
		}
		return obj;
	}

}
