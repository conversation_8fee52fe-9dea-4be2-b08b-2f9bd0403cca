package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdWorkDay;

public class CsmdWorkDayDao extends BaseDaoJdbc implements RowMapper<CsmdWorkDay> {

	private static final String SQL_defaultOrder = "order by work_date";
	
	private static final String SQL_findAll = "select * from icms.CSMD_WORK_DAY";
	public List<CsmdWorkDay> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<CsmdWorkDay>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	/** 判斷是否為假日 */
	private static final String SQL_checkIsHoliday = "SELECT * FROM ICMS.CSMD_WORK_DAY WHERE REG_UNIT_CODE='01' AND WORK_DATE=TO_DATE(?+19110000,'yyyyMMdd') AND ROWNUM=1";
	public boolean checkIsHoliday(String dateStr) {
		if( "".equals(Common.get(dateStr)) ) return false;
		if( Common.get(dateStr).length() != 7 ) return false;
		SQLJob sqljob = new SQLJob(SQL_checkIsHoliday);
		sqljob.addParameter(dateStr);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<CsmdWorkDay> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(),sqljob.getSqltypesArray(),this);
		if(null!=list && !list.isEmpty()) {
			return "N".equalsIgnoreCase(list.get(0).getWork1())?true:false;
		}
		return false;
	}
	
	/** 計算工作日數 */
	public Float sumWorkDays(String startDate, String endDate) {
		if( "".equals(Common.get(startDate)) ) return 0f;
		if( Common.get(endDate).length() != 7 ) return 0f;
		if( "".equals(Common.get(endDate)) ) return 0f;
		if( Common.get(endDate).length() != 7 ) return 0f;
		SQLJob sqljob = new SQLJob("SELECT COUNT(1) FROM ICMS.CSMD_WORK_DAY WHERE REG_UNIT_CODE='01' AND ( WORK_DATE BETWEEN TO_DATE(?+19110000,'yyyyMMdd') AND TO_DATE(?+19110000,'yyyyMMdd') ) AND WORK1='Y'");
		sqljob.addParameter(startDate);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(endDate);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(),sqljob.getSqltypesArray(), Float.class);
	}

	public CsmdWorkDay mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdWorkDay obj = null;
		if(null!=rs) {
			obj = new CsmdWorkDay();
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setWorkDate(rs.getDate("WORK_DATE"));
			obj.setWeek(rs.getString("WEEK"));
			obj.setWork1(rs.getString("WORK1"));
			obj.setHalf(rs.getString("HALF"));
			obj.setFlag(rs.getString("FLAG"));
		}
		return obj;
	}

}