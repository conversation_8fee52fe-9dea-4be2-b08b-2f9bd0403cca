package com.kangdainfo.tcfi.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao;
import com.kangdainfo.tcfi.service.Pre4006Service;
import com.kangdainfo.tcfi.view.pre.PRE4006;

/**
 * 承辦人員處理案件統計
 *
 */
public class Pre4006ServiceImpl implements Pre4006Service {
	private Logger logger = Logger.getLogger(this.getClass());

	/** 承辦人辦結件數統計 */
	public Integer countDataList(String dateStart, String dateEnd, String type) {
		Integer count = 0;
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" select count(1) C");
		sqljob.appendSQL(" from eicm.cedb1000 a");
		sqljob.appendSQL(" where a.staff_name is not null");
		if ("receive".equals(type)) {
			// 收文日期
			sqljob.appendSQL(" and a.receive_date >= ?");
			sqljob.addParameter(dateStart);
			sqljob.appendSQL(" and a.receive_date <= ?");
			sqljob.addParameter(dateEnd);
		} else {
			// 分文日期
			if( !"".equals(Common.get(dateEnd)) && Datetime.getYYYMMDD().compareTo(dateEnd) < 0 ) {
				//迄日 大於 系統日
				sqljob.appendSQL(" and ( a.assign_date >= ? or a.assign_date is null )");
				sqljob.addParameter(dateStart);
			} else {
				sqljob.appendSQL(" and a.assign_date >= ?");
				sqljob.addParameter(dateStart);
				sqljob.appendSQL(" and a.assign_date <= ?");
				sqljob.addParameter(dateEnd);
			}
		} // end else
		if (logger.isInfoEnabled()) logger.info(sqljob);
		List<Map<String,Object>> datas = eicmGeneralQueryDao.queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			BigDecimal c = (BigDecimal)datas.get(0).get("C");
			if(null!=c && c.intValue() > 0 ) {
				count = c.intValue();
			}
		}
		return count;
	}

	/** 承辦人辦結件數 */
	public List<PRE4006> queryDataList(String dateStart, String dateEnd, String type) {
		List<PRE4006> dataList = new ArrayList<PRE4006>();
		List<Cedbc000> users = queryUsers(dateStart, dateEnd, type);
		List<PRE4006> details = null;
		PRE4006 curr = null;
		if(null!=users && !users.isEmpty()) {
			for(Cedbc000 user : users) {
				details = queryUserDataList(dateStart, dateEnd, type, user.getIdNo());
				if(null!=details && !details.isEmpty()) {
					curr = details.get(0);
					curr.setStaffName(user.getStaffName());
					dataList.add(curr);
				}
			}
		}
		//加線上申辦(LL)
		details = queryUserDataList(dateStart, dateEnd, type, "LL");
		if(null!=details && !details.isEmpty()) {
			curr = details.get(0);
			dataList.add(curr);
		}
		return dataList;
	}

	@SuppressWarnings("unchecked")
	private List<PRE4006> queryUserDataList(String dateStart, String dateEnd, String type, String idNo) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" select");
		sqljob.appendSQL("	 staff_name");
		sqljob.appendSQL("	,count(prefix_no) as case_num");
		sqljob.appendSQL("	,(sum(work_day)/count(prefix_no)) as average");
		sqljob.appendSQL("	,sum(case when (close_date is null)                                            then 0 else 1 end) as close_num");
		sqljob.appendSQL("	,sum(case when (close_date is null)                                            then 1 else 0 end) as unclose_num");
		sqljob.appendSQL("	,sum(case when ((close_date is not null) and (work_day<=1))                    then 1 else 0 end) as c1");
		sqljob.appendSQL("	,sum(case when ((close_date is not null) and (work_day>1   and work_day<=2))   then 1 else 0 end) as c2");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>2   and work_day<=2.5)) then 1 else 0 end) as c3");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>2.5 and work_day<=3))   then 1 else 0 end) as c4");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>3   and work_day<=3.5)) then 1 else 0 end) as c5");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>3.5 and work_day<=4))   then 1 else 0 end) as c6");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>4   and work_day<=5))   then 1 else 0 end) as c7");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>5   and work_day<=10))  then 1 else 0 end) as c8");
		sqljob.appendSQL("  ,sum(case when ((close_date is not null) and (work_day>10))                    then 1 else 0 end) as c9");
		sqljob.appendSQL(" from (");
		sqljob.appendSQL("   select");
		sqljob.appendSQL("      a.prefix_no");
		sqljob.appendSQL("     ,a.staff_name");
		sqljob.appendSQL("     ,a.close_date");
		sqljob.appendSQL("     ,a.approve_result");
		sqljob.appendSQL("     ,(select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no and process_status <= '8') as work_day");
		sqljob.appendSQL("   from eicm.cedb1000 a");
		sqljob.appendSQL("   where a.id_no = ?");
		sqljob.addParameter(idNo);

		if ("receive".equals(type)) {
			// 收文日期
			sqljob.appendSQL(" and a.receive_date >= ?");
			sqljob.addParameter(dateStart);
			sqljob.appendSQL(" and a.receive_date <= ?");
			sqljob.addParameter(dateEnd);
		} else {
			// 分文日期
			if( !"".equals(Common.get(dateEnd)) && Datetime.getYYYMMDD().compareTo(dateEnd) < 0 ) {
				//迄日 大於 系統日
				sqljob.appendSQL(" and ( a.assign_date >= ? or a.assign_date is null )");
				sqljob.addParameter(dateStart);
			} else {
				sqljob.appendSQL(" and a.assign_date >= ?");
				sqljob.addParameter(dateStart);
				sqljob.appendSQL(" and a.assign_date <= ?");
				sqljob.addParameter(dateEnd);
			}
		} // end else
		sqljob.appendSQL(" ) t");
		sqljob.appendSQL(" group by staff_name");
		if (logger.isInfoEnabled()) logger.info(sqljob);
		return eicmGeneralQueryDao.query(sqljob, BeanPropertyRowMapper.newInstance(PRE4006.class));
	}

	/** 未分文件數 */
	public ArrayList<String[]> queryNotAssign(String dateStart, String dateEnd, String type) {
		java.util.ArrayList<String[]> dataList = new java.util.ArrayList<String[]>();
		try {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL(" select");
			sqljob.appendSQL("	 TO_CHAR(sum(1)) as c1");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day<=1)) then 1 else 0 end ), '9,999,999') as c2");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day>1) and (work_day <=2)) then 1 else 0 end ), '9,999,999') as c3");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day>2) and (work_day <=3)) then 1 else 0 end ), '9,999,999') as c4");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day>3) and (work_day <=4)) then 1 else 0 end ), '9,999,999') as c5");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day>4) and (work_day <=10)) then 1 else 0 end ), '9,999,999') as c6");
			sqljob.appendSQL("	,TO_CHAR(sum( case when ( (work_day>10)) then 1 else 0 end ), '9,999,999') as c7");
			sqljob.appendSQL(" from (");
			sqljob.appendSQL("   select");
			sqljob.appendSQL("      (select sum(work_day) from eicm.cedb1010 where prefix_no = a.prefix_no and process_status <= '8') as work_day");
			sqljob.appendSQL("   from eicm.cedb1000 a");
			sqljob.appendSQL("   where a.staff_name is null");

			if ("receive".equals(type)) {
				// 收文日期
				sqljob.appendSQL(" and a.receive_date >= ?");
				sqljob.addParameter(dateStart);
				sqljob.appendSQL(" and a.receive_date <= ?");
				sqljob.addParameter(dateEnd);
			} else {
				// 分文日期
				if( !"".equals(Common.get(dateEnd)) && Datetime.getYYYMMDD().compareTo(dateEnd) < 0 ) {
					//迄日 大於 系統日
					sqljob.appendSQL(" and ( a.assign_date >= ? or a.assign_date is null )");
					sqljob.addParameter(dateStart);
				} else {
					sqljob.appendSQL(" and a.assign_date >= ?");
					sqljob.addParameter(dateStart);
					sqljob.appendSQL(" and a.assign_date <= ?");
					sqljob.addParameter(dateEnd);
				}
			} // end else
			sqljob.appendSQL(" ) t");
			if (logger.isInfoEnabled()) logger.info(sqljob);

			List<Map<String, Object>> rs = eicmGeneralQueryDao.queryForList(sqljob);
			if (rs != null && rs.size() > 0) {
				String[] rowArray = new String[7];
				// 即便沒有資料, 查詢結果還是會有一筆空白紀錄
				// 因此用未分文件數這個欄位是否空白作為一個判斷是否有找到資料的標準
				Map<String, Object> temp = rs.get(0);
				if ("".equals(Common.get(temp.get("c1")))) {
					rowArray[0] = "0";
					rowArray[1] = "0";
					rowArray[2] = "0";
					rowArray[3] = "0";
					rowArray[4] = "0";
					rowArray[5] = "0";
					rowArray[6] = "0";
				} else {
					rowArray[0] = Common.get(temp.get("c1"));
					rowArray[1] = Common.get(temp.get("c2"));
					rowArray[2] = Common.get(temp.get("c3"));
					rowArray[3] = Common.get(temp.get("c4"));
					rowArray[4] = Common.get(temp.get("c5"));
					rowArray[5] = Common.get(temp.get("c6"));
					rowArray[6] = Common.get(temp.get("c7"));
				}
				dataList.add(rowArray);
			}
		} catch (Exception e) {
			e.printStackTrace();
			dataList = null;
		} // catch
		return dataList;
	}

	/** 查詢承辦人員 */
	@SuppressWarnings("unchecked")
	private List<Cedbc000> queryUsers(String dateStart, String dateEnd, String type) {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" select");
		sqljob.appendSQL("    id_no");
		sqljob.appendSQL("   ,staff_name");
		sqljob.appendSQL(" from eicm.cedbc000 c");
		sqljob.appendSQL(" where staff_unit='");
		sqljob.appendSQL(cedbc000.getStaffUnit());
		sqljob.appendSQL("'");
		sqljob.appendSQL(" and 0 < (");
		sqljob.appendSQL("   select count(1)");
		sqljob.appendSQL("   from eicm.cedb1000 a");
		sqljob.appendSQL("   where a.id_no=c.id_no");
		if ("receive".equals(type)) {
			// 收文日期
			sqljob.appendSQL(" and a.receive_date >= ?");
			sqljob.addParameter(dateStart);
			sqljob.appendSQL(" and a.receive_date <= ?");
			sqljob.addParameter(dateEnd);
		} else {
			// 分文日期
			sqljob.appendSQL(" and a.assign_date >= ?");
			sqljob.addParameter(dateStart);
			sqljob.appendSQL(" and a.assign_date <= ?");
			sqljob.addParameter(dateEnd);
		} // end else
		sqljob.appendSQL(" )");
		sqljob.appendSQL(" order by staff_name");
		if (logger.isInfoEnabled()) logger.info(sqljob);
		return eicmGeneralQueryDao.query(sqljob, BeanPropertyRowMapper.newInstance(Cedbc000.class));
	}

	private EicmGeneralQueryDao eicmGeneralQueryDao;
	public EicmGeneralQueryDao getEicmGeneralQueryDao() {return eicmGeneralQueryDao;}
	public void setEicmGeneralQueryDao(EicmGeneralQueryDao eicmGeneralQueryDao) {this.eicmGeneralQueryDao = eicmGeneralQueryDao;}

}