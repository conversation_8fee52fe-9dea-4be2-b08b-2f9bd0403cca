package com.kangdainfo.tcfi.lucene.service;

import java.util.List;

import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.model.eicm.bo.SearchLog;

/**
 * 檢索查詢
 *
 */
public interface IndexSearchService {

//	/** 輔助查詢 */
//	public SearchResult helpSearch(String queryString, SearchResult sr) throws Exception;

//	/** 全文檢索 */
//	public SearchResult fullTextSearch(String queryString, SearchResult sr) throws Exception;

//	/** 重建索引檔用 */
//	public SearchResult queryAllSearch(String queryString) throws Exception;

	public SearchResult searchPage(String searchType, String keyword,
			Integer pageNum, Integer pageSize, String sortField,
			String sortReverse) throws Exception;

	public SearchResult searchAll(String searchType, String keyword,
			String sortField, String sortReverse) throws Exception;

	public SearchResult searchAll(String searchType, String keyword,
			String sortField, String sortReverse, boolean checkOrgType) throws Exception;

	public SearchResult searchCount(String searchType, String keyword) throws Exception;

	public SearchResult searchRebuild(String keyword) throws Exception;
	
	public List<String> searchPrefixNos(String keyword) throws Exception;

	/**
	 * 新增查詢紀錄
	 * @param obj
	 */
	public void insertSearchLog(SearchLog obj);

}