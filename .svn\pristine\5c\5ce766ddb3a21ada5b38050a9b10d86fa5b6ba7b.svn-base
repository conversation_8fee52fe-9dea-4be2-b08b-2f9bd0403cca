--DROP TABLE EICM.SYSTEM_NEWS;
-- Create table
CREATE TABLE EICM.SYSTEM_NEWS (
	ID INTEGER not null,
	SUBJECT VARCHAR2(200),
	CONTENT VARCHAR2(800),
	START_DATE VARCHAR2(7),
	END_DATE VARCHAR2(7),
	IS_IMPORTANT CHAR(1),
	ENABLE CHAR(1) not null,
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.SYSTEM_NEWS is '系統公告檔';
-- Add comments to the columns 
comment on column EICM.SYSTEM_NEWS.ID is '主鍵值';
comment on column EICM.SYSTEM_NEWS.SUBJECT is '主旨';
comment on column EICM.SYSTEM_NEWS.CONTENT is '內容';
comment on column EICM.SYSTEM_NEWS.START_DATE is '開始日期';
comment on column EICM.SYSTEM_NEWS.END_DATE is '結束日期';
comment on column EICM.SYSTEM_NEWS.IS_IMPORTANT is '重要公告註記(Y:重要,N:一般)';
comment on column EICM.SYSTEM_NEWS.ENABLE is '是否啟用(Y:啟動,N:停用 )';
comment on column EICM.SYSTEM_NEWS.MOD_ID_NO is '異動人員';
comment on column EICM.SYSTEM_NEWS.MOD_DATE is '異動日期';
comment on column EICM.SYSTEM_NEWS.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.SYSTEM_NEWS
  add constraint PK_SYSTEM_NEWS primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_SYSTEM_NEWS_ID;
-- Create sequence 
create sequence EICM.SEQ_SYSTEM_NEWS_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_SYSTEM_NEWS
Before Insert ON EICM.SYSTEM_NEWS Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_SYSTEM_NEWS_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.SYSTEM_NEWS for EICM.SYSTEM_NEWS;

--GRANT
grant all on EICM.SYSTEM_NEWS to EICM4AP;

--DATA
insert into SYSTEM_NEWS (SUBJECT,CONTENT,START_DATE,END_DATE,IS_IMPORTANT,ENABLE) values ('123測試','1234','1030429','1030529','N','Y');
insert into SYSTEM_NEWS (SUBJECT,CONTENT,START_DATE,END_DATE,IS_IMPORTANT,ENABLE) values ('功能異動-103/5/12全文檢索正式上線使用','103/5/12全文檢索正式上線使用，如有問題請洽-2321-2200','1030429','1030529','N','Y');
insert into SYSTEM_NEWS (SUBJECT,CONTENT,START_DATE,END_DATE,IS_IMPORTANT,ENABLE) values ('程式修改-5/10 修正全文檢索查詢邏輯','修正原本程式中的bug.','1030429','1030529','N','Y');


commit;

