package com.kangdainfo.tcfi.model.osss.bo;

import com.kangdainfo.persistence.BaseModel;


/**
 * 規費主檔(OSSM_FEE_MAIN)
 *
 */
public class OssmFeeMain extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子文件號碼 */
	private String telixNo;
	/** 申辦流程代碼 */
	private String processNo;
	/** 收據號碼 */
	private String receiptNo;
	/** 繳費金額 */
	private Integer amount;
	/** 入帳日期 */
	private String accountDate;
	/** 付款日期 */
	private String payDate;
	/** 付款時間 */
	private String payTime;
	/** 付款方式 */
	private Integer payType;
	/** 收據列印日期 */
	private String receiptPrintDate;
	/** 已退費不印收據(Y) */
	private String returnFlag;

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getProcessNo() {
		return processNo;
	}
	public void setProcessNo(String processNo) {
		this.processNo = processNo;
	}
	public String getReceiptNo() {
		return receiptNo;
	}
	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	public Integer getAmount() {
		return amount;
	}
	public void setAmount(Integer amount) {
		this.amount = amount;
	}
	public String getAccountDate() {
		return accountDate;
	}
	public void setAccountDate(String accountDate) {
		this.accountDate = accountDate;
	}
	public String getPayDate() {
		return payDate;
	}
	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
	public Integer getPayType() {
		return payType;
	}
	public void setPayType(Integer payType) {
		this.payType = payType;
	}
	public String getReceiptPrintDate() {
		return receiptPrintDate;
	}
	public void setReceiptPrintDate(String receiptPrintDate) {
		this.receiptPrintDate = receiptPrintDate;
	}
	public String getReturnFlag() {
		return returnFlag;
	}
	public void setReturnFlag(String returnFlag) {
		this.returnFlag = returnFlag;
	}

}