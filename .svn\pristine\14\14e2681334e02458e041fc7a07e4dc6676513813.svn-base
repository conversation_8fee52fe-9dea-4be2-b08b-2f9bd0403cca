<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	Gson gson = new GsonBuilder().create();
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL("SELECT * FROM CEDBC053 order by country_code");
	List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	if (null!=datas && !datas.isEmpty()) {
		out.write(gson.toJson(datas));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>