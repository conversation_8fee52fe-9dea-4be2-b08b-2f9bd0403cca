<!--
程式目的：
程式代號：
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<!doctype html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1003" />
</jsp:include>
<%
if ("update".equals(obj.getState()) || "updateError".equals(obj.getState()))
	obj.update();
	//if ("updateSuccess".equals(obj.getState()))
	//	obj = (com.kangdainfo.tcfi.view.pre.PRE1003)obj.queryOne();
else{
	obj = (com.kangdainfo.tcfi.view.pre.PRE1003)obj.queryOne();
}
%>
<html>
<head>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/>
<%@ include file="../../home/<USER>"%>
<script>
function chkAttachment() {
	if( !!$("input[name=isPrefixForm]:checked").val() ) {
		if( $("input[name=prefixFormNo]").val().length != 9 ) {
			showMsgBar('附件之預查編號必填且須符合規範');
			return false;
		}
	}
	if( !!$("input[name=isOtherSpec]:checked").val() ) {
		if( $("input[name=otherSpecRemark]").val() == '' ) {
			showMsgBar('附件之其他欄位勾選時註記必須填寫');
			return false;
		}
	}
	return true;
}
 
$( document ).ready(function() {
	//var saveResult = '<%=obj.getErrorMsg()%>';
	//if(saveResult == '儲存完成') {
	//	setTimeout('self.close()', 1000);
	//}
	//附件檢核
	$("#doSave").click(function() {
		if(chkAttachment()){
			form1.state.value = 'update';
			form1.submit();	
		}
	});	
});
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='預查案件收文確認(附件維護)'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%">
	<tr>
		<td class="td_form">預查編號</td>
		<td class="td_form_white">
			<input class="field_RO" type="text" name="prefixNo" name="prefixNo" value="<%=obj.getPrefixNo()%>">
		</td>
		<td class="td_form">附件</td>
		<td class="td_form_white">
			<input type="checkbox" id="isPrefixForm" name="isPrefixForm" value="Y" <%="Y".equals(obj.getIsPrefixForm())?"checked":""%> />
			<select class="field" id="docType" name="docType">
				<%=View.getTextOption("1;預查表正本;3;線上申辦檢還申請書;", obj.getDocType(), 0) %>
			</select>
			<input type="text" class="field" id="prefixFormNo" name="prefixFormNo" maxlength="9" size="10" value="<%=obj.getPrefixFormNo()%>"
					onkeyup="if(this.value!=''){$('#isPrefixForm').attr('checked',true);}else{$('#isPrefixForm').removeAttr('checked');};" />
			<input type="checkbox" id="isOtherForm" name="isOtherForm" value="Y" <%="Y".equals(obj.getIsOtherForm())?"checked":""%> />其他機關核准函
			<input type="checkbox" id="isSpec" name="isSpec" value="Y" <%="Y".equals(obj.getIsSpec())?"checked":""%> />說明書
			<input type="checkbox" id="isOtherSpec" name="isOtherSpec" value="Y" <%="Y".equals(obj.getIsOtherSpec())?"checked":""%> />其他
			<input type="text" class="field" id="otherSpecRemark" name="otherSpecRemark" maxlength="500" size="30" value="<%=obj.getOtherSpecRemark()%>"
					onkeyup="if(this.value!=''){$('#isOtherSpec').attr('checked',true);}else{$('#isOtherSpec').removeAttr('checked');};" />
			<input type="hidden" name="prefixNo" name="prefixNo" value="<%=obj.getPrefixNo()%>" />
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		</td>
	</tr>
	<tr>
		<td class="td_form" colspan="4" style="text-align:center">
			<input class="toolbar_default" type="button" value="存檔" id="doSave" />
			<input class="toolbar_default" type="button" value="離開" onClick="window.close()" />
		</td>
	</tr>
	<c:import url="../common/msgbar.jsp">
  		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
	</table>
	</div>
</td></tr>
</table>
</form>
</body>
</html>