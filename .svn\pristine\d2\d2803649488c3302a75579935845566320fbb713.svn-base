package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.util.report.JasperReportMaker;

/**
 * 解釋函登打維護
 *
 */
public class PRE8006 extends SuperBean { 

  private String rcvNo;	    // 收文文號
  private String clickRcvNo ;
  private String rcvTime;   // 收文日期
  private String keynote;   // 主旨
  private String instruction;   // 內文
  private String busiItems;     // 營業項目
  private String isPublish;     // 是否公示
  private String ruleName;      // 法規名稱
  private String ruleCode;      // 法規代碼
  private String ruleNo;        // 法規條號
  private String receiveUnit;
  private String ccUnit;
  private String printWithUnit;
  
  private String Q_rcvNo;
  private String Q_rcvTime;
  private String Q_endRcvTime;
  private String Q_keynote;
  private String Q_instruction;
  
  // ---------------------   getters and setters below---------------------------------------------------------
  // ------------------------------------------------------------------------------------------------------------------
  public String getRcvNo() {return checkGet(rcvNo);}
  public void setRcvNo(String s) {rcvNo = checkGet(s);}
  
  public String getClickRcvNo() {return checkGet(clickRcvNo);}
  public void setClickRcvNo(String s) {clickRcvNo = checkSet(s);}
  
  public String getRcvTime() {return checkGet(rcvTime);}
  public void setRcvTime(String s) {rcvTime = checkSet(s);}
  
  public String getKeynote() {return checkGet(keynote);}
  public void setKeynote(String s) {keynote = checkSet(s);}
  
  public String getInstruction() {return checkGet(instruction);}
  public void setInstruction(String s) {instruction = checkSet(s);}
  
  public String getBusiItems() {return checkGet(busiItems);}
  public void setBusiItems(String s) {busiItems = checkSet(s);}
  
  public String getIsPublish() {return checkGet(isPublish);}
  public void setIsPublish(String s) {isPublish = checkGet(s);}
    
  public String getRuleName() {return checkGet(ruleName);}
  public void setRuleName(String s) {ruleName = checkSet(s);}
  
  public String getRuleCode() {return checkGet(ruleCode);}
  public void setRuleCode(String s) {ruleCode = checkSet(s);}
  
  public String getRuleNo() {return checkGet(ruleNo);}
  public void setRuleNo(String s) {ruleNo = checkSet(s);}
  
  public String getReceiveUnit() {return checkGet(receiveUnit);}
  public void setReceiveUnit(String s) {receiveUnit = checkGet(s);}
  public String getCcUnit() {return checkGet(ccUnit);}
  public void setCcUnit(String s) {ccUnit = checkGet(s);}
  
  public String getPrintWithUnit() {return checkGet(printWithUnit);}
  public void setPrintWithUnit(String s) {printWithUnit = checkSet(s);}
  
  // -----------------------------------------------------------------------------------
  public String getQ_rcvNo() {return checkGet(Q_rcvNo);}
  public void setQ_rcvNo(String s) {Q_rcvNo = checkSet(s);}
  
  public String getQ_rcvTime() {return checkGet(Q_rcvTime);}
  public void setQ_rcvTime(String s) {Q_rcvTime = checkSet(s);}
  
  public String getQ_endRcvTime() {return checkGet(Q_endRcvTime);}
  public void setQ_endRcvTime(String s) {Q_endRcvTime = checkSet(s);}
  
  public String getQ_keynote() {return checkGet(Q_keynote);}
  public void setQ_keynote(String s) {Q_keynote = checkSet(s);}
  
  public String getQ_instruction() {return checkGet(Q_instruction);}
  public void setQ_instruction(String s) {Q_instruction = checkSet(s);}
  
  private static String[] checkedRestrictionItem;
  private String[] rcvNoListWillChange ; 
  
  public String[] getCheckedRestrictionItem() {return checkedRestrictionItem;}
  public void setCheckedRestrictionItem(String[] s) {checkedRestrictionItem = s;}
  public String[] getRcvNoListWillChange() {return rcvNoListWillChange;}
  public void setRcvNoListWillChange(String s[]) {rcvNoListWillChange = s;}
  
  // ------------------------------------------------------------------------------------
  
  private Log log = LogFactory.getLog(this.getClass());
  
  public List<String[]> buildLawList() {
	List<String[]> dataList = new ArrayList<String[]>();
	String[] rowArray = new String[2];
	rowArray[0] = "0860625010";
	rowArray[1] = "公司法";
	dataList.add(rowArray);
	rowArray = new String[2];
	rowArray[0] = "0880707010";
	rowArray[1] = "公司名稱及業務預查審核準則";
	dataList.add(rowArray);
	rowArray = new String[2];
	rowArray[0] = "0890426010";
	rowArray[1] = "商業登記法";
	dataList.add(rowArray);
	rowArray = new String[2];
	rowArray[0] = "0980323010";
	rowArray[1] = "商業名稱及所營業務預查審核準則";
	dataList.add(rowArray);
	return dataList;
  } 
  
  public List<String[]> buildPublishList() {
		List<String[]> dataList = new ArrayList<String[]>();
		String[] rowArray = new String[2];
		rowArray[0] = "N";
		rowArray[1] = "未公示";
		dataList.add(rowArray);
		rowArray = new String[2];
		rowArray[0] = "1";
		rowArray[1] = "已公示於代碼系統";
		dataList.add(rowArray);
		rowArray = new String[2];
		rowArray[0] = "2";
		rowArray[1] = "已公示於法規系統";
		dataList.add(rowArray);
		rowArray = new String[2];
		rowArray[0] = "3";
		rowArray[1] = "以公示於法規與代碼系統";
		dataList.add(rowArray);
		return dataList;
	  } 
  
  public String getOption(List<String[]> list, String selectedOne, boolean limitLen, int defaultChoice, String event) {
      StringBuilder sb = new StringBuilder();
      if (defaultChoice==1) sb.append("<option value=''>無</option>");
      else if (defaultChoice==2) sb.append("<option value=''></option>");        
      try {
          	if (list!=null && list.size()>0) {
          		String[] obj;
          		for (int i=0; i<list.size(); i++) {
          			  obj = list.get(i);
                      String code = obj[0];
                      String name = obj[1];
                      
                      sb.append("<option value='").append(code).append("' ").append(event);
                      if (selectedOne!= null && selectedOne.equals(code)) {
                          sb.append(" selected ");
                      }
                      if (limitLen && Common.get(name).length()>60) sb.append(">").append(name.substring(0,60)).append("..</option>\n");
                      else sb.append(">").append(name).append("</option>\n");
          		}
          	}        	
      } catch (Exception ex) {
          sb.append("<option value=''>查詢錯誤</option>");
          ex.printStackTrace();
      }
      return sb.toString();
  }
    
  public void doCreate() throws Exception{
	// 1. 依使用者輸入之帳號置資料庫中查詢有無該筆資料
	// 2. 若該帳號已存在表重複輸入, 釋出錯誤訊息
	// 3. 若無則建立新物件obj, 將使用者所輸入之資訊存於此中
	// 4. 將obj丟回資料庫
	  
	  
	  if("".equals(Common.get(getRcvNo() ) ) ) 
        throw new MoeaException("公文文號欄位不可空白，請重新輸入!!");
	  
	  if ( ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(getRcvNo()) != null ) {
		throw new MoeaException( "該筆公文文號已存在，請重新輸入" );
	  }
	  
	  DeclaratoryStatutes obj = new DeclaratoryStatutes();
	  List<DeclaratoryStatutesRcver> dsrList = new ArrayList<DeclaratoryStatutesRcver>();
      DeclaratoryStatutesRcver dsr ;
	  obj.setRcvNo(Common.get(getRcvNo())) ;
	  obj.setRcvTime(Common.get(getRcvTime()));
	  obj.setKeynote(Common.get(getKeynote()));
	  String[] instructions = Common.get(getInstruction()).split("\n");
	  for (int i = 0; i<instructions.length;i++) {
		  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("0");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(instructions[i]);
    	  dsrList.add(dsr);
	  }
	  obj.setInstruction("");
	  obj.setBusiItems( Common.get(getBusiItems()) );
	  obj.setIsPublish( (Common.get(getIsPublish())) );
	  obj.setInsertDate( Datetime.getYYYMMDD() );
	  obj.setInsertTime( Datetime.getHHMMSS() );
	  obj.setInsertUser( getLoginUserId() );
	  obj.setUpdateDate( "" );
	  obj.setUpdateTime( "" );
	  obj.setUpdateUser( "" );
	  obj.setRuleCode( Common.get(getRuleCode()) );
	  obj.setRuleNo( Common.get(getRuleNo()) );
	  obj.setReceiveUnit("");
	  obj.setCcUnit("");
      String[] rcver1 = Common.get(getReceiveUnit()).split("、");
      String[] rcver2 = Common.get(getCcUnit()).split("、");
      
      for (int i=0; i<rcver1.length;i++) {
    	  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("1");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(rcver1[i]);
    	  dsrList.add(dsr);
      }
      for (int i=0; i<rcver2.length;i++) {
    	  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("2");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(rcver2[i]);
    	  dsrList.add(dsr);
      }
      
      log.info("create : "+obj.toString());
      log.info("create : "+dsrList.toString());
      ServiceGetter.getInstance().getPre8006Service().doInsert(obj,dsrList);
      this.setClickRcvNo(obj.getRcvNo());
  } // end doCreate()
  
  
  
  
  public void doUpdate() throws Exception{
	// 建立新物件obj, 並將資料庫中相應之資料存入其中
	// 依使用者輸入之資訊更改obj之內容
	// 將obj丟回資料庫
	  String oldRcvNo = "";
	  if("".equals(Common.get(getClickRcvNo())) ) 
		  throw new MoeaException("修改失敗，請洽系統維運人員！");
	  DeclaratoryStatutes obj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(getRcvNo());
	  DeclaratoryStatutes oldObj = new DeclaratoryStatutes();
	  if(obj != null && !Common.get(obj.getRcvNo()).equals(Common.get(getClickRcvNo()))){
		  throw new MoeaException( "該筆公文文號已存在，請重新輸入" );
	  }else{
		  obj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(getClickRcvNo());
	  }
      if ( obj == null )
    	  throw new MoeaException( "沒有該筆公文文號，請重新輸入" );

      List<DeclaratoryStatutesRcver> dsrList = new ArrayList<DeclaratoryStatutesRcver>();
      DeclaratoryStatutesRcver dsr ;
      oldRcvNo = getClickRcvNo();
      oldObj.setRcvNo(oldRcvNo);
      obj.setId(getClickRcvNo());
	  obj.setRcvNo(getRcvNo()) ;
	  obj.setRcvTime(Common.get(getRcvTime()));
	  obj.setKeynote(Common.get(getKeynote()));
	  String instructions[] = Common.get(getInstruction()).split("\n");
	  for (int i= 0; i<instructions.length;i++) {
		  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("0");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(instructions[i]);
    	  dsrList.add(dsr);
	  }
	  
	  obj.setBusiItems( Common.get(getBusiItems()) );
	  obj.setIsPublish((Common.get(getIsPublish())));
	  obj.setUpdateDate( Datetime.getYYYMMDD() );
	  obj.setUpdateTime( Datetime.getHHMMSS() );
	  obj.setUpdateUser( getLoginUserId() );
	  obj.setRuleCode( Common.get(getRuleCode()) );
	  obj.setRuleNo( Common.get(getRuleNo()) );
	  obj.setReceiveUnit("");
	  obj.setCcUnit("");
	  obj.setInstruction("");
	  String[] rcver1 = Common.get(getReceiveUnit()).split("、");
      String[] rcver2 = Common.get(getCcUnit()).split("、");
      for (int i=0; i<rcver1.length;i++) {
    	  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("1");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(rcver1[i]);
    	  dsrList.add(dsr);
      }
      for (int i=0; i<rcver2.length;i++) {
    	  dsr = new DeclaratoryStatutesRcver();
    	  dsr.setRcvNo(obj.getRcvNo());
    	  dsr.setRcverType("2");
    	  dsr.setSeqNo(i);
    	  dsr.setRcverOrg(rcver2[i]);
    	  dsrList.add(dsr);
      }
      
      log.info("update : "+obj.toString());
      log.info("update : "+dsrList.toString());
	  ServiceGetter.getInstance().getPre8006Service().doUpdate(oldObj,obj,dsrList);
	  this.setClickRcvNo(obj.getRcvNo());
  } // end doUpdate()		

  public void doDelete() throws Exception{			
	    DeclaratoryStatutes obj = new DeclaratoryStatutes();
		obj.setRcvNo(getClickRcvNo());
		List<DeclaratoryStatutes> objList = ServiceGetter.getInstance().getPrefixService().queryDeclaratoryStatutes(obj, null);
		if(objList != null && objList.size() > 0){
			obj = objList.get(0);
			ServiceGetter.getInstance().getPre8006Service().doDelete(obj);
		}else{
			throw new MoeaException("查無資料，無法刪除，請重新操作 !");
		}
		
		log.info("delete : "+obj.toString());
        // ServiceGetter.getInstance().getPrefixService().deleteDeclaratoryStatutes(obj);		
  } // end doDelete()	

  public Object doQueryOne() throws Exception{ 
    PRE8006 obj = this ;
    DeclaratoryStatutes r = new DeclaratoryStatutes() ;
    r.setRcvNo(getClickRcvNo()) ;
    
    DeclaratoryStatutes s = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(r.getRcvNo());
    List<DeclaratoryStatutesRcver> dsrList = ServiceGetter.getInstance().getPrefixService().queryDeclaratoryStatutesRcver(r);
    if (s!=null){
		obj.setRcvNo(s.getRcvNo());
		obj.setRcvTime(s.getRcvTime());
		obj.setKeynote(s.getKeynote());
		obj.setRuleCode(s.getRuleCode());
		obj.setRuleNo(s.getRuleNo());
		obj.setBusiItems(s.getBusiItems());
		obj.setIsPublish((s.getIsPublish()));
		String rcver1 = "";
		String rcver2 = "";
		String instructions = "";
		if ( dsrList != null && dsrList.size() != 0 ) {
			for (int i = 0;i<dsrList.size();i++ ) {
				if ("0".equals(Common.get(dsrList.get(i).getRcverType()))) {
					if ("".equals(instructions))
						instructions+=Common.get(dsrList.get(i).getRcverOrg());
					else
						instructions+="\n"+Common.get(dsrList.get(i).getRcverOrg());
				} 
			    else if ( "1".equals(Common.get(dsrList.get(i).getRcverType()))) {
					if ("".equals(rcver1))
						rcver1+=Common.get(dsrList.get(i).getRcverOrg());
					else
						rcver1+="、"+Common.get(dsrList.get(i).getRcverOrg());
				}
				else {
					if ("".equals(rcver2))
						rcver2+=Common.get(dsrList.get(i).getRcverOrg());
					else
						rcver2+="、"+Common.get(dsrList.get(i).getRcverOrg());
				}
			}
		}
		obj.setInstruction(instructions);
		obj.setReceiveUnit(rcver1);
		obj.setCcUnit(rcver2);	
	}  
    else {
    	obj.setRcvNo("");
		obj.setRcvTime("");
		obj.setKeynote("");
		obj.setInstruction("");
		obj.setRuleCode("");
		obj.setRuleNo("");
		obj.setBusiItems("");
		obj.setIsPublish("");
		obj.setReceiveUnit("");
		obj.setCcUnit("");
    	
    }
	return obj;
  } // end doQueryOne()

	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
		try {
			java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>() ;
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" RCV_NO");
			sqljob.appendSQL(",RCV_TIME");
			sqljob.appendSQL(",KEYNOTE");
			sqljob.appendSQL("FROM DECLARATORY_STATUTES");
			sqljob.appendSQL("WHERE 1=1");
			if(!"".equals(Common.get(getQ_rcvNo()))){
				sqljob.appendSQLCondition("RCV_NO LIKE ?");
				sqljob.addLikeParameter(getQ_rcvNo());
			}
			if(!"".equals(Common.get(getQ_rcvTime()))){
				sqljob.appendSQLCondition("RCV_TIME >= ?");
				sqljob.addParameter(getQ_rcvTime());
			}
			if(!"".equals(Common.get(getQ_endRcvTime()))){
				sqljob.appendSQLCondition("RCV_TIME <= ?");
				sqljob.addParameter(getQ_endRcvTime());
			}
			if(!"".equals(Common.get(getQ_keynote()))){
				//主旨與內容2個地方都要查
				sqljob.appendSQL("AND (");
				sqljob.appendSQL("KEYNOTE LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("INSTRUCTION LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("RCV_NO IN (");
				sqljob.appendSQL("SELECT RCV_NO FROM DECLARATORY_STATUTES_RCVER WHERE RCVER_TYPE='0' AND RCVER_ORG LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL(")");
				sqljob.appendSQL(")");
			}
			if(!"".equals(Common.get(getQ_instruction()))){
				//主旨與內容2個地方都要查
				sqljob.appendSQL("AND (");
				sqljob.appendSQL("KEYNOTE LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("INSTRUCTION LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("RCV_NO IN (");
				sqljob.appendSQL("SELECT RCV_NO FROM DECLARATORY_STATUTES_RCVER WHERE RCVER_TYPE='0' AND RCVER_ORG LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL(")");
				sqljob.appendSQL(")");
			}
			sqljob.appendSQL(" ORDER BY RCV_TIME DESC, RCV_NO ASC");
			
			List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[4];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[4];
					rowArray[0] = Common.get(o.get("RCV_NO"));
					rowArray[1] = Common.get(o.get("RCV_TIME"));
					rowArray[2] = Common.get(o.get("KEYNOTE")).length() > 60  ? Common.get(o.get("KEYNOTE")).substring(0,59) : Common.get(o.get("KEYNOTE"))  ;
					rowArray[3] = Common.get(o.get("IS_PUBLISH"));
					arrList.add(rowArray);	
				}
				return arrList;
			} else {
				throw new MoeaException( "查無資料(NO DATA FOUND)，請變更查詢條件" ) ;
			}
		} catch (MoeaException e) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		}
	} // end doQueryAll
  
  public static String getQuerylist(boolean primaryArray[], boolean displayArray[], String[] arrAlign,
  		ArrayList<String[]> objList, String queryAllFlag, boolean withListNo, boolean linkArray[], String target,String noDataMsg,boolean checkHtml, 
  		boolean defaultRow, int column, boolean disable, String checkboxName) {
  	int i;
  	boolean trFlag = false, targetFlag = false, even = false;
  	String pk = "";
  	StringBuilder sb = new StringBuilder();
  	if (objList!=null && objList.size()>0) {
			String rowArray[]=new String[primaryArray.length];
			java.util.Iterator<String[]> it = objList.iterator();
			
			//boolean defaultRow = true;
			String defaultKey = "null";
			for(i=0;i<primaryArray.length;i++){
				if (primaryArray[i]) defaultKey = "";
			}

			while(it.hasNext()) {
				rowArray= it.next();
				String classTR="listTROdd", classTD = "listTDOdd";				
				if (even) {
					classTR = "listTREven";
					classTD = "listTDEven";
				}				
			
				pk = "";
				for(i=0;i<primaryArray.length;i++){			
					if (primaryArray[i]) pk+=Common.escapeReturnChar(rowArray[i]);
				}				
				StringBuilder v = new StringBuilder().append(defaultKey);
				for(i=0;i<primaryArray.length;i++){
					if (primaryArray[i]) {
						if (trFlag) {
							v.append(",'").append(rowArray[i]).append("'");
						} else {
							v.append("'").append(rowArray[i]).append("'");
							trFlag = true;
						}
					}
				}
				if (targetFlag==false && target!=null && !"".equals(Common.get(target))) {
					v.append(",'").append(target).append("'");
					targetFlag = true;
				}					
				
				//顯示TR
				if (linkArray!=null) {
					sb.append(" <tr class='highLight' >");
				} else {
					sb.append(" <tr id=\"").append("listContainerRow").append(pk).append("\"");
					sb.append(" class='").append(classTR).append("' onmouseover=\"this.className='listTRMouseover'\" onmouseout=\"this.className='").append(classTR).append("'\" onClick=\"listContainerRowClick('").append(pk).append("');\" >\n");	
				}
				
				String dc="";
				if(column >= 0 && column < displayArray.length){
					if(( disable && Common.get(rowArray[column]).length() > 0) ||
					   (!disable && Common.get(rowArray[column]).equals(""))){
						dc = "disabled";
					}
				}
				//顯示TD
				
				if (withListNo) sb.append(" <td class='listTD' >").append("<input class=\"field_Q\" type='checkbox' ").append(dc).append(" id=\"").append(checkboxName).append("\" name=\"").append(checkboxName).append("\" value=\"").append(Common.checkGet(rowArray[0])).append("\" ").append("></td>\n"); //sb.append(" <td class='").append(classTD).append("' style=\"text-align:right\">").append(counter).append("</td>\n");			
				targetFlag = false;
				for(i=0;i<displayArray.length;i++){
					if (displayArray[i]) {
						if (arrAlign!=null && arrAlign.length>0) {
							sb.append(" <td class='").append(classTD).append("' style=\"text-align:").append(arrAlign[i]).append("\"").append("onClick=\"queryOne(").append(v).append(");").append("\">"); //.append(Common.get(rowArray[i])).append("</td>\n");
						} else {
							sb.append(" <td class='").append(classTD).append("' >");
						}
						if (linkArray!=null && linkArray[i]) {
							sb.append("<a href='#' class='sLink2' onClick=\"listContainerRowClick('").append(pk).append("');queryOne(").append(v).append(",").append(i).append(")\"");
							sb.append(">").append(checkHtml?Common.checkGet(rowArray[i]):Common.get(rowArray[i])).append("</a>");
						} else sb.append(checkHtml?Common.checkGet(rowArray[i]):Common.get(rowArray[i]));
						
						sb.append("</td>\n");
					}
				}
				sb.append("</tr>\n");
				trFlag = false;
				even = !even;
			}
  	} else {
  		if ("true".equals(queryAllFlag)) sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>").append("".equals(Common.get(noDataMsg))?"查無資料，請您重新輸入查詢條件！":noDataMsg).append("</td></tr>");
  	}
		return sb.toString();
  }
  
  public File doPrintPdf() throws Exception {
	  File report = null ;  
	  try { 
		  boolean printWithUnit = false;
		  if ( "true".equals(getPrintWithUnit()) )
			  printWithUnit = true;
	      String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre8006.jasper");
	      Map<String, Object> parameters = new HashMap<String,Object>();
	      parameters.put("printDate", Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印日期
		  parameters.put("printTime", Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間
		  parameters.put("printWithUnit",printWithUnit);
		  ArrayList<PRE8006> dataList = new ArrayList<PRE8006>();
		  PRE8006 o = this;
		  o.setRcvTime(Common.formatYYYMMDD(o.getRcvTime(),2));
		  dataList.add(o);

		  report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	  } // end try
	  catch( Exception e ) {
	      e.printStackTrace();
	      if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
		  else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	  } // end catch
		
	  return report ;
  } // doPrintfPdf()
 
  public static int str2intYear( String strSeven ) {
	  int year=0;
	  if (strSeven == null || strSeven.length() == 0) {
	      return 0;
	  }
	  StringBuffer sb = new StringBuffer(strSeven.trim());
	  try {
	    Integer.parseInt(sb.toString(), 10);
	  }
	  catch (Exception e) {
	    //包含非數字, 格式不合
	    e.printStackTrace();
	  }
	  if (sb.length() == 0) {
	     return 0;
	  }
	  else if (sb.length() == 7) {
	     year= 1911+Integer.parseInt(sb.substring(0,3), 10);
	     return year;
	  }
	  else {
	      //格式不合
	      return 0;
	  }
  }
  
  public static String str2DateStr(String strSeven) throws
  IllegalArgumentException {
  if (strSeven == null || strSeven.length() == 0) {
    return "";
  }

  StringBuffer sb = new StringBuffer(strSeven.trim());
  IllegalArgumentException iae = new IllegalArgumentException();
  try {
    Integer.parseInt(sb.toString(), 10);
  }
  catch (Exception e) {
    //包含非數字, 格式不合
    throw iae;
  }
  if (sb.length() == 0) {
    return "";
  }
  else if (sb.length() == 7) {
    sb.insert(3, "/");
    sb.insert(6, "/");
    return sb.toString();
  }
  else {
    //格式不合
    throw iae;
  }

}
  
  public void doPublishCode() throws MoeaException{
	  try {
		  
		  DeclaratoryStatutes obj = new DeclaratoryStatutes();
		  DeclaratoryStatutes oldObj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(getRcvNo());
		  if("1".equals(oldObj.getIsPublish())) {
			  setErrorMsg("該解釋函已被公示於代碼系統，如要修改內容請聯絡代碼系統人員");
			  throw new MoeaException("該解釋函已被公示於代碼系統，如要修改內容請聯絡代碼系統人員");
		  } // if
		  else if("3".equals(oldObj.getIsPublish())) {
			  setErrorMsg("該解釋函已被公示於法規與代碼系統，如要修改內容請聯絡法規系統人員");
			  throw new MoeaException("該解釋函已被公示於法規與代碼系統，如要修改內容請聯絡法規系統人員");
		  } // if
		  obj.setRcvNo(getRcvNo()) ;
		  obj.setBusiItems( Common.get(getBusiItems()) );
		  
		  if ("2".equals(oldObj.getIsPublish()))
			  obj.setIsPublish( "3" );
		  else
			  obj.setIsPublish( "1" );
		  obj.setUpdateDate( Datetime.getYYYMMDD() );
		  obj.setUpdateTime( Datetime.getHHMMSS() );
		  obj.setUpdateUser( getLoginUserId() );
		  ServiceGetter.getInstance().getPrefixService().saveWhenPublish(obj);
		  /*
		  if ( !codePublish( obj.getRcvTime(),obj.getRcvNo(),obj.getKeynote(), obj.getInstruction(),obj.getBusiItems()
				            ,getLoginUserName(),obj.getIsPublish()) ) {
			  setErrorMsg("公示時發生錯誤，請洽系統維運人員");
			  throw new MoeaException("公示時發生錯誤，請洽系統維運人員");
		  } // if
		  */
		  setState("publishCodeSuccess");
		  setErrorMsg("代碼公示成功");
	  } // try
	  catch ( MoeaException e ) {
		  e.printStackTrace();
		  setState("publishCodeError");
		  setErrorMsg(e.getMessage());
	  } // catch
  } // publishCode
  
  public static boolean codePublish( String publishDate, String desNum, String desAbstract, String desContent, 
		  							 String businessItem, String user, String isPublish ) {
	  try {
	  if (publishDate.length() != 7)
			return false;
	  	  int year = str2intYear(publishDate);
	  	  String date = "" + year + str2DateStr(publishDate).substring(3);

	  	  DataSender sender = DataSender.getInstance();
	  	  sender.setRequestURL("http://210.202.24.161"+"/code/CodeProcessServlet");
	  	  sender.addParameter("publishDate", date); //發布日期
	  	  sender.addParameter("desNum", desNum); //函釋字號
	  	  sender.addParameter("desAbstract", desAbstract); //函釋摘要
	  	  //函釋內容
	  	  sender.addParameter("desContent", desContent);
	  	  //營業項目
	  	  sender.addParameter("businessItem", businessItem);
	  	  sender.addParameter("lastModifier", user); //最後異動者
	  	  sender.send();
	  	  return true;
	  } // try
	  catch( Exception e ) {
		  e.printStackTrace();
		  return false;
	  } // catch
	  
  } // codePubkish()
  
  public void doPublishLaw() throws MoeaException{
	  try {
		  
		  DeclaratoryStatutes obj = new DeclaratoryStatutes();
		  DeclaratoryStatutes oldObj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(getRcvNo());
		  if("2".equals(oldObj.getIsPublish())) {
			  setErrorMsg("該解釋函已被公示於法規系統，如要修改內容請聯絡法規系統人員");
			  throw new MoeaException("該解釋函已被公示於法規系統，如要修改內容請聯絡法規系統人員");
		  } // if
		  else if ("3".equals(oldObj.getIsPublish())) {
			  setErrorMsg("該解釋函已被公示於代碼與法規系統，如要修改內容請聯絡法規系統人員");
			  throw new MoeaException("該解釋函已被公示於代碼與法規系統，如要修改內容請聯絡法規系統人員");
		  } // if
		  obj.setRcvNo(getRcvNo());
		  
		  if ("1".equals(oldObj.getIsPublish()))
			  obj.setIsPublish( "3" );
		  else {
			  obj.setIsPublish( "1" );
		  } // else
		  obj.setUpdateDate( Datetime.getYYYMMDD() );
		  obj.setUpdateTime( Datetime.getHHMMSS() );
		  obj.setUpdateUser( getLoginUserId() );
		  ServiceGetter.getInstance().getPrefixService().saveWhenPublish(obj);
		  /*
		  if ( !lawPublish( obj.getRuleCode(),obj.getRuleNo(),obj.getRcvTime(), obj.getRcvNo(),obj.getKeynote()
				  			,obj.getInstruction(),getLoginUserName(),obj.getIsPublish()) ) {
			  setErrorMsg("公示時發生錯誤，請洽系統維運人員");
			  throw new MoeaException("公示時發生錯誤，請洽系統維運人員");
		  } // if
		  */
		  setState("publishLawSuccess");
		  setErrorMsg("法規公示成功");
	  } // try
	  catch ( MoeaException e ) {
		  e.printStackTrace();
		  setState("publishLawError");
		  setErrorMsg(e.getMessage());
	  } // catch
  } // publishLaw
  
  public static boolean lawPublish(String ruleCode, String ruleNo, String publishDate, String desNum, 
		   						   String desAbstract, String desContent, String user, String isPublish ) {
	  try {
		  if (publishDate.length() != 7)
				return false;
		  	  int year = str2intYear(publishDate);
		  	  String date = "" + year + str2DateStr(publishDate).substring(3);

		  	  DataSender sender = DataSender.getInstance();
		  	  sender.setRequestURL("http://210.202.24.161"+"/elaw/RuleProcessServlet");
		  	  sender.addParameter("ruleCode", ruleCode); //法規代碼
		  	  //法規條號
		  	  sender.addParameter("ruleNo", ruleNo);
		  	  sender.addParameter("publishDate", date); //發布日期
		  	  sender.addParameter("desNum", desNum); //函釋字號
		  	  sender.addParameter("desAbstract", desAbstract); //函釋摘要
		  	  //函釋內容
		  	  sender.addParameter("desContent", desContent);
		  	  sender.addParameter("lastModifier", user); //最後異動者
		  	  sender.send();
		  	  return true;
		  } // try
		  catch( Exception e ) {
			  e.printStackTrace();
			  return false;
		  } // catch
  } // lawPublish()
  
  public void doBatchPublishCode() {
	  try {
		  if ( rcvNoListWillChange != null && rcvNoListWillChange.length > 0 ) {
			  DeclaratoryStatutes obj;
			  for ( int i = 0; i<rcvNoListWillChange.length; i++ ) {
				  obj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(rcvNoListWillChange[i]);
				  System.out.println("---"+obj.getIsPublish()+"---");
				  if ( "1".equals(obj.getIsPublish()) || "3".equals(obj.getIsPublish()))  {
					  setErrorMsg(getErrorMsg() + obj.getRcvNo()+"已公示於代碼系統　");
				  } // if
				  else {
					  if ( obj.getBusiItems()!= null && !"".equals(obj.getBusiItems()) ) {
						  if ("2".equals(obj.getIsPublish()))
							  obj.setIsPublish("3");
						  else 
							  obj.setIsPublish("1");
						  obj.setUpdateDate( Datetime.getYYYMMDD() );
						  obj.setUpdateTime( Datetime.getHHMMSS() );
						  obj.setUpdateUser( getLoginUserId() );
						  ServiceGetter.getInstance().getPrefixService().saveWhenPublish(obj);
						  /*if (!codePublish( obj.getRcvTime(),obj.getRcvNo(),obj.getKeynote(), obj.getInstruction(),obj.getBusiItems()
					            	   ,getLoginUserName(),obj.getIsPublish())) {
							  setErrorMsg("公示時發生錯誤，請洽系統維運人員");
							  throw new MoeaException("公示時發生錯誤，請洽系統維運人員");
						  } // if */
					  } // if
					  else {
						  setErrorMsg(getErrorMsg() + obj.getRcvNo()+"的營業項目代碼沒有輸入!　");
					  } // else
				  } // else
			  } // for
			  
			  setErrorMsg(getErrorMsg() + "批次代碼公示成功");
			  setState("batchPublishCodeSuccess");
		  } // if
		  else {
			  setErrorMsg("請先選擇解釋函");
			  throw new MoeaException("請先選擇解釋函");
		  } // else
	  } // try
	  catch( MoeaException e ) {
		  e.printStackTrace();
		  setState("batchPublishCodeError");
		  setErrorMsg(e.getMessage());
	  } // catch
  } // doBatchPublishCode()
  
  public void doBatchPublishLaw() {
	  try {
		  if ( rcvNoListWillChange != null && rcvNoListWillChange.length > 0 ) {
			  DeclaratoryStatutes obj;
			  for ( int i = 0; i<rcvNoListWillChange.length; i++ ) {
				  obj = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(rcvNoListWillChange[i]);
				  if ("2".equals(obj.getIsPublish()) || "3".equals(obj.getIsPublish()) ) {
					  setErrorMsg(getErrorMsg() + obj.getRcvNo()+"已公示於法規系統　");
				  } // if
				  else {
					  if ( "N".equals( obj.getIsPublish() ) || "1".equals( obj.getIsPublish() ) ) {
						  if ( obj.getRuleCode() != null && obj.getRuleNo() != null && !"".equals(obj.getRuleCode()) && !"".equals(obj.getRuleNo()) ) {
							  if ("1".equals(obj.getIsPublish()))
								  obj.setIsPublish("3");
							  else
								  obj.setIsPublish("2");
							  obj.setUpdateDate( Datetime.getYYYMMDD() );
							  obj.setUpdateTime( Datetime.getHHMMSS() );
							  obj.setUpdateUser( getLoginUserId() );
							  ServiceGetter.getInstance().getPrefixService().saveWhenPublish(obj);
							  /*if (!lawPublish( obj.getRuleCode(),obj.getRuleNo(),obj.getRcvTime(), obj.getRcvNo(),obj.getKeynote()
									  		  ,obj.getInstruction(),getLoginUserName(),obj.getIsPublish())) {
								  setErrorMsg("公示時發生錯誤，請洽系統維運人員");
								  throw new MoeaException("公示時發生錯誤，請洽系統維運人員");
							  } // if */
						  } // if
						  else {
							  setErrorMsg(getErrorMsg() + obj.getRcvNo()+"的法規名稱或是法規條號沒有輸入!　");
						  } // else
					  } // if
				  } // else
			  } // for
			  
			  setErrorMsg(getErrorMsg() + "批次法規公示成功");
			  setState("batchPublishLawSuccess");
		  } // if
		  else {
			  setErrorMsg("請先選擇解釋函");
			  throw new MoeaException("請先選擇解釋函");
		  } // else
	  } // try
	  catch( MoeaException e ) {
		  e.printStackTrace();
		  setState("batchPublishLawError");
		  setErrorMsg(e.getMessage());
	  } // catch
	  
  } // doBatchPublishLaw()
  
  
  public void convert()  {
	  ServiceGetter.getInstance().getPre8006Service().doConvert();
  }
  public void convertInstruction()  {
	  ServiceGetter.getInstance().getPre8006Service().doConvertInstruction();
	  
  }
  
    
} // PRE8008