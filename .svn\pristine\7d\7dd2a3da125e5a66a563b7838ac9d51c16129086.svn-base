<!-- 
程式目的：待辦案件清單
程式代號：pre3010
程式日期：1030627
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3012">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3012" />
</jsp:include>
<%
if ( "assignToMe".equals(obj.getState()) ) {
	obj.doAssignToMe();
} // if
objList = obj.doQueryAll3010();
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3010"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_prefixEnd,"預查編號迄");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	if ($("#errorMsg").val().indexOf("預查業務已移交") > -1 ) {
		alert($("#errorMsg").val());
	}
	$.post( getVirtualPath() + "tcfi/ajax/jsonPre3010ChkWorkday.jsp", function( json ) {
		if(null!=json) {
			var workdays;
			$.each(json, function(k, v) {
				if(v) {
					workdays = v.split("-");
					if(workdays.length == 2) {
						$("#listContainerRow" + k).find("td").eq(6).text(workdays[0]).next().next().next().text(workdays[1]);
					}
				}
			});
		}
	});
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;				
				break;
			case "doUpdate":
				$('#state').val("update") ;	
				setBeforePageUnload(false) ;
				form1.submit();
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "assignToMe":
				$('#state').val("assignToMe") ;
				form1.submit();
				break;	
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function popWindow1(id) {	
	var prop="";
	var width = window.screen.width;
	var height = window.screen.height;
	var top = (window.screen.height);
	var left = (window.screen.width );

	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	if (id == "button1") {
		var url = getVirtualPath() + "tcfi/pre/pre4001.jsp";
		window.open(url,'pre4001',prop);
	}
	else if (id == "button2") {
		var url = getVirtualPath() + "tcfi/pre/pre3002.jsp";
		window.open(url,'pre3002',prop);
	}
	else if (id == "button3") {
		var url = getVirtualPath() + "tcfi/pre/pre3008.jsp";
		window.open(url,'pre3008',prop);
	}
}

function queryOne(prefixNo) {
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	var url = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo="+prefixNo +"&hiddenPrefixNos="+prefixNo;
	window.open(url,'pre3001',prop).focus();
}

function doSearch() {
	var caseChecked = "";
	var count = 0;
    var checkboxs = document.getElementsByName("checkedPrefixNo");
	for(var i = 0; i<checkboxs.length;i++) {
		
		if ( checkboxs[i].checked == true ) {
			caseChecked += checkboxs[i].value;
			caseChecked += "-";
			count++;
		} // if
		
	}
	if (count == 0) {
		alert("請先勾選欲查詢的預查編號");
		return false;
	}
	
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;
	prop+="left=0,top=0";
	prop+=",width="+(screen.width);
	prop+=",height="+(screen.height*0.96);
	prop+=",scrollbars=1";
	prop+=",resizable=1";
	prop+=",toolbar=0";
	prop+=",menubar=0";
	prop+=",directories=0";
	prop+=",status=1";
	prop+=",location=0";
	/*
	var url = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo="+caseChecked+"&hiddenPrefixNos="+caseChecked;
	window.open(url,'pre3001',prop).focus();
	*/
	var prefixNos = caseChecked.split("-");
	form1.hiddenPrefixNos.value = caseChecked;
	form1.prefixNo.value = prefixNos[0];
	form1.state.value = "init";
	form1.target = "_blank";
	form1.action = getVirtualPath() + "tcfi/pre/pre3001_00.jsp";
	form1.submit();
	form1.action = "";
	form1.target = "";
	
	
}

function oneClickCheckAll(obj,cName) { 
    var checkboxs = document.getElementsByName(cName); 
    for(var i=0;i<checkboxs.length;i++){checkboxs[i].checked = obj.checked;} 
} 

function windowClose() {
	window.close();
}
</script>
</head>
<body topmargin="5" >
<form id="form1" name="form1" method="post" onSubmit="">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3010'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg">
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" >
		<tr>
			<td class="td_form_white" width="180px" align="right">
			<%=obj.getStaffName()%>
			</td>
			<td class="td_form_white" align="right">
				<input class="toolbar_default" type="button" id="button0" name="assignToMe" value="分文" onClick="whatButtonFireEvent(this.name)" >
				<input class="toolbar_default" type="button" id="buttonSubmit" name="" value="重新整理" onClick="form1.submit();" >
				<input class="toolbar_default" type="button" id="buttonQuery" name="" value="確認送出" onClick="doSearch();" >
	    	<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value='PRE4001,PRE3002,PRE3008,PRE3013'/>
			</c:import>
			</td>
		</tr>
	</table>
	</div>
</td></tr>

<tr><td class="bg" >
  <div id="listContainer" height = "200">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" style="text-align:left;border:0px;" width="3%"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">NO.</a></th>
    		<th class="listTH" style="text-align:center;border:0px;" width="5%">
    			<input type="checkbox" id="checkAll" name="checkAll" value="Y" onClick="oneClickCheckAll(this,'checkedPrefixNo');">
    		</th>
    		<th class="listTH" style="text-align:left;border:0px;" width="13%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查編號</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" ><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">預查名稱</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="8%"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">預查種類</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="13%"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">申請人</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="9%"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">承辦工作<br>天數</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="18%"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">分文時間</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="9%"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">展期原因</a></th>
    		<th class="listTH" style="text-align:left;border:0px;" width="9%"><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">審核工作<br>時數</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = {true,false,false,false,false,false,false,false};
  			boolean displayArray[] = {true,true,true,true,true,true,true,true};
  			String[] alignArray = {"left", "left","left","left","left","left","left","left"};
  			out.write(obj.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",true,true,null,null,"",true,true,"checkedPrefixNo"));
  			%>
  		</tbody>
	</table>
  </div>
</td></tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
	<tr><td style="text-align:center;">
	<inpuy type="hidden" id="errorMsg" name="errorMsg" value="<%=obj.getErrorMsg()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="hiddenPrefixNos" name="hiddenPrefixNos" value="">
	<input type="hidden" id="prefixNo" name="prefixNo" vaues="">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
	</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>