package com.kangdainfo.tcfi.loader;

/**
 * 取件方式
 *
 */
public class SystemCode03Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_03";
	private static final String CODE_KIND = "03";//03:取件方式
	//singleton
	private static SystemCode03Loader instance;
	public SystemCode03Loader() {
		if (SystemCode03Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode03Loader.instance);
		}
		SystemCode03Loader.instance = this;
	}
	public static SystemCode03Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}