package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.report.JasperReportMaker;

/*
程式目的：公司名稱預查規費日報表
程式代號：pre4022
撰寫日期：113.03.22
程式作者：Jackie.Huang
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE4022 extends SuperBean {
	private String q_DateStart;
	private String q_DateEnd;
	private String q_TimeStart;
	private String q_TimeEnd;
	private String q_Type;
	private String q_idNo;
	private String q_changeType;
	private String payType;// 付款方式中文
	private String prefixNo;
	private String changeType;// 預查種類中文
	private String applyway;// 申請方式中文
	private int amount;
	private static Logger logger = Logger.getLogger(PRE4022.class);

	public String getQ_DateStart() {
		return checkGet(q_DateStart);
	}

	public void setQ_DateStart(String q_DateStart) {
		this.q_DateStart = checkSet(q_DateStart);
	}

	public String getQ_DateEnd() {
		return checkGet(q_DateEnd);
	}

	public void setQ_DateEnd(String q_DateEnd) {
		this.q_DateEnd = checkSet(q_DateEnd);
	}

	public String getQ_TimeStart() {
		return checkGet(q_TimeStart);
	}

	public void setQ_TimeStart(String q_TimeStart) {
		this.q_TimeStart = checkSet(q_TimeStart);
	}

	public String getQ_TimeEnd() {
		return checkGet(q_TimeEnd);
	}

	public void setQ_TimeEnd(String q_TimeEnd) {
		this.q_TimeEnd = checkSet(q_TimeEnd);
	}

	public String getQ_Type() {
		return checkGet(q_Type);
	}

	public void setQ_Type(String q_Type) {
		this.q_Type = checkSet(q_Type);
	}

	public String getQ_idNo() {
		return checkGet(q_idNo);
	}

	public void setQ_idNo(String q_idNo) {
		this.q_idNo = checkSet(q_idNo);
	}

	public String getQ_changeType() {
		return checkGet(q_changeType);
	}

	public void setQ_changeType(String q_changeType) {
		this.q_changeType = checkSet(q_changeType);
	}

	public String getPayType() {
		return checkGet(payType);
	}

	public void setPayType(String payType) {
		this.payType = checkSet(payType);
	}

	public String getPrefixNo() {
		return checkGet(prefixNo);
	}

	public void setPrefixNo(String prefixNo) {
		this.prefixNo = checkSet(prefixNo);
	}

	public String getChangeType() {
		return checkGet(changeType);
	}

	public void setChangeType(String changeType) {
		this.changeType = checkSet(changeType);
	}

	public String getApplyway() {
		return checkGet(applyway);
	}

	public void setApplyway(String applyway) {
		this.applyway = checkSet(applyway);
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	// 預先檢查資料在時間範圍內是否存在
	public static String checkForJsp(String dateStart, String dateEnd, String timeStart, String timeEnd, String type)
			throws Exception {
		if (dateStart == null || "".equals(dateStart) || dateEnd == null || "".equals(dateEnd)) {
			return "請將日期填上"; // console 輸出用
		}

		if (type != null && !"".equals(Common.get(type))) {
			if ("receive".equals(type)) {
				if (timeStart == null || "".equals(timeStart) || timeEnd == null || "".equals(timeEnd)) {
					return "請將時間填上";
				}

				// 先去收據主檔(EICM.EEDB1000)確認要查的資料在時間範圍內有資料，若無，則返回錯誤資訊。
				List<Cedb1000> cedb1000s = ServiceGetter.getInstance().getPre4022Service()
						.selectCEDB1000ByTimeInterval(dateStart, dateEnd, timeStart, timeEnd);

				if (cedb1000s == null || cedb1000s.isEmpty()) {
					return "查無資料";
				}
			} else if ("pay".equals(type)) {
				// 先去收據主檔(PREFIX_RECEIPT_NO)確認要查的資料在時間範圍內有資料，若無，則返回錯誤資訊。
				List<PrefixReceiptNo> prefixReceiptNos = ServiceGetter.getInstance().getPre4022Service()
						.selectPrefixReceiptNoByTimeInterval(dateStart, dateEnd);

				if (prefixReceiptNos == null || prefixReceiptNos.isEmpty()) {
					return "查無資料";
				}
			}
		}

		return "ok";
	}

	// 列印公司名稱預查規費日報表
	@SuppressWarnings("null")
	public File doPrintPdf() throws Exception {
		File report = null;
		List<PRE4022> pre4022s = new ArrayList<PRE4022>();
		Map<String, Object> parameters = new HashMap<String, Object>();

		parameters.put("printDate", ServiceGetter.getInstance().getPre4022Service().getPrintDate());// 製表日期
		parameters.put("preTitle", "receive".equals(getQ_Type()) ? "收文" : "繳費");// 副標題類型
		parameters.put("dateStart", formatTaiwanDate(getQ_DateStart()));// 收文或繳費開始日期
		parameters.put("dateEnd", formatTaiwanDate(getQ_DateEnd()));// 收文或繳費結束日期
		parameters.put("receiveTimeStart", "receive".equals(getQ_Type()) ? formatTime(getQ_TimeStart()) : "");// 收文開始時間
		parameters.put("receiveTimeEnd", "receive".equals(getQ_Type()) ? formatTime(getQ_TimeEnd()) : "");// 收文結束時間

		try {
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo()
					.getRealPath("tcfi/report/pre4022.jasper");

			// 獲取資料
			if ("receive".equals(getQ_Type())) {
				pre4022s = ServiceGetter.getInstance().getPre4022Service().selectPre4022ByTypeEqualReceive(
						getQ_DateStart(), getQ_DateEnd(), getQ_TimeStart(), getQ_TimeEnd(), getQ_idNo(),
						getQ_changeType(), getPayType());
			} else if ("pay".equals(getQ_Type())) {
				pre4022s = ServiceGetter.getInstance().getPre4022Service().selectPre4022ByTypeEqualPay(getQ_DateStart(),
						getQ_DateEnd(), getQ_idNo(), getQ_changeType(), getPayType());
			}

			report = JasperReportMaker.makePdfReport(pre4022s, parameters, jasperPath);
			return report;
		} // try
		catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 200) {
				setErrorMsg(getErrorMsg());
			} // end if
			else
				setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch
	}

	/**
	 * 日期轉換文檔
	 * 
	 * @param q_DateStart
	 * @return String
	 */
	private String formatTaiwanDate(String q_Date) {
		String year = q_Date.substring(0, 3);
		String month = q_Date.substring(3, 5);
		String day = q_Date.substring(5);
		return year + "年" + month + "月" + day + "日";
	}

	/**
	 * 時間轉文檔
	 * 
	 * @param q_time
	 * @return String
	 */
	private String formatTime(String q_time) {
		String hour = q_time.substring(0, 2);
		String minute = q_time.substring(2, 4);
		String second = q_time.substring(4);
		return hour + "時" + minute + "分" + second + "秒";
	}

	@Override
	public Object doQueryOne() throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ArrayList doQueryAll() throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void doCreate() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void doUpdate() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void doDelete() throws Exception {
		// TODO Auto-generated method stub

	}

	public static String uploadReceipt(String Env) {
		try {
			if(logger.isInfoEnabled()) logger.info("info: 上傳環境: " + Env);	
			String fileURL = ("prod".equals(Env))
					? "http://" + PrefixConstants.RECEIPT_HOST + ":8080/edipfileprocessor/api/Entry/downloadFileApi.ctr?orgId=MOEAC&serviceGroupId=MOEAC_RECEIPT_LIST_03_03"
					: "http://" + PrefixConstants.RECEIPT_TEST_HOST + ":8080/edipfileprocessor/api/Entry/downloadFileApi.ctr?orgId=MOEAC&serviceGroupId=MOEAC_RECEIPT_LIST_03_03";
			return checkFileURL(fileURL);
		} catch (IOException e) {
			e.printStackTrace();
			if(logger.isInfoEnabled()) logger.info("e_message: " + e.getMessage());
			return "呼叫收據上傳失敗原因: " + e.getMessage();
		}
	}

	private static String checkFileURL(String fileURL) throws IOException {
	    URL url = new URL(fileURL);
	    if(logger.isInfoEnabled()) logger.info("info: 上傳url: " + url.toString());
	    HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
	    int responseCode = httpConn.getResponseCode();
	    if(logger.isInfoEnabled()) logger.info("info: 返回連線狀態code: " + responseCode);
	    httpConn.disconnect();
	    
	    // Check if the response code is HTTP OK (200)
	    if (responseCode == HttpURLConnection.HTTP_OK) {
	        if (logger.isInfoEnabled()) {
	            logger.info("HTTP OK (200) - The file is available for download.");
	        }
	        if(logger.isInfoEnabled()) logger.info("呼叫收據上傳成功: " + responseCode);
	        return "呼叫上傳成功";
	    } else {
	        if (logger.isInfoEnabled()) {
	            logger.info("HTTP response code: " + responseCode);
	        }
	        if(logger.isInfoEnabled()) logger.info("呼叫收據上傳失敗: " + responseCode);
	        return "呼叫上傳失敗: " + responseCode;
	    }   
	}
}
