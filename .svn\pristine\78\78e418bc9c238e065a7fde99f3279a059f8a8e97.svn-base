package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmmCmpyInfo;

public class CsmmCmpyInfoDao extends BaseDaoJdbc implements RowMapper<CsmmCmpyInfo> {

	private static String sql_saveByPartName = "UPDATE CSMM_CMPY_INFO SET PART_NAME = ? WHERE BAN_NO = ? AND IS_NEWEST > 'N' ";
	public int updatePartNameByBanNo(String banNo, String newPartName){
		if("".equals(Common.get(banNo)) || "".equals(Common.get(newPartName))) return 0;
		SQLJob sqljob = new SQLJob(sql_saveByPartName);
		sqljob.addParameter(newPartName);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	private static String sql_findCmpyInfo = "SELECT " + 
			"	A.BAN_NO, A.CMPY_MODIF_NO, A.PRE_REG_NO, A.REG_UNIT_CODE, A.REG_UNIT_NAME, A.SUB_REG_UNIT, A.CMPY_CODE, " +
			"	A.CMPY_NAME, A.PART_NAME, A.AREA_CODE, A.CMPY_ZIP_CODE, A.CMPY_ADD, A.CMPY_TEL_NO, A.CMPY_FAX_NO, " + 
			"	A.CMPY_EMAIL, A.CMPY_URL, A.OLD_BAN_NO, A.OLD_CMPY_NAME, A.ORG_TYPE, A.ORG_TYPE_NAME, A.BUS_TYPE, " +
			"	A.SINGLE_CMPY, A.CASE_STATUS, A.CASE_STATUS_DESC, A.ADD_SEQ_NO, A.CMPY_STATUS,  A.CMPY_STATUS_DESC, A.CTRL_ITEM, " + 
			"	A.INVEST_CODE, A.BR_AMT, A.CAP_AMT, A.REAL_CAP_AMT, A.STOCK_TYPE, A.STOCK_TYPE_NAME, A.STOCK_AMT, " +
			"	A.SHARE_VAL, A.EQUITY_AMT, A.PREF_STOCK_AMT, A.WARR_STOCK_AMT, A.BOND_STOCK_AMT, A.CHG_APP_DATE, A.CHG_APP_NO, " + 
			"	A.CHG_APP_WD, A.REVOKE_APP_DATE, A.REVOKE_APP_NO, A.REVOKE_APP_WD, A.DTR_AMT, A.DTR_BEG_DATE, A.DTR_END_DATE, " +
			"	A.FILE_LOCATE, A.FILE_NO, A.FISCAL_DATE, A.ACC_NAME, A.ACC_ID, A.LQDN_APP_DATE, A.LQDN_APP_NO, " + 
			"	A.LQDN_APP_WD, A.LIQUIDATOR, A.OPEN_DATE, A.ESTAB_APP_DATE, A.ESTAB_APP_NO, A.ESTAB_APP_WD, A.SPVR_AMT, " +
			"	A.SPVR_BEG_DATE, A.SPVR_END_DATE, A.SUS_APP_DATE, A.SUS_APP_NO, A.SUS_APP_UNIT, A.SUS_APP_WD, A.SUS_BEG_DATE, " + 
			"	A.SUS_END_DATE, A.CMPY_ENAME, A.CMPY_EADD, A.ATTOR_NAME, A.ATTOR_ID, A.STOCK_ISSUE, A.CERTIFICATE, A.BASE_ID, A.CO_TYPE, A.ITEM_SCRPT, A.DIST_CODE, " +
			"	A.CHG_CASE_CODE, A.ACT_APP_DATE, A.ACT_APP_WD, A.ACT_APP_NO, A.CMPY_NAME_CNS, A.IND_DTR_AMT, A.COMMITTEE, A.NO_ZONE_NAME, A.UPDATE_DATE, A.UPDATE_USER, " + 
			"	A.CHINA_CODE, A.CONSTITUTION_EST, A.CONSTITUTION_CHG, A.SIPA_BUSI_APP_DATE, B.MODIF_TYPE " +
			"FROM ICMS.CSMM_CMPY_INFO A LEFT JOIN ICMS.CSMX_CMPY_SEQNO_MAP B ON A.CMPY_MODIF_NO = B.CMPY_MODIF_NO  AND A.BAN_NO = B.BAN_NO " + 
			"WHERE B.BAN_NO = ? AND B.MODIF_TYPE in ('4','7','8') AND B.IS_NEWEST = 'N' order by B.MODIF_TYPE ";
	public List<CsmmCmpyInfo> query(String banNo){
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findCmpyInfo);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	
	private static String SQL_findByCondition = " SELECT " +
			"	A.BAN_NO, A.CMPY_MODIF_NO, A.PRE_REG_NO, A.REG_UNIT_CODE, A.REG_UNIT_NAME, A.SUB_REG_UNIT, A.CMPY_CODE, " +
			"	A.CMPY_NAME, A.PART_NAME, A.AREA_CODE, A.CMPY_ZIP_CODE, A.CMPY_ADD, A.CMPY_TEL_NO, A.CMPY_FAX_NO, " + 
			"	A.CMPY_EMAIL, A.CMPY_URL, A.OLD_BAN_NO, A.OLD_CMPY_NAME, A.ORG_TYPE, A.ORG_TYPE_NAME, A.BUS_TYPE, " +
			"	A.SINGLE_CMPY, A.CASE_STATUS, A.CASE_STATUS_DESC, A.ADD_SEQ_NO, A.CMPY_STATUS,  A.CMPY_STATUS_DESC, A.CTRL_ITEM, " + 
			"	A.INVEST_CODE, A.BR_AMT, A.CAP_AMT, A.REAL_CAP_AMT, A.STOCK_TYPE, A.STOCK_TYPE_NAME, A.STOCK_AMT, " +
			"	A.SHARE_VAL, A.EQUITY_AMT, A.PREF_STOCK_AMT, A.WARR_STOCK_AMT, A.BOND_STOCK_AMT, A.CHG_APP_DATE, A.CHG_APP_NO, " + 
			"	A.CHG_APP_WD, A.REVOKE_APP_DATE, A.REVOKE_APP_NO, A.REVOKE_APP_WD, A.DTR_AMT, A.DTR_BEG_DATE, A.DTR_END_DATE, " +
			"	A.FILE_LOCATE, A.FILE_NO, A.FISCAL_DATE, A.ACC_NAME, A.ACC_ID, A.LQDN_APP_DATE, A.LQDN_APP_NO, " + 
			"	A.LQDN_APP_WD, A.LIQUIDATOR, A.OPEN_DATE, A.ESTAB_APP_DATE, A.ESTAB_APP_NO, A.ESTAB_APP_WD, A.SPVR_AMT, " +
			"	A.SPVR_BEG_DATE, A.SPVR_END_DATE, A.SUS_APP_DATE, A.SUS_APP_NO, A.SUS_APP_UNIT, A.SUS_APP_WD, A.SUS_BEG_DATE, " + 
			"	A.SUS_END_DATE, A.CMPY_ENAME, A.CMPY_EADD, A.ATTOR_NAME, A.ATTOR_ID, A.STOCK_ISSUE, A.CERTIFICATE, A.BASE_ID, A.CO_TYPE, A.ITEM_SCRPT, A.DIST_CODE, " +
			"	A.CHG_CASE_CODE, A.ACT_APP_DATE, A.ACT_APP_WD, A.ACT_APP_NO, A.CMPY_NAME_CNS, A.IND_DTR_AMT, A.COMMITTEE, A.NO_ZONE_NAME, A.UPDATE_DATE, A.UPDATE_USER, " + 
			"	A.CHINA_CODE, A.CONSTITUTION_EST, A.CONSTITUTION_CHG, A.SIPA_BUSI_APP_DATE, B.MODIF_TYPE " +
			" FROM ICMS.CSMM_CMPY_INFO A " +
			" INNER JOIN ICMS.CSMX_CMPY_SEQNO_MAP B ON A.CMPY_MODIF_NO=B.CMPY_MODIF_NO AND A.BAN_NO=B.BAN_NO WHERE (B.IS_NEWEST='Y' or B.IS_NEWEST='X')";
	public List<CsmmCmpyInfo> findByCondition(String banNo, String companyName, String prefixNo, String specialName, String respName, String respIdNo) {
		if(!"".equals(Common.get(banNo))
			|| !"".equals(Common.get(companyName)) 
			|| !"".equals(Common.get(prefixNo)) 
			|| !"".equals(Common.get(specialName)) 
			|| !"".equals(Common.get(respName)) 
			|| !"".equals(Common.get(respIdNo)) ) {
			SQLJob sqljob = new SQLJob(SQL_findByCondition);
			if(!"".equals(Common.get(banNo))) {
				sqljob.appendSQL("AND A.BAN_NO=?");
				sqljob.addParameter(banNo);
				sqljob.addSqltypes(oracle.jdbc.OracleTypes.FIXED_CHAR);
			}
			if(!"".equals(Common.get(companyName))) {
				sqljob.appendSQL("AND A.CMPY_NAME like ?");
				sqljob.addSuffixLikeParameter(companyName);
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			//
			if(!"".equals(Common.get(prefixNo))) {
				sqljob.appendSQL("AND A.PRE_REG_NO=?");
				sqljob.addParameter(prefixNo);
				sqljob.addSqltypes(oracle.jdbc.OracleTypes.FIXED_CHAR);
			}
			if(!"".equals(Common.get(specialName))) {
				sqljob.appendSQL("AND A.PART_NAME=?");
				sqljob.addParameter(specialName);
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if(!"".equals(Common.get(respName))) {
				sqljob.appendSQL("AND A.BAN_NO IN ( SELECT BAN_NO FROM ICMS.CSMM_SHARE_HOLDER WHERE BAN_NO=B.BAN_NO AND CMPY_MODIF_NO=B.SH_CMPY_MODIF_NO and CMPY_REP='Y' AND IS_NEWEST='Y' AND NAME=? )");
				sqljob.addParameter(respName);
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if(!"".equals(Common.get(respIdNo))) {
				sqljob.appendSQL("AND A.BAN_NO IN ( SELECT BAN_NO FROM ICMS.CSMM_SHARE_HOLDER WHERE BAN_NO=B.BAN_NO AND CMPY_MODIF_NO=B.SH_CMPY_MODIF_NO and CMPY_REP='Y' AND IS_NEWEST='Y' AND ID_NO=? )");
				sqljob.addParameter(respIdNo);
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), this);
		}
		return null;
	}

	public CsmmCmpyInfo mapRow(ResultSet rs, int idx) throws SQLException {
		CsmmCmpyInfo obj = null;
		if(null!=rs) {
			obj = new CsmmCmpyInfo();
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setCmpyModifNo(rs.getLong("CMPY_MODIF_NO"));
			obj.setPreRegNo(rs.getString("PRE_REG_NO"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setRegUnitName(rs.getString("REG_UNIT_NAME"));
			obj.setSubRegUnit(rs.getString("SUB_REG_UNIT"));
			obj.setCmpyCode(rs.getString("CMPY_CODE"));
			obj.setCmpyName(rs.getString("CMPY_NAME"));
			obj.setPartName(rs.getString("PART_NAME"));
			obj.setAreaCode(rs.getString("AREA_CODE"));
			obj.setCmpyZipCode(rs.getString("CMPY_ZIP_CODE"));
			obj.setCmpyAdd(rs.getString("CMPY_ADD"));
			obj.setCmpyTelNo(rs.getString("CMPY_TEL_NO"));
			obj.setCmpyFaxNo(rs.getString("CMPY_FAX_NO"));
			obj.setCmpyEmail(rs.getString("CMPY_EMAIL"));
			obj.setCmpyUrl(rs.getString("CMPY_URL"));
			obj.setOldBanNo(rs.getString("OLD_BAN_NO"));
			obj.setOldCmpyName(rs.getString("OLD_CMPY_NAME"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
			obj.setOrgTypeName(rs.getString("ORG_TYPE_NAME"));
			obj.setBusType(rs.getString("BUS_TYPE"));
			obj.setSingleCmpy(rs.getString("SINGLE_CMPY"));
			obj.setCaseStatus(rs.getString("CASE_STATUS"));
			obj.setCaseStatusDesc(rs.getString("CASE_STATUS_DESC"));
			obj.setAddSeqNo(rs.getLong("ADD_SEQ_NO"));
			obj.setCmpyStatus(rs.getString("CMPY_STATUS"));
			obj.setCmpyStatusDesc(rs.getString("CMPY_STATUS_DESC"));
			obj.setCtrlItem(rs.getString("CTRL_ITEM"));
			obj.setInvestCode(rs.getString("INVEST_CODE"));
			obj.setBrAmt(rs.getLong("BR_AMT"));
			obj.setCapAmt(rs.getLong("CAP_AMT"));
			obj.setRealCapAmt(rs.getLong("REAL_CAP_AMT"));
			obj.setStockType(rs.getString("STOCK_TYPE"));
			obj.setStockTypeName(rs.getString("STOCK_TYPE_NAME"));
			obj.setStockAmt(rs.getLong("STOCK_AMT"));
			obj.setShareVal(rs.getLong("SHARE_VAL"));
			obj.setEquityAmt(rs.getLong("EQUITY_AMT"));
			obj.setPrefStockAmt(rs.getLong("PREF_STOCK_AMT"));
			obj.setWarrStockAmt(rs.getLong("WARR_STOCK_AMT"));
			obj.setBondStockAmt(rs.getLong("BOND_STOCK_AMT"));
			obj.setChgAppDate(rs.getDate("CHG_APP_DATE"));
			obj.setChgAppNo(rs.getString("CHG_APP_NO"));
			obj.setChgAppWd(rs.getString("CHG_APP_WD"));
			obj.setRevokeAppDate(rs.getDate("REVOKE_APP_DATE"));
			obj.setRevokeAppNo(rs.getString("REVOKE_APP_NO"));
			obj.setRevokeAppWd(rs.getString("REVOKE_APP_WD"));
			obj.setDtrAmt(rs.getString("DTR_AMT"));
			obj.setDtrBegDate(rs.getDate("DTR_BEG_DATE"));
			obj.setDtrEndDate(rs.getDate("DTR_END_DATE"));
			obj.setFileLocate(rs.getString("FILE_LOCATE"));
			obj.setFileNo(rs.getString("FILE_NO"));
			obj.setFiscalDate(rs.getString("FISCAL_DATE"));
			obj.setAccName(rs.getString("ACC_NAME"));
			obj.setAccId(rs.getString("ACC_ID"));
			obj.setLqdnAppDate(rs.getDate("LQDN_APP_DATE"));
			obj.setLqdnAppNo(rs.getString("LQDN_APP_NO"));
			obj.setLqdnAppWd(rs.getString("LQDN_APP_WD"));
			obj.setLiquidator(rs.getString("LIQUIDATOR"));
			obj.setOpenDate(rs.getDate("OPEN_DATE"));
			obj.setEstabAppDate(rs.getDate("ESTAB_APP_DATE"));
			obj.setEstabAppNo(rs.getString("ESTAB_APP_NO"));
			obj.setEstabAppWd(rs.getString("ESTAB_APP_WD"));
			obj.setSpvrAmt(rs.getString("SPVR_AMT"));
			obj.setSpvrBegDate(rs.getDate("SPVR_BEG_DATE"));
			obj.setSpvrEndDate(rs.getDate("SPVR_END_DATE"));
			obj.setSusAppDate(rs.getDate("SUS_APP_DATE"));
			obj.setSusAppNo(rs.getString("SUS_APP_NO"));
			obj.setSusAppUnit(rs.getString("SUS_APP_UNIT"));
			obj.setSusAppWd(rs.getString("SUS_APP_WD"));
			obj.setSusBegDate(rs.getDate("SUS_BEG_DATE"));
			obj.setSusEndDate(rs.getDate("SUS_END_DATE"));
			obj.setCmpyEname(rs.getString("CMPY_ENAME"));
			obj.setCmpyEadd(rs.getString("CMPY_EADD"));
			obj.setAttorName(rs.getString("ATTOR_NAME"));
			obj.setAttorId(rs.getString("ATTOR_ID"));
			obj.setStockIssue(rs.getString("STOCK_ISSUE"));
			obj.setCertificate(rs.getString("CERTIFICATE"));
			obj.setBaseId(rs.getString("BASE_ID"));
			obj.setCoType(rs.getString("CO_TYPE"));
			obj.setItemScrpt(rs.getString("ITEM_SCRPT"));
			obj.setDistCode(rs.getString("DIST_CODE"));
			obj.setChgCaseCode(rs.getString("CHG_CASE_CODE"));
			obj.setActAppDate(rs.getDate("ACT_APP_DATE"));
			obj.setActAppWd(rs.getString("ACT_APP_WD"));
			obj.setActAppNo(rs.getString("ACT_APP_NO"));
			obj.setCmpyNameCns(rs.getString("CMPY_NAME_CNS"));
			obj.setIndDtrAmt(rs.getString("IND_DTR_AMT"));
			obj.setCommittee(rs.getString("COMMITTEE"));
			obj.setNoZoneName(rs.getString("NO_ZONE_NAME"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setChinaCode(rs.getString("CHINA_CODE"));
			obj.setConstitutionEst(rs.getDate("CONSTITUTION_EST"));
			obj.setConstitutionChg(rs.getDate("CONSTITUTION_CHG"));
			obj.setSipaBusiAppDate(rs.getDate("SIPA_BUSI_APP_DATE"));
			obj.setModifType(rs.getString("MODIF_TYPE"));
		}
		return obj;
	}

}