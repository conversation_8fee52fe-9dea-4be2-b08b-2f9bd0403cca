package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.google.gson.Gson;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.service.ApproveService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.lang.CommonArrayUtils;

/**
 * 馬上辦
 *
 */
public class PRE3004 extends SuperBean implements IApprove {
	//查詢條件
	/** 預查編號 */
	private String q_prefixNo;
	/** 電子流水號 */
	private String q_telixNo;
	/** 申請人身分ID */
	private String q_applyId;
	/** 申請人姓名 */
	private String q_applyName;
	/** 統一編號 */
	private String q_banNo;
	/** 公司名稱 */
	private String q_companyName;
	/** 分文日期 */
	private String q_assignDate;
	//預查編號
	private String[] prefixNos;
	//目前的預查編號
	private String current;

	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(",nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL(" ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE");
		sqljob.appendSQL(",A.APPROVE_RESULT");
		sqljob.appendSQL(",A.COMPANY_NAME");
		sqljob.appendSQL(",A.RESERVE_DATE");
		sqljob.appendSQL(",A.COMPANY_STUS");
		sqljob.appendSQL(",A.RECEIVE_DATE");
		sqljob.appendSQL(",A.STAFF_NAME");
		sqljob.appendSQL("FROM CEDB1000 A");
		sqljob.appendSQL("WHERE A.CLOSE_DATE IS NOT NULL");//已結案
		if( !"".equals(getQ_prefixNo()) ) {
			sqljob.appendSQL("AND (A.PREFIX_NO LIKE ?)");
			sqljob.addSuffixLikeParameter(getQ_prefixNo());
		}
		if( !"".equals(getQ_telixNo()) ) {
			sqljob.appendSQL("AND (A.TELIX_NO = ?)");
			sqljob.addParameter(getQ_telixNo().toUpperCase());
		}
		if( !"".equals(getQ_applyId()) ) {
			sqljob.appendSQL("AND (A.APPLY_ID = ?)");
			sqljob.addParameter(getQ_applyId().toUpperCase());
		}
		if( !"".equals(getQ_applyName()) ) {
			sqljob.appendSQL("AND (A.APPLY_NAME = ?)");
			sqljob.addParameter(getQ_applyName());
		}
		if( !"".equals(getQ_banNo()) ) {
			sqljob.appendSQL("AND (A.BAN_NO = ?)");
			sqljob.addParameter(getQ_banNo());
		}
		if( !"".equals(getQ_companyName()) ) {
			sqljob.appendSQL("AND ( A.COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%"));
			//需同步查詢CEDB1001
			//sqljob.appendSQL("OR A.PREFIX_NO IN ( SELECT PREFIX_NO FROM CEDB1001 WHERE COMPANY_NAME LIKE ? )");
			//sqljob.addLikeParameter(getQ_companyName());
			//改用Lucene查詢
			List<String> tempPrefixNos = ServiceGetter.getInstance().getIndexSearchService().searchPrefixNos(getQ_companyName());
			if(null!=tempPrefixNos && !tempPrefixNos.isEmpty()) {
				sqljob.appendSQL("OR A.PREFIX_NO IN (");
				boolean isFirst = true;
				for(String tempPrefixNo : tempPrefixNos) {
					if(!isFirst) sqljob.appendSQL(",");
					sqljob.appendSQL("'"+tempPrefixNo+"'");
					isFirst = false;
				}
				sqljob.appendSQL(")");
			}
			sqljob.appendSQL(")");
		}
		if( !"".equals(getQ_assignDate()) ) {
			sqljob.appendSQL("AND (A.ID_NO = ?)");
			sqljob.appendSQL("AND (A.ASSIGN_DATE = ?)");
			sqljob.addParameter(getLoginUserId());
			sqljob.addParameter(getQ_assignDate());
		}
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");
		System.out.println(sqljob.toString());
		List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<Map<String,Object>> it = objList.iterator();
			Map<String,Object> o;
			String[] rowArray = new String[9];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[9];
				rowArray[0] = Common.get(o.get("PREFIX_NO"));
				rowArray[1] = Common.get(o.get("APPLY_NAME"));
				rowArray[2] = Common.get(o.get("CHANGE_TYPE"));
				rowArray[3] = ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get(o.get("APPROVE_RESULT")));
				rowArray[4] = Common.get(o.get("COMPANY_NAME"));
				rowArray[5] = Common.get(o.get("RESERVE_DATE"));
				rowArray[6] = ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(Common.get(o.get("COMPANY_STUS")));
				rowArray[7] = Common.get(o.get("RECEIVE_DATE"));
				rowArray[8] = Common.get(o.get("STAFF_NAME"));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	private String id;
	private String errmsg;
	private int onLineCnt;
	private String prefixNo;
	private String[] cbPrefixType; // setup, changeName, changeItem (checkbox)
	private String prefixType;
	private String elixNo;
	private String oldCompanyName;
	private String banNo;
	private String contactGetKind; // radio
	private String getKindRemark;
	private String applyType;
	private String receiveDate;
	private String checkPrefixForm;
	private String docType; // select
	private String prefixFormNo;
	private String[] attachment; // checkOtherForm, checkSpec, checkOtherSpec
									// (checkbox)
	private String otherSpecRemark;
	private String applyName;
	private String applyId;
	private String applyAddr;
	private String applyTel;
	private String attorName;
	private String attorId;
	private String attorNo;
	private String attorAddr;
	private String receiveName;
	private String receiveId;
	private String receiveAddr;
	private String receiveTel;
	private String caseCode;
	private String changeType;
	private String telixNo;
	private String contactName;
	private String contactAddr;
	private String companyAddr;
	private String companyName; // pre3004_00.jsp
	private String assignDate;
	private String[] cedb1002Chk; // checkbox

	private String[] busiItem;
	private String[] busiItemNo;
	private String[] seqNo; // 公司名稱用
	private String[] itemSeqNo; // 營業項目用
	private String approveResultAllNo; // checkbox, only one
	private String cases;

	private String setup;
	private String changeName;
	private String changeItem;

	private String verify;
	private String functionName;

	private String json;
	private boolean autoApprove;

	private String showSame01;
	private String showSame02;
	private String showSame03;
	private String showSame04;
	private String showSame05;

	private String assignPrefixNo; // for 分文用
	private String chinaBusitemMark; // 檢查陸商營業項目用

	private String receiveDateTime;
	private String issueKeyinDateTime;
	private String receiveKeyinDateTime;
	private String assignDateTime;
	private String approveDateTime;
	private String closeDateTime;
	private String getDateTime;
	
	private String applyWay;
	
	private String hRemark;
	
	private String resetCloseDateFlag;
	private String atonceType;
	
	private String hiddenPrefixNos;
	
	public void initPrefixNos() {
		
		PRE3004 obj = this;
		if (this.hiddenPrefixNos != null ) {
			String[] prefixNos = this.hiddenPrefixNos.split("-");
			if(prefixNos != null && prefixNos.length > 0){
				String strPrefixNo = "";
				for(int i=0; i< prefixNos.length; i++){
					if(!"".equals(Common.get(prefixNos[i]))){
						if(i == 0){
							// obj.setBanNo(prefixNos[i]);
							//obj.setCurrentPrefixNo(String.valueOf(i));
						}else{
							strPrefixNo += ",";
						}
						strPrefixNo += prefixNos[i];
					}
				}
				obj.setHiddenPrefixNos(strPrefixNo);
			}
		} // if
	}

	public String tempSave() {
		// int index = theForm.getIndex();
		ApproveService approveService = ServiceGetter.getInstance().getApproveService();
		Gson gson = new Gson();

		PrefixVo prefixVo = gson.fromJson(json, PrefixVo.class);
		approveService.convertJsonToObject(prefixVo);

		// kylin 檢查公司名稱格式, 2005.04.26新增
		if (this.functionName.startsWith("approve") || this.functionName.startsWith("keyin")) {
			if (!approveService.doVerifyCmpyNames(prefixVo)) {
				this.setState("error.COMPANY_NAME.format");
				this.setErrmsg("公司名稱格式不對，不可有空白，或重複公司型態");
			}
		}

		// zion 檢查營業項目, 2009.06.11新增
		if (!approveService.doVerifyBusiItem(prefixVo, this.getFunctionName())) {
			this.setState("error.busiitem.onlyGenerality");
			this.setErrmsg("營業項目不可空白或僅登記概括條款");
		}

		String errors = saveToDb(prefixVo, this, approveService, getLoginUserId());
		if (errors.isEmpty()) {
			System.out.println("*** MultiFormIteratorAction 存檔成功 ***");
			// 訊息控制用flag重設 ??
			// theForm.setEverCheckedConflict(false);
			// theForm.setExistConflictCompanyNames(false);
			// theForm.setExistSameCompanyNames(false);
			// 存檔成功訊息
			this.setState("msg.doSave.success");
		} else {
			// 存檔失敗
			System.out.println("*** MultiFormIteratorAction 存檔失敗或存檔成功但仍為審查中 ***");
		}

		this.setApplyWay(TcfiView.getApplyWayByTelixNo(telixNo));
		return "";
	}

	public String saveToDb(PrefixVo prefixVo, PRE3004 pre3004, ApproveService approveService, String userId) {
	    if(approveService.saveToPrefixDataBase(prefixVo, pre3004, userId, PrefixConstants.FUN_CODE_3004)==null){
	    	System.out.println("error.save.Exception");
	    } else {
	    	//介接投審會
	    	ServiceGetter.getInstance().getMoeaicApproveService().notifyMoeaic(prefixVo.getPrefixNo());
	    }
		//同步一站式案件狀態
		ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(prefixVo.getPrefixNo(), getLoginUserId());
		return "";
	}

	public void delBuItem() throws Exception {
		if (cedb1002Chk != null) {
			List<String> cedb1002s = CommonArrayUtils.arrayToList(cedb1002Chk);
			for (String busiItemNo : cedb1002s) {
				if (!prefixNo.isEmpty() && !busiItemNo.isEmpty())
					ServiceGetter.getInstance().getPrefixService().deleteCedb1002ByPrefixNoAndBusiItemNo(prefixNo, busiItemNo);
			}
		}
	}

	public void saveBusiItem() throws Exception {
		List<Cedb1002> cedb1002s = new ArrayList<Cedb1002>();
		if (busiItem != null && busiItemNo != null && seqNo != null) {
			if (busiItem.length == busiItemNo.length && busiItem.length == seqNo.length) {
				for (int i = 0; i < busiItem.length; i++) {
					if (busiItemNo[i].isEmpty() || busiItem[i].isEmpty())
						continue;

					Cedb1002 cedb1002 = new Cedb1002();
					cedb1002.setPrefixNo(prefixNo);
					cedb1002.setSeqNo(seqNo[i]);
					cedb1002.setBusiItemNo(busiItemNo[i]);
					cedb1002.setBusiItem(busiItem[i]);
					cedb1002s.add(cedb1002);
				}
			}
		}
		ServiceGetter.getInstance().getApproveService().insertCedb1002s(cedb1002s);
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getCaseCode() {return caseCode;}
	public void setCaseCode(String s) {this.caseCode = s;}

	public String getChangeType() {return changeType;}
	public void setChangeType(String s) {this.changeType = s;}

	public String[] getCedb1002Chk() {return cedb1002Chk;}
	public void setCedb1002Chk(String[] a) {this.cedb1002Chk = a;}

	public String getId() {return id;}
	public void setId(String s) {this.id = s;}

	public String getErrmsg() {return errmsg;}
	public void setErrmsg(String s) {this.errmsg = s;}

	public int getOnLineCnt() {return onLineCnt;}
	public void setOnLineCnt(int i) {this.onLineCnt = i;}

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public String[] getCbPrefixType() {return cbPrefixType;}
	public void setCbPrefixType(String[] a) {this.cbPrefixType = a;}

	public String getPrefixType() {return prefixType;}
	public void setPrefixType(String s) {this.prefixType = s;}

	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}

	public String getBanNo() {return Common.get(banNo);}
	public void setBanNo(String s) {this.banNo = s;}

	public String getContactGetKind() {return contactGetKind;}
	public void setContactGetKind(String s) {this.contactGetKind = s;}

	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String s) {this.getKindRemark = s;}

	public String getApplyType() {return applyType;}
	public void setApplyType(String s) {this.applyType = s;}

	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}

	public String getCheckPrefixForm() {return checkPrefixForm;}
	public void setCheckPrefixForm(String s) {this.checkPrefixForm = s;}

	public String getDocType() {return docType;}
	public void setDocType(String s) {this.docType = s;}

	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String s) {this.prefixFormNo = s;}

	public String[] getAttachment() {return attachment;}
	public void setAttachment(String[] a) {this.attachment = a;}

	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = s;}

	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {this.applyName = checkSet(s);}

	public String getApplyId() {return checkGet(applyId);}
	public void setApplyId(String s) {this.applyId = checkSet(s);}

	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String s) {this.applyAddr = s;}

	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}

	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}

	public String getAttorId() {return attorId;}
	public void setAttorId(String s) {this.attorId = s;}

	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}

	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}

	public String getReceiveName() {return receiveName;}
	public void setReceiveName(String s) {this.receiveName = s;}

	public String getReceiveId() {return receiveId;}
	public void setReceiveId(String s) {this.receiveId = s;}

	public String getReceiveAddr() {return receiveAddr;}
	public void setReceiveAddr(String s) {this.receiveAddr = s;}

	public String getReceiveTel() {return receiveTel;}
	public void setReceiveTel(String s) {this.receiveTel = s;}

	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}

	public String getContactName() {return contactName;}
	public void setContactName(String s) {this.contactName = s;}

	public String getContactAddr() {return contactAddr;}
	public void setContactAddr(String s) {this.contactAddr = s;}

	public String getCompanyAddr() {return companyAddr;}
	public void setCompanyAddr(String s) {this.companyAddr = s;}

	public String[] getBusiItem() {return busiItem;}
	public void setBusiItem(String[] a) {this.busiItem = a;}

	public String[] getBusiItemNo() {return busiItemNo;}
	public void setBusiItemNo(String[] a) {this.busiItemNo = a;}

	public String[] getSeqNo() {return seqNo;}
	public void setSeqNo(String[] a) {this.seqNo = a;}

	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {this.companyName = checkSet(s);}

	public String getAssignDate() {return checkGet(assignDate);}
	public void setAssignDate(String s) {this.assignDate = checkSet(s);}

	public String getApproveResultAllNo() {return approveResultAllNo;}
	public void setApproveResultAllNo(String s) {this.approveResultAllNo = s;}

	public String getSetup() {return setup;}
	public void setSetup(String s) {this.setup = s;}

	public String getChangeName() {return changeName;}
	public void setChangeName(String s) {this.changeName = s;}

	public String getChangeItem() {return changeItem;}
	public void setChangeItem(String s) {this.changeItem = s;}

	public String getCases() {return cases;}
	public void setCases(String s) {this.cases = s;}

	public String[] getItemSeqNo() {return itemSeqNo;}
	public void setItemSeqNo(String[] a) {this.itemSeqNo = a;}

	public String getVerify() {return verify;}
	public void setVerify(String verify) {this.verify = verify;}

	public String getFunctionName() {return functionName;}
	public void setFunctionName(String s) {this.functionName = s;}

	public String getJson() {return json;}
	public void setJson(String s) {this.json = s;}

	public boolean isAutoApprove() {return autoApprove;}
	public void setAutoApprove(boolean b) {this.autoApprove = b;}

	public String getShowSame01() {return showSame01;}
	public void setShowSame01(String s) {this.showSame01 = s;}

	public String getShowSame02() {return showSame02;}
	public void setShowSame02(String s) {this.showSame02 = s;}

	public String getShowSame03() {return showSame03;}
	public void setShowSame03(String s) {this.showSame03 = s;}

	public String getShowSame04() {return showSame04;}
	public void setShowSame04(String s) {this.showSame04 = s;}

	public String getShowSame05() {return showSame05;}
	public void setShowSame05(String s) {this.showSame05 = s;}

	public String getAssignPrefixNo() {return assignPrefixNo;}
	public void setAssignPrefixNo(String s) {this.assignPrefixNo = s;}

	public String getChinaBusitemMark() {return chinaBusitemMark;}
	public void setChinaBusitemMark(String s) {this.chinaBusitemMark = s;}

	public String getReceiveDateTime() {return checkGet(receiveDateTime);}
	public void setReceiveDateTime(String s) {this.receiveDateTime = checkSet(s);}

	public String getIssueKeyinDateTime() {return checkGet(issueKeyinDateTime);}
	public void setIssueKeyinDateTime(String s) {this.issueKeyinDateTime = checkSet(s);}

	public String getReceiveKeyinDateTime() {return checkGet(receiveKeyinDateTime);}
	public void setReceiveKeyinDateTime(String s) {this.receiveKeyinDateTime = checkSet(s);}

	public String getAssignDateTime() {return checkGet(assignDateTime);}
	public void setAssignDateTime(String s) {this.assignDateTime = checkSet(s);}

	public String getApproveDateTime() {return checkGet(approveDateTime);}
	public void setApproveDateTime(String s) {this.approveDateTime = checkSet(s);}

	public String getCloseDateTime() {return checkGet(closeDateTime);}
	public void setCloseDateTime(String s) {this.closeDateTime = checkSet(s);}

	public String getGetDateTime() {return checkGet(getDateTime);}
	public void setGetDateTime(String s) {this.getDateTime = checkSet(s);}

	public String getApplyWay() {return applyWay;}
	public void setApplyWay(String s) {this.applyWay = s;}

	public String gethRemark() {return hRemark;}
	public void sethRemark(String s) {this.hRemark = s;}

	public String getElixNo() {return elixNo;}
	public void setElixNo(String s) {this.elixNo = s;}

	public String getResetCloseDateFlag() {return resetCloseDateFlag;}
	public void setResetCloseDateFlag(String s) {this.resetCloseDateFlag = s;}

	public String getAtonceType() {return atonceType;}
	public void setAtonceType(String s) {this.atonceType = s;}


	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}

	public String getQ_telixNo() {return checkGet(q_telixNo);}
	public void setQ_telixNo(String s) {this.q_telixNo = checkSet(s);}

	public String getQ_applyId() {return checkGet(q_applyId);}
	public void setQ_applyId(String s) {this.q_applyId = checkSet(s);}

	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {this.q_applyName = checkSet(s);}

	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {this.q_banNo = checkSet(s);}

	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {this.q_companyName = checkSet(s);}

	public String getQ_assignDate() {return checkGet(q_assignDate);}
	public void setQ_assignDate(String s) {this.q_assignDate = checkSet(s);}

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] prefixNos) {this.prefixNos = prefixNos;}

	public String getCurrent() {return checkGet(current);}
	public void setCurrent(String s) {this.current = checkSet(s);}
	
	public String getHiddenPrefixNos() {return checkGet(hiddenPrefixNos);}
	public void setHiddenPrefixNos(String s) {this.hiddenPrefixNos = checkSet(s);}

}