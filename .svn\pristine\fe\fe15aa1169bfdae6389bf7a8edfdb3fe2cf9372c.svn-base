package com.kangdainfo.tcfi.service.impl;

import java.util.List;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;
import com.kangdainfo.tcfi.model.eicm.bo.RestrictionItem;
import com.kangdainfo.tcfi.model.eicm.dao.RestrictionDao;
import com.kangdainfo.tcfi.model.eicm.dao.RestrictionItemDao;
import com.kangdainfo.tcfi.service.Pre8011Service;

public class Pre8011ServiceImpl implements Pre8011Service {
	// 1. 找出全部的restrictionList
	// 2. 看restrictionItem中,依照傳入的restrictionId, itemCode是否找的到東西
	//    2a. 如果沒有找到, 看idListWillChange有沒有這筆id
	//        有的話表示要把這個itemCode加到目前的restrictionItem底下
	//        沒有的話就維持現狀
	//    2b. 如果有找到, 看idListWillChange有沒有這筆id
	//        有的話維持現狀
	//        沒有的話表示要把這筆itemCode從目前的restrictionItem中移除
	RestrictionDao restrictionDao;
	RestrictionItemDao restrictionItemDao;
	
	public RestrictionDao getRestrictionDao() {return restrictionDao;}
	public void setRestrictionDao(RestrictionDao dao) {this.restrictionDao = dao;}
	public RestrictionItemDao getRestrictionItemDao() {return restrictionItemDao;}
	public void setRestrictionItemDao(RestrictionItemDao dao) {this.restrictionItemDao = dao;}
	
	public void doSave(String itemCode, String[] idListWillChange, String userId) throws Exception {
		List<Restriction> restrictionList = getRestrictionDao().find(); // 找出資料庫內全部的限制條件
		RestrictionItem restrictionItem ;
		boolean found = false ;
		
		for (int i = 0;i<restrictionList.size();i++) {
			int length = 0;
			if ( idListWillChange != null )
				length = idListWillChange.length;
			found = false;
		    restrictionItem = getRestrictionItemDao().findByCode(restrictionList.get(i).getId(), itemCode);
			if ( restrictionItem == null ) {
			    for ( int j =0; j<length && ! found ;j++ ) {
				    if ( idListWillChange[j].substring(0).equals( Integer.toString(restrictionList.get(i).getId() )))
					   found = true;
				} // for
				if ( found ) {
				    restrictionItem = new RestrictionItem();
					restrictionItem.setRestrictionId(restrictionList.get(i).getId());
					restrictionItem.setItemCode(itemCode);
					restrictionItem.setModIdNo(userId);
					restrictionItem.setModDate(Datetime.getYYYMMDD());
					restrictionItem.setModTime(Datetime.getHHMMSS());
					getRestrictionItemDao().insert(restrictionItem);
					
				} // if
				else {
					
				} // else
			} //if
			else {
			    for ( int j =0; j<length && ! found ;j++ ) {
				    if ( idListWillChange[j].substring(0).equals( Integer.toString(restrictionList.get(i).getId() )))
				        found = true;
				} // for
				if ( found ) {
					
				} // if
				else {
				    getRestrictionItemDao().delete(restrictionItem);
				    
				} // else
				
			} // else
		} // for

	} // doSave()
}