--DROP TABLE EICM.RESTRICTION_ITEM;
-- Create table
CREATE TABLE EICM.RESTRICTION_ITEM (
	ID NUMBER(15) not null,
	RESTRICTION_ID NUMBER(15) not null,
	ITEM_CODE VARCHAR2(7),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.RESTRICTION_ITEM is '營業項目限制條件營業項目檔';
-- Add comments to the columns 
comment on column EICM.RESTRICTION_ITEM.ID is '主鍵值';
comment on column EICM.RESTRICTION_ITEM.RESTRICTION_ID is '限制條件_鍵值';
comment on column EICM.RESTRICTION_ITEM.ITEM_CODE is '營業項目';
comment on column EICM.RESTRICTION_ITEM.MOD_ID_NO is '異動人員';
comment on column EICM.RESTRICTION_ITEM.MOD_DATE is '異動日期';
comment on column EICM.RESTRICTION_ITEM.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.RESTRICTION_ITEM
  add constraint PK_RESTRICTION_ITEM primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_RESTRICTION_ITEM_ID;
-- Create sequence 
create sequence EICM.SEQ_RESTRICTION_ITEM_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_RESTRICTION_ITEM
Before Insert ON EICM.RESTRICTION_ITEM Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_RESTRICTION_ITEM_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.RESTRICTION_ITEM for EICM.RESTRICTION_ITEM;

--GRANT
grant all on EICM.RESTRICTION_ITEM to EICM4AP;
