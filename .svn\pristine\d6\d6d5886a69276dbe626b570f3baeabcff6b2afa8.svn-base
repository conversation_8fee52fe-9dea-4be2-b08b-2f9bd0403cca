package com.kangdainfo.tcfi.view.pre;
/*
程式目的：當月所有承辦人員馬上辦及檢還件數統計表
程式代號：pre2007
撰寫日期：103.06.03
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ibm.icu.text.DecimalFormat;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE2007 extends SuperBean {
	
	private String q_yearMonth ;
	
	private String index ;
	private String staffName ;
	private String c1 ;
	private String c2 ;
	
	public String getQ_yearMonth() {return checkGet(q_yearMonth);}
	public void setQ_yearMonth(String s) {q_yearMonth = checkSet(s);}
	
	public String getIndex() {return checkGet(index);} 
	public void setIndex(String s){index = checkGet(s);}
	public String getStaffName() {return checkGet(staffName);} 
	public void setStaffName(String s){staffName = checkGet(s);}
	public String getC1() {return checkGet(c1);}
	public void setC1(String s) {c1 = checkSet(s);}
	public String getC2() {return checkGet(c2);}
	public void setC2(String s) {c2 = checkSet(s);}
	
	public SQLJob doAppendSqljob( String yearMonth  ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL( "select" );
		sqljob.appendSQL( "a.staff_name as name" ) ;
		sqljob.appendSQL( ",(select count(1) from eicm.cedb1010 b where b.id_no=a.id_no and b.process_status='C' and b.process_date like '" + yearMonth + "%') as c1" ) ;
		sqljob.appendSQL( " ,(select count(1) from eicm.cedb1010 b where b.id_no=a.id_no and b.process_status='9' and b.process_date like '" + yearMonth + "%') as c2" ) ;
		sqljob.appendSQL( "from eicm.cedbc000 a" ) ;
		sqljob.appendSQL( "where 0 < ( select count(1) from eicm.cedb1010 where id_no = a.id_no and process_date like '" + yearMonth + "%' and process_status in ('9','C' ) )" ) ;
		return sqljob ;
	}  // doAppendSqljob()
		
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		return null ;
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
	  List<Map<String, Object>> pre2007List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_yearMonth() ));
	  if ( pre2007List == null || pre2007List.size() <= 0 ) {
		  setErrorMsg( "該月份無馬上辦及檢還資料，請變更查詢條件" ) ;
		  return null ;
	  } // end if
	  else {
	      ArrayList<String[]> dataList = new ArrayList<String[]>() ;
	      int i = 0 ;
	      String[] rowArray;
	      int totalC1 = 0;
	      int totalC2 = 0;
	      while ( i < pre2007List.size() ) {
	    	  rowArray = new String[3] ; 
		      rowArray[0] = Common.get( pre2007List.get(i).get("name") )  ;
		      rowArray[1] =  Common.get( pre2007List.get(i).get("c1") )  ;
		      rowArray[2] = Common.get( pre2007List.get(i).get("c2") )  ;
		      totalC1 = totalC1 + Integer.parseInt(Common.get(pre2007List.get(i).get("c1")));
		      totalC2 = totalC2 + Integer.parseInt(Common.get(pre2007List.get(i).get("c2")));
		      i++ ;
		      dataList.add(rowArray) ;
	      } // end while
	      
	      rowArray = new String[3];
	      rowArray[0] = "總計";
	      rowArray[1] = Integer.toString(totalC1);
	      rowArray[2] = Integer.toString(totalC2);
	      dataList.add(rowArray) ;
	      return dataList ;
	  } // end else
    } // doQueryAll()
	
	public String RatioFormat( double inputNum ) {
	  inputNum = inputNum * 100 ;
	  DecimalFormat df = new DecimalFormat("##.00");
	  inputNum = Double.parseDouble(df.format(inputNum));
	  String ratio = String.valueOf(inputNum) ;
	  if ( "0.0".equals( ratio.substring(0)) )
	  	return "0" ;
	  else
	    return ratio ;
    } // RatioFormat()
	
	public String DateFormat( String inputDate ) {
	  String tempDate = "" ;
	  if ( inputDate.length() == 9 ) {
	    String year = inputDate.substring(0, 3) ;
	    if ( "0".equals(year.substring(0,1)) )
	      year = year.substring(1) ;
	    String month = inputDate.substring(4, 6) ;
	    if ( "0".equals(month.substring(0,1)) )
	      month = month.substring(1) ;    
	    String day = inputDate.substring(7) ;
	    if ( "0".equals(day.substring(0,1)) )
	      day = day.substring(1) ;
	    tempDate = year + "年" + month + "月" + day + "日" ;
	  } // end if
	  else if ( inputDate.length() == 5 ) {
		String year = inputDate.substring(0, 3) ;
		if ( "0".equals(year.substring(0,1)) )
		    year = year.substring(1) ;
		String month = inputDate.substring(3) ;
		if ( "0".equals(month.substring(0,1)) )
			month = month.substring(1) ;
		tempDate = year + "年" + month + "月" ;
	  } // end else if
	  return tempDate ;
	} // DateFormat()
	  
	public String TimeFormat( String inputTime ) {
	  String tempTime = "" ;
	  String hour = inputTime.substring(0, 2) ;
	  String minute = inputTime.substring(2, 4) ;
	  String second = inputTime.substring(4) ;
	  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
	  return tempTime ;
	} // TimeFormat()
	
	public File doPrintPdf() throws Exception {
	  try {
		File report = null  ;
		String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre2007.jasper");
		Map<String, Object> parameters = new HashMap<String,Object>();
		String printDate = Datetime.getRocDateFromYYYYMMDD(Datetime.getCurrentDate("yyyy/MM/dd")) ;//列印時間年月日
        String printTime = Datetime.getHHMMSS() ;    // 列印時間時分秒               
        parameters.put("printDate", DateFormat(printDate));//列印時間
	    parameters.put("printTime", TimeFormat(printTime));//列印時間
	    parameters.put("yearMonth", DateFormat(getQ_yearMonth()));//列印時間
	    List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(getQ_yearMonth()));
	    if ( rs == null || rs.size() <= 0 ) {
	    	this.setErrorMsg( "該月份無馬上辦及檢還案件資料" ) ;
 		    throw new Exception( "該月份無馬上辦及檢還案件資料" ) ;
	    } // end if
 	    else {
            ArrayList<PRE2007> dataList = new ArrayList<PRE2007>();
	        int i = 0;
	        PRE2007 data ;
	        while( i < rs.size()){
		      data = new PRE2007();
		      data.setIndex( Integer.toString(i+1) ) ;
		      data.setStaffName(Common.get(rs.get(i).get("name"))) ;
		      data.setC1(Common.get(rs.get(i).get("c1")));
		      data.setC2(Common.get(rs.get(i).get("c2"))) ;
		      dataList.add(data);
		      i++;
	        } // end while
	        
	        i = 0;
	        int totalC1 = 0;
	        int totalC2 = 0;
	        while ( i < dataList.size() ) {
	        	totalC1 = totalC1 + Integer.parseInt( dataList.get(i).getC1() ) ;
	        	totalC2 = totalC2 + Integer.parseInt( dataList.get(i).getC2() ) ;
	        	i++ ;
	        } // end while
	        
	        parameters.put("totalC1", Integer.toString( totalC1 ) );
	        parameters.put("totalC2", Integer.toString( totalC2 ) );
	        report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
 	    } // end else
		return report ;
	  } // try
	  catch( Exception e  ) {
		e.printStackTrace();
	    if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
		else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	    return null ;
	  } // catch
	} // doPrintfPdf()	
	
} // PRE2007()