<!-- 
程式目的：自取案件處理
程式代號：pre2002
程式日期：1030527
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2002">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2002" />
</jsp:include>

<%
if ( "update".equals(obj.getState()) ) {
	obj.update() ;
	obj = (com.kangdainfo.tcfi.view.pre.PRE2002)obj.queryOne();
} // end if
else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE2002)obj.queryOne();
} // end if

%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_prefixNo,"預查編號");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;				
				break;
			case "doQueryAll":
				var x = "&nbsp";
				document.getElementById("ERRMSG").innerHTML = x;
				$('#state').val("queryAll") ;
				break;	
			case "doQueryOne":
				$('#state').val( "queryOne" ) ;
				break ;
			case "doUpdate":
				$('#state').val( "update" ) ;
				setBeforePageUnload(false) ;
				form1.submit();
				// setBeforePageUnload(false) ;
				break ;
			case "doClear":
				form1.q_prefixNo.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function keyDown() {
	if (event.keyCode==13) {
		$("#doQueryOne").click();
	} 
}

</script>
</head>
<body topmargin="5">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE2002'/>
</c:import>

<table width="100%" height="50%" cellspacing="0" cellpadding="0">
<!-- Query Area  -->
<tr>
<td class="bg" >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
       <tr>
		<td class="td_form" width="15%">預查編號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_prefixNo" size="10" maxlength="9" value="<%=obj.getQ_prefixNo()%>" onKeyDown="keyDown()">
           <span id="addButtonSpan">
		   		<input class="toolbar_default" type="submit" followPK="false" id="doQueryOne" name="doQueryOne" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
		   </span>
		   <span id="addButtonSpan">
				<input class="toolbar_default" type="button" followPK="false" id="doUpdate" name="doUpdate" value="確定存檔" >
			 	<script> 
    			document.getElementById('doUpdate').onclick = function() {
    				if ( form1.hiddenPrefixNo.value == "" ) {
    					alert("存檔前請先查詢一筆預查編號");
    					return false;
    				} 
    				else {
        				whatButtonFireEvent(this.name);
    				}
				}
				</script>
			</span>
			<span>
				<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)">
			</span>	
        </td>
	   </tr>			
  </table>
  </div>
</td>
</tr>

<!-- Form area -->
<tr>
<td class="bg" >
  <div id="formContainer" style="height:auto">  
  
   <table cellpadding=0 cedlsapcing=0>
    <tr>
    <TD align="left" class="tab_border1" id="selMAN1">
			      <DIV align="center" class="" id="selDIVMAN1">申請人資料</DIV>
	</TD>
    </tr>
    </table>
  <table class="table_form" width="100%" height="100%">  
      <tr>
        <td class="td_form" width="15%">姓名 ：</td>
        <td class="td_form_white" > 
           <input class="field_RO cmex" type="text" name="applyName" size="35" maxlength="35" value="<%=obj.getApplyName()%>">
        </td>
        <td class="td_form" width="15%">身分ID：</td>
        <td class="td_form_white" > 
           <input class="field_RO" type="text" name="applyId" size="15" maxlength="15" value="<%=obj.getApplyId()%>">
        </td>
      </tr>
	  <tr>
        <td class="td_form">電話 ：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="tel" size="18" maxlength="15" value="<%=obj.getTel()%>">
        </td>
        <td class="td_form">統一編號：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="banNo" size="15" maxlength="15" value="<%=obj.getBanNo()%>">
        </td>
      </tr>
      <tr>
        <td class="td_form">地址 ：</td>
        <td class="td_form_white" colspan="3"> 
           <input class="field_RO" type="text" name="addr" size="60" maxlength="60" value="<%=obj.getAddr()%>">
        </td>
      </tr>	 	 		
      <tr>
        <td class="td_form">預查名稱 ：</td>
        <td class="td_form_white" colspan="3"> 
           <input class="field_RO cmex" type="text" name="companyName" size="25" maxlength="35" value="<%=obj.getCompanyName()%>">
        </td>
      </tr>	 
  </table>
    <table cellpadding=0 cedlsapcing=0>
    <tr>
    <TD align="left" class="tab_border1" id="selMAN1">
	<DIV align="center" class="" id="selDIVMAN1">申請案資料</DIV>
	</TD>
    </tr>
    </table>
  	<table class="table_form" width="100%" height="100%">  
      <tr>
        <td class="td_form" width="15%">收件日期：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="receiveDate" size="10" maxlength="15" value="<%=obj.getReceiveDate()%>">
           <input class="field_RO" type="text" name="receiveTime" size="10" maxlength="15" value="<%=obj.getReceiveTime()%>">
        </td>
      </tr>
      <tr>
      <td class="td_form">核覆日期：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="approveDate" size="10" maxlength="15" value="<%=obj.getApproveDate()%>">
           <input class="field_RO" type="text" name="approveTime" size="10" maxlength="15" value="<%=obj.getApproveTime()%>">
        </td>
      </tr>
	  <tr>
        <td class="td_form">發文日期：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="closeDate" size="10" maxlength="15" value="<%=obj.getCloseDate()%>">
           <input class="field_RO" type="text" name="closeTime" size="10" maxlength="15" value="<%=obj.getCloseTime()%>">
        </td>
      </tr>
      <tr>
      <td class="td_form">取件日期：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="getDate" size="10" maxlength="15" value="<%=obj.getGetDate()%>">
           <input class="field_RO" type="text" name="getTime" size="10" maxlength="15" value="<%=obj.getGetTime()%>">
        </td>
      </tr>
      <tr>
        <td class="td_form">核覆結果：</td>
        <td class="td_form_white"> 
           <input class="field_RO" type="text" name="approveResult" size="10" maxlength="10" value="<%=obj.getApproveResult()%>">
        </td>
      </tr>
      <tr>
        <td class="td_form">備註：</td>
        <td class="td_form_white"> 
           <textarea rows="1" cols="75" class="field" type="text" name="remark1" maxlength="900" ><%=obj.getRemark1() %></textarea>
        </td>
      </tr>	 	 		
      <tr>
        <td class="td_form">領件方式： </td>
        <td class="td_form_white">
            <input type="radio" name="getKind" value="3" disabled <%="3".equals( obj.getGetKind() )?"checked":""%>>線上列印  <!--2024/03/17 新增線上列印 -->
		    <input type="radio" name="getKind" value="1" disabled <%="1".equals( obj.getGetKind() )?"checked":""%>>自取
		    <input type="radio" name="getKind" value="2" disabled <%="2".equals( obj.getGetKind() )?"checked":"" %>>郵寄
		</td>
      </tr>	 
  </table>
  </div>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
</td>
</tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:center;">
<input type="hidden" id="banNo" name="banNo" value="<%=obj.getBanNo()%>">
<input type="hidden" id="hiddenPrefixNo" name="hiddenPrefixNo" value="<%=obj.getHiddenPrefixNo()%>">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
<!-- 新增按鈕區 -->
<!--List區============================================================-->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>