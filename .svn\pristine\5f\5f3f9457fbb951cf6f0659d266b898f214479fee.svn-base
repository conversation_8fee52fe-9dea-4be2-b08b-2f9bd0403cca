package com.kangdainfo.tcfi.service.impl;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.model.eicm.dao.IndexLogDao;
import com.kangdainfo.tcfi.service.Com0001Service;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class Com0001ServiceImpl implements Com0001Service {

	@Override
	public void Fun_0010(String wsId, String param1, String param2, String param3, String executeDate, String createUser) {
		Fun_0010(wsId, param1, param2, param3, executeDate, createUser, null);
	}
	
	public void Fun_0010(String wsId , String param1, String param2, String param3, String executeDate, String createUser, String remark){
		//基本要有WS_ID, Param1
		if("".equals(Common.get(wsId)) || "".equals(Common.get(param1))) return;
		IndexLog obj = new IndexLog();
		obj.setWsId(wsId);
		obj.setParam1(param1);
		obj.setParam2(param2);
		obj.setParam3(param3);
		obj.setExecuteDate(executeDate);
		obj.setCreateDate(Datetime.getYYYMMDDHHMISS());
		obj.setCreateUser(createUser);
		obj.setStatus(PrefixConstants.INDEX_LOG_STATUS_0);//待執行
		obj.setRemark(remark);
		indexLogDao.insert(obj);
	}

	@Override
	public IndexLog Fun_0011(String execType, Long indexLogId, String msg) {
		if("".equals(Common.get(indexLogId)) || "".equals(Common.get(execType))) return null;
		IndexLog obj = new IndexLog();
		obj.setId(indexLogId);
		obj = indexLogDao.query(obj);
		if(obj != null){
			if(PrefixConstants.INDEX_LOG_STATUS_1.equals(execType))
				obj.setStartTime(Datetime.getYYYMMDDHHMISS());
			else if(PrefixConstants.INDEX_LOG_STATUS_2.equals(execType) || PrefixConstants.INDEX_LOG_STATUS_3.equals(execType))
				obj.setFinishTime(Datetime.getYYYMMDDHHMISS());
			obj.setStatus(execType);
			obj.setRemark(msg);
			
			return indexLogDao.update(obj);
		}
		return null;
	}
	
	public IndexLog checkIndexLog(String wsId, String param1){
		IndexLog bo = new IndexLog();
		bo.setWsId(wsId);
		bo.setParam1(param1);
		bo.setStatus(PrefixConstants.INDEX_LOG_STATUS_0);
		return indexLogDao.query(bo);
		
	}

	private IndexLogDao indexLogDao;

	public IndexLogDao getIndexLogDao() {return indexLogDao;}
	public void setIndexLogDao(IndexLogDao dao) {this.indexLogDao = dao;}

}