package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;


public class Cedb1019Dao extends BaseDaoJdbc implements RowMapper<Cedb1019> {

	public String findMaxPrefixNo(String twYear) {
		SQLJob sqljob = new SQLJob("SELECT PREFIX_NO, YEAR_NO FROM CEDB1019 WHERE YEAR_NO = ?");
		sqljob.addParameter(twYear);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1019> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), this);
		if(null!=list && !list.isEmpty()) {
			return list.get(0).getPrefixNo();
		}
		return "目前無可用資料";
	}

	// 依傳入的收文文號至資料庫中尋找相應的目標
	public synchronized Cedb1019 findByYearNo(String yearNo) {
		if("".equals(Common.get(yearNo))) return null;
		SQLJob sqljob = new SQLJob("SELECT YEAR_NO, PREFIX_NO FROM CEDB1019 WHERE YEAR_NO = ?");
		sqljob.addParameter(yearNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1019> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), this);
		if(null!=list && !list.isEmpty()) {
			return list.get(0);
		} else {
			return insert();
		}
	}

    public java.util.List<Cedb1019> find(Cedb1019 bo) {
		SQLJob sqljob = new SQLJob("SELECT PREFIX_NO, YEAR_NO FROM CEDB1019");
		if(null!=bo) {
			if ( !"".equals(Common.get(bo.getYearNo())) ) {
				sqljob.appendSQLCondition("YEAR_NO LIKE ?");
				sqljob.addLikeParameter(Common.get(bo.getYearNo()));
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if ( !"".equals(Common.get(bo.getPrefixNo())) ) {
				sqljob.appendSQLCondition("PREFIX_NO LIKE ?");
				sqljob.addLikeParameter(Common.get(bo.getPrefixNo()));
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
		}
		sqljob.appendSQL("ORDER BY YEAR_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray(), this);
	}

    public java.util.List<Cedb1019> list() {
		SQLJob sqljob = new SQLJob("SELECT PREFIX_NO, YEAR_NO FROM CEDB1019");
		sqljob.appendSQL("ORDER BY YEAR_NO");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public Cedb1019 insert() {
		String yearNo = Datetime.getYYY();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("INSERT INTO CEDB1019 (PREFIX_NO,YEAR_NO) values (?,?)");
		sqljob.addParameter(yearNo+"000000");
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(yearNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	    return findByYearNo(yearNo);
	}

	public Cedb1019 update(Cedb1019 bo) {
		if(!"".equals(Common.get(bo.getYearNo()))){
			// 表示資料庫內有該筆資料
			// update
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("UPDATE CEDB1019 SET");
			sqljob.appendSQL("PREFIX_NO=?");
			sqljob.appendSQL("WHERE YEAR_NO = ?");
			sqljob.addParameter(bo.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getYearNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	    return findByYearNo(bo.getYearNo());
	}
	
	/**
	 * 傳入一個Cedb1019,內含舊的預查編號,依據舊的預查編號產生新的預查編號
	 * 新的預查編號 = 舊的預查編號加 1
	 * @param cedb1019
	 * @return
	 * @throws Exception
	 */
	public synchronized Cedb1019 updatePrefixNoSeq(Cedb1019 cedb1019) throws Exception {
		int preno = Integer.parseInt(cedb1019.getPrefixNo());
		String newprefixno;
		preno++;
		// 將預查編號補齊"0"+93000001
		if (String.valueOf(preno).length() == 8) 
			newprefixno = "0" + String.valueOf(preno);
		else
			newprefixno = String.valueOf(preno);
		// 可以用到民國999年啦
		cedb1019.setPrefixNo(newprefixno);
		update(cedb1019);
		return cedb1019;
	}

	@Override
	public Cedb1019 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1019 obj = null;
		if (null != rs) {
			obj = new Cedb1019();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setYearNo(rs.getString("YEAR_NO"));
		}
		return obj;
	}

}