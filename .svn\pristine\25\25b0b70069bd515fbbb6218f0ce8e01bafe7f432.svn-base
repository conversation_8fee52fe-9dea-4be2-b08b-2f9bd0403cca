package com.kangdainfo.tcfi.view.pre;

/*
程式目的：申辦案件(收文方式)統計分析表
程式代號：pre4003
撰寫日期：103.05.05
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.BeanPropertyRowMapper;


import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE4003 extends SuperBean {	
  private String q_DateStart ;  // 收件日期起
  private String q_DateEnd ;    // 收件日期迄
  private String withLimit;
  
  private String kind ;         // 申請方式
  private String sss ;          // 設立案件數
  private String ccc ;          // 變更案件數
  private String total ;        // 合計
  private String ratio ;        // 比例
  
  // ----------------------------------getters and setters of local variable bellow ---------------------------
  
  public String getQ_DateStart() {return checkGet(q_DateStart);}
  public void setQ_DateStart(String s) {q_DateStart = checkSet(s);}
  public String getQ_DateEnd() {return checkGet(q_DateEnd);}
  public void setQ_DateEnd(String s) {q_DateEnd = checkSet(s);}
  
  public String getWithLimit() {return checkGet(withLimit);}
  public void setWithLimit(String s) {withLimit = checkSet(s);}

  public String getKind() {return checkGet(kind);}
  public void setKind(String s) {kind = checkSet(s);}
  public String getSss() {return checkGet(sss);}
  public void setSss(String s) {sss = checkSet(s);}
  public String getCcc() {return checkGet(ccc);}
  public void setCcc(String s) {ccc = checkSet(s);}
  public String getTotal() {return checkGet(total);}
  public void setTotal(String s) {total = checkSet(s);}
  public String getRatio() {return checkGet(ratio);}
  public void setRatio(String s) {ratio = checkSet(s);}
  
  // ----------------------------------------------------------------------------------------------------------

  // ----------------------------------function never used bellow----------------------------------------------
  
  public void doCreate() throws Exception{	  
  } // end doCreate()
	  
  public void doUpdate() throws Exception{
  } // end doUpdate()		
	  
  public void doDelete() throws Exception{			
  } // end doDelete()	
	  
  public Object doQueryOne() throws Exception{ 
    return null ;
  } // end doQueryOne()
  
  public String RatioFormat( double inputNum ) {
    inputNum = inputNum * 100 ;
    DecimalFormat df = new DecimalFormat("##0.00");
    String ratio = df.format(inputNum);
    return ratio ;
  } // RatioFormat()

  public static SQLJob doAppendSqljob(String dateStart, String dateEnd, String withLimit) {
	  SQLJob sqljob = new SQLJob();
      sqljob.appendSQL("select 1,'線上申辦' as kind");
      sqljob.appendSQL(  ",nvl(sum(ss),0) as sss,nvl(sum(cc),0) as ccc,nvl(sum(ss),0)+nvl(sum(cc),0) as total");
      sqljob.appendSQL("from (");
      sqljob.appendSQL(  "select telix_no,id_no");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no=a.prefix_no and change_type = '0') as ss");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no=a.prefix_no and (change_type is null or change_type <> '0')) as cc");
      sqljob.appendSQL(  "from eicm.cedb1000 a ");
      if (!"3".equals(withLimit)) 
    	  sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
      sqljob.appendSQL( "where a.receive_date>=? and a.receive_date<=? AND A.PREFIX_NO <> '10305782X'" ) ;
      if (!"3".equals(withLimit)) {
    	  if ("1".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");
    	  } else if ("2".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
    	  }
      }
      
      sqljob.appendSQL(") b");
      sqljob.appendSQL("where ( b.telix_no like 'A%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) <> 'LL' ) )");
      sqljob.appendSQL("union");
      sqljob.appendSQL("select 2,'線上審核' as kind");
      sqljob.appendSQL(  ",nvl(sum(ss),0) as sss,nvl(sum(cc),0) as ccc,nvl(sum(ss),0)+nvl(sum(cc),0) as total");
      sqljob.appendSQL("from (");
      sqljob.appendSQL(  "select telix_no,id_no");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and change_type = '0') as ss");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and (change_type is null or change_type <> '0')) as cc");
      sqljob.appendSQL(  "from eicm.cedb1000 a");
      if (!"3".equals(withLimit)) 
    	  sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
      sqljob.appendSQL(  "where a.receive_date>=? and a.receive_date<=?");
      if (!"3".equals(withLimit)) {
    	  if ("1".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");  
    	  } else if ("2".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
    	  }
      }
    	  
      sqljob.appendSQL(") b");
      sqljob.appendSQL("where ( b.telix_no like 'L%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) = 'LL' ) )");
      sqljob.appendSQL("union");
      sqljob.appendSQL("select 3,'一維條碼' as kind");
      sqljob.appendSQL(  ",nvl(sum(ss),0) as sss,nvl(sum(cc),0) as ccc,nvl(sum(ss),0)+nvl(sum(cc),0) as total");
      sqljob.appendSQL("from (");
      sqljob.appendSQL(  "select telix_no,id_no");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and change_type = '0') as ss");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and (change_type is null or change_type <> '0')) as cc");
      sqljob.appendSQL(  "from eicm.cedb1000 a");
      if (!"3".equals(withLimit)) 
    	  sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
      sqljob.appendSQL(  "where a.receive_date>=? and a.receive_date<=?");
      if (!"3".equals(withLimit)) {
    	  if ("1".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");  
    	  } else if ("2".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
    	  }
      }

      sqljob.appendSQL(") b");
      sqljob.appendSQL("where (b.telix_no like '0%' or b.telix_no like 'Z%')");
      sqljob.appendSQL("union");
      sqljob.appendSQL("select 4,'紙本收文' as kind");
      sqljob.appendSQL(  ",nvl(sum(ss),0) as sss,nvl(sum(cc),0) as ccc,nvl(sum(ss),0)+nvl(sum(cc),0) as total");
      sqljob.appendSQL("from (");
      sqljob.appendSQL(  "select telix_no,id_no");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and change_type = '0') as ss");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and (change_type is null or change_type <> '0')) as cc ");
      sqljob.appendSQL(  "from eicm.cedb1000 a");
      if (!"3".equals(withLimit)) 
    	  sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
      sqljob.appendSQL(  "where a.receive_date>=? and a.receive_date<=?");
      if (!"3".equals(withLimit)) {
    	  if ("1".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");  
    	  } else if ("2".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
    	  }
      }
      sqljob.appendSQL(") b");
      sqljob.appendSQL("where (b.telix_no is null)") ;
      sqljob.appendSQL("union");
      sqljob.appendSQL("select 5,'合計' as kind") ;
      sqljob.appendSQL(  ",nvl(sum(ss),0) as sss,nvl(sum(cc),0) as ccc,nvl(sum(ss),0)+nvl(sum(cc),0) as total") ;
      sqljob.appendSQL("from (");
      sqljob.appendSQL(  "select telix_no,id_no");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and change_type = '0') as ss");
      sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and (change_type is null or change_type <> '0')) as cc");
      sqljob.appendSQL(  "from eicm.cedb1000 a");
      if (!"3".equals(withLimit)) 
    	  sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
      sqljob.appendSQL(  "where a.receive_date>=? and a.receive_date<=?");
      if (!"3".equals(withLimit)) {
    	  if ("1".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");  
    	  } else if ("2".equals(withLimit)) {
    		  sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
    	  }
      }
      sqljob.appendSQL(") b");
      sqljob.appendSQL("where ( b.telix_no like 'A%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) <> 'LL' ) )");
      sqljob.appendSQL("or ( b.telix_no like 'L%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) = 'LL' ) )");
      sqljob.appendSQL("or (b.telix_no like '0%' or b.telix_no like 'Z%')");
      sqljob.appendSQL("or (b.telix_no is null)") ;
      sqljob.appendSQL("order by 1");
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      sqljob.addParameter(dateStart);
      sqljob.addParameter(dateEnd);
      return sqljob ;
  } // doAppendSqljob()

	public ArrayList<String[]> doQueryAll() throws Exception {   
		try {
			System.out.println(getWithLimit());
			Integer total = count(getQ_DateStart(), getQ_DateEnd(), getWithLimit());
			if ( 0 == total ) {
				setErrorMsg( "查無資料，請變更查詢條件" );
				throw new MoeaException( "查無資料，請變更查詢條件" ) ;
			} // if
			else {
				List<?> datas = 
						ServiceGetter.getInstance().getEicmGeneralQueryDao().query(
								doAppendSqljob(getQ_DateStart(), getQ_DateEnd(), getWithLimit()),
								BeanPropertyRowMapper.newInstance(PRE4003.class));

				Integer netCount = 0;//網路合計
				Integer sssCount = 0;//設立案件數合計
				Integer cccCount = 0;//變更案件數合計
				Integer totalCount = 0;//合計
				
				ArrayList<String[]> dataList = new ArrayList<String[]>();
				String[] rowArray = new String[5];
				for(Object d4003 : datas) {
					PRE4003 data = (PRE4003)d4003;
					rowArray = new String[5];
					rowArray[0] = data.getKind();
					rowArray[1] = data.getSss();
					rowArray[2] = data.getCcc();
					rowArray[3] = data.getTotal();
					rowArray[4] = RatioFormat( Double.parseDouble(data.getTotal()) / new Double(total) )+"%";
					dataList.add(rowArray);

					if ( "合計".equals( data.getKind() ) ) {
						sssCount = Integer.parseInt(data.getSss());
						cccCount = Integer.parseInt(data.getCcc());
						totalCount = Integer.parseInt(data.getTotal());
					}

					//if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) || "一維條碼".equals(data.getKind()) ) {
					//	netCount += Integer.parseInt(data.getTotal());
					//}
					//一維條碼 不計入 網路合計(2015.01.14)
					if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) ) {
						netCount += Integer.parseInt(data.getTotal());
					}
				}
				
				//比例
				String[] ratioRowArray = new String[5];
				ratioRowArray[0] = "比例";
				ratioRowArray[1] = RatioFormat( new Double(sssCount) / new Double(total) )+"%";
				ratioRowArray[2] = RatioFormat( new Double(cccCount) / new Double(total) )+"%";
				ratioRowArray[3] = RatioFormat( new Double(totalCount) / new Double(total) )+"%";
				ratioRowArray[4] = RatioFormat( new Double(netCount) / new Double(total) )+"%(網路合計)";
				dataList.add(ratioRowArray) ;

				setErrorMsg("查詢成功 ！");
				return dataList;
			} // end else
		} // try
		catch ( Exception e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		} // catch
	} // doQueryAll()

	// -----------------------------------------------------------------------------------------------------------  
	public File doPrintPdf() throws Exception {  
		File report = null ;
		try {
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4003.jasper");
			
			System.out.println(jasperPath);
			Map<String, Object> parameters = new HashMap<String,Object>();
			parameters.put("printDate", "列印日期："+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印時間
			parameters.put("printTime", "列印時間："+Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間
			parameters.put("dateStart", Common.formatYYYMMDD(getQ_DateStart(),2))  ;
			parameters.put("dateEnd", Common.formatYYYMMDD(getQ_DateEnd(),2)) ;
			parameters.put("withLimit", getWithLimit());

			Integer total = count(getQ_DateStart(), getQ_DateEnd(), getWithLimit());
			if ( 0 == total ) {
				setErrorMsg( "查無資料，請變更查詢條件" );
				throw new MoeaException( "查無資料，請變更查詢條件" ) ;
			} // end if
			else {
				List<?> datas =
						ServiceGetter.getInstance().getEicmGeneralQueryDao().query(
								doAppendSqljob(getQ_DateStart(), getQ_DateEnd(), getWithLimit()),
								BeanPropertyRowMapper.newInstance(PRE4003.class));

				Integer netCount = 0;//網路合計
				Integer sssCount = 0;//設立案件數合計
				Integer cccCount = 0;//變更案件數合計
				Integer totalCount = 0;//合計

				for(Object d4003 : datas) {
					PRE4003 data = (PRE4003)d4003;
					data.setRatio(RatioFormat( Double.parseDouble(data.getTotal()) / new Double(total) )+"%");

					if ( "合計".equals( data.getKind() ) ) {
						sssCount = Integer.parseInt(data.getSss());
						cccCount = Integer.parseInt(data.getCcc());
						totalCount = Integer.parseInt(data.getTotal());
					}

					//if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) || "一維條碼".equals(data.getKind()) ) {
					//	netCount += Integer.parseInt(data.getTotal());
					//}
					//一維條碼 不計入 網路合計(2015.01.14)
					if ( "線上申辦".equals(data.getKind()) || "線上審核".equals(data.getKind()) ) {
						netCount += Integer.parseInt(data.getTotal());
					}
				}
				
				//比例
			    parameters.put( "sssRatio", RatioFormat( new Double(sssCount) / new Double(total) )+"%" ) ;
			    parameters.put( "cccRatio", RatioFormat( new Double(cccCount) / new Double(total) )+"%" );
			    parameters.put( "totalRatio", RatioFormat( new Double(totalCount) / new Double(total) )+"%" ) ;
			    parameters.put( "ratioRatio", RatioFormat( new Double(netCount) / new Double(total) )+"%(網路合計)" ) ;

				report = JasperReportMaker.makePdfReport(datas, parameters, jasperPath);
			} // end else
		} // end try 
		catch ( Exception e ) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<300) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		} // end catch
		return report ;
	} // doPrintfPdf()

	public static String checkForjsp( String dateStart, String dateEnd, String withLimit) {
		Integer total = count(dateStart, dateEnd, withLimit);
		if ( 0 == total ) 
			return "查無資料，請變更查詢條件！";
		else
			return "ok";
	}

	public static Integer count(String dateStart, String dateEnd, String withLimit) {
		Integer result = 0;
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select ") ;
	    sqljob.appendSQL(  "nvl(sum(ss),0)+nvl(sum(cc),0) as c") ;
	    sqljob.appendSQL("from (");
	    sqljob.appendSQL(  "select telix_no,id_no");
	    sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and change_type = '0') as ss");
	    sqljob.appendSQL(  ",(select 1 from eicm.cedb1023 where prefix_no = a.prefix_no and (change_type is null or change_type <> '0')) as cc");
	    sqljob.appendSQL(  "from eicm.cedb1000 a");
	    if (!"3".equals(withLimit)) 
	    	sqljob.appendSQL(" join eicm.cedb1023 r on a.prefix_no = r.prefix_no ");
	    sqljob.appendSQL(  "where a.receive_date>=? and a.receive_date<=?");
	    if (!"3".equals(withLimit)) {
	    	if ("1".equals(withLimit)) {
	    		sqljob.appendSQL(" and nvl(r.org_type,'00') <> '05' ");
	    	} else if ("2".equals(withLimit)) {
	    		sqljob.appendSQL(" and nvl(r.org_type,'00') = '05' ");
	    	}
	    }
	    sqljob.appendSQL(") b");
	    sqljob.appendSQL("where ( b.telix_no like 'A%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) <> 'LL' ) )");
	    sqljob.appendSQL("	or ( b.telix_no like 'L%' or ( b.telix_no like 'O%' and nvl(b.id_no, 0) = 'LL' ) )");
	    sqljob.appendSQL("	or (b.telix_no like '0%' or b.telix_no like 'Z%')");
	    sqljob.appendSQL("	or (b.telix_no is null)") ;
		sqljob.addParameter(dateStart);
	    sqljob.addParameter(dateEnd);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			result = Integer.parseInt(datas.get(0).get("c").toString());
		}
		return result;
	}
} // PPE4003()