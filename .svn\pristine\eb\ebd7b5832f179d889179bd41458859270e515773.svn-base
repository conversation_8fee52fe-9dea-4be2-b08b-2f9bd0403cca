package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc053;

public class Cedbc053Dao extends BaseDaoJdbc implements RowMapper<Cedbc053> {

	private static final String DEFAULT_ORDER = "ORDER BY COUNTRY_CODE";
	private static final String SQL_findAll = "SELECT * FROM CEDBC053";
	public List<Cedbc053> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(DEFAULT_ORDER);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public java.util.List<Cedbc053> find(Cedbc053 bo) {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if ( !"".equals(Common.get(bo.getCountryCode())) ) {
			sqljob.appendSQL("COUNTRY_CODE LIKE ?");
			sqljob.addLikeParameter(bo.getCountryCode());
		}
		if ( !"".equals(Common.get(bo.getCountry())) ) {
			sqljob.appendSQL("COUNTRY LIKE ?");
			sqljob.addLikeParameter(bo.getCountry());
		}
		sqljob.appendSQL(DEFAULT_ORDER);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByCountryCode = "SELECT * FROM CEDBC053 WHERE COUNTRY_CODE = ?";

	/**
	 * 取得所有國籍名稱與國籍代碼(CEDBC053)
	 * @throws MoeaException 預查系統Exception, ApplicationResource Key = error.NO_DATA
	 * @throws Exception
	 * @return Vector 符合條件的資料,型別為 Cedbc053.class
	 */
	public Cedbc053 findByCountryCode(String countryCode) {
		SQLJob sqljob = new SQLJob(SQL_findByCountryCode);
		sqljob.addParameter(countryCode);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedbc053> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : (Cedbc053) list.get(0);
	}

	public Cedbc053 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc053 obj = null;
		if(null!=rs) {
			obj = new Cedbc053();
			obj.setCountryCode(rs.getString("COUNTRY_CODE"));
			obj.setCountry(rs.getString("COUNTRY"));
		}
		return obj;
	}

}