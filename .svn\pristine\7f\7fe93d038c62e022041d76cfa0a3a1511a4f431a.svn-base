package com.kangdainfo.util;

import java.util.MissingResourceException;
import java.util.ResourceBundle;
import java.util.Set;


public abstract class ResourcePropertiesUtils
{
	public static Boolean checkResourceExist(String bundle)
	{
		Boolean isExist = false;
		try{
			ResourceBundle.getBundle(bundle);
			isExist = true;
		}catch(MissingResourceException e){
			e.printStackTrace();
		}
		return isExist;
	}

	public static String getValue(String bundle, String key)
	{
		ResourceBundle res = ResourceBundle.getBundle(bundle);
		String value = res.getString(key);
		return value;
	}
	
	public static boolean containsKey(String bundle, String key)
	{
		ResourceBundle res = ResourceBundle.getBundle(bundle);
		return res.containsKey(key);
	}

	public static Set<String> getKeySet(String bundle)
	{
		ResourceBundle res = ResourceBundle.getBundle(bundle);
		return res.keySet();
	}
}