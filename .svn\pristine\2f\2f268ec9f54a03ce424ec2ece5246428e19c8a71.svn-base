package com.kangdainfo.tcfi.model.osss.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;

public class OssmOrgChangeDao extends BaseDaoJdbc implements RowMapper<OssmOrgChange> {

	private static final String SQL_findByTelixNo = "SELECT * FROM OSSM_ORG_CHANGE WHERE TELIX_NO = ?";
	public OssmOrgChange findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<OssmOrgChange> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	@Override
	public OssmOrgChange mapRow(ResultSet rs, int idx) throws SQLException {
		OssmOrgChange obj = null;
		if(null!=rs) {
			obj = new OssmOrgChange();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setApplicantType(rs.getString("APPLICANT_TYPE"));
			obj.setCorpName(rs.getString("CORP_NAME"));
			obj.setCorpBanNo(rs.getString("CORP_BAN_NO"));
			obj.setCorpApplyName(rs.getString("CORP_APPLY_NAME"));
			obj.setCorpApplyId(rs.getString("CORP_APPLY_ID"));
			obj.setCmpyAreaCode(rs.getString("CMPY_AREA_CODE"));
			obj.setCmpyNeiborCode(rs.getString("CMPY_NEIBOR_CODE"));
			obj.setCmpyAddr(rs.getString("CMPY_ADDR"));
			obj.setCmpyZipCode(rs.getString("CMPY_ZIP_CODE"));
			obj.setCmpyAddrComb(rs.getString("CMPY_ADDR_COMB"));
			obj.setRegUnitName(rs.getString("REG_UNIT_NAME"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setCmpyRemitEname(rs.getString("CMPY_REMIT_ENAME"));
		}
		return obj;
	}
}
