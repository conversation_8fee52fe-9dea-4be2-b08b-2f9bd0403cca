package com.kangdainfo.tcfi.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.StringUtility;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;
import com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiptNoSetupDAO;
import com.kangdainfo.tcfi.service.Pre5001Service;
import com.kangdainfo.tcfi.view.pre.PRE5002;
import com.kangdainfo.util.report.JasperReportMaker;


public class Pre5001ServiceImpl implements Pre5001Service {
	ReceiptNoSetupDAO receiptNoSetupDAO;
	PrefixReceiptNoDAO prefixReceiptNoDAO;
	Eedb1000Dao eedb1000DAO;
	Eedb3300Dao eedb3300DAO;
	public ReceiptNoSetupDAO getReceiptNoSetupDAO() {
		return receiptNoSetupDAO;
	} 
	public void setReceiptNoSetupDAO( ReceiptNoSetupDAO dao) {
		this.receiptNoSetupDAO = dao;
	}
	public PrefixReceiptNoDAO getPrefixReceiptNoDAO() {
		return prefixReceiptNoDAO;
	}
	public void setPrefixReceiptNoDAO(PrefixReceiptNoDAO prefixReceiptNoDAO) {
		this.prefixReceiptNoDAO = prefixReceiptNoDAO;
	}
	public Eedb1000Dao getEedb1000DAO() {
		return eedb1000DAO;
	}
	public void setEedb1000Dao(Eedb1000Dao eedb1000DAO ) {
		this.eedb1000DAO = eedb1000DAO;
	}
	public Eedb3300Dao getEedb3300DAO() {
		return eedb3300DAO;
	}
	public void setEedb3300Dao(Eedb3300Dao eedb3300DAO ) {
		this.eedb3300DAO = eedb3300DAO;
	}

	
	public ReceiptNoSetup selectReceiptNoSetupByReceiptType(String receiptType) {
		return receiptNoSetupDAO.selectByReceiptType(receiptType);
	}
	public boolean insertPrefixReceiptNoAndUpdateReceiptNoSetup(PrefixReceiptNo vo1, ReceiptNoSetup vo2) throws Exception {
		return prefixReceiptNoDAO.insert(vo1) != 0 && receiptNoSetupDAO.update(vo2)!= 0;
	}
	public Eedb1000 selectEedb1000ByTelixNo(String telixNo) {
		return eedb1000DAO.findByTelixNo(telixNo);
	}
	public Eedb3300 selectEedb3300ByTelixNo(String telixNo) {
		return eedb3300DAO.findByTelixNo(telixNo);
	}
	public PrefixReceiptNo selectPrefixReceiptNo(String receiptNo) {
		return prefixReceiptNoDAO.selectByReceiptNo(receiptNo);
	}
	public PrefixReceiptNo selectPrefixReceiptNoByTelixNo(String telixNo) {
		return prefixReceiptNoDAO.selectByTelixNo(telixNo);
	}
	
	public File createPdfFile(PrefixReceiptNo prefixReceiptNo) throws Exception {
		
		File report = null ;
		String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre5001.jasper");
		String imageAccountantPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/accountant.png");
		String imageCashierPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/cashier.png");
		String imageDirectorPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("images/report/director.png");
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		String amount = "";
		String applyName = "";
		if (prefixReceiptNo != null) {
			if (prefixReceiptNo.getPayName() == null || "".equals(prefixReceiptNo.getPayName())) {
				Eedb1000 eedb1000 = selectEedb1000ByTelixNo(prefixReceiptNo.getTelixNo());
				if (eedb1000 != null) {
					applyName = eedb1000.getApplyName(); 
				}
			} else {
				applyName = prefixReceiptNo.getPayName();// TODO 需擬用4011邏輯重新修改，2024/10/17
			}
			amount = String.valueOf(prefixReceiptNo.getAmount());
		}
		
		Map<String, Object> parameters = new HashMap<String,Object>();
		parameters.put("printDate", "中華民國"+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印時間
		parameters.put("applyName", applyName);
		parameters.put("amount", "".equals(amount)?"300":amount);
		parameters.put("prefixNo", prefixReceiptNo.getPrefixNo());
		parameters.put("receiptNo", prefixReceiptNo.getReceiptNo());
		parameters.put("staffName", user.getUserName());
		parameters.put("imageAccountantPath", imageAccountantPath);
		parameters.put("imageCashierPath", imageCashierPath);
		parameters.put("imageDirectorPath", imageDirectorPath);
		
		String remark = "";
		String payType = prefixReceiptNo.getPayType();
		if ("1".equals(payType) || "5".equals(payType)) {
			if (StringUtils.isNotBlank(prefixReceiptNo.getChNo())) {
				remark = prefixReceiptNo.getChNo();
			}
		} else if ("6".equals(payType)) {
			remark = "悠遊卡" + StringUtility.chgNullToEmpty(prefixReceiptNo.getChNo());
		} else if ("7".equals(payType)) {
			remark = "台灣Pay" + StringUtility.chgNullToEmpty(prefixReceiptNo.getChNo());
		}
		
		parameters.put("remark", remark);
		List<PRE5002> datas = new ArrayList<PRE5002>();		
		PRE5002 pre5002 = new PRE5002();
		datas.add(pre5002);
		report = JasperReportMaker.makePdfReport(datas, parameters, jasperPath);
		return report;
	}
	
}