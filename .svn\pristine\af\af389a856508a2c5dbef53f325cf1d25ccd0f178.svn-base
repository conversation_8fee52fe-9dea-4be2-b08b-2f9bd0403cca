package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * <AUTHOR>
 * 有限合夥各類代碼對照檔
 * 113/04/16
 */
public class LmsdCodemapping extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String agencyCode;
	
	private String regUnitCode;
	
	private String kind;
	
	private String code;
	
	private String description;
	
	private String remark;
	
	private String keyinStaffCode;
	
	private String keyinStaffName;
	
	private String keyinDate;
	
	private String enable;

	public String getAgencyCode() {
		return agencyCode;
	}

	public void setAgencyCode(String agencyCode) {
		this.agencyCode = agencyCode;
	}

	public String getRegUnitCode() {
		return regUnitCode;
	}

	public void setRegUnitCode(String regUnitCode) {
		this.regUnitCode = regUnitCode;
	}

	public String getKind() {
		return kind;
	}

	public void setKind(String kind) {
		this.kind = kind;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getKeyinStaffCode() {
		return keyinStaffCode;
	}

	public void setKeyinStaffCode(String keyinStaffCode) {
		this.keyinStaffCode = keyinStaffCode;
	}

	public String getKeyinStaffName() {
		return keyinStaffName;
	}

	public void setKeyinStaffName(String keyinStaffName) {
		this.keyinStaffName = keyinStaffName;
	}

	public String getKeyinDate() {
		return keyinDate;
	}

	public void setKeyinDate(String keyinDate) {
		this.keyinDate = keyinDate;
	}

	public String getEnable() {
		return enable;
	}

	public void setEnable(String enable) {
		this.enable = enable;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
