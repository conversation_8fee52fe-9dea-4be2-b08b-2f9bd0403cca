package com.kangdainfo.tcfi.view.test;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;

/**
 * 計算工作日數
 *
 */
public class CountWorkDay extends SuperBean {

	/** 查詢條件 */
	private String q_prefixNo;
	/** 資料欄位 */
	private String prefixNo;
	private String idNo;
	private String processDate;
	private String processTime;
	private String processStatus;
	private float workDay;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<Cedb1010> objList = ServiceGetter.getInstance().getPrefixService().getCedb1010sByPrefixNo(q_prefixNo);			
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[6];
			for(Cedb1010 o : objList) {
				rowArray = new String[6];
				rowArray[0] = Common.get(o.getPrefixNo());
				rowArray[1] = Common.get(o.getIdNo());
				rowArray[2] = Common.get(o.getProcessDate());
				rowArray[3] = Common.get(o.getProcessTime());
				rowArray[4] = Common.get(o.getProcessStatus());
				rowArray[5] = Common.get(o.getWorkDay());
				arrList.add(rowArray);
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {
		CountWorkDay obj = this;
		Cedb1010 o = ServiceGetter.getInstance().getPrefixService().getCedb1010ByUniqueKey(prefixNo, idNo, processStatus);
		if (null!=o) {
	        obj.setPrefixNo(o.getPrefixNo());
	        obj.setIdNo(o.getIdNo());
	        obj.setProcessDate(o.getProcessDate());
	        obj.setProcessTime(o.getProcessTime());
	        obj.setProcessStatus(o.getProcessStatus());
	        obj.setWorkDay(o.getWorkDay());
		} else throw new Exception("查無該筆資料！");
		return obj;
	}

	@Override
	public void doUpdate() throws Exception {
		Cedb1010 o = ServiceGetter.getInstance().getPrefixService().getCedb1010ByUniqueKey(prefixNo, idNo, processStatus);
		if(null==o) throw new Exception("資料不存在!");
		o.setWorkDay(ServiceGetter.getInstance().getCaseFlowService().getWorkDay(o));
		ServiceGetter.getInstance().getPrefixService().updateCedb1010(o);
	}

	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getIdNo() {return checkGet(idNo);}
	public void setIdNo(String s) {this.idNo = checkSet(s);}
	public String getProcessDate() {return checkGet(processDate);}
	public void setProcessDate(String s) {this.processDate = checkSet(s);}
	public String getProcessTime() {return checkGet(processTime);}
	public void setProcessTime(String s) {this.processTime = checkSet(s);}
	public String getProcessStatus() {return checkGet(processStatus);}
	public void setProcessStatus(String s) {this.processStatus = checkSet(s);}
	public float getWorkDay() {return workDay;}
	public void setWorkDay(float workDay) {this.workDay = workDay;}

}