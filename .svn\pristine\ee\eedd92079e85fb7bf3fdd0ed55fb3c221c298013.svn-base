package com.kangdainfo.tcfi.service.impl;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao;
import com.kangdainfo.tcfi.service.Pre8005Service;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 人工更改分文
 *
 */
public class Pre8005ServiceImpl implements Pre8005Service {
	
	Cedbc000Dao cedbc000Dao;
	Cedb1000Dao cedb1000Dao;
	Cedb1010Dao cedb1010Dao;

	public Cedbc000Dao getCedbc000Dao() {return cedbc000Dao;}
	public void setCedbc000Dao(Cedbc000Dao dao) {this.cedbc000Dao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	
	public void doSave( String[] checkedPrefixNo, String[] seqNoList, String[] idNo, String userId ) throws Exception {
		Cedb1000 cedb1000;
        Cedbc000 cedbc000;
		int seqNo = -1;
		for (int i=0; i<checkedPrefixNo.length;i++) {
		    cedb1000 = getCedb1000Dao().findByPrefixNo(checkedPrefixNo[i], null);
		    seqNo = Integer.parseInt(seqNoList[i]);
		    if ( !"".equals(idNo[seqNo]) && !cedb1000.getIdNo().equals(idNo[seqNo]) ) {
		    	cedb1000.setIdNo(idNo[seqNo]);
		    	cedbc000 = getCedbc000Dao().findByIdNo(cedb1000.getIdNo());
		    	cedb1000.setStaffName(cedbc000.getStaffName());
		    	cedb1000.setRcvCheck("");
		    	cedb1000.setAssignDate(Datetime.getYYYMMDD());
				cedb1000.setAssignTime(Datetime.getHHMMSS());
				cedb1000.setUpdateDate(Datetime.getYYYMMDD());
				cedb1000.setUpdateTime(Datetime.getHHMMSS());
				cedb1000.setUpdateIdNo(userId);
				
				//寫入案件歷程
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000.getPrefixNo(), userId, PrefixConstants.PREFIX_STATUS_3);
				//備份主檔
				ServiceGetter.getInstance().getBackupService().doBackup(cedb1000.getPrefixNo(), userId);
				getCedb1000Dao().setWhenPre8005(cedb1000);
		    } // if
		} // for
	} // doSave()
}