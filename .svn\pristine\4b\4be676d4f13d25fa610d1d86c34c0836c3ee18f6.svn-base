package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.BusiItem;

public class BusiItemDao extends BaseDaoJdbc implements RowMapper<BusiItem> {

	private static final String SQL_find = "SELECT * FROM BUSI_ITEM ";
	public List<BusiItem> findAll() {
		SQLJob sqljob = new SQLJob(SQL_find);
		sqljob.appendSQL(" ORDER BY ITEM_CODE");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<BusiItem>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public List<BusiItem> findAllCondition( String itemCode, String businessItem, String masterCode, int length) {
		SQLJob sqljob = new SQLJob(SQL_find);
		if ( !"".equals( Common.get(itemCode) ) ){
			sqljob.appendSQLCondition(" ITEM_CODE LIKE ? ");
			sqljob.addSuffixLikeParameter(itemCode);
		}
		if ( !"".equals( Common.get(businessItem) ) ){
			sqljob.appendSQLCondition(" BUSINESS_ITEM LIKE ? ");
			sqljob.addLikeParameter(businessItem);
		}
		if ( !"".equals( Common.get(masterCode) ) ){
			sqljob.appendSQLCondition(" MASTER_CODE LIKE ? ");
			sqljob.addSuffixLikeParameter(masterCode);
		}
		if(length > 0){
			sqljob.appendSQLCondition( " LENGTH(ITEM_CODE) = ? " );
			sqljob.addParameter(length);
		}
		sqljob.appendSQL( " order by item_code" );
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<BusiItem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public BusiItem findByItemCode( String itemCode ) {
		if("".equals(Common.get(itemCode)))	return null;
		SQLJob sqljob = new SQLJob(SQL_find);
		sqljob.appendSQLCondition( " ITEM_CODE =  ? " ) ;
		sqljob.addParameter(itemCode);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<BusiItem> list =  (List<BusiItem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0) ; 
	}
	
	public BusiItem update( BusiItem obj ) {
		if ( obj == null || "".equals(Common.get(obj.getId())))	return null;
		SQLJob sqljob = new SQLJob("update busi_item set ");
		sqljob.appendSQL("business_item = ? ");
		sqljob.appendSQL(",master_code = ? ");
		sqljob.appendSQL(",moea_post_date = ? ");
		sqljob.appendSQL(",moea_post_no = ? ");
		sqljob.appendSQL(",reserve_365 = ? ");	
		sqljob.appendSQL(",mod_id_no = ? ");
		sqljob.appendSQL(",mod_date = ? ");
		sqljob.appendSQL(",mod_time = ? ");
		sqljob.appendSQL(" where item_code = ? ");
		Object[] parameters = {
				 obj.getBusinessItem()
				,obj.getMasterCode()
				,obj.getMoeaPostDate()
				,obj.getMoeaPostNo()
				,obj.getReserve365()
				,obj.getModIdNo()
				,obj.getModDate()
				,obj.getModTime()
				,obj.getItemCode()};
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), parameters); 
		return findByItemCode(obj.getItemCode());
	}
	
	public BusiItem insert( BusiItem obj ) {
		if ( obj == null )	return null ;
		SQLJob sqljob = new SQLJob("insert into busi_item( ");
		sqljob.appendSQL("item_code");
		sqljob.appendSQL(", business_item");
		sqljob.appendSQL(", master_code ");
		sqljob.appendSQL(", moea_post_date");
		sqljob.appendSQL(", moea_post_no");
		sqljob.appendSQL(", reserve_365");
		sqljob.appendSQL(", mod_id_no");
		sqljob.appendSQL(", mod_date");
		sqljob.appendSQL(", mod_time");
		sqljob.appendSQL(") values (?,?,?,?,?,?,?,?,?) ");
		sqljob.addParameter(obj.getItemCode());
		sqljob.addParameter(obj.getBusinessItem());
		sqljob.addParameter(obj.getMasterCode());
		sqljob.addParameter(obj.getMoeaPostDate());
		sqljob.addParameter(obj.getMoeaPostNo());
		sqljob.addParameter(obj.getReserve365());
		sqljob.addParameter(obj.getModIdNo());
		sqljob.addParameter(obj.getModDate());
		sqljob.addParameter(obj.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		return findByItemCode(obj.getItemCode());
	}
	
	public void delete( BusiItem obj ) {
		if ( obj == null || "".equals(Common.get(obj.getId())))	return;
		SQLJob sqljob = new SQLJob("delete from busi_item where id = ? ");
		sqljob.addParameter(obj.getId());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	public BusiItem mapRow(ResultSet rs, int idx) throws SQLException {
		BusiItem obj = null;
		if(null!=rs) {
			obj = new BusiItem();
			obj.setId(rs.getString("ID"));
			obj.setItemCode(rs.getString("ITEM_CODE"));
			obj.setBusinessItem(rs.getString("BUSINESS_ITEM"));
			obj.setMasterCode(rs.getString("MASTER_CODE"));
			obj.setMoeaPostDate(rs.getString("MOEA_POST_DATE"));
			obj.setMoeaPostNo(rs.getString("MOEA_POST_NO"));
			obj.setReserve365(rs.getString("RESERVE_365"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
			
		}
		return obj;
	}

	public List<BusiItem> findItemCodeByLength(int length) {
		SQLJob sqljob = new SQLJob("SELECT * FROM BUSI_ITEM WHERE (LENGTH(ITEM_CODE) = ?)");
		sqljob.addParameter(length);
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public List<BusiItem> findItemByLengthAndKeyword(int length, String keyword) {
		SQLJob sqljob = new SQLJob("SELECT * FROM BUSI_ITEM WHERE ((LENGTH(ITEM_CODE) = ?) AND (BUSINESS_ITEM LIKE ?))");
		sqljob.addParameter(length);
		sqljob.addLikeParameter(keyword);
		
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

}