-- Create table
create table EICM.INDEX_LOG
(
  ID NUMBER not null,
  WS_ID VARCHAR2(20),  
  PARAM1 VARCHAR2(20),
  PARAM2 VARCHAR2(20),
  PARAM3 VARCHAR2(20),
  EXECUTE_DATE VARCHAR2(20),
  CREATE_DATE VARCHAR2(20),
  CREATE_USER VARCHAR2(20),
  START_TIME VARCHAR2(20),
  FINISH_TIME VARCHAR2(20),
  STATUS CHAR(1),
  REMARK VARCHAR2(2000)
);

-- Add comments to the table 
comment on table EICM.INDEX_LOG is '索引紀錄檔';
-- Add comments to the columns 
comment on column EICM.INDEX_LOG.WS_ID is '執行WS的程式代碼';
comment on column EICM.INDEX_LOG.PARAM1 is '參數1';
comment on column EICM.INDEX_LOG.PARAM2 is '參數2';
comment on column EICM.INDEX_LOG.PARAM3 is '參數3';
comment on column EICM.INDEX_LOG.EXECUTE_DATE is '預計執行日期(預設空值表示立即執行;有設定日期,要比對日期小於等於sysDate才執行)';
comment on column EICM.INDEX_LOG.CREATE_DATE is '建立日期';
comment on column EICM.INDEX_LOG.CREATE_USER is '建立者';
comment on column EICM.INDEX_LOG.START_TIME is '開始時間';
comment on column EICM.INDEX_LOG.FINISH_TIME is '完成時間';
comment on column EICM.INDEX_LOG.STATUS is '狀態(0:待執行/1:執行中/2:執行成功/3:執行失敗)';
comment on column EICM.INDEX_LOG.REMARK is '備註';      

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.INDEX_LOG
  add constraint PK_INDEX_LOG_ID primary key (ID)
  using index ;

-- Create sequence 
create sequence EICM.SEQ_INDEX_LOG_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_INDEX_LOG
Before Insert ON EICM.INDEX_LOG Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_INDEX_LOG_ID.NextVal into :nu.id From Dual;
End;
   
-- SYNONYM 
create or replace synonym EICM4AP.INDEX_LOG for EICM.INDEX_LOG;
create or replace synonym EICM4CMPY.INDEX_LOG for EICM.INDEX_LOG;
create or replace synonym EICM4PREFIX.INDEX_LOG for EICM.INDEX_LOG;
create or replace synonym ICMS.INDEX_LOG for EICM.INDEX_LOG;

-- Grant/Revoke object privileges 
grant all on EICM.INDEX_LOG to EICM4AP;
grant all on EICM.INDEX_LOG to EICM4PREFIX;
grant all on EICM.INDEX_LOG to ICMS;

create index EICM.INDEX_LOG_IDX1 on EICM.INDEX_LOG (WS_ID, STATUS)
  tablespace EICM_IDX
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );