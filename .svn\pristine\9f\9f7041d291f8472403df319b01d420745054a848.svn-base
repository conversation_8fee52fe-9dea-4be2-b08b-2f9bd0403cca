<!DOCTYPE html>
<!--
程式目的：使用者資料維護
程式代號：PRE9001
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9001" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE9001)obj.queryOne();
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		//alertStr += checkQuery();
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.idNo,"帳號");
		alertStr += checkEmpty(form1.staffName, "姓名");
		//不檢查密碼規則
		//alertStr += checkMustMixAlphaInt(form1.idPassword, "密碼", 6);
		alertStr += checkEmpty(form1.groupId, "角色權限");
		alertStr += checkEmpty(form1.staffUnit, "單位");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}
function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->
<div id="queryContainer" style="width:700px;height:300px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
		<td nowrap class="queryTDLable">帳號：</td>
		<td nowrap class="queryTDInput">
			<input class="field_Q" type="text" name="q_idNo" size="20" maxlength="20" value="<%=obj.getQ_idNo()%>">
		</td>
	</tr>
	<tr>
		<td nowrap class="queryTDLable">姓名：</td>
		<td nowrap class="queryTDInput">
			<input class="field_Q cmex" type="text" name="q_staffName" size="20" maxlength="50" value="<%=obj.getQ_staffName()%>">
		</td>
	</tr>
	<!-- 
	<tr>
		<td nowrap class="queryTDLable">單位：</td>
		<td nowrap class="queryTDInput">			
			<select class="field_Q" name="q_staffUnit">
				<%=View.getTextOption("六科;六科;離職;離職;", obj.getQ_staffUnit(), 1) %>
			</select>
		</td>
	</tr>
	 -->
	<tr>
		<td nowrap class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定" >
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9001'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" style="text-align:left">
<input type="hidden" name="id" value="<%=obj.getId()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="Y" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="Y" />
	<jsp:param name="btnDelete" value="Y" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="Y" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td nowrap class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td nowrap class="td_form" width="15%"><font color="red">*</font>帳號：</td>
		<td nowrap class="td_form_white" width="35%">
			<input class="field_P" type="text" name="idNo" size="20" maxlength="20" value="<%=obj.getIdNo()%>">
		</td>
		<td nowrap class="td_form"><font color="red">*</font>姓名：</td>
	  	<td nowrap class="td_form_white">
	    	<input class="field" type="text" name="staffName" size="20" maxlength="50" value="<%=obj.getStaffName()%>">		
	    </td>
	</tr>
	<tr>
	  	<td nowrap class="td_form"><font color="red">*</font>密碼：</td>
	  	<td nowrap class="td_form_white" colspan="3">
	  		<input class="field" type="password" autocomplete="off" name="idPassword" size="20" maxlength="50" value="<%=obj.getIdPassword()%>">
	  	</td>
	</tr>
	<tr>
	  	<td nowrap class="td_form"><font color="red">*</font>角色權限：</td>
	  	<td nowrap class="td_form_white" colspan="3">
	    	<select class="field" name="groupId">
				<%=View.getOptionSystemCode("02", obj.getGroupId(), false, 1, "") %>
			</select>
		</td>
	</tr>
	<tr>
	  	<td nowrap class="td_form">職稱：</td>
	  	<td nowrap class="td_form_white">
	  		<input class="field" type="text" name="staffTitle" size="20" maxlength="20" value="<%=obj.getStaffTitle()%>">
	  	</td>
		<td nowrap class="td_form" width="15%">電話分機號：</td>
		<td nowrap class="td_form_white" width="35%">
			<input class="field" type="text" name="brPhoneNo" size="4" maxlength="4" value="<%=obj.getBrPhoneNo()%>">
		</td>
	</tr>
	<tr>
	  	<td nowrap class="td_form"><font color="red">*</font>單位：</td>
	  	<td nowrap class="td_form_white" colspan="3">
	    	<select class="field" name="staffUnit">
				<%=View.getTextOption("六科;六科;預查科;預查科;離職;離職;", obj.getStaffUnit(), 1) %>
			</select>
		</td>
	</tr>
	<tr>
	  	<td nowrap class="td_form">異動資訊：</td>
	  	<td nowrap class="td_form_white" colspan="3"> [
	    	<input class="field_RO" type="text" name="editID" size="10" value="<%=obj.getEditID()%>">
	    	/
	    	<input class="field_RO" type="text" name="editDate" size="7" value="<%=obj.getEditDate()%>">
	    	] 
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" ><a class="text_link_w" >序號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">帳號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">姓名</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">角色權限</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">職稱</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">單位</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true,false,false,false,false};
	boolean displayArray[] = {true,true,true,true,true};
	String[] alignArray = {"center","center","left","left","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>