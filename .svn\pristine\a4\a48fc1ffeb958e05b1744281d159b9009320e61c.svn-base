package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.util.lang.CommonStringUtils;

public class DeclaratoryStatutesDao extends BaseDaoJdbc implements RowMapper<DeclaratoryStatutes> {
	
	private static final String SQL_find = "SELECT * FROM DECLARATORY_STATUTES";
	
	public java.util.List<DeclaratoryStatutes> find(DeclaratoryStatutes bo, String endTime) {
		SQLJob sqljob = new SQLJob(SQL_find);
		if(!"".equals(Common.get(bo.getRcvNo()))){
			sqljob.appendSQLCondition(" RCV_NO LIKE ? ");
			sqljob.addLikeParameter(bo.getRcvNo());
		}
		if(!"".equals(Common.get(bo.getRcvTime()))){
			sqljob.appendSQLCondition(" RCV_TIME >= ? ");
			sqljob.addParameter(bo.getRcvTime());
		}
		if(!"".equals(Common.get(endTime))){
			sqljob.appendSQLCondition(" RCV_TIME <= ? ");
			sqljob.addParameter(endTime);
		}
		//主旨與內容2個地方都要查
		if(!"".equals(Common.get(bo.getKeynote())) || !"".equals(Common.get(bo.getInstruction()))){
			sqljob.appendSQLCondition("( ");
			if(!"".equals(Common.get(bo.getKeynote()))){
				sqljob.appendSQL("( KEYNOTE LIKE ? OR INSTRUCTION LIKE ? )");
				sqljob.addLikeParameter(bo.getKeynote());
				sqljob.addLikeParameter(bo.getKeynote());					
			}
			if(!"".equals(Common.get(bo.getInstruction()))){
				if(!"".equals(Common.get(bo.getKeynote())))
					sqljob.appendSQL(" OR ");
				sqljob.appendSQL("( KEYNOTE LIKE ? OR INSTRUCTION LIKE ? )");
				sqljob.addLikeParameter(bo.getInstruction());
				sqljob.addLikeParameter(bo.getInstruction());	
			}
			sqljob.appendSQL(") ");
		}
		sqljob.appendSQL(" ORDER BY RCV_TIME DESC, RCV_NO ASC");
		return (java.util.List<DeclaratoryStatutes>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	// 依傳入的收文文號至資料庫中尋找相應的目標
	public DeclaratoryStatutes findByRcvNo(String rcvNo) {
		if("".equals(Common.get(rcvNo)))	return null;
		SQLJob sqljob = new SQLJob(SQL_find);
		sqljob.appendSQLCondition(" TRIM(RCV_NO) = ? ");
		sqljob.addParameter(rcvNo);
		List<DeclaratoryStatutes> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0) ;
	}
	
	public DeclaratoryStatutes set( DeclaratoryStatutes bo ) {
		if("".equals(Common.get(bo.getId())))	return null;
		//update
		SQLJob sqljob = new SQLJob("UPDATE declaratory_statutes SET ");
		sqljob.appendSQL(" RCV_NO = ?");
		sqljob.appendSQL(" ,RCV_TIME = ?");
		sqljob.appendSQL(" ,KEYNOTE = ?");
		sqljob.appendSQL(" ,INSTRUCTION = ?");
		sqljob.appendSQL(" ,BUSI_ITEMS = ?");
		sqljob.appendSQL(" ,IS_PUBLISH = ?");
		sqljob.appendSQL(" ,UPDATE_DATE = ?");
		sqljob.appendSQL(" ,UPDATE_TIME = ?");
		sqljob.appendSQL(" ,UPDATE_USER = ?");
		sqljob.appendSQL(" ,RULE_CODE = ?");
		sqljob.appendSQL(" ,RULE_NO = ?");
		sqljob.appendSQL(" ,RCV_TYPE_1 = ?");
		sqljob.appendSQL(" ,RCV_TYPE_2 = ?");
		sqljob.appendSQL(" WHERE TRIM(RCV_NO) = ?");
		sqljob.addParameter(bo.getRcvNo());
		sqljob.addParameter(bo.getRcvTime());
		sqljob.addParameter(bo.getKeynote());
		sqljob.addParameter(bo.getInstruction());
		sqljob.addParameter(bo.getBusiItems());
		sqljob.addParameter(bo.getIsPublish());
		sqljob.addParameter(bo.getUpdateDate());
		sqljob.addParameter(bo.getUpdateTime());
		sqljob.addParameter(bo.getUpdateUser());
		sqljob.addParameter(bo.getRuleCode());
		sqljob.addParameter(bo.getRuleNo());
		sqljob.addParameter(bo.getReceiveUnit());
		sqljob.addParameter(bo.getCcUnit());
		sqljob.addParameter(bo.getId());
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	    return findByRcvNo(bo.getRcvNo());
	} // set
	
	public int updateWhenPublish(DeclaratoryStatutes bo) {
		if ( bo == null )
			return 0;
		StringBuffer sql = new StringBuffer();
		sql.append("update declaratory_statutes set");
		sql.append(" Is_Publish = ?");
		sql.append(" ,update_date = ?");
		sql.append(" ,update_time = ?");
		sql.append(" ,update_user = ?");
		sql.append(" where rcv_no = ?");
		Object[] parameters = {
				 bo.getIsPublish()
				 ,bo.getUpdateDate()
				 ,bo.getUpdateTime()
				 ,bo.getUpdateUser()
				 ,bo.getRcvNo()
				 }; 
		getJdbcTemplate().update(sql.toString(), parameters);
		return 1;
	} // publishUpdate
	
	public DeclaratoryStatutes insert(DeclaratoryStatutes bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getRcvNo())) return null;
		//check exist
		DeclaratoryStatutes t = findByRcvNo(bo.getRcvNo());
		if(null!=t) return t;
		//insert
		StringBuffer sql = new StringBuffer();
		sql.append(" INSERT INTO DECLARATORY_STATUTES (");
		sql.append(" RCV_NO");
		sql.append(",RCV_TIME");
		sql.append(",KEYNOTE");
		sql.append(",INSTRUCTION");
		sql.append(",BUSI_ITEMS");
		sql.append(",IS_PUBLISH");
		sql.append(",INSERT_DATE");
		sql.append(",INSERT_TIME");
		sql.append(",INSERT_USER");
		sql.append(",UPDATE_DATE");
		sql.append(",UPDATE_TIME");
		sql.append(",UPDATE_USER");
		sql.append(",RULE_CODE");
		sql.append(",RULE_NO");
		sql.append(",RCV_TYPE_1");
		sql.append(",RCV_TYPE_2");
		sql.append(") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		Object[] parameters = {
				bo.getRcvNo()
				,bo.getRcvTime()
				,bo.getKeynote()
				,bo.getInstruction()
				,bo.getBusiItems()
				,bo.getIsPublish()
				,bo.getInsertDate()
				,bo.getInsertTime()
				,bo.getInsertUser()
				,bo.getUpdateDate()
				,bo.getUpdateTime()
				,bo.getUpdateUser()
				,bo.getRuleCode()
				,bo.getRuleNo()
				,bo.getReceiveUnit()
				,bo.getCcUnit()
				};
		getJdbcTemplate().update(sql.toString(), parameters);
		return findByRcvNo(bo.getRcvNo());
	}
	
	public DeclaratoryStatutes update(DeclaratoryStatutes bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getRcvNo())) return null;
		//check exist
		DeclaratoryStatutes t = findByRcvNo(bo.getRcvNo());
		if(null==t) return null;
		else {
			//update
			StringBuffer sql = new StringBuffer();
			sql.append(" UPDATE DECLARATORY_STATUTES");
			sql.append(" SET");
			sql.append("   RCV_TIME = ?");
			sql.append("  ,KEYNOTE = ?");
			sql.append("  ,INSTRUCTION = ?");
			sql.append("  ,BUSI_ITEMS = ?");
			sql.append("  ,IS_PUBLISH = ?");
			sql.append("  ,INSERT_DATE = ?");
			sql.append("  ,INSERT_TIME = ?");
			sql.append("  ,INSERT_USER = ?");
			sql.append("  ,UPDATE_DATE = ?");
			sql.append("  ,UPDATE_TIME = ?");
			sql.append("  ,UPDATE_USER = ?");
			sql.append("  ,RULE_CODE = ?");
			sql.append("  ,RULE_NO = ?");
			sql.append("  ,RCV_TYPE_1 = ?");
			sql.append("  ,RCV_TYPE_2 = ?");
			sql.append(" WHERE RCV_NO = ?");
			Object[] parameters = {
					bo.getRcvTime()
					,bo.getKeynote()
					,bo.getInstruction()
					,bo.getBusiItems()
					,bo.getIsPublish()
					,bo.getInsertDate()
					,bo.getInsertTime()
					,bo.getInsertUser()
					,bo.getUpdateDate()
					,bo.getUpdateTime()
					,bo.getUpdateUser()
					,bo.getRuleCode()
					,bo.getRuleNo()
					,bo.getReceiveUnit()
					,bo.getCcUnit()
					,bo.getRcvNo()
					};
			getJdbcTemplate().update(sql.toString(), parameters);
			return findByRcvNo(bo.getRcvNo());
		}
	}
	
	public void delete(DeclaratoryStatutes bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getRcvNo()) ) {
			//delete
			StringBuffer sql = new StringBuffer();
			sql.append(" DELETE FROM DECLARATORY_STATUTES");
			sql.append(" WHERE RCV_NO = ?");
			Object[] parameters = {
					bo.getRcvNo()
					};
			getJdbcTemplate().update(sql.toString(), parameters);
		}
	}

	public DeclaratoryStatutes mapRow(ResultSet rs, int idx) throws SQLException {
		DeclaratoryStatutes obj = null;
		if(null!=rs) {
			obj = new DeclaratoryStatutes();
			obj.setRcvNo(rs.getString("RCV_NO"));
			obj.setRcvTime(rs.getString("RCV_TIME"));
			obj.setKeynote(rs.getString("KEYNOTE"));
			obj.setInstruction(rs.getString("INSTRUCTION"));
			obj.setBusiItems(rs.getString("BUSI_ITEMS"));
			obj.setIsPublish(rs.getString("IS_PUBLISH"));
			obj.setInsertDate(rs.getString("INSERT_DATE"));
			obj.setInsertTime(rs.getString("INSERT_TIME"));
			obj.setInsertUser(rs.getString("INSERT_USER"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setRuleCode(rs.getString("RULE_CODE"));
			obj.setRuleNo(rs.getString("RULE_NO"));
			obj.setReceiveUnit(rs.getString("RCV_TYPE_1"));
			obj.setCcUnit(rs.getString("RCV_TYPE_2"));
		}
		return obj;
	}
}