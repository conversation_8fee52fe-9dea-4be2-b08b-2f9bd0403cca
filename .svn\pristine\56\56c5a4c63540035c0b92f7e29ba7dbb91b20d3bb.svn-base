package com.kangdainfo.tcfi.model.eicm.bo;

import java.util.List;

import com.kangdainfo.persistence.BaseModel;

public class CmpyQueryVo extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 公司統編 */
	private String banNo;
	/** 公司名稱 */
	private String companyName;
	/** 預查編號 */
	private String prefixNo;
	/** 特取名稱 */
	private String specialName;
	/** 組織型態 */
	private String orgType;
	/** 組織型態名稱 */
	private String orgName;
	/** 負責人姓名 */
	private String respName;
	/** 公司狀態 */
	private String companyStatus;
	/** 申登機關 */
	private String regUnit;
	/** 申登機關名稱 */
	private String regUnitName;
	/** 公司電話 */
	private String companyTel;
	/** 公司地址 */
	private String companyAddr;
	/** 資本總額 */
	private String capitalAmount;
	/** 實收資本額 */
	private String realAmt;
	/** 管制項目 */
	private String controlItem;
	/** 分公司家數 */
	private String totBranch;
	/** 僑外資事業 */
	private String investmentCode;
	/** 持有者種類 */
	private String ownerType;
	/** 核准設立日 */
	private String setupDate;
	/** 核准設立字號 */
	private String approveWord;
	/** 核准設立文號 */
	private String approveNo;
	/** 核准變更日 */
	private String changeDate;
	/** 核准變更字號 */
	private String changeWord;
	/** 核准變更文號 */
	private String changeNo;
	/** 核准停業日期 */
	private String suspendDate;
	/** 核准停業字號 */
	private String suspendWord;
	/** 核准停業文號 */
	private String suspendNo;
	/** 核准停業機關 */
	private String suspendUnit;
	/** 停業/延展開始日 */
	private String suspendBegDate;
	/** 停業/延展終結日 */
	private String suspendEndDate;
	/** 撤銷(破產/廢止/解散...)日期 */
	private String endDate;
	/** 陸資事業 */
	private String chinaCode;
	/** 免繳註記 */
	private String noNeedPay;
	/** 是否為閉鎖性 */
	private String closed;
	/** 所營事業 */
	private List<Cedb2002> cedb2002s;
	/** 董監事 */
	private List<CmpyShareholderVo> shareholders;

	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String s) {this.specialName = s;}
	public String getOrgType() {return orgType;}
	public void setOrgType(String s) {this.orgType = s;}
	public String getOrgName() {return orgName;}
	public void setOrgName(String s) {this.orgName = s;}
	public String getRespName() {return respName;}
	public void setRespName(String s) {this.respName = s;}
	public String getCompanyStatus() {return companyStatus;}
	public void setCompanyStatus(String s) {this.companyStatus = s;}
	public String getRegUnit() {return regUnit;}
	public void setRegUnit(String s) {this.regUnit = s;}
	public String getRegUnitName() {return regUnitName;}
	public void setRegUnitName(String s) {this.regUnitName = s;}
	public String getCompanyTel() {return companyTel;}
	public void setCompanyTel(String s) {this.companyTel = s;}
	public String getCompanyAddr() {return companyAddr;}
	public void setCompanyAddr(String s) {this.companyAddr = s;}
	public String getCapitalAmount() {return capitalAmount;}
	public void setCapitalAmount(String s) {this.capitalAmount = s;}
	public String getControlItem() {return controlItem;}
	public void setControlItem(String s) {this.controlItem = s;}
	public String getTotBranch() {return totBranch;}
	public void setTotBranch(String s) {this.totBranch = s;}
	public String getInvestmentCode() {return investmentCode;}
	public void setInvestmentCode(String s) {this.investmentCode = s;}
	public String getOwnerType() {return ownerType;}
	public void setOwnerType(String s) {this.ownerType = s;}
	public String getSetupDate() {return setupDate;}
	public void setSetupDate(String s) {this.setupDate = s;}
	public String getApproveWord() {return approveWord;}
	public void setApproveWord(String s) {this.approveWord = s;}
	public String getApproveNo() {return approveNo;}
	public void setApproveNo(String s) {this.approveNo = s;}
	public String getChangeDate() {return changeDate;}
	public void setChangeDate(String s) {this.changeDate = s;}
	public String getChangeWord() {return changeWord;}
	public void setChangeWord(String s) {this.changeWord = s;}
	public String getChangeNo() {return changeNo;}
	public void setChangeNo(String s) {this.changeNo = s;}
	public String getSuspendDate() {return suspendDate;}
	public void setSuspendDate(String s) {this.suspendDate = s;}
	public String getSuspendWord() {return suspendWord;}
	public void setSuspendWord(String s) {this.suspendWord = s;}
	public String getSuspendNo() {return suspendNo;}
	public void setSuspendNo(String s) {this.suspendNo = s;}
	public String getSuspendUnit() {return suspendUnit;}
	public void setSuspendUnit(String s) {this.suspendUnit = s;}
	public String getSuspendBegDate() {return suspendBegDate;}
	public void setSuspendBegDate(String s) {this.suspendBegDate = s;}
	public String getSuspendEndDate() {return suspendEndDate;}
	public void setSuspendEndDate(String s) {this.suspendEndDate = s;}
	public String getEndDate() {return endDate;}
	public void setEndDate(String s) {this.endDate = s;}
	public String getNoNeedPay() {return noNeedPay;}
	public void setNoNeedPay(String s) {this.noNeedPay = s;}
	public String getChinaCode() {return chinaCode;}
	public void setChinaCode(String s) {this.chinaCode = s;}
	public String getClosed() {return closed;}
	public void setClosed(String s) {this.closed = s;}
	public List<Cedb2002> getCedb2002s() {return cedb2002s;}
	public void setCedb2002s(List<Cedb2002> l) {this.cedb2002s = l;}
	public List<CmpyShareholderVo> getShareholders() {return shareholders;}
	public void setShareholders(List<CmpyShareholderVo> l) {this.shareholders = l;}
	public String getRealAmt() {return realAmt;}
	public void setRealAmt(String s) {this.realAmt = s;}

}