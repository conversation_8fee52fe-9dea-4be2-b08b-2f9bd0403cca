<%
/**
程式目的：預查線上申辦電子核定書
程式代號：pre4011
程式日期：103.05.15
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4013" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4013">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = null;
	if ("prefix".equals(obj.getQ_type()) && !"".equals(obj.getQ_prefixNoEnd())
			&& !obj.getQ_prefixNo().equals(obj.getQ_prefixNoEnd())) {
		report = obj.makeMutiReportByPrefixNo();
	} else {
		report = obj.doPrintPdf();
	}
	
	if(null!=report) {
		obj.outputFile(response, report, "PRE4013.pdf", true);
		out.clear();
		out = pageContext.pushBody();
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(form1.q_type[0].checked){
		alertStr += checkEmpty(form1.q_telixNo,"電子流水號");
		form1.q_prefixNo.style.backgroundColor="";
		form1.q_prefixNoEnd.style.backgroundColor="";
	}else if(form1.q_type[1].checked){
		alertStr += checkEmpty(form1.q_prefixNo,"預查編號");
		form1.q_telixNo.style.backgroundColor="";
	}else{
		alertStr += "請先點選查詢種類。";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

function changeRadio(type) {
    if(type == 'telix'){
    	form1.q_type[0].checked = true;
        form1.q_prefixNo.value="";
        form1.q_prefixNoEnd.value="";
    }else if(type == 'prefix'){
    	form1.q_type[1].checked = true;
       	form1.q_telixNo.value="";
    }
}

$(document).ready(function() {

	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				if(checkField()){
					var prop="";
					var width = window.screen.width;
					var height = window.screen.height;
					var top = (window.screen.height - height);
					var left = (window.screen.width - width);
					prop=prop+"width=1024px,height=768px,";
					prop=prop+"top="+top+"px,";
					prop=prop+"left="+left+"px,";
					prop=prop+"scrollbars=0,resizable=1";
					$('#state').val("preview") ;
					var target = 'PRE4013_'+randomUUID().replace(/\-/g,"");
					window.open("",target,prop);
					form1.target = target;
					form1.submit();
					// form1.target = '';	
				}		
				break;				
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
	if($("input[name=q_telixNo]").val()) {
		$("input[name=q_telixNo]").focus();
	} else if($("input[name=q_prefixNo]").val()) {
		$("input[name=q_prefixNo]").focus();
	}

	if(commonUtils.getURLParameter("validate") == 'false') {
		var prop="";
		var width = window.screen.width;
		var height = window.screen.height;
		var top = (window.screen.height - height);
		var left = (window.screen.width - width);
		prop=prop+"width=1024px,height=768px,";
		prop=prop+"top="+top+"px,";
		prop=prop+"left="+left+"px,";
		prop=prop+"scrollbars=0,resizable=1";
		$('#validate').val('false');
		$('#state').val("preview") ;
		window.open("",'popReport',prop);
		form1.target = 'popReport';
		form1.submit();
		window.close();
	}
	
	//$("#doPrintPdf").click();
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var prefixNo = form1.q_prefixNo.value;
		var prefixNoEnd = form1.q_prefixNoEnd.value;
		var telixNo = form1.q_telixNo.value;
		var type = document.getElementsByName("q_type");
		var x="";
		
		if ((prefixNoEnd - prefixNo) > 99) {
			x = "請勿超過100筆！";
		} else {
			if (type[0].checked) {
				x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4013.jsp?prefixNo='+prefixNo+'&prefixNoEnd='+prefixNoEnd+'&telixNo='+telixNo+'&type=telix');
			} else {
				x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4013.jsp?prefixNo='+prefixNo+'&prefixNoEnd='+prefixNoEnd+'&telixNo='+telixNo+'&type=prefix');
			}
		}
		
		if (x == 'ok') {
			whatButtonFireEvent("doPrintPdf");
		} else {
			document.getElementById("ERRMSG").innerHTML = x;
		}
	} 	
}
</script>
</head>
<body topmargin="5">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4013'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="15%">
				<input type="radio" name="q_type" value="telix" onclick="changeRadio(this.value);">電子流水號：
			</td>
			<td class="td_form_white" width="85%">
				<input class="field_Q" type="text" name="q_telixNo" size="20" maxlength="16" value="<%=obj.getQ_telixNo()%>" onfocus="changeRadio('telix');">
			</td>
		</tr>
		<tr>     
		    <td class="td_form" width="15%">
		        <input type="radio" name="q_type" value="prefix" onclick="changeRadio(this.value);">預查編號：
		    </td>
	        <td class="td_form_white" width="85%"> 
				<input class="field_Q" type="text" name="q_prefixNo" size="20" maxlength="10" value="<%=obj.getQ_prefixNo()%>" onfocus="changeRadio('prefix');">
				~
				<input class="field_Q" type="text" name="q_prefixNoEnd" size="20" maxlength="10" value="<%=obj.getQ_prefixNoEnd()%>" onfocus="changeRadio('prefix');">
	        	&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="核定書列印" onClick="doSomeCheck()" >
	        </td>
	    </tr>	
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td>
</tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<input type="hidden" id="validate" name="validate" value="">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>