package com.kangdainfo.tcfi.loader;

import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;
import com.kangdainfo.tcfi.model.eicm.dao.SystemNewsDao;

/**
 * 系統公告
 *
 */
public class SystemNewsLoader extends BaseLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_NEWS";
	//singleton
	private static SystemNewsLoader instance;
	public SystemNewsLoader() {
		if (SystemNewsLoader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemNewsLoader.instance);
		}
		SystemNewsLoader.instance = this;
	}
	public static SystemNewsLoader getInstance() {return instance;}

	/**
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<SystemNews> loadSystemNews() {
		if(getServletContext().getAttribute(CACHE_NAME) == null)
			reload();
		return (List<SystemNews>) getServletContext().getAttribute(CACHE_NAME);
	}

	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	/** 重新載入 */
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(CACHE_NAME, systemNewsDao.findNews());
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}
	
	private SystemNewsDao systemNewsDao;
	public SystemNewsDao getSystemNewsDao() {return systemNewsDao;}
	public void setSystemNewsDao(SystemNewsDao dao) {this.systemNewsDao = dao;}

}