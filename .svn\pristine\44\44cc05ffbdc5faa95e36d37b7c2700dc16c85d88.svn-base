package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;

public class Cedbc058Dao extends BaseDaoJdbc implements RowMapper<Cedbc058> {

	private static String sql_findAll = "SELECT * FROM CEDBC058 WHERE CAN_USE = 'Y' ";
	private static String SQL_defaultOrder = "ORDER BY UPDATE_DATE DESC, UPDATE_TIME DESC";
	
	public Cedbc058 queryById(Integer id){
		if(id == null)	return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		sqljob.appendSQLCondition(" ID = ? ");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Cedbc058> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null: list.get(0);
	}
	
	public java.util.List<Cedbc058> queryAll(){
		SQLJob sqljob = new SQLJob(sql_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (java.util.List<Cedbc058>)getJdbcTemplate().query(sqljob.getSQL(), this);
	}
	
	public java.util.List<Cedbc058> queryBySameName(String sameName) {
		if(sameName == null || "".equals(sameName)) return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		sqljob.appendSQLCondition(" (SAME_NAME = ? OR SAME_NAME_1 = ?) ");
		sqljob.appendSQL(SQL_defaultOrder);
		sqljob.addParameter(sameName);
		sqljob.addParameter(sameName);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (java.util.List<Cedbc058>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Cedbc058 queryBySameName(String sameName, String sameName1) {
		if(sameName == null || "".equals(sameName) || sameName1 == null || "".equals(sameName1)) return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		sqljob.appendSQLCondition(" (SAME_NAME = ? OR SAME_NAME_1 = ? OR SAME_NAME = ? OR SAME_NAME_1 = ?) ");
		sqljob.appendSQLCondition(" ROWNUM = 1 ");
		sqljob.addParameter(sameName);
		sqljob.addParameter(sameName);
		sqljob.addParameter(sameName1);
		sqljob.addParameter(sameName1);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Cedbc058> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null: list.get(0);
	}
	
	public Cedbc058 queryBySameNameForDelete(String sameName) {
		if(sameName == null || "".equals(sameName)) return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		sqljob.appendSQLCondition(" SAME_NAME_1 = ? ");
		sqljob.appendSQLCondition(" ROWNUM = 1 ");
		sqljob.addParameter(sameName);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Cedbc058> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null: list.get(0);
	}
	
	public java.util.List<Cedbc058> queryByStatus(String id, String... status) {
		if(status == null || status.length == 0) return null;	
		String sql_condition = "";
		
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDBC058 ");
		for(int i = 0; i < status.length; i++){
			if(i == 0)
				sql_condition += "?";
			else
				sql_condition += ",?";
			sqljob.addParameter(status[i]);
		}
		if(!"".equals(Common.get(sql_condition))){
			sqljob.appendSQLCondition(" STATUS IN ("+sql_condition+") ");
		}
		if(!"".equals(Common.get(id))){
			sqljob.appendSQLCondition(" ID = ? ");
			sqljob.addParameter(id);
		}
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (java.util.List<Cedbc058>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public java.util.List<Cedbc058> queryByCondition(String sameName1, String source) {
		//if("".equals(Common.get(sameName)) && "".equals(Common.get(sameName1)) && "".equals(Common.get(source)) ) return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		if(!"".equals(Common.get(sameName1))){
			sqljob.appendSQLCondition(" BASE_NAME IN (SELECT BASE_NAME FROM CEDBC058 WHERE (SAME_NAME_1 = ? OR SAME_NAME = ?) AND CAN_USE = 'Y' )");
			sqljob.addParameter(sameName1);
			sqljob.addParameter(sameName1);
		}
		if(!"".equals(Common.get(source))){
			sqljob.appendSQLCondition(" SOURCE LIKE ? ");
			sqljob.addLikeParameter(source);
		}
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public Cedbc058 queryByObj(Cedbc058 obj){
		if(obj == null)	return null;
		SQLJob sqljob = new SQLJob(sql_findAll);
		if(!"".equals(Common.get(obj.getSameName1()))){
			sqljob.appendSQLCondition(" SAME_NAME_1 = ? ");
			sqljob.addParameter(obj.getSameName1());
		}
		if(!"".equals(Common.get(obj.getSameName()))){
			sqljob.appendSQLCondition(" SAME_NAME = ? ");
			sqljob.addParameter(obj.getSameName());
		}
		if(!"".equals(Common.get(obj.getBaseName()))){
			sqljob.appendSQLCondition(" BASE_NAME = ? ");
			sqljob.addParameter(obj.getBaseName());
		}
		if(!"".equals(Common.get(obj.getBeUsed()))){
			sqljob.appendSQLCondition(" BE_USED = ? ");
			sqljob.addParameter(obj.getBeUsed());
		}
		if(!"".equals(Common.get(obj.getStatus()))){
			sqljob.appendSQLCondition(" STATUS = ? ");
			sqljob.addParameter(obj.getStatus());
		}
		if(!"".equals(Common.get(obj.getSource()))){
			sqljob.appendSQLCondition(" SOURCE = ? ");
			sqljob.addParameter(obj.getSource());
		}
		sqljob.appendSQLCondition(" ROWNUM = 1 ");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<Cedbc058> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null: list.get(0);
	}
	
	public Cedbc058 update(Cedbc058 obj) {
		if(obj == null || "".equals(Common.get(obj.getId()))) return null;
		SQLJob sqljob = new SQLJob("UPDATE CEDBC058 SET ");
		sqljob.appendSQL(" SAME_NAME_1 = ?");
		sqljob.appendSQL(" ,SAME_NAME = ?");
		sqljob.appendSQL(" ,BASE_NAME = ?");
		sqljob.appendSQL(" ,CAN_USE = ?");
		sqljob.appendSQL(" ,BE_USED = ?");
		sqljob.appendSQL(" ,STATUS = ?");
		sqljob.appendSQL(" ,SOURCE = ?");
		sqljob.appendSQL(" ,ENABLED = ?");
		sqljob.appendSQL(" ,UPDATE_USER = ?");
		sqljob.appendSQL(" ,UPDATE_DATE = ?");
		sqljob.appendSQL(" ,UPDATE_TIME = ?");
		sqljob.appendSQL(" WHERE ID = ?");
		sqljob.addParameter(obj.getSameName1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSameName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBaseName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCanUse());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBeUsed());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSource());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getEnabled());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getId());
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		return queryById(obj.getId());
	}
	
	public Cedbc058 insert(Cedbc058 obj) {
		if(obj == null) return null;
		SQLJob sqljob = new SQLJob("INSERT INTO CEDBC058(");
		sqljob.appendSQL(" SAME_NAME_1,");
		sqljob.appendSQL(" SAME_NAME,");
		sqljob.appendSQL(" BASE_NAME,");
		sqljob.appendSQL(" CAN_USE,");
		sqljob.appendSQL(" BE_USED,");
		sqljob.appendSQL(" STATUS,");
		sqljob.appendSQL(" SOURCE,");
		sqljob.appendSQL(" ENABLED,");
		sqljob.appendSQL(" CREATE_USER,");
		sqljob.appendSQL(" CREATE_DATE,");
		sqljob.appendSQL(" CREATE_TIME,");
		sqljob.appendSQL(" UPDATE_USER,");
		sqljob.appendSQL(" UPDATE_DATE,");
		sqljob.appendSQL(" UPDATE_TIME )");
		sqljob.appendSQL(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
		sqljob.addParameter(obj.getSameName1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSameName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBaseName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCanUse());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getBeUsed());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSource());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getEnabled());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCreateUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCreateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCreateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getUpdateTime());	
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		return queryByObj(obj);
	}
	
	public void delete(Integer id){
		if(id == null || "".equals(id))	return;
		SQLJob sqljob = new SQLJob("DELETE CEDBC058 WHERE ID = ?");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	public void updateAllCanUseToY() {
		SQLJob sqljob = new SQLJob("UPDATE CEDBC058 SET ENABLED = 'Y' WHERE CAN_USE = 'Y' AND ENABLED = 'N'");
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL());
	}
	
	@Override
	public Cedbc058 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc058 obj = null;
		if(null!=rs) {
			obj = new Cedbc058();
			obj.setId(rs.getInt("ID"));
			obj.setSameName1(rs.getString("SAME_NAME_1"));
			obj.setSameName(rs.getString("SAME_NAME"));
			obj.setBaseName(rs.getString("BASE_NAME"));
			obj.setCanUse(rs.getString("CAN_USE"));
			obj.setBeUsed(rs.getString("BE_USED"));
			obj.setStatus(rs.getString("STATUS"));
			obj.setSource(rs.getString("SOURCE"));
			obj.setEnabled(rs.getString("ENABLED"));
			obj.setCreateUser(rs.getString("CREATE_USER"));
			obj.setCreateDate(rs.getString("CREATE_DATE"));
			obj.setCreateTime(rs.getString("CREATE_TIME"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
		}
		return obj;
	}

}