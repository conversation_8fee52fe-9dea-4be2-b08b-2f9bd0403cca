<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="org.owasp.esapi.*" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.common.util.Common" %>
<%@ page import="com.kangdainfo.tcfi.model.eedb.bo.Eedb1000" %>
<%@ page import="com.kangdainfo.tcfi.model.eedb.bo.Eedb3300" %>
<%@ page import="com.kangdainfo.ServiceGetter" %>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
	Eedb1000 eedb1000 = ServiceGetter.getInstance().getPre5001Service().selectEedb1000ByTelixNo(telixNo);
	if (eedb1000!=null) {
		Gson gson = new GsonBuilder().create();
		if (org.apache.commons.lang.StringUtils.isBlank(eedb1000.getBanNo())) { // 暫時以是否有統編來判斷是否為設立案, 沒統編視為設立案.
			if (eedb1000.getRoleType().equals("2")){ // 20211213 法人設立案繳款人姓名設定為法人公司名稱
				Eedb3300 eedb3300 = ServiceGetter.getInstance().getPre5001Service().selectEedb3300ByTelixNo(telixNo);
				out.write(gson.toJson(eedb3300.getOrgCorpName()));
			} else {
				out.write(gson.toJson(eedb1000.getApplyName()));
			}
		} else {
			out.write(gson.toJson(eedb1000.getCompanyName()));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>