package com.kangdainfo.tcfi.view.pre;
/*
程式目的：批次分文查詢
程式代號：pre8003
撰寫日期：103.06.03
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;


public class PRE4021 extends SuperBean {

	private String q_yearMonthDateStart ;
	private String q_yearMonthDateEnd ;
	private String index ;
	private String staffName ;
	private String notAssign ;
	private String startPrefix ;
    private String endPrefix ; 
    private String lastClosedPrefix ;

	public String getQ_yearMonthDateStart() {return checkGet(q_yearMonthDateStart);}
	public void setQ_yearMonthDateStart(String s) {q_yearMonthDateStart = checkSet(s);}
	public String getQ_yearMonthDateEnd() {return checkGet(q_yearMonthDateEnd);}
	public void setQ_yearMonthDateEnd(String s) {q_yearMonthDateEnd = checkSet(s);}
	public String getIndex() {return checkGet(index);}
	public void setIndex(String s) {index = checkSet(s);}
	public String getStaffName() {return checkGet(staffName);}
	public void setStaffName(String s) {staffName = checkSet(s);}
	public String getNotAssign() {return checkGet(notAssign);}
	public void setNotAssign(String s) {notAssign = checkSet(s);}
	public String getStartPrefix() {return checkGet(startPrefix);}
	public void setStartPrefix(String s) {startPrefix = checkSet(s);}
	public String getEndPrefix() {return checkGet(endPrefix);}
	public void setEndPrefix(String s) {endPrefix = checkSet(s);}
	public String getLastClosedPrefix() {return checkGet(lastClosedPrefix);}
	public void setLastClosedPrefix(String s) {lastClosedPrefix = checkSet(s);}
	
	
	public SQLJob doAppendSqljob(String yearMonthDateStart, String yearMonthDateEnd) {
		
		SQLJob sqljob = new SQLJob();
		if (yearMonthDateEnd == null || "".equals(yearMonthDateEnd)) {
			yearMonthDateEnd = yearMonthDateStart;
		}
		sqljob.appendSQL( "SELECT" ) ;
		sqljob.appendSQL( "A.ID_NO" ) ;
		sqljob.appendSQL( ",A.STAFF_NAME" ) ;
		sqljob.appendSQL( ",( SELECT COUNT(ID_NO) FROM EICM.CEDB1000 WHERE ID_NO = A.ID_NO AND ASSIGN_DATE between ? and ? ) as d" ) ;
		sqljob.appendSQL( "FROM EICM.CEDBC000 A" ) ;
		sqljob.appendSQL( "WHERE A.GROUP_ID <> 'XX'" ) ;
		sqljob.appendSQL( "AND 0 < ( SELECT COUNT(1) FROM EICM.CEDB1000 WHERE ID_NO = A.ID_NO )" ) ;
		sqljob.appendSQL( "ORDER BY A.ID_NO" ) ;
		sqljob.addParameter(yearMonthDateStart);
		sqljob.addParameter(yearMonthDateEnd);
		return sqljob ;
	}  // doAppendSqljob()
	
	public SQLJob appendNotAssign() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" SELECT COUNT(1) as notAssign");
		sqljob.appendSQL(" FROM EICM.CEDB1000");
		sqljob.appendSQL(" WHERE ID_NO is null");
		sqljob.appendSQL(" and approve_result='A'");
		//and receive_date like SUBSTR(SUBSTR('00'||cast((to_char(sysdate,'yyyy')-1911) as varchar(10)),-3),0,3)||'%' ") ;
		return sqljob ;
	} // appendNotAssign()
	
	public SQLJob appendStartAndEnd() {
		SQLJob sqljob = new SQLJob() ;
		sqljob.appendSQL(" select max(prefix_no) as endPrefix, min(prefix_no) as startPrefix");
		sqljob.appendSQL(" from eicm.cedb1000");
		sqljob.appendSQL(" where id_no is null");
		sqljob.appendSQL(" and approve_result='A'");
		// and receive_date like SUBSTR(SUBSTR('00'||cast((to_char(sysdate,'yyyy')-1911) as varchar(10)),-3),0,3)||'%'" ) ;
		return sqljob ;
	} // appendStartAndEnd()
	
	public SQLJob appendLastClose() {
		SQLJob sqljob = new SQLJob() ;
		sqljob.appendSQL( "select max(prefix_no) as last from eicm.cedb1021"); // where close_date is not null and receive_date like SUBSTR(SUBSTR('00'||cast((to_char(sysdate,'yyyy')-1911) as varchar(10)),-3),0,3)||'%'" ) ;
		return sqljob ;
	} // appendLastClose
		
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		return null ;
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
		List<Map<String, Object>> pre8003List = new ArrayList<Map<String, Object>>();
		pre8003List = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(getQ_yearMonthDateStart(), getQ_yearMonthDateEnd()));
		List<Map<String, Object>> notAssign = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendNotAssign());
		setNotAssign( Common.get( notAssign.get(0).get("notAssign") ) ) ;
		List<Map<String, Object>> prefix = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendStartAndEnd());
		setStartPrefix( Common.get( prefix.get(0).get("startPrefix") ) ) ;
		setEndPrefix( Common.get( prefix.get(0).get("endPrefix") ) ) ;
		List<Map<String, Object>> lastClosedPrefix = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(appendLastClose());
		setLastClosedPrefix( Common.get( lastClosedPrefix.get(0).get("last") ) ) ;
	  
		if ( pre8003List == null || pre8003List.size() <= 0 ) {
			return null ;
		} // end if
		else {
			ArrayList<String[]> dataList = new ArrayList<String[]>() ;
			int i = 0 ;
			String[] rowArray ;
			while ( i < pre8003List.size() ) {
				rowArray = new String[4] ; 
				rowArray[0] = Common.get( pre8003List.get(i).get("staff_Name") )  ;
				rowArray[1] = Common.get( pre8003List.get(i).get("D") )  ;
				i++;
				dataList.add(rowArray) ;
			} // end while
			return dataList ;
		} // end else
    } // doQueryAll()
} // PRE8003()