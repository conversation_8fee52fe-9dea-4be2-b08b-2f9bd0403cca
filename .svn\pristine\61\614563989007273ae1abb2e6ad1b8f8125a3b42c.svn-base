package com.kangdainfo.common.view.json;

import java.util.ArrayList;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.SuperBean;

public class JsonMsg extends SuperBean {

	public java.util.ArrayList<String[]> getUnReadMsg(CommonUser user, boolean isSlide) {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		
		if (user!=null) {
//			java.util.Map<Long,String> h = new java.util.HashMap<Long,String>(); 
//			String hql = "from CommonMsgLog where commonMsg.startDate=" + Common.sqlChar(Datetime.getYYYMMDD()) + " and userId=" + Common.sqlChar(user.getUserId());	
//			java.util.List list = ServiceGetter.getInstance().getCommonService().load(hql);
//			if (list!=null && list.size()>0) {
//				for (int i=0; i<list.size(); i++) {
//					CommonMsgLog obj = (CommonMsgLog) list.get(i);			
//					h.put(obj.getCommonMsg().getId(), "Y");
//				}
//			}
//				
//			StringBuilder ids = null;
//			hql = "from CommonUserGroup where commonUser.id=" + user.getId();
//			list = ServiceGetter.getInstance().getCommonService().load(hql);		
//			if (list!=null && list.size()>0) {
//				ids = new StringBuilder();
//				for (int i=0; i<list.size(); i++) {
//					CommonUserGroup group = (CommonUserGroup) list.get(i);
//					ids.append(",").append(group.getCommonGroup().getId());
//				}		 		
//			}
//			
//			StringBuilder sb = new StringBuilder();
//			sb.append("from CommonMsgGroup where commonMsg.startDate=" + Common.sqlChar(Datetime.getYYYMMDD()) + " and (commonMsg.isOpen='Y' "); 
//			sb.append(" or userId=").append(user.getId());
//			sb.append(" or deptId=").append(user.getCommonDepartment().getId());	
//			if (ids!=null) sb.append(" or groupId in (-1").append(ids).append(") ");	
//			sb.append(") order by commonMsg.id");
//			String msgId = "";	//20100622 Carter for 避免多個身分別會多次收到同一封訊息
//			list = ServiceGetter.getInstance().getCommonService().load(sb.toString());
//			if (list!=null && list.size()>0) {
//				java.util.List<CommonMsgLog> saveList = new java.util.ArrayList<CommonMsgLog>();
//				for (int i=0; i<list.size(); i++) {
//					CommonMsgGroup o = (CommonMsgGroup) list.get(i);
//					
//					if (isSlide && h.get(o.getCommonMsg().getId())!=null) continue;
//									
//					if (isSlide && h.get(o.getCommonMsg().getId())==null) {
//						CommonMsgLog obj = new CommonMsgLog();
//						obj.setLogDate(Datetime.getYYYMMDD());
//						obj.setLogTime(Datetime.getHHMMSS());
//						obj.setCommonMsg(o.getCommonMsg());
//						obj.setUserId(user.getUserId());
//						obj.setIsSlide("Y");
//						saveList.add(obj);
//					}
//					if(!msgId.equals(Common.get(o.getCommonMsg().getId()))){ //20100622 Carter for 避免多個身分別會多次收到同一封訊息
//					String rowArray[] = new String[4];
//					rowArray[0] = Common.get(o.getCommonMsg().getId());				
//					rowArray[1] = Common.get(o.getCommonMsg().getMsgSender());
//					rowArray[2] = Common.FormatStr(Common.get(o.getCommonMsg().getMsgBody()),"N");
//					rowArray[3] = Common.formatYYYMMDD(o.getCommonMsg().getStartDate(),4) + " " + Common.formatHHMMSS(o.getCommonMsg().getStartTime());	
//					arrList.add(rowArray);
//					} //20100622 Carter for 避免多個身分別會多次收到同一封訊息
//					msgId = Common.get(o.getCommonMsg().getId()); //20100622 Carter for 避免多個身分別會多次收到同一封訊息
//				}
//			}				
		}
		return arrList;
	}

	public Object doQueryOne() throws Exception {return null;}
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	public void doCreate() throws Exception {}
	public void doUpdate() throws Exception {}
	public void doDelete() throws Exception {}

}