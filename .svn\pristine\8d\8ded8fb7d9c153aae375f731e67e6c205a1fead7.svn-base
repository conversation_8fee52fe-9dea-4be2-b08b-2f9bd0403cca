package com.kangdainfo.tcfi.service;

import java.io.File;
import java.util.List;

import com.kangdainfo.tcfi.model.crmsmoea.bo.CsyssUser;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;
import com.kangdainfo.tcfi.model.eedb.bo.EedbV8000;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb5000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.EedbV8000Dao;
import com.kangdainfo.tcfi.model.eicm.bo.BusiItem;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1007;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1008;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1011;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1021;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1028;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc053;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc055;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyMemoInfo;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.tcfi.model.eicm.bo.LoginLog;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;
import com.kangdainfo.tcfi.model.eicm.bo.RestrictionItem;
import com.kangdainfo.tcfi.model.eicm.bo.SynonymWord;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1003Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCountry;
import com.kangdainfo.tcfi.model.icms.bo.CsmmCmpyInfo;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;


public interface PrefixService {

	/**
	 * 查詢 預查種類
	 * @param prefixNo
	 * @return
	 */
	public String getApplyKindDesc(String prefixNo);
	
	/** 查詢營業項目 */
	public List<Cedb1002> getCedb1002sByPrefixNo(String prefixNo);
	
	/** 新增營業項目 */
	public void insertCedb1002(Cedb1002 obj);
	
	/** 刪除營業項目 
	 * @return */
	public int deleteCedb1002ByPrefixNoAndBusiItemNo(String prefixNo, String busiItemNo);

	public Cedbc000 getCedbc000ByIdNoAndPwd(String idNo, String pwd);
	
	public Cedbc000 getCedbc000ByIdNo(String idNo);	
	
	public List<Cedbc000> getCedbc000ByCondition(String id, String name, String unit);	
	
	public Cedbc000 insertCedbc000(Cedbc000 obj);
	
	public Cedbc000 updateCedbc000(Cedbc000 obj);
	
	public void deleteCedbc000(Cedbc000 obj);

	public void setAssignData(String prefixNo, String updateIdNo, String staffName);
	
	public Cedb1000 getSmallestNotAssign();
	
	public Cedb1000 getCedb1000ByPrefixNo(String prefixNo);
	
	public Cedb1000 getCedb1000ByTelixNo(String telixNo);
	
	public List<Cedb1000> getBetweenPrefixNo( String prefixStart, String prefixEnd );
	
	/** 人工改分文(PRE8005)查詢用 */
	public List<Cedb1000> getCedb1000ForPre8005(String searchType, String startNo, String endNo);
	
	public List<Cedb1000> getCedb1000ListWhenPRE2004( String prefixStart, String prefixEnd, String[] prefixList );
	
	public List<Cedb1001> getCedb1001ByPrefixNo(String prefixNo);
	
	public Cedb1001 getApprovedCedb1001ByPrefixNo(String prefixNo);
	
	public List<com.kangdainfo.tcfi.model.eicm.bo.Eedb1000> getEedb1000ListWhenPRE2004(String prefixStart,String prefixEnd,String[] prefixList);
	
	public List<com.kangdainfo.tcfi.model.eicm.bo.Eedb1002> getEedb1002ListWhenPRE2004(String prefixNo);

	public List<EedbV8000> getEedbV8000ByTelixNo(String telixNo);
	
	public OssmApplMain getOssmApplMainByTelixNo(String telixNo);
	
	public OssmApplFlow getOssmApplFlowByTelixNoAndProcessNo(String telixNo, String ProcessNo);
	
	public OssmFeeMain getOssmFeeMainByTelixNoAndProcessNo(String telixNo, String ProcessNo);
	
	public void saveOssmFeeMainByTelixNo(String telixNo );
	
	public OssmFeeDetail getOssmFeeDetailByTelixNoAndProcessNo(String telixNo, String ProcessNo);
	
	public OssmOrgChange getOssmOrgChangeByTelixNo(String telixNo);
	
	public List<Cedb1002> getCedb1002ByPrefixNo(String prefixNo);
	
	public Cedb1002 getCedb1002ByPrefixNoAndSeqNo(String prefixNo, String seqNo);
	
	public List<Cedb1002> getCedb1002ByBanNo(String banNo);

	public Cedb1022 getCedb1022ByPrefixNo(String prefixNo);
	
	public Cedb1023 getCedb1023ByPrefixNo(String prefixNo);

	public List<Cedb1027> getCedb1027ByPrefixNo(String prefixNo);
	
	public List<Cedb1027> getCedb1027ByNoStartAndNoEnd( String NoStart, String NoEnd, String type, String postType ) ;
	
	public Cedb1027 getLastOneCedb1027ByPrefixNo(String prefixNo);
	
	public List<PostRecord> getPostRecordByPrefixNo( String prefixNo );
	
	public PostRecord getPostRecordByPrefixNoAndPostNo( String prefixNo, String postNo );
	
	public List<PostRecord> getPostRecordByNoStartAndNoEnd( String NoStart, String NoEnd, String type, String postType );
	
	public PostRecord getLastOnePostRecordByPrefixNo(String prefixNo);
	
	public PostRecord getMainPostRecordByPrefixNo(String prefixNo);
	
	public int insertPostRecord(PostRecord obj);

	public Cedb1011 queryCedb1011ByPrefixNo( String prefixNo ) ;
	
	public void deleteCedb1011ByPrefixNo(String prefixNo);

	public int saveCedb1011( Cedb1011 obj );

	public List<DeclaratoryStatutes> queryDeclaratoryStatutes(DeclaratoryStatutes bo, String endTime);
	
	public List<DeclaratoryStatutesRcver> queryDeclaratoryStatutesRcver(DeclaratoryStatutes bo);
	
	public int deleteDeclaratoryStatutesRcver(DeclaratoryStatutes bo);
	
	public int insertDeclaratoryStatutesRcver(DeclaratoryStatutesRcver bo);
	
	
	public List<Cedbc053> queryCedbc053(Cedbc053 bo);
	
	public List<Cedbc055> queryAllCedbc055( String itemCode, String businessItem, String masterCode );
	
	public List<BusiItem> queryAllBusiItem( String itemCode, String businessItem, String masterCode, int length);
	
	public Cedbc055 queryCedbc055ByItemCode( String itemCode );
	
	public BusiItem queryBusiItemByItemCode( String itemCode );
	
	public int saveCedbc055( Cedbc055 obj );
	
	public BusiItem saveBusiItem( BusiItem obj );
	
	public int insertCedbc055( Cedbc055 obj );
	
	public BusiItem insertBusiItem( BusiItem obj );
	
	public int deleteCedbc055( Cedbc055 obj ) ;
	
	public void deleteBusiItem( BusiItem obj ) ;
	
	public List<Cedb1019> queryCedb1019(Cedb1019 bo);

	public List<Cedb1019> queryCedb1019();

	public List<Cedb1017> queryCedb1017(String staffUnit);
	
	public Cedb1017 queryCedb1017ByPostType(String staffUnit, String postType);
	
	public List<Cedb1021> getCedb1021List(String prefixStart, String prefixEnd);
	
	public Cedb1021 getCedb1021ByPrefixNo(String postNo);
	
	public int insertCedb1021(Cedb1021 obj);
	
	public int updateCedb1021(Cedb1021 obj);
	
	public SynonymWord querySynonymWordById(String id);
	
	public List<SynonymWord> querySynonymWord(SynonymWord bo);
	
	public SynonymWord querySynonymWordByCheckWord(String word, String synonymWord);
	
	public DeclaratoryStatutes getDeclaratoryStatutes(String rcvNo);
	
	public Cedb2000 getCedb2000ByBanNo(String banNo);
	
	public List<Cedb2002> getCedb2002ByBanNo(String banNo);
	
	public List<Cedb2004> getCedb2004ByBanNo(String banNo);
	
	public DeclaratoryStatutes saveDeclaratoryStatutes(DeclaratoryStatutes obj);
	
	public int saveWhenPublish(DeclaratoryStatutes obj);
	
	public void saveCedb1019(Cedb1019 obj);
	
	public void saveCedb1017(Cedb1017 obj);
	
	public DeclaratoryStatutes insertDeclaratoryStatutes(DeclaratoryStatutes obj);
	
	public void deleteDeclaratoryStatutes(DeclaratoryStatutes obj);

	int findOnlineDocNum();
	
	public LmsmBussMain getLmsmBussMainByBanNo(String banNo);//2024/04/17 新增
	
	public LmsmBussMain updateLmsmBussMain(LmsmBussMain lmsmBussMain); //2024/04/19 新增

	String findMaxPrefixNo(String twYear);

  	public int updateCsmmCmpyInfoByPartName(String banNo, String newPartName);
  	
  	public List<CsmmCmpyInfo> getCsmmCmpyInfoByBanNo(String banNo);
  	
  	public CsyssUser getStaffInfoById(String idNo); // 2024/05/20 新增

  	/** 查詢公司資料 */
  	public List<CsmmCmpyInfo> getCsmmCmpyInfosByCondition(String banNo,
			String companyName, String prefixNo, String specialName,
			String respName, String respIdNo);
 	
	File printApplyForm(String prefixNo, String searchType);

	/** 更新取件日期時間 */
	public void updateCedb1000GetDateTime(Cedb1000 cedb1000);
	
	/** 更新附件資料 */
	public void updateCedb1000Appendix(String prefixNo, String isPrefixForm,
			String docType, String prefixFormNo, String isOtherForm,
			String isSpec, String isOtherSpec, String otherSpecRemark,
			String userId);

	/** 更新CEDB1000 */
	public void updateCedb1000(Cedb1000 cedb1000);
	
	public Eedb1300 getEedb1300ByTelixNo(String telix_No);

	public com.kangdainfo.tcfi.model.eedb.bo.Eedb1000 findEedb1000ByTelixNo(String telxiNo);
	
	public Eedb1100 findEedb1100ByTelixNo(String telixNo);
	
	public Eedb5000 findEedb5000ByTelixNo(String telxiNo);
	
	public Eedb3300 findEedb3300ByTelixNo(String telixNo);

	/** 查詢 清算完結資料檔by banNo */
	public Cedb1028 getCedb1028ByBanNo(String banNo);
	/** 查詢 清算完結資料檔by receiveNo */
	public Cedb1028 getCedb1028ByReceiveNo(String receiveNo);
	/** 修改 完結資料檔 */
	public Cedb1028 updateCedb1028(Cedb1028 obj);
	/** 新增 清算完結資料檔by receiveNo */
	public Cedb1028 insertCedb1028(Cedb1028 obj);
	/** 刪除 清算完結資料檔by receiveNo */
	public void deleteCedb1028(String banNo);
	
	/** 查詢 公司名稱管制備忘檔 by rcvNo */
	public CmpyMemoInfo getCmpyMemoInfoByRcvNo(String rcvNo);
	/** 查詢 公司名稱管制備忘檔檔 by Condition */
	public List<CmpyMemoInfo> getCmpyMemoInfoByCondition(String rcvNo, String companyName, String reserveDateS, String reserveDateE);
	/** 修改 公司名稱管制備忘檔檔 */
	public void updateCmpyMemoInfo(CmpyMemoInfo obj);
	/** 新增 公司名稱管制備忘檔檔 */
	public void insertCmpyMemoInfo(CmpyMemoInfo obj);
	/** 刪除 公司名稱管制備忘檔檔 */
	public void deleteCmpyMemoInfo(String rcvNo);

	/** 查詢 功能選單檔 by id*/
	public FunctionMenu getFunctionMenuById(int id);
	/** 查詢 功能選單檔 by code*/
	public FunctionMenu getFunctionMenuByCode(String code);
	/** 查詢 功能選單檔 by pid */
	public List<FunctionMenu> getFunctionMenuByPid(int pid);
	/** 查詢 功能選單檔 query all */
	public List<FunctionMenu> getFunctionMenuAll();
	/** 查詢 功能選單檔 by function_menu_auth.groupId */
	public List<FunctionMenu> getFunctionMenuByGroupId(String groupId);
	/** 查詢 功能選單檔 (USE PRE9002) */
	public List<FunctionMenu> getFunctionMenuByNotExistsAuth(String groupId);
	/** 新增 功能選單檔  */
	public FunctionMenu insertFunctionMenu(FunctionMenu obj);
	/** 修改 功能選單檔  */
	public FunctionMenu updateFunctionMenu(FunctionMenu obj);
	/** 刪除 功能選單檔  */
	public void deleteFunctionMenu(FunctionMenu obj);
	/** 新增/刪除 功能選單權限檔 */
	public void confirmFunctionMenuAuth(String optype, String groupId, String[] menuId, String editId);
	
	/** 查詢 參數代碼檔 by id */
	public SystemCode getSystemCodeById(Integer id);
	/** 查詢 參數代碼檔 by codeKind */
	public List<SystemCode> getSystemCodesByCodeKind(String codeKind);
	/** 查詢 參數代碼檔 by codeKind, code */
	public SystemCode getSystemCodeByCodeKindAndCode(String codeKind, String code);
	/** 查詢 參數代碼檔 by codeKind, code*/
	public String getSystemCodeNameByCodeKindAndCode(String codeKind, String code);
	/** 查詢 參數代碼檔 by codeKind, codeName*/
	public List<SystemCode> querySystemCode(String codeKind, String codeName);
	/** 新增 參數代碼檔 */
	public SystemCode insertSystemCode(SystemCode obj);
	/** 修改 參數代碼檔 */
	public SystemCode updateSystemCode(SystemCode obj);
	/** 刪除 參數代碼檔 */
	public void deleteSystemCode(SystemCode obj);
	/** 查詢 參數代碼檔 count*/
	public Integer countSystemCode(String codeKind, String codeName);
	
	/** 查詢 營業項目限制條件資料檔 by id */
	public Restriction getRestrictionById(Integer id);
	public List<Restriction> getRestriction();
	/** 查詢 營業項目限制條件資料檔 by code */
	public Restriction getRestrictionByCode(String code);
	/** 查詢 營業項目限制條件資料檔 by condition */
	public List<Restriction> getRestrictionByCondition(String code, String name, String enable);
	/** 新增 營業項目限制條件資料檔  */
	public Restriction insertRestriction(Restriction bo);
	/** 修改 營業項目限制條件資料檔  */
	public Restriction updateRestriction(Restriction bo);
	/** 刪除 營業項目限制條件資料檔  */
	public void deleteRestriction(Restriction bo);
	
	/** 查詢 營業項目限制條件營業項目檔 by restrictionId */
	public List<RestrictionItem> getRestrictionItemByRestrictionId(Integer id);
	public List<RestrictionItem> getRestrictionItem();
	/** 查詢 營業項目限制條件營業項目檔 by code */
	public RestrictionItem getRestrictionItemByCode(Integer id, String code);
	/** 新增/刪除 營業項目限制條件營業項目檔 */
	public void confirmRestrictionItem(String optype, String id, String[] item, String editId);
	
	public void insertIntoRestrictionItem(RestrictionItem bo);
	
	public void deleteFromRestrictionItem(RestrictionItem bo);
	/** 查詢 登入紀錄檔 */
	public List<LoginLog> getLoginLog(String loginId, String loginName, String startDate, String endDate);
	/** 新增 登入紀錄檔 */
	public void insertLoginLog(LoginLog obj);
	
	/** SameName_Queue */
	public java.util.List<Queue>  getSameNameQueueWithLotsCondition(String prefixNo, String statutas, String dateStart, String dateEnd);
	public Queue  getSameNameQueueWithPk(String id);
	public int insertSameNameQueue(Queue obj);
	public Queue updateSameNameQueue(Queue obj);
	public void deleteSameNameQueue(Queue obj);
	
	/** SyncOss_Queue */
	public java.util.List<Queue>  getSyncOssQueueWithLotsCondition(String prefixNo, String statutas, String dateStart, String dateEnd);
	public Queue  getSyncOssQueueWithPk(String id);
	public int insertSyncOssQueue(Queue obj);
	public Queue updateSyncOssQueue(Queue obj);
	public void deleteSyncOssQueue(Queue obj);
	
    EedbV8000Dao getEedbV8000Dao();
    
    Eedb1000Dao getEedb1000Dao();
	
	Eedb1100Dao getEedb1100Dao();
	
	Eedb1300Dao getEedb1300Dao();
	
	Eedb3000Dao getEedb3000Dao();
	
	Eedb3300Dao getEedb3300Dao();
	
	Eedb5000Dao getEedb5000Dao();

	OssmApplFlowDao getOssmApplFlowDao();
	
	OssmApplMainDao getOssmApplMainDao();
	
	OssmOrgNameDao getOssmOrgNameDao();

	OssmBussItemDao getOssmBussItemDao();
	
	OssmOrgChangeDao getOssmOrgChangeDao();
	
	Cedb1000Dao getCedb1000Dao();
	
	Cedb1001Dao getCedb1001Dao();

	Cedb2000Dao getCedb2000Dao();
	
	Cedb2002Dao getCedb2002Dao();

	Eedb3100Dao getEedb3100Dao();

	void chanegObjType(List<?> vc_add, List<?> vc_source, int objtype)
			throws Exception;

	public Cedb1022Dao getCedb1022Dao();

	PrefixVo findPrefixData(String prefixNo, String approveResult);

	Cedb1003Dao getCedb1003Dao();

	/** 判斷申請方式 */
	public String getApplyWayByTelixNo(String telixNo);
	
	public SynonymWord insertSynonymWord(SynonymWord obj);
	public SynonymWord updateSynonymWord(SynonymWord obj);
	public void deleteSynonymWord(SynonymWord obj);

	Cedbc054Dao getCedbc054Dao();

	Cedbc053Dao getCedbc053Dao();

	String areaCodeAddrBulid(String areacode, String addr);

	/**
	 * 依營業項目判斷保留期限是否為一年
	 * @param prefixNo
	 * @return
	 */
	public boolean checkReserve365(String prefixNo);

	/**
	 * 新增 特取名稱更改記錄(PART_NAME_LOG)
	 * @param banNo
	 * @param loginIdNo
	 * @param oldName
	 * @param newName
	 */
	public void insertPartNameLog(String banNo, String loginIdNo, String oldName, String newName);

	List<CsmdCountry> queryCsmdCountry(CsmdCountry bo);

	/**
	 * 計算限辦期限
	 * @param closeDate
	 * @param reserveDays
	 * @param reserveMark
	 * @return
	 */
	public String countReserveDate(String closeDate, Integer reserveDays, String reserveMark);

	/**
	 * 查詢案件歷程
	 * @return
	 */
	public List<Cedb1010> getCedb1010sByPrefixNo(String prefixNo);
	public Cedb1010 getCedb1010sByPrefixNoAndStatus(String prefixNo, String Status);
	/**
	 * 查詢案件歷程
	 * @param prefixNo
	 * @param idNo
	 * @param processStatus
	 * @return
	 */
	public Cedb1010 getCedb1010ByUniqueKey(String prefixNo, String idNo, String processStatus);

	/**
	 * 更新案件歷程
	 * @param cedb1010
	 */
	public void updateCedb1010(Cedb1010 cedb1010);

	/**
	 * 查詢同名資料
	 * @param prefixNo
	 * @return
	 */
	public List<Cedb1004> getCedb1004sByPrefixNo(String prefixNo);

	void doIsNeedRollBackToReceive(Cedb1000 cedb1000);

	/**
	 * 收文確認
	 * @param prefixNo
	 * @param userId
	 */
	public void confirmRcvCheck(String prefixNo, String userId);

	/**
	 * 同步一站式申辦類型
	 * @param cedb1000
	 * @param ossmApplMain
	 */
	public void doSyncOsssApplyType(String prefixNo);
	
	/**
	 * ajax取得案件流程
	 * @param prefixNo
	 * @return PrefixVo
	 */
	public PrefixVo findAjaxCedb1010s(String prefixNo);
	
	/** 查詢 CEDB1006 歷程 */
	public Cedb1006 getCedb1006ByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime);
	
	/** 查詢 CEDB1007 歷程 */
	public List<Cedb1007> getCedb1007sByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime);
	
	/** 查詢 CEDB1008 歷程 */
	public List<Cedb1008> getCedb1008sByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime);
	
	/** 查詢有限合夥營業項目 */
	public List<Cedb1002> getLmsBusiItemByBanNo(String banNo);

}