<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String prefixNo = Common.get(request.getParameter("prefixNo"));
String withdrawCompanyName = Common.get(request.getParameter("withdrawCompanyName"));
System.out.println("-----"+withdrawCompanyName+"-----");

try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(prefixNo)) {
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(prefixNo);
		List<Cedb1000> datas = ServiceGetter.getInstance().getApproveService().findWithdrawWithIdAndCompanyName(cedb1000.getApplyId(), withdrawCompanyName, prefixNo);
		if (null!=datas && !datas.isEmpty()) {
			out.write("true");
		}
		else {
			out.write("false");
		}
		
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>