--DROP TABLE EICM.RESTRICTION;
-- Create table
CREATE TABLE EICM.RESTRICTION (
	ID NUMBER(15) not null,
	CODE VARCHAR2(10),
	NAME NVARCHAR2(255),
	NAME_SPECIAL CHAR(1),
	ITEM_SPECIAL CHAR(1),
	OTHER_SPECIAL CHAR(1),
	OTHER_NOTE NVARCHAR2(255),
	ORG_TYPE VARCHAR2(2),
	RELATED_UNIT NVARCHAR2(255),
	RELATED_DATE VARCHAR2(7),
	RELATED_NO NVARCHAR2(50),
	ENABLE CHAR(1) not null,
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.RESTRICTION is '營業項目限制條件資料檔';
-- Add comments to the columns 
comment on column EICM.RESTRICTION.ID is '主鍵值';
comment on column EICM.RESTRICTION.CODE is '限制條件代碼';
comment on column EICM.RESTRICTION.NAME is '限制條件名稱';
comment on column EICM.RESTRICTION.NAME_SPECIAL is '限制條件項目_名稱標明專業';
comment on column EICM.RESTRICTION.ITEM_SPECIAL is '限制條件項目_營業項目限專業經營';
comment on column EICM.RESTRICTION.OTHER_SPECIAL is '限制條件項目_其他 ';
comment on column EICM.RESTRICTION.OTHER_NOTE is '限制條件項目_其他內容 ';
comment on column EICM.RESTRICTION.ORG_TYPE is '限制條件項目_組織別';
comment on column EICM.RESTRICTION.RELATED_UNIT is '相關函號_單位';
comment on column EICM.RESTRICTION.RELATED_DATE is '相關函號_日期';
comment on column EICM.RESTRICTION.RELATED_NO is '相關函號_文號';
comment on column EICM.RESTRICTION.ENABLE is '狀態(Y:啟用,N:停用)';
comment on column EICM.RESTRICTION.MOD_ID_NO is '異動人員';
comment on column EICM.RESTRICTION.MOD_DATE is '異動日期';
comment on column EICM.RESTRICTION.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.RESTRICTION
  add constraint PK_RESTRICTION primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_RESTRICTION_ID;
-- Create sequence 
create sequence EICM.SEQ_RESTRICTION_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
set define off;
Create Or Replace Trigger EICM.TG_RESTRICTION
Before Insert ON EICM.RESTRICTION Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_RESTRICTION_ID.NextVal into :nu.id From Dual;
End;
/

-- SYNONYM 
create or replace synonym EICM4AP.RESTRICTION for EICM.RESTRICTION;

--GRANT
grant all on EICM.RESTRICTION to EICM4AP;

