<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>"%>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
String from = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("from")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		com.kangdainfo.tcfi.view.pre.PRE4020 o = ServiceGetter.getInstance().getPre4020Service().getDataByTelixNo(q);
		out.write(gson.toJson(o));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>