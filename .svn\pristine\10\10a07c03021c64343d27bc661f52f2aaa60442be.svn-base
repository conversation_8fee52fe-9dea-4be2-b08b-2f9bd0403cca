<%@page import="com.kangdainfo.sys.common.Constants"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM CEDB2000 WHERE BAN_NO=?");
		sqljob.addParameter(q);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			datas.get(0).put("STATUS_CODE", ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode((String)datas.get(0).get("STATUS_CODE")));
			out.write(gson.toJson(datas.get(0)));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>