<!DOCTYPE html>
<!--
程式目的：營業項目限制條件維護
程式代號：PRE8013
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8013_02">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8013" />
</jsp:include>
<%
if("update".equals(obj.getState())){
	obj.update();
	if("updateSuccess".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE8013_02)obj.queryOne();
	}
}else{
	obj = (com.kangdainfo.tcfi.view.pre.PRE8013_02)obj.queryOne();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<style type="text/css">
.listTHR{
	font-weight:bold;
	font-size: 15px;
	z-index: 20;
	padding: 4px 2px 2px 2px;		
	color: #000000;
	text-align:right;
	height:24px;
	position:relative;
	border: 1px solid silver;
	background-color: #dddddd;
}
</style>
<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function init(){}

function checkURL(surl){
	form1.state.value="queryAll";
	form1.action = surl;
	beforeSubmit();
	form1.submit();
}

function setItemCode(srcObj, subCode, subCode2, type){
	var obj1 = subCode;
	var oldValue = subCode.value;
	obj1.options.length=0;
	obj1.options.add(new Option("請選擇",""));
	
	if(subCode2 != null){
		var obj2 = subCode2;
		obj2.options.length=0;
		obj2.options.add(new Option("請選擇",""));
	}

	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonCedbc055.jsp?codeKind='+type+'&code='+srcObj.value, "");
	if (x!=null && x.length>0){
		var json = eval('(' + x + ')'); 
		var i = 0;
		for (i=0; i<json.length; i++){
			if(json[i].CODE==null)
				continue;	
			var oOption = new Option(json[i].CODE+" "+json[i].NAME, json[i].CODE);
	    	//if(json[i].CODE == oldValue) oOption.selected=true;		
			obj1.options.add(oOption);	
		}
		obj1.disabled = false;
		searchItemCode();
	}
}

function searchItemCode(){
	var code="";
	if(form1.subCode2.value != "")	code = form1.subCode2.value;
	else if(form1.subCode1.value != "")	code = form1.subCode1.value;
	else if(form1.masterCode.value != "") code = form1.masterCode.value;
	
	if(code == ""){
		alert("請至少選取一項主類別或子類別");
		return;
	}

	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonCedbc055.jsp?codeKind=7&code='+code, "");	
	if (x!=null && x.length>0){
		var json = eval('(' + x + ')'); 
		var i = 0;
		
		$('#addItemList').empty();
		
		for (i=0; i<json.length; i++){
			
			if(json[i].CODE==null)
				continue;
			addRow('addItemList', i+1, 'addItem', json[i].CODE, json[i].CODE, json[i].NAME);
		}
	}
}

function addRow(tblId, seq, inputName, id, code, codeName){
	var str = "<tr><td class='listTROdd'>";
	str += "<input class='field_Q' type='checkbox' id='"+inputName+"' name='"+inputName+"' value='"+id+"'></td>";
	if(seq != null){
		str += "<td class='listTROdd'>"+seq+"</td>";
	}
	str += "<td class='listTROdd'>"+code+"</td>";	
	str += "<td class='listTROdd' style='text-align:left;'>"+codeName+"</td></tr>";
	$('#'+tblId).append(str);
}

function checkListIsSelect(buttonName, itemName){
	var alertStr="";
	var $checks = $("input[name="+itemName+"]").filter(":checked");

	if($checks.size() < 1){
		if(buttonName=="remove"){
			alertStr += "[你必須先勾選左邊要移除的營業項目]";
		}else if(buttonName=="add"){
			alertStr += "[你必須先勾選右邊要增加的營業項目]";
		}
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	
	document.all("optype").value = buttonName;
	form1.state.value = "update";
}

function checkAll(isChecked, itemName){
	if(isChecked){
		commonUtils.all(itemName);
	}else{
		commonUtils.unAll(itemName);
	}
}

$(function(){
	<%=obj.getRestrictionItemList()%>
});
</script>
</head>

<body topmargin="0" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8013'/>
</c:import>

<table cellpadding=0 cellspacing=0 valign="top">
	<tr>
		<td nowrap ID=t1 CLASS="tab_border2" width="100" height="25"><a href="#" onClick="return checkURL('pre8013.jsp');">限制條件</td>
		<td nowrap ID=t2 CLASS="tab_border1" width="100">營業項目</a></td>
	</tr>
	<tr>
		<td nowrap class="tab_line1"></td>
		<td nowrap class="tab_line2"></td>	
	</tr>
</table>

<table border="1" width="100%">
	<tr><td nowrap>
		<table class="table_form" width="100%" height="25" border="0">
    		<tr>	
		    	<td nowrap class="td_form" width="15%">限制條件名稱：</td>
		        <td nowrap class="td_form_white">
		            <%=obj.getName()%>
		        </td>
		     </tr>
		</table>
		<c:import url="../common/msgbar.jsp">
			 <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
		</c:import>
	</td></tr>
	<tr><td nowrap>
		<table border="1" width="100%">
			<tr>
				<td nowrap width="45%" valign="top">
					<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
						<td nowrap class="listTH">選擇產業別既有營業項目</td>
					</table>
					<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
						<thead id="listTHEAD">
							<tr>
								<th class="listTH" width="5%">
									<input class="field_Q" type="checkbox" name="thisItemAll" onClick="checkAll(this.checked, 'thisItem');">
								</th>
								<th class="listTH" width="20%">代碼</th>
								<th class="listTH" width="75%">營業項目</th>
							</tr>
						</thead>
						<tbody id="thisItemList">
						</tbody>
					</table>
				</td>
				<td nowrap align="center" valign="top">
					<table border="0">
						<tr><td nowrap align="center" valign="middle">
							<br><br><br>
							<input class="toolbar_default" type="submit" name="add" value="<<加入<<" onClick="return checkListIsSelect(this.name, 'addItem')">
				        	<br><br><br>	
							<input class="toolbar_default" type="submit" name="remove" value=">>移除>>" onClick="return checkListIsSelect(this.name, 'thisItem')">
							<br><br><br>
							<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
							<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
							<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
							<input type="hidden" name="optype">
						</td></tr>
					</table>
				</td>
				<td nowrap width="45%" valign="top">
					<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
						<tr>
							<td nowrap class="listTHR" width="20%" colspan="2">主類別：</td>
							<td class="td_form_white" width="80%" colspan="2">
								<select class="field_Q" name="masterCode" onChange="setItemCode(this,subCode1,subCode2, 2);">
		    						<%=View.getOptionBusiItemCodeKind(1, "", obj.getMasterCode(),false, 1, "")%>
		    					</select>
		    					<input class="toolbar_default" type="button" name="codeKind" value="查詢" onClick="searchItemCode();">
							</td>
						</tr>
						<tr>
							<td nowrap class="listTHR" colspan="2">子類別：</td>
							<td class="td_form_white" colspan="2">
								<select class="field_Q" name="subCode1" onChange="setItemCode(this,subCode2, null, 4);">
		    						<%=View.getOptionBusiItemCodeKind(2, "", obj.getSubCode1(),false, 1, "")%>
		    					</select>
		    					<select class="field_Q" name="subCode2">
		    						<%=View.getOptionBusiItemCodeKind(4, "", obj.getSubCode2(),false, 1, "")%>
		    					</select>
							</td>
						</tr>
					</table>
					<div id="listContainer">
						<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
							<thead id="listTHEAD">
								<tr>
									<th class="listTH" width="5%"><input class="field_Q" type="checkbox" name="addItemAll" onClick="checkAll(this.checked, 'addItem');"></th>
									<th class="listTH" width="15%">NO</th>
									<th class="listTH" width="20%">代碼</th>
									<th class="listTH" width="60%">營業項目</th>
								</tr>
							</thead>
							<tbody id="addItemList" >
							</tbody>
						</table>
					</div>
				</td>
			</tr>
		</table>
	</td></tr>
</table>

</form>
</body>
</html>