package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 人員基本資料檔(CEDBC000)
 *
 */
public class Cedbc000 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** ID */
	private String id;
	/** 識別碼 */
	private String staffCode;
	/** 密碼 */
	private String staffPassword;
	/** 職稱 */
	private String staffTitle;
	/** 姓名 */
	private String staffName;
	/** 使用權責類別 */
	private String groupId;
	/** 電話分機號 */
	private String branchPhoneNo;
	/** 更新人員識別碼 */
	private String updateUser;
	/** 更新日期 */
	private String updateDate;
	/** 更新時間 */
	private String updateTime;
	/** 密碼 */
	private String staffPasswd;
	/** 改變日期 */
	private String changeDate;
	/** 單位別 */
	private String staffUnit;
	/** 身份證字號 */
	private String idNo;
	/** 密碼 */
	private String idPassword;

	public String getId() {return id;}
	public void setId(String id) {this.id = id;}
	public String getStaffCode() {return staffCode;}
	public void setStaffCode(String staffCode) {this.staffCode = staffCode;}
	public String getStaffPassword() {return staffPassword;}
	public void setStaffPassword(String staffPassword) {this.staffPassword = staffPassword;}
	public String getStaffTitle() {return staffTitle;}
	public void setStaffTitle(String staffTitle) {this.staffTitle = staffTitle;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String staffName) {this.staffName = staffName;}
	public String getGroupId() {return groupId;}
	public void setGroupId(String groupId) {this.groupId = groupId;}
	public String getBranchPhoneNo() {return branchPhoneNo;}
	public void setBranchPhoneNo(String branchPhoneNo) {this.branchPhoneNo = branchPhoneNo;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getStaffPasswd() {return staffPasswd;}
	public void setStaffPasswd(String staffPasswd) {this.staffPasswd = staffPasswd;}
	public String getChangeDate() {return changeDate;}
	public void setChangeDate(String changeDate) {this.changeDate = changeDate;}
	public String getStaffUnit() {return staffUnit;}
	public void setStaffUnit(String staffUnit) {this.staffUnit = staffUnit;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	public String getIdPassword() {return idPassword;}
	public void setIdPassword(String idPassword) {this.idPassword = idPassword;}

}