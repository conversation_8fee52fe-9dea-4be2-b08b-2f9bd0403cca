<!DOCTYPE html>  
<%-- 上面那行拿掉在輸入統編時會出現script error --%>
<%--
程式目的：收/發文-收/發文登打-基本資料
程式代號：PRE1006
撰寫日期：103.05.19
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
--%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1006">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<%
obj.initPrefixNos();
if("saveBusiItem".equals(obj.getState())) {
	obj.saveBusiItem();
} else if("tempSave".equals(obj.getState())) {
	obj.tempSave();
}
%>
<html>
<head>
<%-- 使用跳出視窗方式的功能需指定 TITLE --%>
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE1006"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<script type="text/javascript" src="../../js/jquery.splendid.textchange.js"></script>
<SCRIPT LANGUAGE="JAVASCRIPT">
var prefixNos;
	var prefixJson;
	
	function checkBeforeSubmit() {
		if( $("input[name=prefixFormNo]").val() == '' || $("input[name=prefixFormNo]").val().length == 9) {
			alert("附件欄位中的預查編號只能是空白或9碼");
			return false;
		} 

		return true;
	}

	var ilocation = -1;
	function setfocus(idx) {
		ilocation = idx;
		document.forms[0].focus.value = ilocation;
	}

	function addSpecialWord() {
		
	    if(ilocation < 0) {
	    	$(".cedb1002:last").find("[type=button]").click();
			ilocation = $(".cedb1002").size() + 1;
			
	    } else {
	    	$("#cedb1002s tr").eq(ilocation).find("[type=button]").click();
	    }
	    
	    $("#cedb1002s tr").eq(ilocation).find("input[name=busiItemNo]").val("ZZ99999");
		$("#cedb1002s tr").eq(ilocation).find("input[name=busiItem]").val("除許可業務外，得經營法令非禁止或限制之業務");
	}

	function changeCase() {
		var e = jQuery.Event("keypress");
		e.which = 13;
		jQuery('#prefixNo').trigger(e);
	}

	function isReceiveExist() {
		if ($("input[name=receiveName]").val() != '') {
			alert('提示！已有收件人資料，請檢查。');
			return false;
		}
		if ($("input[name=receiveAddr]").val() != '') {
			alert('提示！已有收件人資料，請檢查。');
			return false;
		}

		return true;
	}
	

$(document).ready(function() {
	
	if($("input[name=state]").val().indexOf('success') != -1) {
		showMsgBar('存檔成功');
	}
	
	//離開
	$('#sc_close').click(function(){
		form1.action = "pre1006.jsp";
		form1.state.value = "init";
		form1.submit();
	});
	//備註歷史
	$("#btnHistor").click(function(){
		closeReturnWindow();
		returnWindow=window.open('pre4001_02.jsp?prefixNo='+$("#prefixNo").val(),'pre4001_02','width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');
	});
	//複製至收件人
	$("#copyApplyToReceiver").click(function(){
		if(isReceiveExist()){
			$("input[name=receiveName]").val($("input[name=applyName]").val());
			$("input[name=receiveAddr]").val($("input[name=applyAddr]").val());
		}
	});
	//複製至收件人
	$("#copyAttorToReceiver").click(function(){
		if(isReceiveExist()){
			$("input[name=receiveName]").val($("input[name=attorName]").val());
			$("input[name=receiveAddr]").val($("input[name=attorAddr]").val());
		}
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("input[name=hiddenPrefixNos]").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if(++currentIndex >= prefixNos.length) {
			form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[0];
			//window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[0] +"&hiddenPrefixNos="+prefixNos;
		} else {
			form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[currentIndex];
			//window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		form2.submit();
		$.cookie("activeTabIndex", 0);
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("input[name=hiddenPrefixNos]").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if((currentIndex-1) < 0) {
			form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[(prefixNos.length-1)];
			// window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[(prefixNos.length-1)] +"&hiddenPrefixNos="+prefixNos;
		} else {
			form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[--currentIndex];
			// window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[--currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		form2.submit();
		$.cookie("activeTabIndex", 0);
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("input[name=hiddenPrefixNos]").val().split(",");
		var last = prefixNos.length - 1;
		form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[last];
		// window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[last] +"&hiddenPrefixNos="+prefixNos;
		form2.submit();
		$.cookie("activeTabIndex", 0);
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		form2.action = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		// window.location.href = getVirtualPath() + "tcfi/pre/pre1006_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		form2.submit();
		$.cookie("activeTabIndex", 0);
	});
	//上層頁籤
	$("#tabs").tabs();
	//下層頁籤
	$("#tabs2").tabs();

		if( $("#state").val() == "assignSuccess" ) {
			var assignPrefixNo = $("#assignPrefixNo").val();
			
			try {
				prefixNos = JSON.parse($.cookie("prefixNos"));
			}catch(e){}
			if($.inArray(assignPrefixNo, prefixNos) == -1) {
				prefixNos.push($("#assignPrefixNo").val());
			}
			
			$.cookie("prefixNos", JSON.stringify(prefixNos));
			$("#prefixNo").val(prefixNos[prefixNos.length-1]);
		}

		init();

		try {
			prefixNos = JSON.parse($.cookie("prefixNos"));
		}catch(e){}

		$("#current").val($("#prefixNo").val());

		$("#cedb1002s").delegate('input[type=text]', 'focus', function () {
			var index = $(this).closest('tr').index();
			setfocus(index);
		});

		$("#saveBusiItem").click(function() {
			$("#tempSave").trigger('click');
		});

		$("#zz9999").click(function(){
			addSpecialWord();
		});

		$("#delBuItem").click(function() {
			$("#cedb1002s input[name=cedb1002Chk]:checked").closest('tr').remove();
			resetCedb1002SeqNo();
		});

		$("#saveCompanyName").click(function(){
			$("#tempSave").trigger("click");
		});

		$("#tempSave").click(function(e) {
			form1.state.value = "tempSave";
			$("#hRemark").val($("input[name=remark]", "#fragment-1").val());
			var json1 = $("#form1").serializeObject();
		    var data = $.extend(prefixJson, getPrefixVo(), json1);
		    delete data.cedb1006s;
		    delete data.cedb1010s;
		    data.approveResult = $("select[name=approveResult]").val();
		    data.remark = $('[name=remark]').eq(0).val();
		    data.companyName = $("#fragment-1 input[name=companyName]").val();
		    data.applyKind = getApplyKindSave();
		    //附件
		    data = setAttachment(data);
			$("#json").val(JSON.stringify(data));
		    form1.submit();
		});

		$("#save").click(function(e) {
			
			if ( !checkApplyId()) {
				return 
			}
			/*
			if ($("#orgType").val() == "") {
				alert("請選擇組織別");
				return
			}
			*/
			if( !chkAttachment() || !checkGetKind()) {
				return;
			}
			
			//　1040115 領件方式選擇為自取時，如果郵寄註記只有輸入數字則不可讓使用者存檔 //2024/03/17 新增線上列印
			if ( ($("input[name=getKind]:checked").val() == 1 || $("input[name=getKind]:checked").val() == 3) && hasNumberInGetKindRemark() ) {
				alert("領件方式勾選為自取或線上列印時，郵寄註記不得只輸入數字");//2024/03/17 新增線上列印
				return
			}
			
			//只有名稱變更時，不檢核營業項目
			if( 1 == getApplyKind()) {
				$("#tempSave").trigger("click");
			} else {
				if(!checkCedb1002s() || checkDuplicateItemCode()) {
					e.preventDefault();
				} else {
					$("#tempSave").trigger("click");
				}
			}
		});

		$("#buItemSelectAll").click(function(){
			commonUtils.all("cedb1002Chk");
		});

		$("#buItemUnSelectAll").click(function(){
			if(this.checked)
				commonUtils.unAll("cedb1002Chk");
		});

		$("#companyNo").keypress(function(e){
			if(e.which == 13) {
				e.preventDefault();
				$.post(getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + $(this).val(), function(data){
					$("input[name='companyName']", "#cedb1001s").val('').eq(0).val(data.COMPANY_NAME);
				});
			}
		});
		
		// 103/10/30 依使用者要求加入輸完公司統編自動帶出公司現況主檔名稱的功能
		
		$("#banNo").on('textchange', function(e) {
			if($(this).val().length == 8) {
				e.preventDefault();
				$.post( getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + $(this).val(), function( data ) {
					if (data.CLOSED == 'Y') {
						$("#mainFileCompanyName").val(data.COMPANY_NAME+'(閉鎖性)');
						$("#xMainFileCompanyName").html(data.COMPANY_NAME+'(閉鎖性)');
					} else {
						$("#mainFileCompanyName").val(data.COMPANY_NAME);
						$("#xMainFileCompanyName").html(data.COMPANY_NAME);
					}
					// syncChangeTypeCheckbox(data.CHANGE_TYPE);
				});
			} else {
				//reset
				$("#mainFileCompanyName").val('');
				$("#xMainFileCompanyName").html('');
			}
		});

		$("#prefixNo").keypress(function(e){
			if(e.which == 13) {

				$.post( getVirtualPath() + "tcfi/ajax/jsonPrefixVo.jsp?from=PRE1006&q=" + $("#prefixNo").val(), function( data ) {

					if(!data)
						return;
					
					commonUtils.mappingJsonByName(data, false);
					selectApplyKind(data.applyKind);
					document.getElementById("xMainFileCompanyName").innerText = form1.mainFileCompanyName.value;
					document.getElementById("xCompanyName").innerText = form1.companyName.value;
					if(data.cedb1027) {
						$("input[name=postNo]").val(data.cedb1027.postNo);
						$("#postNoView").html(data.cedb1027.postNo);
					}
					if(data.approveResult) {
						$("select[name=approveResult] option:contains('"+ data.approveResult +"')").prop("selected", true);
					}

					var isSamePerson = $("input[name='applyName']").val() == $("input[name='contactName']").val();
					
					if(isSamePerson && $("input[name='applyAddr']").val()=="") {
						$("input[name='applyAddr']").val(data.CONTACT_ADDR);
					}

					var applyType = data.applyType;
					var contactGetKind = data.contactGetKind;

					$('#applyWay').html(data.applyWay);//申請方式
					$('#receiveDateTime').html(data.receiveDateTime);//收件日期時間
					$('#receiveKeyinDateTime').html(data.receiveKeyinDateTime);//收文登打日期時間
					$('#assignDateTime').html(data.assignDateTime);//分文日期時間
					$('#approveDateTime').html(data.approveDateTime);//審核日期時間
					$('#issueKeyinDateTime').html(data.issueKeyinDateTime);//發文登打日期時間
					$('#closeDateTime').html(data.closeDateTime);//發文日期時間
					$('#getDateTime').html(data.getDateTime);//領件日期時間

					if(data.cedb1023) {
						$("input[name=receiveName]").val(data.cedb1023.getName);
						$("input[name=receiveId]").val(data.cedb1023.getName);
						$("input[name=contactCel]").val(data.cedb1023.contactCel);
						$("input[name=receiveAddr]").val(data.cedb1023.getAddr);
						$("input[name=sms]").val(data.cedb1023.sms);
						$("input[name=changeType]").val(data.cedb1023.changeType);
						//$("input[name=closed]").val(data.cedb1023.closed);
						if (data.cedb1023.closed == 'Y') {
							document.getElementsByName('closed')[0].checked = true;
						}
						$("select[name=orgType]").val(data.cedb1023.orgType);
					}
					
					//if ($("input[name='closed']").prop("checked")) {
					//	document.getElementById("xMainFileCompanyName").innerText =  document.getElementById("xMainFileCompanyName").innerText + '(閉鎖性)';	
					//}

					if($("#cedb1001s tr").length == 4) {
						var cedb1001Html = "";
						var seqNo = "";
						var companyName = "";
						var isApprove = "";
						var remark = "";
						var tr_class = "";

						for(var i=0; i< 5; i++) {
							if(i%2==0) tr_class = "listTREven";
							else tr_class = "listTROdd";

							if(data.cedb1001s[i]) {
								seqNo = data.cedb1001s[i].seqNo;
								companyName = data.cedb1001s[i].companyName;
								isApprove = data.cedb1001s[i].approveResult == 'Y' ? "checked" : "";
								remark = commonUtils.trimUndefined(data.cedb1001s[i].remark);
							}else {
								seqNo = commonUtils.padZero(i+1, 2);
								companyName = "";
								isApprove = "";
								remark = "";
							}
							
							cedb1001Html += '<tr class="'+tr_class+'">'+
							'<td style="text-align: center">' +
							'<input class="field_RO" readonly type="text" size="2" style="text-align: center" name="seqNo" value="'+ seqNo +'">'+
							'</td>' +
							'<td>'+
							'<input class="field" type="text" size="30" name="companyName" value="'+ companyName +'">'+
							'</td>' +
							'<td style="text-align: center; padding: 0 28px;">' +
							'<input type="checkbox" '+ isApprove +' name="approveResult" value="Y">'+
							'</td>' +
							'<td >'+
							'<input class="field_RO" type="text" name="showSame0'+ (i+1) +'" size="30" value="">' +
							'</td>'+
							'<td style="text-align: center">'+
							'<input size="30" type="text" name="remark" class="field_RO" value="'+ remark  +'" readonly>'+
							'</td>'+
							'</tr>';
						}
	
						 $("#cedb1001s tr").eq(0).after(cedb1001Html);
					}

					//重構至approve.js
					if(data.cedb1001s && data.cedb1004s)
						showSames(data.cedb1001s, data.cedb1004s);
					
					//法人資訊
					setCedb1022(data.cedb1022);
					//營業項目
					setCedb1002Row(data.cedb1002s);
					//案件流程
					setCedb1010Row(data.cedb1010s);
					
					prefixJson = $.extend(prefixJson, data);
				});


				if($("#companyNames").find("tr").size() == 1) {
					setTimeout(function(){
						var html = '';
						
						$.post( getVirtualPath() + "tcfi/ajax/jsonCedbc1001.jsp?q2=Y&q=" + $("#prefixNo").val(), function( data ) {
							html += '<tr><td><input name="seqNo" class="inputNoBorder" type="text" value="'+ data[0].SEQ_NO +'" readonly /></td>' +
							'<td><input name="applyCompanyNamesArray" class="inputNoBorder" type="text" value="'+ data[0].COMPANY_NAME +'" readonly /></td></tr>';
							
							$("#companyNames").append(html).show();
						});
					}, 500);
				}
		    }
		});
	});

	$(window).load(function(){

		changeCase();
		
		$("#cedb1001s").on("change", "input[name=approveResult]", function() {
			$("[name="+$(this).prop('name')+"]").prop("checked", false);
		    $(this).prop("checked", true);
			commonUtils.unAll("approveResultAllNo");
		});
	});

	function getPrefixVo() {
		var prefixVo = {};
		var cedb1001s = [];
		var cedb1002s = [];
		var cedb1022 = {};
		var cedb1023 = {};
		var prefixNo = $("#prefixNo").val();
		var seqNo = "";
		var seq = 1;
		$("input[name=companyName]", "#cedb1001s").each(function(k, v) {

			var companyName = $(v).val();
			if(companyName) {
				var cedb1001 = {};
				//序號要檢查重排
				seqNo = commonUtils.padZero(seq++, 2);
				cedb1001.prefixNo = prefixNo;
				cedb1001.companyName = companyName;
				cedb1001.seqNo = seqNo;

				var approveResult = $(v).closest('td').next('td').find("input[name=approveResult]").is(":checked");
				cedb1001.approveResult = approveResult ? "Y" : "N";
				var remark = $(v).closest('tr').find("input[name=remark]").val();
				if(remark) {
					cedb1001.remark = remark.replaceAll(' ', '').replaceAll('"', "''");
				}
				cedb1001s.push(cedb1001);
			}

		});

		$("input[name=busiItem]").each(function(k, v) {

			var busiItem = $(v).val().replaceAll(' ', '');
			if(busiItem) {
				var cedb1002 = {};
				cedb1002.prefixNo = prefixNo;
				cedb1002.busiItem = busiItem;
				cedb1002.seqNo = $(v).closest('td').prev('td').prev('td').find("input[name=itemSeqNo]").val();
				cedb1002.busiItemNo = $(v).closest('td').prev('td').find("input[name=busiItemNo]").val();
				if(cedb1002.busiItemNo == '') {
					cedb1002.busiItemNo = 'null';
				}
				
				cedb1002s.push(cedb1002);
			}
		});
		
		cedb1022.prefixNo = prefixNo;
		cedb1022.applyLawName = $('input[name=applyLawName]').val();
		cedb1022.applyBanNo = $('input[name=applyBanNo]').val();

		cedb1023.prefixNo = prefixNo;
		cedb1023.getAddr = $("input[name=receiveAddr]").val();
		cedb1023.getName = $("input[name=receiveName]").val();
		cedb1023.sms = $("input[name=sms]").val();
		cedb1023.contactCel = $("input[name=contactCel]").val();
		cedb1023.changeType = getApplyKind();
		if ($("input[name=closed]").prop("checked")) {
			cedb1023.closed = 'Y';
		} else {
			cedb1023.closed = 'N';
		}
		cedb1023.orgType = $("select[name=orgType]").val();

		prefixVo.cedb1001s = cedb1001s;
		prefixVo.cedb1002s = cedb1002s;
		prefixVo.cedb1023 = cedb1023;
		prefixVo.cedb1022 = cedb1022;

		return prefixVo;
	}
		
	function approveResultAllNoClick(input){
		commonUtils.unAll("approveResult");
	}

	function saveData(printName) {

		if (document.forms[0].prefixFormNo.value.length != 9
				&& document.forms[0].prefixFormNo.value.length > 0) {
			alert("附件預查表編號長度不符，請重新輸入");
			return;
		}

		document.forms[0].method.value = "save";
		document.forms[0].printName.value = printName;
		document.forms[0].submit();
	}
	
	function hasNumberInGetKindRemark() {
		if(isNaN($("#getKindRemark").val()) || $("#getKindRemark").val() == '')    // 判斷是否為合法數字
			return false;
		else 
			return true;
	}
	
</SCRIPT>
<style>
#iframe {
	height: 580px;
	width: 100%;
}
.inputNoBorder {
	float: left;
	border: none !important;
	margin-top: 7px;
	background-color: White;
	background-repeat: no-repeat;
	background-position: center center;
}
</style>
<script type="text/javascript" src="<%=contextPath%>/js/approve.js"></script>
</head>
<body>
<form id="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1006'/>
</c:import>

<table id="shareBar" width="100%" border="0" cellpadding="2" cellspacing="0">
	<tr>
		<td style="text-align: left;padding-left:100px;">
			<table>
				<tr><td>
					<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
				</td>
				<td width="100" align="center">
					<input type="text" class="field_RO" id="current" size="10" value="" readonly />
 				</td>
				<td>
					<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
 				</td>
				</tr>
			</table>
		</td>
		<td style="text-align: right;padding-right:15px;">
			<input class="toolbar_default" type="button" id="save" value="存檔" />&nbsp;
			<input class="toolbar_default" type="button" name="btnQuery6" disabled value="列印申請表" />&nbsp;
			<input class="toolbar_default" type="button" id="previewApproveForm" value="電子核定書" />&nbsp;
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
				<c:param name="shortcut" value='N'/>
			</c:import>
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" />
		</td>
	</tr>
</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1"><span>案件資料</span></a></li>
		<li><a href="#fragment-2"><span>預查名稱</span></a></li>
		<li><a href="#fragment-3"><span>營業項目</span></a></li>
		<li><a href="#fragment-4"><span>案件流程</span></a></li>
	</ul>
	<div id="fragment-1">
		<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="7" style="font-size:1px;height:3px;" >&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="80px">預查編號</td>
				<td class="td_form_white" width="60px">
					<input type="text" class="field_RO" id="prefixNo" name="prefixNo" size="8" value="" readonly />
				</td>
				<td class="td_form_white" width="90px">
					<div id="applyWay" style="bold;color:#008000"></div>
				</td>
				<td class="td_form" width="100px">預查種類</td>
				<td class="td_form_white">
					<input type="checkbox" name="setup" value="true">設立
					<input type="checkbox" name="changeName" value="true">名稱變更
					<input type="checkbox" name="changeItem" value="true">所營變更
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="closed" value="Y">閉鎖性股份有限公司
					<!-- <input type="checkbox" id="checkClosed" name="checkClosed"> -->
				</td>
				<td class="td_form" width="100px">組織別</td>
				<td class="td_form_white">
					<select class="field" id="orgType" name="orgType">
						<option value="">請選擇</option>
						<option value="01">股份有限公司</option>
						<option value="02">有限公司</option>
						<option value="03">無限公司</option>
						<option value="04">兩合公司</option>
						<option value="05">有限合夥</option>
					</select>
				</td>
			</tr>
			<tr>
				<td class="td_form">附件</td>
				<td class="td_form_white" colspan="6">
					<input type="checkbox" name="isPrefixForm" value="Y">
					<select class="field" name="docType">
						<option value="1">預查表正本</option>
						<option value="3">線上申辦檢還申請書</option>
					</select>
					<input type="text" class="field" id="prefixFormNo" name="prefixFormNo" maxlength="9" size="9" value="" />
					<input type="checkbox" name="isOtherForm" value="Y">其他機關核准函
					<input type="checkbox" name="isSpec" value="Y">說明書
					<input type="checkbox" name="isOtherSpec" value="Y">其他
					<input type="text" class="field" id="otherSpecRemark" name="otherSpecRemark" maxlength="500" size="30" value="" />
				</td>
			</tr>
			<tr>
				<td class="td_form">統一編號</td>
				<td class="td_form_white" colspan="2">
				<!-- 103/10/30 依使用者要求固定統編為8碼 -->
					<input type="text" class="field" id="banNo" name="banNo" size="10" maxLength="8" value="">
				</td>
				<td class="td_form">領件方式</td>
				<td class="td_form_white">
				    <input type="radio" name="getKind" value="3">線上列印 <!--2024/03/17 新增線上列印 -->
					<input type="radio" name="getKind" value="1">自取
					<input type="radio" name="getKind" value="2">郵寄
					郵資<input type="text" class="field" id="getKindRemark" name="getKindRemark" maxlength="3" size="3" value="">
					&nbsp;&nbsp;&nbsp;掛號號碼
					<span id="postNoView" name="postNoView"></span>
					<input type="hidden" class="field_RO" id="postNo" name="postNo" maxlength="10" size="12" value="" />
				</td>
				<td class="td_form" width="100px">網路收文號</td>
				<td class="td_form_white" width="130px">
					<input type="text" class="field_RO" id="telixNo" name="telixNo" size="20" value="" />
				</td>
			</tr>
			<tr>
				<td class="td_form">特取名稱</td>
				<td class="td_form_white" colspan="2">
					<input type="text" class="field cmex" id="specialName" name="specialName" size="20" value="" />
				</td>
				<td class="td_form">本次預查名稱</td>
				<td class="td_form_white">
					<span id=xCompanyName></span>
					<input type="hidden" class="field_RO cmex" id="companyName" name="companyName" size="18" value="" readonly />
				</td>
				<td class="td_form">收件日期</td>
				<td class="td_form_white"><div id="receiveDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">承辦人</td>
				<td class="td_form_white" colspan="2">
					<input type="text" class="field_RO" id="staffName" name="staffName" size="10" value="" readonly />
				</td>
				<td class="td_form">前次預查名稱</td>
				<td class="td_form_white">
					<input type="text" class="field cmex" id="lastCompanyName" name="lastCompanyName" size="18" value="" />
				</td>
				<td class="td_form">收文登打</td>
				<td class="td_form_white"><div id="receiveKeyinDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form" width="80px" style="padding:5px;">現況<br/>主檔名稱</td>
				<td class="td_form_white" colspan="2">
					<span id=xMainFileCompanyName></span>
					<input type="hidden" class="field_RO" id="mainFileCompanyName" name="mainFileCompanyName" size="25" value="" />
				</td>
				<td class="td_form">前次異動者</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="updateName" name="updateName" size="10" value="" readonly />
				</td>
				<td class="td_form">分文日期</td>
				<td class="td_form_white"><div id="assignDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form" width="80px">
					備註紀錄<br/>
					<input class="toolbar_form" type="button" id="btnHistor" name="btnHistor" value="備註歷史" />
				</td>
				<td class="td_form_white" colspan="4">
					<textarea class="field_RO" id="remark1" name="remark1" cols="50" rows="2" readonly ></textarea>
				</td>
				<td class="td_form">審核日期</td>
				<td class="td_form_white"><div id="approveDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">註記說明</td>
				<td class="td_form_white" colspan="4">
					<input type="text" class="field" id="remark" name="remark" size="40" value=""/>(檢還,撤件專用)
				</td>
				<td class="td_form">發文登打</td>
				<td class="td_form_white"><div id="issueKeyinDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">核覆結果</td>
				<td class="td_form_white" colspan="2">
					<select class="field" id="approveResult" name="approveResult" disabled>
						<option value="A">審查中</option>
						<option value="N">不予核准</option>
						<option value="Y">核准保留</option>
					</select>
				</td>
				<td class="td_form">案件狀態</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="prefixStatusDesc" name="prefixStatusDesc" size="18" value="" readonly />
				</td>
				<td class="td_form">發文日期</td>
				<td class="td_form_white"><div id="closeDateTime" ></div></td>
			</tr>
			<tr>
				<td class="td_form">保留期限</td>
				<td class="td_form_white" colspan="4">
					<input type="text" class="field_RO" id="reserveDate" name="reserveDate" maxlength="10" size="12" value="" readonly>
					<input type="radio" name="reserveDays" value="180" disabled>保留半年
					<input type="radio" name="reserveDays" value="365" disabled>保留一年
					<input type="radio" name="reserveMark" value="Y" disabled>延長期限一個月
				</td>
				<td class="td_form">領件日期</td>
				<td class="td_form_white"><div id="getDateTime" ></div></td>
			</tr>
		</TABLE>

		<div id="tabs2">
			<ul>
				<li><a href="#tabs2-f1"><span>申請人資料</span></a></li>
				<li><a href="#tabs2-f2"><span>代理人資料</span></a></li>
				<li><a href="#tabs2-f3"><span>收件人資料</span></a></li>
				<li><a href="#tabs2-f4"><span>自由填列事項</span></a></li>
			</ul>
			<div id="tabs2-f1">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">申請人姓名</td>
						<td class="td_form_white">
							<input type="text" class="field_Q cmex" id="applyName" name="applyName" size="15" maxlength="130" value="" />
						</td>
						<td class="td_form">身分ID</td>
						<td class="td_form_white">
							<input type="text" class="field_Q" id="applyId" name="applyId" size="15" value="" maxlength="10" />
						</td>
						<td class="td_form">申請人電話</td>
						<td class="td_form_white">
							<input type="text" class="field_Q" id="applyTel" name="applyTel" size="15" value="" />
						</td>
					</tr>
					<tr>
						<td class="td_form">所代表法人</td>
						<td class="td_form_white">
							<input type="text" class="field_Q" id="applyLawName" name="applyLawName" size="15" maxlength="130" value="">
						</td>
						<td class="td_form">法人統編</td>
						<td class="td_form_white" colspan="3">
							<input type="text" class="field_Q" id="applyBanNo" name="applyBanNo" size="15" value="">
						</td>
					</tr>
					<tr>
						<td class="td_form">申請人地址</td>
						<td class="td_form_white" colspan="5">
							<input type="text" class="field_Q" id="applyAddr" name="applyAddr" size="80" value="" />
							<input type="button" class="toolbar_default" id="copyApplyToReceiver" name="copyApplyToReceiver" value="複製至收件人" />
						</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f2">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">代理人姓名</td>
						<td class="td_form_white">
							<input type="text" class="field_Q cmex" id="attorName" name="attorName" size="10" value="" />
						</td>
						<td class="td_form">證書號碼╱身分ID</td>
						<td class="td_form_white">
							<input type="text" class="field_Q" id="attorNo" name="attorNo" size="20" value="" />
						</td>
						<td class="td_form">聯絡電話</td>
						<td class="td_form_white">
							<input type="text" class="field_Q" id="attorTel" name="attorTel" size="15" value="" />
						</td>
					</tr>
					<tr>
						<td class="td_form">事務所所在地</td>
						<td class="td_form_white" colspan="5">
							<input type="text" class="field_Q" id="attorAddr" name="attorAddr" size="80" value="" />
							<input type="button" class="toolbar_default" id="copyAttorToReceiver" name="copyAttorToReceiver" value="複製至收件人" />
						</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f3">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">收件人姓名</td>
						<td class="td_form_white">
							<input class="field_Q cmex" id="receiveName" name="receiveName" type="text" size="10" value="" />
						</td>
						<td class="td_form" width="160px">簡訊通知回覆電話</td>
						<td class="td_form_white">
							<input class="field_Q" id="contactCel" name="contactCel" type="text" size="15" value="" />
						</td>
						<td class="td_form" width="100px">寄件日期</td>
						<td class="td_form_white">
							<input type="text" class="field_RO" name="getDate" size="9" value="" readonly />
							<input type="text" class="field_RO" name="getTime" size="8" value="" readonly />
						</td>
					</tr>
					<tr>
						<td class="td_form">聯絡地址</td>
						<td class="td_form_white" colspan="5">
							<input class="field_Q" name="receiveAddr" type="text" size="80" value="">
							<input type="hidden" type="text" id="changeType" name="changeType" value="">
							<input type="hidden" type="text" name="sms" value="">
						</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f4">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form_white" colspan="6">（不納入預查審核項目）</td>
					</tr>
					<tr>
						<td class="td_form" width="180px">國外匯款使用英文名稱</td>
						<td class="td_form_white" colspan="5">
							<input class="field_Q" id="extRemitEname" name="extRemitEname" type="text"
							 size="50" value="" maxlength="120" />(僅提供銀行開戶使用)
					</tr>
				</TABLE>
			</div>
		</div>
	</div>
	<div id="fragment-2">
		<TABLE id="cedb1001s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" style="text-align: center;width:50px;">序號</td>
				<td class="td_form" style="text-align: center;width:240px;">預查名稱
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;輸入統編帶預查名稱<br/>
					<input class="field_Q" name="companyNo" id="companyNo" type="text" size="10" value="">
				</td>
				<td class="td_form" style="text-align: center;width:70px;">審核結果</td>
				<td class="td_form" style="text-align: center;width:350px;">同名組織/結果</td>
				<td class="td_form" style="text-align: center">同名註記</td>
			</tr>
			<!-- insert here -->
			<tr>
			</tr>
			<tr>
			</tr>
			<tr>
			</tr>
		</TABLE>
	</div>
	<div id="fragment-3">
		<TABLE id="cedb1002s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" style="text-align: center">
					<input class="toolbar_default" type="button" id="tempSave" value="暫存" style="display : none"/>
				</td>
				<td class="td_form" style="text-align: left;width:60px;" colspan="6">
					<input class="toolbar_default" type="button" id="delBuItem" value="刪除營業項目" />
					<input class="toolbar_default" type="button" id="buItemSelectAll" value="全部選取" />
					<input class="toolbar_default" type="button" id="buItemUnSelectAll" value="取消全選" />
					<input class="toolbar_default" type="button" id="zz9999" value="除許可.." />
				</td>
			</tr>
			<tr>
				<td class="td_form" style="text-align: center;width:50px;padding-top:50px;" valign="top" rowspan="999">
					<input type="image" src="../../images/pre/btn_bi_query.gif" alt="所營項目輔助查詢"
						id="btnItemList" name="btnItemList"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_01.jsp', 'pre3001_01','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_import.gif" alt="匯入營業項目"
						id="btnImportCeItem" name="btnImportCeItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_02.jsp', 'pre3001_02','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_select.gif" alt="線上申辦所營項目"
						id="btnImportEicmItem" name="btnImportEicmItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_03.jsp', 'pre3001_03','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
				</td>
				<td class="td_form" style="text-align:center;width:40px;">選取</td>
				<td class="td_form" style="text-align:center;width:60px;">序號</td>
				<td class="td_form" style="text-align:center;width:100px;">代碼</td>
				<td class="td_form" style="text-align:center;width:400px;">營業項目</td>
				<td class="td_form" style="text-align:left;width:50px;"><input class="toolbar_default" type="button"  value="  +  "></td>
				<td class="td_form" style="text-align:center">檢視訊息</td>
			</tr>
		</table>
	</div>
	<div id="fragment-4">
		<TABLE id="cedb1010s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" style="text-align: left">序號</td>
				<td class="td_form" style="text-align: left">案件處理狀況</td>
				<td class="td_form" style="text-align: left">案件處理時間</td>
				<td class="td_form" style="text-align: left">處理人員</td>
				<td class="td_form" style="text-align: left">工作日數</td>
			</tr>
		</TABLE>
	</div>
</div>
<input type="hidden" name="hiddenPrefixNos" value="<%=obj.getHiddenPrefixNos()%>">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="focus" name="focus" value="">
<input type="hidden" id="functionName" name="functionName" value="receive-keyin">
<input type="hidden" id="json" name="json" value="">
<input type="hidden" id="assignPrefixNo" name="assignPrefixNo" value="<%=obj.getAssignPrefixNo()%>">
<input type="hidden" id="autoApprove" name="autoApprove" value="false">
<input type="hidden" id="hRemark" name="hRemark" value="false">
<input type="hidden" id="prefixStatus" name="prefixStatus" value="">
<input type="checkbox" name="approveResultAllNo" value="true" style="display:none;" />

<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<%
if(null!=obj.getPrefixNos()) {
	for(String p : obj.getPrefixNos()) {
		out.write("<input type='hidden' name='prefixNos' value='"+p+"' />");
	}
}
%>
</form>
<form id="form2" name="form2" method="post">
<input type="hidden" name="hiddenPrefixNos" value="<%=obj.getHiddenPrefixNos()%>">
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>