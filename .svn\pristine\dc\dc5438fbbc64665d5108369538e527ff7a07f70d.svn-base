package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1300;

public class Eedb1300Dao extends BaseDaoJdbc implements RowMapper<Eedb1300> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB_V_1300 WHERE TELIX_NO = ?";
	public Eedb1300 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb1300> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
		return list.isEmpty() ? null : list.get(0);
	}
	
	public Eedb1300 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb1300 obj = null;
		if(null!=rs) {
			obj = new Eedb1300();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setPayType(rs.getString("PAY_TYPE"));
			obj.setAmount(rs.getString("AMOUNT"));
			obj.setSignCaName(rs.getString("SIGN_CA_NAME"));
			obj.setPayDate(rs.getString("PAY_DATE"));
			obj.setPayTime(rs.getString("PAY_TIME"));
			obj.setBankCode(rs.getString("BANK_CODE"));
			obj.setBankName(rs.getString("BANK_NAME"));
			obj.setCaseCode(rs.getString("CASE_CODE"));
			obj.setCaseShortName(rs.getString("CASE_SHORTNAME"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setReceiptDate(rs.getString("RECEIPT_DATE"));
			obj.setReceiptNo(rs.getString("RECEIPT_NO"));
			obj.setReceiptTitle(rs.getString("RECEIPT_TITLE"));
			obj.setRegCost(rs.getString("REG_COST"));
			obj.setScriptCost(rs.getString("SCRIPT_COST"));
			obj.setManCost(rs.getString("MAN_COST"));
			obj.setExamineCost(rs.getString("EXAMINE_COST"));
			obj.setTransDate(rs.getString("TRANS_DATE"));
			obj.setReturnDate(rs.getString("RETURN_DATE"));
			obj.setReturnAmount(rs.getString("RETURN_AMOUNT"));
			obj.setAccountDate(rs.getString("ACCOUNT_DATE"));
		}
		return obj;
	}
}
