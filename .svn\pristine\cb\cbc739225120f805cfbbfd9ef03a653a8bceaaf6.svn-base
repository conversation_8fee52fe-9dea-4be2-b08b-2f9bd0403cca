package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.service.CorrectDataService;

public class CorrectDataServiceImpl implements CorrectDataService {
	
	private Logger logger = Logger.getLogger(this.getClass());

	public void doCorrectReserveDate() {
		if(logger.isInfoEnabled()) logger.info("[doCorrectReserveDate][Start]");
		List<Cedb1000> datas = cedb1000Dao.findForCorrectReserveDate();
		if(null!=datas && !datas.isEmpty()) {
			String closeDate = null;
			Integer reserveDays = null;
			String reserveMark = null;
			String reserveDate = null;
			String prefixNo = null;
			for(Cedb1000 data : datas) {
				prefixNo = data.getPrefixNo();
				closeDate = data.getCloseDate();
				reserveDays = data.getReserveDays();
				reserveMark = data.getReserveMark();
				reserveDate = data.getReserveDate();

				if(logger.isInfoEnabled()) logger.info("[Correct][PrefixNo:"+prefixNo+"]");
				if(logger.isInfoEnabled()) logger.info("[Correct][CloseDate:"+closeDate+"]");
				if(logger.isInfoEnabled()) logger.info("[Correct][ReserveDate:"+reserveDate+"][before]");
				reserveDate = ServiceGetter.getInstance().getPrefixService().countReserveDate(closeDate, reserveDays, reserveMark);
				if(logger.isInfoEnabled()) logger.info("[Correct][ReserveDate:"+reserveDate+"][after]");
				//備份
				ServiceGetter.getInstance().getBackupService().doBackup(prefixNo, "osss");
				//更新
				cedb1000Dao.updateReserveDate(prefixNo, reserveDate);
			}
		}
		if(logger.isInfoEnabled()) logger.info("[doCorrectReserveDate][End]");
	}

	private Cedb1000Dao cedb1000Dao;
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

}