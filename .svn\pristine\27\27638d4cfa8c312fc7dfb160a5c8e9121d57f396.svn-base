<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8014">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8014" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
	objList = (java.util.ArrayList) obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
		}
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function queryOne(code,name,updateDate,updateUser) {
	//$('#q_code').val(code);
	//$('#q_name').val(name);
	//$('#q_updateDate').val(updateDate);
	//$('#q_updateUser').val(updateUser);
}
</script>
</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');" >
<form id="form1" name="form1" method="post" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8014'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" style="width:100%;height:auto;">  
		<tr>
			<td class="td_form" width="15%">國家代碼：</td>
			<td class="td_form_white" width="85%"> 
				<input class="field_Q" type="text" id="q_code" name="q_code" size="30" maxlength="30" value="<%=obj.getQ_code()%>" />
			</td>
		</tr>
		<tr>
			<td class="td_form" width="15%">國家名稱：</td>
			<td class="td_form_white" width="85%"> 
				<input class="field_Q" type="text" id="q_name" name="q_name" size="30" maxlength="30" value="<%=obj.getQ_name()%>" />
				<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
<!-- FORM AREA -->

<% if ("queryAllSuccess".equals(obj.getState())) { %>
<!-- LIST AREA -->
<tr><td nowrap class="bgList">
	<div id="listContainer" style="height:auto">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
		<thead id="listTHEAD">
			<tr>    
				<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">國家代碼</a></th>
				<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">中文名稱</a></th>
				<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">異動日期</a></th>
				<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">異動人員</a></th>
			</tr>
		</thead>
		<tbody id="listTBODY">
		<%
			boolean primaryArray[] = {true,true,true,true};
			boolean displayArray[] = {true,true,true,true};
			String[] alignArray = {"center","center","center","center"};
			out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
		%>
		</tbody>
	</table>
	</div>
</td></tr>
<!-- LIST AREA -->
<% } %>

<!-- TOOLBAR AREA -->
<tr><td>
	<input type="hidden" id="code" name="code" value="<%=obj.getCode()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>