package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 參數代碼檔(SYSTEM_CODE)
 *
 */
public class SystemCode extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
    /** 代碼類別 */
    private String codeKind;
    /** 代碼 */
    private String code;
    /** 代碼名稱 */
    private String codeName;
    /** 代碼說明 */
    private String codeDesc;
    /** 代碼參數1 */
    private String codeParam1;
    /** 代碼參數2 */
    private String codeParam2;
    /** 代碼參數3 */
    private String codeParam3;
    /** 備註 */
    private String remark;
	/** 排序 */
	private Integer sorted;
	/** 是否啟用(Y:啟動,N:停用 ) */
	private String enable;
	/** 異動人員 */
	private String modIdNo;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;

	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}

    /** 代碼類別 */
    public String getCodeKind() {return codeKind;}
    /** 代碼類別 */
	public void setCodeKind(String codeKind) {this.codeKind = codeKind;}

	/** 代碼 */
	public String getCode() {return code;}
    /** 代碼 */
	public void setCode(String code) {this.code = code;}

    /** 代碼名稱 */
	public String getCodeName() {return codeName;}
    /** 代碼名稱 */
	public void setCodeName(String codeName) {this.codeName = codeName;}

    /** 代碼說明 */
	public String getCodeDesc() {return codeDesc;}
    /** 代碼說明 */
	public void setCodeDesc(String codeDesc) {this.codeDesc = codeDesc;}

    /** 備註 */
	public String getRemark() {return remark;}
    /** 備註 */
	public void setRemark(String remark) {this.remark = remark;}

	/** 排序 */
	public Integer getSorted() {return sorted;}
	/** 排序 */
	public void setSorted(Integer sorted) {this.sorted = sorted;}

	/** 是否啟用(Y:啟動,N:停用 ) */
	public String getEnable() {return enable;}
	/** 是否啟用(Y:啟動,N:停用 ) */
	public void setEnable(String enable) {this.enable = enable;}

    /** 代碼參數1 */
	public String getCodeParam1() {return codeParam1;}
    /** 代碼參數1 */
	public void setCodeParam1(String codeParam1) {this.codeParam1 = codeParam1;}

    /** 代碼參數2 */
	public String getCodeParam2() {return codeParam2;}
    /** 代碼參數2 */
	public void setCodeParam2(String codeParam2) {this.codeParam2 = codeParam2;}
	
    /** 代碼參數3 */
	public String getCodeParam3() {return codeParam3;}
    /** 代碼參數3 */
	public void setCodeParam3(String codeParam3) {this.codeParam3 = codeParam3;}
	
	/** 異動人員 */
	public String getModIdNo() {return modIdNo;}
	/** 異動人員 */
	public void setModIdNo(String modIdNo) {this.modIdNo = modIdNo;}

	/** 異動日期 */
	public String getModDate() {return modDate;}
	/** 異動日期 */
	public void setModDate(String modDate) {this.modDate = modDate;}

	/** 異動時間 */
	public String getModTime() {return modTime;}
	/** 異動時間 */
	public void setModTime(String modTime) {this.modTime = modTime;}

}