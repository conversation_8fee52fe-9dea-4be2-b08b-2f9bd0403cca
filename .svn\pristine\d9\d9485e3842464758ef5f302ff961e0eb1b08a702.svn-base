package com.kangdainfo.tcfi.loader;

/**
 * 郵寄類別
 *
 */
public class SystemCode07Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_07";
	private static final String CODE_KIND = "07";//07:郵寄類別
	//singleton
	private static SystemCode07Loader instance;
	public SystemCode07Loader() {
		if (SystemCode07Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode07Loader.instance);
		}
		SystemCode07Loader.instance = this;
	}
	public static SystemCode07Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}