package com.kangdainfo.web.filter;

import java.io.IOException;
import java.util.Enumeration;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.owasp.esapi.ESAPI;
import tw.gov.moea.aa.filter.MoeaAAClientFilter;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.web.util.WebConstants;
import com.kangdainfo.web.util.WebProperties;

/**
 * the AuthenticateFilter
 * 
 * <AUTHOR>
 */
public class AuthenticateFilter extends MoeaAAClientFilter implements Filter {

	public interface KEY {
		public String REQUEST_STARTTIME = "request_start_time";
	}
	
	private Logger logger = Logger.getLogger(this.getClass());

	//public void init(FilterConfig filterConfig) throws ServletException {
	//	this.filterConfig = filterConfig;
	//}
	
	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
			FilterChain chain) throws IOException, ServletException {
		HttpSession session = null;
		HttpServletRequest myRequest = null;
		HttpServletResponse myResponse = null;

		if (request instanceof HttpServletRequest) {
			myRequest = (HttpServletRequest) request;
			session = myRequest.getSession();
		}
		if (response instanceof HttpServletResponse) {
			myResponse = (HttpServletResponse) response;
		}
		
		logRequest(myRequest);

		if (session != null) {
			String isSSO = "";
			//找session
			if("".equals(Common.get(isSSO))) isSSO = (String) session.getAttribute(WebConstants.LOGINKEY_IS_SSO);
			//找request
			if("".equals(Common.get(isSSO))) isSSO = (String) request.getParameter("isSSO");

			if(!"N".equals(isSSO)){
				//使用憑證登入，先進MoeaAAClientFilter驗證取SSO資料
				super.doFilter(myRequest, myResponse, chain);
				//check login, CommonUser
				boolean isPass = ServiceGetter.getInstance().getAuthenticationService().authenticate(myRequest);
				if(isPass){
					//int authenticationStatus = authenticate(myRequest, myResponse);
					//boolean authenticated = authenticationStatus==WebConstants.AuthenticationStatus.ALREADY_AUTHENTICATED ||
					//		authenticationStatus==WebConstants.AuthenticationStatus.AUTHENTICATION_PASSED;
					putMdc(myRequest.getSession());
				} else {
					//清除Session
					clearHttpSession(myRequest.getSession());
					//導向 no_session.jsp
					authenticateFail(myRequest,myResponse);
					return;
				}
			}else{
				//使用帳密登入
				// authenticate
				int authenticationStatus = authenticate(myRequest, myResponse);
				//if(authenticationStatus==WebConstants.AuthenticationStatus.AUTHENTICATION_PASSED) {
				//	authenticationPassed(myRequest,myResponse);
				//}
				boolean authenticated = authenticationStatus==WebConstants.AuthenticationStatus.ALREADY_AUTHENTICATED ||
											authenticationStatus==WebConstants.AuthenticationStatus.AUTHENTICATION_PASSED;
				
				if (authenticated) {
					putMdc(session);
					chain.doFilter(request, response);
					//removeMdc(request);
				} else {
					//清除Session
					clearHttpSession(session);
					//導向 no_session.jsp
					authenticateFail(myRequest,myResponse);
					return;
				}
			}
		} else {
			//清除Session
			clearHttpSession(session);
			//導向 no_session.jsp
			authenticateFail(myRequest,myResponse);
			return;
		}
	}

	private int authenticate(HttpServletRequest request, HttpServletResponse response) {
		int result = com.kangdainfo.ServiceGetter.getInstance().getAuthenticationService().checkAuthentication(request, response);
		return result;
	}

	protected void authenticationPassed(HttpServletRequest request, HttpServletResponse response) {
		
	}

	public void putMdc(HttpSession session) {
		if(session!=null) {				
			CommonUser user = (CommonUser) session.getAttribute(WebConstants.SESSION_CURRENT_USER);
			if(user!=null) {
				MDC.put(WebConstants.SESSION_CURRENT_USER, user);				
			}
		}
	}

	public void removeMdc(ServletRequest request) {
		//MDC.remove(WebConstants.SessionKeys.CURRENT_USER);	
		/*
		if (request instanceof HttpServletRequest) {
			HttpServletRequest httpRequest = (HttpServletRequest) request;
			HttpSession session = httpRequest.getSession();
			if(session!=null) {		
				MDC.remove(WebConstants.SessionKeys.CURRENT_USER);				
			}
		}
		*/	
	}

	/**
	 * 清除SESSION
	 * @param session
	 */
	private void clearHttpSession(HttpSession session) {
		if(null!=session) session.invalidate();
	}

	/**
	 * 驗證失敗時，導向 首頁
	 */
	private void authenticateFail(HttpServletRequest request, HttpServletResponse response) throws IOException {
		try {
			ESAPI.httpUtilities().sendRedirect(response,request.getContextPath());
		} catch (Exception e) {
			//e.printStackTrace();
			//logger.error(e.getMessage(), e);				    
		}
	}

	protected void logRequest(HttpServletRequest req) {
		Enumeration<?> headerNames = req.getHeaderNames();
		ToStringBuilder toStringBuilder = new ToStringBuilder("headers");
		while (headerNames.hasMoreElements()) {
			String headerName = (String) headerNames.nextElement();
			String headerValue = req.getHeader(headerName);
			toStringBuilder.append(headerName, headerValue);
		}
		toStringBuilder.append("localAddr", req.getLocalAddr());
		toStringBuilder.append("localName", req.getLocalName());
		toStringBuilder.append("localPort", req.getLocalPort());
		toStringBuilder.append("remoteAddr", req.getRemoteAddr());
		toStringBuilder.append("remoteHost", req.getRemoteHost());
		toStringBuilder.append("remotePort", req.getRemotePort());
		logger.debug(toStringBuilder.toString());
		logger.debug(WebProperties.getRequestParameters(req).toString());
	}

	public void destroy() {
		this.filterConfig = null;
	}

	private FilterConfig filterConfig = null;

	public FilterConfig getFilterConfig() {return filterConfig;}
	public void setFilterConfig(FilterConfig filterConfig) {this.filterConfig = filterConfig;}

}