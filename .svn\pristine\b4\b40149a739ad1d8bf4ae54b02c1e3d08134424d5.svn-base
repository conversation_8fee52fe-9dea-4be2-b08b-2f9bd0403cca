<!-- 
程式目的：每月申請案件統計表
程式代號：pre1009
程式日期：1030530
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->

<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE1009" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1009">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
if ( "print".equals(obj.getState()) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report) {
		//輸出 檔案 到client端
		session.setAttribute(com.kangdainfo.tcfi.view.pre.PRE1009.MSG_KEY, "請等候報表輸出...");
		obj.outputFile(response, report, "PRE1009.pdf");
		session.setAttribute(com.kangdainfo.tcfi.view.pre.PRE1009.MSG_KEY, "執行成功");
		out.clear();
		out = pageContext.pushBody();
	} else {
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_yearMonth,"月份");
	alertStr += checkYYYMM(form1.q_yearMonth,"");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				if(checkField()) {
					$('#state').val("print") ;
					var ran = 'PRE1009_'+randomUUID().replace(/\-/g,"");
					window.open("",ran);
					form1.target = ran;
					form1.submit();
					// form1.target = '';
					document.getElementById("ERRMSG").innerHTML = '請等候報表輸出...';
					setTimeout("checkProcessStatus()", 3000);//等3秒
				}
				break;
			case "doClear":
				form1.q_yearMonth.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function checkProcessStatus() {
	var msg = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre1009Msg.jsp', "");
	if( msg == '請等候報表輸出...' ) {
		setTimeout("checkProcessStatus()", 3000);//等3秒
	}
	document.getElementById("ERRMSG").innerHTML = msg;
}

function keyDown() {
	if (event.keyCode==13) {
		$("#doPrintPdf").click();
	}
}

</script>
</head>
<body topmargin="5">
<form id="form1" name="form1" method="post" onSubmit="return checkField()">

<!-- BANNER -->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE1009'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- Query Area  -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="120">查詢年月份：</td>
			<td class="td_form_white">
				<input class="field_Q" type="text" name="q_yearMonth" size="10" maxlength="5" value="<%=obj.getQ_yearMonth()%>" onKeyDown="keyDown()">
				<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="whatButtonFireEvent(this.name);" >
				<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name);" >
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>