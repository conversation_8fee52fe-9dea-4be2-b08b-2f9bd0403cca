package com.kangdainfo.tcfi.service;

import java.util.List;

import com.kangdainfo.tcfi.view.pre.PRE1001;

/**
 * 預查申請資料收文
 *
 */
public interface Pre1001Service {

	/** 存檔  */
	public String savePrefixCase(PRE1001 bean, String userId) throws Exception;

	/** 智慧型預查  */
	public String doPreSearch(String prefixNo);
	
	/** 判斷電子收文號是否已收文 */
	public boolean queryTelixNoExist(String telixNo);

	/** 線上審核案件 - 直接核准 */
	public void checkOnlineAudit(String prefixNo, String userId);
	
	/** 查詢 - 線上收文每次收文件數 */
	public Integer findReceiveLimit();
	
	/** 查詢一站式待收案件 */
	public List<String> readOsssCase(int limit);

	/** 更新一站式狀態為已收文  */
	public void saveOsssStatus101(String telixNo, String userId, String prefixNo);

	/** 查詢待收案件資料 */
	public PRE1001 generateOnlineCase(String telixNo);
	
	/** 取號存檔時判斷這個電子收文號是否已開收據 */
	public boolean selectPrefixReceiptNoByTelixNo(String telixNo);
	
	/** 現場收文時憑申請者手上的收據單號搜尋prefixReceiptNo以確認是否確實開立過此收據 */
	public boolean selectPrefixReceiptNoByReceiptNo(String receiptNo);

}