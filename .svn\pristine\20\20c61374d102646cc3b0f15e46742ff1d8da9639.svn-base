<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/transitional.dtd">  
<%--
程式目的：資料查詢-登錄及維護
程式代號：PRE4001
撰寫日期：103.06.11
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
--%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4001">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<html>
<head>
<%-- 使用跳出視窗方式的功能需指定 TITLE --%>
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE4001"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/> 
<SCRIPT type="text/javascript">
//for IE8 Array indexOf
if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = Array.prototype.indexOf || function(obj, start) {
		for (var i = (start || 0), j = this.length; i < j; i++) {
			if (this[i] === obj) {
				return i;
			}
		}
		return -1;
	};
}

var prefixVo;
var prefixNos = "";
$(document).ready(function() {
	//離開
	$('#sc_close').click(function(){
		if($('#functionName').val() == "query" ) {
			form1.action = "pre4001.jsp";
			form1.state.value = "init";
			form1.submit();
		} else {
			window.close();
		}
	});
	//異動內容
	$("#btnHisttoryList").click(function() {
		if($("#banNo").val() != '') {
			closeReturnWindow();
			returnWindow=window.open("pre4001_01.jsp?banNo="+$("#banNo").val(),"pre4001_01","width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0");
		} else {
			alert("查無資料");
		}
	});
	//備註歷史
	$("#btnHistor").click(function(){
		closeReturnWindow();
		returnWindow=window.open('pre4001_02.jsp?prefixNo='+$("#current").val(),'pre4001_02','width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = prefixNos.indexOf($("#current").val());
		var pno = $("#current").val();
		if(++currentIndex >= prefixNos.length) {
			pno = prefixNos[0];
		} else {
			pno = prefixNos[currentIndex];
		}
		$("#current").val(pno);
		enterCurrent();
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = prefixNos.indexOf($("#current").val());
		var pno = $("#current").val();
		if((currentIndex-1) < 0) {
			pno = prefixNos[(prefixNos.length-1)];
		} else {
			pno = prefixNos[--currentIndex];
		}
		$("#current").val(pno);
		enterCurrent();
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		var pno = prefixNos[prefixNos.length - 1];
		$("#current").val(pno);
		enterCurrent();
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		var pno = prefixNos[0];
		$("#current").val(pno);
		enterCurrent();
	});
	//上層頁籤
	$("#tabs").tabs();
	//下層頁籤
	$("#tabs2").tabs();

	$("#current").keypress(function(e){
		$.blockUI({message:'<h1> 資料載入中，請稍後  <img src="/prefix/images/jquery/busy.gif" /></h1>',overlayCSS:{backgroundColor:'#F3EFEF'}});
		$("#tabs2-1").click();
		$("#tabs1").click();
		clearUI();
		showMsgBar("&nbsp;");
		if(e.which == 13) {
			$.post( getVirtualPath() + "tcfi/pre/pre4001_03.jsp?q="+$("#current").val(),function(data) {
				if(!data) {
					prefixVo = null;
					showMsgBar("查無資料!");
					return;
				}
				prefixVo = data;

				$('#currPrefixNo').html(data.prefixNo);
				
				var applyWayColor = '#008000';
				if( ("一維條碼"==data.applyWay) || ("紙本送件"==data.applyWay) ) {
					applyWayColor = '#FF0000';
				}

				$('#prefixNo').html(data.prefixNo+"&nbsp;&nbsp;<font color='"+applyWayColor+"'>"+data.applyWay+"</font>");
				if("Y"==data.setup) $('#setup').attr("checked",true);
				else $('#setup').removeAttr("checked");
				if("Y"==data.changeName) $('#changeName').attr("checked",true);
				else $('#changeName').removeAttr("checked");
				if("Y"==data.changeItem) $('#changeItem').attr("checked",true);
				else $('#changeItem').removeAttr("checked");
				$('#closed').val(data.closed);
				if ($("#closed").val() == 'Y') {
					$("#textClosed").html("閉鎖性股份有限公司");	
				} else {
					$("#textClosed").html("");
				}

				$('#telixNo').html(data.telixNo);
				$('#banNo').val(data.banNo);
				if("1"==data.getKind) {//2024/03/17 新增線上列印
					$('input[name="getKind"][value="1"]').attr("checked",true);//2024/03/17 新增線上列印
					$('input[name="getKind"][value="2"]').removeAttr("checked");//2024/03/17 新增線上列印
					$('input[name="getKind"][value="3"]').removeAttr("checked");//2024/03/17 新增線上列印
				} else if("2"==data.getKind) {
					$('input[name="getKind"][value="2"]').attr("checked",true);//2024/03/17 新增線上列印
					$('input[name="getKind"][value="1"]').removeAttr("checked");//2024/03/17 新增線上列印
					$('input[name="getKind"][value="3"]').removeAttr("checked");//2024/03/17 新增線上列印
				} else if("3"==data.getKind) {
					$('input[name="getKind"][value="3"]').attr("checked",true);//2024/03/17 新增線上列印
					$('input[name="getKind"][value="1"]').removeAttr("checked");//2024/03/17 新增線上列印
					$('input[name="getKind"][value="2"]').removeAttr("checked");//2024/03/17 新增線上列印
				}else {
					$('input[name="getKind"][value="1"]').removeAttr("checked");//2024/03/17 新增線上列印
					$('input[name="getKind"][value="2"]').removeAttr("checked");//2024/03/17 新增線上列印
					$('input[name="getKind"][value="3"]').removeAttr("checked");//2024/03/17 新增線上列印
				}
				$('#getKindDesc').html(data.getKindDesc);
				$('#specialName').html(data.specialName);
				$('#companyName').html(data.companyName+"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b><font color='#FF0000'>"+data.reserveTip+"</font></b>");
				$('#staffName').html(data.staffName);
				$('#lastCompanyName').html(data.lastCompanyName);
				$('#updateName').val(data.updateName);
				$("#remark1").val(data.remark1);
				$('#remark').html(commonUtils.trimUndefined(data.remark)+"&nbsp;&nbsp;(檢還,撤件專用)");
				$('#mainFileCompanyName').html(data.mainFileCompanyName);
				$('#approveResultDesc').html(data.approveResultDesc);
				$('#prefixStatusDesc').html(data.prefixStatusDesc);
				$("#reserveDate").val(data.reserveDate);
				if(180==data.reserveDays) {
					$('input[name="reserveDays"][value="180"]').attr("checked",true);
					$('input[name="reserveDays"][value="365"]').removeAttr("checked");
				} else if(365==data.reserveDays) {
					$('input[name="reserveDays"][value="180"]').removeAttr("checked");
					$('input[name="reserveDays"][value="365"]').attr("checked",true);
				} else {
					$('input[name="reserveDays"][value="180"]').removeAttr("checked");
					$('input[name="reserveDays"][value="365"]').removeAttr("checked");
				}
				if("Y"==data.reserveMark) $('#reserveMark').attr("checked",true);
				else $('#reserveMark').removeAttr("checked");
				$('#applyName').html(data.applyName);
				$('#applyId').html(data.applyId);
				$('#applyTel').html(data.applyTel);
				$('#applyLawName').html(data.applyLawName);
				$('#applyBanNo').html(data.applyBanNo);
				$('#applyAddr').html(data.applyAddr);
				$('#attorName').html(data.attorName);
				$('#attorNo').html(data.attorNo);
				$('#attorTel').html(data.attorTel);
				$('#attorAddr').html(data.attorAddr);
				$("#receiveName").html(data.receiveName);
				$("#contactCel").html(data.contactCel);
				$("#sendDateTime").html(data.sendDateTime);
				$("#receiveAddr").html(data.receiveAddr);
				$('#receiveDateTime').html(data.receiveDateTime);//收件日期時間
				$('#receiveKeyinDateTime').html(data.receiveKeyinDateTime);//收文登打日期時間
				$('#assignDateTime').html(data.assignDateTime);//分文日期時間
				$('#approveDateTime').html(data.approveDateTime);//審核日期時間
				$('#issueKeyinDateTime').html(data.issueKeyinDateTime);//發文登打日期時間
				$('#closeDateTime').html(data.closeDateTime);//發文日期時間
				$('#getDateTime').html(data.getDateTime);//領件日期時間
				$('#extRemitEname').html(((data.extRemitEname==undefined) ? "" : data.extRemitEname) + "&nbsp;&nbsp;(僅提供銀行開戶使用)");

				$('#companyName01').html(data.companyName01);
				$('#approveResult01').attr("checked",("Y"==data.approveResult01));
				$('#remark01').html(data.remark01);
				$('#showSame01').html("<pre>"+data.showSame01+"</pre>");
				$('#showSame01').unbind('click').click(function() {
					if(""!=data.showSame01) {
						loadCedb1004s("01", data.cedb1004s_1);
						$("#showSameList01").dialog({title:'同名組織詳細清單',height:280,width:'auto'});
					}
				});
				$('#companyName02').html(data.companyName02);
				$('#approveResult02').attr("checked",("Y"==data.approveResult02));
				$('#remark02').html(data.remark02);
				$('#showSame02').html("<pre>"+data.showSame02+"</pre>");
				$('#showSame02').unbind('click').click(function() {
					if(""!=data.showSame02) {
						loadCedb1004s("02", data.cedb1004s_2);
						$("#showSameList02").dialog({title:'同名組織詳細清單',height:280,width:'auto'});
					}
				});
				$('#companyName03').html(data.companyName03);
				$('#approveResult03').attr("checked",("Y"==data.approveResult03));
				$('#remark03').html(data.remark03);
				$('#showSame03').html("<pre>"+data.showSame03+"</pre>");
				$('#showSame03').unbind('click').click(function() {
					if(""!=data.showSame03) {
						loadCedb1004s("03", data.cedb1004s_3);
						$("#showSameList03").dialog({title:'同名組織詳細清單',height:280,width:'auto'});
					}
				});
				$('#companyName04').html(data.companyName04);
				$('#approveResult04').attr("checked",("Y"==data.approveResult04));
				$('#remark04').html(data.remark04);
				$('#showSame04').html("<pre>"+data.showSame04+"</pre>");
				$('#showSame04').unbind('click').click(function() {
					if(""!=data.showSame04) {
						loadCedb1004s("04", data.cedb1004s_4);
						$("#showSameList04").dialog({title:'同名組織詳細清單',height:280,width:'auto'});
					}
				});
				$('#companyName05').html(data.companyName05);
				$('#approveResult05').attr("checked",("Y"==data.approveResult05));
				$('#remark05').html(data.remark05);
				$('#showSame05').html("<pre>"+data.showSame05+"</pre>");
				$('#showSame05').unbind('click').click(function() {
					if(""!=data.showSame05) {
						loadCedb1004s("05", data.cedb1004s_5);
						$("#showSameList05").dialog({title:'同名組織詳細清單',height:280,width:'auto'});
					}
				});
				$.ajax({
				    type: 'GET',
				    url: getVirtualPath() + "tcfi/ajax/jsonPreSearch.jsp?q=" + data.prefixNo,
				    async: false
				}).done(function (res) {
					let resultsObj = (new DOMParser()).parseFromString(res, "text/xml");
					if (resultsObj.getElementsByTagName("errorcode")[0] == null) {	
						let results = resultsObj.getElementsByTagName("results")[0].getElementsByTagName("result");
						for(let i = 0; i < results.length; i++){
							let result = results[i];
							$('#showPreSearch'+ result.getAttribute("SeqNO")).html(result.getElementsByTagName("examineDescr")[0].textContent);
						}
					}
				});
				showMsgBar("查詢成功!");
				$.unblockUI();
			});
	    }
		setTimeout(function(){$.unblockUI();},2000);
	});
	//畫面初始
	var prefixNo = $('input[name="prefixNos"]').map(function() {
		return $(this).val();
	}).get().join('-');
	prefixNos = prefixNo.match(/[^-]+/g) || [];
	$("#current").val(prefixNos[0]);
	//觸發Enter
	enterCurrent();
});

function enterCurrent() {
	var e = jQuery.Event("keypress");
	e.which = 13;
	jQuery('#current').trigger(e);
}

//營業項目
function loadCedb1002s() {
	var datas = prefixVo.cedb1002s;
	//clear
	$("#cedb1002s > tbody").html("");
	//add
	for(var j=0; j<datas.length; j++) {
		addCedb1002((j%2==0)?"listTREven":"listTROdd",
				commonUtils.trimUndefined(datas[j].seqNo),
				commonUtils.trimUndefined(datas[j].busiItemNo),
				commonUtils.trimUndefined(datas[j].busiItem) );
	}
}
//營業項目
function addCedb1002(tr_class, seqNo, busiItemNo, busiItem) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItemNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItem +'</td>';
	txt += '</tr>';
	$("#cedb1002s tbody").append(txt);
}
//案件流程
function loadCedb1010s() {
	var datas = prefixVo.cedb1010s;
	//clear
	$("#cedb1010s > tbody").html("");
	//add
	for(var k=0; k<datas.length; k++) {
		addCedb1010((k%2==0)?"listTREven":"listTROdd",(k+1),datas[k].processStatus,datas[k].processDate,datas[k].idNo,datas[k].workDay);
	}
}
//案件流程
function addCedb1010(tr_class, seqNo, processStatus, processDate, idNo, workDay) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ processStatus +'</td>';
	txt += '<td style="text-align:left">'+ processDate +'</td>';
	txt += '<td style="text-align:left">'+ idNo +'</td>';
	txt += '<td style="text-align:left">'+ workDay +'</td>';
	txt += '</tr>';
	$("#cedb1010s tbody").append(txt);
}

function clearUI() {
	$('#currPrefixNo').html("&nbsp;");
	$('#prefixNo').html("&nbsp;");
	$('#setup').removeAttr("checked");
	$('#changeName').removeAttr("checked");
	$('#changeItem').removeAttr("checked");
	$('#telixNo').html("&nbsp;");
	$('#banNo').val("");
	$('input[name="getKind"][value="1"]').removeAttr("checked");
	$('input[name="getKind"][value="2"]').removeAttr("checked");
	$('input[name="getKind"][value="3"]').removeAttr("checked");
	$('#getKindDesc').html("&nbsp;");
	$('#specialName').html("&nbsp;");
	$('#companyName').html("&nbsp;");
	$('#staffName').html("&nbsp;");
	$('#lastCompanyName').html("&nbsp;");
	$('#updateName').val("");
	$("#remark1").val("");
	$('#remark').html(""+"&nbsp;&nbsp;(檢還,撤件專用)");
	$('#mainFileCompanyName').html("&nbsp;");
	$('#approveResultDesc').html("&nbsp;");
	$('#prefixStatusDesc').html("&nbsp;");
	$("#reserveDate").val("");
	$('input[name="reserveDays"][value="180"]').removeAttr("checked");
	$('input[name="reserveDays"][value="365"]').removeAttr("checked");
	$('#reserveMark').removeAttr("checked");
	$('#applyName').html("&nbsp;");
	$('#applyId').html("&nbsp;");
	$('#applyTel').html("&nbsp;");
	$('#applyLawName').html("&nbsp;");
	$('#applyBanNo').html("&nbsp;");
	$('#applyAddr').html("&nbsp;");
	$('#attorName').html("&nbsp;");
	$('#attorNo').html("&nbsp;");
	$('#attorTel').html("&nbsp;");
	$('#attorAddr').html("&nbsp;");
	$("#receiveName").html("&nbsp;");
	$("#contactCel").html("&nbsp;");
	$("#sendDateTime").html("&nbsp;");
	$("#receiveAddr").html("&nbsp;");
	$('#receiveDateTime').html("&nbsp;");
	$('#receiveKeyinDateTime').html("&nbsp;");
	$('#assignDateTime').html("&nbsp;");
	$('#approveDateTime').html("&nbsp;");
	$('#issueKeyinDateTime').html("&nbsp;");
	$('#closeDateTime').html("&nbsp;");
	$('#getDateTime').html("&nbsp;");
	$('#extRemitEname').html("&nbsp;(僅提供銀行開戶使用)");
	$('#companyName01').html("&nbsp;");
	$('#approveResult01').attr("checked",false);
	$('#remark01').html("&nbsp;");
	$('#showSame01').html("&nbsp;");
	$('#showSame01').unbind('click');
	$('#companyName02').html("&nbsp;");
	$('#approveResult02').attr("checked",false);
	$('#remark02').html("&nbsp;");
	$('#showSame02').html("&nbsp;");
	$('#showSame02').unbind('click');
	$('#companyName03').html("&nbsp;");
	$('#approveResult03').attr("checked",false);
	$('#remark03').html("&nbsp;");
	$('#showSame03').html("&nbsp;");
	$('#showSame03').unbind('click');
	$('#companyName04').html("&nbsp;");
	$('#approveResult04').attr("checked",false);
	$('#remark04').html("&nbsp;");
	$('#showSame04').html("&nbsp;");
	$('#showSame04').unbind('click');
	$('#companyName05').html("&nbsp;");
	$('#approveResult05').attr("checked",false);
	$('#remark05').html("&nbsp;");
	$('#showSame05').html("&nbsp;");
	$('#showSame05').unbind('click');
}

//同名公司
function loadCedb1004s(seqNo, datas) {
	//clear
	$("#showSameList"+seqNo+" tbody").html("");
	var txt = "";
	var prefixNo = "";
	var banNo = "";
	for(var j=0; j<datas.length; j++) {
		prefixNo = commonUtils.trimUndefined(datas[j].samePrefixNo);
		banNo = commonUtils.trimUndefined(datas[j].sameBanNo) ;

		txt += "<tr>";
		txt += "<td align='center'>"+commonUtils.trimUndefined(datas[j].sameSeqNo)+"</td>";
		txt += "<td><a href='#' onclick='queryCase(\""+prefixNo+"\", \"prefixNo\")'>" + prefixNo +"</a></td>";
		txt += "<td><a href='#' onclick='queryCase(\""+banNo+"\", \"banNo\")'>" + banNo + "</a></td>";
		txt += "<td>" + commonUtils.trimUndefined(datas[j].sameCompanyName) + "</td>";
		txt += "<td>" + commonUtils.trimUndefined(datas[j].cmpyStatus) + "</td>";
		txt += "<td>" + commonUtils.trimUndefined(datas[j].applyName) + "</td>";
		txt += "<td>" + commonUtils.trimUndefined(datas[j].reserveDate) +"</td>";
		txt += "<td>" + commonUtils.trimUndefined(datas[j].revokeAppDate) + "</td>";
		txt += "</tr>";
	}
	//append
	$("#showSameList"+seqNo+" tbody").append(txt);
}

function queryCase(valueNo, searchType) {
	if(valueNo == "" || searchType == "")	return;
	var prop="";
	var width = 1024;
	var height = 748;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=1,resizable=1";
	var url = "";
	if(searchType == "prefixNo")
		url = getVirtualPath() + "tcfi/pre/pre4001_00.jsp?prefixNos="+valueNo;
	else if(searchType == "banNo")
		url = getVirtualPath() + "tcfi/pre/pre3008_00.jsp?banNos="+valueNo;
	else 	return;
	window.open(url,'queryCase',prop);
}

</SCRIPT>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE4001'/>
</c:import>

<table id="shareBar" width="100%" border="0" cellpadding="2" cellspacing="0">
	<tr>
		<td style="text-align: right;padding-right:15px;">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value='PRE4001,PRE3002,PRE3008,PRE3013'/>
				<c:param name="shortcut" value='N'/>
			</c:import>
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" />
		</td>
	</tr>
</table>

<table class="title_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<td class="title_form_label" width="70px" >預查編號</td>
		<td class="title_form_value" width="200px" id="prefixNo">&nbsp;</td>
		<td class="title_form_label" width="70px">預查種類</td>
		<td class="title_form_value" width="450px">
			<input type="checkbox" id="setup" name="setup" value="true" disabled />設立
			<input type="checkbox" id="changeName" name="changeName" value="true" disabled />名稱變更
			<input type="checkbox" id="changeItem" name="changeItem" value="true" disabled />所營變更
			<input type="hidden" id="closed" name="closed" value="">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<span id="textClosed" style="color:#FF0000;font-weight:bold;"></span>
		</td>
		<td class="title_form_label" width="80px">電子流水號</td>
		<td class="title_form_value" id="telixNo">&nbsp;</td>
	</tr>
</table>
<table class="title_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<td class="title_form_label" width="70px" >統一編號</td>
		<td class="title_form_value" width="200px" >
			<input type="text" class="field_RO" id="banNo" name="banNo" size="10" value="" readonly>
		</td>
		<td class="title_form_label" width="70px">領件方式</td>
		<td class="title_form_value" width="450px" >
		    <input type="radio" name="getKind" value="3" disabled />線上列印 <!--2024/03/17 新增線上列印 -->
			<input type="radio" name="getKind" value="1" disabled />自取
			<input type="radio" name="getKind" value="2" disabled />郵寄
			<span id="getKindDesc"></span>
		</td>
		<td class="title_form_value" >
			<table>
				<tr><td>
					<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
				</td>
				<td width="100" align="center" id="currPrefixNo">&nbsp;</td>
				<td>
					<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
 				</td></tr>
			</table>
		</td>
	</tr>
</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1" id="tabs1"><span>案件資料</span></a></li>
		<li><a href="#fragment-2"><span>預查名稱</span></a></li>
		<li><a href="#fragment-3" onclick="loadCedb1002s()"><span>營業項目</span></a></li>
		<li><a href="#fragment-4" onclick="loadCedb1010s()"><span>案件流程</span></a></li>
	</ul>
	<div id="fragment-1" style="padding:0px;">
		<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="6" style="font-size:1px;height:3px;">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="90px">特取名稱</td>
				<td class="td_form_white" width="200px" id="specialName">&nbsp;</td>
				<td class="td_form" width="100px">本次預查名稱</td>
				<td class="td_form_white" colspan="3" id="companyName">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >承辦人</td>
				<td class="td_form_white" id="staffName">&nbsp;</td>
				<td class="td_form" >前次預查名稱</td>
				<td class="td_form_white" colspan="3" id="lastCompanyName">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >前次異動者</td>
				<td class="td_form_white">
					<input type="text" class="field_RO" id="updateName" name="updateName" size="5" value="" readonly>
					<input type="button" class="toolbar_default" id="btnHisttoryList" name="btnHisttoryList" value="異動內容" />
				</td>
				<td class="td_form_white" >&nbsp;</td>
				<td class="td_form_white" width="250px">&nbsp;</td>
				<td class="td_form" width="100px">收件日期</td>
				<td class="td_form_white" id="receiveDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" rowspan="2">
					備註紀錄<br/>
					<input type="button" class="toolbar_default" id="btnHistor" name="btnHistor" value="備註歷史" />
				</td>
				<td class="td_form_white" colspan="3" rowspan="2">
					<textarea class="field_RO" id="remark1" name="remark1" cols="50" rows="3" readonly></textarea>
				</td>
				<td class="td_form">收文登打</td>
				<td class="td_form_white" id="receiveKeyinDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="100px">分文日期</td>
				<td class="td_form_white" id="assignDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">現況<br/>主檔名稱</td>
				<td class="td_form_white" colspan="3" id="mainFileCompanyName">&nbsp;</td>
				<td class="td_form" >審核日期</td>
				<td class="td_form_white" id="approveDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">註記說明</td>
				<td class="td_form_white" colspan="3" id="remark" style="width:550px;word-wrap: break-word;">&nbsp;</td>
				<td class="td_form" >發文登打</td>
				<td class="td_form_white" id="issueKeyinDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">核覆結果</td>
				<td class="td_form_white" id="approveResultDesc">&nbsp;</td>
				<td class="td_form">案件狀態</td>
				<td class="td_form_white" id="prefixStatusDesc">&nbsp;</td>
				<td class="td_form" >發文日期</td>
				<td class="td_form_white" id="closeDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">保留期限</td>
				<td class="td_form_white" colspan="3">
					<input type="text" class="field_RO" id="reserveDate" name="reserveDate" maxlength="10" size="12" value="" readonly>
					<input type="radio" name="reserveDays" value="180" disabled>保留半年
					<input type="radio" name="reserveDays" value="365" disabled>保留一年
					<input type="checkbox" id="reserveMark" name="reserveMark" value="Y" disabled>延長期限一個月
				</td>
				<td class="td_form" >領件日期</td>
				<td class="td_form_white" id="getDateTime">&nbsp;</td>
			</tr>
		</table>
		<div id="tabs2" style="padding:0px;">
			<ul>
				<li><a href="#tabs2-f1" id="tabs2-1"><span>申請人資料</span></a></li>
				<li><a href="#tabs2-f2"><span>代理人資料</span></a></li>
				<li><a href="#tabs2-f3"><span>收件人資料</span></a></li>
				<li><a href="#tabs2-f4"><span>自由填列事項</span></a></li>
			</ul>
			<div id="tabs2-f1" style="padding:0px;">
				<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">申請人姓名</td>
						<td class="td_form_white" width="240px" id="applyName">&nbsp;</td>
						<td class="td_form" width="150px">身分ID</td>
						<td class="td_form_white" width="150px" id="applyId">&nbsp;</td>
						<td class="td_form" width="100px">申請人電話</td>
						<td class="td_form_white" id="applyTel">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">所代表法人</td>
						<td class="td_form_white" id="applyLawName">&nbsp;</td>
						<td class="td_form">法人統編</td>
						<td class="td_form_white" colspan="3" id="applyBanNo">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">申請人地址</td>
						<td class="td_form_white" colspan="5" id="applyAddr">&nbsp;</td>
					</tr>
				</table>
			</div>
			<div id="tabs2-f2" style="padding:0px;">
				<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">代理人姓名</td>
						<td class="td_form_white" width="240px" id="attorName">&nbsp;</td>
						<td class="td_form" width="150px">證書號碼╱身分ID</td>
						<td class="td_form_white" width="150px" id="attorNo">&nbsp;</td>
						<td class="td_form" width="100px">聯絡電話</td>
						<td class="td_form_white" id="attorTel">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" >事務所所在地</td>
						<td class="td_form_white" colspan="5" id="attorAddr">&nbsp;</td>
					</tr>
				</table>
			</div>
			<div id="tabs2-f3" style="padding:0px;">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">收件人姓名</td>
						<td class="td_form_white" width="240px" id="receiveName">&nbsp;</td>
						<td class="td_form" width="150px">簡訊通知回覆電話</td>
						<td class="td_form_white" width="150px" id="contactCel">&nbsp;</td>
						<td class="td_form" width="100px">寄件日期</td>
						<td class="td_form_white" id="sendDateTime">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">聯絡地址</td>
						<td class="td_form_white" colspan="5" id="receiveAddr">&nbsp;</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f4" style="padding:0px;">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form_white" colspan="6">（不納入預查審核項目）</td>
					</tr>
					<tr>
						<td class="td_form" width="180px">國外匯款使用英文名稱</td>
						<td class="td_form_white" colspan="5" id="extRemitEname">&nbsp;</td>
					</tr>
				</TABLE>
			</div>
		</div>
	</div>
	<div id="fragment-2" style="padding:0px;">
		<TABLE id="cedb1001s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" style="text-align: left;width:50px;">序號</td>
				<td class="td_form" style="text-align: left;width:240px;">預查名稱</td>
				<td class="td_form" style="text-align: left;width:70px;">審核結果</td>
				<td class="td_form" style="text-align: left;width:350px;">同名組織/結果</td>
				<td class="td_form" style="text-align: center;width:350px;">同名註記</td>
				<td class="td_form" style="text-align: center;">智慧型預查</td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">01</td>
				<td style="text-align:left" id="companyName01">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult01" name="approveResult01" value="Y" disabled /></td>
				<td style="text-align:left" id="showSame01">&nbsp;</td>
				<td style="text-align:left" id="remark01">&nbsp;</td>
				<td style="text-align:left" id="showPreSearch01">&nbsp;</td>
			</tr>
			<tr class="listTREven">
				<td style="text-align:left">02</td>
				<td style="text-align:left" id="companyName02">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult02" name="approveResult02" value="Y" disabled /></td>
				<td style="text-align:left" id="showSame02">&nbsp;</td>
				<td style="text-align:left" id="remark02">&nbsp;</td>
				<td style="text-align:left" id="showPreSearch02">&nbsp;</td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">03</td>
				<td style="text-align:left" id="companyName03">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult03" name="approveResult03" value="Y" disabled /></td>
				<td style="text-align:left" id="showSame03">&nbsp;</td>
				<td style="text-align:left" id="remark03">&nbsp;</td>
				<td style="text-align:left" id="showPreSearch03">&nbsp;</td>
			</tr>
			<tr class="listTREven">
				<td style="text-align:left">04</td>
				<td style="text-align:left" id="companyName04">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult04" name="approveResult04" value="Y" disabled /></td>
				<td style="text-align:left" id="showSame04">&nbsp;</td>
				<td style="text-align:left" id="remark04">&nbsp;</td>
				<td style="text-align:left" id="showPreSearch04">&nbsp;</td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">05</td>
				<td style="text-align:left" id="companyName05">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult05" name="approveResult05" value="Y" disabled /></td>
				<td style="text-align:left" id="showSame05">&nbsp;</td>
				<td style="text-align:left" id="remark05">&nbsp;</td>
				<td style="text-align:left" id="showPreSearch05">&nbsp;</td>
			</tr>
		</TABLE>
	</div>
	<div id="fragment-3" style="padding:0px;">
		<TABLE id="cedb1002s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">代碼</td>
					<td class="td_form" style="text-align: left">營業項目</td>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</table>
	</div>
	<div id="fragment-4" style="padding:0px;">
		<TABLE id="cedb1010s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">案件處理狀況</td>
					<td class="td_form" style="text-align: left">案件處理時間</td>
					<td class="td_form" style="text-align: left">處理人員</td>
					<td class="td_form" style="text-align: left">工作日數</td>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</TABLE>
	</div>
</div>
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="current" name="current" value="<%=obj.getCurrent()%>" />
<input type="hidden" id="functionName" name="functionName" value="<%=obj.getFunctionName()%>" />

<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<%
if(null!=obj.getPrefixNos()) {
	for(String p : obj.getPrefixNos()) {
out.write("<input type='hidden' name='prefixNos' value='"+p+"' />\n");
	}
}
%>

<div id="showSameList01" style="display: none">
	<table border=1>
		<thead>
			<tr>
				<td class="thead" width="20px">&nbsp;</td>
				<td class="thead" width="80px">預查編號</td>
				<td class="thead" width="80px">統編</td>
				<td class="thead">同名組織名稱</td>
				<td class="thead" width="80px">狀態</td>
				<td class="thead" width="76px">申請人</td>
				<td class="thead" width="80px">保留期限</td>
				<td class="thead" width="80px">解撤廢日</td>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
</div>

<div id="showSameList02" style="display: none">
	<table border=1>
		<thead>
			<tr>
				<td class="thead" width="20px">&nbsp;</td>
				<td class="thead" width="80px">預查編號</td>
				<td class="thead" width="80px">統編</td>
				<td class="thead">同名組織名稱</td>
				<td class="thead" width="80px">狀態</td>
				<td class="thead" width="76px">申請人</td>
				<td class="thead" width="80px">保留期限</td>
				<td class="thead" width="80px">解撤廢日</td>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
</div>

<div id="showSameList03" style="display: none">
	<table border=1>
		<thead>
			<tr>
				<td class="thead" width="20px">&nbsp;</td>
				<td class="thead" width="80px">預查編號</td>
				<td class="thead" width="80px">統編</td>
				<td class="thead">同名組織名稱</td>
				<td class="thead" width="80px">狀態</td>
				<td class="thead" width="76px">申請人</td>
				<td class="thead" width="80px">保留期限</td>
				<td class="thead" width="80px">解撤廢日</td>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
</div>

<div id="showSameList04" style="display: none">
	<table border=1>
		<thead>
			<tr>
				<td class="thead" width="20px">&nbsp;</td>
				<td class="thead" width="80px">預查編號</td>
				<td class="thead" width="80px">統編</td>
				<td class="thead">同名組織名稱</td>
				<td class="thead" width="80px">狀態</td>
				<td class="thead" width="76px">申請人</td>
				<td class="thead" width="80px">保留期限</td>
				<td class="thead" width="80px">解撤廢日</td>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
</div>

<div id="showSameList05" style="display: none">
	<table border=1>
		<thead>
			<tr>
				<td class="thead" width="20px">&nbsp;</td>
				<td class="thead" width="80px">預查編號</td>
				<td class="thead" width="80px">統編</td>
				<td class="thead">同名組織名稱</td>
				<td class="thead" width="80px">狀態</td>
				<td class="thead" width="76px">申請人</td>
				<td class="thead" width="80px">保留期限</td>
				<td class="thead" width="80px">解撤廢日</td>
			</tr>
		</thead>
		<tbody>
		</tbody>
	</table>
</div>

</form>
</body>
</html>