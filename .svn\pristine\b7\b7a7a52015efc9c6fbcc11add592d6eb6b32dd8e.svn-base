<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.math.BigDecimal" %>
<%@ page import="com.kangdainfo.common.util.SQLJob" %>
<%@ page import="com.kangdainfo.ServiceGetter" %>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL("SELECT COUNT(1) AS ONLINE_DOC_NUM");
	sqljob.appendSQL("FROM OSSS.OSSM_APPL_FLOW");
	sqljob.appendSQL("WHERE (TELIX_NO LIKE 'OSC%' OR TELIX_NO LIKE 'OSS%')");
	sqljob.appendSQL("AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I')");
	sqljob.appendSQL("AND PROCESS_STATUS = '003'");
	//System.out.println(sqljob);
	List<Map<String,Object>> datas = ServiceGetter.getInstance().getOsssGeneralQueryDao().queryForList(sqljob);
	if (null!=datas && !datas.isEmpty()) {
		out.write(com.kangdainfo.common.util.Common.get(datas.get(0).get("ONLINE_DOC_NUM")));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>