package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.common.util.SQLJob;

/*
程式目的：已審核未結案清單
程式代號：pre4015
撰寫日期：103.06.23
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE4016 extends SuperBean {	
  
 
	private String q_dateStart;  		// 核覆日期起
	private String q_dateEnd;    		// 核覆日期迄
	private String[] q_approveResult;	// 核覆結果(A;審核中;Y;核准;N;否準)
  
	// ----------------------------------getters and setters of local variable bellow ---------------------------
	public String getQ_dateStart() {return checkGet(q_dateStart);}
	public void setQ_dateStart(String s) {q_dateStart = checkSet(s);}
	public String getQ_dateEnd() {return checkGet(q_dateEnd);}
	public void setQ_dateEnd(String s) {q_dateEnd = checkSet(s);}
	public String[] getQ_approveResult() {return q_approveResult;}
	public void setQ_approveResult(String[] q_approveResult) {this.q_approveResult = q_approveResult;}
	// ----------------------------------------------------------------------------------------------------------
  
	// ----------------------------------function never used bellow----------------------------------------------
	public SQLJob doAppendSqljob() {
		/*
		 * 報表邏輯模糊，與坤宏討論後有2種情況：
		 * 1. 有展期過的案件(EXTEND_MARK = 'Y')，目前從資料面看，即使後續審核發文結案後，EXTEND_MARK還是Y
		 * 2. 現在正處於展期中的案件(CEDB1010流程 3,4,5  狀態中，WORK_DAY = 0.08 且沒有後續 6,7,8 狀態者)
		 * 目前先以第1種做法
		 * */
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" SELECT A.PREFIX_NO, A.RECEIVE_DATE, EXTEND_DATE, A.STAFF_NAME, ");
		sqljob.appendSQL("		decode(a.extend_reason,'90',a.extend_other,c14.code_name) EXTEND_REASON, ");
		sqljob.appendSQL("		(select sum(work_day) from cedb1010 b where b.prefix_no = a.prefix_no and b.process_status in ('3', '4', '5')) WORK_DAY ");
		sqljob.appendSQL(" FROM CEDB1000 A ");
		sqljob.appendSQL("		left outer join system_code c14 on c14.code_kind='14' and c14.code = a.extend_reason ");
		sqljob.appendSQL(" WHERE A.EXTEND_MARK = 'Y' AND ");		//展期備註 = 'Y'
		sqljob.appendSQL("       A.RECEIVE_DATE BETWEEN ? AND ? ");//收文日期
		sqljob.addParameter(getQ_dateStart());
		sqljob.addParameter(getQ_dateEnd());
		//核覆結果
		if(q_approveResult != null && q_approveResult.length > 0){
			sqljob.appendSQL("       AND A.APPROVE_RESULT IN( " );
			for(int i = 0; i < q_approveResult.length; i++){
				if(i > 0)	sqljob.appendSQL(",");
				sqljob.appendSQL(" ? ");
				sqljob.addParameter(q_approveResult[i]);
			}
			sqljob.appendSQL(" ) " );
		}
		sqljob.appendSQL(" ORDER BY A.PREFIX_NO ");
		if(logger.isInfoEnabled()) logger.info(sqljob);	
		return sqljob ;
	} // doAppendSqljob()
	
	public ArrayList<String[]> doQueryAll() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>();
		List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob());
		if ( rs != null && rs.size() > 0 ) {
			int i = 0;
			String[] rowArray = new String[6];
			Map<String,Object> temp = null;
			while( i < rs.size()){
				rowArray = new String[6];
				temp = rs.get(i);	      
				rowArray[0] = Common.get(temp.get("PREFIX_NO"));
				rowArray[1] = Common.formatYYYMMDD(Common.get(temp.get("RECEIVE_DATE")), 4);
				rowArray[2] = Common.formatYYYMMDD(Common.get(temp.get("EXTEND_DATE")), 4);
				rowArray[3] = Common.get(temp.get("STAFF_NAME"));
				rowArray[4] = Common.get(temp.get("WORK_DAY"));
				rowArray[5] = Common.get(temp.get("EXTEND_REASON"));
				dataList.add(rowArray);
				i++;
			} // end while
		}else { 
			this.setErrorMsg( "查無資料，請您重新輸入查詢條件！") ;
		} // end while
		
		return dataList;
	} // doQueryAll()
  
	public void doCreate() throws Exception{} // end doCreate()
	  
	public void doUpdate() throws Exception{} // end doUpdate()		
	  
	public void doDelete() throws Exception{} // end doDelete()	
	  
	public Object doQueryOne() throws Exception{ return null ;} // end doQueryOne() 
  
} // PPE4008()