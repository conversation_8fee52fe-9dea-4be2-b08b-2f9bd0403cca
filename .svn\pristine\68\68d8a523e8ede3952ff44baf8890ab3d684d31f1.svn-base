<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.test.QueryUmsMt">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.test.QueryUmsMt)obj.queryOne();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(umsNo){
	form1.id.value=umsNo;
	form1.umsNo.value=umsNo;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}
function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();showErrorMsg('<%=obj.getErrorMsg()%>');">

<form id="form1" name="form1" method="post" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:300px;height:100px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">預查編號：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_prefixNo" size="15" maxlength="10" value="<%=obj.getQ_prefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getUmsNo()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">訊息編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="umsNo" value="<%=obj.getUmsNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">手機號碼：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="idNo" value="<%=obj.getDstaddr()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">簡訊內容：</td>
		<td class="td_form_white">
			<textarea class="field" rows="5" cols="80" name="smbody"><%=obj.getSmbody()%></textarea>
		</td>
	</tr>
	<tr>
		<td class="td_form">處理時間：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="mtTime" value="<%=obj.getMtTime()%>">
		</td>
	</tr>
	</table>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
	</div>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" href="#">手機號碼</a></th>
		<th class="listTH"><a class="text_link_w" href="#">簡訊內容</a></th>
		<th class="listTH"><a class="text_link_w" href="#">處理時間</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = { true,false,false,false};
	boolean displayArray[] = {false, true, true, true};
	String[] alignArray = {"center","center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>