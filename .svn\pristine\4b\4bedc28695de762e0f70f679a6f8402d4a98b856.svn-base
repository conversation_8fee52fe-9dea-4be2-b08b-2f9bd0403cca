package com.kangdainfo.tcfi.loader;

/**
 * 有限合夥各類代碼對照加載類--組織類型
 * <AUTHOR>
 * 113/04/16
 */
public class LmsdCodemappingOrgLoader extends LmsdCodemappingLoader {
	private static final String CACHE_NAME = "CACHE_NAME_LMSD_CODEMAPPING_ORG";
	private static final String KIND = "ORG";// 組織類型
	
	private static LmsdCodemappingOrgLoader Instance;
	
	public LmsdCodemappingOrgLoader() {
		if(LmsdCodemappingOrgLoader.Instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist: "
					+ LmsdCodemappingOrgLoader.Instance);
		}
		LmsdCodemappingOrgLoader.Instance = this;
	}

	@Override
	protected String getCacheName() {
		return CACHE_NAME;
	}

	@Override
	protected String getKind() {
		return KIND;
	}
}
