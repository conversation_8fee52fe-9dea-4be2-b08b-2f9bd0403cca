package com.kangdainfo.tcfi.util;

/**
 * 系統常數
 */
public abstract class PrefixConstants {

	public static final String MESSAGE_NO_DATA_FOUND	=	"沒有符合查詢條件資料!";
	public static final String MESSAGE_INSERT_SUCCESS	=	"新增成功";
	public static final String MESSAGE_INSERT_FAILURE	=	"新增失敗";
	public static final String MESSAGE_DELETE_SUCCESS	=	"刪除成功";
	public static final String MESSAGE_DELETE_FAILURE	=	"刪除失敗";
	public static final String MESSAGE_UPDATE_SUCCESS	=	"更新成功";
	public static final String MESSAGE_UPDATE_FAILURE	=	"更新失敗";
	public static final String MESSAGE_UPLOAD_SUCCESS	=	"上傳成功";
	public static final String MESSAGE_UPLOAD_FAILURE	=	"上傳失敗";
	public static final String MESSAGE_EXECUTE_SUCCESS	=	"執行成功";
	public static final String MESSAGE_EXECUTE_FAILURE	=	"執行失敗";
	public static final String MESSAGE_PREPRINT_SUCCESS	=	"預覽列印完成!!";
	public static final String MESSAGE_PREPRINT_FAILURE	=	"預覽列印失敗!!";
	public static final String MESSAGE_PRINT_SUCCESS	=	"列印完成!!";
	public static final String MESSAGE_PRINT_FAILURE	=	"列印失敗!!";	

	public static final String SelectALL = "全部";

	/** XML編碼 */
	public static final String XML_ENCODING = "UTF-8";
	/** 網頁編碼 */
	public static final String WEB_ENCODING = "UTF-8";

	/** 系統帳號 */
	public static final String SYS = "SYS";
	
	/** 線上審核 - 專用承辦人代碼 */
	public static final String L0100_ID_NO = "LL";
	/** 線上審核 - 專用承辦人名稱 */
	public static final String L0100_STAFF_NAME = "線上審核";

	/** 保留期限 - 半年(180天) */
	public static final int RESERVE_DAYS_HALF_YEAR = 180;
	/** 保留期限 - 一年(365天) */
	public static final int RESERVE_DAYS_ONE_YEAR = 365;

	/** 檢索類別 - (1)輔助查詢 */
	public static final String SEARCH_TYPE_HELP = "1";
	/** 檢索類別 - (2)全文檢索 */
	public static final String SEARCH_TYPE_FULL = "2";

	/** 系統排程 - 整批重建檢索檔(WS10000) */
	public static final String JOB_WS10000 = "WS10000";
	/** 系統排程 - 異動公司檢索資料(WS10001) */
	public static final String JOB_WS10001 = "WS10001";
	/** 系統排程 - 異動預查檢索資料(WS10002) */
	public static final String JOB_WS10002 = "WS10002";
	/** 系統排程 - 異動同音同義字資料(WS10003) */
	public static final String JOB_WS10003 = "WS10003";
	/** 系統排程 - 異動有限合夥檢索資料(WS10004) */
	public static final String JOB_WS10004 = "WS10004";

	/** 檢索檔執行狀態 - 待執行(0) */
	public static final String INDEX_LOG_STATUS_0 = "0";
	/** 檢索檔執行狀態 - 執行中(1) */
	public static final String INDEX_LOG_STATUS_1 = "1";
	/** 檢索檔執行狀態 - 執行成功(2) */
	public static final String INDEX_LOG_STATUS_2 = "2";
	/** 檢索檔執行狀態 - 執行失敗(3) */
	public static final String INDEX_LOG_STATUS_3 = "3";

	/** 索引類型 - (1)公司資料 */
	public static final String INDEX_TYPE_1 = "1";
	/** 索引類型 - (2)已收文資料 */
	public static final String INDEX_TYPE_2 = "2";
	/** 索引類型 - (3)預查資料 */
	public static final String INDEX_TYPE_3 = "3";
	/** 索引類型 - (4)預查否准資料 */
	public static final String INDEX_TYPE_4 = "4";
	/** 索引類型 - (5)有限合夥資料 */
	public static final String INDEX_TYPE_5 = "5";
	/** 索引顯示顏色 - (1)公司 (紫色)*/
	public static final String COLOR_INDEX_TYPE_1 = "#4B0082";//舊預查:"Indigo";
	/** 索引顯示顏色 - (2)已收文資料 (黃色) */
	public static final String COLOR_INDEX_TYPE_2 = "#808040";//舊預查:"#808040";
	/** 索引顯示顏色 - (3)預查資料 (綠色) */
	public static final String COLOR_INDEX_TYPE_3 = "#008000";//舊預查:"Green";

	/** 代碼種類 - (01)系統參數資料 */
	public static final String CODE_KIND_01 = "01";
	/** 代碼種類 - (02)使用者群組 */
	public static final String CODE_KIND_02 = "02";
	/** 代碼種類 - (03)取件方式 */
	public static final String CODE_KIND_03 = "03";
	/** 代碼種類 - (04)公司型態 */
	public static final String CODE_KIND_04 = "04";
	/** 代碼種類 - (05)核覆結果 */
	public static final String CODE_KIND_05 = "05";
	/** 代碼種類 - (06)案件狀態 */
	public static final String CODE_KIND_06 = "06";
	/** 代碼種類 - (07)郵寄類別 */
	public static final String CODE_KIND_07 = "07";
	/** 代碼種類 - (08)申登機關 */
	public static final String CODE_KIND_08 = "08";
	/** 代碼種類 - (09)馬上辦案由 */
	public static final String CODE_KIND_09 = "09";
	/** 代碼種類 - (10)公司狀態 */
	public static final String CODE_KIND_10 = "10";
	/** 代碼種類 - (11)預查公司狀態 */
	public static final String CODE_KIND_11 = "11";
	/** 代碼種類 - (12)身分證件別 */
	public static final String CODE_KIND_12 = "12";
	/** 代碼種類 - (13)預查種類 */
	public static final String CODE_KIND_13 = "13";
	/** 代碼種類 - (14)展期原因 */
	public static final String CODE_KIND_14 = "14";
	/** 代碼種類 - (15)片語 */
	public static final String CODE_KIND_15 = "15";

	/** 使用者群組(02)-系統管理者(00) */
	public static final String USER_GROUP_00 = "00";
	/** 使用者群組(02)-科長主管(17) */
	public static final String USER_GROUP_17 = "17";
	/** 使用者群組(02)-收文(14) */
	public static final String USER_GROUP_14 = "14";
	/** 使用者群組(02)-收文登打(12) */
	public static final String USER_GROUP_12 = "12";
	/** 使用者群組(02)-審核(11) */
	public static final String USER_GROUP_11 = "11";
	/** 使用者群組(02)-發文(13) */
	public static final String USER_GROUP_13 = "13";
	/** 使用者群組(02)-檢還撤件(18) */
	public static final String USER_GROUP_18 = "18";
	/** 使用者群組(02)-馬上辦(16) */
	public static final String USER_GROUP_16 = "16";
	/** 使用者群組(02)-客服(19) */
	public static final String USER_GROUP_19 = "19";
	/** 使用者群組(02)-查詢(90) */
	public static final String USER_GROUP_90 = "90";
	/** 使用者群組(02)-離職(XX) */
	public static final String USER_GROUP_XX = "XX";

	/** 取件方式(03)-自取(1) */
	public static final String GET_KIND_SELF = "1";
	/** 取件方式(03)-郵寄(2) */
	public static final String GET_KIND_POST = "2";
	/** 取件方式(03)-線上列印 */
	public static final String GET_KIND_ONLINE_PRINT = "3"; // 2024/03/28 新增

	/** 公司型態(04)-股份有限公司(01) */
	public static final String ORGN_TYPE_01 = "01";
	/** 公司型態(04)-有限公司(02) */
	public static final String ORGN_TYPE_02 = "02";
	/** 公司型態(04)-無限公司(03) */
	public static final String ORGN_TYPE_03 = "03";
	/** 公司型態(04)-兩合公司(04) */
	public static final String ORGN_TYPE_04 = "04";
	/** 公司型態(04)-外國公司認許(07) */
	public static final String ORGN_TYPE_07 = "07";
	/** 公司型態(04)-外國公司報備(08) */
	public static final String ORGN_TYPE_08 = "08";
	/** 公司型態(04)-大陸公司許可登記(15) */
	public static final String ORGN_TYPE_15 = "15";
	/** 公司型態(04)-大陸公司許可報備(16) */
	public static final String ORGN_TYPE_16 = "16";

	/** 核覆結果(05)-核准保留(Y) */
	public static final String APPROVE_RESULT_Y = "Y";
	/** 核覆結果(05)-不予核准(N) */
	public static final String APPROVE_RESULT_N = "N";
	/** 核覆結果(05)-審查中(A) */
	public static final String APPROVE_RESULT_A = "A";

	/** 案件狀態(06)-已收文(1) */
	public static final String PREFIX_STATUS_1 = "1";
	/** 案件狀態(06)-收文登打完成(2) */
	public static final String PREFIX_STATUS_2 = "2";
	/** 案件狀態(06)-已分文(3) */
	public static final String PREFIX_STATUS_3 = "3";
	/** 案件狀態(06)-承辦決行中(4) */
	public static final String PREFIX_STATUS_4 = "4";
	/** 案件狀態(06)-已審核(5) */
	public static final String PREFIX_STATUS_5 = "5";
	/** 案件狀態(06)-發文登打中(6) */
	public static final String PREFIX_STATUS_6 = "6";
	/** 案件狀態(06)-發文登打完成(7) */
	public static final String PREFIX_STATUS_7 = "7";
	/** 案件狀態(06)-發文結案(8) */
	public static final String PREFIX_STATUS_8 = "8";
	/** 案件狀態(06)-檢還(9) */
	public static final String PREFIX_STATUS_9 = "9";
	/** 案件狀態(06)-撤件(A) */
	public static final String PREFIX_STATUS_A = "A";
	/** 案件狀態(06)-遺失補發(B) */
	public static final String PREFIX_STATUS_B = "B";
	/** 案件狀態(06)-馬上辦(C) */
	public static final String PREFIX_STATUS_C = "C";
	/** 案件狀態(06)-刪檔(D) */
	public static final String PREFIX_STATUS_D = "D";
	/** 案件狀態(06)-撤回退費(E) */
	public static final String PREFIX_STATUS_E = "E";
	/** 案件狀態(06)-查閱(Q) */
	public static final String PREFIX_STATUS_Q = "Q";
	/** 案件狀態(06)-最近一次異動日期及時間(Z) */
	public static final String PREFIX_STATUS_Z = "Z";
	/** 案件狀態(06)-電子核定書寄送時間(W) */
	public static final String PREFIX_STATUS_W = "W";
	/** 案件狀態(06)-電子核定書收取時間(X) */
	public static final String PREFIX_STATUS_X = "X";
	/** 案件狀態(06)-電子核定書查閱時間(Y) */
	public static final String PREFIX_STATUS_Y = "Y";

	/** 郵寄類別(07)-普掛(01) */
	public static final String MAIL_TYPE_06 = "06"; //2024/03/28 配合2024/01/24調整  01改06
	/** 郵寄類別(07)-限掛(02) */
	public static final String MAIL_TYPE_07 = "07"; //2024/03/28 配合2024/01/24調整  02改07
	/** 郵寄類別(07)-公文掛號(03) */
	public static final String MAIL_TYPE_03 = "03";
	/** 郵寄類別(07)-特約普掛(01) */
	public static final String MAIL_TYPE_01 = "01"; //2024/03/28 新增，配合2024/01/24調整
	/** 郵寄類別(07)-特約限掛(01) */
	public static final String MAIL_TYPE_02 = "02"; //2024/03/28 新增，配合2024/01/24調整

	/** 一站式流程狀態 - 資料登打中(000) */
	public static final String OSSS_STATUS_000 = "000";
	/** 一站式流程狀態 - 資料登打完成(001) */
	public static final String OSSS_STATUS_001 = "001";
	/** 一站式流程狀態 - 等待付款(002) */
	public static final String OSSS_STATUS_002 = "002";
	/** 一站式流程狀態 - 資料已傳送(003) */
	public static final String OSSS_STATUS_003 = "003";
	/** 一站式流程狀態 - 已接收資料(101) */
	public static final String OSSS_STATUS_101 = "101";
	/** 一站式流程狀態 - 案件審理中(102) */
	public static final String OSSS_STATUS_102 = "102";
	/** 一站式流程狀態 - 已結案(103) */
	public static final String OSSS_STATUS_103 = "103";
	/** 一站式流程狀態 - 移文中(104) */
	public static final String OSSS_STATUS_104 = "104";
	/** 一站式流程狀態 - 已撤件(105) */
	public static final String OSSS_STATUS_105 = "105";
	/** 一站式流程狀態 - 等待文件補送中(106) */
	public static final String OSSS_STATUS_106 = "106";
	
	/** 一站式案件類型 - 公司名稱預查(C1000) */
	public static final String OSSS_APPLY_TYPE_C1000 = "C1000";
	/** 一站式案件類型 - 公司名稱及所營事業變更預查(L1000) */
	public static final String OSSS_APPLY_TYPE_L1000 = "L1000";
	/** 一站式案件類型 - 公司名稱變更預查(L1100) */
	public static final String OSSS_APPLY_TYPE_L1100 = "L1100";
	/** 一站式案件類型 - 公司所營事業變更預查(L1010) */
	public static final String OSSS_APPLY_TYPE_L1010 = "L1010";
	/** 一站式案件類型 - 公司名稱及所營事業變更預查(L1110) */
	public static final String OSSS_APPLY_TYPE_L1110 = "L1110";
	/** 一站式案件類型 - 線上審核(L0100) */
	public static final String OSSS_APPLY_TYPE_L0100 = "L0100";
	/** 一站式案件類型 - 馬上辦(L0010) */
	public static final String OSSS_APPLY_TYPE_L0010 = "L0010";
	
	/** 一站式案件類型 - 有限合夥名稱預查(S1000) */
	public static final String OSSS_APPLY_TYPE_S1000 = "S1000";
	/** 一站式案件類型 - 有限合夥名稱及所營事業變更預查(L1110) */
	public static final String OSSS_APPLY_TYPE_O1000 = "O1000";
	
	/** 一站式付款工具 - 信用卡		*/
	public static final String OSSS_PAY_BY_CREDIT_CARD = "1";
	/** 一站式付款工具 - 金融帳戶		*/
	public static final String OSSS_PAY_BY_TRANS = "2";
	/** 一站式付款工具 - 晶片金融卡		*/
	public static final String OSSS_PAY_BY_IC_CARD = "3";
	/** 一站式付款工具 - ibon		*/
	public static final String OSSS_PAY_BY_IBON = "4";
	/** 一站式付款工具 - Life-ET	*/
	public static final String OSSS_PAY_BY_LIFEET = "5";
	/** 一站式付款工具 - FamiPort	*/
	public static final String OSSS_PAY_BY_FAMIPORT = "6";
	
	/** 預查公司狀態(11)-代收申請中(00) */
	public static final String COMPANY_STATUS_00 = "00";
	/** 預查公司狀態(11)-申請中(01) */
	public static final String COMPANY_STATUS_01 = "01";
	/** 預查公司狀態(11)-撤銷申請案(02) */
	public static final String COMPANY_STATUS_02 = "02";
	/** 預查公司狀態(11)-已核准(03) */
	public static final String COMPANY_STATUS_03 = "03";
	/** 預查公司狀態(11)-申請案退件(04) */
	public static final String COMPANY_STATUS_04 = "04";

	/** 預查種類(13)-設立(0) */
	public static final String CHANGE_TYPE_0 = "0";
	/** 預查種類(13)-名稱變更(1) */
	public static final String CHANGE_TYPE_1 = "1";
	/** 預查種類(13)-所營變更(2) */
	public static final String CHANGE_TYPE_2 = "2";
	/** 預查種類(13)-名稱及所營變更(3) */
	public static final String CHANGE_TYPE_3 = "3";
	
	/** 個資軌跡(異動類) */
	public static final String TRACK_LOG_UPDATE = "E";
	/** 個資軌跡(查詢類) */
	public static final String TRACK_LOG_SEARCH = "Q";
	
	/** 程式代號 - 預查申請資料收文 */
	public static final String FUN_CODE_1001 = "PRE1001";
	/** 程式代號 - 收文登打作業 */
	public static final String FUN_CODE_1006 = "PRE1006";
	/** 程式代號 - 發文登打作業 */
	public static final String FUN_CODE_2001 = "PRE2001";
	/** 程式代號 - 郵寄掛號登錄及維護 */
	public static final String FUN_CODE_2003 = "PRE2003";
	/** 程式代號 - 預查審核登錄及維護 */
	public static final String FUN_CODE_3001 = "PRE3001";
	/** 程式代號 - 馬上辦 */
	public static final String FUN_CODE_3004 = "PRE3004";
	
	/** DI檔 版本號**/
	public static final String DI_DTD_VERSION_104_2_UTF8 = "104_2_utf8.dtd"; // 新增於2024/05/17
	/** DI公文發文機關 **/
	public static final String DI_REG_UNIT = "經濟部商業發展署";// 新增於2024/05/20
	/** DI公文發文機關帶碼 **/
	public static final String DI_REG_UNIT_CODE = "A13010000G";// 新增於2024/05/20
	/** DI公文發文機關地址 **/
	public static final String DI_REG_UNIT_ADDR = "100210 臺北市福州街15號";// 新增於2024/05/20
	/** DI公文發文字號 s**/
	public static final String DI_BUSS_TYPE = "商資";// 新增於2024/05/20
	
	/** SFTP 相關設置 **/
	public static final String SFTP_NAME_PREFIX_SFTP = "prefix";// 新增於2024/07/29
	
	public static final String PREFIX_FILE_SFTP_USERNAME = "prefix_file_manager";// 新增於2024/07/29
	
	public static final String RECEIPT_HOST = "**************";// 新增於2024/07/31
	
	public static final String RECEIPT_TEST_HOST = "*************";// 新增於2024/07/31
	
	public static final String PREFIX_FILE_SFTP_HOST = "*************";// 新增於2024/07/29
	
	public static final String PREFIX_FILE_SFTP_TEST_HOST = "*************";// 新增於2024/07/29
	
	public static final int PREFIX_FILE_SFTP_PORT = 22;// 新增於2024/07/29
	
	public static final String PREFIX_FILE_SFTP_PASSWORD = "M@nagerP@ssw0rd#1130731";// 新增於2024/07/29
	
	public static final String TESTDIR = "";// 新增於2024/07/29
	
	public static final boolean IF_OSS_TEST_SECONEND = true;// 新增於2024/07/29
	
	public static final String PREFIX_FILE_SFTP_PATH = "/opt/receipt/";// 新增於2024/07/29
	
	public static final String PREFIX_FILE_SFTP_PATH_RELATIVE = "opt/receipt";// 新增於2024/07/29
}