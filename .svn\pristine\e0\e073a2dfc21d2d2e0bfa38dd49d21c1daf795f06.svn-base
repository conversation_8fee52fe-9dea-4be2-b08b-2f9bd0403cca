package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.SynonymWord;
import com.kangdainfo.util.lang.CommonStringUtils;

public class SynonymWordDao extends BaseDaoJdbc implements RowMapper<SynonymWord> {

	private static final String SQL_query = "SELECT * FROM SYNONYM_WORD ";

	/**
	 * 依pk查詢
	 * @param id
	 * @return
	 */
	public SynonymWord queryByPk(String id){
		if("".equals(Common.get(id))) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQLCondition(" ID = ? ");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		java.util.List<SynonymWord> objList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(objList != null && objList.size() > 0)
			return objList.get(0);
		return null;
	}

	public List<SynonymWord> find(SynonymWord bo) {
		SQLJob sqljob = new SQLJob(SQL_query);
		//三個欄位皆用模糊比對
		if ( !"".equals(Common.get(bo.getWord())) ){
			sqljob.appendSQLCondition(" (WORD LIKE ? OR SYNONYM_WORD LIKE ? )");
			sqljob.addLikeParameter(bo.getWord());
			sqljob.addLikeParameter(bo.getWord());
		}
		//if ( !"".equals(Common.get(bo.getSynonymWord())) ){
		//	sqljob.appendSQLCondition(" SYNONYM_WORD LIKE ? ");
		//	sqljob.addLikeParameter(bo.getSynonymWord());
		//}
		if ( !"".equals(Common.get(bo.getSource())) ){
			sqljob.appendSQLCondition(" SOURCE LIKE ? ");
			sqljob.addLikeParameter(bo.getSource());
		}
		sqljob.appendSQL(" ORDER BY MOD_DATE DESC, MOD_TIME DESC");
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (java.util.List<SynonymWord>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	/**
	 * 依字詞查詢
	 * @param word
	 * @return 
	 */
	public SynonymWord findByWord(String word, String synonymWord) {
		if("".equals(Common.get(word)) || "".equals(Common.get(synonymWord))) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQL(" WHERE WORD = ? AND SYNONYM_WORD = ? ");
		sqljob.addParameter(word);
		sqljob.addParameter(synonymWord);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<SynonymWord> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}
	
	public SynonymWord findByCheckWord(String word, String synonymWord) {
		if("".equals(Common.get(word)) || "".equals(Common.get(synonymWord))) return null;
		SQLJob sqljob = new SQLJob(SQL_query);
		sqljob.appendSQL(" WHERE (TRIM(WORD) = ? AND TRIM(SYNONYM_WORD) = ? ) ");
		sqljob.appendSQL(" OR (TRIM(WORD) = ? AND TRIM(SYNONYM_WORD) = ? ) ");
		sqljob.addParameter(word);
		sqljob.addParameter(synonymWord);
		sqljob.addParameter(synonymWord);
		sqljob.addParameter(word);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<SynonymWord> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}
	
	public SynonymWord insert(SynonymWord obj){
		if(obj == null) return null;
		SQLJob sqljob = new SQLJob("INSERT INTO SYNONYM_WORD(");
		sqljob.appendSQL("WORD, ");
		sqljob.appendSQL("SYNONYM_WORD, ");
		sqljob.appendSQL("SOURCE, ");
		sqljob.appendSQL("MOD_ID_NO, ");
		sqljob.appendSQL("MOD_DATE, ");
		sqljob.appendSQL("MOD_TIME) ");
		sqljob.appendSQL("VALUES(?, ?, ?, ?, ?, ?)");
		sqljob.addParameter(obj.getWord());
		sqljob.addParameter(obj.getSynonymWord());
		sqljob.addParameter(obj.getSource());
		sqljob.addParameter(obj.getModIdNo());
		sqljob.addParameter(obj.getModDate());
		sqljob.addParameter(obj.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), 
				new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR
		});
		return findByWord(obj.getWord(), obj.getSynonymWord());
	}
	
	public SynonymWord update(SynonymWord obj){
		if("".equals(Common.get(obj.getId()))) return null;
		
		SynonymWord t = queryByPk(obj.getId());
		if(null==t) return null;
		
		SQLJob sqljob = new SQLJob("UPDATE SYNONYM_WORD SET ");
		sqljob.appendSQL(" WORD = ?,");
		sqljob.appendSQL(" SYNONYM_WORD = ?,");
		sqljob.appendSQL(" SOURCE = ?,");		
		sqljob.appendSQL(" MOD_ID_NO = ?,");
		sqljob.appendSQL(" MOD_DATE = ?, ");
		sqljob.appendSQL(" MOD_TIME = ? ");
		sqljob.appendSQL(" WHERE ID = ? ");
		sqljob.addParameter(obj.getWord());
		sqljob.addParameter(obj.getSynonymWord());		
		sqljob.addParameter(obj.getSource());
		sqljob.addParameter(obj.getModIdNo());
		sqljob.addParameter(obj.getModDate());
		sqljob.addParameter(obj.getModTime());
		sqljob.addParameter(obj.getId());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(),
				new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR
		});
		return queryByPk(obj.getId());
	}
	
	
	public void delete(SynonymWord bo) {
		//check pk
		if(null!=bo && CommonStringUtils.isNotEmpty(bo.getId()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM SYNONYM_WORD WHERE ID = ? ");
			sqljob.addParameters(bo.getId());
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	public SynonymWord mapRow(ResultSet rs, int idx) throws SQLException {
		SynonymWord obj = null;
		if(null!=rs) {
			obj = new SynonymWord();
			obj.setId(rs.getString("ID"));
			obj.setWord(rs.getString("WORD"));
			obj.setSynonymWord(rs.getString("SYNONYM_WORD"));
			obj.setSource(rs.getString("SOURCE"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}
}