package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;

/**
 * 排程(PRE0011)
 * 備份 SAMENAME_QUEUE 到 SAMENAME_QUEUE_H
 */
public class Pre0011QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		ServiceGetter.getInstance().getSameNameCompareService().doBackupSameNameQueue();
	}

}