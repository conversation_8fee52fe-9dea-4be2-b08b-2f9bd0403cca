package com.kangdainfo.tcfi.scheduling;

import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;

public abstract class BaseQuartzJobBean extends QuartzJobBean
{
	protected Log log = LogFactory.getLog(this.getClass());
	protected Boolean enable;
	
	protected void executeInternal(JobExecutionContext context)
		throws JobExecutionException
	{
		if(enable)
		{
			if(log.isDebugEnabled()) log.debug("[START]"+getDateTime());
			executeJob(context);
			if(log.isDebugEnabled()) log.debug("[END]"+getDateTime());
		}
		else
		{
			if(log.isDebugEnabled()) log.debug("[JOB DISABLED]");
		}
	}

	protected abstract void executeJob(JobExecutionContext context) throws JobExecutionException;

	public Boolean getEnable() {	return enable;	}
	public void setEnable(Boolean enable) {	this.enable = enable;	}

	protected Date getDateTime() {	return new Date();	}

}