package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 排程(PRE0001)
 * 重建索引
 *
 */
public class Pre0001QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context)
			throws JobExecutionException {
		
		//1.start
		IndexLog indexLog = ServiceGetter.getInstance().getIndexCreateService().doStartBuildIndex();
		if(null!=indexLog) {
			//2.execute
			indexLog = ServiceGetter.getInstance().getIndexCreateService().doBuildIndex(indexLog);
			//3.end
			ServiceGetter.getInstance().getIndexCreateService().doEndBuildIndex(indexLog);
			if (PrefixConstants.INDEX_LOG_STATUS_2.equals(indexLog.getStatus())) {
				// 若重buildIndex成功, 更新當天新增的同音同義字為enable
				ServiceGetter.getInstance().getPre8010Service().updateAllCanUseToY();
				com.kangdainfo.ServiceGetter.getInstance().getCedbc058CodeLoader().reload();
			}
		}
	}
}
