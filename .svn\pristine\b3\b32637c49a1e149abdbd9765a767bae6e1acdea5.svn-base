package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司預查申請案收件人異動記錄檔(CEDB1023L)
 *
 */
public class Cedb1023L extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 收件人地址 */
	private String getAddr;
	/** 收件人姓名 */
	private String getName;
	/** 是否接受簡訊服務 */
	private String sms;
	/** 接收簡訊手機號碼 */
	private String contactCel;
	/** 變更類別 */
	private String changeType;
	/** 閉鎖性註記 */
	private String closed;
	/** 組織別(01:股份有限公司, 02:有限公司, 03:無限公司, 04:兩合公司, 05:有限合夥) */
	private String orgType;
	/** 異動日期 */
	private String updateDate;
	/** 異動時間 */
	private String updateTime;
	/** 異動人員識別碼 */
	private String updateIdNo;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getGetAddr() {return getAddr;}
	public void setGetAddr(String s) {this.getAddr = s;}
	public String getGetName() {return getName;}
	public void setGetName(String s) {this.getName = s;}
	public String getSms() {return sms;}
	public void setSms(String s) {this.sms = s;}
	public String getContactCel() {return contactCel;}
	public void setContactCel(String s) {this.contactCel = s;}
	public String getChangeType() {return changeType;}
	public void setChangeType(String s) {this.changeType = s;}
	public String getClosed() {return closed;}
	public void setClosed(String s) {this.closed = s;}
	public String getOrgType() {return orgType;}
	public void setOrgType(String s) {this.orgType = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}
	public String getUpdateIdNo() {return updateIdNo;}
	public void setUpdateIdNo(String s) {this.updateIdNo = s;}

}