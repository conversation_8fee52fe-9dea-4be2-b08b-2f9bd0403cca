package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.tcfi.model.eicm.bo.UmsMt;
import com.kangdainfo.tcfi.model.eicm.dao.UmsMtDao;
import com.kangdainfo.tcfi.service.TestService;

public class TestServiceImpl implements TestService {

	private Logger logger = Logger.getLogger(this.getClass());

	private UmsMtDao umsMtDao;

	public UmsMt getUmsMtByUmsNo(String umsNo) {
		if(logger.isInfoEnabled()) logger.info("[getUmsMtByUmsNo][umsNo:"+umsNo+"]");
		return umsMtDao.findByUmsNo(umsNo);
	}
	
	public List<UmsMt> getUmsMtsByPrefixNo(String prefixNo) {
		if(logger.isInfoEnabled()) logger.info("[getUmsMtsByPrefixNo][prefixNo:"+prefixNo+"]");
		return umsMtDao.findByPrefixNo(prefixNo);
	}

	public UmsMtDao getUmsMtDao() {return umsMtDao;}
	public void setUmsMtDao(UmsMtDao umsMtDao) {this.umsMtDao = umsMtDao;}

}