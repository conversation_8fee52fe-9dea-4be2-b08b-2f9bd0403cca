package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class RestrictionItem extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
	/** FK RRESTRICTION.ID */
	private Integer restrictionId;
	/** 營業項目 */
	private String itemCode;
	/** 營業項目中文名稱 */
	private String businessItem;
	/** 異動人員 */
	private String modIdNo;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;
	
	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}
	
	/** FK RRESTRICTION.ID */
	public Integer getRestrictionId() {return restrictionId;}
	/** FK RRESTRICTION.ID */
	public void setRestrictionId(Integer restrictionId) {this.restrictionId = restrictionId;}
	
	/** 營業項目 */
	public String getItemCode() {return itemCode;}
	/** 營業項目 */
	public void setItemCode(String itemCode) {this.itemCode = itemCode;}
	
	/** 營業項目中文名稱 */
	public String getBussinessItem() {return businessItem;}
	/** 營業項目中文名稱 */
	public void setBusinessItem(String itemCode) {this.businessItem = itemCode;}
	
	/** 異動人員 */
	public String getModIdNo() {return modIdNo;}
	/** 異動人員 */
	public void setModIdNo(String modIdNo) {this.modIdNo = modIdNo;}

	/** 異動日期 */
	public String getModDate() {return modDate;}
	/** 異動日期 */
	public void setModDate(String modDate) {this.modDate = modDate;}

	/** 異動時間 */
	public String getModTime() {return modTime;}
	/** 異動時間 */
	public void setModTime(String modTime) {this.modTime = modTime;}
}
