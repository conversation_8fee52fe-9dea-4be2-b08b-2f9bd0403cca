package com.kangdainfo.tcfi.model.eedb.bo;

import com.kangdainfo.persistence.BaseModel;

public class Eedb1000 extends BaseModel {
	private static final long serialVersionUID = 1L;

	private String telixNo;
	private String writerType;
	private String roleType;
	private String prefixNo;
	private String applyType;
	private String printFlag;
	private Integer totalFee;
	private String banNo;
	private String houseTaxNo;
	private String orgnType;
	private String closed;
	private String companyName;
	private String companyZipCode;
	private String companyAreaCode;
	private String companyAddr;
	private String companyVillage;
	private String applyName;
	private String applyId;
	private String contactName;
	private String contactAddr;
	private String contactVillage;
	private String contactAreaCode;
	private String contactZipCode;
	private String contactTel;
	private String contactCel;
	private String contactFax;
	private String contactGetKind;
	private String contactEmail;
	private String attorName;
	private String attorId;
	private String attorAddr;
	private String attorVillage;
	private String attorAreaCode;
	private String attorZipCode;
	private String attorTel;
	private String attorNo;
	private String orderDate;
	private String orderTime;
	private String openDate;
	private String receiveDate;
	private String receiveTime;
	private String approveDate;
	private String approveTime;
	private String approveResult;
	private String processStatus;
	private String paymentStatus;
	private String reserveDate;
	private String writerMemo;
	private String enrolmentXml;
	private String updateDate;
	private String updateTime;
	private String uid;
	private String sms;
	private String isRcv;
	private String cmpyRemitEname;

	public String getApplyId() {
		return applyId;
	}
	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getApplyType() {
		return applyType;
	}
	public void setApplyType(String applyType) {
		this.applyType = applyType;
	}
	public String getApproveDate() {
		return approveDate;
	}
	public void setApproveDate(String approveDate) {
		this.approveDate = approveDate;
	}
	public String getApproveResult() {
		return approveResult;
	}
	public void setApproveResult(String approveResult) {
		this.approveResult = approveResult;
	}
	public String getApproveTime() {
		return approveTime;
	}
	public void setApproveTime(String approveTime) {
		this.approveTime = approveTime;
	}
	public String getAttorAddr() {
		return attorAddr;
	}
	public void setAttorAddr(String attorAddr) {
		this.attorAddr = attorAddr;
	}
	public String getAttorAreaCode() {
		return attorAreaCode;
	}
	public void setAttorAreaCode(String attorAreaCode) {
		this.attorAreaCode = attorAreaCode;
	}
	public String getAttorId() {
		return attorId;
	}
	public void setAttorId(String attorId) {
		this.attorId = attorId;
	}
	public String getAttorName() {
		return attorName;
	}
	public void setAttorName(String attorName) {
		this.attorName = attorName;
	}
	public String getAttorNo() {
		return attorNo;
	}
	public void setAttorNo(String attorNo) {
		this.attorNo = attorNo;
	}
	public String getAttorTel() {
		return attorTel;
	}
	public void setAttorTel(String attorTel) {
		this.attorTel = attorTel;
	}
	public String getAttorVillage() {
		return attorVillage;
	}
	public void setAttorVillage(String attorVillage) {
		this.attorVillage = attorVillage;
	}
	public String getAttorZipCode() {
		return attorZipCode;
	}
	public void setAttorZipCode(String attorZipCode) {
		this.attorZipCode = attorZipCode;
	}
	public String getBanNo() {
		return banNo;
	}
	public void setBanNo(String banNo) {
		this.banNo = banNo;
	}
	public String getCompanyAddr() {
		return companyAddr;
	}
	public void setCompanyAddr(String companyAddr) {
		this.companyAddr = companyAddr;
	}
	public String getCompanyAreaCode() {
		return companyAreaCode;
	}
	public void setCompanyAreaCode(String companyAreaCode) {
		this.companyAreaCode = companyAreaCode;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyVillage() {
		return companyVillage;
	}
	public void setCompanyVillage(String companyVillage) {
		this.companyVillage = companyVillage;
	}
	public String getCompanyZipCode() {
		return companyZipCode;
	}
	public void setCompanyZipCode(String companyZipCode) {
		this.companyZipCode = companyZipCode;
	}
	public String getContactAddr() {
		return contactAddr;
	}
	public void setContactAddr(String contactAddr) {
		this.contactAddr = contactAddr;
	}
	public String getContactAreaCode() {
		return contactAreaCode;
	}
	public void setContactAreaCode(String contactAreaCode) {
		this.contactAreaCode = contactAreaCode;
	}
	public String getContactCel() {
		return contactCel;
	}
	public void setContactCel(String contactCel) {
		this.contactCel = contactCel;
	}
	public String getContactEmail() {
		return contactEmail;
	}
	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}
	public String getContactFax() {
		return contactFax;
	}
	public void setContactFax(String contactFax) {
		this.contactFax = contactFax;
	}
	public String getContactGetKind() {
		return contactGetKind;
	}
	public void setContactGetKind(String contactGetKind) {
		this.contactGetKind = contactGetKind;
	}
	public String getContactName() {
		return contactName;
	}
	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	public String getContactTel() {
		return contactTel;
	}
	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	public String getContactVillage() {
		return contactVillage;
	}
	public void setContactVillage(String contactVillage) {
		this.contactVillage = contactVillage;
	}
	public String getContactZipCode() {
		return contactZipCode;
	}
	public void setContactZipCode(String contactZipCode) {
		this.contactZipCode = contactZipCode;
	}
	public String getEnrolmentXml() {
		return enrolmentXml;
	}
	public void setEnrolmentXml(String enrolmentXml) {
		this.enrolmentXml = enrolmentXml;
	}
	public String getHouseTaxNo() {
		return houseTaxNo;
	}
	public void setHouseTaxNo(String houseTaxNo) {
		this.houseTaxNo = houseTaxNo;
	}
	public String getOpenDate() {
		return openDate;
	}
	public void setOpenDate(String openDate) {
		this.openDate = openDate;
	}
	public String getOrderDate() {
		return orderDate;
	}
	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}
	public String getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}
	public String getOrgnType() {
		return orgnType;
	}
	public void setOrgnType(String orgnType) {
		this.orgnType = orgnType;
	}
	public String getClosed() {
		return closed;
	}
	public void setClosed(String closed) {
		this.closed = closed;
	}
	public String getPaymentStatus() {
		return paymentStatus;
	}
	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}
	public String getPrefixNo() {
		return prefixNo;
	}
	public void setPrefixNo(String prefixNo) {
		this.prefixNo = prefixNo;
	}
	public String getPrintFlag() {
		return printFlag;
	}
	public void setPrintFlag(String printFlag) {
		this.printFlag = printFlag;
	}
	public String getProcessStatus() {
		return processStatus;
	}
	public void setProcessStatus(String processStatus) {
		this.processStatus = processStatus;
	}
	public String getReceiveDate() {
		return receiveDate;
	}
	public void setReceiveDate(String receiveDate) {
		this.receiveDate = receiveDate;
	}
	public String getReceiveTime() {
		return receiveTime;
	}
	public void setReceiveTime(String receiveTime) {
		this.receiveTime = receiveTime;
	}
	public String getReserveDate() {
		return reserveDate;
	}
	public void setReserveDate(String reserveDate) {
		this.reserveDate = reserveDate;
	}
	public String getRoleType() {
		return roleType;
	}
	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public Integer getTotalFee() {
		return totalFee;
	}
	public void setTotalFee(Integer totalFee) {
		this.totalFee = totalFee;
	}
	public String getUid() {
		return uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}
	public String getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getWriterMemo() {
		return writerMemo;
	}
	public void setWriterMemo(String writerMemo) {
		this.writerMemo = writerMemo;
	}
	public String getWriterType() {
		return writerType;
	}
	public void setWriterType(String writerType) {
		this.writerType = writerType;
	}
	public String getSms() {
		return sms;
	}
	public void setSms(String sms) {
		this.sms = sms;
	}
	public String getIsRcv() {
		return isRcv;
	}
	public void setIsRcv(String isRcv) {
		this.isRcv = isRcv;
	}
	public String getCmpyRemitEname() {
		return cmpyRemitEname;
	}
	public void setCmpyRemitEname(String cmpyRemitEname) {
		this.cmpyRemitEname = cmpyRemitEname;
	}

}