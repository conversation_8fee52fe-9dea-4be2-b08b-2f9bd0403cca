<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.test.CustomerVoice">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.test.CustomerVoice)obj.queryOne();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(c1,c2,c3,c4,c5,c6,c7,c8,c9,c10,c11,c12,c13,c14,c15,c16,c17,c18,c19,c20,c21,c22,c23,c24,c25,c26,c27,c28,c29,c30,c31,c32,c33,c34,c35,c36,c37,c38,c39,c40,c41,c42,c43,c44,c45,c46,c47,c48,c49,c50,c51,c52,c53){
	form1.id.value=c1;
	form1.prefixNo.value=c1;
	form1.applyAddr.value=c2;
	form1.applyId.value=c3;
	form1.applyKind.value=c4;
	form1.applyKindDesc.value=c5;
	form1.applyName.value=c6;
	form1.applyTel.value=c7;
	form1.applyType.value=c8;
	form1.approveDate.value=c9;
	form1.approveMark.value=c10;
	form1.approveResult.value=c11;
	form1.approveResultDesc.value=c12;
	form1.approveTime.value=c13;
	form1.assignDate.value=c14;
	form1.assignTime.value=c15;
	form1.attorAddr.value=c16;
	form1.attorName.value=c17;
	form1.attorNo.value=c18;
	form1.attorTel.value=c19;
	form1.banNo.value=c20;
	form1.closeDate.value=c21;
	form1.closeTime.value=c22;
	form1.codeName.value=c23;
	form1.codeNo.value=c24;
	form1.companyName.value=c25;
	form1.companyStus.value=c26;
	form1.companyStusDesc.value=c27;
	form1.controlCd1.value=c28;
	form1.controlCd2.value=c29;
	form1.getDate.value=c30;
	form1.getKind.value=c31;
	form1.getTime.value=c32;
	form1.idNo.value=c33;
	form1.oldCompanyName.value=c34;
	form1.prefixStatus.value=c35;
	form1.receiveDate.value=c36;
	form1.receiveTime.value=c37;
	form1.regDate.value=c38;
	form1.regUnit.value=c39;
	form1.remark.value=c40;
	form1.remark1.value=c41;
	form1.reserveDate.value=c42;
	form1.reserveDays.value=c43;
	form1.reserveMark.value=c44;
	form1.specialName.value=c45;
	form1.staffName.value=c46;
	form1.telixNo.value=c47;
	form1.updateCode.value=c48;
	form1.updateDate.value=c49;
	form1.updateIdNo.value=c50;
	form1.updateTime.value=c51;
	form1.workDay.value=c52;
	form1.zoneCode.value=c53;
}
function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">

<form id="form1" name="form1" method="post" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:300px;height:100px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">預查編號：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_prefixNo" size="15" maxlength="10" value="<%=obj.getQ_prefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getPrefixNo()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">預查編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="prefixNo" value="<%=obj.getPrefixNo()%>">
		</td>
		<td class="td_form">ApplyAddr：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyAddr" value="<%=obj.getApplyAddr()%>">
		</td>
		<td class="td_form">ApplyId：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyId" value="<%=obj.getApplyId()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ApplyKind：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyKind" value="<%=obj.getApplyKind()%>">
		</td>
		<td class="td_form">ApplyKindDesc：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyKindDesc" value="<%=obj.getApplyKindDesc()%>">
		</td>
		<td class="td_form">ApplyName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyName" value="<%=obj.getApplyName()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ApplyTel：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyTel" value="<%=obj.getApplyTel()%>">
		</td>
		<td class="td_form">ApplyType：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="applyType" value="<%=obj.getApplyType()%>">
		</td>
		<td class="td_form">ApproveDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="approveDate" value="<%=obj.getApproveDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ApproveMark：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="approveMark" value="<%=obj.getApproveMark()%>">
		</td>
		<td class="td_form">ApproveResult：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="approveResult" value="<%=obj.getApproveResult()%>">
		</td>
		<td class="td_form">ApproveResultDesc：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="approveResultDesc" value="<%=obj.getApproveResultDesc()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ApproveTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="approveTime" value="<%=obj.getApproveTime()%>">
		</td>
		<td class="td_form">AssignDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="assignDate" value="<%=obj.getAssignDate()%>">
		</td>
		<td class="td_form">AssignTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="assignTime" value="<%=obj.getAssignTime()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">AttorAddr：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="attorAddr" value="<%=obj.getAttorAddr()%>">
		</td>
		<td class="td_form">AttorName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="attorName" value="<%=obj.getAttorName()%>">
		</td>
		<td class="td_form">AttorNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="attorNo" value="<%=obj.getAttorNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">AttorTel：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="attorTel" value="<%=obj.getAttorTel()%>">
		</td>
		<td class="td_form">BanNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="banNo" value="<%=obj.getBanNo()%>">
		</td>
		<td class="td_form">CloseDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="closeDate" value="<%=obj.getCloseDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">CloseTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="closeTime" value="<%=obj.getCloseTime()%>">
		</td>
		<td class="td_form">CodeName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="codeName" value="<%=obj.getCodeName()%>">
		</td>
		<td class="td_form">CodeNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="codeNo" value="<%=obj.getCodeNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">CompanyName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="companyName" value="<%=obj.getCompanyName()%>">
		</td>
		<td class="td_form">CompanyStus：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="companyStus" value="<%=obj.getCompanyStus()%>">
		</td>
		<td class="td_form">CompanyStusDesc：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="companyStusDesc" value="<%=obj.getCompanyStusDesc()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ControlCd1：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="controlCd1" value="<%=obj.getControlCd1()%>">
		</td>
		<td class="td_form">ControlCd2：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="controlCd2" value="<%=obj.getControlCd2()%>">
		</td>
		<td class="td_form">GetDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="getDate" value="<%=obj.getGetDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">GetKind：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="getKind" value="<%=obj.getGetKind()%>">
		</td>
		<td class="td_form">GetTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="getTime" value="<%=obj.getGetTime()%>">
		</td>
		<td class="td_form">IdNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="idNo" value="<%=obj.getIdNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">OldCompanyName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="oldCompanyName" value="<%=obj.getOldCompanyName()%>">
		</td>
		<td class="td_form">PrefixStatus：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="prefixStatus" value="<%=obj.getPrefixStatus()%>">
		</td>
		<td class="td_form">ReceiveDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="receiveDate" value="<%=obj.getReceiveDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ReceiveTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="receiveTime" value="<%=obj.getReceiveTime()%>">
		</td>
		<td class="td_form">RegDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="regDate" value="<%=obj.getRegDate()%>">
		</td>
		<td class="td_form">RegUnit：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="regUnit" value="<%=obj.getRegUnit()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">Remark：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="remark" value="<%=obj.getRemark()%>">
		</td>
		<td class="td_form">Remark1：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="remark1" value="<%=obj.getRemark1()%>">
		</td>
		<td class="td_form">ReserveDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="reserveDate" value="<%=obj.getReserveDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">ReserveDays：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="reserveDays" value="<%=obj.getReserveDays()%>">
		</td>
		<td class="td_form">ReserveMark：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="reserveMark" value="<%=obj.getReserveMark()%>">
		</td>
		<td class="td_form">SpecialName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="specialName" value="<%=obj.getSpecialName()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">StaffName：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="staffName" value="<%=obj.getStaffName()%>">
		</td>
		<td class="td_form">TelixNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="telixNo" value="<%=obj.getTelixNo()%>">
		</td>
		<td class="td_form">UpdateCode：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updateCode" value="<%=obj.getUpdateCode()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">UpdateDate：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updateDate" value="<%=obj.getUpdateDate()%>">
		</td>
		<td class="td_form">UpdateIdNo：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updateIdNo" value="<%=obj.getUpdateIdNo()%>">
		</td>
		<td class="td_form">UpdateTime：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updateTime" value="<%=obj.getUpdateTime()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">WorkDay：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="workDay" value="<%=obj.getWorkDay()%>">
		</td>
		<td class="td_form">ZoneCode：</td>
		<td class="td_form_white" colspan="3">
			<input class="field" type="text" name="zoneCode" value="<%=obj.getZoneCode()%>">
		</td>
	</tr>
	</table>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
	</div>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" href="#">預查編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">統一編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">公司名稱</a></th>
		<th class="listTH"><a class="text_link_w" href="#">網路流水號</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {
			 true, true, true, true, true, true, true, true, true, true
			,true, true, true, true, true, true, true, true, true, true
			,true, true, true, true, true, true, true, true, true, true
			,true, true, true, true, true, true, true, true, true, true
			,true, true, true, true, true, true, true, true, true, true
			,true, true, true };
	boolean displayArray[] = {
			  true,false,false,false,false,false,false,false,false,false
			,false,false,false,false,false,false,false,false,false, true
			,false,false,false,false, true,false,false,false,false,false
			,false,false,false,false,false,false,false,false,false,false
			,false,false,false,false,false,false, true,false,false,false
			,false,false,false };
	String[] alignArray = {
			"center","center","center","center","center","center","center","center","center","center"
			,"center","center","center","center","center","center","center","center","center","center"
			,"center","center","center","center","center","center","center","center","center","center"
			,"center","center","center","center","center","center","center","center","center","center"
			,"center","center","center","center","center","center","center","center","center","center"
			,"center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>