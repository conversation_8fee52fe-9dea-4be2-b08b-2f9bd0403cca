package com.kangdainfo.tcfi.model.osss.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;


/**
 * 申辦案件主檔(OSSM_APPL_MAIN)
 *
 */
public class OssmApplMain extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子案號 */
	private String telixNo;
	/** 申辦類型 */
	private String applyType;
	/** 辦理方式 */
	private String writerType;
	/** 組織類型 */
	private String orgType;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人身分證字號 */
	private String applyId;
	/** 申請人地址縣市鄉鎮代碼 */
	private String applyAreaCode;
	/** 申請人地址地址-鄰 */
	private String applyNeiborCode;
	/** 申請人地址 */
	private String applyAddr;
	/** 申請人郵遞區號 */
	private String applyZipCode;
	/** 申請人出生年月日 */
	private Date applyBirthday;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人身分證字號 */
	private String attorId;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 代理人事務所所在地縣市鄉鎮代碼 */
	private String attorAreaCode;
	/** 代理人事務所所在地地址-鄰 */
	private String attorNeiborCode;
	/** 代理人事務所所在地 */
	private String attorAddr;
	/** 代理人郵遞區號 */
	private String attorZipCode;
	/** 代理人聯絡電話 */
	private String attorTel;
	/** 聯絡人姓名 */
	private String contactName;
	/** 聯絡人縣市代碼 */
	private String contactAreaCode;
	/** 聯絡人地址-鄰 */
	private String contactNeiborCode;
	/** 聯絡人地址 */
	private String contactAddr;
	/** 聯絡人郵遞區號 */
	private String contactZipCode;
	/** 聯絡人聯絡電話 */
	private String contactTel;
	/** 聯絡人行動電話 */
	private String contactCel;
	/** 聯絡人傳真電話 */
	private String contactFax;
	/** 聯絡人信箱 */
	private String contactEmail;
	/** 帳號 */
	private String userid;
	/** 公司統一編號 */
	private String banNo;
	/** 預查編號 */
	private String prefixNo;
	/** 建立時間 */
	private Date createTime;
	/** 修改時間 */
	private Date updateTime;
	/** 申請人合併地址 */
	private String applyAddrComb;
	/** 代理人合併地址 */
	private String attorAddrComb;
	/** 聯絡人合併地址 */
	private String contactAddrComb;
	/** 申登機關代碼 */
	private String regUnitCode;
	/** 申請日期 */
	private Date applyDate;
	/** 公司(商登)名稱 */
	private String orgName;
	/** 智慧型表單案件單號 */
	private String iformTelixNo;
	/** 案件型態 */
	private String caseType;
	/** 申請人英文地址 */
	private String applyEngAddr;
	/** 國貿局的案由 */
	private String tradeApplyType;
	/** 國貿局的英文名稱 */
	private String tradeOrgName;
    /** 閉鎖性 */
	private String ifClosed;
	
	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getApplyType() {
		return applyType;
	}
	public void setApplyType(String applyType) {
		this.applyType = applyType;
	}
	public String getWriterType() {
		return writerType;
	}
	public void setWriterType(String s) {
		this.writerType = s;
	}
	public String getOrgType() {
		return orgType;
	}
	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getApplyId() {
		return applyId;
	}
	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}
	public String getApplyAreaCode() {
		return applyAreaCode;
	}
	public void setApplyAreaCode(String applyAreaCode) {
		this.applyAreaCode = applyAreaCode;
	}
	public String getApplyNeiborCode() {
		return applyNeiborCode;
	}
	public void setApplyNeiborCode(String applyNeiborCode) {
		this.applyNeiborCode = applyNeiborCode;
	}
	public String getApplyAddr() {
		return applyAddr;
	}
	public void setApplyAddr(String applyAddr) {
		this.applyAddr = applyAddr;
	}
	public String getApplyZipCode() {
		return applyZipCode;
	}
	public void setApplyZipCode(String applyZipCode) {
		this.applyZipCode = applyZipCode;
	}
	public Date getApplyBirthday() {
		return applyBirthday;
	}
	public void setApplyBirthday(Date applyBirthday) {
		this.applyBirthday = applyBirthday;
	}
	public String getAttorName() {
		return attorName;
	}
	public void setAttorName(String attorName) {
		this.attorName = attorName;
	}
	public String getAttorId() {
		return attorId;
	}
	public void setAttorId(String attorId) {
		this.attorId = attorId;
	}
	public String getAttorNo() {
		return attorNo;
	}
	public void setAttorNo(String attorNo) {
		this.attorNo = attorNo;
	}
	public String getAttorAreaCode() {
		return attorAreaCode;
	}
	public void setAttorAreaCode(String attorAreaCode) {
		this.attorAreaCode = attorAreaCode;
	}
	public String getAttorNeiborCode() {
		return attorNeiborCode;
	}
	public void setAttorNeiborCode(String attorNeiborCode) {
		this.attorNeiborCode = attorNeiborCode;
	}
	public String getAttorAddr() {
		return attorAddr;
	}
	public void setAttorAddr(String attorAddr) {
		this.attorAddr = attorAddr;
	}
	public String getAttorZipCode() {
		return attorZipCode;
	}
	public void setAttorZipCode(String attorZipCode) {
		this.attorZipCode = attorZipCode;
	}
	public String getAttorTel() {
		return attorTel;
	}
	public void setAttorTel(String attorTel) {
		this.attorTel = attorTel;
	}
	public String getContactName() {
		return contactName;
	}
	public void setContactName(String contactName) {
		this.contactName = contactName;
	}
	public String getContactAreaCode() {
		return contactAreaCode;
	}
	public void setContactAreaCode(String contactAreaCode) {
		this.contactAreaCode = contactAreaCode;
	}
	public String getContactNeiborCode() {
		return contactNeiborCode;
	}
	public void setContactNeiborCode(String contactNeiborCode) {
		this.contactNeiborCode = contactNeiborCode;
	}
	public String getContactAddr() {
		return contactAddr;
	}
	public void setContactAddr(String contactAddr) {
		this.contactAddr = contactAddr;
	}
	public String getContactZipCode() {
		return contactZipCode;
	}
	public void setContactZipCode(String contactZipCode) {
		this.contactZipCode = contactZipCode;
	}
	public String getContactTel() {
		return contactTel;
	}
	public void setContactTel(String contactTel) {
		this.contactTel = contactTel;
	}
	public String getContactCel() {
		return contactCel;
	}
	public void setContactCel(String contactCel) {
		this.contactCel = contactCel;
	}
	public String getContactFax() {
		return contactFax;
	}
	public void setContactFax(String contactFax) {
		this.contactFax = contactFax;
	}
	public String getContactEmail() {
		return contactEmail;
	}
	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getBanNo() {
		return banNo;
	}
	public void setBanNo(String banNo) {
		this.banNo = banNo;
	}
	public String getPrefixNo() {
		return prefixNo;
	}
	public void setPrefixNo(String prefixNo) {
		this.prefixNo = prefixNo;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getApplyAddrComb() {
		return applyAddrComb;
	}
	public void setApplyAddrComb(String applyAddrComb) {
		this.applyAddrComb = applyAddrComb;
	}
	public String getAttorAddrComb() {
		return attorAddrComb;
	}
	public void setAttorAddrComb(String attorAddrComb) {
		this.attorAddrComb = attorAddrComb;
	}
	public String getContactAddrComb() {
		return contactAddrComb;
	}
	public void setContactAddrComb(String contactAddrComb) {
		this.contactAddrComb = contactAddrComb;
	}
	public String getRegUnitCode() {
		return regUnitCode;
	}
	public void setRegUnitCode(String regUnitCode) {
		this.regUnitCode = regUnitCode;
	}
	public Date getApplyDate() {
		return applyDate;
	}
	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getIformTelixNo() {
		return iformTelixNo;
	}
	public void setIformTelixNo(String iformTelixNo) {
		this.iformTelixNo = iformTelixNo;
	}
	public String getCaseType() {
		return caseType;
	}
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}
	public String getApplyEngAddr() {
		return applyEngAddr;
	}
	public void setApplyEngAddr(String applyEngAddr) {
		this.applyEngAddr = applyEngAddr;
	}
	public String getTradeApplyType() {
		return tradeApplyType;
	}
	public void setTradeApplyType(String tradeApplyType) {
		this.tradeApplyType = tradeApplyType;
	}
	public String getTradeOrgName() {
		return tradeOrgName;
	}
	public void setTradeOrgName(String tradeOrgName) {
		this.tradeOrgName = tradeOrgName;
	}
	public String getIfClosed() {
		return ifClosed;
	}
	public void setIfClosed(String ifClosed) {
		this.ifClosed = ifClosed;
	}

}