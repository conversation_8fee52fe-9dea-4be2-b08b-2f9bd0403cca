<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(prefixNo)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM EEDB1100 B WHERE B.TELIX_NO = ?");
		sqljob.addParameter(prefixNo);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEedbGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas.get(0)));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>