package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.ibm.icu.text.SimpleDateFormat;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCountry;

public class CsmdCountryDao extends BaseDaoJdbc implements RowMapper<CsmdCountry> {

	private static final String SQL_defaultOrder = "ORDER BY CN_CODE";

	private static final String SQL_findAll = "select * from icms.CSMD_COUNTRY where enable = 'Y'";
	public List<CsmdCountry> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	//除代碼、異動人員、異動日期以外的欄位皆用模糊比對的方式搜尋資料
	public List<CsmdCountry> find(CsmdCountry bo) {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if ( !"".equals(Common.get(bo.getCnCode())) ) {
			sqljob.appendSQL("AND CN_CODE = ?");
			sqljob.addParameter(bo.getCnCode());
		}
		if ( !"".equals(Common.get(bo.getCn())) ) {
			sqljob.appendSQL("AND CN LIKE ?");
			sqljob.addLikeParameter(bo.getCn());
		}
		if ( !"".equals(Common.get(bo.getUpdateUser())) ) {
			sqljob.appendSQL("AND UPDATE_USER = ?");
			sqljob.addParameter(bo.getUpdateUser());
		}
		if ( !"".equals(Common.get(bo.getUpdateDate())) ) {
			sqljob.appendSQL("AND UPDATE_DATE >= TO_DATE(?, 'DD-MM-YY')");
			SimpleDateFormat sf = new SimpleDateFormat("dd-MM-yy");
			sqljob.addParameter(sf.format(bo.getUpdateDate()));
		}
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdCountry mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdCountry obj = null;
		if(null!=rs) {
			obj = new CsmdCountry();
			obj.setCnCode(rs.getString("CN_CODE"));
			obj.setCn(rs.getString("CN"));
			obj.setCneName(rs.getString("CNE_NAME"));
			obj.setCneType(rs.getString("CNE_TYPE"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
		}
		return obj;
	}

}