package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 排程(PRE0013)
 * 異動有限合夥索引資料
 */
public class Pre0013QuartzJobBean extends BaseQuartzJobBean
{
	/**
	 * 異動有限合夥檢索資料程序
	 * 1. 讀取 INDEX_LOG 執行WS10004
	 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
	 * 3. 呼叫 delIndex.delIndexByBanNo() 刪除檢索檔資料
	 * 4. 呼叫 IndexDatabase.batchAppendIndex() 重建檢索檔 (含同音同義字)
	 * 5. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
	 */
	protected void executeJob(JobExecutionContext context)
			throws JobExecutionException {

		//1.start
		IndexLog indexLog = ServiceGetter.getInstance().getIndexUpdateService().doStartUpdate(PrefixConstants.JOB_WS10004);
		if(null!=indexLog) {
			//2.execute
			indexLog = ServiceGetter.getInstance().getIndexUpdateService().doUpdateIndex(indexLog);
			//3.end
			ServiceGetter.getInstance().getIndexUpdateService().doEndUpdate(indexLog);
		}
	}
}