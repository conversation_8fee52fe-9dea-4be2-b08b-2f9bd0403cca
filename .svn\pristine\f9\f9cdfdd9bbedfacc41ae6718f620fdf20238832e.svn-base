package com.kangdainfo.tcfi.service.impl;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023L;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixLogVo;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1007Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023LDao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao;
import com.kangdainfo.tcfi.service.Pre3007Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;

/**
 * 案件異動紀錄查詢
 *
 */
public class Pre3007ServiceImpl implements Pre3007Service {

	public PrefixLogVo findPrefixLog(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		Cedb1006 m = cedb1006Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime);
		if (m != null) {
			String companyStus = m.getCompanyStus();
			String reserveDate = m.getReserveDate();
			String approveResult = m.getApproveResult();
			String prefixStatus = m.getPrefixStatus();

			PrefixLogVo vo = new PrefixLogVo();
			vo.setCedb1007s(cedb1007Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime));
			vo.setCedb1008s(cedb1008Dao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime));

			vo.setCedb1010s(cedb1010Dao.findByPrefixNo(prefixNo));
			for(Cedb1010 cedb1010 : vo.getCedb1010s()) {
				cedb1010.setProcessStatus(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(cedb1010.getProcessStatus()));
				cedb1010.setProcessDate(Datetime.formatRocDate(cedb1010.getProcessDate())+"  "+Datetime.formatRocTime(cedb1010.getProcessTime()));
				cedb1010.setIdNo(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(cedb1010.getIdNo()));
			}

			String receiverAddr = "";//收件人地址
			String receiverName = "";//收件人姓名
			String contactCel = "";//接收簡訊手機號碼
			String applyKindDesc = "";
			Cedb1023L cedb1023L = cedb1023LDao.findByComposite(prefixNo, updateIdNo, updateDate, updateTime);
			if(null!=cedb1023L) {
				receiverAddr = cedb1023L.getGetAddr();
				receiverName = cedb1023L.getGetName();
				contactCel = cedb1023L.getContactCel();
				applyKindDesc = ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(cedb1023L.getChangeType());
			} else {
				Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
				if(null!=cedb1023) {
					receiverAddr = cedb1023.getGetAddr();
					receiverName = cedb1023.getGetName();
					contactCel = cedb1023.getContactCel();
					applyKindDesc = ServiceGetter.getInstance().getSystemCode13Loader().getCodeNameByCode(cedb1023.getChangeType());
				}
			}
			vo.setReceiverAddr(receiverAddr);
			vo.setReceiverName(receiverName);
			vo.setContactCel(contactCel);
			vo.setApplyKindDesc(("".equals(Common.get(applyKindDesc))?("1".equals(m.getApplyKind())?"設立":"變更"):applyKindDesc));

			String applyLawName = "";//所代表法人
			String applyBanNo = "";//法人統編
			Cedb1022 cedb1022 = cedb1022Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1022) {
				applyLawName = Common.get(cedb1022.getApplyLawName());
				applyBanNo = Common.get(cedb1022.getApplyBanNo());
			}
			vo.setApplyLawName(applyLawName);
			vo.setApplyBanNo(applyBanNo);
			
			String getKindDesc = "";
			if( "1".equals(m.getGetKind()) ) {
				getKindDesc = "自取";
			} else {
				String postNo = "";
				Cedb1027 cedb1027 = cedb1027Dao.findLastOneByPrefixNo(prefixNo);
				if(null!=cedb1027) {
					postNo = cedb1027.getPostNo();
				}
				getKindDesc = "郵寄　掛號號碼"+Common.get(postNo);
			}
			vo.setGetKindDesc(getKindDesc);

			vo.setPrefixNo(m.getPrefixNo());
			vo.setBanNo(m.getBanNo());
			vo.setApplyName(m.getApplyName());
			vo.setApplyId(m.getApplyId());
			vo.setApplyAddr(m.getApplyAddr());
			vo.setApplyTel(m.getApplyTel());
			vo.setAttorName(m.getAttorName());
			vo.setAttorNo(m.getAttorNo());
			vo.setAttorAddr(m.getAttorAddr());
			vo.setAttorTel(m.getAttorTel());
			vo.setApplyType(m.getApplyType());
			vo.setCompanyName(m.getCompanyName());
			vo.setTelixNo(m.getTelixNo());
			vo.setReceiveDate(m.getReceiveDate());
			vo.setReceiveTime(m.getReceiveTime());
			vo.setApproveDate(m.getApproveDate());
			vo.setApproveTime(m.getApproveTime());
			vo.setApproveResult(m.getApproveResult());
			vo.setReserveMark(m.getReserveMark());
			vo.setReserveDate(m.getReserveDate());
			vo.setGetDate(m.getGetDate());
			vo.setGetTime(m.getGetTime());
			vo.setSpecialName(m.getSpecialName());
			vo.setCompanyStus(m.getCompanyStus());
			vo.setRegUnit(m.getRegUnit());
			vo.setRemark(m.getRemark());
			vo.setAssignDate(m.getAssignDate());
			vo.setAssignTime(m.getAssignTime());
			vo.setIdNo(m.getIdNo());
			vo.setStaffName(m.getStaffName());
			vo.setUpdateIdNo(m.getUpdateIdNo());
			vo.setUpdateDate(m.getUpdateDate());
			vo.setUpdateTime(m.getUpdateTime());
			vo.setRegDate(m.getRegDate());
			vo.setZoneCode(m.getZoneCode());
			vo.setApproveMark(m.getApproveMark());
			vo.setCloseDate(m.getCloseDate());
			vo.setCloseTime(m.getCloseTime());
			vo.setRemark1(m.getRemark1());
			vo.setOldCompanyName(m.getOldCompanyName());
			vo.setPrefixStatus(m.getPrefixStatus());
			vo.setReserveDays(m.getReserveDays());
			vo.setUpdateDateTime(Datetime.formatRocDate(m.getUpdateDate())+" "+Datetime.formatRocTime(m.getUpdateTime()));

			vo.setApproveResultDesc(ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(approveResult));
			vo.setPrefixStatusDesc(ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(prefixStatus));
			vo.setApplyWay(TcfiView.getApplyWayByTelixNo(m.getTelixNo()));
			vo.setUpdateName(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(m.getUpdateIdNo()));
			vo.setReceiveDateTime(Datetime.formatRocDate(m.getReceiveDate())+" "+Datetime.formatRocTime(m.getReceiveTime()));
			vo.setAssignDateTime(Datetime.formatRocDate(m.getAssignDate())+" "+Datetime.formatRocTime(m.getAssignTime()));
			vo.setApproveDateTime(Datetime.formatRocDate(m.getApproveDate())+" "+Datetime.formatRocTime(m.getApproveTime()));
			vo.setCloseDateTime(Datetime.formatRocDate(m.getCloseDate())+" "+Datetime.formatRocTime(m.getCloseTime()));
			vo.setGetDateTime(Datetime.formatRocDate(m.getGetDate())+" "+Datetime.formatRocTime(m.getGetTime()));
			vo.setReceiveKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_2));
			vo.setIssueKeyinDateTime(cedb1010Dao.findProcessDateTime(prefixNo, PrefixConstants.PREFIX_STATUS_7));
			//提示文字
			vo.setReserveTip(TcfiView.getReserveTip(approveResult, reserveDate, prefixStatus, companyStus));
			return vo;
		}
		return null;
	}

	private Cedb1006Dao cedb1006Dao;
	private Cedb1007Dao cedb1007Dao;
	private Cedb1008Dao cedb1008Dao;
	private Cedb1010Dao cedb1010Dao;
	private Cedb1022Dao cedb1022Dao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1023LDao cedb1023LDao;
	private Cedb1027Dao cedb1027Dao;

	public Cedb1006Dao getCedb1006Dao() {return cedb1006Dao;}
	public void setCedb1006Dao(Cedb1006Dao dao) {this.cedb1006Dao = dao;}
	public Cedb1007Dao getCedb1007Dao() {return cedb1007Dao;}
	public void setCedb1007Dao(Cedb1007Dao dao) {this.cedb1007Dao = dao;}
	public Cedb1008Dao getCedb1008Dao() {return cedb1008Dao;}
	public void setCedb1008Dao(Cedb1008Dao dao) {this.cedb1008Dao = dao;}
	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	public Cedb1027Dao getCedb1027Dao() {return cedb1027Dao;}
	public void setCedb1027Dao(Cedb1027Dao dao) {this.cedb1027Dao = dao;}
	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}
	public Cedb1023LDao getCedb1023LDao() {return cedb1023LDao;}
	public void setCedb1023LDao(Cedb1023LDao dao) {cedb1023LDao = dao;}

}