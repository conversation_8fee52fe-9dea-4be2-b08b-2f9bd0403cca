package com.kangdainfo.tcfi.model.eedb.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * (EEDB5000)
 *
 */
public class Eedb5000 extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String telixNo ;
	private String receiptNo ;
	private String amount ;
	private String id ;
	private String signCaNo ;
	private String signCaName ;
	private String receiptDate ;
	private String payDate ;
	private String payTime ;
	private String transDate ;
	private String returnDate ;
	private String returnAmount ;
	private String accountDate ;
	private String returnFlag ;
	
	
	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getReceiptNo() {
		return receiptNo;
	}
	public void setReceiptNo(String receiptNo) {
		this.receiptNo = receiptNo;
	}
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getSignCaNo() {
		return signCaNo;
	}
	public void setSignCaNo(String signCaNo) {
		this.signCaNo = signCaNo;
	}
	public String getSignCaName() {
		return signCaName;
	}
	public void setSignCaName(String signCaName) {
		this.signCaName = signCaName;
	}
	public String getReceiptDate() {
		return receiptDate;
	}
	public void setReceiptDate(String receiptDate) {
		this.receiptDate = receiptDate;
	}
	public String getPayDate() {
		return payDate;
	}
	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}
	public String getPayTime() {
		return payTime;
	}
	public void setPayTime(String payTime) {
		this.payTime = payTime;
	}
	public String getTransDate() {
		return transDate;
	}
	public void setTransDate(String transDate) {
		this.transDate = transDate;
	}
	public String getReturnDate() {
		return returnDate;
	}
	public void setReturnDate(String returnDate) {
		this.returnDate = returnDate;
	}
	public String getReturnAmount() {
		return returnAmount;
	}
	public void setReturnAmount(String returnAmount) {
		this.returnAmount = returnAmount;
	}
	public String getAccountDate() {
		return accountDate;
	}
	public void setAccountDate(String accountDate) {
		this.accountDate = accountDate;
	}
	public String getReturnFlag() {
		return returnFlag;
	}
	public void setReturnFlag(String returnFlag) {
		this.returnFlag = returnFlag;
	}
	
} // Eedb5000