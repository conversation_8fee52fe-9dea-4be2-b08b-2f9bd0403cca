package com.kangdainfo.tcfi.model.lms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;

public class LmsmBusiItemDao extends BaseDaoJdbc implements RowMapper<Cedb1002>{
	
	private static String sql_findByBanNo = ""
			+ "SELECT I.* "
			+ "FROM LMS.LMSM_BUSS_MAIN M, LMS.LMSM_BUSI_ITEM I "
			+ "WHERE M.TBPK = I.PARPK AND M.BAN_NO = I.BAN_NO AND M.IS_NEWEST = 'Y' AND M.BAN_NO = ? "
			+ "ORDER BY BUSS_ITEM_SEQ ";
	
	public List<Cedb1002> findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.addParameter(banNo);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public List<Cedb2002> findCedb2002ByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.addParameter(banNo);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), new Cedb2002RowMapper());
	}
	
	@Override
	public Cedb1002 mapRow(ResultSet rs, int arg1) throws SQLException {
		Cedb1002 obj = null;
		if(null!=rs) {
			obj = new Cedb1002();
			obj.setSeqNo(rs.getString("BUSS_ITEM_SEQ"));
			obj.setBusiItem(rs.getString("BUSS_ITEM"));
			obj.setBusiItemNo(rs.getString("BUSS_ITEM_CODE"));
		}
		return obj;
	}
}

class Cedb2002RowMapper implements RowMapper<Cedb2002> {
    public Cedb2002 mapRow(ResultSet rs, int index) throws SQLException {
    	Cedb2002 obj = null;
    	if (null!=rs) {
    		obj = new Cedb2002();
    		obj.setSeqNo(rs.getString("BUSS_ITEM_SEQ"));
    		obj.setBusiItem(rs.getString("BUSS_ITEM"));
    		obj.setBusiItemNo(rs.getString("BUSS_ITEM_CODE"));
    	}
        return obj;
    }
}
