package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.NumberFormat;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;

public class PRE8013 extends SuperBean{

	private String q_id;
	private String q_code;
	private String q_name;
	private String q_enable;
	
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}
	public String getQ_code() {return checkGet(q_code);}
	public void setQ_code(String q_code) {this.q_code = checkSet(q_code);}
	public String getQ_name() {return checkGet(q_name);}
	public void setQ_name(String q_name) {this.q_name = checkSet(q_name);}
	public String getQ_enable() {return checkGet(q_enable);}
	public void setQ_enable(String q_enable) {this.q_enable = checkSet(q_enable);}

	private String id;
	private String code;
	private String name;
	private String nameSpecial;
	private String itemSpecial;
	private String otherSpecial;
	private String otherNote;
	private String orgType;
	private String relatedUnit;
	private String relatedDate;
	private String relatedNo;
	private String enable;
	
	public String getId() {return checkGet(id);}
	public void setId(String s) { id = checkSet(s);}
	public String getCode() {return checkGet(code);}
	public void setCode(String code) {this.code = checkSet(code);}
	public String getName() {return checkGet(name);}
	public void setName(String name) {this.name = checkSet(name);}
	public String getNameSpecial() {return checkGet(nameSpecial);}
	public void setNameSpecial(String nameSpecial) {this.nameSpecial = checkSet(nameSpecial);}
	public String getItemSpecial() {return checkGet(itemSpecial);}
	public void setItemSpecial(String itemSpecial) {this.itemSpecial = checkSet(itemSpecial);}
	public String getOtherSpecial() {return checkGet(otherSpecial);}
	public void setOtherSpecial(String otherSpecial) {this.otherSpecial = checkSet(otherSpecial);}
	public String getOtherNote() {return checkGet(otherNote);}
	public void setOtherNote(String otherNote) {this.otherNote = checkSet(otherNote);}
	public String getOrgType() {return checkGet(orgType);}
	public void setOrgType(String orgType) {this.orgType = checkSet(orgType);}
	public String getRelatedUnit() {return checkGet(relatedUnit);}
	public void setRelatedUnit(String relatedUnit) {this.relatedUnit = checkSet(relatedUnit);}
	public String getRelatedDate() {return checkGet(relatedDate);}
	public void setRelatedDate(String relatedDate) {this.relatedDate = checkSet(relatedDate);}
	public String getRelatedNo() {return checkGet(relatedNo);}
	public void setRelatedNo(String relatedNo) {this.relatedNo = checkSet(relatedNo);}
	public String getEnable() {return checkGet(enable);}
	public void setEnable(String enable) {this.enable = checkSet(enable);}
	
	@Override
	public Object doQueryOne() throws Exception {
		PRE8013 obj = this;
		Restriction o = ServiceGetter.getInstance().getPrefixService().getRestrictionById(Common.getInt(getId()));
		if (null!=o) {
			obj.setId(Common.get(o.getId()));
			obj.setCode(o.getCode());
			obj.setCode(o.getCode());
			obj.setName(o.getName());
			obj.setNameSpecial(o.getNameSpecial());
			obj.setItemSpecial(o.getItemSpecial());
			obj.setOtherSpecial(o.getOtherSpecial());
			obj.setOtherNote(o.getOtherNote());
			obj.setOrgType(o.getOrgType());
			obj.setRelatedUnit(o.getRelatedUnit());
			obj.setRelatedDate(o.getRelatedDate());
			obj.setRelatedNo(o.getRelatedNo());
			obj.setEnable(o.getEnable());
			obj.setEditID(o.getModIdNo());
			obj.setEditDate(o.getModDate());
		}else 
			this.setErrorMsg("查無該筆資料！");
		return obj;
	}

	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		java.util.List<Restriction> objList = ServiceGetter.getInstance().getPrefixService().getRestrictionByCondition(getQ_code(), getQ_name(), getQ_enable());
		if(objList != null && objList.size() > 0){
			java.util.Iterator<Restriction> it = objList.iterator();
			Restriction o;
			String[] rowArray = new String[7];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[7];
				rowArray[0] = Common.get(o.getId());
				rowArray[1] = Common.get(o.getName());
				rowArray[2] = Common.get(o.getNameSpecial());
				rowArray[3] = Common.get(o.getItemSpecial());
				if("01".equals(o.getOrgType()))
					rowArray[4] = "公司";
				else if("02".equals(o.getOrgType()))
					rowArray[4] = "股份有限公司";
				else if("03".equals(o.getOrgType()))
					rowArray[4] = "限本國公司";
				else if("04".equals(o.getOrgType()))
					rowArray[4] = "限中華郵政股份有限公司";
				else if("05".equals(o.getOrgType()))
					rowArray[4] = "限中央存款保險股份有限公司";
				else
					rowArray[4] = "";
				rowArray[5] = "Y".equals(o.getEnable())?"開啟":"停用";
				String[] strings = Common.get(o.getName()).split("、");
				rowArray[6] = Integer.toString(NumberFormat.cnNumericToArabic(strings[0], false));
				arrList.add(rowArray);
			}
		} else {
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		sort(arrList);
		return arrList;
	}

	@Override
	public void doCreate() throws Exception {
		if(getUpdateCheck()){
			Restriction o = new Restriction();
			o.setCode(getCode());
			o.setName(getName());
			o.setNameSpecial(getNameSpecial());
			o.setItemSpecial(getItemSpecial());
			o.setOtherSpecial(getOtherSpecial());
			o.setOtherNote(getOtherNote());
			o.setOrgType(getOrgType());
			o.setRelatedUnit(getRelatedUnit());
			o.setRelatedDate(getRelatedDate());
			o.setRelatedNo(getRelatedNo());
			o.setEnable(getEnable());
			o.setModIdNo(getLoginUserId());
			o.setModDate(Datetime.getYYYMMDD());
			o.setModTime(Datetime.getHHMMSS());
			o = ServiceGetter.getInstance().getPrefixService().insertRestriction(o);
			this.setId(Common.get(o.getId()));
		}else{
			throw new MoeaException("已存在相同限制條件代碼的資料，請重新輸入！");
		}
	}

	@Override
	public void doUpdate() throws Exception {
		Restriction o = ServiceGetter.getInstance().getPrefixService().getRestrictionById(Common.getInt(getId()));
		if(null==o)	throw new MoeaException("資料不存在!");
		
		if(getUpdateCheck()){
			o.setCode(getCode());
			o.setName(getName());
			o.setNameSpecial(getNameSpecial());
			o.setItemSpecial(getItemSpecial());
			o.setOtherSpecial(getOtherSpecial());
			o.setOtherNote(getOtherNote());
			o.setOrgType(getOrgType());
			o.setRelatedUnit(getRelatedUnit());
			o.setRelatedDate(getRelatedDate());
			o.setRelatedNo(getRelatedNo());
			o.setEnable(getEnable());
			o.setModIdNo(getLoginUserId());
			o.setModDate(Datetime.getYYYMMDD());
			o.setModTime(Datetime.getHHMMSS());
			ServiceGetter.getInstance().getPrefixService().updateRestriction(o);
		}else{
			throw new MoeaException("已存在相同限制條件代碼的資料，請重新輸入！");
		}
	}

	@Override
	public void doDelete() throws Exception {
		Restriction o = ServiceGetter.getInstance().getPrefixService().getRestrictionById(Common.getInt(getId()));
		if(null==o) throw new MoeaException("資料不存在!");
		ServiceGetter.getInstance().getPrefixService().deleteRestriction(o);
		this.setId("");
	}
	
	/** 檢核 code 是否重複 */
	protected boolean getUpdateCheck(){
		Restriction o = ServiceGetter.getInstance().getPrefixService().getRestrictionByCode(getCode());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getId().equals(Common.getInt(this.getId())))
				return true;
		}
		return false;
	}
	
	public List<String[]> sort(List<String[]> list) {
		// 重複N次
		for (int i = 0;i<list.size();i++) {
			boolean swapped = false;
			//比較到上一輪最後一個
			for (int j = 0;j<list.size()-1-i;j++) {
				String[] temp = new String[7];
				if ( Integer.parseInt(list.get(j)[6]) > Integer.parseInt(list.get(j+1)[6]) ) {
					for (int index = 0; index < 7; index++) {
						temp[index] = list.get(j)[index];
						list.get(j)[index] = list.get(j+1)[index];
						list.get(j+1)[index] = temp[index];
					}
					swapped  = true;
				} // if
			} // for
			if (!swapped) {
				break;
			} // if
		} // for
		
		return list;
	} // sort

}
