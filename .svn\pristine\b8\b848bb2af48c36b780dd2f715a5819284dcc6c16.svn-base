<!--
程式目的：審核-預查審核-資料檢核
程式代號：PRE3001_07
撰寫日期：103.08.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3001" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001_07">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
String str1 = new String(request.getParameter("withdrawCompanyName").getBytes("UTF-8"));
obj.setWithdrawCompanyName(str1);
if("init".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
} else if("doWithdraw".equals(obj.getState())) {
	obj.doWithdraw();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty() && obj.getErrorMsg().isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
$( document ).ready(function() {
	//全部選擇
	$("#selectAll").click(function() {
		commonUtils.all("prefixNos");
	});
	//取消選取
	$("#unselectAll").click(function() {
		commonUtils.unAll("prefixNos");
	});

});

function chkRequire() {
	var prefixNoSize = $("input[name=prefixNos]:checked").size();
	if (prefixNoSize != 0) {
		return true;
	}
	alert("請選擇欲檢還的預查編號");
	return false;
}

function doWithdraw(dom) {
	form1.state.value = 'doWithdraw';
	form1.withdrawType.value = (dom.name == 'oneDimensional') ? '01' : '02';
	if( chkRequire() ) { //檢查是否有選擇預查編號
		form1.submit();
	}
}

</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='審核-預查審核-資料檢核'/>
</c:import>

<table class="bgToolbar" width="100%">
  <tr>
    <td align="left">
		<input type="button" class="toolbar_default" id="selectAll" name="selectAll" value="全部選擇" />&nbsp;
		<input type="button" class="toolbar_default" id="unselectAll" name="unselectAll" value="取消選取" />&nbsp;
		<input type="button" class="toolbar_default" name="oneDimensional" value="檢還資料確認(一維)" onClick="doWithdraw(this);">&nbsp;
		<input type="button" class="toolbar_default" name="oneSite" value="撤件確認(一站式)" onClick="doWithdraw(this);">&nbsp;
		<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" onclick="javascript:window.close();">&nbsp;
    </td>
  </tr>
</table>

<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- LIST AREA -->
<tr><td class="bg">
	<div id="listContainer">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
  		    <th class="listTH" width="3%">&nbsp;</th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">預查編號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">統一編號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">申請方式</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">公司名稱</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">案件狀態</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">申請人</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">保留期限</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = { true,false,false,false,false,false,false};
  			boolean displayArray[] = { true, true, true, true, true, true, true};
  			String[] alignArray = {"left","left","left","left","left","left","left"};
  			out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),"prefixNos"));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>
<!-- LIST AREA -->

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="prefixNo" name="prefixNo" value="<%=obj.getPrefixNo() %>" />
		<input type="hidden" id="withdrawType" name="withdrawType" value="" />
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<input type="hidden" id="withdrawCompanyName" name="withdrawCompanyName" value="<%=obj.getWithdrawCompanyName()%>">
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>