package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 功能選單權限檔(FUNCTION_MENU_AUTH)
 *
 */
public class FunctionMenuAuth extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
    /** 群組代號 */
    private String groupId;
	/** 功能選單鍵值 */
	private Integer functionMenuId;
	/** 異動人員 */
	private String modId;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;

	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer i) {this.id = i;}

    /** 群組代號 */
	public String getGroupId() {return groupId;}
    /** 群組代號 */
	public void setGroupId(String s) {this.groupId = s;}

	/** 功能選單鍵值 */
	public Integer getFunctionMenuId() {return functionMenuId;}
	/** 功能選單鍵值 */
	public void setFunctionMenuId(Integer i) {this.functionMenuId = i;}
	
	/** 異動人員 */
	public String getModId() {return modId;}
	public void setModId(String modId) {this.modId = modId;}
	
	/** 異動日期 */
	public String getModDate() {return modDate;}
	public void setModDate(String modDate) {this.modDate = modDate;}
	
	/** 異動時間 */
	public String getModTime() {return modTime;}
	public void setModTime(String modTime) {this.modTime = modTime;}

}