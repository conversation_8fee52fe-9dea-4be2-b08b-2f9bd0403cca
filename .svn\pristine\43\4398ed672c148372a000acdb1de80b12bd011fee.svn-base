package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * VIEW
 * 公司所營事業資料檔(CEDB2002)
 *
 */
public class Cedb2002 extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 統一編號 */
	private String banNo;
	/** 營業項目序號 */
	private java.lang.String seqNo;
	/** 營業項目代碼 */
	private java.lang.String busiItemNo;
	/** 營業項目 */
	private java.lang.String busiItem;
	/** 核准設立變更日期 */
	private java.lang.String approveDate;
	/** 資料異動者 */
	private java.lang.String updateUser;
	/** 資料異動日期 */
	private java.lang.String updateDate;
	/** 資料異動時間 */
	private java.lang.String updateTime;
	/** 營業項目確認 */
	private java.lang.String confirmCode;

	/** 營業項目序號 */
	public java.lang.String getSeqNo() {return seqNo;}
	
	/** 統一編號 */
	public String getBanNo() { return banNo; }
	/** 統一編號 */
	public void setBanNo(String banNo) { this.banNo = banNo;}
	
	/** 營業項目序號 */
	public void setSeqNo(java.lang.String s) {this.seqNo = s;}
	/** 營業項目代碼 */
	public java.lang.String getBusiItemNo() {return busiItemNo;}
	/** 營業項目代碼 */
	public void setBusiItemNo(java.lang.String s) {this.busiItemNo = s;}
	/** 營業項目 */
	public java.lang.String getBusiItem() {return busiItem;}
	/** 營業項目 */
	public void setBusiItem(java.lang.String s) {this.busiItem = s;}
	/** 核准設立變更日期 */
	public java.lang.String getApproveDate() {return approveDate;}
	/** 核准設立變更日期 */
	public void setApproveDate(java.lang.String s) {this.approveDate = s;}
	/** 資料異動者 */
	public java.lang.String getUpdateUser() {return updateUser;}
	/** 資料異動者 */
	public void setUpdateUser(java.lang.String s) {this.updateUser = s;}
	/** 資料異動日期 */
	public java.lang.String getUpdateDate() {return updateDate;}
	/** 資料異動日期 */
	public void setUpdateDate(java.lang.String s) {this.updateDate = s;}
	/** 資料異動時間 */
	public java.lang.String getUpdateTime() {return updateTime;}
	/** 資料異動時間 */
	public void setUpdateTime(java.lang.String s) {this.updateTime = s;}
	/** 營業項目確認 */
	public java.lang.String getConfirmCode() {return confirmCode;}
	/** 營業項目確認 */
	public void setConfirmCode(java.lang.String s) {this.confirmCode = s;}

}