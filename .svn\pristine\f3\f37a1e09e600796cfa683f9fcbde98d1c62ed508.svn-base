-- Create table
create table EICM.CEDB1122
(
  PREFIX_NO      NVARCHAR2(9) not null,
  APPLY_BAN_NO   NVARCHAR2(8),
  APPLY_LAW_NAME NVARCHAR2(60)
)
tablespace EICM_DAT
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the table 
comment on table EICM.CEDB1122 is '公司預查封存法人資料檔';
-- Add comments to the columns 
comment on column EICM.CEDB1122.PREFIX_NO is '預查編號';
comment on column EICM.CEDB1122.APPLY_BAN_NO is '法人統編';
comment on column EICM.CEDB1122.APPLY_LAW_NAME is '法人名稱';
-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.CEDB1122
  add constraint PK_CEDB1122 primary key (PREFIX_NO)
  using index 
  tablespace EICM_IDX
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );

  
--Synonymn(use dba to run)
CREATE OR REPLACE SYNONYM "EICM4AP"."CEDB1122" FOR "EICM"."CEDB1122";
--GRANT(use dba to run)
grant all on EICM.CEDB1122 to EICM4AP;
