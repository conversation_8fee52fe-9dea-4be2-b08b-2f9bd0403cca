package com.kangdainfo.common.util;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.zip.GZIPOutputStream;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.io.IOUtils;

public class CommonCompressUtils {

	public static void main(String[] args) {
		targzip(new File("test"));
	}

	public static void targzip(File inputFile) {
		TarArchiveOutputStream tarout = null;
		GZIPOutputStream gzipout = null;
		try {
			if(inputFile.isDirectory()) {
				String fileName = inputFile.getName();
				String tarFileName = fileName+".tar.gz";
				if( fileName.indexOf(".") > -1 )
					tarFileName = fileName.substring(0, fileName.lastIndexOf("."))+".tar.gz";
				tarout = new TarArchiveOutputStream(new GzipCompressorOutputStream(new BufferedOutputStream(new FileOutputStream(new File(inputFile.getParentFile(), tarFileName)))));
				tarout.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);

				for(File f : inputFile.listFiles()) {
					copyFile(tarout, "" , f);
				}
			} else {
				String fileName = inputFile.getName();
				String tarFileName = fileName+".tar.gz";
				tarout = new TarArchiveOutputStream(new GzipCompressorOutputStream(new BufferedOutputStream(new FileOutputStream(new File(inputFile.getParentFile(), tarFileName)))));
				tarout.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);

				copyFile(tarout, "" , inputFile);
			}
			tarout.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			IOUtils.closeQuietly(tarout);
			IOUtils.closeQuietly(gzipout);
		}
	}
	
	private static void copyFile(TarArchiveOutputStream tarout, String dir, File src) throws Exception {
		if( src.isDirectory() ) {
			tarout.putArchiveEntry(new TarArchiveEntry(src, src.getName()));
			tarout.closeArchiveEntry();
			for(File f : src.listFiles()) {
				copyFile(tarout, src.getName()+"/" , f);
			}
		} else {
			tarout.putArchiveEntry(new TarArchiveEntry(src, dir+src.getName()));
			IOUtils.copy(new FileInputStream(src), tarout);
			tarout.closeArchiveEntry();
		}
	}

}