<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.test.CountWorkDay">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.test.CountWorkDay)obj.queryOne();
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(prefixNo, idNo, processStatus){
	form1.id.value=prefixNo;
	form1.prefixNo.value=prefixNo;
	form1.idNo.value=idNo;
	form1.processStatus.value=processStatus;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}
function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();showErrorMsg('<%=obj.getErrorMsg()%>');">

<form id="form1" name="form1" method="post" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:400px;height:250px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">預查編號：</td>
		<td class="queryTDInput">
			<input class="field_Q cmex" type="text" name="q_prefixNo" size="10" maxlength="10" value="<%=obj.getQ_prefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getPrefixNo()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="Y" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="Y" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">預查編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="prefixNo" value="<%=obj.getPrefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">承辦人員：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="idNo" value="<%=obj.getIdNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">處理日期：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="processDate" value="<%=obj.getProcessDate()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">處理時間：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="processTime" value="<%=obj.getProcessTime()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">流程狀態：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="processStatus" value="<%=obj.getProcessStatus()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">工作日數：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="workDay" value="<%=obj.getWorkDay()%>">
		</td>
	</tr>
	</table>
	</div>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" href="#">預查編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">承辦人員</a></th>
		<th class="listTH"><a class="text_link_w" href="#">處理日期</a></th>
		<th class="listTH"><a class="text_link_w" href="#">處理時間</a></th>
		<th class="listTH"><a class="text_link_w" href="#">流程狀態</a></th>
		<th class="listTH"><a class="text_link_w" href="#">工作日數</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true, true,false,false, true,false};
	boolean displayArray[] = {true, true, true, true, true, true};
	String[] alignArray = {"center","center","center","center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>