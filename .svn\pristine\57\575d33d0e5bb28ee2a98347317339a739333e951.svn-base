package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1123;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 公司預查申請案收件人封存資料檔(CEDB1123)
 *
 */
public class Cedb1123Dao
	extends BaseDaoJdbc
	implements RowMapper<Cedb1123>
{
	public Cedb1123 findByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1123 WHERE PREFIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		List<?> list = getJdbcTemplate().query(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray(), this);
		return list.isEmpty() ? null : (Cedb1123) list.get(0);
	}

	public int insert(Cedb1123 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1123 (PREFIX_NO,GET_ADDR,GET_NAME,SMS,CONTACT_CEL,CHANGE_TYPE) VALUES (?,?,?,?,?,?)");
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getSms());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getContactCel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getChangeType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public int update(Cedb1123 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("UPDATE CEDB1123 SET");
		sqljob.appendSQL("  GET_ADDR=?");
		sqljob.appendSQL(" ,GET_NAME=?");
		sqljob.appendSQL(" ,SMS=?");
		sqljob.appendSQL(" ,CONTACT_CEL=?");
		sqljob.appendSQL(" ,CHANGE_TYPE=?");
		sqljob.appendSQL(" WHERE PREFIX_NO=?");
		sqljob.addParameter(o.getGetAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getGetName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getSms());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getContactCel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getChangeType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public void deleteByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return;
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1123 WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1123 mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1123 obj = null;
		if (null != rs) {
			obj = new Cedb1123();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setGetAddr(rs.getString("GET_ADDR"));
			obj.setGetName(rs.getString("GET_NAME"));
			obj.setSms(rs.getString("SMS"));
			obj.setContactCel(rs.getString("CONTACT_CEL"));
			obj.setChangeType(rs.getString("CHANGE_TYPE"));
		}
		return obj;
	}

}