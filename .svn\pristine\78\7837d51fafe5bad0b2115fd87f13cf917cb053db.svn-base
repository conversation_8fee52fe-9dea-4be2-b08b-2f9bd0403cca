<!DOCTYPE html>
<!--
程式目的：案件異動紀錄查詢(PRE3007)
程式代號：PRE3007
撰寫日期：103.06.30
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3007">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/> 
<script type="text/javascript">
//for IE8 Array indexOf
if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = Array.prototype.indexOf || function(obj, start) {
		for (var i = (start || 0), j = this.length; i < j; i++) {
			if (this[i] === obj) {
				return i;
			}
		}
		return -1;
	};
}

var prefixVo;
var prefixNos = "";
$(document).ready(function() {
	//離開
	$('#sc_close').click(function(){
		form1.action = "pre3007.jsp";
		form1.state.value = "init";
		form1.submit();
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = prefixNos.indexOf($("#current").val());
		var pno = $("#current").val();
		if(++currentIndex >= prefixNos.length) {
			pno = prefixNos[0];
		} else {
			pno = prefixNos[currentIndex];
		}
		$("#current").val(pno);
		enterCurrent();
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		var currentIndex = prefixNos.indexOf($("#current").val());
		var pno = $("#current").val();
		if((currentIndex-1) < 0) {
			pno = prefixNos[(prefixNos.length-1)];
		} else {
			pno = prefixNos[--currentIndex];
		}
		$("#current").val(pno);
		enterCurrent();
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		var pno = prefixNos[prefixNos.length - 1];
		$("#current").val(pno);
		enterCurrent();
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		var pno = prefixNos[0];
		$("#current").val(pno);
		enterCurrent();
	});
	//上層頁籤
	$("#tabs").tabs();
	//下層頁籤
	$("#tabs2").tabs();
	//查詢
	$("#current").keypress(function(e){
		$.blockUI({message:'<h1> 資料載入中，請稍後  <img src="/prefix/images/jquery/busy.gif" /></h1>',overlayCSS:{backgroundColor:'#F3EFEF'}});
		$("#tabs2-1").click();
		$("#tabs1").click();
		if(e.which == 13) {
			$.post( getVirtualPath() + "tcfi/pre/pre3007_01.jsp?q=" + $("#current").val(), function( data ) {
				$("#tabs").tabs({ selected: 1 });
				if(!data) {
					prefixVo = null;
					return;
				}
				
				prefixVo = data;
				
				$('#remark1').val(data.remark1);
				$('#prefixNo').html(data.prefixNo+"&nbsp;&nbsp;<font color='#008000'>"+data.applyWay+"</font>");
				$('#updateDateTime').html(data.updateDateTime);
				$('#banNo').html(data.banNo);
				$('#telixNo').html(data.telixNo);
				$('#specialName').html(data.specialName);
				$('#companyName').html(data.companyName);
				$('#staffName').html(data.staffName);
				$('#oldCompanyName').html(data.oldCompanyName);
				$('#applyName').html(data.applyName);
				$('#applyId').html(data.applyId);
				$('#applyTel').html(data.applyTel);
				$('#applyAddr').html(data.applyAddr);
				$('#applyLawName').html(data.applyLawName);
				$('#applyBanNo').html(data.applyBanNo);
				$('#attorName').html(data.attorName);
				$('#attorNo').html(data.attorNo);
				$('#attorTel').html(data.attorTel);
				$('#attorAddr').html(data.attorAddr);
				$('#receiverName').html(data.receiverName);
				$('#contactCel').html(data.contactCel);
				$('#receiverAddr').html(data.receiverAddr);
				$('#getKindDesc').html(data.getKindDesc);
				$('#applyKindDesc').html(data.applyKindDesc);
				$('#reserveDate').html(data.reserveDate);
				$('#remark').html(data.remark);//註記說明
				$('#updateName').html(data.updateName);//前次異動者
				$('#receiveDateTime').html(data.receiveDateTime);//收件日期時間
				$('#receiveKeyinDateTime').html(data.receiveKeyinDateTime);//收文登打日期時間
				$('#assignDateTime').html(data.assignDateTime);//分文日期時間
				$('#approveDateTime').html(data.approveDateTime);//審核日期時間
				$('#issueKeyinDateTime').html(data.issueKeyinDateTime);//發文登打日期時間
				$('#closeDateTime').html(data.closeDateTime);//發文日期時間
				$('#getDateTime').html(data.getDateTime);//領件日期時間
				$('#sendDateTime').html(data.getDateTime);//寄件日期時間
				$('#approveResultDesc').html(data.approveResultDesc);//核覆結果
				$('#prefixStatusDesc').html(data.prefixStatusDesc);//案件狀態

				//公司名稱
				for(var i=0; i<data.cedb1007s.length; i++) {
					if(i==0) {
						$('#companyName01').html(data.cedb1007s[i].companyName);
						$('#approveResult01').attr("checked",("Y"==data.cedb1007s[i].approveResult));
					} else if(i==1) {
						$('#companyName02').html(data.cedb1007s[i].companyName);
						$('#approveResult02').attr("checked",("Y"==data.cedb1007s[i].approveResult));
					} else if(i==2) {
						$('#companyName03').html(data.cedb1007s[i].companyName);
						$('#approveResult03').attr("checked",("Y"==data.cedb1007s[i].approveResult));
					} else if(i==3) {
						$('#companyName04').html(data.cedb1007s[i].companyName);
						$('#approveResult04').attr("checked",("Y"==data.cedb1007s[i].approveResult));
					} else if(i==4) {
						$('#companyName05').html(data.cedb1007s[i].companyName);
						$('#approveResult05').attr("checked",("Y"==data.cedb1007s[i].approveResult));
					}
				}
				$.unblockUI();
			});
	    }
		setTimeout(function(){
			$.unblockUI();
		}, 2000);
	});

	//畫面初始
	var prefixNo = $('input[name="prefixNos"]').map(function() {
		return $(this).val();
	}).get().join('-');
	prefixNos = prefixNo.match(/[^-]+/g) || [];
	$("#current").val(prefixNos[0]);
	//觸發Enter
	enterCurrent();
});

function enterCurrent() {
	var e = jQuery.Event("keypress");
	e.which = 13;
	jQuery('#current').trigger(e);
}

//營業項目
function loadCedb1008s() {
	var datas = prefixVo.cedb1008s;
	//clear
	$("#cedb1008s > tbody").html("");
	//add
	for(var j=0; j<datas.length; j++) {
		addCedb1008((j%2==0)?"listTREven":"listTROdd",
				commonUtils.trimUndefined(datas[j].seqNo),
				commonUtils.trimUndefined(datas[j].busiItemNo),
				commonUtils.trimUndefined(datas[j].busiItem) );
	}
}
//營業項目
function addCedb1008(tr_class, seqNo, busiItemNo, busiItem) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItemNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItem +'</td>';
	txt += '</tr>';
	$("#cedb1008s tbody").append(txt);
}
//案件流程
function loadCedb1010s() {
	var datas = prefixVo.cedb1010s;
	//clear
	$("#cedb1010s > tbody").html("");
	//add
	for(var k=0; k<datas.length; k++) {
		addCedb1010((k%2==0)?"listTREven":"listTROdd",(k+1),datas[k].processStatus,datas[k].processDate,datas[k].idNo,datas[k].workDay);
	}
}
//案件流程
function addCedb1010(tr_class, seqNo, processStatus, processDate, idNo, workDay) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ processStatus +'</td>';
	txt += '<td style="text-align:left">'+ processDate +'</td>';
	txt += '<td style="text-align:left">'+ idNo +'</td>';
	txt += '<td style="text-align:left">'+ workDay +'</td>';
	txt += '</tr>';
	$("#cedb1010s tbody").append(txt);
}
</SCRIPT>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<table width="100%" cellspacing="0" cellpadding="0">
<tbody><tr><td class="td_default_banner" width="100%">案件異動紀錄查詢-審核詳細資料(PRE3007)</td></tr></tbody>
</table>

<table class="title_form" width="100%" cellpadding="2" cellspacing="0" >
	<tr>
		<td class="title_form_label" width="70px" >預查編號</td>
		<td class="title_form_value" width="180px" id="prefixNo">&nbsp;</td>
		<td class="title_form_label" width="70px">預查種類</td>
		<td class="title_form_value" width="300px" id="applyKindDesc">&nbsp;</td>
		<td class="title_form_label" width="80px">電子流水號</td>
		<td class="title_form_value" colspan="2" id="telixNo">&nbsp;</td>
	</tr>
	<tr>
		<td class="title_form_label">統一編號</td>
		<td class="title_form_value" id="banNo">&nbsp;</td>
		<td class="title_form_label">領件方式</td>
		<td class="title_form_value" id="getKindDesc">&nbsp;</td>
		<td class="title_form_value">
			<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
			<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
		</td>
		<td class="title_form_value" width="150px" align="center" id="updateDateTime">&nbsp;</td>
		<td class="title_form_value">
			<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
			<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" />
		</td>
	</tr>
</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1" id="tabs1"><span>案件資料</span></a></li>
		<li><a href="#fragment-2"><span>預查名稱</span></a></li>
		<li><a href="#fragment-3" onclick="loadCedb1008s()"><span>營業項目</span></a></li>
		<li><a href="#fragment-4" onclick="loadCedb1010s()"><span>案件流程</span></a></li>
	</ul>
	<div id="fragment-1" style="padding:0px;">
		<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="80px">特取名稱</td>
				<td class="td_form_white" width="200px" id="specialName">&nbsp;</td>
				<td class="td_form" width="100px">本次預查名稱</td>
				<td class="td_form_white" colspan="2" id="companyName">&nbsp;</td>
				<td class="td_form_white">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >承辦人</td>
				<td class="td_form_white" id="staffName">&nbsp;</td>
				<td class="td_form" >原名稱</td>
				<td class="td_form_white" width="185px" id="oldCompanyName">&nbsp;</td>
				<td class="td_form" width="90px">收件日期</td>
				<td class="td_form_white" id="receiveDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >前次異動者</td>
				<td class="td_form_white" colspan="3" id="updateName">&nbsp;</td>
				<td class="td_form">收文登打</td>
				<td class="td_form_white" id="receiveKeyinDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="80px" rowspan="2">備註紀錄</td>
				<td class="td_form_white" colspan="3" rowspan="2">
					<textarea class="field_RO" id="remark1" name="remark1" cols="50" rows="3" readonly></textarea>
				</td>
				<td class="td_form">分文日期</td>
				<td class="td_form_white" id="assignDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">審核日期</td>
				<td class="td_form_white" id="approveDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">註記說明</td>
				<td class="td_form_white" colspan="3" id="remark">&nbsp;</td>
				<td class="td_form">發文登打</td>
				<td class="td_form_white" id="issueKeyinDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">核覆結果</td>
				<td class="td_form_white" id="approveResultDesc">&nbsp;</td>
				<td class="td_form">案件狀態</td>
				<td class="td_form_white" id="prefixStatusDesc">&nbsp;</td>
				<td class="td_form">發文日期</td>
				<td class="td_form_white" id="closeDateTime">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" width="80px">保留期限</td>
				<td class="td_form_white" colspan="3" id="reserveDate">&nbsp;</td>
				<td class="td_form">領件日期</td>
				<td class="td_form_white" id="getDateTime">&nbsp;</td>
			</tr>
		</table>

		<div id="tabs2">
			<ul>
				<li><a href="#tabs2-f1" id="tabs2-1"><span>申請人資料</span></a></li>
				<li><a href="#tabs2-f2"><span>代理人資料</span></a></li>
				<li><a href="#tabs2-f3"><span>收件人資料</span></a></li>
			</ul>
			<div id="tabs2-f1" style="padding:0px;">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">申請人姓名</td>
						<td class="td_form_white" width="150px" id="applyName">&nbsp;</td>
						<td class="td_form" width="100px">身分ID</td>
						<td class="td_form_white" width="150px" id="applyId">&nbsp;</td>
						<td class="td_form" width="100px">申請人電話</td>
						<td class="td_form_white" id="applyTel">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">所代表法人</td>
						<td class="td_form_white" id="applyLawName">&nbsp;</td>
						<td class="td_form">法人統編</td>
						<td class="td_form_white" colspan="3" id="applyBanNo">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">申請人地址</td>
						<td class="td_form_white" colspan="5" id="applyAddr">&nbsp;</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f2" style="padding:0px;">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">代理人姓名</td>
						<td class="td_form_white" width="150px" id="attorName">&nbsp;</td>
						<td class="td_form" width="150px">證書號碼╱身分ID</td>
						<td class="td_form_white" width="150px" id="attorNo">&nbsp;</td>
						<td class="td_form" width="100px">聯絡電話</td>
						<td class="td_form_white" id="attorTel">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">事務所所在地</td>
						<td class="td_form_white" colspan="5" id="attorAddr">&nbsp;</td>
					</tr>
				</TABLE>
			</div>
			<div id="tabs2-f3" style="padding:0px;">
				<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
					<tr>
						<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form" width="100px">收件人姓名</td>
						<td class="td_form_white" width="150px" id="receiverName">&nbsp;</td>
						<td class="td_form" width="150px">簡訊通知回覆電話</td>
						<td class="td_form_white"　width="150px" id="contactCel">&nbsp;</td>
						<td class="td_form" width="100px">寄件日期</td>
						<td class="td_form_white" id="sendDateTime">&nbsp;</td>
					</tr>
					<tr>
						<td class="td_form">聯絡地址</td>
						<td class="td_form_white" colspan="5" id="receiverAddr">&nbsp;</td>
					</tr>
				</TABLE>
			</div>
		</div>
	</div>
	<div id="fragment-2" style="padding:0px;">
		<TABLE id="cedb1007s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<tr>
				<td class="td_form" style="text-align: left;width:50px;">序號</td>
				<td class="td_form" style="text-align: left;width:240px;">公司名稱</td>
				<td class="td_form" style="text-align: left;width:70px;">審核結果</td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">01</td>
				<td style="text-align:left" id="companyName01">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult01" name="approveResult01" value="Y" disabled></td>
			</tr>
			<tr class="listTREven">
				<td style="text-align:left">02</td>
				<td style="text-align:left" id="companyName02">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult02" name="approveResult02" value="Y" disabled></td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">03</td>
				<td style="text-align:left" id="companyName03">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult03" name="approveResult03" value="Y" disabled></td>
			</tr>
			<tr class="listTREven">
				<td style="text-align:left">04</td>
				<td style="text-align:left" id="companyName04">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult04" name="approveResult04" value="Y" disabled></td>
			</tr>
			<tr class="listTROdd">
				<td style="text-align:left">05</td>
				<td style="text-align:left" id="companyName05">&nbsp;</td>
				<td style="text-align:left"><input type="checkbox" id="approveResult05" name="approveResult05" value="Y" disabled></td>
			</tr>
		</TABLE>
	</div>
	<div id="fragment-3" style="padding:0px;">
		<TABLE id="cedb1008s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">代碼</td>
					<td class="td_form" style="text-align: left">營業項目</td>
				</tr>
			</thead>
			<tbody>
			<!-- insert here -->
			</tbody>
		</table>
	</div>
	<div id="fragment-4" style="padding:0px;">
		<TABLE id="cedb1010s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">案件處理狀況</td>
					<td class="td_form" style="text-align: left">案件處理時間</td>
					<td class="td_form" style="text-align: left">處理人員</td>
					<td class="td_form" style="text-align: left">工作日數</td>
				</tr>
			</thead>
			<tbody>
			<!-- insert here -->
			</tbody>
		</TABLE>
	</div>
</div>

<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="current" name="current" value="<%=obj.getCurrent()%>" />
<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<%
if(null!=obj.getPrefixNos()) {
	for(String p : obj.getPrefixNos()) {
out.write("<input type='hidden' name='prefixNos' value='"+p+"' />\n");
	}
}
%>
</form>
</body>
</html>