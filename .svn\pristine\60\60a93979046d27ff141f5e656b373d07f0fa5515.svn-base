package com.kangdainfo.tcfi.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1021;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1021Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao;
import com.kangdainfo.tcfi.model.eicm.dao.SyncOssQueueDao;
import com.kangdainfo.tcfi.service.Pre2004Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.view.pre.PRE2004;

/**
 * 預查核准領件編號維護(PRE2004)
 *
 */
public class Pre2004ServiceImpl implements Pre2004Service {

	public Logger logger = Logger.getLogger(getClass());

	public String queryStartNo() {
		String result = "";
		SQLJob sqljob = new SQLJob("SELECT MAX(PREFIX_NO) AS PREFIX_NO FROM CEDB1021 WHERE UPDATE_DATE < ( to_char(sysdate,'YYYYMMDD') - 19110000 )");
		List<Map<String, Object>> maplist = eicmGeneralQueryDao.queryForList(sqljob);
		if(null!=maplist && !maplist.isEmpty()) {
			result = (String)maplist.get(0).get("PREFIX_NO");
		}
		return result;
	}

	public String queryEndNo() {
		String result = "";
		SQLJob sqljob = new SQLJob("SELECT MAX(PREFIX_NO) AS PREFIX_NO FROM CEDB1021 WHERE UPDATE_DATE >= ( to_char(sysdate,'YYYY') - 1911 )||'0000'");
		List<Map<String, Object>> maplist = eicmGeneralQueryDao.queryForList(sqljob);
		if(null!=maplist && !maplist.isEmpty()) {
			result = (String)maplist.get(0).get("PREFIX_NO");
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	public List<PRE2004> queryPre2004s(String prefixNoStart, String prefixNoEnd) {
		SQLJob sqljob = new SQLJob("SELECT");
		sqljob.appendSQL(" a.prefix_no");
		sqljob.appendSQL(",a.telix_no");
		sqljob.appendSQL(",nvl(c13.code_name,decode(a.apply_kind,'1','設立','2','變更','')) as change_type");
		sqljob.appendSQL(",a.apply_name");
		sqljob.appendSQL(",a.attor_name");
		sqljob.appendSQL(",(case when nvl(a.receive_date,' ') = ' ' then a.receive_date");
		sqljob.appendSQL("       when nvl(a.receive_time,' ') = ' ' then a.receive_time");
		sqljob.appendSQL("       else substr(a.receive_date,1,3)||'/'||substr(a.receive_date,4,2)||'/'||substr(a.receive_date,6,2)||'  '||substr(a.receive_time,1,2)||':'||substr(a.receive_time,3,2)||':'||substr(a.receive_time,5,2)");
		sqljob.appendSQL("       end");
		sqljob.appendSQL(") AS apply_date_time");
		sqljob.appendSQL(",nvl(c03.code_name,'') as get_kind");
		sqljob.appendSQL(",(case when nvl(a.assign_date,' ') = ' ' then a.assign_date");
		sqljob.appendSQL("       when nvl(a.assign_time,' ') = ' ' then a.assign_time");
		sqljob.appendSQL("       else substr(a.assign_date,1,3)||'/'||substr(a.assign_date,4,2)||'/'||substr(a.assign_date,6,2)||'  '||substr(a.assign_time,1,2)||':'||substr(a.assign_time,3,2)||':'||substr(a.assign_time,5,2)");
		sqljob.appendSQL("       end");
		sqljob.appendSQL(") AS assign_date_time");
		sqljob.appendSQL("from cedb1000 a");
		sqljob.appendSQL("  left outer join cedb1023 b on a.prefix_no = b.prefix_no");
		sqljob.appendSQL("  left outer join system_code c13 on c13.code_kind='13' and c13.code=b.change_type");
		sqljob.appendSQL("  left outer join system_code c03 on c03.code_kind='03' and c03.code=a.get_kind");
		sqljob.appendSQL("WHERE (a.APPROVE_RESULT='Y' or a.APPROVE_RESULT='N')");//已審查
		sqljob.appendSQL("AND a.APPROVE_DATE IS NOT NULL");//已審查
		sqljob.appendSQL("AND (a.CLOSE_DATE IS NULL OR a.CLOSE_DATE= '')");//未結案
		sqljob.appendSQL("AND EXISTS (SELECT 1 FROM CEDB1010 WHERE PREFIX_NO=a.PREFIX_NO AND PROCESS_STATUS='7')");//已執行發文登打(7)
		sqljob.appendSQL("AND (a.PREFIX_STATUS IS NULL or a.PREFIX_STATUS NOT IN ('A','E'))");//不為撤件(A)或撤回退費(E)
		sqljob.appendSQL("AND a.APPROVE_DATE >= (to_char(sysdate,'yyyy')-1912)||to_char(sysdate,'MMdd')");//只列出系統日期前一年內可開放領件的案件

		if( !"".equals(Common.get(prefixNoStart)) ) {
			sqljob.appendSQL("AND A.PREFIX_NO >= ?");
			sqljob.addParameter(prefixNoStart);
		}
		if( !"".equals(Common.get(prefixNoEnd)) ) {
			sqljob.appendSQL("AND A.PREFIX_NO <= ?");
			sqljob.addParameter(prefixNoEnd);
		}
		sqljob.appendSQL("ORDER BY A.PREFIX_NO");//排序
		if(logger.isInfoEnabled()) logger.info(sqljob);
		return eicmGeneralQueryDao.query(sqljob, BeanPropertyRowMapper.newInstance(PRE2004.class));
	}

	public void saveCedb1021(String maxPrefixNo, String modIdNo) {
		if( !"".equals(Common.get(maxPrefixNo)) ) {
			Cedb1021 obj = new Cedb1021();
			obj.setPrefixNo(maxPrefixNo);
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj.setUpdateIdNo(modIdNo);
			cedb1021Dao.insert(obj);
		}
	}

	public String doCloseCase(String prefixNo, String modIdNo) {
		if(logger.isInfoEnabled()) logger.info("[doCloseCase][PrefixNo:"+prefixNo+"][ModIdNo:"+modIdNo+"]");
		String error = "";
		//1.查詢待結案件
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null==cedb1000) {
			return "主檔資料不存在";
		}
		String approveResult = cedb1000.getApproveResult();
		if( !PrefixConstants.APPROVE_RESULT_Y.equals(approveResult) && !PrefixConstants.APPROVE_RESULT_N.equals(approveResult) ) {
			return "主檔尚未審查";
		}
		if( "".equals(Common.get(cedb1000.getApproveDate())) ) {
			return "主檔尚未審查";
		}
		if( !"".equals(Common.get(cedb1000.getCloseDate())) ) {
			return "主檔已結案";
		}
		Cedb1010 cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, PrefixConstants.PREFIX_STATUS_7);
		if(null==cedb1010) {
			return "發文登打尚未完成";
		}

		//更新主檔(CEDB1000)
		String closeDate = Datetime.getYYYMMDD();
		String closeTime = Datetime.getHHMMSS();
		if( !"".equals(Common.get(cedb1000.getGetDate())) ) {
			//若已取件, 則以取件日期時間作為結案日期時間
			closeDate = Common.get(cedb1000.getGetDate());
			closeTime = Common.get(cedb1000.getGetTime());
		}
		
		Integer reserveDays = cedb1000.getReserveDays();
		//若未設定保留天數, 才重新判斷要保留半年或一年
		if(null==reserveDays || (reserveDays != PrefixConstants.RESERVE_DAYS_HALF_YEAR && reserveDays != PrefixConstants.RESERVE_DAYS_ONE_YEAR) ) {
			//預設是保留半年
			reserveDays = PrefixConstants.RESERVE_DAYS_HALF_YEAR;
			//1.判斷是否為設立案
			boolean isSetup = false;
			Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1023) {
				if( PrefixConstants.CHANGE_TYPE_0.equals(cedb1023.getChangeType()) ) {
					isSetup = true;
				}
			}
			if( isSetup ) {
				//2.若為設立案才要判斷是否要保留一年
				if (ServiceGetter.getInstance().getPrefixService().checkReserve365(prefixNo)) {
					reserveDays = PrefixConstants.RESERVE_DAYS_ONE_YEAR;
				}
			} else {
				//3.變更案一律為保留半年，除非是(J504011)有線電視系統經營業要保留一年
				// 5/24 坤宏說六科說要改為"增加此營業項目才保留一年", 若原本就有即維持半年
			    Cedb1002 j504011 = cedb1002Dao.findByPrefixNoAndBusiItemNo(prefixNo, "J504011");
			    if(null!=j504011) {
			    	Cedb2002 j504011inCedb2002 = cedb2002Dao.findByBanNoAndBusiItemNo(cedb1000.getBanNo(), "J504011");
			    	if (j504011inCedb2002 == null) {
			    		reserveDays = PrefixConstants.RESERVE_DAYS_ONE_YEAR;
			    	}
			    }
			}
		}

		cedb1000.setCloseDate(closeDate);
		cedb1000.setCloseTime(closeTime);
		cedb1000.setUpdateDate(Datetime.getYYYMMDD());
		cedb1000.setUpdateTime(Datetime.getHHMMSS());
		cedb1000.setUpdateIdNo(modIdNo);
		cedb1000.setPrefixStatus(PrefixConstants.PREFIX_STATUS_8);

		// 當核准保留時才異動保留期限 
		if (PrefixConstants.APPROVE_RESULT_Y.equals(cedb1000.getApproveResult())) {
			cedb1000.setReserveDays(reserveDays);
			cedb1000.setReserveDate(ServiceGetter.getInstance().getPrefixService().countReserveDate(closeDate, reserveDays, "N"));
		} else if (PrefixConstants.APPROVE_RESULT_N.equals(cedb1000.getApproveResult())) {
			//不予核准時, 要清除保留期限
			cedb1000.setReserveDays(null);
			cedb1000.setReserveDate(null);
		}
		cedb1000.setReserveMark("N");//延展保留一個月不在發文時執行（不在第一次給保留期限時作）
		cedb1000Dao.update(cedb1000);
		//更新EICM.EEDB1000
		eedb1000Dao.updateClosed(prefixNo);
		//更新EICM.EEDB1002
		eedb1002Dao.updateClosed(prefixNo);
		//同步一站式案件狀態
		if( Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS")  ) {
			ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(prefixNo, modIdNo);
		}
		return error;
	}

	public String checkRemark (String prefixNo) { 
		if(logger.isInfoEnabled()) logger.info("[checkRemark][PrefixNo:" + prefixNo + "]");
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if ((cedb1000.getRemark() != null && cedb1000.getRemark().indexOf("本預查案件已申請線上撤件") != -1)){
			return cedb1000.getRemark().substring(cedb1000.getRemark().indexOf("已申請"));
		}
		return "N";
	}
	
	private Cedb1021Dao cedb1021Dao;
	private Cedb1000Dao cedb1000Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1010Dao cedb1010Dao;
	private Eedb1000Dao eedb1000Dao;
	private Eedb1002Dao eedb1002Dao;
	private EicmGeneralQueryDao eicmGeneralQueryDao;
	private SyncOssQueueDao syncOssQueueDao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb2002Dao cedb2002Dao;

	public Cedb1021Dao getCedb1021Dao() {return cedb1021Dao;}
	public void setCedb1021Dao(Cedb1021Dao dao) {this.cedb1021Dao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	public Eedb1000Dao getEedb1000Dao() {return eedb1000Dao;}
	public void setEedb1000Dao(Eedb1000Dao dao) {this.eedb1000Dao = dao;}
	public Eedb1002Dao getEedb1002Dao() {return eedb1002Dao;}
	public void setEedb1002Dao(Eedb1002Dao dao) {this.eedb1002Dao = dao;}
	public EicmGeneralQueryDao getEicmGeneralQueryDao() {return eicmGeneralQueryDao;}
	public void setEicmGeneralQueryDao(EicmGeneralQueryDao dao) {this.eicmGeneralQueryDao = dao;}
	public SyncOssQueueDao getSyncOssQueueDao() {return syncOssQueueDao;}
	public void setSyncOssQueueDao(SyncOssQueueDao dao) {this.syncOssQueueDao = dao;}
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}
	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}

}