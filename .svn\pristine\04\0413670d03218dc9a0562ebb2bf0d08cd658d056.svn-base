<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- USE JNDI CONNECTION -->
	<!-- <bean id="eicmDataSource" class="org.springframework.jndi.JndiObjectFactoryBean"> 
		<property name="jndiName"> <value>java:comp/env/jdbc/eicm</value> </property> 
		</bean> -->
	<bean id="eicmQDDataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${eicmQD.dataSource.driverClassName}" />
		<property name="url" value="${eicmQD.dataSource.url}" />
		<property name="username" value="${eicmQD.dataSource.username}" />
		<property name="password" value="${eicmQD.dataSource.password}" />
		<property name="validationQuery" value="${eicmQD.dataSource.validationQuery}" />
		<property name="poolPreparedStatements" value="${eicmQD.dataSource.poolPreparedStatements}" />
		<property name="maxOpenPreparedStatements" value="${eicmQD.dataSource.maxOpenPreparedStatements}" />
		<property name="maxActive" value="${eicmQD.dataSource.maxActive}" />
		<property name="maxIdle" value="${eicmQD.dataSource.maxIdle}" />
	</bean>
	<!-- 
	<bean id="cedb2000Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedb2002Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedb2004Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedb2006Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2006Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedb2013Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2013Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedbc050Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc050Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedbc053Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedbc054Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	<bean id="cedbc055Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc055Dao">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	
	 -->
</beans>