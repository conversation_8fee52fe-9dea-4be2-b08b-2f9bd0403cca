package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;

public class Cedb1017Dao extends BaseDaoJdbc implements RowMapper<Cedb1017> {
	
	private static final String SQL_findAll = "SELECT * FROM CEDB1017 where staff_unit=? order by post_type";
    public List<Cedb1017> findAll(String staffUnit) {
    	SQLJob sqljob = new SQLJob(SQL_findAll);
    	sqljob.addParameter(staffUnit);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPostType = "SELECT * FROM CEDB1017 where staff_unit = ? and POST_TYPE=?";
    public Cedb1017 findByPostType(String staffUnit, String postType) {
    	SQLJob sqljob = new SQLJob(SQL_findByPostType);
    	sqljob.addParameter(staffUnit);
    	sqljob.addParameter(postType);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1017> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list.isEmpty()? null:list.get(0);
    }

    public void update(Cedb1017 bo) { 
		if(!"".equals(bo.getPostType())){
			// 表示資料庫內有該筆資料
			// update
			SQLJob sqljob = new SQLJob("UPDATE CEDB1017 SET");
			sqljob.appendSQL(" START_POST_NO = ?");
			sqljob.appendSQL(",END_POST_NO = ?");
			sqljob.appendSQL(",USED_POST_NO = ?");
			sqljob.appendSQL("WHERE POST_TYPE = ? and staff_unit = ?");
			sqljob.addParameter(bo.getStartPostNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getEndPostNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getUsedPostNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getPostType());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getStaffUnit());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}

	@Override
	public Cedb1017 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1017 obj = null;
		if (null != rs) {
			obj = new Cedb1017();
			obj.setPostType(rs.getString("POST_TYPE"));
			obj.setStartPostNo(rs.getString("START_POST_NO"));
			obj.setEndPostNo(rs.getString("END_POST_NO"));
			obj.setUsedPostNo(rs.getString("USED_POST_NO"));
			obj.setStaffUnit(rs.getString("STAFF_UNIT"));
		}
		return obj;
	}

}