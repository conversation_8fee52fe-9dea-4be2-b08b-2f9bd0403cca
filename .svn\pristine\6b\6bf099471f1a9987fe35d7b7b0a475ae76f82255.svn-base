<%
	response.setHeader("cache-control", "private");
	response.setHeader("X-Frame-Options", "deny");
	response.setHeader("X-UA-Compatible", "IE=edge");
	//弱點修補：漏或不安全的HTTP Strict-Transport-Security 標頭
	response.setHeader("Strict-Transport-Security", "max-age=31536000");
	//弱點修補：遺漏或不安全的"X-Content-Type-Options" 標頭
	response.setHeader("X-Content-Type-Options", "nosniff");
%>
<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ page import="com.kangdainfo.*"%>
<%@ page import="com.kangdainfo.common.util.*"%>
<%@ page import="com.kangdainfo.common.model.bo.*"%>
<%
String p_uid = "";
CommonUser commonUser = null;
String errorFlg = "1";
boolean isPass = ServiceGetter.getInstance().getAuthenticationService().authenticate(request);
if(!isPass) {
	//登入失敗
	session.invalidate();
	response.sendRedirect("index.jsp?error="+errorFlg);
} else {
	//登入成功
	response.sendRedirect("home/frame.jsp");
	//update session data
	commonUser = (CommonUser) session.getAttribute(com.kangdainfo.web.util.WebConstants.SESSION_CURRENT_USER);
	String isSSO = commonUser.getIsSSO();
	commonUser = ServiceGetter.getInstance().getAuthenticationService().getCommonUserByUserId(commonUser.getUserId());
	commonUser.setIsSSO(isSSO);
	session.setAttribute(com.kangdainfo.web.util.WebConstants.SESSION_CURRENT_USER,commonUser);
}
%>
<head>
<script>
function setCookie(c_name,value,exHours)
{
	var exdate=new Date();
	exdate.setHours(exdate.getHours() + exHours);
	var c_value=escape(value) + ((exHours==null) ? "" : "; expires="+exdate.toUTCString());
	document.cookie=c_name + "=" + c_value;
}
</script>
</head>
<body onload="setCookie('UID','<%=p_uid%>',1);">
</body>
</html>