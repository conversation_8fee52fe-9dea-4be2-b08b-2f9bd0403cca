package com.kangdainfo.tcfi.view.pre;

/*
程式目的：一站式案件查詢
程式代號：pre4020
撰寫日期：103.12.04
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmBussItem;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgName;

public class PRE4020 extends SuperBean {
	//查詢條件
	/** 網路流水號 */
	private String q_telixNo;
	/** 預查編號 */
	private String q_prefixNo;
	/** 統一編號 */
	private String q_banNo;
	/** 公司名稱 */
	private String q_companyName;
	/** 申請人身分ID */
	private String q_applyId;
	/** 申請人姓名 */
	private String q_applyName;
	/** 申請帳號 */
	private String q_userid;

	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		if( !"".equals(getQ_telixNo())
			|| !"".equals(getQ_prefixNo())
			|| !"".equals(getQ_userid()) )
		{
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" A.PREFIX_NO");
			sqljob.appendSQL(" ,nvl(");
			sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
			sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
			sqljob.appendSQL("  )");
			sqljob.appendSQL("  ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE_NAME");
			sqljob.appendSQL(",A.COMPANY_NAME");
			sqljob.appendSQL(",A.APPLY_NAME");
			sqljob.appendSQL(",A.TELIX_NO");
			sqljob.appendSQL(",A.APPROVE_RESULT");
			sqljob.appendSQL(",A.RESERVE_DATE");
			sqljob.appendSQL(",decode(A.APPROVE_RESULT,'Y','核准保留','N','不予核准','A','審查中','') as APPROVE_RESULT_DESC");
			sqljob.appendSQL("FROM CEDB1000 A");
			sqljob.appendSQL("WHERE (A.TELIX_NO LIKE 'OSC%' OR A.TELIX_NO LIKE 'OSS%')");
			if( !"".equals(getQ_telixNo()) ) {
				sqljob.appendSQL("AND (A.TELIX_NO=?)");
				sqljob.addParameter(getQ_telixNo());
			}
			if( !"".equals(getQ_prefixNo()) ) {
				sqljob.appendSQL("AND (A.PREFIX_NO LIKE ?)");
				sqljob.addSuffixLikeParameter(getQ_prefixNo());
			}
			if( !"".equals(getQ_banNo()) ) {
				sqljob.appendSQL("AND (A.BAN_NO = ?)");
				sqljob.addParameter(getQ_banNo());
			}
			if( !"".equals(getQ_companyName()) ) {
				sqljob.appendSQL("AND (A.COMPANY_NAME LIKE ?");
				sqljob.addLikeParameter(getQ_companyName());
				//需同步查詢CEDB1001
				sqljob.appendSQL("OR A.PREFIX_NO IN (SELECT PREFIX_NO FROM CEDB1001 WHERE COMPANY_NAME LIKE ?)");
				sqljob.addLikeParameter(getQ_companyName());
				sqljob.appendSQL(")");
			}
			if( !"".equals(getQ_applyId()) ) {
				sqljob.appendSQL("AND (A.APPLY_ID = ?)");
				sqljob.addParameter(getQ_applyId().toUpperCase());
			}
			if( !"".equals(getQ_applyName()) ) {
				sqljob.appendSQL("AND (A.APPLY_NAME = ?)");
				sqljob.addParameter(getQ_applyName());
			}
			if( !"".equals(getQ_userid()) ) {
				List<OssmApplMain> ossmApplMains = ServiceGetter.getInstance().getPre4020Service().getOssmApplMainsByUserid(getQ_userid());
				if(null==ossmApplMains || ossmApplMains.isEmpty()) {
					sqljob.appendSQL("AND 1=2");
				} else {
					sqljob.appendSQL("AND A.TELIX_NO IN (");
					boolean isFirst = true;
					for(OssmApplMain ossmApplMain : ossmApplMains) {
						if(!isFirst) sqljob.appendSQL(",");
						sqljob.appendSQL("?");
						sqljob.addParameter(ossmApplMain.getTelixNo());
						isFirst = false;
					}
					sqljob.appendSQL(")");
				}
			}
			sqljob.appendSQL("ORDER BY A.PREFIX_NO");
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[7];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[7];
					rowArray[0] = Common.get(o.get("PREFIX_NO"));
					rowArray[1] = Common.get(o.get("CHANGE_TYPE_NAME"));
					rowArray[2] = Common.get(o.get("COMPANY_NAME"));
					rowArray[3] = Common.get(o.get("APPLY_NAME"));
					rowArray[4] = Common.get(o.get("TELIX_NO"));
					rowArray[5] = Common.get(o.get("APPROVE_RESULT_DESC"));
					rowArray[6] = Common.get(o.get("RESERVE_DATE"));
					arrList.add(rowArray);
				}
			}
		}
		return arrList;
	}

	public Object doQueryOne() throws Exception {return null;}
	public void doCreate() throws Exception {}
	public void doUpdate() throws Exception {}
	public void doDelete() throws Exception {}

	public String getQ_telixNo() {return checkGet(q_telixNo);}
	public void setQ_telixNo(String s) {this.q_telixNo = checkSet(s);}
	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}
	public String getQ_applyId() {return checkGet(q_applyId);}
	public void setQ_applyId(String s) {this.q_applyId = checkSet(s);}
	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {this.q_applyName = checkSet(s);}
	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {this.q_banNo = checkSet(s);}
	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {this.q_companyName = checkSet(s);}
	public String getQ_userid() {return checkGet(q_userid);}
	public void setQ_userid(String s) {this.q_userid = checkSet(s);}

	private String[] telixNos;
	public String[] getTelixNos() {return telixNos;}
	public void setTelixNos(String[] telixNos) {this.telixNos = telixNos;}
	private String telixNo;
	public String getTelixNo() {return checkGet(telixNo);}
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}
	private OssmApplMain ossmApplMain;
	public OssmApplMain getOssmApplMain() {return ossmApplMain;}
	public void setOssmApplMain(OssmApplMain o) {this.ossmApplMain = o;}
	private List<OssmApplFlow> ossmApplFlows;
	public List<OssmApplFlow> getOssmApplFlows() {return ossmApplFlows;}
	public void setOssmApplFlows(List<OssmApplFlow> l) {this.ossmApplFlows = l;}
	private OssmFeeMain ossmFeeMain;
	public OssmFeeMain getOssmFeeMain() {return ossmFeeMain;}
	public void setOssmFeeMain(OssmFeeMain o) {this.ossmFeeMain = o;}
	private OssmFeeDetail ossmFeeDetail;
	public OssmFeeDetail getOssmFeeDetail() {return ossmFeeDetail;}
	public void setOssmFeeDetail(OssmFeeDetail o) {this.ossmFeeDetail = o;}
	private List<OssmBussItem> ossmBussItems;
	public List<OssmBussItem> getOssmBussItems() {return ossmBussItems;}
	public void setOssmBussItems(List<OssmBussItem> l) {this.ossmBussItems = l;}
	private OssmOrgChange ossmOrgChange;
	public OssmOrgChange getOssmOrgChange() {return ossmOrgChange;}
	public void setOssmOrgChange(OssmOrgChange o) {this.ossmOrgChange = o;}
	private List<OssmOrgName> ossmOrgNames;
	public List<OssmOrgName> getOssmOrgNames() {return ossmOrgNames;}
	public void setOssmOrgNames(List<OssmOrgName> l) {this.ossmOrgNames = l;}

}