package com.kangdainfo.tcfi.model.eicm.bo;

import java.util.List;

import com.kangdainfo.persistence.BaseModel;

public class PrefixQueryVo extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 公司名稱 */
	private String companyName01;
	private String approveResult01;
	private String remark01;
	private String showSame01;
	private String companyName02;
	private String approveResult02;
	private String remark02;
	private String showSame02;
	private String companyName03;
	private String approveResult03;
	private String remark03;
	private String showSame03;
	private String companyName04;
	private String approveResult04;
	private String remark04;
	private String showSame04;
	private String companyName05;
	private String approveResult05;
	private String remark05;
	private String showSame05;
	/** 同名公司 */
	private List<Cedb1004> cedb1004s_1;
	/** 同名公司 */
	private List<Cedb1004> cedb1004s_2;
	/** 同名公司 */
	private List<Cedb1004> cedb1004s_3;
	/** 同名公司 */
	private List<Cedb1004> cedb1004s_4;
	/** 同名公司 */
	private List<Cedb1004> cedb1004s_5;
	/** 營業項目 */
	private List<Cedb1002> cedb1002s;
	/** 案件過程 */
	private List<Cedb1010> cedb1010s;

	/** 預查編號 */
	private String prefixNo;
	/** 申辦方式 */
	private String applyWay;
	/** 預查種類:設立 */
	private String setup;
	/** 預查種類:名稱變更 */
	private String changeName;
	/** 預查種類:所營變更 */
	private String changeItem;
	/** 閉鎖性屬性 */
	private String closed;
	/** 網路收文電子流水號 */
	private String telixNo;
	/** 統一編號 */
	private String banNo;
	/** 預查結果領取方式 */
	private String getKind;
	/** 領件方式備註 */
	private String getKindDesc;
	/** 公司名稱 */
	private String companyName;
	/** 前次異動者 */
	private String updateName;
	/** 備註紀錄 */
	private String remark1;
	/** 註記說明(檢還/撤件) */
	private String remark;
	/** 審核結果 */
	private String approveRemark;
	/** 核覆結果 */
	private String approveResultDesc;
	/** 案件狀態 */
	private String prefixStatusDesc; //
	/** 承辦人員姓名 */
	private String staffName;
	/** 前次預查名稱 */
	private String lastCompanyName;
	/** 特取名稱 */
	private String specialName;
	/** 保留期限 */
	private String reserveDate;
	/** 保留天數 */
	private Integer reserveDays;
	/** 延展保留期限註記 */
	private String reserveMark;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人身分證ID */
	private String applyId;
	/** 申請人電話 */
	private String applyTel;
	/** 所代表法人 */
	private String applyLawName;
	/** 法人統編 */
	private String applyBanNo;
	/** 申請人地址 */
	private String applyAddr;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 代理人聯絡電話 */
	private String attorTel;
	/** 代理人所在地 */
	private String attorAddr;
	/** 收件人姓名 */
	private String receiveName;
	/** 簡訊通知回覆電話 */
	private String contactCel;
	/** 寄件日期 */
	private String sendDateTime;
	/** 聯絡地址 */
	private String receiveAddr;
	/** 公司現況主檔名稱 */
	private String mainFileCompanyName;
	/** 原公司名稱 */
	private String oldCompanyName;
	/** 案件狀態 */
	private String prefixStatus;
	/** 收件日期時間 */
	private String receiveDateTime;
	/** 收文登打日期時間 */
	private String receiveKeyinDateTime;
	/** 分文日期時間 */
	private String assignDateTime;
	/** 審核日期時間 */
	private String approveDateTime;
	/** 發文登打日期時間 */
	private String issueKeyinDateTime;
	/** 發文日期時間 */
	private String closeDateTime;
	/** 領件日期時間 */
	private String getDateTime;
	/** 案件提示文字 */
	private String reserveTip;
	/** 公司設立日期 */
	private String cmpySetupDate;
	/** 是否有預查表附件 */
	private String isPrefixForm;
	/** 附件-預查表編號 */
	private String prefixFormNo;
	/** 是否有其他機關核准函附件 */
	private String isOtherForm;
	/** 是否有說明書附件 */
	private String isSpec;
	/** 是否有其他附件 */
	private String isOtherSpec;
	/** 其他附件註記 */
	private String otherSpecRemark;
	/** 正副本別 */
	private String docType;
	/** 領件方式註記 */
	private String getKindRemark;
    // 自由填列事項（不納入預查審核項目）
    /** 國外匯款使用英文名稱(僅提供銀行開戶使用)  */
    private String extRemitEname;

	public List<Cedb1002> getCedb1002s() {return cedb1002s;}
	public void setCedb1002s(List<Cedb1002> l) {this.cedb1002s = l;}
	public List<Cedb1010> getCedb1010s() {return cedb1010s;}
	public void setCedb1010s(List<Cedb1010> l) {this.cedb1010s = l;}
	public String getPrefixStatusDesc() {return prefixStatusDesc;}
	public void setPrefixStatusDesc(String s) {this.prefixStatusDesc = s;}
	public String getMainFileCompanyName() {return mainFileCompanyName;}
	public void setMainFileCompanyName(String s) {this.mainFileCompanyName = s;}
	public String getLastCompanyName() {return lastCompanyName;}
	public void setLastCompanyName(String s) {this.lastCompanyName = s;}
	public String getUpdateName() {return updateName;}
	public void setUpdateName(String s) {this.updateName = s;}
	public String getApplyWay() {return applyWay;}
	public void setApplyWay(String s) {this.applyWay = s;}
	public String getReceiveDateTime() {return receiveDateTime;}
	public void setReceiveDateTime(String s) {this.receiveDateTime = s;}
	public String getReceiveKeyinDateTime() {return receiveKeyinDateTime;}
	public void setReceiveKeyinDateTime(String s) {this.receiveKeyinDateTime = s;}
	public String getAssignDateTime() {return assignDateTime;}
	public void setAssignDateTime(String s) {this.assignDateTime = s;}
	public String getApproveDateTime() {return approveDateTime;}
	public void setApproveDateTime(String s) {this.approveDateTime = s;}
	public String getIssueKeyinDateTime() {return issueKeyinDateTime;}
	public void setIssueKeyinDateTime(String s) {this.issueKeyinDateTime = s;}
	public String getCloseDateTime() {return closeDateTime;}
	public void setCloseDateTime(String s) {this.closeDateTime = s;}
	public String getGetDateTime() {return getDateTime;}
	public void setGetDateTime(String s) {this.getDateTime = s;}
	public String getReserveTip() {return reserveTip;}
	public void setReserveTip(String s) {this.reserveTip = s;}
	public String getCmpySetupDate() {return cmpySetupDate;}
	public void setCmpySetupDate(String s) {this.cmpySetupDate = s;}
    public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String s) {this.applyAddr = s;}
	public String getApplyId() {return applyId;}
	public void setApplyId(String s) {this.applyId = s;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String s) {this.applyName = s;}
	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}
	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}
	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}
	public String getAttorTel() {return attorTel;}
	public void setAttorTel(String s) {this.attorTel = s;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getGetKind() {return getKind;}
	public void setGetKind(String s) {this.getKind = s;}
	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}
	public String getPrefixStatus() {return prefixStatus;}
	public void setPrefixStatus(String s) {this.prefixStatus = s;}
	public String getRemark() {return remark;}
	public void setRemark(String s) {this.remark = s;}
	public String getRemark1() {return remark1;}
	public void setRemark1(String s) {this.remark1 = s;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String s) {this.reserveDate = s;}
	public Integer getReserveDays() {return reserveDays;}
	public void setReserveDays(Integer i) {this.reserveDays = i;}
	public String getReserveMark() {return reserveMark;}
	public void setReserveMark(String s) {this.reserveMark = s;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String s) {this.specialName = s;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String s) {this.staffName = s;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String s) {this.telixNo = s;}
	public String getSetup() {return setup;}
	public void setSetup(String s) {this.setup = s;}
	public String getChangeName() {return changeName;}
	public void setChangeName(String s) {this.changeName = s;}
	public String getChangeItem() {return changeItem;}
	public void setChangeItem(String s) {this.changeItem = s;}
	public String getClosed() {return closed;}
	public void setClosed(String s) {this.closed = s;}
	public String getGetKindDesc() {return getKindDesc;}
	public void setGetKindDesc(String s) {this.getKindDesc = s;}
	public String getApproveResultDesc() {return approveResultDesc;}
	public void setApproveResultDesc(String s) {this.approveResultDesc = s;}
	public String getApplyLawName() {return applyLawName;}
	public void setApplyLawName(String s) {this.applyLawName = s;}
	public String getApplyBanNo() {return applyBanNo;}
	public void setApplyBanNo(String s) {this.applyBanNo = s;}
	public String getReceiveName() {return receiveName;}
	public void setReceiveName(String s) {this.receiveName = s;}
	public String getContactCel() {return contactCel;}
	public void setContactCel(String s) {this.contactCel = s;}
	public String getSendDateTime() {return sendDateTime;}
	public void setSendDateTime(String s) {this.sendDateTime = s;}
	public String getReceiveAddr() {return receiveAddr;}
	public void setReceiveAddr(String s) {this.receiveAddr = s;}
	public List<Cedb1004> getCedb1004s_1() {return cedb1004s_1;}
	public void setCedb1004s_1(List<Cedb1004> l) {this.cedb1004s_1 = l;}
	public List<Cedb1004> getCedb1004s_2() {return cedb1004s_2;}
	public void setCedb1004s_2(List<Cedb1004> l) {this.cedb1004s_2 = l;}
	public List<Cedb1004> getCedb1004s_3() {return cedb1004s_3;}
	public void setCedb1004s_3(List<Cedb1004> l) {this.cedb1004s_3 = l;}
	public List<Cedb1004> getCedb1004s_4() {return cedb1004s_4;}
	public void setCedb1004s_4(List<Cedb1004> l) {this.cedb1004s_4 = l;}
	public List<Cedb1004> getCedb1004s_5() {return cedb1004s_5;}
	public void setCedb1004s_5(List<Cedb1004> l) {this.cedb1004s_5 = l;}
	public String getCompanyName01() {return companyName01;}
	public void setCompanyName01(String s) {this.companyName01 = s;}
	public String getApproveResult01() {return approveResult01;}
	public void setApproveResult01(String s) {this.approveResult01 = s;}
	public String getRemark01() {return remark01;}
	public void setRemark01(String s) {this.remark01 = s;}
	public String getShowSame01() {return showSame01;}
	public void setShowSame01(String s) {this.showSame01 = s;}
	public String getCompanyName02() {return companyName02;}
	public void setCompanyName02(String s) {this.companyName02 = s;}
	public String getApproveResult02() {return approveResult02;}
	public void setApproveResult02(String s) {this.approveResult02 = s;}
	public String getRemark02() {return remark02;}
	public void setRemark02(String s) {this.remark02 = s;}
	public String getShowSame02() {return showSame02;}
	public void setShowSame02(String s) {this.showSame02 = s;}
	public String getCompanyName03() {return companyName03;}
	public void setCompanyName03(String s) {this.companyName03 = s;}
	public String getApproveResult03() {return approveResult03;}
	public void setApproveResult03(String s) {this.approveResult03 = s;}
	public String getRemark03() {return remark03;}
	public void setRemark03(String s) {this.remark03 = s;}
	public String getShowSame03() {return showSame03;}
	public void setShowSame03(String s) {this.showSame03 = s;}
	public String getCompanyName04() {return companyName04;}
	public void setCompanyName04(String s) {this.companyName04 = s;}
	public String getApproveResult04() {return approveResult04;}
	public void setApproveResult04(String s) {this.approveResult04 = s;}
	public String getRemark04() {return remark04;}
	public void setRemark04(String s) {this.remark04 = s;}
	public String getShowSame04() {return showSame04;}
	public void setShowSame04(String s) {this.showSame04 = s;}
	public String getCompanyName05() {return companyName05;}
	public void setCompanyName05(String s) {this.companyName05 = s;}
	public String getApproveResult05() {return approveResult05;}
	public void setApproveResult05(String s) {this.approveResult05 = s;}
	public String getRemark05() {return remark05;}
	public void setRemark05(String s) {this.remark05 = s;}
	public String getShowSame05() {return showSame05;}
	public void setShowSame05(String s) {this.showSame05 = s;}
	public String getApproveRemark() {return approveRemark;}
	public void setApproveRemark(String s) {this.approveRemark = s;}
	public String getIsPrefixForm() {return isPrefixForm;}
	public void setIsPrefixForm(String s) {this.isPrefixForm = s;}
	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String s) {this.prefixFormNo = s;}
	public String getIsOtherForm() {return isOtherForm;}
	public void setIsOtherForm(String s) {this.isOtherForm = s;}
	public String getIsSpec() {return isSpec;}
	public void setIsSpec(String s) {this.isSpec = s;}
	public String getIsOtherSpec() {return isOtherSpec;}
	public void setIsOtherSpec(String s) {this.isOtherSpec = s;}
	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = s;}
	public String getDocType() {return docType;}
	public void setDocType(String s) {this.docType = s;}
	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String s) {this.getKindRemark = s;}
	public String getExtRemitEname() {return extRemitEname;}
	public void setExtRemitEname(String s) {this.extRemitEname = s;}
}
