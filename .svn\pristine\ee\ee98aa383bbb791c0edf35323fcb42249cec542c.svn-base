package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.lucene.document.Document;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.lucene.bo.Hit;
import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.lucene.util.ChineseConverter;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.bo.SearchLog;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE3003 extends SuperBean{

	private String id;
	private String indexKind;
	private String q_cmpyName;
	private int recordCount;
	private String tabId;
	private String sortField;
	private String sortReverse;

	private static final String fullText = "全文檢索";
	private static final String helpText = "輔助查詢";

	public List<Map<String,String>> pagingSearch() throws Exception {
		if(logger.isDebugEnabled()) logger.debug("[pagingSearch]");
		List<Map<String,String>> maplist = new ArrayList<Map<String,String>>();
		boolean waitForSync = false;
		char[] cs = getQ_cmpyName().trim().toCharArray();
		for (char c : cs) {
			List<Cedbc058> objs = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058ObjsByBaseName(String.valueOf(c));
			for ( Cedbc058 obj : objs  ) {
				if ("N".equals(obj.getEnabled())) {
					waitForSync = true;
				}
			}
		}
		if (!waitForSync) {
			SearchResult sr = ServiceGetter
					.getInstance()
					.getIndexSearchService()
					.searchPage(getTabId(), getQ_cmpyName(), getCurrentPage(),
							getPageSize(), getSortField(), getSortReverse());
			
			if(null!=sr) {
				List<Hit> hits = sr.getHits();
				Document doc;
				Map<String,String> map;
				for (Hit hit : hits){
					doc = hit.getDoc();
					map = new HashMap<String,String>();
					map.put("ID",doc.get("ID"));
					map.put("INDEX_TYPE",doc.get("INDEX_TYPE"));
					map.put("COMPANY_NAME",doc.get("COMPANY_NAME"));
					map.put("PREFIX_NO",doc.get("PREFIX_NO"));
					map.put("BAN_NO",doc.get("BAN_NO"));
					map.put("HTML",hit.getHtml());
					map.put("CMPY_STATUS", TcfiView.getStatusName(doc.get("INDEX_TYPE"),doc.get("CMPY_STATUS"),doc.get("ORGN_TYPE"), true));
					map.put("REVOKE_APP_DATE",Common.formatYYYMMDD(TcfiView.checkIndexDate(doc.get("REVOKE_APP_DATE")), 4));
					map.put("RESERVE_DATE",Common.formatYYYMMDD(TcfiView.checkIndexDate(doc.get("RESERVE_DATE")), 4));
					map.put("APPLY_NAME",doc.get("APPLY_NAME"));
					maplist.add(map);
				}
			}
		}
		return maplist;
	}
	
	public Integer searchCount() throws Exception {
		if(logger.isDebugEnabled()) logger.debug("[searchCount]");
		Integer count = 0;
		SearchResult sr = ServiceGetter
				.getInstance()
				.getIndexSearchService()
				.searchCount(getTabId(), getQ_cmpyName());
		if(null!=sr) {
			count = sr.getRecordCount();
			//儲存查詢紀錄
			insertSearchLog(getTabId(), getQ_cmpyName(), sr.getRecordCount(), sr.getCost());
		}
		return count;
	}
	
	public String getQueryStr() {
		StringBuffer result = new StringBuffer();
		String[] tempStrs = ChineseConverter.denormalization(getQ_cmpyName());
		char[] cs = getQ_cmpyName().trim().toCharArray();
		for (char c : cs) {
			List<Cedbc058> objs = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058ObjsByBaseName(String.valueOf(c));
			for ( Cedbc058 obj : objs  ) {
				if ("N".equals(obj.getEnabled())) {
					return "同音同義字"+obj.getSameName()+"及"+obj.getSameName1()+"系統尚在處理中, 請於隔日再行審核";
				}
			}
		}
		if(null!=tempStrs && tempStrs.length>0) {
			for(String tempStr : tempStrs) {
				if(!"".equals(result.toString()))
					result.append(", ");
				result.append(tempStr);
			}
		} else {
			result.append(getQ_cmpyName());
		}
		return result.toString();
	}

	/** 儲存查詢紀錄 */
	private void insertSearchLog(String tabId, String keyword, Integer resultNum, Long processTime) {
		//SearchLog BO
		SearchLog obj = new SearchLog();
		obj.setKind("2".equals(tabId)?fullText:helpText);
		obj.setKeyWord(keyword);
		obj.setResultNum(resultNum);
		obj.setProcessTime(processTime);
		obj.setIdNo(getLoginUserId());	//userId
		obj.setLogDate(Datetime.getYYYMMDD());
		obj.setLogTime(Datetime.getHHMMSS());
		ServiceGetter.getInstance().getIndexSearchService().insertSearchLog(obj);
	}

	public File doExportXls() throws Exception {
		File report = null;
		try{
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/PRE3003R.jasper");
			Map<String, Object> parameters = new HashMap<String,Object>();
			parameters.put("report_title", ("2".equals(getTabId())?fullText:helpText));
			parameters.put("report_cmpy_name", getQueryStr());
			parameters.put("report_count", getRecordCount());

			java.util.ArrayList<Map<String,String>> datas = new java.util.ArrayList<Map<String,String>>();
			//search
			SearchResult sr = ServiceGetter
					.getInstance()
					.getIndexSearchService()
					.searchAll(getTabId(), getQ_cmpyName(), getSortField(), getSortReverse());
			if(null!=sr) {
				setRecordCount(sr.getRecordCount());
				List<Hit> results = sr.getHits();
				int i = 1;
				Map<String,String> map = null;
				for (Hit doc:results){
					map = new HashMap<String,String>(); 
					map.put("NO", String.valueOf(i++));
					map.put("PREFIX_NO", doc.getDoc().get("PREFIX_NO"));
					map.put("BAN_NO", doc.getDoc().get("BAN_NO"));
					map.put("COMPANY_NAME", doc.getDoc().get("COMPANY_NAME"));
					map.put("CMPY_STATUS", TcfiView.getStatusName(doc.getDoc().get("INDEX_TYPE"), doc.getDoc().get("CMPY_STATUS"), doc.getDoc().get("ORGN_TYPE"), false));
					map.put("REVOKE_APP_DATE",Common.formatYYYMMDD(TcfiView.checkIndexDate(doc.getDoc().get("REVOKE_APP_DATE")), 4));
					map.put("RESERVE_DATE", Common.formatYYYMMDD(doc.getDoc().get("RESERVE_DATE"), 4));
					map.put("APPLY_NAME", doc.getDoc().get("APPLY_NAME"));
					datas.add(map);
				}
			}
			report = JasperReportMaker.makeXlsReport(datas, parameters, jasperPath);
			return report;
		}catch( Exception e ) {
	    	e.printStackTrace();
	        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	    }
		return report;
	}

	public void setQ_prefixNo(String prefixNo) {
		Cedb1001 cedb1001 = ServiceGetter.getInstance().getPrefixService().getApprovedCedb1001ByPrefixNo(prefixNo);
		if(null!=cedb1001) {
			//將公司名稱去除組織型態
			setQ_cmpyName(ChineseConverter.getCheckOrgType(cedb1001.getCompanyName()));
		}
	}

	@Override
	public Object doQueryOne() throws Exception {return this;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}

	public String getId() {return checkGet(id);}
	public void setId(String s) {this.id = checkSet(s);}
	public String getIndexKind() {return checkGet(indexKind);}
	public void setIndexKind(String s) {this.indexKind = checkSet(s);}
	public String getQ_cmpyName() {return checkGet(q_cmpyName);}
	public void setQ_cmpyName(String s) {this.q_cmpyName = checkSet(s);}
	public int getRecordCount() {return checkGet(recordCount);}
	public void setRecordCount(int i) {this.recordCount = checkSet(i);}
	public String getTabId() {return checkGet(tabId);}
	public void setTabId(String s) {this.tabId = checkSet(s);}
	public String getSortField() {return checkGet(sortField);}
	public void setSortField(String s) {this.sortField = checkSet(s);}
	public String getSortReverse() {return checkGet(sortReverse);}
	public void setSortReverse(String s) {this.sortReverse = checkSet(s);}

}