package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 同義詞檔(SYNONYM_WORD)
 * VIEW
 */
public class SynonymWord extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	/** 主鍵 */
	private String id;
	/**	字詞  */
	private String word;		
	/**	同義詞  */
	private String synonymWord;	
	/**	詞源依據  */
	private String source;		
	/**	異動人員 */
	private String modIdNo;		
	/**	異動日期  */
	private String modDate;		
	/**	異動時間  */
	private String modTime;
	
	
	public String getWord() {
		return word;
	}
	public void setWord(String word) {
		this.word = word;
	}
	public String getSynonymWord() {
		return synonymWord;
	}
	public void setSynonymWord(String synonymWord) {
		this.synonymWord = synonymWord;
	}
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public String getModIdNo() {
		return modIdNo;
	}
	public void setModIdNo(String modIdNo) {
		this.modIdNo = modIdNo;
	}
	public String getModDate() {
		return modDate;
	}
	public void setModDate(String modDate) {
		this.modDate = modDate;
	}
	public String getModTime() {
		return modTime;
	}
	public void setModTime(String modTime) {
		this.modTime = modTime;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}

	
}