package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 案件歷程檔(FLOW_LOG)
 *
 */
public class FlowLog extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 主鍵值 */
	private Long id;
	/** 預查編號 */
	private String prefixNo;
	/** 承辦人員帳號 */
	private String idNo;
	/** 處理日期 */
	private String processDate;
	/** 處理時間 */
	private String processTime;
	/** 流程代碼 */
	private String processStatus;
	/** 工作日數 */
	private Float workDay;
	/** 異動人員 */
	private String modIdNo;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;

	public Long getId() {return id;}
	public void setId(Long l) {this.id = l;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String s) {this.idNo = s;}
	public String getProcessDate() {return processDate;}
	public void setProcessDate(String s) {this.processDate = s;}
	public String getProcessTime() {return processTime;}
	public void setProcessTime(String s) {this.processTime = s;}
	public String getProcessStatus() {return processStatus;}
	public void setProcessStatus(String s) {this.processStatus = s;}
	public Float getWorkDay() {return workDay;}
	public void setWorkDay(Float f) {this.workDay = f;}
	public String getModIdNo() {return modIdNo;}
	public void setModIdNo(String s) {this.modIdNo = s;}
	public String getModDate() {return modDate;}
	public void setModDate(String s) {this.modDate = s;}
	public String getModTime() {return modTime;}
	public void setModTime(String s) {this.modTime = s;}

}