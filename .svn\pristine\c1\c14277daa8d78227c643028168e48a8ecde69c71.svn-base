package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.lucene.bo.Hit;
import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc058Dao;
import com.kangdainfo.tcfi.model.eicm.dao.IndexLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.service.Pre0004Service;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 同音同義字異動
 */
public class Pre0004ServiceImpl implements Pre0004Service{
	
	public static final String QUEUE_NUM = "PRE0004_QueueNum";		//待重建索引筆數限制'
	public static final String SEARCH_MAX = "PRE0004_SearchMax";	//比對結果筆數限制
	
	@Override
	public synchronized IndexLog getIndexData(IndexLog obj) {
		if(obj == null || "".equals(Common.get(obj.getWsId())))	return null;
		obj = indexLogDao.query(obj);
		if(obj != null){
			//檢查IndexLog待執行的筆數是否『小於』設定的筆數，『小於』才要執行
			if(checkPre0004QueueNum(QUEUE_NUM))
				return ServiceGetter.getInstance().getCom0001Service().Fun_0011(PrefixConstants.INDEX_LOG_STATUS_1, obj.getId(), "");
		}
		return null;
	}
	
	@Override
	public void doBuildIndex(IndexLog obj){
		try {
			String remark = "";
			//檢核做單筆還是全部，預期Param1會是Cedbc058.ID, 有ID做單筆，沒有就做全部(1,2,3)
			java.util.List<Cedbc058> c058List = null;
			if(!"".equals(Common.get(obj.getParam1())))
				c058List = cedbc058Dao.queryByStatus(obj.getParam1(), "1", "2", "3");
			else
				c058List = cedbc058Dao.queryByStatus(null, "1", "2", "3");
			
			//從index查出COMPANY_NAME_BAS or SPECIAL_NAME_BAS 符合 SameName1
			if(c058List != null && c058List.size() > 0){
				remark = batchAppendIndex(c058List);
			}

			//執行完畢，UPDATE INDEX_LOG.STATUS = 2
			ServiceGetter.getInstance().getCom0001Service().Fun_0011(PrefixConstants.INDEX_LOG_STATUS_2, obj.getId(), remark);
		} catch(Exception e) {
			e.printStackTrace();
			//執行失敗，UPDATE INDEX_LOG.STATUS = 3
			ServiceGetter.getInstance().getCom0001Service().Fun_0011(PrefixConstants.INDEX_LOG_STATUS_3, obj.getId(), e.getMessage());
		}
	}
	
	public String batchAppendIndex(java.util.List<Cedbc058> objList) throws Exception{
		String remark = "";
		long count = 0L;
		for(Cedbc058 c: objList){
			if(!"".equals(Common.get(c.getSameName1()))){
				//從index查出COMPANY_NAME_BAS or SPECIAL_NAME_BAS 符合 SameName1
				SearchResult sr = ServiceGetter.getInstance().getIndexSearchService().searchRebuild(c.getSameName1());
				if(null!=sr) {
					//
					if(checkPre0004SearchMax(sr.getRecordCount(), SEARCH_MAX)){
						List<Hit> results = sr.getHits();
						String wsId, indexType, key;
						count += sr.getRecordCount();
						for (Hit doc:results) {
							indexType = doc.getDoc().get("INDEX_TYPE");

							if(PrefixConstants.INDEX_TYPE_1.equals(indexType)) {
								wsId = PrefixConstants.JOB_WS10001;
								key = doc.getDoc().get("BAN_NO");
							} else if(PrefixConstants.INDEX_TYPE_2.equals(indexType)) {
								wsId = PrefixConstants.JOB_WS10002;
								key = doc.getDoc().get("PREFIX_NO");
							} else if(PrefixConstants.INDEX_TYPE_3.equals(indexType)) {
								wsId = PrefixConstants.JOB_WS10002;
								key = doc.getDoc().get("PREFIX_NO");
							} else if(PrefixConstants.INDEX_TYPE_4.equals(indexType)) {
								wsId = PrefixConstants.JOB_WS10002;
								key = doc.getDoc().get("PREFIX_NO");
							} else if(PrefixConstants.INDEX_TYPE_5.equals(indexType)) {
								wsId = PrefixConstants.JOB_WS10004;
								key = doc.getDoc().get("BAN_NO");
							}
							else
								continue;

							//INSERT INDEX_LOG, 由排程去做
							ServiceGetter.getInstance().getCom0001Service().Fun_0010(wsId, key, null, null, null, PrefixConstants.SYS, PrefixConstants.JOB_WS10003);
						}
					}else{
						remark = "檢索結果：異動筆數 " + Common.get(sr.getRecordCount()) + " 大於比對結果筆數限制";
						break;
					}
				}
			}
			remark =  "檢索結果：Success, 異動筆數：" + Common.get(count);
		}
		return remark;
	}
	
	/** 檢查 PRE0004 排程 待重建索引筆數限制 */
	public boolean checkPre0004QueueNum(String codeName) {
		boolean bulidIndex = false;
		Integer indexLogCount = indexLogDao.queryCount();
		SystemCode s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, codeName);	
		if(s != null && !"".equals(Common.get(s.getCodeParam1()))){
			java.util.regex.Matcher isNum = java.util.regex.Pattern.compile("[0-9]*").matcher(s.getCodeParam1());
			if(isNum.matches() ){
				if(indexLogCount.compareTo(Integer.valueOf(s.getCodeParam1())) < 0) 
					bulidIndex = true;
			}
		}
		return bulidIndex;
	}
	
	/** 檢查 PRE0004 排程 比對結果筆數限制 */
	public boolean checkPre0004SearchMax(Integer indexCount, String codeName) {
		boolean bulidIndex = false;
		SystemCode s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, codeName);	
		if(s != null && !"".equals(Common.get(s.getCodeParam1()))){
			java.util.regex.Matcher isNum = java.util.regex.Pattern.compile("[0-9]*").matcher(s.getCodeParam1());
			if(isNum.matches() ){
				if(indexCount.compareTo(Integer.valueOf(s.getCodeParam1())) < 0) 
					bulidIndex = true;
			}
		}
		return bulidIndex;
	}

	private IndexLogDao indexLogDao;
	private Cedbc058Dao cedbc058Dao;
	private SystemCodeDao systemCodeDao;

	public IndexLogDao getIndexLogDao() {return indexLogDao;}
	public void setIndexLogDao(IndexLogDao dao) {this.indexLogDao = dao;}
	public Cedbc058Dao getCedbc058Dao() {return cedbc058Dao;}
	public void setCedbc058Dao(Cedbc058Dao dao) {this.cedbc058Dao = dao;}
	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao systemCodeDao) {this.systemCodeDao = systemCodeDao;}
}