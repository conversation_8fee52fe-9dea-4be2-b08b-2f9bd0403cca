<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4011"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("telixNo")));
String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
try {
	if( !"".equals(prefixNo) || !"".equals(telixNo) ) {
		String checkResult = PRE4011.ajaxCheck(prefixNo, telixNo);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>