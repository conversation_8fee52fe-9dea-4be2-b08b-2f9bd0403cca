package com.kangdainfo.tcfi.loader;

/**
 * 使用者群組
 *
 */
public class SystemCode02Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_02";
	private static final String CODE_KIND = "02";//02:使用者群組
	//singleton
	private static SystemCode02Loader instance;
	public SystemCode02Loader() {
		if (SystemCode02Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode02Loader.instance);
		}
		SystemCode02Loader.instance = this;
	}
	public static SystemCode02Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}