package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1027;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1017Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao;
import com.kangdainfo.tcfi.service.Pre2003Service;

public class Pre2003ServiceImpl implements Pre2003Service {
	
	Cedb1023Dao cedb1023Dao;
	Cedb1000Dao cedb1000Dao;
	Cedb1027Dao cedb1027Dao;
	Cedb1017Dao cedb1017Dao;
	
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public Cedb1027Dao getCedb1027Dao() {return cedb1027Dao;}
	public void setCedb1027Dao(Cedb1027Dao dao) {this.cedb1027Dao = dao;}
	public Cedb1017Dao getCedb1017Dao() {return cedb1017Dao;}
	public void setCedb1017Dao(Cedb1017Dao dao) {this.cedb1017Dao = dao;}
	
	public int doSave( Cedb1023 cedb1023, Cedb1027 cedb1027, Cedb1000 cedb1000, String cedb1023Flag, String funCode) throws Exception {
		// 先找cedb1023內有沒有prefixNo這個預查編號
	    // 沒有的話就新增一筆; 有的話就更新receiveName, receiveAddr這兩個欄位
		if ( "insert".equals( cedb1023Flag ) ) {
			cedb1023Dao.insert(cedb1023) ;
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end if
		else if ( "update".equals( cedb1023Flag )) {
			cedb1023Dao.set(cedb1023) ;
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end else if
		
		if (cedb1027Dao.insert(cedb1027) == 0) {
			throw new MoeaException("公文掛號重複!請重新輸入");
		} // end if	
		cedb1000Dao.setWhenPre2003(cedb1000) ;
				
	    return 0 ;
	}
	
	public int doUpdate( Cedb1023 cedb1023, Cedb1027 cedb1027, Cedb1000 cedb1000, String cedb1023Flag, String funCode) throws Exception {
		// 先找cedb1023內有沒有prefixNo這個預查編號
	    // 沒有的話就新增一筆; 有的話就更新receiveName, receiveAddr這兩個欄位
		if ( "insert".equals( cedb1023Flag ) ) {
			cedb1023Dao.insert(cedb1023) ;
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end if
		else if ( "update".equals( cedb1023Flag )) {
			cedb1023Dao.set(cedb1023) ;
			//寫入個資軌跡TrackLog(CEDB1023)
			ServiceGetter.getInstance().getTrackLogService().doUpateTrack(funCode, cedb1023);
		} // end else if
		
		cedb1027Dao.update(cedb1027); 
		cedb1000Dao.setWhenPre2003(cedb1000) ;
				
	    return 0 ;
	}
	
	public int doAssign( Cedb1017 cedb1017 ) throws Exception{
		// 給過的號碼寫回cedb1017
		cedb1017Dao.update(cedb1017) ;
		return 0 ;	
	}
	
	public int rollBack(Cedb1000 cedb1000, Cedb1027 cedb1027) throws Exception {
		List<Cedb1027> list = cedb1027Dao.findByPrefixNo(cedb1000.getPrefixNo());
		if (list.size() == 1) {
			cedb1000Dao.setWhenPre2003(cedb1000);
		}
		cedb1027Dao.delete(cedb1027);
		return 0;
	}
	
	public List<Cedb1027> getCedb1027List(String prefixNo) {
		return cedb1027Dao.findByPrefixNo(prefixNo);
	}

	public Cedb1027 getCedb1027ByPrefixNo(String prefixNo) {
		return cedb1027Dao.findLastOneByPrefixNo(prefixNo);
	}

}