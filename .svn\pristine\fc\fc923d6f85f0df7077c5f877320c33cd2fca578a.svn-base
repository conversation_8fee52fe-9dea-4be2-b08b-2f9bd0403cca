<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String codeKind = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(codeKind))
	{
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND ENABLE='Y' ORDER BY SORTED, CODE");
		sqljob.addParameter(codeKind);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>