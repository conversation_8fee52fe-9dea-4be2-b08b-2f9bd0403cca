package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc054;

public class Cedbc054Dao extends BaseDaoJdbc implements RowMapper<Cedbc054> {

	private static final String SQL_findAll = "SELECT * FROM CEDBC054";
	public List<Cedbc054> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedbc054>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	private static final String SQL_findByAreaCode = "SELECT * FROM CEDBC054 WHERE AREA_CODE = ?";
	/**
	 * 取得鄉鎮代碼、鄉鎮名，query key：areaCode
	 * @param areaCode String
	 * @throws Exception
	 * @return Cedbc054
	 */
	public Cedbc054 findByAreaCode(String areaCode) {
		SQLJob sqljob = new SQLJob(SQL_findByAreaCode);
		sqljob.addParameter(areaCode);
		if (logger.isDebugEnabled())
			logger.debug(sqljob);

		List<Cedbc054> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);

		return list.isEmpty() ? null : list.get(0);
	}

	public Cedbc054 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc054 obj = null;
		if(null!=rs) {
			obj = new Cedbc054();
			obj.setAreaCode(rs.getString("AREA_CODE"));
			obj.setAreaName(rs.getString("AREA_NAME"));
		}
		return obj;
	}

}