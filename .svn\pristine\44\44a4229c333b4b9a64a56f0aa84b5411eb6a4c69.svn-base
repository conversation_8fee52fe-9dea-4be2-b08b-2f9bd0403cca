package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.FlowLog;

public class FlowLogDao extends BaseDaoJdbc implements RowMapper<FlowLog>{

	public void insert(FlowLog obj){
		if(obj != null){
			SQLJob sqljob = new SQLJob("INSERT INTO FLOW_LOG (");
			sqljob.appendSQL(" PREFIX_NO,ID_NO,PROCESS_DATE,PROCESS_TIME,PROCESS_STATUS");
			sqljob.appendSQL(",WORK_DAY,MOD_ID_NO,MOD_DATE,MOD_TIME");
			sqljob.appendSQL(") VALUES (");
			sqljob.appendSQL(" ?,?,?,?,?");
			sqljob.appendSQL(",?,?,?,?");
			sqljob.appendSQL(")");
			sqljob.addParameter(obj.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getIdNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getProcessDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getProcessTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getProcessStatus());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getWorkDay());
			sqljob.addSqltypes(java.sql.Types.NUMERIC);
			sqljob.addParameter(obj.getModIdNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getModDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(obj.getModTime());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}
	
	@Override
	public FlowLog mapRow(ResultSet rs, int arg1) throws SQLException {
		FlowLog obj = null;
		if(null!=rs) {
			obj = new FlowLog();
			obj.setId(rs.getLong("ID"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setProcessDate(rs.getString("PROCESS_DATE"));
			obj.setProcessTime(rs.getString("PROCESS_TIME"));
			obj.setProcessStatus(rs.getString("PROCESS_STATUS"));
			obj.setWorkDay(rs.getFloat("WORK_DAY"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}

}