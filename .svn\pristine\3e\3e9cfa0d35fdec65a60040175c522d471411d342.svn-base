package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdArea;

public class CsmdAreaDao extends BaseDaoJdbc implements RowMapper<CsmdArea> {

	private static final String SQL_defaultOrder = "order by AREA_CODE";
	
	private static final String SQL_findAll = "select * from icms.CSMD_AREA where enable = 'Y'";
	public List<CsmdArea> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findCities = "select * from icms.csmd_area where enable = 'Y' and area_attrib = 1";
	public List<CsmdArea> findCities() {
		SQLJob sqljob = new SQLJob(SQL_findCities);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findTowns = "select * from icms.csmd_area where enable = 'Y' and area_attrib = 2 and up_area_code = ?";
	public List<CsmdArea> findTowns(String upAreaCode) {
		SQLJob sqljob = new SQLJob(SQL_findTowns);
		sqljob.addParameter(upAreaCode);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdArea mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdArea obj = null;
		if(null!=rs) {
			obj = new CsmdArea();
			obj.setAreaCode(rs.getString("AREA_CODE"));
			obj.setAreaCode4(rs.getString("AREA_CODE_4"));
			obj.setAreaName(rs.getString("AREA_NAME"));
			obj.setZipCode(rs.getString("ZIP_CODE"));
			obj.setAreaAttrib(rs.getString("AREA_ATTRIB"));
			obj.setUpAreaCode(rs.getString("UP_AREA_CODE"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
		}
		return obj;
	}

}