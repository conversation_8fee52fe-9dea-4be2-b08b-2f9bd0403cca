package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1002;

public class Eedb1002Dao extends BaseDaoJdbc implements RowMapper<Eedb1002> {

	public List<Eedb1002> findByPrefixNo(String prefixNo) {
		String sql = "SELECT * FROM Eedb1002 WHERE PREFIX_NO = ? ORDER BY SEQ_NO";
		Object[] parameters = {prefixNo};
		return (List<Eedb1002>) getJdbcTemplate().query(sql, parameters, this);
	}

	public Eedb1002 findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		String sql = "SELECT * FROM Eedb1002 WHERE PREFIX_NO = ? AND SEQ_NO = ?";
		Object[] parameters = {prefixNo, seqNo};
		List<Eedb1002> Eedb1002s = getJdbcTemplate().query(sql, parameters, this);
		return Eedb1002s.isEmpty() ? null : Eedb1002s.get(0);
	}
	
	public List<Eedb1002> findByTelixNo(String telixNo) {
		String sql = "SELECT * FROM EEDB1002 WHERE TELIX_NO = ?";
		Object[] parameters = {telixNo};
		
		return (List<Eedb1002>) getJdbcTemplate().query(sql, parameters, this);
	}
	
	private static String sql_saveByObj = "INSERT INTO Eedb1002(TELIX_NO, BUSI_ITEM, BUSI_ITEM_NO, PREFIX_NO, SEQ_NO) "
			+ "VALUES (?, ?, ?, ?, ?) ";

	public int insert(Eedb1002 eedb1002) {
		if (eedb1002 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);
		
		sqljob.addParameter(eedb1002.getTelixNo());
		sqljob.addParameter(eedb1002.getBusiItem());
		sqljob.addParameter(eedb1002.getBusiItemNo());
		sqljob.addParameter(eedb1002.getPrefixNo());
		sqljob.addParameter(eedb1002.getSeqNo() == null ? "" : eedb1002.getSeqNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	public void deleteByPrefixNoAndBusiItemNo(String prefixNo, String busiItemNo) {
		
		if(prefixNo != null && busiItemNo != null) {
			
			SQLJob sqljob = new SQLJob("DELETE FROM Eedb1002 ");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.appendSQL("AND BUSI_ITEM_NO = ?");
			sqljob.addParameter(prefixNo);
			sqljob.addParameter(busiItemNo);
			
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	private int[] getSqlTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}
	
	public void updateClosed(String prefixNo) {
		SQLJob deljob = new SQLJob("DELETE FROM EEDB1002 WHERE PREFIX_NO = ?");
		deljob.addParameter(prefixNo);
		deljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(deljob);
		getJdbcTemplate().update(deljob.getSQL(), deljob.getParametersArray(), deljob.getSqltypesArray());

		SQLJob insjob = new SQLJob("");
		insjob.appendSQL(" INSERT INTO EEDB1002 (TELIX_NO,PREFIX_NO,SEQ_NO,BUSI_ITEM_NO,BUSI_ITEM)");
		insjob.appendSQL(" SELECT");
		insjob.appendSQL("  cedb1000.telix_no");
		insjob.appendSQL(" ,cedb1000.prefix_no");
		insjob.appendSQL(" ,cedb1002.seq_no");
		insjob.appendSQL(" ,cedb1002.busi_item_no");
		insjob.appendSQL(" ,cedb1002.busi_item");
		insjob.appendSQL(" FROM cedb1002, cedb1000");
		insjob.appendSQL(" WHERE cedb1002.prefix_no = cedb1000.prefix_no");
		insjob.appendSQL(" AND cedb1000.prefix_no = ?");
		insjob.appendSQL(" AND cedb1000.telix_no not like 'O%'");//一站式案件不處理
		insjob.addParameter(prefixNo);
		insjob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(insjob);
		getJdbcTemplate().update(insjob.getSQL(), insjob.getParametersArray(), insjob.getSqltypesArray());
	}

	@Override
	public Eedb1002 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb1002 obj = null;
		if(null!=rs) {
			obj = new Eedb1002();
			obj.setPrefixNo(rs.getString("TELIX_NO"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBusiItem(rs.getString("BUSI_ITEM"));
			obj.setBusiItemNo(rs.getString("BUSI_ITEM_NO"));
		}
		return obj;
	}

}
