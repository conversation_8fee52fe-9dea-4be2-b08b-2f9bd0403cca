<!-- 

程式目的：承辦案件列表
程式代號：pre4004
程式日期：1030612
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------

-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4004">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4004" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
}else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report){
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4004.pdf");
		out.clear();
		out = pageContext.pushBody();
	}else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function init(){
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_date,"分文日期");
	alertStr += checkDate(form1.q_date,"分文日期") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var date = form1.q_date.value;
		var idNo = form1.q_idNo.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4004.jsp?date='+date+'&idNo='+idNo);
		if ( x == 'ok'  )
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE4004_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';
				break;
			case "doQueryAll":
				checkField();
				$('#state').val("queryAll") ;
				form1.submit();
				break;
			case "doClear":
				form1.q_idNo.value = "";
				form1.q_date.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function queryOne() {
	
}
</script>
</head>
<!-- Form area -->

<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4004'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>  
			<td class="td_form" width="10%">承辦人：</td>
			<td class="td_form_white" width="15%">
				<select name="q_idNo" id="q_idNo" value="<%=obj.getQ_idNo()%>">
					<%=View.getOptionStaffName( obj.getQ_idNo(), false, 1) %>
				</select>
			</td>
			<td class="td_form" width="10%">分文日期：</td>
			<td class="td_form_white"> 
				<%=View.getPopCalendar("field_Q","q_date",obj.getQ_date()) %>
				
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    <th class="listTH" width="8%" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">申請人</a></th>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">預查種類</a></th>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">核覆結果</a></th>
    <th class="listTH"            ><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">預查名稱</a></th>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">保留期限</a></th>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">狀況</a></th>
    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">分文日期</a></th>
    <th class="listTH" width="8%" ><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">承辦人</a></th> 
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false,false,false,false,false,false,false,false};
  boolean displayArray[] = {true,true,true,true,true,true,true,true,true};
  String[] alignArray = {"center", "center","center","center","left","center","center","center","center"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",false, null, null, "",true, true));
  %>
  </tbody>
</table>
</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>