package com.kangdainfo.tcfi.lucene.service;

import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;


/**
 * 更新檢索檔
 */
public interface IndexUpdateService {

	/** 開始更新 */
	public IndexLog doStartUpdate(String wsId);

	/** 執行更新 */
	public IndexLog doUpdateIndex(IndexLog indexLog);
	
	/** 結束更新 */
	public void doEndUpdate(IndexLog indexLog);

	/** 重新執行索引失敗的排程 */
	public void doRetryError();
	
	/** 備份IndexLog到IndexLogH */
	public void doBackupIndexLog();

}