package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司預查申請案郵寄資料檔(for 沒預查編號的案件)
 *
 */
public class PostRecord extends BaseModel {
	private static final long serialVersionUID = 1L;
	private int id;
	/** 預查編號 */
	private String prefixNo;
	/** 掛號編號 */
	private String postNo;
	/** 掛號種類 */
	private String postType;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人id */
	private String applyId;
	/** 申請人電話 */
	private String applyTel;
	/** 申請人地址 */
	private String applyAddr;
	/** 公司名稱 */
	private String companyName;
	/** 收件人姓名 */
	private String getName;
	/** 收件人地址 */
	private String getAddr;
	/** 領件方式 */
	private String getKind;
	/** 領件方式註記 */
	private String getKindRemark;
	/** 取件日期 */
	private String getDate;
	/** 取件時間 */
	private String getTime;
	/** 退件日期 */
	private String backDate;
	/** 退件時間 */
	private String backTime;
	/** 退件原因 */
	private String backReason;
	/** 其他退件原因 */
	private String backReasonRemark;
	/** 處理方式 */
	private String otherMethod;
	/** 其他處理方式 */
	private String otherMethodRemark;
	
	private String modIdNo;
	private String modDate;
	private String modTime;
	
	
	public int getId() {return id;}
	public void setId(int id) {this.id = id;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getPostNo() {return postNo;}
	public void setPostNo(String postNo) {this.postNo = postNo;}
	public String getPostType() {return postType;}
	public void setPostType(String postType) {this.postType = postType;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String applyName) {this.applyName = applyName;}
	public String getApplyId() {return applyId;}
	public void setApplyId(String applyId) {this.applyId = applyId;}
	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String applyTel) {this.applyTel = applyTel;}
	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String applyAddr) {this.applyAddr = applyAddr;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getGetName() {return getName;}
	public void setGetName(String getName) {this.getName = getName;}
	public String getGetAddr() {return getAddr;}
	public void setGetAddr(String getAddr) {this.getAddr = getAddr;}
	
	public String getGetKind() {return getKind;}
	public void setGetKind(String getKind) {this.getKind = getKind;}
	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String getKindRemark) {this.getKindRemark = getKindRemark;}
	
	public String getGetDate() {return getDate;}
	public void setGetDate(String getDate) {this.getDate = getDate;}
	public String getGetTime() {return getTime;}
	public void setGetTime(String getTime) {this.getTime = getTime;}
	public String getBackDate() {return backDate;}
	public void setBackDate(String backDate) {this.backDate = backDate;}
	public String getBackTime() {return backTime;}
	public void setBackTime(String backTime) {this.backTime = backTime;}
	public String getBackReason() {return backReason;}
	public void setBackReason(String backReason) {this.backReason = backReason;}
	public String getBackReasonRemark() {return backReasonRemark;}
	public void setBackReasonRemark(String backReasonRemark) {this.backReasonRemark = backReasonRemark;}
	public String getOtherMethod() {return otherMethod;}
	public void setOtherMethod(String otherMethod) {this.otherMethod = otherMethod;}
	public String getOtherMethodRemark() {return otherMethodRemark;}
	public void setOtherMethodRemark(String otherMethodRemark) {this.otherMethodRemark = otherMethodRemark;}
	public String getModIdNo() {return modIdNo;}
	public void setModIdNo(String modIdNo) {this.modIdNo = modIdNo;}
	public String getModDate() {return modDate;}
	public void setModDate(String modDate) {this.modDate = modDate;}
	public String getModTime() {return modTime;}
	public void setModTime(String modTime) {this.modTime = modTime;}
	
}
