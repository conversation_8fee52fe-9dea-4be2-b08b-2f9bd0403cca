package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.util.report.JasperReportMaker;

/*
 程式目的：承辦人員處理案件統計
 程式代號：pre4006
 撰寫日期：103.04.28
 程式作者：
 --------------------------------------------------------
 修改作者　　修改日期　　　修改目的
 --------------------------------------------------------
 */
public class PRE4006 extends SuperBean {

	private String q_DateStart;
	private String q_DateEnd;
	private String q_Type; // 判斷是依分文或收文日期查詢

	public String getQ_DateStart() {return checkGet(q_DateStart);}
	public void setQ_DateStart(String s) {q_DateStart = checkSet(s);}
	public String getQ_DateEnd() {return checkGet(q_DateEnd);}
	public void setQ_DateEnd(String s) {q_DateEnd = checkSet(s);}
	public String getQ_Type() {return checkGet(q_Type);}
	public void setQ_Type(String s) {q_Type = checkSet(s);}
	
	/** 承辦人 */
	private String staffName;
	public String getStaffName() {return checkGet(staffName);}
	public void setStaffName(String s) {this.staffName = checkSet(s);}
	/** 承辦件數 */
	private Integer caseNum;
	public Integer getCaseNum() {return checkGet(caseNum);}
	public void setCaseNum(Integer i) {this.caseNum = checkSet(i);}
	/** 平均處理日數 */
	private Double average;
	public Double getAverage() {return checkGet(average);}
	public void setAverage(Double d) {this.average = checkSet(d);}
	/** 完成件數 */
	private Integer closeNum;
	public Integer getCloseNum() {return checkGet(closeNum);}
	public void setCloseNum(Integer i) {this.closeNum = checkSet(i);}
	/** 未結件數 */
	private Integer uncloseNum;
	public Integer getUncloseNum() {return checkGet(uncloseNum);}
	public void setUncloseNum(Integer i) {this.uncloseNum = checkSet(i);}
	/** 1日內完成件數 */
	private Integer c1;
	public Integer getC1() {return checkGet(c1);}
	public void setC1(Integer i) {this.c1 = checkSet(i);}
	/** 1到2日 */
	private Integer c2;
	public Integer getC2() {return checkGet(c2);}
	public void setC2(Integer i) {this.c2 = checkSet(i);}
	/** 2到2.5日 */
	private Integer c3;
	public Integer getC3() {return checkGet(c3);}
	public void setC3(Integer i) {this.c3 = checkSet(i);}
	/** 2.5到3日 */
	private Integer c4;
	public Integer getC4() {return checkGet(c4);}
	public void setC4(Integer i) {this.c4 = checkSet(i);}
	/** 3到3.5日 */
	private Integer c5;
	public Integer getC5() {return checkGet(c5);}
	public void setC5(Integer i) {this.c5 = checkSet(i);}
	/** 3.5到4日 */
	private Integer c6;
	public Integer getC6() {return checkGet(c6);}
	public void setC6(Integer i) {this.c6 = checkSet(i);}
	/** 4到5日 */
	private Integer c7;
	public Integer getC7() {return checkGet(c7);}
	public void setC7(Integer i) {this.c7 = checkSet(i);}
	/** 5到10日 */
	private Integer c8;
	public Integer getC8() {return checkGet(c8);}
	public void setC8(Integer i) {this.c8 = checkSet(i);}
	/** 10日以上  */
	private Integer c9;
	public Integer getC9() {return checkGet(c9);}
	public void setC9(Integer i) {this.c9 = checkSet(i);}

	/** 列印前檢核 */
	public String checkForJsp() {
		Integer count = ServiceGetter.getInstance().getPre4006Service().countDataList(getQ_DateStart(), getQ_DateEnd(), getQ_Type());
		if(null!=count && count > 0)
			return "ok";
		else
			return "查無資料，請變更查詢條件！";
	}

	public ArrayList<?> doQueryAll() throws Exception {
		DecimalFormat numFormat = new DecimalFormat("#,###");
		DecimalFormat doubleFormat = new DecimalFormat("0.00");
		
		java.util.ArrayList<String[]> dataList = new java.util.ArrayList<String[]>();
		List<PRE4006> datas = ServiceGetter.getInstance().getPre4006Service().queryDataList(getQ_DateStart(), getQ_DateEnd(), getQ_Type());
		if(null!=datas && !datas.isEmpty()) {
			String[] rowArray = new String[14];
			// 小計加總
			Integer totalCaseNum = 0;
			Integer totalCloseNum = 0;
			Integer totalUncloseNum = 0;
			Double  totalAverage = 0d;
			Integer totalC1 = 0;
			Integer totalC2 = 0;
			Integer totalC3 = 0;
			Integer totalC4 = 0;
			Integer totalC5 = 0;
			Integer totalC6 = 0;
			Integer totalC7 = 0;
			Integer totalC8 = 0;
			Integer totalC9 = 0;
			for(PRE4006 data : datas) {
				rowArray = new String[14];
				rowArray[0] = Common.get(data.getStaffName());
				rowArray[1] = numFormat.format(data.getCaseNum());
				rowArray[2] = doubleFormat.format(data.getAverage());
				rowArray[3] = numFormat.format(data.getCloseNum());
				rowArray[4] = numFormat.format(data.getUncloseNum());
				rowArray[5] = numFormat.format(data.getC1());
				rowArray[6] = numFormat.format(data.getC2());
				rowArray[7] = numFormat.format(data.getC3());
				rowArray[8] = numFormat.format(data.getC4());
				rowArray[9] = numFormat.format(data.getC5());
				rowArray[10] = numFormat.format(data.getC6());
				rowArray[11] = numFormat.format(data.getC7());
				rowArray[12] = numFormat.format(data.getC8());
				rowArray[13] = numFormat.format(data.getC9());
				dataList.add(rowArray);
				
				//小計加總
				totalCaseNum += data.getCaseNum();
				totalCloseNum += data.getCloseNum();
				totalUncloseNum += data.getUncloseNum();
				totalAverage += data.getAverage()*data.getCaseNum();
				totalC1 += data.getC1();
				totalC2 += data.getC2();
				totalC3 += data.getC3();
				totalC4 += data.getC4();
				totalC5 += data.getC5();
				totalC6 += data.getC6();
				totalC7 += data.getC7();
				totalC8 += data.getC8();
				totalC9 += data.getC9();
			}
			
			// 小計
			rowArray = new String[14];
			rowArray[0] = "小計";
			rowArray[1] = numFormat.format(totalCaseNum);
			rowArray[2] = doubleFormat.format(totalAverage / (totalCaseNum == 0 ? 1 : totalCaseNum));
			rowArray[3] = numFormat.format(totalCloseNum);
			rowArray[4] = numFormat.format(totalUncloseNum);
			rowArray[5] = numFormat.format(totalC1);
			rowArray[6] = numFormat.format(totalC2);
			rowArray[7] = numFormat.format(totalC3);
			rowArray[8] = numFormat.format(totalC4);
			rowArray[9] = numFormat.format(totalC5);
			rowArray[10] = numFormat.format(totalC6);
			rowArray[11] = numFormat.format(totalC7);
			rowArray[12] = numFormat.format(totalC8);
			rowArray[13] = numFormat.format(totalC9);
			dataList.add(rowArray);
			
			setErrorMsg("查詢成功!");
		} else {
			setErrorMsg("查無承辦人辦結件數 ，請變更查詢條件！");
		}
		return dataList;
	}

	public ArrayList<?> queryNotAssign() throws Exception {
		java.util.ArrayList<String[]> dataList = ServiceGetter.getInstance().getPre4006Service().queryNotAssign(getQ_DateStart(), getQ_DateEnd(), getQ_Type());
		if(null!=dataList && !dataList.isEmpty()) {
			//
		} else {
			setErrorMsg("查無未分文件數，請變更查詢條件");
		}
		return dataList;
	}

	public File doPrintPdf() {
		File report = null;
		try {
			// 報表樣板
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4006.jasper");
			// 參數
			Map<String, Object> parameters = new HashMap<String, Object>();
			parameters.put("printDate", "列印日期：" + Common.formatYYYMMDD(Datetime.getYYYMMDD(), 3));
			parameters.put("printTime", "列印時間：" + Common.formatHHMMSS(Datetime.getHHMMSS(), 2));
			parameters.put("date", "收文日期：" + Common.formatYYYMMDD(getQ_DateStart(), 3) + "～" + Common.formatYYYMMDD(getQ_DateEnd(), 3));

			java.util.ArrayList<String[]> dataList = new java.util.ArrayList<String[]>();
			//java.util.ArrayList<String[]> dataList = ServiceGetter.getInstance().getPre4006Service().queryDataList(getQ_DateStart(), getQ_DateEnd(), getQ_Type());
			if (dataList != null && dataList.size() > 0) {
				ArrayList<Map<String, String>> dataMaps = new ArrayList<Map<String, String>>();
				Map<String, String> dataMap = null;
				for (String[] data : dataList) {
					if (data.length == 14) {
						if ("小計".equals(data[0])) {
							parameters.put("string0", data[0]);
							parameters.put("string1", data[1]);
							parameters.put("string2", data[2]);
							parameters.put("string3", data[3]);
							parameters.put("string4", data[4]);
							parameters.put("string5", data[5]);
							parameters.put("string6", data[6]);
							parameters.put("string7", data[7]);
							parameters.put("string8", data[8]);
							parameters.put("string9", data[9]);
							parameters.put("string10", data[10]);
							parameters.put("string11", data[11]);
							parameters.put("string12", data[12]);
							parameters.put("string13", data[13]);
						} else {
							dataMap = new HashMap<String, String>();
							dataMap.put("staffName", data[0]);
							dataMap.put("c1", data[1]);
							dataMap.put("c2", data[2]);
							dataMap.put("c3", data[3]);
							dataMap.put("c4", data[4]);
							dataMap.put("c5", data[5]);
							dataMap.put("c6", data[6]);
							dataMap.put("c7", data[7]);
							dataMap.put("c8", data[8]);
							dataMap.put("c9", data[9]);
							dataMap.put("c10", data[10]);
							dataMap.put("c11", data[11]);
							dataMap.put("c12", data[12]);
							dataMap.put("c13", data[13]);
							dataMaps.add(dataMap);
						}
					}
				}
				dataList.clear();
				dataList = ServiceGetter.getInstance().getPre4006Service().queryNotAssign(getQ_DateStart(), getQ_DateEnd(), getQ_Type());
				if (dataList != null && dataList.size() > 0) {
					String[] data = dataList.get(0);
					// 避免queryNotAssign()與報表不一致
					if (data.length == 7) {
						parameters.put("na1", data[0]);
						parameters.put("na2", data[1]);
						parameters.put("na3", data[2]);
						parameters.put("na4", data[3]);
						parameters.put("na5", data[4]);
						parameters.put("na6", data[5]);
						parameters.put("na7", data[6]);
					}
				}
				// 產出報表
				report = JasperReportMaker.makePdfReport(dataMaps, parameters, jasperPath);
			} else {
				setErrorMsg("查無資料，請變更查詢條件！");
			}
		} catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage() != null && e.getMessage().length() < 200)
				setErrorMsg(e.getMessage());
			else
				setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		} // catch

		return report;
	} // doPrintfPdf()

	public void doCreate() throws Exception {} // end doCreate()
	public void doUpdate() throws Exception {} // end doUpdate()
	public void doDelete() throws Exception {} // end doDelete()
	public Object doQueryOne() throws Exception {return null;} // end doQueryOne()

} // PPE4006()