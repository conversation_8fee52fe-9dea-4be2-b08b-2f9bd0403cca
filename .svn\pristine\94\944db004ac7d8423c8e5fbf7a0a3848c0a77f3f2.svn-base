package com.kangdainfo.tcfi.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyQueryVo;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyShareholderVo;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.tcfi.model.icms.dao.CsmdCtrlitemDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBusiItemDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBussMainDao;
import com.kangdainfo.tcfi.service.Pre3013Service;
import com.kangdainfo.util.report.JasperReportMaker;

public class Pre3013ServiceImpl implements Pre3013Service {
	private Logger logger = Logger.getLogger(this.getClass());
	
	public void doRecover(String banNo) throws Exception {
		if(logger.isInfoEnabled()) logger.info("[doRecover][banNo:"+banNo+"]");
		GeneralityBusitem generBO = ServiceGetter.getInstance().getNoPayMarkService().getByBanNo(banNo);
		if(null!=generBO) {
			String telixNo = generBO.getTelixNo();
			if(null!=telixNo && !"0000".equals(telixNo) && !"".equals(telixNo) ) {
				ServiceGetter.getInstance().getPrefixService().saveOssmFeeMainByTelixNo(telixNo);
			}
		}
		ServiceGetter.getInstance().getNoPayMarkService().updateNotUsed(banNo);
	}

	public CmpyQueryVo findLmsQueryVo(String banNo) {
		
		if(logger.isInfoEnabled()) logger.info("[findCmpyQueryVo][banNo:"+banNo+"]");
		CmpyQueryVo vo = null;
		LmsmBussMain mainvo = lmsmBussMainDao.queryLmsInfoByBanNo(banNo);
		if(null!=mainvo) {
			vo = new CmpyQueryVo();
			vo.setBanNo(Common.get(mainvo.getBanNo()));
			vo.setCompanyName(Common.get(mainvo.getLmsName()));
			vo.setPrefixNo(Common.get(mainvo.getPrefixNo()));
			vo.setSpecialName("");
			vo.setOrgType(Common.get(mainvo.getOrganType()));
			vo.setOrgName(Common.get(mainvo.getOrganName()));
			vo.setRespName(Common.get(mainvo.getRepName()));
			vo.setCompanyStatus(Common.get(mainvo.getStatusName()));
			vo.setRegUnit(Common.get(mainvo.getRegUnitCode()));
			vo.setRegUnitName(Common.get(mainvo.getAgencyName()));
			vo.setCompanyTel(Common.get(mainvo.getTelno()));
			vo.setCompanyAddr(Common.get(mainvo.getBusiAddr()));
			vo.setCapitalAmount(Common.get(mainvo.getRegisterFunds()));
			vo.setRealAmt(Common.get(mainvo.getRealFunds()));
			vo.setInvestmentCode(Common.get(mainvo.getInvestCode()));
			vo.setSetupDate(Common.get(mainvo.getApproveDate()));
			vo.setApproveNo(Common.get(mainvo.getApproveNo()));
			vo.setChangeDate(Common.get(mainvo.getLastChangeDate()));
			vo.setChangeNo(Common.get(mainvo.getLastChgNo()));
			
			vo.setCedb2002s(lmsBusiItemDao.findCedb2002ByBanNo(banNo));
			vo.setShareholders(queryShareholdersByBanNo(mainvo.getRegUnitCode(), mainvo.getBanNo(), mainvo.getTbpk()));
			vo.setNoNeedPay(ServiceGetter.getInstance().getNoPayMarkService().getNoPayMark(banNo));
			vo.setChinaCode(Common.get(mainvo.getChinaCode()));
			
			List<?> branchList = lmsmBussMainDao.queryBranchByCapiBanNo(mainvo.getBanNo());
			
			int size = 0;
			if (branchList != null)
				size = branchList.size();
			vo.setTotBranch(String.valueOf(size));
		}

		return vo;
	}

	private List<CmpyShareholderVo> queryShareholdersByBanNo(String regUnitCode, String banNo, String parpk) {
		List<CmpyShareholderVo> list = null;
		if(!"".equals(Common.get(banNo))) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT ");
			sqljob.appendSQL(" DUTY_CODE, NAME, ID_NO ");
			sqljob.appendSQL(" ,DECODE(DUTY_CODE , '01', '代表人', '02', '普通合夥人', '03','有限合夥人', '07', '經理人' ) AS POS_CODE_NAME ");
			sqljob.appendSQL(" FROM lms.LMSM_DIRECTOR WHERE REG_UNIT_CODE = ? AND BAN_NO = ? AND PARPK = ? ORDER BY DUTY_CODE");
			sqljob.addParameter(regUnitCode);
			sqljob.addParameter(banNo);
			sqljob.addParameter(parpk);
			List<Map<String,Object>> maps = ServiceGetter.getInstance().getIcmsGeneralQueryDao().queryForList(sqljob);
			if(null!=maps && !maps.isEmpty()) {
				list = new ArrayList<CmpyShareholderVo>();
				CmpyShareholderVo vo;
				for(Map<String,Object> map : maps) {
					vo = new CmpyShareholderVo();
					vo.setSortNo(Common.get(map.get("DUTY_CODE")));
					vo.setName(Common.get(map.get("NAME")));
					vo.setIdNo(Common.get(map.get("ID_NO")));
					vo.setPositionName(Common.get(map.get("POS_CODE_NAME")));
					vo.setCorpName("");
					list.add(vo);
				}
			}
		}
		return list;
	}

	public File generateRptPdf(String banNo) throws Exception {
		if(logger.isInfoEnabled()) logger.info("[generateRptPdf][banNo:"+banNo+"]");
		File report = null;
		try {
	        String reportPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre3008.jasper");

	        Map<String, Object> parameters = new HashMap<String,Object>();
	        parameters.put("printDate", "列印日期："+Datetime.formatRocDate(Datetime.getYYYMMDD()));
	        parameters.put("printTime", "列印時間："+Datetime.formatRocTime(Datetime.getHHMMSS()));
	        parameters.put("org", "limit");

	        CmpyQueryVo vo = findLmsQueryVo(banNo);
	        if(null!=vo) {
	        	//format
	        	//vo.setCapitalAmount( Common.MoneyFormat(vo.getCapitalAmount(),0,true) );
	        	//vo.setRealAmt( Common.MoneyFormat(vo.getRealAmt(),0,true) );
	        	vo.setSetupDate(Datetime.formatRocDate(vo.getSetupDate()));
	        	vo.setChangeDate(Datetime.formatRocDate(vo.getChangeDate()));
	        }
			List<CmpyQueryVo> dataList = new ArrayList<CmpyQueryVo>();
			dataList.add(vo);
			report = JasperReportMaker.makePdfReport(dataList, parameters, reportPath);
		} catch (Exception e) {
	    	e.printStackTrace();
	    	throw e;
		}
	    return report;
	}
	
	private LmsmBusiItemDao lmsBusiItemDao;
	public LmsmBusiItemDao getLmsBusiItemDao() {return lmsBusiItemDao;}
	public void setLmsBusiItemDao(LmsmBusiItemDao dao) {this.lmsBusiItemDao = dao;}
	

	private LmsmBussMainDao lmsmBussMainDao;
	public LmsmBussMainDao getLmsmBussMainDao() {return lmsmBussMainDao;}
	public void setLmsmBussMainDao(LmsmBussMainDao dao) {this.lmsmBussMainDao = dao;}
	
	private CsmdCtrlitemDao csmdCtrlitemDao;
	public CsmdCtrlitemDao getCsmdCtrlitemDao() {return csmdCtrlitemDao;}
	public void setCsmdCtrlitemDao(CsmdCtrlitemDao dao) {this.csmdCtrlitemDao = dao;}

}