<!-- 
程式目的：分文區間查詢
程式代號：pre4021
程式日期：1060126
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4021">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4021"/>
</jsp:include>

<%
if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
	objList = obj.queryAll();	
} // end if
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init() {
}

function doSearch() {
	form1.state.value = "queryAll";
	form1.submit();
}

function queryOne() {
	
}

</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='<%=obj.getProgID()%>'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- form area -->
<tr><td class="bg">
    <div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%" cellspacing="0" cellpadding="0">
		<input class="field_RO" type="hidden" id="progID" size="10" value="<%=obj.getProgID()%>">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<tr>
			<td class="td_form" width="100px">已發文號碼：</td>
			<td class="td_form_white"><font color="red"><%=obj.getLastClosedPrefix()%></font>
           		<input class="field_RO" type="hidden" name="lastClosedPrefix" size="10" color="red" maxlength="10" value="<%=obj.getLastClosedPrefix()%>">
           		
			</td>
			<td class="td_form" width="140px">尚未分文案件累計：</td>
			<td class="td_form_white"><font color="blue"><%=obj.getNotAssign()%></font> 
				<input class="field_RO" type="hidden" name="notAssign" size="10" maxlength="10" value="<%=obj.getNotAssign()%>">
			</td>
			<td class="td_form" width="140px">尚未分文起訖號：</td>
			<td class="td_form_white"><font color="black"><%=obj.getStartPrefix()%>~<%=obj.getEndPrefix()%></font>
				<input class="field_RO" type="hidden" name="startPrefix" size="10" maxlength="10" value="<%=obj.getStartPrefix()%>">
	           	<input class="field_RO" type="hidden" name="endPrefix" size="10" maxlength="10" value="<%=obj.getEndPrefix()%>">
			</td>
		</tr> 
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
<tr>
	查詢日期起訖：<input type="text" class="field" id="q_yearMonthDateStart" name="q_yearMonthDateStart" maxlength="7" size="9" value="<%=obj.getQ_yearMonthDateStart()%>">~<input type="text" class="field" id="q_yearMonthDateEnd" name="q_yearMonthDateEnd" maxlength="7" size="9" value="<%=obj.getQ_yearMonthDateEnd()%>">
	<input class="toolbar_default" type="button" id="btnQuery" name="btnQuery" value="查詢" onclick="doSearch()">
</tr>
<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
	<div id="listContainer" height="auto">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
		<thead id="listTHEAD"> 
			<tr>
			    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">承辦人</a></th>
			    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">本區間已分文件數</a></th>
			</tr>
		</thead>
		<tbody id="listTBODY">
		  <%
		  boolean primaryArray[] = {true,false};
		  boolean displayArray[] = {true,true};
		  String[] alignArray = {"center", "center"};
		  //boolean linkArray[] = {false,false,false,false};
		  out.write(View.getQuerylist(primaryArray, displayArray, alignArray, objList, obj.getQueryAllFlag(), false, null, null, "", true, true));
		  %>
		</tbody>
	</table>
	</div>
</td></tr>

</table>
</form>
</body>
</html>