<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>"%>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(request.getParameter("q"));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		com.kangdainfo.tcfi.model.eicm.bo.PrefixQueryVo vo = gson.fromJson(q, com.kangdainfo.tcfi.model.eicm.bo.PrefixQueryVo.class);
		String msg = ServiceGetter.getInstance().getPre2001Service().resetCloseDate(vo.getPrefixNo(), User.getUserId());
		out.write(gson.toJson(msg));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>