package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyMemoInfo;;

public class PRE8007 extends SuperBean{
	
	private String q_id;
	private String q_rcvNo;
	private String q_reserveDateS;
	private String q_reserveDateE;
	private String q_companyName;

	private String id;
	private String rcvNo;
	private String reserveDate;
	private String companyName;
	
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}	
	public String getQ_rcvNo() {return checkGet(q_rcvNo);}
	public void setQ_rcvNo(String q_rcvNo) {this.q_rcvNo = checkSet(q_rcvNo);}
	public String getQ_reserveDateS() {return checkGet(q_reserveDateS);}
	public void setQ_reserveDateS(String q_reserveDateS) {this.q_reserveDateS = checkSet(q_reserveDateS);}
	public String getQ_reserveDateE() {return checkGet(q_reserveDateE);}
	public void setQ_reserveDateE(String q_reserveDateE) {this.q_reserveDateE = checkSet(q_reserveDateE);}
	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String q_companyName) {this.q_companyName = checkSet(q_companyName);}

	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getRcvNo() {return checkGet(rcvNo);}
	public void setRcvNo(String rcvNo) {this.rcvNo = checkSet(rcvNo);}
	public String getReserveDate() {return checkGet(reserveDate);}
	public void setReserveDate(String reserveDate) {this.reserveDate = checkSet(reserveDate);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String companyName) {this.companyName = checkSet(companyName);}

	@Override
	public Object doQueryOne() throws Exception {
		PRE8007 obj = this;	
		CmpyMemoInfo s = ServiceGetter.getInstance().getPrefixService().getCmpyMemoInfoByRcvNo(getId());
		if (s != null){
			obj.setId(s.getRcvNo());
			obj.setRcvNo(s.getRcvNo());
			obj.setCompanyName(s.getCompanyName());
			obj.setReserveDate(s.getReserveDate());
		}else{
			this.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}

	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<CmpyMemoInfo> objList = ServiceGetter.getInstance().getPrefixService().getCmpyMemoInfoByCondition(
				getQ_rcvNo(), getQ_companyName(), getQ_reserveDateS(), getQ_reserveDateE());
		
		if (objList != null && objList.size() > 0){
			for(CmpyMemoInfo dtl : objList){
				String[] rowArray = new String[4];
				rowArray[0] = dtl.getRcvNo();
				rowArray[1] = dtl.getRcvNo();
				rowArray[2] = Common.formatDateTime(dtl.getReserveDate(), 4);
				rowArray[3] = dtl.getCompanyName();  
				arrList.add(rowArray);
			}
			objList.clear();
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}

	@Override
	public void doCreate() throws Exception {
		
		if("".equals(Common.get(getRcvNo())) || "".equals(Common.get(getCompanyName())) || "".equals(Common.get(getReserveDate())))
			throw new MoeaException("資料有誤，請重新輸入!!");
		
		if(getUpdateCheck()){
			CmpyMemoInfo obj = new CmpyMemoInfo();	
			obj.setRcvNo(getRcvNo());
			obj.setCompanyName(getCompanyName());
			obj.setReserveDate(getReserveDate());
			obj.setEnable("Y");
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj.setUpdateUser(getLoginUserId());
			ServiceGetter.getInstance().getPrefixService().insertCmpyMemoInfo(obj);
		}else{
			throw new MoeaException("已存在相同預查編號!!");
		}
	}

	@Override
	public void doUpdate() throws Exception {
		
		if("".equals(Common.get(getRcvNo())) || "".equals(Common.get(getCompanyName())) || "".equals(Common.get(getReserveDate())))
			throw new MoeaException("資料有誤，請重新輸入!!");
		if(getUpdateCheck()){
			CmpyMemoInfo obj = new CmpyMemoInfo();
			obj.setId(getId());
			obj.setRcvNo(getRcvNo());
			obj.setCompanyName(getCompanyName());
			obj.setReserveDate(getReserveDate());
			obj.setEnable("Y");
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj.setUpdateUser(getLoginUserId());
			ServiceGetter.getInstance().getPrefixService().updateCmpyMemoInfo(obj);
		}else{
			throw new MoeaException("該預查編號已存在，請重新輸入!!");
		}
	}

	@Override
	public void doDelete() throws Exception {
		CmpyMemoInfo obj = ServiceGetter.getInstance().getPrefixService().getCmpyMemoInfoByRcvNo(getId());
		if(obj != null){
			ServiceGetter.getInstance().getPrefixService().deleteCmpyMemoInfo(obj.getRcvNo());
		}else{
			throw new MoeaException("查無資料，無法刪除，請重新操作 !");
		}
	}
	
	/** 檢核 ID 是否重複 */
	protected boolean getUpdateCheck(){
		CmpyMemoInfo o = ServiceGetter.getInstance().getPrefixService().getCmpyMemoInfoByRcvNo(getRcvNo());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getRcvNo().equals(this.getId()))
				return true;
		}
		return false;
	}

}
