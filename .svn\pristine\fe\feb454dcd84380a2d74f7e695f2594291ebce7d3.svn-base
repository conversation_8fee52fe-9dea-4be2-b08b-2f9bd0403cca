package com.kangdainfo.tcfi.model.eicm.bo;

import java.util.Date;

/**
 * 預查案件收據資料檔(PREFIX_RECEIPT_NO)
 *
 */
public class PrefixReceiptNo {
	/** 預查編號 */
	private String prefixNo;
	/** 電子流水號 */
	private String telixNo;
	/** 收據編號(一站式收據號直接帶入, 其他依當時收據號取用) */
	private String receiptNo;
	/** 收據類別(0:一站式, 1:郵寄及現場) */
	private String receiptType;
	/** 付款方式(0:現金, 1:匯票, 2:信用卡, 3:金融卡, 4:金融帳戶, 5:支票) */
	private String payType;
	/** 付款日期 */
	private String payDate;
	/** 支匯票單據號 */
	private String chNo;
	/** 付款金額(一站式直接帶入金額(應為150),郵寄及現場固定帶300) */
	private int amount;
	/** 是否退費(Y:退費 N/Null:否) */
	private String refund;
	/** 付款人姓名 */
	private String payName;
	/** 收件人姓名 */
	private String recipientName;
	/** 收件人地址 */
	private String recipientAddr;
	/** 退還書編號 */
	private String returnNo;
	/** 退費日期 */
	private Date returnDate;
	/** 退費人員 */
	private String returnUser;
	/** 退費類別(1:撤件,2:檢還,3:撤回退費,4:退費) */
	private String returnType;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String s) {this.telixNo = s;}
	public String getReceiptNo() {return receiptNo;}
	public void setReceiptNo(String s) {this.receiptNo = s;}
	public String getReceiptType() {return receiptType;}
	public void setReceiptType(String s) {this.receiptType = s;}
	public String getPayType() {return payType;}
	public void setPayType(String s) {this.payType = s;}
	public String getPayDate() {return payDate;}
	public void setPayDate(String s) {this.payDate = s;}
	public String getChNo() {return chNo;}
	public void setChNo(String s) {this.chNo = s;}
	public int getAmount() {return amount;}
	public void setAmount(int n) {this.amount = n;}
	public String getRefund() {return refund;}
	public void setRefund(String s) {this.refund = s;}
	public String getPayName() {return payName;}
	public void setPayName(String s) {this.payName = s;}
	public String getRecipientName() {return recipientName;}
	public void setRecipientName(String s) {this.recipientName = s;}
	public String getRecipientAddr() {return recipientAddr;}
	public void setRecipientAddr(String s) {this.recipientAddr = s;}
	public String getReturnNo() {return returnNo;}
	public void setReturnNo(String s) {this.returnNo = s;}
	public Date getReturnDate() {return returnDate;}
	public void setReturnDate(Date d) {this.returnDate = d;}
	public String getReturnUser() {return returnUser;}
	public void setReturnUser(String s) {this.returnUser = s;}
	public String getReturnType() {return returnType;}
	public void setReturnType(String s) {this.returnType = s;}
}