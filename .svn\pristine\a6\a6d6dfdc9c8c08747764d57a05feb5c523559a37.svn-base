package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;

public class Cedb1006Dao extends BaseDaoJdbc implements RowMapper<Cedb1006> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1006 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1006> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByBanNo = "SELECT * FROM CEDB1006 WHERE BAN_NO = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1006> findByBanNo(String banNo) {
		SQLJob sqljob = new SQLJob(SQL_findByBanNo);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByApplyId = "SELECT * FROM CEDB1006 WHERE APPLY_ID = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1006> findByApplyId(String applyId) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyId);
		sqljob.addParameter(applyId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findByComposite = "SELECT * FROM CEDB1006 WHERE PREFIX_NO = ? AND UPDATE_ID_NO = ? AND UPDATE_DATE = ? AND UPDATE_TIME = ?";
	public Cedb1006 findByComposite(String prefixNo, String updateIdNo, String updateDate, String updateTime) {
		SQLJob sqljob = new SQLJob(SQL_findByComposite);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(updateIdNo);
		sqljob.addParameter(updateDate);
		sqljob.addParameter(updateTime);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1006> cedb1006s = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		
		return cedb1006s.isEmpty() ? null : cedb1006s.get(0);
	}
	
	private static final String SQL_findByApplyName = "SELECT * FROM CEDB1006 WHERE APPLY_NAME = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1006> findByApplyName(String applyName) {
		SQLJob sqljob = new SQLJob(SQL_findByApplyName);
		sqljob.addParameter(applyName);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	/**
	 * 依照公司名稱來找
	 * 原公司名
	 * 原公司名+有限公司
	 * 原公司名+股份有限公司
	 * 原公司名+無限公司
	 * 原公司名+兩合公司
	 */
	private static final String SQL_findByCompanyNames = "SELECT * FROM CEDB1006 WHERE COMPANY_NAME = ? OR COMPANY_NAME = ? OR COMPANY_NAME = ? OR COMPANY_NAME = ? OR COMPANY_NAME = ? ORDER BY PREFIX_NO, UPDATE_DATE, UPDATE_TIME";
	public List<Cedb1006> findByCompanyName(String[] companyNames) {
		SQLJob sqljob = new SQLJob(SQL_findByCompanyNames);
		
		for(String companyName : companyNames) {
			sqljob.addParameter(companyName);
		}
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public int insert(Cedb1006 cedb1006) {
		if (cedb1006 == null)
			return 0;
		SQLJob sqljob = new SQLJob("INSERT INTO Cedb1006(");
		sqljob.appendSQL("PREFIX_NO,BAN_NO,APPLY_KIND,APPLY_NAME,APPLY_ID,");
		sqljob.appendSQL("APPLY_ADDR,APPLY_TEL,ATTOR_NAME,ATTOR_NO,ATTOR_ADDR,");
		sqljob.appendSQL("ATTOR_TEL,GET_KIND,APPLY_TYPE,COMPANY_NAME,TELIX_NO,");
		sqljob.appendSQL("RECEIVE_DATE,RECEIVE_TIME,APPROVE_DATE,APPROVE_TIME,APPROVE_RESULT,");
		sqljob.appendSQL("RESERVE_MARK,RESERVE_DATE,GET_DATE,GET_TIME,SPECIAL_NAME,");
		sqljob.appendSQL("COMPANY_STUS,REG_UNIT,REMARK,ASSIGN_DATE,ASSIGN_TIME,");
		sqljob.appendSQL("ID_NO,STAFF_NAME,UPDATE_CODE,CODE_NO,CODE_NAME,");
		sqljob.appendSQL("UPDATE_ID_NO,UPDATE_DATE,UPDATE_TIME,REG_DATE,CONTROL_CD1,");
		sqljob.appendSQL("CONTROL_CD2,ZONE_CODE,APPROVE_MARK,OLD_COMPANY_NAME,REMARK1,");
		sqljob.appendSQL("PREFIX_STATUS,RESERVE_DAYS,APPROVE_REMARK,IS_PREFIX_FORM,PREFIX_FORM_NO,");
		sqljob.appendSQL("IS_OTHER_FORM,IS_SPEC,GET_KIND_REMARK,IS_OTHER_SPEC,OTHER_SPEC_REMARK,");
		sqljob.appendSQL("DOC_TYPE,EXTEND_MARK,RCV_CHECK,ATONCE_TYPE,REFUND_NO,");
		sqljob.appendSQL("EXTEND_REASON,EXTEND_OTHER,EXTEND_DATE,OTHER_REASON,CLOSE_DATE,");
		sqljob.appendSQL("CLOSE_TIME, EXT_REMIT_ENAME");
		sqljob.appendSQL(")VALUES(");
		sqljob.appendSQL("?,?,?,?,?,");//5
		sqljob.appendSQL("?,?,?,?,?,");//10
		sqljob.appendSQL("?,?,?,?,?,");//15
		sqljob.appendSQL("?,?,?,?,?,");//20
		sqljob.appendSQL("?,?,?,?,?,");//25
		sqljob.appendSQL("?,?,?,?,?,");//30
		sqljob.appendSQL("?,?,?,?,?,");//35
		sqljob.appendSQL("?,?,?,?,?,");//40
		sqljob.appendSQL("?,?,?,?,?,");//45
		sqljob.appendSQL("?,?,?,?,?,");//50
		sqljob.appendSQL("?,?,?,?,?,");//55
		sqljob.appendSQL("?,?,?,?,?,");//60
		sqljob.appendSQL("?,?,?,?,?,");//65
		sqljob.appendSQL("?, ?");
		sqljob.appendSQL(")");
		//1
		sqljob.addParameter(cedb1006.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApplyKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApplyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApplyId());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//6
		sqljob.addParameter(cedb1006.getApplyAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApplyTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAttorName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAttorNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAttorAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//11
		sqljob.addParameter(cedb1006.getAttorTel());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getGetKind());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApplyType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//16
		sqljob.addParameter(cedb1006.getReceiveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getReceiveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApproveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApproveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//21
		sqljob.addParameter(cedb1006.getReserveMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getReserveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getGetDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getGetTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getSpecialName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//26
		sqljob.addParameter(cedb1006.getCompanyStus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRegUnit());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAssignDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAssignTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//31
		sqljob.addParameter(cedb1006.getIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getStaffName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getUpdateCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getCodeNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getCodeName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//36
		sqljob.addParameter(cedb1006.getUpdateIdNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getUpdateDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getUpdateTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRegDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getControlCd1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//41
		sqljob.addParameter(cedb1006.getControlCd2());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getZoneCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getApproveMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getOldCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRemark1());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//46
		sqljob.addParameter(cedb1006.getPrefixStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getReserveDays());
		sqljob.addSqltypes(java.sql.Types.NUMERIC);
		sqljob.addParameter(cedb1006.getApproveRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getIsPrefixForm());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getPrefixFormNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//51
		sqljob.addParameter(cedb1006.getIsOtherForm());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getIsSpec());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getGetKindRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getIsOtherSpec());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getOtherSpecRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//56
		sqljob.addParameter(cedb1006.getDocType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getExtendMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRcvCheck());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getAtonceType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getRefundNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//61
		sqljob.addParameter(cedb1006.getExtendReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getExtendOther());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getExtendDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getOtherReason());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getCloseDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		//66
		sqljob.addParameter(cedb1006.getCloseTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(cedb1006.getExtRemitEname());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1006 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1006 obj = null;
		if(null!=rs) {
			obj = new Cedb1006();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setApplyKind(rs.getString("APPLY_KIND"));
			obj.setApplyName(rs.getString("APPLY_NAME"));
			obj.setApplyId(rs.getString("APPLY_ID"));
			obj.setApplyAddr(rs.getString("APPLY_ADDR"));
			obj.setApplyTel(rs.getString("APPLY_TEL"));
			obj.setAttorName(rs.getString("ATTOR_NAME"));
			obj.setAttorNo(rs.getString("ATTOR_NO"));
			obj.setAttorAddr(rs.getString("ATTOR_ADDR"));
			obj.setAttorTel(rs.getString("ATTOR_TEL"));
			obj.setGetKind(rs.getString("GET_KIND"));
			obj.setApplyType(rs.getString("APPLY_TYPE"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setReceiveDate(rs.getString("RECEIVE_DATE"));
			obj.setReceiveTime(rs.getString("RECEIVE_TIME"));
			obj.setApproveDate(rs.getString("APPROVE_DATE"));
			obj.setApproveTime(rs.getString("APPROVE_TIME"));
			obj.setApproveResult(rs.getString("APPROVE_RESULT"));
			obj.setReserveMark(rs.getString("RESERVE_MARK"));
			obj.setReserveDate(rs.getString("RESERVE_DATE"));
			obj.setGetDate(rs.getString("GET_DATE"));
			obj.setGetTime(rs.getString("GET_TIME"));
			obj.setSpecialName(rs.getString("SPECIAL_NAME"));
			obj.setCompanyStus(rs.getString("COMPANY_STUS"));
			obj.setRegUnit(rs.getString("REG_UNIT"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setAssignDate(rs.getString("ASSIGN_DATE"));
			obj.setAssignTime(rs.getString("ASSIGN_TIME"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setStaffName(rs.getString("STAFF_NAME"));
			obj.setUpdateCode(rs.getString("UPDATE_CODE"));
			obj.setCodeNo(rs.getString("CODE_NO"));
			obj.setCodeName(rs.getString("CODE_NAME"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setRegDate(rs.getString("REG_DATE"));
			obj.setControlCd1(rs.getString("CONTROL_CD1"));
			obj.setControlCd2(rs.getString("CONTROL_CD2"));
			obj.setZoneCode(rs.getString("ZONE_CODE"));
			obj.setApproveMark(rs.getString("APPROVE_MARK"));
			obj.setOldCompanyName(rs.getString("OLD_COMPANY_NAME"));
			obj.setRemark1(rs.getString("REMARK1"));
			obj.setPrefixStatus(rs.getString("PREFIX_STATUS"));
			obj.setReserveDays(rs.getInt("RESERVE_DAYS"));
			obj.setApproveRemark(rs.getString("APPROVE_REMARK"));
			obj.setIsPrefixForm(rs.getString("IS_PREFIX_FORM"));
			obj.setPrefixFormNo(rs.getString("PREFIX_FORM_NO"));
			obj.setIsOtherForm(rs.getString("IS_OTHER_FORM"));
			obj.setIsSpec(rs.getString("IS_SPEC"));
			obj.setGetKindRemark(rs.getString("GET_KIND_REMARK"));
			obj.setIsOtherSpec(rs.getString("IS_OTHER_SPEC"));
			obj.setOtherSpecRemark(rs.getString("OTHER_SPEC_REMARK"));
			obj.setDocType(rs.getString("DOC_TYPE"));
			obj.setExtendMark(rs.getString("EXTEND_MARK"));
			obj.setRcvCheck(rs.getString("RCV_CHECK"));
			obj.setAtonceType(rs.getString("ATONCE_TYPE"));

			obj.setRefundNo(Common.get(rs.getString("REFUND_NO")));
			obj.setExtendReason(Common.get(rs.getString("EXTEND_REASON")));
			obj.setExtendOther(Common.get(rs.getString("EXTEND_OTHER")));
			obj.setExtendDate(Common.get(rs.getString("EXTEND_DATE")));
			obj.setOtherReason(Common.get(rs.getString("OTHER_REASON")));

			obj.setCloseDate(Common.get(rs.getString("CLOSE_DATE")));
			obj.setCloseTime(Common.get(rs.getString("CLOSE_TIME")));
			
			obj.setExtRemitEname(Common.get(rs.getString("EXT_REMIT_ENAME")));
		}
		return obj;
	}
}
