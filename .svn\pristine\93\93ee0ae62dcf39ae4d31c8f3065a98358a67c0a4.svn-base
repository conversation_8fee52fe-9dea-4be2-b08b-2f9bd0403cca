package com.kangdainfo.tcfi.model.eicm.bo;

import java.util.List;

import com.kangdainfo.persistence.BaseModel;

/**
 * 案件異動紀錄
 *
 */
public class PrefixLogVo extends BaseModel {

	private static final long serialVersionUID = 1L;

	private List<Cedb1007> cedb1007s;
	private List<Cedb1008> cedb1008s;
	private List<Cedb1010> cedb1010s;

	/** 預查編號 */
	private String prefixNo;
	/** 統一編號 */
	private String banNo;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人身分證統一編號 */
	private String applyId;
	/** 申請人地址 */
	private String applyAddr;
	/** 申請人聯絡電話 */
	private String applyTel;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 代理人事務所所在地 */
	private String attorAddr;
	/** 代理人聯絡電話 */
	private String attorTel;
	/** 案件性質 */
	private String applyType;
	/** 公司名稱 */
	private String companyName;
	/** 網路收文號 */
	private String telixNo;
	/** 收件日期 */
	private String receiveDate;
	/** 收件時間 */
	private String receiveTime;
	/** 核覆日期 */
	private String approveDate;
	/** 核覆時間 */
	private String approveTime;
	/** 核覆結果 */
	private String approveResult;
	/** 延展保留期限註記 */
	private String reserveMark;
	/** 保留期限 */
	private String reserveDate;
	/** 取件日期 */
	private String getDate;
	/** 取件時間 */
	private String getTime;
	/** 特取名稱 */
	private String specialName;
	/** 預查公司狀態 */
	private String companyStus;
	/** 申登機關 */
	private String regUnit;
	/** 檢還/撤件註記說明 */
	private String remark;
	/** 分文日期 */
	private String assignDate;
	/** 分文時間 */
	private String assignTime;
	/** 承辦人員識別碼 */
	private String idNo;
	/** 承辦人員姓名 */
	private String staffName;
	/** 異動人員識別碼 */
	private String updateIdNo;
	/** 異動日期 */
	private String updateDate;
	/** 異動時間 */
	private String updateTime;
	/** 申請／撤銷申請登記日期 */
	private String regDate;
	/** 郵遞區號 */
	private String zoneCode;
	/** 檢還/撤件註記 */
	private String approveMark;
	/** 結案日期 */
	private String closeDate;
	/** 結案時間 */
	private String closeTime;
	/** 工作天數 */
	private Float workDay;
	/** 備註 */
	private String remark1;
	/** 原公司名稱 */
	private String oldCompanyName;
	/** 案件狀態 */
	private String prefixStatus;
	/** 保留天數 */
	private Integer reserveDays;	
	/** 退費公文號 */
	private String refundNo;
	/** 展期原因 */
	private String extendReason;
	/** 展期其他原因 */
	private String extendOther;
	/** 展期日期 */
	private String extendDate;

	/** 申辦方式 */
	private String applyWay;
	/** 前次異動者 */
	private String updateName;
	/** 核覆結果 */
	private String approveResultDesc;
	/** 案件狀態 */
	private String prefixStatusDesc;
	/** 收件日期時間 */
	private String receiveDateTime;
	/** 收文登打日期時間 */
	private String receiveKeyinDateTime;
	/** 分文日期時間 */
	private String assignDateTime;
	/** 審核日期時間 */
	private String approveDateTime;
	/** 發文登打日期時間 */
	private String issueKeyinDateTime;
	/** 發文日期時間 */
	private String closeDateTime;
	/** 領件日期時間 */
	private String getDateTime;
	/** 案件提示文字 */
	private String reserveTip;

	/** 收件人地址 */
	private String receiverAddr;
	/** 收件人姓名 */
	private String receiverName;
	/** 接收簡訊手機號碼 */
	private String contactCel;
	/** 領件方式 */
	private String getKindDesc;
	/** 預查種類 */
	private String applyKindDesc;
	/** 所代表法人 */
	private String applyLawName;
	/** 法人統編 */
	private String applyBanNo;
	
	/** 異動日期時間 */
	private String updateDateTime;

	public List<Cedb1007> getCedb1007s() {return cedb1007s;}
	public void setCedb1007s(List<Cedb1007> l) {this.cedb1007s = l;}

	public List<Cedb1008> getCedb1008s() {return cedb1008s;}
	public void setCedb1008s(List<Cedb1008> l) {this.cedb1008s = l;}

	public List<Cedb1010> getCedb1010s() {return cedb1010s;}
	public void setCedb1010s(List<Cedb1010> l) {this.cedb1010s = l;}

	public String getApplyWay() {return applyWay;}
	public void setApplyWay(String s) {this.applyWay = s;}

	public String getUpdateName() {return updateName;}
	public void setUpdateName(String s) {this.updateName = s;}

	public String getReceiveDateTime() {return receiveDateTime;}
	public void setReceiveDateTime(String s) {this.receiveDateTime = s;}

	public String getReceiveKeyinDateTime() {return receiveKeyinDateTime;}
	public void setReceiveKeyinDateTime(String s) {this.receiveKeyinDateTime = s;}

	public String getAssignDateTime() {return assignDateTime;}
	public void setAssignDateTime(String s) {this.assignDateTime = s;}

	public String getApproveDateTime() {return approveDateTime;}
	public void setApproveDateTime(String s) {this.approveDateTime = s;}

	public String getIssueKeyinDateTime() {return issueKeyinDateTime;}
	public void setIssueKeyinDateTime(String s) {this.issueKeyinDateTime = s;}

	public String getCloseDateTime() {return closeDateTime;}
	public void setCloseDateTime(String s) {this.closeDateTime = s;}

	public String getGetDateTime() {return getDateTime;}
	public void setGetDateTime(String s) {this.getDateTime = s;}

	public String getReserveTip() {return reserveTip;}
	public void setReserveTip(String s) {this.reserveTip = s;}

	public String getApproveResultDesc() {return approveResultDesc;}
	public void setApproveResultDesc(String s) {this.approveResultDesc = s;}

	public String getPrefixStatusDesc() {return prefixStatusDesc;}
	public void setPrefixStatusDesc(String s) {this.prefixStatusDesc = s;}

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String s) {this.applyAddr = s;}
	public String getApplyId() {return applyId;}
	public void setApplyId(String s) {this.applyId = s;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String s) {this.applyName = s;}
	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String s) {this.applyTel = s;}
	public String getApplyType() {return applyType;}
	public void setApplyType(String s) {this.applyType = s;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String s) {this.approveDate = s;}
	public String getApproveMark() {return approveMark;}
	public void setApproveMark(String s) {this.approveMark = s;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String s) {this.approveResult = s;}
	public String getApproveTime() {return approveTime;}
	public void setApproveTime(String s) {this.approveTime = s;}
	public String getAssignDate() {return assignDate;}
	public void setAssignDate(String s) {this.assignDate = s;}
	public String getAssignTime() {return assignTime;}
	public void setAssignTime(String s) {this.assignTime = s;}
	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String s) {this.attorAddr = s;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}
	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String s) {this.attorNo = s;}
	public String getAttorTel() {return attorTel;}
	public void setAttorTel(String s) {this.attorTel = s;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getCloseDate() {return closeDate;}
	public void setCloseDate(String s) {this.closeDate = s;}
	public String getCloseTime() {return closeTime;}
	public void setCloseTime(String s) {this.closeTime = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getCompanyStus() {return companyStus;}
	public void setCompanyStus(String s) {this.companyStus = s;}
	public String getGetDate() {return getDate;}
	public void setGetDate(String s) {this.getDate = s;}
	public String getGetTime() {return getTime;}
	public void setGetTime(String s) {this.getTime = s;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}
	public String getPrefixStatus() {return prefixStatus;}
	public void setPrefixStatus(String s) {this.prefixStatus = s;}
	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String s) {this.receiveDate = s;}
	public String getReceiveTime() {return receiveTime;}
	public void setReceiveTime(String s) {this.receiveTime = s;}
	public String getRegDate() {return regDate;}
	public void setRegDate(String s) {this.regDate = s;}
	public String getRegUnit() {return regUnit;}
	public void setRegUnit(String s) {this.regUnit = s;}
	public String getRemark() {return remark;}
	public void setRemark(String s) {this.remark = s;}
	public String getRemark1() {return remark1;}
	public void setRemark1(String s) {this.remark1 = s;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String s) {this.reserveDate = s;}
	public Integer getReserveDays() {return reserveDays;}
	public void setReserveDays(Integer s) {this.reserveDays = s;}
	public String getReserveMark() {return reserveMark;}
	public void setReserveMark(String s) {this.reserveMark = s;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String s) {this.specialName = s;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String s) {this.staffName = s;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String s) {this.telixNo = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateIdNo() {return updateIdNo;}
	public void setUpdateIdNo(String s) {this.updateIdNo = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}
	public Float getWorkDay() {return workDay;}
	public void setWorkDay(Float workDay) {this.workDay = workDay;}
	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String s) {this.zoneCode = s;}
	public String getRefundNo() {return refundNo;}
	public void setRefundNo(String s) {this.refundNo = s;}
	public String getExtendReason() {return extendReason;}
	public void setExtendReason(String s) {this.extendReason = s;}
	public String getExtendOther() {return extendOther;}
	public void setExtendOther(String s) {this.extendOther = s;}
	public String getExtendDate() {return extendDate;}
	public void setExtendDate(String s) {this.extendDate = s;}
	public String getReceiverAddr() {return receiverAddr;}
	public void setReceiverAddr(String s) {this.receiverAddr = s;}
	public String getReceiverName() {return receiverName;}
	public void setReceiverName(String s) {this.receiverName = s;}
	public String getContactCel() {return contactCel;}
	public void setContactCel(String s) {this.contactCel = s;}
	public String getGetKindDesc() {return getKindDesc;}
	public void setGetKindDesc(String s) {this.getKindDesc = s;}
	public String getApplyKindDesc() {return applyKindDesc;}
	public void setApplyKindDesc(String s) {this.applyKindDesc = s;}
	public String getUpdateDateTime() {return updateDateTime;}
	public void setUpdateDateTime(String s) {this.updateDateTime = s;}
	public String getApplyLawName() {return applyLawName;}
	public void setApplyLawName(String s) {this.applyLawName = s;}
	public String getApplyBanNo() {return applyBanNo;}
	public void setApplyBanNo(String s) {this.applyBanNo = s;}

}