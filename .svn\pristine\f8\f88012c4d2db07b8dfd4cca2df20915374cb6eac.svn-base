package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenuAuth;
import com.kangdainfo.util.lang.CommonStringUtils;

public class FunctionMenuAuthDao extends BaseDaoJdbc implements RowMapper<FunctionMenuAuth> {

	private static final String SQL_findByPk = "SELECT * FROM FUNCTION_MENU_AUTH WHERE GROUP_ID = ? AND FUNCTION_MENU_ID = ?";
	public FunctionMenuAuth findByPk(String groupId, Integer functionMenuId) {
		//check pk
		if(CommonStringUtils.isEmpty(groupId)) return null;
		if(null==functionMenuId) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPk);
		sqljob.addParameter(groupId);
		sqljob.addParameter(functionMenuId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<FunctionMenuAuth> list = (List<FunctionMenuAuth>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()?null:list.get(0);
	}

	private static final String SQL_findByGroupId = "SELECT * FROM FUNCTION_MENU_AUTH WHERE GROUP_ID = ?";
	public List<FunctionMenuAuth> findByGroupId(String groupId) {
		if(null==groupId) return null;
		SQLJob sqljob = new SQLJob(SQL_findByGroupId);
		sqljob.addParameter(groupId);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<FunctionMenuAuth>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public FunctionMenuAuth insert(FunctionMenuAuth bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getGroupId())) return null;
		if(null==bo.getFunctionMenuId()) return null;
		//check exist
		FunctionMenuAuth t = findByPk(bo.getGroupId(), bo.getFunctionMenuId());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO FUNCTION_MENU_AUTH (");
		sqljob.appendSQL("GROUP_ID");
		sqljob.appendSQL(",FUNCTION_MENU_ID");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(") VALUES (?,?,?,?,?)");
		sqljob.addParameter(bo.getGroupId());
		sqljob.addParameter(bo.getFunctionMenuId());
		sqljob.addParameter(bo.getModId());
		sqljob.addParameter(Datetime.getYYYMMDD());
		sqljob.addParameter(Datetime.getHHMMSS());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.NUMERIC,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR}
				);
		return findByPk(bo.getGroupId(), bo.getFunctionMenuId());
	}
	
	public void confirmFunctionMenuAuth(String optype, String groupId, String[] menuId, String editId){
		FunctionMenuAuth obj = null;
		if("add".equals(optype) && menuId!=null && menuId.length>0){
			//saveOrUpdate
			for (int i=0; i<menuId.length; i++) {
				//obj = findByPk(groupId, Common.getInteger(menuId[i]));
				//if(obj != null)		continue;
				obj = new FunctionMenuAuth();
				obj.setGroupId(groupId);
				obj.setFunctionMenuId(Common.getInteger(menuId[i]));
				insert(obj);
			}
		}else if("remove".equals(optype) && menuId!=null && menuId.length>0){
			//delete
			for (int i=0; i<menuId.length; i++) {
				obj = new FunctionMenuAuth();
				obj.setGroupId(groupId);
				obj.setFunctionMenuId(Common.getInteger(menuId[i]));
				delete(obj);
			}
		}	
	}

	public void delete(FunctionMenuAuth bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getGroupId())
				&& null!=bo.getFunctionMenuId() ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM FUNCTION_MENU_AUTH WHERE GROUP_ID = ? AND FUNCTION_MENU_ID = ?");
			sqljob.addParameter(bo.getGroupId());
			sqljob.addParameter(bo.getFunctionMenuId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public FunctionMenuAuth mapRow(ResultSet rs, int idx) throws SQLException {
		FunctionMenuAuth obj = null;
		if(null!=rs) {
			obj = new FunctionMenuAuth();
			obj.setId(rs.getInt("ID"));
			obj.setGroupId(rs.getString("GROUP_ID"));
			obj.setFunctionMenuId(rs.getInt("FUNCTION_MENU_ID"));
		}
		return obj;
	}

}