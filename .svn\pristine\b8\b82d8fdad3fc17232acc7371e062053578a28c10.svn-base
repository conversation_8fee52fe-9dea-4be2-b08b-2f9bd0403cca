package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.UmsMt;

public class UmsMtDao extends BaseDaoJdbc implements RowMapper<UmsMt>{

	public UmsMt findByUmsNo(String umsNo) {
		if(null==umsNo || "".equals(umsNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM UMS_MT");
		sqljob.appendSQL("WHERE UMS_NO=?");
		sqljob.addParameter(umsNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<UmsMt> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	public List<UmsMt> findByPrefixNo(String prefixNo) {
		if(null==prefixNo || "".equals(prefixNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM UMS_MT");
		sqljob.appendSQL("WHERE SYSTEM = 'PSM/MT'");
		sqljob.appendSQL("AND SMBODY LIKE ?");
		sqljob.addLikeParameter(prefixNo.substring(0,3)+"-"+prefixNo.substring(3));
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public UmsMt mapRow(ResultSet rs, int rowNum) throws SQLException {
		UmsMt obj = null;
		if (null != rs) {
			obj = new UmsMt();
			obj.setUmsNo(rs.getString("UMS_NO"));
			obj.setMsgid(rs.getString("MSGID"));
			obj.setDstaddr(rs.getString("DSTADDR"));
			obj.setSmbody(rs.getString("SMBODY"));
			obj.setMtTime(rs.getString("MT_TIME"));
			obj.setMtCode(rs.getString("MT_CODE"));
			obj.setMtStr(rs.getString("MT_STR"));
			obj.setMxTime(rs.getString("MX_TIME"));
			obj.setMxCode(rs.getString("MX_CODE"));
			obj.setMxStr(rs.getString("MX_STR"));
			obj.setRetry(rs.getInt("RETRY"));
			obj.setSystem(rs.getString("SYSTEM"));
		}
		return obj;	}

}
