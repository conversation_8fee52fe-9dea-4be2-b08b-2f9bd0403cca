<%@page import="com.kangdainfo.sys.common.Constants"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

//String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
//要修正!!
String q = Common.get(request.getParameter("q"));
//System.out.println(q);
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" RCV_NO");
		sqljob.appendSQL(",RCV_TIME");
		sqljob.appendSQL(",KEYNOTE");
		sqljob.appendSQL("FROM DECLARATORY_STATUTES");
		sqljob.appendSQL("WHERE TRIM(RCV_NO)=?");
		sqljob.addParameter(q);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		Map<String,Object> data = null;
		if (null!=datas && !datas.isEmpty()) {
			data = datas.get(0);
			
			DeclaratoryStatutes bo2 = new DeclaratoryStatutes();
			bo2.setRcvNo(((String)data.get("RCV_NO")).trim());
			List<DeclaratoryStatutesRcver> dsrList = ServiceGetter.getInstance().getPrefixService().queryDeclaratoryStatutesRcver(bo2);
			if ( dsrList != null && dsrList.size() != 0 ) {
				String instructions = "";
				String rcver1 = "";
				String rcver2 = "";
				for (int i = 0;i<dsrList.size();i++ ) {
					if ( "0".equals(Common.get(dsrList.get(i).getRcverType()))) {
						if ("".equals(instructions))
							instructions+=Common.get(dsrList.get(i).getRcverOrg());
						else
							instructions+="\n"+Common.get(dsrList.get(i).getRcverOrg());
					}
					else if ( "1".equals(Common.get(dsrList.get(i).getRcverType()))) {
						if ("".equals(rcver1))
							rcver1+=Common.get(dsrList.get(i).getRcverOrg());
						else
							rcver1+="、"+Common.get(dsrList.get(i).getRcverOrg());
					}
					else {
						if ("".equals(rcver2))
							rcver2+=Common.get(dsrList.get(i).getRcverOrg());
						else
							rcver2+="、"+Common.get(dsrList.get(i).getRcverOrg());
					}
				}
				data.put("INSTRUCTION",instructions);
				data.put("RCV_TYPE_1",rcver1);
				data.put("RCV_TYPE_2",rcver2);
			}
			out.write(gson.toJson(data));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>