package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;

import com.ekera.presearch.Examine;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.pdf.CommonPdfUtils;

/**
 * 預查申請資料收文
 */
public class PRE1001 extends SuperBean {
	/** 統一編號 */
	private String banNo;
	/** 原公司名稱 */
	private String oldCompanyName;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人身分ID */
	private String applyId;
	/** 申請人地址 */
	private String companyAddr;
	/** 申請人電話 */
	private String applyTel;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人身分ID */
	private String attorId;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 事務所所在地 */
	private String attorAddr;
	/** 代理人電話 */
	private String attorTel;
	/** 收件人姓名 */
	private String contactName;
	/** 收件人身分ID */
	private String receiveId;
	/** 收件人聯絡地址 */
	private String contactAddr;
	/** 簡訊回覆通知電話 */
	private String contactCel;
	/** 領件方式(1:自取,2:郵寄) */
	private String contactGetKind;
	/** 郵寄註記 */
	private String getKindRemark;
	/** 預查編號 */
	private String prefixNo;
	/** 是否有預查表附件 */
	private String isPrefixForm;
	/** 附件-正副本別 */
	private String docType;
	/** 附件-預查表編號 */
	private String prefixFormNo;
	/** 附件-是否有其他機關核准函附件 */
	private String isOtherForm;
	/** 附件-是否有說明書附件 */
	private String isSpec;
	/** 附件-是否有其他附件 */
	private String isOtherSpec;
	/** 附件-其他附件註記 */
	private String otherSpecRemark;
	/** 收件日期 */
	private String receiveDate;
	/** 網路收文號 */
	private String telixNo;
	/** 預查種類 - 設立 */
	private String setup;
	/** 預查種類 - 名稱變更 */
	private String changeName;
	/** 預查種類 - 所營變更 */
	private String changeItem;
	/** 線上收文件數 */
	private String onLineCnt;
	/** 公司名稱 */
	private String companyName;
	/** 是否同時列印地址條 */
	private String printAddressTag;
	/** 線上收文申請表檔名 */
	private String onlineFilePath;
	/** 所代表法人 */
	private String applyLawName;
	/** 法人統編 */
	private String applyBanNo;
	/** 是否為閉鎖性 */
	private String closed;
	/** 組織別 */
	private String orgType;
	/** 收據號 */
	private String receiptNo;
	/** 國外匯款使用英文名稱 */
	private String extRemitEname;
	
	/** 線上收文 */
	public void makeOnlineCnt() throws Exception {
		PRE1001 obj = this;
		//電子流水號
		String telixNo = obj.getTelixNo();
		//找案件
		List<String> newCases = null;
		if(!"".equals(Common.get(telixNo)) && (telixNo.startsWith("OSC") || telixNo.startsWith("OSS"))) {
			//有輸入電子流水號, 就只收這一個號碼
			newCases = new ArrayList<String>();
			newCases.add(telixNo);
		} else {
			//每次收文件數
			int limit = ServiceGetter.getInstance().getPre1001Service().findReceiveLimit();
			//線上收文件數
			int onlineCnt = Integer.parseInt(obj.getOnLineCnt());
			if( limit == 99999 || limit > onlineCnt ) {
				limit = onlineCnt;
			}
			if(logger.isInfoEnabled()) logger.info("[線上收文]本次收文件數:"+limit);
			newCases = ServiceGetter.getInstance().getPre1001Service().readOsssCase(limit);
		}
		
		if(logger.isInfoEnabled()) logger.info("[線上收文]待存檔");
		List<String> printCases = new ArrayList<String>();
		if(null!=newCases && !newCases.isEmpty()) {
			List<PRE1001> objs = new ArrayList<PRE1001>();
			for(String newCase : newCases) {
				objs.add( ServiceGetter.getInstance().getPre1001Service().generateOnlineCase(newCase) );
			}
			//存檔
			printCases.addAll( savePrefixCase(objs, getLoginUserId()) );
		}
		if(logger.isInfoEnabled()) logger.info("[線上收文]已存檔");
		//產製
		File mergeFile = null;
		if(null!=printCases && !printCases.isEmpty()) {
			// 合併線上收文的pdf以供列印
			List<File> pdfFiles = new ArrayList<File>(); 
			for(String prefixNo : printCases) {
				//新增 同名比對排程
				//ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(prefixNo, getLoginUserId());
				//判斷是否為線上審核案件, 若為線上審核案件則直接核准
				ServiceGetter.getInstance().getPre1001Service().checkOnlineAudit(prefixNo, getLoginUserId());
				//產製申請書
				pdfFiles.add(ServiceGetter.getInstance().getPrefixService().printApplyForm(prefixNo,"onLineCnt"));
				//同步一站式案件狀態
				ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(prefixNo, getLoginUserId());
			}
			mergeFile = CommonPdfUtils.concatPdfFiles(pdfFiles, true);
		}
		if(logger.isInfoEnabled()) logger.info("[線上收文]pdf產製完成");

		if (mergeFile != null) {
			//收文成功
			setState("makeOnlineCntSuccess");
			setErrorMsg("收文成功，本次收文"+printCases.size()+"件");

			if(logger.isInfoEnabled()) logger.info("[線上收文]檔案路徑:"+mergeFile.getAbsolutePath());
			String onlineFilePath = "";
			try {
				Base64 base64encoder = new Base64();
				onlineFilePath = new String(base64encoder.encode(mergeFile.getAbsolutePath().getBytes()));
			} catch(Exception e) {
				e.printStackTrace();
			}
			setOnlineFilePath(onlineFilePath);
			if(logger.isInfoEnabled()) logger.info("[線上收文]檔案路徑加密:"+onlineFilePath);
		} else {
			//此筆資料已收文
			setState("justUpdateState");
			setErrorMsg("無一站式案件可收文!");
		}
	}

	/** 取號存檔 */
	public void save() throws Exception {
		PRE1001 obj = this;
		if (getTelixNo() == null || "".equals(getTelixNo())) {
			if (getReceiptNo() == null || "".equals(getReceiptNo())) {
				obj.setErrorMsg("現場收文必須輸入收據號碼");
				return;
			} else {
				if (!ServiceGetter.getInstance().getPre1001Service().selectPrefixReceiptNoByReceiptNo(getReceiptNo())) {
					obj.setErrorMsg("查無開立該收據的紀錄，請確認是否已繳費");
					return;
				}
			}
		} else {
			setReceiptNo("");
			if ( !ServiceGetter.getInstance().getPre1001Service().selectPrefixReceiptNoByTelixNo(getTelixNo()) ) {
				obj.setErrorMsg("該一維條碼案件尚未開立收據，請確認是否已繳費");
				return;
			}
			
			Eedb1000 eedb1000 = ServiceGetter.getInstance().getPre5001Service().selectEedb1000ByTelixNo(telixNo);
			if (eedb1000 == null) {
				obj.setErrorMsg("輸入的電子案號錯誤, 資料庫裡沒有該筆資料");
				return;
			}
			
			obj.setExtRemitEname(eedb1000.getCmpyRemitEname());
		}

		//判斷電子收文號是不是有重複
		if( ServiceGetter.getInstance().getPre1001Service().queryTelixNoExist(getTelixNo()) ) {
			obj.setErrorMsg("一維條碼已存在，請確認是否重複收文");
		} else {
			//存檔
			List<PRE1001> objs = new ArrayList<PRE1001>();
			objs.add(obj);

			List<String> newPrefixNos = savePrefixCase(objs, getLoginUserId());
			if(null==newPrefixNos || newPrefixNos.isEmpty()) {
				obj.setErrorMsg( ("".equals(obj.getTelixNo())?"紙本":"一維條碼") + "收文發生錯誤");
			} else {
				obj.setPrefixNo(newPrefixNos.get(0));
				obj.setErrorMsg( ("".equals(obj.getTelixNo())?"紙本":"一維條碼") + "收文存檔成功");
				if( "".equals(obj.getTelixNo()) ) {
					//紙本收文, 還沒有公司名稱資料, 不做同名比對
				} else {
					//新增 同名比對排程
					ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(obj.getPrefixNo(), getLoginUserId());
				}
				//判斷是否為線上審核案件, 若為線上審核案件則直接核准
				ServiceGetter.getInstance().getPre1001Service().checkOnlineAudit(obj.getPrefixNo(), getLoginUserId());
				//同步一站式案件狀態
				if( getTelixNo().startsWith("OSC") || getTelixNo().startsWith("OSS") ) {
					ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(obj.getPrefixNo(), getLoginUserId());
				}
			}
		}
	}

	public static String checkNoNeedPay(String telixNo, String changeType, String banNo) {
		String result = "";
		if ( "2".equals( changeType ) || "3".equals( changeType ) ) {
			if ( banNo != null && !"".equals(banNo) ) {
				GeneralityBusitem gBusitem = ServiceGetter.getInstance().getNoPayMarkService().getByBanNo(banNo);
				if ( null != gBusitem ) {
					if( "".equals(Common.get(gBusitem.getPrefixNo())) && "".equals(Common.get(gBusitem.getTelixNo())) ) {
						//尚未使用免繳, 要判斷是否符合免繳資格
						
						//一站式要確定沒繳
						boolean hasPaid = false;
						if( telixNo.startsWith("OSC") || telixNo.startsWith("OSS") ) {
							OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(telixNo);
							if(null!=ossmApplMain) {
								String processNo = "B";
							    if ( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals( ossmApplMain.getApplyType()) ) {
							    	processNo = "I";
							    }
								OssmFeeMain ossmFeeMain = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(telixNo,processNo);
								if(null!=ossmFeeMain) {
									//OssmFeeMain 有資料, 表示有繳, 應該要走退費
									hasPaid = true;
								}
							}
						}
						
						if(!hasPaid) {
							//1.設立日期須在0980702(含)之前設立，> 0 代表在這時間之後設立
							Cedb2000 cedb2000 = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(banNo);
							if(null!=cedb2000 && !"".equals(Common.get(cedb2000.getSetupDate())) && ("0980702".compareTo(cedb2000.getSetupDate()) >= 0) ) {
								//2.原本登的營業項目有ZZ99999
								SQLJob zz99999Sql = new SQLJob("select busi_item_no from cedb2002 where ban_no = ? and (busi_item_no = 'ZZ99999' or busi_item like '除許可業務外，得經營法令非禁止或限制之業務%')");
								zz99999Sql.addParameter(banNo);
								List<Map<String, Object>> listZZ99999 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(zz99999Sql);
								if (listZZ99999 != null && listZZ99999.size() > 0 ) {
									/*
									"SELECT BUSI_ITEM_NO"
									"FROM CEDB1002 WHERE prefix_no = ?"
									"AND busi_item_no NOT IN ("
									"  SELECT BUSI_ITEM_NO FROM CEDB2002"
									"  WHERE BAN_NO = ?"
									"  AND BUSI_ITEM_NO != 'ZZ99999'"
									")"
									"AND BUSI_ITEM_NO like '%0'"
									"AND ROWNUM = 1"
									*/
									// 一維條碼需查eedb資料庫兩者無法併為同一段SQL, 因此將以上pre1003原文改寫為以下程式碼
									// 1.先選出本次申請的所有營業項目
									// 2.再選出公司現況主檔營業項目中代碼ZZ99999以外的項目
									// 3.將1選出的集合扣掉2選出的集合 看剩下的集合中是否有代碼結尾為0的營業項目
									SQLJob sqljob;
									sqljob= new SQLJob();
									sqljob.appendSQL("SELECT BUSI_ITEM_NO FROM EEDB3100 WHERE TELIX_NO = ?");
									sqljob.addParameter(telixNo);
									List<Map<String, Object>> eedb3100 = ServiceGetter.getInstance().getEedbGeneralQueryDao().queryForList(sqljob);
									if (eedb3100!=null && eedb3100.size()!=0 ) {
										sqljob = new SQLJob();
										sqljob.appendSQL("SELECT BUSI_ITEM_NO FROM CEDB2002 WHERE BAN_NO = ? AND BUSI_ITEM_NO <> 'ZZ99999'");
										sqljob.addParameter(banNo);
										List<Map<String, Object>> cedb2002 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
										eedb3100.removeAll(cedb2002);
										if ( eedb3100.size() > 0 ) {
											int count = 0;
											for (int i=0; i<eedb3100.size();i++ ) {
												if ( Common.get(eedb3100.get(i).get("BUSI_ITEM_NO")).endsWith("0") ) 
													count++;
												
											} // for
											if (count>0)
												result =  "true";
										} // if
									} // if
								} // if
							} // if
						} // if
						
					} // else if
				} //if
			} // if
		} // if
		return result;
	} // checkNoNeedPay
	
	/** 避免搶號, 要使用  synchronized static */
	private synchronized static List<String> savePrefixCase(List<PRE1001> objs, String userid) {
		List<String> results = new ArrayList<String>();
		try {
			if(null!=objs && !objs.isEmpty()) {
				String newPrefixNo = "";
				for(PRE1001 obj : objs) {
					newPrefixNo = ServiceGetter.getInstance().getPre1001Service().savePrefixCase(obj, userid);
					if(!"".equals(Common.get(newPrefixNo))) {
						//智慧型預查
						ServiceGetter.getInstance().getPre1001Service().doPreSearch(newPrefixNo);
						results.add(newPrefixNo);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return results;
	}

	@Override
	public Object doQueryOne() throws Exception {return this;}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}
	/** 統一編號 */
	public String getBanNo() {return checkGet(banNo);}
	/** 統一編號 */
	public void setBanNo(String s) {this.banNo = checkSet(s);}
	/** 原公司名稱 */
	public String getOldCompanyName() {return checkGet(oldCompanyName);}
	/** 原公司名稱 */
	public void setOldCompanyName(String s) {this.oldCompanyName = checkSet(s);}
	/** 申請人姓名 */
	public String getApplyName() {return checkGet(applyName);}
	/** 申請人姓名 */
	public void setApplyName(String s) {this.applyName = checkSet(s);}
	/** 申請人身分ID */
	public String getApplyId() {return checkGet(applyId);}
	/** 申請人身分ID */
	public void setApplyId(String s) {this.applyId = checkSet(s);}
	/** 申請人地址 */
	public String getCompanyAddr() {return checkGet(companyAddr);}
	/** 申請人地址 */
	public void setCompanyAddr(String s) {this.companyAddr = checkSet(s);}
	/** 申請人電話 */
	public String getApplyTel() {return checkGet(applyTel);}
	/** 申請人電話 */
	public void setApplyTel(String s) {this.applyTel = checkSet(s);}
	/** 代理人姓名 */
	public String getAttorName() {return checkGet(attorName);}
	/** 代理人姓名 */
	public void setAttorName(String s) {this.attorName = checkSet(s);}
	/** 代理人身分ID */
	public String getAttorId() {return checkGet(attorId);}
	/** 代理人身分ID */
	public void setAttorId(String s) {this.attorId = checkSet(s);}
	/** 代理人證書號碼 */
	public String getAttorNo() {return checkGet(attorNo);}
	/** 代理人證書號碼 */
	public void setAttorNo(String s) {this.attorNo = checkSet(s);}
	/** 事務所所在地 */
	public String getAttorAddr() {return checkGet(attorAddr);}
	/** 事務所所在地 */
	public void setAttorAddr(String s) {this.attorAddr = checkSet(s);}
	/** 代理人電話 */
	public String getAttorTel() {return checkGet(attorTel);}
	/** 代理人電話 */
	public void setAttorTel(String s) {this.attorTel = checkSet(s);}
	/** 收件人姓名 */
	public String getContactName() {return checkGet(contactName);}
	/** 收件人姓名 */
	public void setContactName(String s) {this.contactName = checkSet(s);}
	/** 收件人身分ID */
	public String getReceiveId() {return checkGet(receiveId);}
	/** 收件人身分ID */
	public void setReceiveId(String s) {this.receiveId = checkSet(s);}
	/** 收件人聯絡地址 */
	public String getContactAddr() {return checkGet(contactAddr);}
	/** 收件人聯絡地址 */
	public void setContactAddr(String s) {this.contactAddr = checkSet(s);}
	/** 簡訊回覆通知電話 */
	public String getContactCel() {return checkGet(contactCel);}
	/** 簡訊回覆通知電話 */
	public void setContactCel(String s) {this.contactCel = checkSet(s);}
	/** 領件方式(1:自取,2:郵寄) */
	public String getContactGetKind() {return checkGet(contactGetKind);}
	/** 領件方式(1:自取,2:郵寄) */
	public void setContactGetKind(String s) {this.contactGetKind = checkSet(s);}
	/** 郵寄註記 */
	public String getGetKindRemark() {return checkGet(getKindRemark);}
	/** 郵寄註記 */
	public void setGetKindRemark(String s) {this.getKindRemark = checkSet(s);}
	/** 預查編號 */
	public String getPrefixNo() {return checkGet(prefixNo);}
	/** 預查編號 */
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	/** 收件日期 */
	public String getReceiveDate() {return checkGet(receiveDate);}
	/** 收件日期 */
	public void setReceiveDate(String s) {this.receiveDate = checkSet(s);}
	/** 網路收文號 */
	public String getTelixNo() {return checkGet(telixNo);}
	/** 網路收文號 */
	public void setTelixNo(String s) {this.telixNo = checkSet(s);}
	/** 預查種類 - 設立 */
	public String getSetup() {return checkGet(setup);}
	/** 預查種類 - 設立 */
	public void setSetup(String s) {this.setup = checkSet(s);}
	/** 預查種類 - 名稱變更 */
	public String getChangeName() {return checkGet(changeName);}
	/** 預查種類 - 名稱變更 */
	public void setChangeName(String s) {this.changeName = checkSet(s);}
	/** 預查種類 - 所營變更 */
	public String getChangeItem() {return checkGet(changeItem);}
	/** 預查種類 - 所營變更 */
	public void setChangeItem(String s) {this.changeItem = checkSet(s);}
	/** 線上收文件數 */
	public String getOnLineCnt() {return checkGet(onLineCnt);}
	/** 線上收文件數 */
	public void setOnLineCnt(String s) {this.onLineCnt = checkSet(s);}
	/** 公司名稱 */
	public String getCompanyName() {return checkGet(companyName);}
	/** 公司名稱 */
	public void setCompanyName(String s) {this.companyName = checkSet(s);}
	/** 是否同時列印地址條 */
	public String getPrintAddressTag() {return checkGet(printAddressTag);}
	/** 是否同時列印地址條 */
	public void setPrintAddressTag(String s) {this.printAddressTag = checkSet(s);}
	/** 是否有預查表附件 */
	public String getIsPrefixForm() {return checkGet(isPrefixForm);}
	/** 是否有預查表附件 */
	public void setIsPrefixForm(String s) {this.isPrefixForm = checkSet(s);}
	/** 附件-正副本別 */
	public String getDocType() {return checkGet(docType);}
	/** 附件-正副本別 */
	public void setDocType(String s) {this.docType = checkSet(s);}
	/** 附件-預查表編號 */
	public String getPrefixFormNo() {return checkGet(prefixFormNo);}
	/** 附件-預查表編號 */
	public void setPrefixFormNo(String s) {this.prefixFormNo = checkSet(s);}
	/** 附件-是否有其他機關核准函附件 */
	public String getIsOtherForm() {return checkGet(isOtherForm);}
	/** 附件-是否有其他機關核准函附件 */
	public void setIsOtherForm(String s) {this.isOtherForm = checkSet(s);}
	/** 附件-是否有說明書附件 */
	public String getIsSpec() {return checkGet(isSpec);}
	/** 附件-是否有說明書附件 */
	public void setIsSpec(String s) {this.isSpec = checkSet(s);}
	/** 附件-是否有其他附件 */
	public String getIsOtherSpec() {return checkGet(isOtherSpec);}
	/** 附件-是否有其他附件 */
	public void setIsOtherSpec(String s) {this.isOtherSpec = checkSet(s);}
	/** 附件-其他附件註記 */
	public String getOtherSpecRemark() {return checkGet(otherSpecRemark);}
	/** 附件-其他附件註記 */
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = checkSet(s);}
	/** 線上收文申請表檔名 */
	public String getOnlineFilePath() {return checkGet(onlineFilePath);}
	/** 線上收文申請表檔名 */
	public void setOnlineFilePath(String s) {this.onlineFilePath = checkSet(s);}
	/** 所代表法人 */
	public String getApplyLawName() {return checkGet(applyLawName);}
	/** 所代表法人 */
	public void setApplyLawName(String s) {this.applyLawName = checkSet(s);}
	/** 法人統編 */
	public String getApplyBanNo() {return checkGet(applyBanNo);}
	/** 法人統編 */
	public void setApplyBanNo(String s) {this.applyBanNo = checkSet(s);}
	/** 是否為閉鎖性 */
	public String getClosed() {return checkGet(closed);}
	/** 是否為閉鎖性 */
	public void setClosed(String s) {this.closed = checkSet(s);}
	/** 組織別 */
	public String getOrgType() {return checkGet(orgType);}
	/** 組織別 */
	public void setOrgType(String s) {this.orgType = checkSet(s);}
	/** 收據號 */
	public String getReceiptNo() {return checkGet(receiptNo);}
	/** 收據號 */
	public void setReceiptNo(String s) {this.receiptNo = checkSet(s);}
	/** 國外匯款使用英文名稱 */
	public String getExtRemitEname() {return checkGet(extRemitEname);}
	/** 國外匯款使用英文名稱 */
	public void setExtRemitEname(String s) {this.extRemitEname = checkSet(s);}
}