package com.kangdainfo.tcfi.view.ajax;

import java.util.List;
import java.util.Map;

//import org.apache.axis.utils.StringUtils;// 2024/07/31 暫時注釋掉
//import org.apache.poi.util.StringUtil.StringsIterator;// 2024/07/31 暫時注釋掉

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

public class PopSystemCode extends SuperBean
{
	public PopSystemCode doQueryOne() throws Exception {
		PopSystemCode obj = null;
		if (!"".equals(getCodeKind())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT CODE_KIND, CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND ENABLE='Y' ORDER BY SORTED, CODE");
			sqljob.addParameter(getCodeKind());
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (null!=datas && !datas.isEmpty()) {
				Map<String,Object> data = datas.get(0);
				obj = new PopSystemCode();
				obj.setCodeKind(Common.get(data.get("CODE_KIND")));
				obj.setCode(Common.get(data.get("CODE")));
				obj.setCodeName(Common.get(data.get("CODE_NAME")));
			}
		}
		return obj;
	}

	public java.util.ArrayList<String[]> doQueryAll() throws Exception
	{
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		//arrList.add(new String[]{"", com.kangdainfo.tcfi.util.PrefixConstants.SelectALL});
		if (!"".equals(getCodeKind())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND ENABLE='Y' ");
			sqljob.addParameter(getCodeKind());
			//2019/9/24 針對CODE_KIND=15的「片語」添加以CODE開頭為條件的判斷	2019/9/24 Amy Chung
			if(getCodeKind().equals("15") && !"".equals(getCode())) {
				sqljob.appendSQL("AND CODE LIKE "+Common.sqlChar(getCode()+"%"));
			}
			sqljob.appendSQL("ORDER BY SORTED, CODE");
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (null!=datas && !datas.isEmpty()) {
				for(Map<String,Object> data : datas) {
					String[] rows = new String[2];
					rows[0] = Common.get(data.get("CODE"));
					rows[1] = Common.get(data.get("CODE_NAME"));
					arrList.add(rows);
				}
			}
		}
		return arrList;
	}

    /** 代碼類別說明 */
	public String getCodeKindDesc() {
		String result = "";
		if (!"".equals(getCodeKind())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND CODE=?");
			sqljob.addParameter("00");
			sqljob.addParameter(getCodeKind());
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (null!=datas && !datas.isEmpty()) {
				Map<String,Object> data = datas.get(0);
				if(null!=data) {
					result = Common.get(data.get("CODE_NAME"));
				}
			}
		}
		return result;
	}

    /** 代碼類別 */
    private String codeKind;
    /** 代碼 */
    private String code;
    /** 代碼名稱 */
    private String codeName;

    public String getCodeKind() {return checkGet(codeKind);}
	public void setCodeKind(String codeKind) {this.codeKind = checkSet(codeKind);}

	public String getCode() {return checkGet(code);}
	public void setCode(String code) {this.code = checkSet(code);}

	public String getCodeName() {return checkGet(codeName);}
	public void setCodeName(String codeName) {this.codeName = checkSet(codeName);}

	@Override
	public void doCreate() throws Exception {
	}

	@Override
	public void doUpdate() throws Exception {
	}

	@Override
	public void doDelete() throws Exception {
	}

}