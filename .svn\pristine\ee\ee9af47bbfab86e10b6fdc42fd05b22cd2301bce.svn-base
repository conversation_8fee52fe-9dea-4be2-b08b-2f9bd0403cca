package com.kangdainfo.tcfi.model.icms.dao;

import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;

/**
 * 一般查詢
 *
 */
public class IcmsGeneralQueryDao extends BaseDaoJdbc {

	public List<Map<String,Object>> queryForList(String sql) {
		if(logger.isDebugEnabled()) logger.debug(sql);
		return getJdbcTemplate().queryForList(sql);
	}

	public List<Map<String,Object>> queryForList(String sql, Object[] args) {
		if(logger.isDebugEnabled()) logger.debug(sql);
		return getJdbcTemplate().queryForList(sql, args);
	}
	
	public List<Map<String,Object>> queryForList(SQLJob sqljob) {
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Map<String,Object>> results = null;
		if(null!=sqljob.getSqltypesArray()) {
			results = getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		} else {
			results = getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray());
		}
		return results;
	}
	
	@SuppressWarnings("rawtypes")
	public List query(SQLJob sqljob, BeanPropertyRowMapper<?> beanRowMapper) {
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), beanRowMapper);
	}
}
