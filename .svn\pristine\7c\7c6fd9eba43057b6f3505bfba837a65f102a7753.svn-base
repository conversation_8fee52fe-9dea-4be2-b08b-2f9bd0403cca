package com.kangdainfo.tcfi.view.pre;

import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;

public class DataSender {
	private static DataSender ds;
	private static String requestURL;
	private static HashMap<String, String> parameters;

	public static DataSender getInstance() {
		if (ds == null) {
			ds = new DataSender();
		}
		if (parameters == null) {
			parameters = new HashMap<String, String>();
		}
		return ds;
	}

	public void setRequestURL(String url) {
		requestURL = url;
	}

	public void addParameter(String key, String value) {
		parameters.put(key, value);
	}

	public void resetParameters() {
		parameters.clear();
	}

	public void send() throws Exception {
		try {
			URL url = new URL(requestURL);
			URLConnection uc = url.openConnection();
			uc.setDoOutput(true);
			uc.setDoInput(true);

			OutputStream buffered = new BufferedOutputStream(uc.getOutputStream());
			OutputStreamWriter out = new OutputStreamWriter(buffered, "utf-8");

			String urlParameters = getURLParameters(parameters);
			out.write(urlParameters);
			out.flush();
			out.close();
			InputStream in = uc.getInputStream();
			in.close();
		} catch (Exception e) {
			throw e;
		} finally {
		}
	}

	private String getURLParameters(HashMap<String, String> param) throws UnsupportedEncodingException {
		StringBuffer sb = new StringBuffer();
		Iterator<String> keys = param.keySet().iterator();
		String name, value;

		if (keys.hasNext()) {
			name = keys.next();
			value = URLEncoder.encode(param.get(name), "UTF-8");
			sb.append(name).append("=").append(value);
		}
		while (keys.hasNext()) {
			name = keys.next();
			value = URLEncoder.encode(param.get(name), "UTF-8");
			sb.append("&").append(name).append("=").append(value);
		}
		sb.append("\r\n");
		return sb.toString();
	}
}