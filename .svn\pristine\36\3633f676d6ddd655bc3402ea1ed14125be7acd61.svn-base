package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.SynonymWord;

public class PRE8017 extends SuperBean {

	private String id; 			// 主鍵
	private String word; 		// 字詞
	private String synonymWord; // 同義詞
	private String source; 		// 詞源依據詞
	private String modIdNo; 	// 異動人員
	private String modDate; 	// 異動日期
	private String modTime; 	// 異動時間

	private String q_id;
	private String q_word;
	private String q_synonymWord;
	private String q_source;
	private String q_modIdNo;
	private String q_modDate;
	private String q_modTime;

	@Override
	public Object doQueryOne() throws Exception {
		PRE8017 obj = this;
		SynonymWord s = ServiceGetter.getInstance().getPrefixService().querySynonymWordById(getId());
		if(s != null){
			obj.setId(s.getId());
			obj.setWord(s.getWord());
			obj.setSynonymWord(s.getSynonymWord());
			obj.setSource(s.getSource());
			obj.setEditID(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(s.getModIdNo()));
			obj.setEditDate(s.getModDate());
			//obj.setModTime(s.getModTime());
		}else{
			obj.setId("");
			this.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}
	
	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		
		SynonymWord obj = new SynonymWord();
		if (!"".equals(Common.get(getQ_word())))
			obj.setWord(getQ_word());
		//if (!"".equals(Common.get(getQ_synonymWord())))
		//	obj.setSynonymWord(getQ_synonymWord());
		if (!"".equals(Common.get(getQ_source())))			
			obj.setSource(getQ_source());

		ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>();
		List<SynonymWord> tempList = ServiceGetter.getInstance().getPrefixService().querySynonymWord(obj);

		//this.processCurrentPageAttribute(tempList.size());
		if (tempList != null && tempList.size() > 0) {
			SynonymWord dtl;
			String[] rowArray = new String[6];
			for (Object dtlObj : tempList) {
				dtl = (SynonymWord) dtlObj;
				rowArray = new String[6];
				if (tempList.size()==1){
					this.setQ_id(dtl.getId());
				}
				rowArray[0] = dtl.getId();
				rowArray[1] = dtl.getWord();
				rowArray[2] = dtl.getSynonymWord();
				rowArray[3] = Common.get(dtl.getSource());
				rowArray[4] = Common.get(ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(dtl.getModIdNo()));
				rowArray[5] = Common.formatYYYMMDD(dtl.getModDate(), 4);		
				arrayList.add(rowArray);
			}
			tempList.clear();
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrayList;
	}

	@Override
	public void doCreate() throws Exception {
		
		if("".equals(Common.get(getWord())) || "".equals(Common.get(getSynonymWord())))
			throw new MoeaException("字詞或同義詞資料有誤，請重新輸入");
		
		// 檢查字詞是否已存在
		if (isWordExits(getWord(), getSynonymWord())) {
			SynonymWord obj = new SynonymWord();
			obj.setWord(getWord());
			obj.setSynonymWord(getSynonymWord());		
			obj.setSource(getSource());
			obj.setModIdNo(getLoginUserId());
			obj.setModDate(Datetime.getYYYMMDD());
			obj.setModTime(Datetime.getHHMMSS());
			obj = ServiceGetter.getInstance().getPrefixService().insertSynonymWord(obj);
			this.setId(obj.getId());
		}else{
			throw new MoeaException("字詞或同義詞已存在，請重新輸入!!");
		}
	}
	
	@Override
	public void doUpdate() throws Exception {
		
		if("".equals(Common.get(getWord())) || "".equals(Common.get(getSynonymWord())))
			throw new MoeaException("字詞或同義詞資料有誤，請重新輸入");
		
		SynonymWord obj = ServiceGetter.getInstance().getPrefixService().querySynonymWordById(getId());
		if(obj == null)	throw new MoeaException("資料不存在!");
		
		// 檢查字詞是否已存在
		if (isWordExits(getWord(), getSynonymWord())) {
			obj.setWord(getWord());
			obj.setSynonymWord(getSynonymWord());		
			obj.setSource(getSource());
			obj.setModIdNo(getLoginUserId());
			obj.setModDate(Datetime.getYYYMMDD());
			obj.setModTime(Datetime.getHHMMSS());
			obj = ServiceGetter.getInstance().getPrefixService().updateSynonymWord(obj);
			this.setId(obj.getId());
		}else{
			throw new Exception("字詞或同義詞已存在，請重新輸入!!");
		}
	}
	
	@Override
	public void doDelete() throws Exception {
		
		SynonymWord obj = ServiceGetter.getInstance().getPrefixService().querySynonymWordById(getId());
		if(obj == null)	throw new MoeaException("查無資料，無法刪除，請重新操作 !");
		ServiceGetter.getInstance().getPrefixService().deleteSynonymWord(obj);
		this.setId("");
	}

	/**
	 * 檢查字詞/同義詞是否已存在
	 * 
	 * @param synonymWord
	 * @return
	 */
	private boolean isWordExits(String word, String synonymWord) {

		SynonymWord obj = ServiceGetter.getInstance().getPrefixService().querySynonymWordByCheckWord(word, synonymWord);
		if(obj == null)
			return true;
		else{
			if("update".equals(this.getState()) && obj.getId().equals(this.getId()))
				return true;
		}
		return false;
	}

	/*
	 * getters and setters
	 */
	public String getWord() {return checkGet(word);}
	public void setWord(String s) {this.word = checkSet(s);}

	public String getSynonymWord() {return checkGet(synonymWord);}
	public void setSynonymWord(String s) {this.synonymWord = checkSet(s);}

	public String getSource() {return checkGet(source);}
	public void setSource(String s) {this.source = checkSet(s);}

	public String getModIdNo() {return checkGet(modIdNo);}
	public void setModIdNo(String s) {this.modIdNo = checkSet(s);}

	public String getModDate() {return checkGet(modDate);}
	public void setModDate(String s) {this.modDate = checkSet(s);}

	public String getModTime() {return checkGet(modTime);}
	public void setModTime(String s) {this.modTime = checkSet(s);}

	public String getQ_word() {return checkGet(q_word);}
	public void setQ_word(String s) {this.q_word = checkSet(s);}

	public String getQ_synonymWord() {return checkGet(q_synonymWord);}
	public void setQ_synonymWord(String s) {this.q_synonymWord = checkSet(s);}

	public String getQ_source() {return checkGet(q_source);}
	public void setQ_source(String s) {this.q_source = checkSet(s);}

	public String getQ_modIdNo() {return checkGet(q_modIdNo);}
	public void setQ_modIdNo(String s) {this.q_modIdNo = checkSet(s);}

	public String getQ_modDate() {return checkGet(q_modDate);}
	public void setQ_modDate(String s) {this.q_modDate = checkSet(s);}

	public String getQ_modTime() {return checkGet(q_modTime);}
	public void setQ_modTime(String s) {this.q_modTime = checkSet(s);}

	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String s) {this.q_id = checkSet(s);}

	public String getId() {return checkGet(id);}
	public void setId(String s) {this.id = checkSet(s);}

}