package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2006;

public class Cedb2006Dao extends BaseDaoJdbc implements RowMapper<Cedb2006> {

	private static String sql_findByBanNo = "SELECT * FROM CEDB2006";
	public List<Cedb2006> findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.appendSQLCondition(" BAN_NO = ? ");
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	@Override
	public Cedb2006 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb2006 obj = null;
		if(null!=rs) {
			obj = new Cedb2006();
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setForeignName(Common.get(rs.getString("FOREIGN_NAME")));
			obj.setForeignAddr(Common.get(rs.getString("FOREIGN_ADDR")));
			obj.setForeignForAddr(Common.get(rs.getString("FOREIGN_FOR_ADDR")));
			obj.setNativeCode(Common.get(rs.getString("NATIVE_CODE")));
			obj.setSetupDate(rs.getDate("SETUP_DATE"));
			obj.setOperateDate(rs.getDate("OPERATE_DATE"));
			obj.setCurrencyType(Common.get(rs.getString("CURRENCY_TYPE")));
			obj.setCapitalAmt(rs.getLong("CAPITAL_AMT"));
			obj.setStockAmt(rs.getLong("STOCK_AMT"));
			obj.setRealAmt(rs.getLong("REAL_AMT"));
			obj.setStockType(Common.get(rs.getString("STOCK_TYPE")));
			obj.setAppointName(Common.get(rs.getString("APPOINT_NAME")));
			obj.setAppointForName(Common.get(rs.getString("APPOINT_FOR_NAME")));
			obj.setAppointNativeCode(Common.get(rs.getString("APPOINT_NATIVE_CODE")));
			obj.setAppointIdCode(Common.get(rs.getString("APPOINT_ID_CODE")));
			obj.setAppointIdNo(Common.get(rs.getString("APPOINT_ID_NO")));
			obj.setAppointAddress(Common.get(rs.getString("APPOINT_ADDRESS")));
			obj.setRespLawActivity(Common.get(rs.getString("RESP_LAW_ACTIVITY")));
			obj.setUpdateUser(Common.get(rs.getString("UPDATE_USER")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateTime(Common.get(rs.getString("UPDATE_TIME")));
		}
		return obj;
	}

}