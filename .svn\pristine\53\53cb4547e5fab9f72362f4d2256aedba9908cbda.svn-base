<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE2005"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String start = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("start")));
String end = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("end")));
String type = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("type")));
String printType = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("printType")));

try {
	if ((start!=null&&!"".equals(start))&&(end!=null&&!"".equals(end))&&(type!=null&&!"".equals(type)))
	{
		String checkResult = PRE2005.checkForjsp(start, end, type, printType);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>