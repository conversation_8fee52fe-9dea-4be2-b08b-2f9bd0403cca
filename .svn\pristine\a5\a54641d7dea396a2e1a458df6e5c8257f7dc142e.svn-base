<!DOCTYPE html>
<!--
程式目的：公司特取名稱維護
程式代號：PRE8001
撰寫日期：103.03.25
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8001" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())){
	obj = (com.kangdainfo.tcfi.view.pre.PRE8001)obj.queryOne();	
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())){
	obj.update();
	//if ("updateSuccess".equals(obj.getState())) {
		obj = (com.kangdainfo.tcfi.view.pre.PRE8001)obj.queryOne();	
	//}
}
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkEmpty(form1.q_banNo, "統一編號");
		//if(form1.q_banNo.value != "")
	    	//alertStr += checkNumber(form1.q_banNo, "統一編號");
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.newPartName, "新特取名稱");  
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	form1.submit();
}

function init(){
	setDisplayItem("spanInsert,spanClear,spanConfirm,spanUpdate,spanDelete,spanListPrint,spanListHidden","H");
}

$(document).ready(function(){
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				form1.state.value="queryAll";
				//setBeforePageUnload(false);
				checkField();
				break;
			case "doConfirm":
				if(form1.id.value == ""){
					alert("請先執行查詢!!");
				   	return false;
				} 
				form1.state.value="update";
				setBeforePageUnload(false);
				checkField();
				break;
		}
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<!--Query區============================================================-->

<table width="100%" cellspacing="0" cellpadding="0">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8001'/>
</c:import>

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:left;">
	<input class="toolbar_default" type="button" followPK="false" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)">&nbsp;
	<input class="toolbar_default" type="button" followPK="false" name="doConfirm" value="存　檔" onClick="whatButtonFireEvent(this.name)">&nbsp;
	<input type="hidden" name="id" value="<%=obj.getId()%>">	
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
</td></tr>
</table>
</td></tr>
<!-- TOOLBAR AREA -->

<!--Form區============================================================-->
<tr><td class="bg" >
	<div id="formContainer" style="height:200">
	<table class="table_form" width="100%" height="100%">
       <tr>
          	<td nowrap class="td_form" width="15%">統一編號：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<input class="field_Q" type="text" name="q_banNo" size="10" maxlength="10" value="<%=obj.getQ_banNo()%>">
          	</td>  
        </tr>
       <tr>
          	<td nowrap class="td_form" width="15%">統一編號：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<%=obj.getBanNo()%>
          	</td>  
        </tr>
        <tr>
          	<td nowrap class="td_form" width="15%">公司/有限合夥名稱：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<%=obj.getCompanyName()%>
          	</td>
        </tr>        
        <tr>
          	<td nowrap class="td_form" width="15%">狀況：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<%=obj.getCompanyStatus()%>
          	</td>  
        </tr>  
        <tr>
          	<td nowrap class="td_form" width="15%">原特取名稱：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<%=obj.getPartName()%>
          	</td>  
        </tr>
        <tr>
          	<td nowrap class="td_form" width="15%">新特取名稱：</td>
          	<td nowrap colspan="3" class="td_form_white">
            	<input class="field_Q cmex" type="text" name="newPartName" size="50" maxlength="50" value="<%=obj.getNewPartName()%>">
     		</td>  
		</tr> 
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

</table>	
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>