package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Restriction;
import com.kangdainfo.util.lang.CommonStringUtils;

public class RestrictionDao extends BaseDaoJdbc implements RowMapper<Restriction> {

	private static final String SQL_find = "SELECT * FROM RESTRICTION WHERE 1 = 1";
	public Restriction findById(Integer id) {
		//check pk
		if(id == null) return null;
		SQLJob sqljob = new SQLJob(SQL_find + " AND ID = ? ");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Restriction> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}
	
	public List<Restriction> find() {
		//check pk
		SQLJob sqljob = new SQLJob(SQL_find);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Restriction> list = getJdbcTemplate().query(sqljob.getSQL(), this);
		return list.isEmpty()? null : list;
	}
	
	public Restriction findByCode(String code) {
		//check pk
		if("".equals(Common.get(code))) return null;
		SQLJob sqljob = new SQLJob(SQL_find + " AND CODE = ? ");
		sqljob.addParameter(code);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Restriction> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}

	public List<Restriction> findByCondition(String code, String name, String enable){
		SQLJob sqljob = new SQLJob(SQL_find);
		if(!"".equals(Common.get(code))){
			sqljob.appendSQLCondition(" CODE = ? ");
			sqljob.addParameter(code);
		}
		if(!"".equals(Common.get(name))){
			sqljob.appendSQLCondition(" NAME LIKE ? ");
			sqljob.addLikeParameter(name);
		}
		if(!"".equals(Common.get(enable))){
			sqljob.appendSQLCondition(" ENABLE = ? ");
			sqljob.addParameter(enable);
		}
		sqljob.appendSQL(" ORDER BY ID ");
		if(logger.isInfoEnabled()) logger.info(sqljob);
		return (List<Restriction>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Restriction insert(Restriction bo) {
		//check pk
		if(null==bo) return null;
		//check exist
		Restriction t = findByCode(bo.getCode());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO RESTRICTION (");
		sqljob.appendSQL(" CODE");
		sqljob.appendSQL(",NAME");
		sqljob.appendSQL(",NAME_SPECIAL");
		sqljob.appendSQL(",ITEM_SPECIAL");
		sqljob.appendSQL(",OTHER_SPECIAL");
		sqljob.appendSQL(",OTHER_NOTE");
		sqljob.appendSQL(",ORG_TYPE");
		sqljob.appendSQL(",RELATED_UNIT");
		sqljob.appendSQL(",RELATED_DATE");
		sqljob.appendSQL(",RELATED_NO");
		sqljob.appendSQL(",ENABLE");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getCode());
		sqljob.addParameter(bo.getName());
		sqljob.addParameter(bo.getNameSpecial());
		sqljob.addParameter(bo.getItemSpecial());
		sqljob.addParameter(bo.getOtherSpecial());
		sqljob.addParameter(bo.getOtherNote());
		sqljob.addParameter(bo.getOrgType());
		sqljob.addParameter(bo.getRelatedUnit());
		sqljob.addParameter(bo.getRelatedDate());
		sqljob.addParameter(bo.getRelatedNo());
		sqljob.addParameter(bo.getEnable());
		sqljob.addParameter(bo.getModIdNo());
		sqljob.addParameter(bo.getModDate());
		sqljob.addParameter(bo.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
			,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
			});
		return findByCode(bo.getCode());
	}
	
	public Restriction update(Restriction bo) {
		//check pk
		if(null==bo) return null;
		if("".equals(Common.get(bo.getId()))) return null;
		//check exist
		Restriction t = findById(bo.getId());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE RESTRICTION SET");
			sqljob.appendSQL(" CODE = ?");
			sqljob.appendSQL(",NAME = ?");
			sqljob.appendSQL(",NAME_SPECIAL = ?");
			sqljob.appendSQL(",ITEM_SPECIAL = ?");
			sqljob.appendSQL(",OTHER_SPECIAL = ?");
			sqljob.appendSQL(",OTHER_NOTE = ?");
			sqljob.appendSQL(",ORG_TYPE = ?");
			sqljob.appendSQL(",RELATED_UNIT = ?");
			sqljob.appendSQL(",RELATED_DATE = ?");
			sqljob.appendSQL(",RELATED_NO = ?");
			sqljob.appendSQL(",ENABLE = ?");
			sqljob.appendSQL(",MOD_ID_NO = ?");
			sqljob.appendSQL(",MOD_DATE = ?");
			sqljob.appendSQL(",MOD_TIME = ?");
			sqljob.appendSQL("WHERE ID = ?");
			sqljob.addParameter(bo.getCode());
			sqljob.addParameter(bo.getName());
			sqljob.addParameter(bo.getNameSpecial());
			sqljob.addParameter(bo.getItemSpecial());
			sqljob.addParameter(bo.getOtherSpecial());
			sqljob.addParameter(bo.getOtherNote());
			sqljob.addParameter(bo.getOrgType());
			sqljob.addParameter(bo.getRelatedUnit());
			sqljob.addParameter(bo.getRelatedDate());
			sqljob.addParameter(bo.getRelatedNo());
			sqljob.addParameter(bo.getEnable());
			sqljob.addParameter(bo.getModIdNo());
			sqljob.addParameter(bo.getModDate());
			sqljob.addParameter(bo.getModTime());
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
				});
			return findById(bo.getId());
		}
	}
	
	public void delete(Restriction bo) {
		//check pk
		if(null!=bo && CommonStringUtils.isNotEmpty(Common.get(bo.getId()))) {
			SQLJob sqljob = new SQLJob("DELETE RESTRICTION WHERE ID = ? ");
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public Restriction mapRow(ResultSet rs, int idx) throws SQLException {
		Restriction obj = null;
		if(null!=rs) {
			obj = new Restriction();
			obj.setId(rs.getInt("ID"));
			obj.setCode(rs.getString("CODE"));
			obj.setName(rs.getString("NAME"));
			obj.setNameSpecial(rs.getString("NAME_SPECIAL"));
			obj.setItemSpecial(rs.getString("ITEM_SPECIAL"));
			obj.setOtherSpecial(rs.getString("OTHER_SPECIAL"));
			obj.setOtherNote(rs.getString("OTHER_NOTE"));
			obj.setOrgType(rs.getString("ORG_TYPE"));
			obj.setRelatedUnit(rs.getString("RELATED_UNIT"));
			obj.setRelatedDate(rs.getString("RELATED_DATE"));
			obj.setRelatedNo(rs.getString("RELATED_NO"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}

}