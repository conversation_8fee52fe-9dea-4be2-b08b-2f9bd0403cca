<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="msgbar" scope="request" class="com.kangdainfo.tcfi.view.common.Msgbar">
	<jsp:setProperty name="msgbar" property="*"/>
</jsp:useBean>
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
<tr><td width="60px;">訊息列:</td><td style="text-align:left;"><div id="ERRMSG" class="field_MSGERR">&nbsp;</div></td></tr>
</table>
<script type="text/javascript">
function showMsgBar(msg) {
	$('#ERRMSG').html(msg+'&nbsp;');
	//if(msg.indexOf('完成') > -1 || msg.indexOf('成功') > -1) {
	//	$('#ERRMSG').attr('class','field_MSG');
	//}else{
	//	$('#ERRMSG').attr('class','field_MSGERR');
	//}
}
showMsgBar('<c:out value="${msgbar.errorMsg}" />&nbsp;');
</script>