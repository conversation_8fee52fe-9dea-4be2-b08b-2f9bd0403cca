package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.util.PrefixConstants;

/**
 * 同音同義字維護
 *
 */
public class PRE8010 extends SuperBean{
	
	private String q_id;
	private String q_sameName;
	private String q_sameName1;
	private String q_source;

	private String id;
	private String sameName;
	private String sameName1;
	private String source;

	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String s) {this.q_id = checkSet(s);}
	public String getQ_sameName() {return checkGet(q_sameName);}
	public void setQ_sameName(String s) {this.q_sameName = checkSet(s);}
	public String getQ_sameName1() {return checkGet(q_sameName1);}
	public void setQ_sameName1(String s) {this.q_sameName1 = checkSet(s);}
	public String getQ_source() {return checkGet(q_source);}
	public void setQ_source(String s) {this.q_source = checkSet(s);}
	
	public String getId() {return checkGet(id);}
	public void setId(String s) {this.id = checkSet(s);}
	public String getSameName() {return checkGet(sameName);}
	public void setSameName(String s) {this.sameName = checkSet(s);}
	public String getSameName1() {return checkGet(sameName1);}
	public void setSameName1(String s) {this.sameName1 = checkSet(s);}
	public String getSource() {return checkGet(source);}
	public void setSource(String s) {this.source = checkSet(s);}

	@Override
	public Object doQueryOne() throws Exception {
		PRE8010 obj = this;
		Cedbc058 c = ServiceGetter.getInstance().getPre8010Service().getCedbc058ById(Common.getInt(getId()));
		if(c != null){
			obj.setSameName1(c.getSameName1());
			obj.setSameName(c.getSameName());
			obj.setSource(c.getSource());
			obj.setEditID(c.getUpdateUser());
			obj.setEditDate(c.getUpdateDate());		
		} else {
			obj.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}

	public ArrayList<String[]> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<Cedbc058> objList = ServiceGetter.getInstance().getPre8010Service().getCedbc058ByCondition(getQ_sameName1(), getQ_source());
		if (objList != null && objList.size() > 0){
			for(Cedbc058 dtl : objList){
				String[] rowArray = new String[6];
				rowArray[0] = Common.get(dtl.getId());
				rowArray[1] = dtl.getSameName1();
				rowArray[2] = dtl.getSameName();  
				rowArray[3] = Common.get(dtl.getSource());  
				rowArray[4] = Common.get(dtl.getUpdateUser());  
				rowArray[5] = Common.formatDateTime(dtl.getUpdateDate(), 4);  
				arrList.add(rowArray);
			}
			objList.clear();
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}

	public void doCreate() throws Exception {
		if("".equals(Common.get(getSameName())) || "".equals(Common.get(getSameName1())))
			throw new MoeaException("資料有誤，請重新輸入!!");
		
		if(getUpdateCheck()){
			String baseName = checkBaseName(getSameName(), getSameName1());
			Cedbc058 obj = new Cedbc058();
			obj.setSameName1(getSameName1());
			obj.setSameName(getSameName());
			obj.setBaseName(baseName);
			obj.setCanUse("Y");
			obj.setBeUsed("N");
			obj.setStatus("1");
			obj.setSource(getSource());
			obj.setCreateUser(getLoginUserId());
			obj.setCreateDate(Datetime.getYYYMMDD());
			obj.setCreateTime(Datetime.getHHMMSS());
			obj.setUpdateUser(getLoginUserId());
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj.setEnabled("N");
			obj = ServiceGetter.getInstance().getPre8010Service().insertCedbc058(obj);
			setIndexLog(obj);
			this.setId(Common.get(obj.getId()));
			//因為要insert 2筆，通過getUpdateCheck()檢核後第二次還是需要做檢核
			Cedbc058 o = getSameCedbc058(getSameName(), getSameName1());
			if(o!=null){
				obj = o;
				obj.setBaseName(baseName);
				obj.setCanUse("Y");
				obj.setBeUsed("N");
				obj.setStatus("1");
				obj.setSource(getSource());
				obj.setUpdateUser(getLoginUserId());
				obj.setUpdateDate(Datetime.getYYYMMDD());
				obj.setUpdateTime(Datetime.getHHMMSS());
				obj.setEnabled("N");
				obj = ServiceGetter.getInstance().getPre8010Service().updateCedbc058(obj);
			}else{
				//第 2 筆 SameName1 與 SameName 互換
				obj.setSameName1(getSameName());
				obj.setSameName(getSameName1());
				obj.setEnabled("N");
				obj = ServiceGetter.getInstance().getPre8010Service().insertCedbc058(obj);
			}
			setIndexLog(obj);
			reLoadCedbc058();
		}else{
			throw new MoeaException("已存在相同同音同義字的資料，請重新輸入！");
		}
	}

	public void doUpdate() throws Exception {
		if("".equals(Common.get(getSameName())) || "".equals(Common.get(getSameName1())))
			throw new MoeaException("資料有誤，請重新輸入!!");
		
		Cedbc058 obj = ServiceGetter.getInstance().getPre8010Service().getCedbc058ById(Common.getInt(getId()));
		if(obj == null)	throw new MoeaException("資料不存在!");
		
		if(getUpdateCheck()){
			String baseName = checkBaseName(getSameName(), getSameName1()) ;
			obj.setSameName1(getSameName1());
			obj.setSameName(getSameName());
			obj.setBaseName(baseName);
			obj.setStatus("2");
			obj.setSource(getSource());
			obj.setUpdateUser(getLoginUserId());
			obj.setUpdateDate(Datetime.getYYYMMDD());
			obj.setUpdateTime(Datetime.getHHMMSS());
			obj = ServiceGetter.getInstance().getPre8010Service().updateCedbc058(obj);
			setIndexLog(obj);
			reLoadCedbc058();
		}else{
			throw new MoeaException("已存在相同同音同義字的資料，請重新輸入！");
		}
	}
	
	public void doDelete() throws Exception {
		boolean canDelete = true;
		Cedbc058 obj  = ServiceGetter.getInstance().getPre8010Service().getCedbc058ById(Common.getInt(getId()));
		if(obj != null){
			String sameName  = obj.getSameName();
			//檢查本筆資料是否已被使用，若已使用則註記本筆資料Status = 3 and Can_Use = 'N'
			if("Y".equals(obj.getBeUsed())){
				obj.setCanUse("N");
				obj.setStatus("3");
				obj.setUpdateUser(getLoginUserId());
				obj.setUpdateDate(Datetime.getYYYMMDD());
				obj.setUpdateTime(Datetime.getHHMMSS());
				obj = ServiceGetter.getInstance().getPre8010Service().updateCedbc058(obj);
				setIndexLog(obj);
				canDelete = false;
			}
			
			Cedbc058 obj2  = ServiceGetter.getInstance().getPre8010Service().getCedbc058BySameNameForDelete(sameName);
			if (obj2 != null) {
				if("Y".equals(obj2.getBeUsed())){
					obj2.setCanUse("N");
					obj2.setStatus("3");
					obj2.setUpdateUser(getLoginUserId());
					obj2.setUpdateDate(Datetime.getYYYMMDD());
					obj2.setUpdateTime(Datetime.getHHMMSS());
					obj2 = ServiceGetter.getInstance().getPre8010Service().updateCedbc058(obj2);
					setIndexLog(obj2);
					canDelete = false;
				}
				if (canDelete) {
				//沒使用即可刪除
					// System.out.println("==="+obj.getId()+"==="+obj2.getId());
					ServiceGetter.getInstance().getPre8010Service().deleteCedbc058Both(obj.getId(), obj2.getId());
				}
				reLoadCedbc058();
				this.setId("");
			} // if
		}else{
			throw new Exception("查無資料，無法刪除，請重新操作 !");
		}
	}

	/**
	 * 檢查基礎字(baseName)
	 * 1.先檢查是否已經有相關的字,有的話,就用已經存在的基礎字
	 * 2.若沒有,就判斷哪個字是常用字,拿常用字來當基礎字
	 */
	private String checkBaseName(String sameName, String sameName1){
		String baseName = "";
		Cedbc058 obj = ServiceGetter.getInstance().getPre8010Service().getCedbc058BySameName(sameName, sameName1);
		if(obj != null && !"".equals(Common.get(obj.getBaseName()))){
			baseName = obj.getBaseName();
		} else {
			Integer sameNameCount = checkSameNameCount(sameName);
			Integer sameName1Count = checkSameNameCount(sameName1);
			if(sameNameCount > sameName1Count) {
				baseName = sameName;
			} else if(sameName1Count > sameNameCount) {
				baseName = sameName1;
			} else {
				//預設用 sameName1
				baseName = sameName1;
			}
		}
		return baseName;
	}
	
	private Integer checkSameNameCount(String sameName) {
		Integer count = 0;
		try {
			SearchResult sr = ServiceGetter.getInstance().getIndexSearchService().searchRebuild(sameName);
			if(null!=sr) {
				count = sr.getRecordCount();
			}
		} catch (Exception e) {
			count = 0;
		}
		return count;
	}

	/** 檢核 同音同義字 是否重複 */
	private boolean getUpdateCheck(){
		Cedbc058 o = getSameCedbc058(getSameName1(), getSameName());
		if(o == null) {
			return true;
		} else {
			if("update".equals(this.getState()) && Common.get(o.getId()).equals(this.getId()))
				return true;
		}
		return false;
	}

	/** 取得CEDBC058 SameName1與SameName相同的同音同義字 */
	private Cedbc058 getSameCedbc058(String sameName1, String sameName){
		Cedbc058 o = new Cedbc058();
		o.setSameName1(sameName1);
		o.setSameName(sameName);
		return ServiceGetter.getInstance().getPre8010Service().getCedbc058ByCheck(o);
	}

	private void reLoadCedbc058(){
		ServiceGetter.getInstance().getCedbc058CodeLoader().reload();
	}

	//同步異動全文檢索索引
	private void setIndexLog(Cedbc058 obj){
		if(obj == null) return;
		//先判斷是否有之前已經異動過還沒有執行的IndexLog
		if(ServiceGetter.getInstance().getCom0001Service().checkIndexLog(PrefixConstants.JOB_WS10003, Common.get(obj.getId())) != null)
			return;
		ServiceGetter.getInstance().getCom0001Service().Fun_0010(PrefixConstants.JOB_WS10003,Common.get(obj.getId()),null,null,Datetime.getYYYMMDDHHMISS(),getLoginUserId());
	}
}
