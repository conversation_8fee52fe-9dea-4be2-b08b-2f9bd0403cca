package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.util.lang.CommonStringUtils;

public class Cedb1022Dao
	extends BaseDaoJdbc
	implements RowMapper<Cedb1022>
{
	public Cedb1022 findByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1022 WHERE PREFIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		List<?> list = getJdbcTemplate().query(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray(), this);
		return list.isEmpty() ? null : (Cedb1022) list.get(0);
	}

	public int insert(Cedb1022 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1022 (PREFIX_NO,APPLY_BAN_NO,APPLY_LAW_NAME) VALUES (?,?,?)");
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyLawName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob); 
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public int update(Cedb1022 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("UPDATE CEDB1022 SET");
		sqljob.appendSQL("  APPLY_BAN_NO=?");
		sqljob.appendSQL(" ,APPLY_LAW_NAME=?");
		sqljob.appendSQL(" WHERE PREFIX_NO=?");
		sqljob.addParameter(o.getApplyBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyLawName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public void deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1022 WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1022 mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1022 obj = null;
		if (null != rs) {
			obj = new Cedb1022();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApplyBanNo(rs.getString("APPLY_BAN_NO"));
			obj.setApplyLawName(rs.getString("APPLY_LAW_NAME"));
		}
		return obj;
	}

}