package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.IndexLog;

public interface Com0001Service {

	/**
	 * 公司、預查新增一筆紀錄
	 */
	public void Fun_0010(String wsId , String param1, String param2, String param3, String executeDate, String createUser);
	
	/** 公司、預查新增一筆紀錄 */
	public void Fun_0010(String wsId , String param1, String param2, String param3, String executeDate, String createUser, String remark);
	
	/**
	 * 更新待辦工作狀態
	 */
	public IndexLog Fun_0011(String execType, Long indexLogId, String msg);
	
	/** 檢查是否已有相同待執行的IndexLog */
	public IndexLog checkIndexLog(String wsId, String param1);
}