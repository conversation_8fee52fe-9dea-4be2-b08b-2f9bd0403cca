<!-- 
程式目的：展期案件查詢
程式代號：pre4016
程式日期：1030623
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4016">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4016" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function init(){
	if ( form1.state.value == "queryAllSuccess" )
		document.getElementById("listContainer").style.display = '';
	else
		document.getElementById("listContainer").style.display = 'none';
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_dateStart,"收文日期起");
	alertStr += checkEmpty(form1.q_dateEnd,"收文日期迄");
	alertStr += checkDate(form1.q_dateStart,"收文日期起") ;
	alertStr += checkDate(form1.q_dateEnd,"收文日期迄") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function queryOne(){}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>
<!-- Form area -->

<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4016'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="15%"><font color="red">*</font>收文日期：</td>
			<td class="td_form_white" width="85%">
				(起)<%=View.getPopCalendar("field_Q","q_dateStart",obj.getQ_dateStart()) %>
				~(迄)<%=View.getPopCalendar("field_Q","q_dateEnd",obj.getQ_dateEnd()) %>
				&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>
		<tr>
			<td class="td_form">核覆結果：</td>
			<td class="td_form_white">
				<%=View.getCheckBoxTextOption("","q_approveResult", "A;審核中;Y;核准;N;否準", obj.getQ_approveResult()) %>
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
 
<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">收文時間</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">展期時間</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦人</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦工作天數</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">展期原因</a></th>
	<tr>
	</thead>
	<tbody id="listTBODY">
	  <%
	  boolean primaryArray[] = {true,false,false,false,false,false};
	  boolean displayArray[] = {true,true,true,true,true,true};
	  String[] alignArray = {"center", "center","center","center","center","left"};
	  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag()));
	  %>
	</tbody>
</table>
</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>