package com.kangdainfo.tcfi.view.common;

import java.util.ArrayList;

import com.kangdainfo.common.util.SuperBean;

/**
 * 快捷列
 *
 */
public class Shortcut extends SuperBean {

	private String functions;
	private String shortcut;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getFunctions() {return checkGet(functions);}
	public void setFunctions(String s) {this.functions = checkSet(s);}
	public String getShortcut() {return checkGet(shortcut);}
	public void setShortcut(String s) {this.shortcut = checkSet(s);}

}