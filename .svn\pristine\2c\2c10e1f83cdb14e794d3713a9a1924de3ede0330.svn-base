<!--
程式目的：審核-案件異動紀錄查詢
程式代號：PRE3007_00
撰寫日期：103.06.30
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3007" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3007">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>
<%
if ("init".equals(obj.getState())) {
} else if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script type="text/javascript">
function validateInput() {
	var hasInput = false;
	if( ''!=$('#q_prefixNo').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_banNo').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_applyId').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_applyName').val() ) {
		hasInput = true;
	}
	if( ''!=$('#q_companyName').val() ) {
		hasInput = true;
	}
	return hasInput;
}

$( document ).ready(function() {

	//事件註冊
	$('#q_prefixNo').keydown(function(e){
		if ( e.which == 13 ) {
			var applyId = document.getElementById("q_applyId");
			toUpper(applyId);
			if( this.value.length >= 5 ) {
				e.preventDefault();
				$('#doSearch').click();
			} else {
				showMsgBar('預查編號太短，請確認後再執行查詢!');
				$("#listContainer").hide();
			}
		}
	});
	$('#q_banNo, #q_applyId, #q_applyName, #q_companyName').keydown(function(e){
		if ( e.which == 13 ) {
			var applyId = document.getElementById("q_applyId");
			toUpper(applyId);
			if( this.value.length > 0 ) {
				e.preventDefault();
				$('#doSearch').click();
			}
		}
	});
	//重新輸入
	$('#resetForm').click(function(){
		$('#q_prefixNo, #q_banNo, #q_applyId, #q_applyName, #q_companyName').val('');
		showMsgBar('&nbsp;');
		$("#listContainer").hide();
	});
	//執行查詢
	$('#doSearch').click(function(){
		if( validateInput() ) {
			form1.state.value = "queryAll";
			form1.submit();
		} else {
			showMsgBar('未輸入任何查詢條件，請確認後再執行查詢!');
			$("#listContainer").hide();
		}
	});
	//全選
	$("#checkAll").click(function(){
		commonUtils.all("prefixNos");
	});
	//全不選
	$("#uncheckAll").click(function(){
		commonUtils.unAll("prefixNos");
	});
	//確認送出
	$("#showDetail").click(function(){
		var $checks = $("input[name=prefixNos]").filter(":checked");
		if( $checks.size() > 0) {
			form1.state.value = "init";
			form1.action = getVirtualPath() + "tcfi/pre/pre3007_00.jsp";
			$.cookie("activeTabIndex", 0);
			form1.submit();
		}
	});

<% if( null!=objList && objList.size() == 1 ) { %>
//只有一筆資料時, 直接顯示明細
commonUtils.all("prefixNos");
$("#showDetail").click();
<% } %>

<% if ("true".equals(obj.getQueryAllFlag())){ %>
//執行查詢以後才顯示清單
$("#listContainer").show();
<% } %>

$("#checkAll").click();
});

function queryOne(v) {
}
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<table width="100%" cellspacing="0" cellpadding="0">
<tbody><tr><td class="td_default_banner" width="100%">案件異動紀錄查詢-案件選擇(PRE3007)</td></tr></tbody>
</table>
<script type="text/javascript">
<!--
$(window).load(function(){
	if (isObj(top) && isObj(top.fbody) && isObj(top.fbody.menuleft)) top.fbody.menuleft.doMax();
});
//-->
</script>

<!--上方按鈕-->
<table class="bgToolbar" width="100%">
	<tr>
		<td align="left">
			<input class="toolbar_default" type="button" id="resetForm" name="resetForm" value="重新輸入" />&nbsp;
			<input class="toolbar_default" type="button" id="doSearch" name="doSearch" value="執行查詢" />&nbsp;
		</td>
		<td align="right">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
			</c:import>
		</td>
	</tr>
</table>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr >
		<td class="td_form" width="140px">預查編號</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_prefixNo" name="q_prefixNo" size="15" maxlength="9" value="<%=obj.getQ_prefixNo() %>" />
		</td>
		<td class="td_form" width="140px">統一編號</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_banNo" name="q_banNo" size="15" maxlength="8" value="<%=obj.getQ_banNo() %>" />
		</td>
	</tr>
	<tr >
		<td class="td_form">申請人身分ID</td>
		<td class="td_form_white">
			<input type="text" class="field" id="q_applyId" name="q_applyId" size="15" value="<%=obj.getQ_applyId() %>" onblur="toUpper(this);"/>
		</td>
		<td class="td_form">申請人姓名</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field cmex" id="q_applyName" name="q_applyName" size="27" value="<%=obj.getQ_applyName() %>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">預查名稱</td>
		<td class="td_form_white" colspan="3">
			<input type="text" class="field cmex" id="q_companyName" name="q_companyName" size="50" value="<%=obj.getQ_companyName() %>" />
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>


<tr><td class="bgList">
	<div id="listContainer" style="display:none;">
	<table>
		<tr><td>
			<input class="toolbar_default" type="button" id="checkAll" name="checkAll" value="全部選取">&nbsp;
			<input class="toolbar_default" type="button" id="uncheckAll" name="uncheckAll" value="取消選取">&nbsp;
			<input class="toolbar_default" type="button" id="showDetail" name="showDetail" value="確認送出">&nbsp;
		</td></tr>
	</table>
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
  		    <th class="listTH" width="3%">&nbsp;</th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">預查編號</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">異動日期</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">異動時間</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">異動人</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">申請人</a></th>
    		<th class="listTH" width="8%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">預查種類</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">核覆結果</a></th>
    		<th class="listTH"            style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">預查名稱</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',11,false);" href="#">承辦人</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">保留期限</a></th>
    		<th class="listTH" width="7%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">狀況</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = { true,false,false,false,false,false,false,false,false,false,false,false};
  			boolean displayArray[] = {false, true, true, true, true, true, true, true, true, true, true, true};
  			String[] alignArray = {"left","left","left","left","left","left","left","left","left","left","left","left"};
  			out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,"true","prefixNos"));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<input type="hidden" id="functionName" name="functionName" value="querylog" />
		<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>