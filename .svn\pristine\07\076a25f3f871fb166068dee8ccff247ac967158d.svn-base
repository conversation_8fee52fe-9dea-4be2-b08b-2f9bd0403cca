package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.util.lang.CommonStringUtils;

public class Cedbc000Dao extends BaseDaoJdbc implements RowMapper<Cedbc000> {
	
	private static final String queryAll = "SELECT * FROM CEDBC000";
	public List<Cedbc000> queryAll() {
		SQLJob sqljob = new SQLJob(queryAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByIdNo = "SELECT * FROM CEDBC000 WHERE ID_NO = ?";
	public Cedbc000 findByIdNo(String idNo) {
		if(CommonStringUtils.isEmpty(idNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByIdNo);
		sqljob.addParameter(idNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedbc000> list = (List<Cedbc000>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}

	private static final String SQL_FIND_BY_ID_TOKEN = "SELECT * FROM CEDBC000 WHERE ID_NO = ? AND ID_PASSWORD = ?";
	public Cedbc000 findByIdNoAndPwd(String idNo, String pwd) {
		if(CommonStringUtils.isEmpty(idNo)) return null;
		if(CommonStringUtils.isEmpty(pwd)) return null;
		SQLJob sqljob = new SQLJob(SQL_FIND_BY_ID_TOKEN);
		sqljob.addParameter(idNo);
		sqljob.addParameter(pwd);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedbc000> list = (List<Cedbc000>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}
	
	public List<Cedbc000> queryByCondition(String id, String name, String unit) {
		SQLJob sqljob = new SQLJob(queryAll);
		if(!"".equals(Common.get(id))){
			sqljob.appendSQLCondition(" ID_NO = ? ");
			sqljob.addParameter(id);
		}
		if(!"".equals(Common.get(name))){
			sqljob.appendSQLCondition(" STAFF_NAME LIKE ? ");
			sqljob.addLikeParameter(name);
		}
		if(!"".equals(Common.get(unit))){
			sqljob.appendSQLCondition(" STAFF_UNIT = ? ");
			sqljob.addParameter(unit);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Cedbc000 insert(Cedbc000 bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getIdNo())) return null;
		//check exist
		Cedbc000 t = findByIdNo(bo.getIdNo());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO CEDBC000 (");
		sqljob.appendSQL(" STAFF_CODE");
		sqljob.appendSQL(",STAFF_PASSWORD");
		sqljob.appendSQL(",STAFF_TITLE");
		sqljob.appendSQL(",STAFF_NAME");
		sqljob.appendSQL(",GROUP_ID");
		sqljob.appendSQL(",BRANCH_PHONE_NO");
		sqljob.appendSQL(",UPDATE_USER");
		sqljob.appendSQL(",UPDATE_DATE");
		sqljob.appendSQL(",UPDATE_TIME");
		sqljob.appendSQL(",STAFF_PASSWD");
		sqljob.appendSQL(",CHANGE_DATE");
		sqljob.appendSQL(",STAFF_UNIT");
		sqljob.appendSQL(",ID_NO");
		sqljob.appendSQL(",ID_PASSWORD");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getStaffCode());
		sqljob.addParameter(bo.getStaffPassword());
		sqljob.addParameter(bo.getStaffTitle());
		sqljob.addParameter(bo.getStaffName());
		sqljob.addParameter(bo.getGroupId());
		sqljob.addParameter(bo.getBranchPhoneNo());
		sqljob.addParameter(bo.getUpdateUser());
		sqljob.addParameter(bo.getUpdateDate());
		sqljob.addParameter(bo.getUpdateTime());
		sqljob.addParameter(bo.getStaffPasswd());
		sqljob.addParameter(bo.getChangeDate());
		sqljob.addParameter(bo.getStaffUnit());
		sqljob.addParameter(bo.getIdNo());
		sqljob.addParameter(bo.getIdPassword());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR}
				);
		return findByIdNo(bo.getIdNo());
	}
	
	public Cedbc000 update(Cedbc000 bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getId())) return null;
		//check exist
		Cedbc000 t = findByIdNo(bo.getId());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE CEDBC000 SET");
			sqljob.appendSQL("STAFF_CODE=?");
			sqljob.appendSQL(",STAFF_PASSWORD=?");
			sqljob.appendSQL(",STAFF_TITLE=?");
			sqljob.appendSQL(",STAFF_NAME=?");
			sqljob.appendSQL(",GROUP_ID=?");
			sqljob.appendSQL(",BRANCH_PHONE_NO=?");
			sqljob.appendSQL(",UPDATE_USER=?");
			sqljob.appendSQL(",UPDATE_DATE=?");
			sqljob.appendSQL(",UPDATE_TIME=?");
			sqljob.appendSQL(",STAFF_PASSWD=?");
			sqljob.appendSQL(",CHANGE_DATE=?");
			sqljob.appendSQL(",STAFF_UNIT=?");
			sqljob.appendSQL(",ID_PASSWORD=?");
			sqljob.appendSQL(",ID_NO=?");
			sqljob.appendSQL("WHERE ID_NO=?");
			sqljob.addParameter(bo.getStaffCode());
			sqljob.addParameter(bo.getStaffPassword());
			sqljob.addParameter(bo.getStaffTitle());
			sqljob.addParameter(bo.getStaffName());
			sqljob.addParameter(bo.getGroupId());
			sqljob.addParameter(bo.getBranchPhoneNo());
			sqljob.addParameter(bo.getUpdateUser());
			sqljob.addParameter(bo.getUpdateDate());
			sqljob.addParameter(bo.getUpdateTime());
			sqljob.addParameter(bo.getStaffPasswd());
			sqljob.addParameter(bo.getChangeDate());
			sqljob.addParameter(bo.getStaffUnit());
			sqljob.addParameter(bo.getIdPassword());
			sqljob.addParameter(bo.getIdNo());
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
					,new int[]{
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR}
					);
			return findByIdNo(bo.getIdNo());
		}
	}
	
	public void delete(Cedbc000 bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getIdNo()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM CEDBC000 WHERE ID_NO = ?");
			sqljob.addParameter(bo.getIdNo());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}

	public Cedbc000 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc000 obj = null;
		if(null!=rs) {
			obj = new Cedbc000();
			obj.setStaffCode(rs.getString("STAFF_CODE"));
			obj.setStaffPassword(rs.getString("STAFF_PASSWORD"));
			obj.setStaffTitle(rs.getString("STAFF_TITLE"));
			obj.setStaffName(rs.getString("STAFF_NAME"));
			obj.setGroupId(rs.getString("GROUP_ID"));
			obj.setBranchPhoneNo(rs.getString("BRANCH_PHONE_NO"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
			obj.setStaffPasswd(rs.getString("STAFF_PASSWD"));
			obj.setChangeDate(rs.getString("CHANGE_DATE"));
			obj.setStaffUnit(rs.getString("STAFF_UNIT"));
			obj.setIdNo(rs.getString("ID_NO"));
			obj.setIdPassword(rs.getString("ID_PASSWORD"));
		}
		return obj;
	}

}