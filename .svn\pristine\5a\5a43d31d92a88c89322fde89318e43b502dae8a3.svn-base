package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1011;

public class Cedb1011Dao extends BaseDaoJdbc implements RowMapper<Cedb1011> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1011 WHERE PREFIX_NO = ?";

	public List<Cedb1011> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled())
			logger.debug(sqljob);
		return (List<Cedb1011>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Cedb1011 queryByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled())
			logger.debug(sqljob);
		List<Cedb1011> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : (Cedb1011) list.get(0);
	}

	private static String sql_saveByObj = "INSERT INTO Cedb1011(PREFIX_NO, FOREIGN_MARK, COMPANY_NAME, BAN_NO, CHINA_MARK) "
			+ "VALUES (?, ?, ?, ?, ?) ";

	public int insert(Cedb1011 cedb1011) {
		if (cedb1011 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_saveByObj);

		sqljob.addParameter(cedb1011.getPrefixNo());
		sqljob.addParameter(cedb1011.getForeignMark());
		sqljob.addParameter(cedb1011.getCompanyName());
		sqljob.addParameter(cedb1011.getBanNo());
		sqljob.addParameter(cedb1011.getChinaMark());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getTypes());
	}
	
	private static String sql_updateByObj = "UPDATE Cedb1011 SET FOREIGN_MARK = ?, COMPANY_NAME = ?, BAN_NO = ?, CHINA_MARK = ? WHERE PREFIX_NO = ?";
	public int update(Cedb1011 cedb1011) {
		if (cedb1011 == null)
			return 0;

		SQLJob sqljob = new SQLJob(sql_updateByObj);

		sqljob.addParameter(cedb1011.getForeignMark());
		sqljob.addParameter(cedb1011.getCompanyName());
		sqljob.addParameter(cedb1011.getBanNo());
		sqljob.addParameter(cedb1011.getChinaMark());
		sqljob.addParameter(cedb1011.getPrefixNo());

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getTypes());
	}

	private static final String SQL_deleteByPrefixNo = "DELETE FROM CEDB1011 WHERE PREFIX_NO = ?";

	public int deleteByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_deleteByPrefixNo);
		sqljob.addParameter(prefixNo);

		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	private int[] getTypes() {
		return new int[]{
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}

	@Override
	public Cedb1011 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1011 obj = null;
		if (null != rs) {
			obj = new Cedb1011();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setForeignMark(rs.getString("FOREIGN_MARK"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setChinaMark(rs.getString("CHINA_MARK"));
		}
		return obj;
	}

}
