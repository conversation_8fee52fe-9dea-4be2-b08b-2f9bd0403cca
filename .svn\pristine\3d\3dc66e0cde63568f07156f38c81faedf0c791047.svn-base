package com.kangdainfo.tcfi.view.pre;
/* 
程式目的：每月申請案件統計表
程式代號：pre2006
程式日期：1030603
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
public class PRE2006 extends SuperBean {
	
	private String q_yearMonth ;
    private String c1 ;
    private String c2 ;
    private String c3 ;
	
	public String getQ_yearMonth() {return checkGet(q_yearMonth);}
	public void setQ_yearMonth(String s) {q_yearMonth = checkSet(s);}
	public String getC1() {return checkGet(c1);}
	public void setC1(String s) {c1 = checkSet(s);}
	public String getC2() {return checkGet(c2);}
	public void setC2(String s) {c2 = checkSet(s);}
	public String getC3() {return checkGet(c3);}
	public void setC3(String s) {c3 = checkSet(s);}
	
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		PRE2006 pre2006 = this ;
		if ( getQ_yearMonth().length() != 5 ) {
			setErrorMsg( "請輸入正確的時間格式yyy/mm" ) ;
			pre2006.setC1( "" ) ;
    	    pre2006.setC2( "" ) ;
    	    pre2006.setC3( "" ) ;
		    return pre2006 ;
		} // end if
		else {
		    List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_yearMonth() ));
		    if ( "0".equals( Common.get(rs.get(0).get("c4"))) ) {
		    	setErrorMsg( "該月份沒有已發文紀錄"  ) ;
		    	pre2006.setC1( "" ) ;
	    	    pre2006.setC2( "" ) ;
	    	    pre2006.setC3( "" ) ;
			    return pre2006 ;	
		    } // end if
		    else {
    	      pre2006.setC1( Common.get(rs.get(0).get("c1")) ) ;
    	      pre2006.setC2( Common.get(rs.get(0).get("c2")) ) ;
    	      pre2006.setC3( Common.get(rs.get(0).get("c3")) ) ;
		      return pre2006 ;
		    } // end else
		} // end else 
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
		return null ;
    } // doQueryAll()
	
	
	public SQLJob doAppendSqljob( String yearMonth  ) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select") ;
		sqljob.appendSQL("sum( case when telix_no like 'Z%' then 1 else 0 end ) as c1") ;
		sqljob.appendSQL(", sum( case when get_kind = '2' then 1 else 0 end ) as c2") ;
		sqljob.appendSQL(", sum( case when get_kind = '1' then 1 else 0 end ) as c3") ;
		sqljob.appendSQL(", count(1) as c4") ;
		sqljob.appendSQL("from eicm.cedb1000 where close_date like '" + yearMonth + "%'") ;
		return sqljob ;
	}  // doAppendSqljob()
	
	
	public File doPrintPdf() throws Exception {
		return null ;
	} // doPrintPdf()
} // PRE2006