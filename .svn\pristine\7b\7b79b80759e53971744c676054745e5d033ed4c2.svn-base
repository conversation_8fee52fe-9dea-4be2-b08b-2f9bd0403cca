<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4008"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String dateStart = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateStart")));
String dateEnd = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("dateEnd")));
String staff = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("staff")));
String companyName = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("compnayName")));
try {
	if ( ( dateStart != null && !"".equals(dateStart) && dateEnd != null && !"".equals(dateEnd ) ) )
	{
		String checkResult = PRE4008.checkForjsp(dateStart, dateEnd, staff, companyName);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>