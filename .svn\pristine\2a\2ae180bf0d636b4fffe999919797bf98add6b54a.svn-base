<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.PrefixVo"%>
<%@ page import="com.google.gson.*"%>
<%@ page import="org.apache.commons.logging.Log"%>
<%@ page import="org.apache.commons.logging.LogFactory"%>
<%@ page import="org.apache.log4j.Logger"%>
<%
Log findPrefixDataLog = LogFactory.getLog("findPrefixDataLog");
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
String from = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("from")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		findPrefixDataLog.info(q+",0.find prefixData start---"+System.currentTimeMillis());
		if( "PRE4001".equalsIgnoreCase(from) ) {
			Cedb1000 tempCedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(q);
			//若是尚未發文結案的案件，要新增查閱案件的歷程
			if ( tempCedb1000.getCloseDate() == null || "".equals(tempCedb1000.getCloseDate()) ) {
				ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(q, User.getUserId(), "Q");
			}
		}
		PrefixVo preVo = ServiceGetter.getInstance().getPrefixService().findPrefixData(q, null);
		//新增查詢類的個資軌跡
		if(!"".equals(from)){
			ServiceGetter.getInstance().getTrackLogService().doSearchTrack(from, preVo);
		}
		findPrefixDataLog.info(q+",16.find prefixData complete---"+System.currentTimeMillis());
		out.write(gson.toJson(preVo));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>