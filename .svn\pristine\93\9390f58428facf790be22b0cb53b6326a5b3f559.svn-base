/*--------------------------------------------------|
| dTree 2.05 | www.destroydrop.com/javascript/tree/ |
|---------------------------------------------------|
| Copyright (c) 2002-2003 <PERSON><PERSON><PERSON>?              |
|--------------------------------------------------*/

.dtree {
	font-family: 新細明體, 細明體, Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size: 15px;
	color: #000000;
	white-space: nowrap;
}
.dtree img {
	border: 0px;
	vertical-align: middle;
}
.dtree a {
	color: #333;
	text-decoration: none;
}
.dtree a.node, .dtree a.nodeSel {
	white-space: nowrap;
	padding: 1px 2px 1px 2px;
}
.dtree a.node:hover, .dtree a.nodeSel:hover {
	color: #333;
	text-decoration: underline;
}
.dtree a.nodeSel {
	background-color: #c0d2ec;
}
.dtree .clip {
	overflow: hidden;
}

.dtree .Q {
	background-color: #AAFFAA;	
}

.dtree .M {
	background-color: #FFAAAA;	
}

body {
	font-family: 新細明體, 細明體, Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size: 12px;
	margin-top: 0px;
	margin-left: 12px;
	margin-right: 0px;
	margin-bottom: 0px;
}

.contextmenu {
	font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size: 10pt;
	color: #666;
	white-space: nowrap;
	width: 45px;
	line-height:1.5;
	text-align: center;
	align: center;			
}

.contextmenu a:link {  color: #000000; font-size: 10pt; text-decoration: none}
.contextmenu a:active { color: #3333FF; font-size: 10pt; text-decoration: none}
.contextmenu a:visited { color: #000000 ; font-size: 10pt; text-decoration: none}
.contextmenu a:hover { color: #3333FF; font-size: 10pt; text-decoration: none}	

.checkbox {
	height:12px;
	width:12px;
	padding:2px 2px 2px 2px;
}

.field_RO {
	font-size: 10px;
	COLOR: #333333;
	background-color: #FFFFFF;
	border: 1px #FFFFFF solid;
	border-bottom-color: black;
	border-bottom-style: dashed;			
}	