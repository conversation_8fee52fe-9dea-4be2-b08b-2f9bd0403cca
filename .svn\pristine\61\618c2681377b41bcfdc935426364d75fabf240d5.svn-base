<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE3005"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String prefixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("prefixNo")));
try {
	if ( ( prefixNo != null && !"".equals(prefixNo) ) ){
		String checkResult = PRE3005.checkOutputRpt(prefixNo);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>