package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;

/**
 * 排程(CmpySameName)
 * 背景同名公司檢查
 */
public class CmpySameNameQuartzJobBean  extends BaseQuartzJobBean{
	/**
	 * 背景同名公司檢查程序
	 * 1. 讀取SAMENAME_QUEUE 待執行的資料(一次一筆)
	 * 2. 更新執行紀錄的狀態，status = 1 (執行中)
	 * 3. 用預查編號 撈 CEDB1001 的資料，一個案件最多會有5筆(公司名稱)
	 * 4. 將 CEDB1001 的公司名稱 去碰 CEDB2000 (要完全一樣)
	 * 	  PS. 公司名稱同音同義轉換後，若有基本字的話要各自組出不一樣的名稱，並配上4種公司型態，所以至少有 4 組以上的公司名稱。
	 * 5. 將查詢結果寫入 CEDB1004, 前 3 欄 來自於 CEDB1001, 之後的欄位是檢核結果
	 * 6. 更新執行紀錄的狀態，status = 2 (執行成功), status = 3 (執行失敗)
	 */
	@Override
	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		//1.start
		Queue queueObj = ServiceGetter.getInstance().getSameNameCompareService().getSameNameQueueData();
		if(queueObj != null){
			//2.execute
			queueObj = ServiceGetter.getInstance().getSameNameCompareService().doCheckCmpySameName(queueObj);
			//3.end
			ServiceGetter.getInstance().getSameNameCompareService().doCompleteCheck(queueObj);
		}
	}

}
