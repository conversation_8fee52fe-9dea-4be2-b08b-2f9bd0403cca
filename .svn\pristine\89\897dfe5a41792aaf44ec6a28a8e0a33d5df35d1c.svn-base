<!-- 
程式目的：檢還、撤件及撤回退費
程式代號：pre3005
程式日期：1030606
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3005" />
</jsp:include>
<%
if ( "doWithdraw".equals(obj.getState()) ) {
	obj.doWithdraw();
	if("updateSuccess".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE3005)obj.queryOne();
		obj.setErrorMsg("存檔成功");
	}
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE3005)obj.queryOne();
}else if ( "preview".equals( obj.getState() ) || "printRecipientList".equals(obj.getState()) ) {
	java.io.File report = null;
	String fileType = "";
	if ("preview".equals(obj.getState())) {
		report = obj.doPrintPdf();
		fileType = "PRE3005.pdf";// 2024/03/01修改，僅受款人清單按客戶需求調整為word
	} else {
		report = obj.printRecipientList();
		fileType = "PRE3005.doc";// 2024/03/01修改，僅受款人清單按客戶需求調整為word
	}
	
	if(null!=report){
		//輸出 檔案 到client端
		obj.outputFile(response, report, fileType);// 2024/03/01修改，僅受款人清單按客戶需求調整為word
		out.clear();
		out = pageContext.pushBody();
	}else{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }	
} 
%>
<html>
<head>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/> 
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryOne"){
		alertStr += checkEmpty(form1.q_prefixNo,"預查編號");
	}else if(form1.state.value=="doWithdraw"){
		if($('#prefixNo').val() == "")	alertStr += "請先執行查詢";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}



function changeProcessType() {
	if ($('#noPayMark').val().startsWith("已免繳一次")) {
		return false;
	}
	var e = document.getElementById("processType");
	var value = e.options[e.selectedIndex].value;
	if (value == '04' || value == '03') {
		document.getElementById('contactName').disabled = false;
		document.getElementById('contactAddr').disabled = false;
	} else {
		document.getElementById('contactName').disabled = true;
		document.getElementById('contactAddr').disabled = true;
	}
	document.getElementsByName('processType')[0].value = value;
}

function query(){
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/3;
	//固定800*500
	prop=prop+"width=800px,height=500px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	var url = getVirtualPath() + "tcfi/pre/pre3005Detail.jsp";
	window.open(url,'pre3005Detail',prop);
}

function doSomeCheck(){
	if (form1.prefixNo.value == "") {
		alert('列印前請先執行查詢!');
		return false;
	}
	var prefixNo = form1.prefixNo.value;
	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre3005.jsp?prefixNo='+prefixNo);
	if ( x == 'ok'  )
		return true;
	else 
		document.getElementById("ERRMSG").innerHTML = x;
	return false;
	//setAllReadonly();
}

function doPrintRecipientDoc() {// 2024/03/01修改，按客戶需求調整為word
	$('#state').val('printRecipientList');
	var target = 'PRE3005_'+randomUUID().replace(/\-/g,"");
	window.open("",target);
	form1.target = target;
	form1.submit();
	form1.target = '';
}

$(document).ready(function() {
	if($('#prefixNo').val() != ""){
		$('#prePrefixNo').removeAttr("disabled");
		//不開放選，因為也沒存這欄位
		//$("input[name='getKind']").each(function(){$(this).removeAttr("disabled");});
		$('#processType').removeAttr("disabled");
		$('#refundNo').removeAttr("disabled");
		if ($('#noPayMark').val().startsWith("已免繳一次")) {
			alert("本案為免繳規費預查案件, 僅可撤件不需退費");
			$('#processType').val("02");
			$('#processType').attr("disabled", "disabled");
		}
		document.getElementsByName('processType')[0].value = $('#processType').val();
	}
	
	$('#doQueryOne').click(function(){
		$('#state').val("queryOne");
	});
	$('#doUpdate').click(function(){
		if (confirm("確定要存檔嗎?")) {
			if (form1.processType.value == "01" && form1.prePrefixNo.value == ""){
				alert("檢還時請輸入本案預查編號");
			}
			else {
				$('#state').val("doWithdraw");
				form1.submit();
			} // else
		} // if
	});
	$('#doPrintPdf').click(function(){
		if(doSomeCheck()){
			$('#state').val("preview");
			var target = 'PRE3005_'+randomUUID().replace(/\-/g,"");
			window.open("",target);
			form1.target = target;
			form1.submit();
			form1.target = '';
		}
	});
	$('#doExit').click(function(){
		window.close();
	});
	if ($("#approveResult").text() != "核准保留 " ) {
		$("#xCompanyName").html("");
	}
	
});

</script>
</head>
<body topmargin="0" onLoad="showErrorMsg('<%=obj.getPopErrorMsg()%>')">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE3005'/>
</c:import>

<div id="queryContainer" style="top:200px;width:600px;height:200px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:left;">
			<input class="toolbar_default" type="button" name="querySubmit" value="執行查詢" onclick="doPrintRecipientDoc()" >
			<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="queryHidden('queryContainer')">
		</td>
	</tr>
    <tr> 
    	<td class="td_form">退費日期：</td>
        <td class="td_form_white"> 
           <%=View.getPopCalendar("field_Q", "q_returnDateStart", obj.getQ_returnDateStart())%>至 <%=View.getPopCalendar("field_Q","q_returnDateEnd",obj.getQ_returnDateEnd()) %>
        </td>
    </tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
	<table width="100%">
		<tr>
			<td style="text-align:left" width="80%">
				<input class="toolbar_default" type="submit" id="doQueryOne" name="doQueryOne" value="查　詢" onclick="">&nbsp;
				<input class="toolbar_default" type="button" id="doUpdate" name="doUpdate" value="存　檔" onclick="">&nbsp;
				<input class="toolbar_default" type="button" id="doPrintPdf" name="doPrintPdf" value="列印退還書">&nbsp;
				<input class="toolbar_default" type="button" id="doPopQuery" name="doPopQuery" value="撤件/撤回退費案件查詢" onClick="query();">
				<input class="toolbar_default" type="button" id="printRecipientList" name="printRecipientList" value="列印受款人清單" onclick="queryShow('queryContainer');">
			</td>
			<td style="text-align:right" colspan="1" width="20%">
				<!-- <input class="toolbar_default" type="button" id="doExit" name="doExit" value="離　開">  -->
			</td>
		</tr>
	</table>
</td></tr>

<!-- Query Area  -->

<tr><td class="bg" >
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" style="width:100px">預查編號：</td>
			<td class="td_form_white" style="width:250px"> 
				<input class="field_Q" type="text" name="q_prefixNo" size="10" maxlength="9" value="<%=obj.getQ_prefixNo()%>">
				<input type="hidden" id="noPayMark" name="noPayMark" value="<%=obj.getNoPayMark()%>">
				<br/><font color="black">(請輸入前案預查編號)</font>
			</td>
			<td class="td_form" style="width:90px">作業型別：</td>
        	<td class="td_form_white" style="width:100px"> 
        		<input type="hidden" name="processType">
          		<select id="processType" name="processType" class="field" disabled="disabled" onchange="changeProcessType()">
          		    <option value="02">撤件</option>
          		    <option value="01">檢還</option>
            		<option value="03">撤回退費</option>
            		<option value="04">退費</option>
          		</select>
        	</td>
        	<td class="td_form" style="width:100px">退費公文號：</td>
			<td class="td_form_white" > 
				<font color="black">
				<input class="field" type="text" id="refundNo" name=refundNo size="20" maxlength="15" value="<%=obj.getRefundNo()%>" disabled="disabled">
				</font>
        	</td>
		</tr>			
	</table>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- Form area -->
<tr><td>
	<table cellpadding=0 cedlsapcing=0>
      <tr>
        <td nowrap id="t1" CLASS="tab_border1">追溯前後案</td>
      </tr>
    </table> 
	<table class="table_form" width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td class="td_form" style="width:100px">預查編號：</td>
			<td class="td_form_white" style="width:250px"> 
          		<input class="field" type="text" id="prePrefixNo" name="prePrefixNo" size="10" maxlength="9" value="<%=obj.getPrePrefixNo()%>" disabled="disabled">
				<br/><font color="black">(請輸入後案預查編號，撤件可不填)</font>
        	</td>
        	<td class="td_form" style="width:140px">檢還本案預查編號：</td>
        	<td class="td_form_white">
        		<font color="black">
          		<input class="field_RO" type="text" id="prefixNo" name="prefixNo" size="10" maxlength="10" value="<%=obj.getPrefixNo()%>">
          		</font>
        	</td>
		</tr>
	</table>
</td></tr>

<tr><td>
	<table cellpadding=0 cedlsapcing=0>
      <tr>
        <td nowrap id="t1" CLASS="tab_border1">申請人基本資料</td>
      </tr>
    </table> 
	<table class="table_form" width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td class="td_form" style="width:100px">姓名：</td>
			<td class="td_form_white" style="width:250px"> 
				<font color="black"><%=obj.getApplyName()%></font>
				<input type="hidden" id="applyName" name="applyName" value="<%=obj.getApplyName()%>">
        	</td>
        	<td class="td_form" style="width:140px">身份ID ：</td>
        	<td class="td_form_white"> 
        		<font color="black"><%=obj.getApplyId()%></font>
        		<input type="hidden" id="applyId" name="applyId" value="<%=obj.getApplyId()%>">
        	</td>
		</tr>
		<tr>
			<td class="td_form">電話：</td>
			<td class="td_form_white"> 
				<font color="black"><%=obj.getTel()%></font>
          		<input type="hidden" id="tel" name="tel" value="<%=obj.getTel()%>">
        	</td>
        	<td class="td_form">統一編號 ：</td>
        	<td class="td_form_white"> 
        		<font color="black"><%=obj.getBanNo()%></font>
          		<input type="hidden" id="banNo" name="banNo" value="<%=obj.getBanNo()%>">
        	</td>
		</tr>
		<tr>
			<td class="td_form">地址：</td>
			<td class="td_form_white" >
				<font color="black"> <%=obj.getAddr()%></font>
          		<input type="hidden" id="addr" name="addr" value="<%=obj.getAddr()%>">
        	</td>
        	<td class="td_form">收件人姓名：</td>
			<td class="td_form_white" >
          		<input type="text" class="cmex" id="contactName" name="contactName" maxlength="25" value="<%=obj.getContactName()==null?"":obj.getContactName()%>" disabled>
        	</td>
		</tr>
		<tr>
			<td class="td_form">預查名稱：</td>
			<td class="td_form_white"> 
				<font color="black"><%=obj.getCompanyName()%></font>
          		<input type="hidden" id="companyName" name="companyName" value="<%=obj.getCompanyName()%>">
        	</td>
        	<td class="td_form">收件人地址：</td>
			<td class="td_form_white"> 
          		<input type="text" id="contactAddr" name="contactAddr" maxlength="60" value="<%=obj.getContactAddr()==null?"":obj.getContactAddr()%>" disabled>
        	</td>
		</tr>
	</table>
</td></tr>

<tr><td>
	<table cellpadding=0 cedlsapcing=0>
      <tr>
        <td nowrap id="t1" CLASS="tab_border1">申請案資料</td>
      </tr>
    </table> 
	<table class="table_form" width="100%" cellpadding="0" cellspacing="0">
		<tr>
			<td class="td_form" style="width:100px">領件方式：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input type="radio" name="getKind" value="3" class="field" <%="3".equals( obj.getGetKind() )?"checked":""%>>線上列印  <!--2024/03/17 新增線上列印 -->
			    <input type="radio" name="getKind" value="1" class="field" <%="1".equals( obj.getGetKind() )?"checked":""%>>自取
			    <input type="radio" name="getKind" value="2" class="field" <%="2".equals( obj.getGetKind() )?"checked":""%>>郵寄
			    </font>
        	</td>
        	
		</tr>
		<tr>
			<td class="td_form" >收件日期：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black"><%=obj.getReceiveDate()%>&nbsp;&nbsp;<%=obj.getReceiveTime()%></font>
        	</td>
        	
		</tr>
		<tr>
			<td class="td_form" >核覆日期：</td>
			<td class="td_form_white" colspan="3">
				<font color="black"><%=obj.getApproveDate()%>&nbsp;&nbsp;<%=obj.getApproveTime()%></font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >保留期限：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black"> <%=obj.getReserveDate()%></font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >取件日期：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black"><%=obj.getGetDate()%>&nbsp;&nbsp;<%=obj.getGetTime()%></font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >核覆結果：</td>
			<td class="td_form_white" id="approveResult" style="width:250px"> 
				<font color="black"><%=obj.getApproveResult()%></font>
        	</td>
        	<td class="td_form" style="width:140px">案件狀態：</td>
			<td class="td_form_white" > 
				<font color="black"><%=obj.getPefixStatus()%></font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >收據編號：</td>
			<td class="td_form_white" style="width:250px">
				<font color="black"> <%=obj.getReceiptNo()%></font>
        	</td>
        	<td class="td_form" style="width:140px">出納科收據狀態：</td>
			<td class="td_form_white" > 
				<font color="black"><%=obj.getReceiptState()%></font>
        	</td>
		</tr>
	</table>
</td></tr>

<!-- hidden area -->
<tr><td>
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="accountDate" name="accountDate" value="<%=obj.getAccountDate()%>">
	<input type="hidden" id="returnDate" name="returnDate" value="<%=obj.getReturnDate()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
</td></tr>

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>