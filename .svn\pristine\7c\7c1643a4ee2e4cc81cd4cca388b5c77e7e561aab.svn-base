package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;


/**
 * 免繳註記
 *
 */
public interface NoPayMarkService {

	/**
	 * 查詢免繳註記
	 * @param banNo
	 * @return
	 */
	public GeneralityBusitem getByBanNo(String banNo);
	
	
	/**
	 * 檢查免繳資格
	 * @param prefixNo
	 * @return
	 */
	public String checkNoPayMark(String prefixNo, boolean canMod);
	
	/**
	 * 查詢免繳註記
	 * @param banNo
	 * @return
	 */
	public String getNoPayMark(String banNo);
	/**
	 * 查詢免繳註記(pre3005)
	 * @param banNo
	 * @return
	 */
	public String getNoPayMark4Pre3005(String banNo, String prefixNo, String telixNo);
	/**
	 * 修改免繳註記為「尚未使用」
	 * @param banNo
	 */
	public void updateNotUsed(String banNo);
	
	/**
	 * 修改免繳註記為「無可免繳註記」
	 * @param banNo
	 */
	public void updateNoMark(String banNo);

	/**
	 * 修改免繳註記為「已免繳一次，預查編號：prefixNo」
	 * @param banNo
	 * @param prefixNo
	 */
	public void updatePrefixNo(String banNo, String prefixNo);

	/**
	 * 修改免繳註記為「已免繳一次，電子流水號：telixNo」
	 * @param banNo
	 * @param prefixNo
	 */
	public void updateTelixNo(String banNo, String prefixNo);

	/**
	 * 修改免繳註記為「免繳資格已被手動註銷」
	 * @param banNo
	 * @param prefixNo
	 */
	public void updateCancel(String banNo);

	void insertNoPayMark(Cedb1000 cedb1000);

}