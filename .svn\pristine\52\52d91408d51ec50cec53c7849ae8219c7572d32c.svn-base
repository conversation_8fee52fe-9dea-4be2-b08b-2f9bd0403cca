package com.kangdainfo.tcfi.lucene.util;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;

public abstract class ChineseConverter {

	private static Map<String, List<String>> VADict = new HashMap<String, List<String>>();
	private static Map<String, List<String>> STDict = new HashMap<String, List<String>>();
	private static Map<String, List<String>> TSDict = new HashMap<String, List<String>>();

	/**
	 * 反正規化
	 * @param queryString
	 * @return
	 */
	public static String[] denormalization(String queryString) {
		Set<String> results = new TreeSet<String>();
		if (null==queryString) queryString = "";
		queryString = queryString.replaceAll("\\*", "");
		queryString = queryString.replaceAll(" ", "");
		//檢查公司名稱
		queryString = getCheckOrgType(queryString);
		//逐字去轉換
		char[] cs = queryString.trim().toCharArray();
		if(cs != null && cs.length > 0){
			List<Cedbc058> objs = null;
			String[] tempResults;
			for(char c: cs){
				objs = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058ObjsByBaseName(String.valueOf(c));
				if(null!=objs && !objs.isEmpty()) {
					if(results.isEmpty()) {
						for(Cedbc058 obj : objs) {
							results.add(obj.getSameName1());
							results.add(obj.getSameName());
						}
					} else {
						tempResults = results.toArray(new String[results.size()]);
						results = new TreeSet<String>();
						for(String tempResult : tempResults) {
							for(Cedbc058 obj : objs) {
								results.add(tempResult + obj.getSameName1());
								results.add(tempResult + obj.getSameName());
							}
						}
					}
				} else {
					if(results.isEmpty()) {
						results.add(String.valueOf(c));
					} else {
						tempResults = results.toArray(new String[results.size()]);
						results = new TreeSet<String>();
						for(String tempResult : tempResults) {
							results.add(tempResult + String.valueOf(c));
						}
					}
				}
			}
		}
		return results.toArray(new String[results.size()]);
	}

	/**
	 * 將傳入的字串轉換成同音同義字集
	 * 此方法至少會回傳一組
	 * @param string
	 * @return
	 */
	public static Set<String> getAllVariants(String string) {
		return prepareKeywords(string.toCharArray(), VADict);
	}

	/**
	 * 正規化(同音同義轉換
	 * 1.輸入"糠"xxx
	     Select Base_Name, CEDBC058_ID from CEDBC058 where Can_Used ='Y' and SAME_NAME_1 = '糠'and rownum = 1 
		 1.1 沒找到就不用替換此字
         1.2 有找到 '康'
         1.2.1 update CEDBC058 set Be_Used = 'Y' where ID = ?
         1.2.2 替換查詢出來的 COMPANY_NAME = '康xxx' 寫入 COMPANY_NAME_BAS 或 special_name = '康xxx' 寫入 special_name_BAS
	 * @param word
	 * @return
	 * @throws Exception 
	 * @throws SQLException      (A:a and B:b and C:c) or () or () 
	 */
	public static String normalization(String word) {
		return normalization(word,true);
	}
	public static String normalization(String word, boolean checkOrgType) {
		//檢查公司名稱
		if( checkOrgType ) word = getCheckOrgType(word);
		//逐字去轉換
		char[] chars = word.trim().toCharArray();
		StringBuffer newWord = new StringBuffer("");
		try{	
			if(chars != null && chars.length > 0){
				List<Cedbc058> objs = null;
				for(char c: chars){
					word = String.valueOf(c);
					objs = ServiceGetter.getInstance().getCedbc058CodeLoader().getCedbc058ObjsByBaseName(word);
					if(null!=objs && !objs.isEmpty()){
						String baseName = "";
						//更新註記為已使用
						for(Cedbc058 obj : objs) {
							//有找到baseName, update Be_Used = 'Y', status = '9'
							if(!"Y".equals(Common.get(obj.getBeUsed()))){
								ServiceGetter.getInstance().getPre8010Service().updateCedbc058BeUsed(obj.getId());
								obj.setBeUsed("Y");
								obj.setStatus("9");
							}
							
							if( !"".equals(Common.get(obj.getBaseName())) ) {
								baseName = obj.getBaseName();
							}
						}
						newWord.append(baseName);
					}else{
						newWord.append(word);
					}
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		return newWord.toString();
	}
	
	/**
	 * 檢核公司名稱，長度大於4個字，又有公司型態才排除
	 * 股份有限公司, 有限公司, 無限公司, 兩合公司
	 * @param word
	 * @return
	 */
	public static String getCheckOrgType(String word) {
		String[] companyType = {"股份有限公司", "有限公司", "無限公司", "兩合公司", "有限合夥"};
		word = word.trim();
		if(!"".equals(word) && word.length() > 4){
			for(String s: companyType){
				if(word.endsWith(s))
					word = word.replace(s, "");
			}
		}
		return word;
	}

	/**
	 * 將傳入的字串藉由字典轉換成所有的可能字集
	 * @param keywordChars 欲轉換字串
	 * @param dictionary 資料字典(異體字典,簡體字典,繁體字典)
	 * @return List<String> 所有可能字集
	 * Ex.異體字轉換 臺證 -> [臺證, 臺証, 台證, 台証]
	 */
	private static Set<String> prepareKeywords(char[] keywordChars, Map<String, List<String>> dictionary) {

		final int keywordLength = keywordChars.length;
		Set<String> keywords = new HashSet<String>();

		for (int i = 0; i < keywordLength; i++) {

			List<String> converts = dictionary.get(keywordChars[i] + "");

			if (converts == null || converts.size() == 1) {
				continue;
			}

			if (keywords.isEmpty()) {
				char[] copy = new char[keywordLength];
				System.arraycopy(keywordChars, 0, copy, 0, keywordLength);

				for (String similar : converts) {
					copy[i] = similar.charAt(0); // 代換成相似字
					keywords.add(String.valueOf(copy));
				}
			} else {
				List<String> tempKeywords = new ArrayList<String>();
				for (String keyword : keywords) { // 依序取得keyword後將該字元代換
					char[] copy = new char[keywordLength];
					System.arraycopy(keyword.toCharArray(), 0, copy, 0, keywordLength);
					for (String similar : converts) {
						copy[i] = similar.charAt(0); // 代換成相似字
						tempKeywords.add(String.valueOf(copy));
					}
				}
				keywords.clear();
				keywords.addAll(tempKeywords);
			}
		}

		// System.out.println(keywords);

		if (keywords.isEmpty()) { // 如果還是空的就把自己放進去
			keywords.add(String.valueOf(keywordChars));
		}
		return keywords;
	}

	public static String coverSC2TC(String tsString) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < tsString.length(); i++) {
			String currentChar = tsString.charAt(i) + "";
			List<String> converts = TSDict.get(currentChar);

			if (converts != null) {
				if (converts.size() == 1) {
					sb.append(converts.get(0));
					continue;
				} else {
					for (String conver : converts) {
						if (currentChar.equals(conver)) {
							sb.append(conver);
							continue;
						}
					}
				}
			} else {
				sb.append(currentChar);
			}
		}
		return sb.toString();
	}

	public static String coverTC2SC(String scString) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < scString.length(); i++) {
			String currentChar = scString.charAt(i) + "";
			List<String> converts = STDict.get(currentChar);

			if (converts != null) {
				if (converts.size() == 1) {
					sb.append(converts.get(0));
					continue;
				} else {
					for (String conver : converts) {
						if (currentChar.equals(conver)) {
							sb.append(conver);
							continue;
						}
					}
				}
			} else {
				sb.append(currentChar);
			}
		}
		return sb.toString();
	}

}