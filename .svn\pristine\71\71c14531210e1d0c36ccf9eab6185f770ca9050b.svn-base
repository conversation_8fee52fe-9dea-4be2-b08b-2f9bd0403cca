package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;

public class PrefixReceiptNoDAO extends BaseDaoJdbc  implements RowMapper<PrefixReceiptNo> {
	
	private static final String SQL_SELECT_ALL = 
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE 1=1";
	public List<PrefixReceiptNo> selectAll() {
		SQLJob sqljob = new SQLJob(SQL_SELECT_ALL);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), this);
    	return list;
	}
	
	private static final String SQL_SELECT_BY_PREFIX_NO = 
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE PREFIX_NO = ? ";
	public PrefixReceiptNo selectByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_PREFIX_NO);
    	sqljob.addParameter(prefixNo);
    	sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list.isEmpty()? null:list.get(0);
	}
	
	private static final String SQL_SELECT_BY_TELIX_NO = 
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE TELIX_NO = ? ";
	public PrefixReceiptNo selectByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_TELIX_NO);
    	sqljob.addParameter(telixNo);
    	sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list.isEmpty()? null:list.get(0);
	}
	
	private static final String SQL_SELECT_BY_RECEIPT_NO = 
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE RECEIPT_NO = ? ";
	public PrefixReceiptNo selectByReceiptNo(String receiptNo) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_RECEIPT_NO);
    	sqljob.addParameter(receiptNo);
    	sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list.isEmpty()? null:list.get(0);
	}
	
	private static final String SQL_SELECT_BY_RETURN_DATE =
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE RETURN_DATE BETWEEN to_date(?,'YYYYMMDD') AND to_date(?,'YYYYMMDD')";
	public List<PrefixReceiptNo> selectByReturnDate(String returnDateStart, String returnDateEnd) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_RETURN_DATE);
		sqljob.addParameter(returnDateStart);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(returnDateEnd);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list;
	}
	
	private static final String SQL_SELECT_BY_RETURN_DATE_PDF =
			"select prefix_no, telix_no, nvl(apply_law_name, RECIPIENT_NAME) as RECIPIENT_NAME, RECIPIENT_ADDR "
			+ ", RECEIPT_NO, RECEIPT_TYPE, PAY_DATE, PAY_TYPE, CH_NO, AMOUNT, REFUND, PAY_NAME "
			+ ", RETURN_NO, RETURN_DATE, RETURN_USER, RETURN_TYPE "
			+ " from ( "
			+ " SELECT "
			+ " PREFIX_NO, TELIX_NO, RECEIPT_NO, RECEIPT_TYPE, PAY_DATE, PAY_TYPE, CH_NO, AMOUNT, REFUND, PAY_NAME "
			+ " , (case when (select apply_kind from cedb1000 m where m.prefix_no = a.prefix_no ) = '1' "
			+ " then (SELECT apply_name FROM CEDB1000 M WHERE M.PREFIX_NO = A.PREFIX_NO) "
			+ " else (SELECT old_company_name FROM CEDB1000 M WHERE M.PREFIX_NO = A.PREFIX_NO) end ) AS RECIPIENT_NAME "
			+ " , (select apply_law_name from cedb1022 q where a.prefix_no = q.prefix_no) as apply_law_name "
			+ " , (SELECT APPLY_ADDR FROM CEDB1000 M WHERE M.PREFIX_NO = A.PREFIX_NO) AS RECIPIENT_ADDR "
			+ " , RETURN_NO, RETURN_DATE, RETURN_USER, RETURN_TYPE "
			+ " FROM " 
			+ " PREFIX_RECEIPT_NO A "  
			+ " WHERE " 
			+ " RETURN_DATE BETWEEN to_date(?,'YYYYMMDD') AND to_date(?,'YYYYMMDD') + 1 "// 2024/02/05 修正時間條件
			+ " AND RETURN_DATE < to_date(?,'YYYYMMDD') + 1 order by return_no " // 2024/02/05 修正時間條件
			+ " ) ";
	public List<PrefixReceiptNo> selectByReturnDatePDF(String returnDateStart, String returnDateEnd) {
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_RETURN_DATE_PDF);
		sqljob.addParameter(returnDateStart);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(returnDateEnd);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(returnDateEnd);// 2024/02/05 新增時間條件
		sqljob.addSqltypes(java.sql.Types.VARCHAR);// 2024/02/05 新增時間條件
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list;
	}
	
	/**
	 * sql 根據付款時間區間尋找收據資料
	 */
	private static final String SQL_SELECT_BY_PAYDATE_INTERVAL = 
			"SELECT * FROM PREFIX_RECEIPT_NO " 
			+ " WHERE CAST( PAY_DATE AS NUMBER) BETWEEN ? AND ?";// 2024/03/27 新增SQL
	
	/**
	 * 根據付款時間區間尋找收據資料
	 * @param payDateStart
	 * @param payDateEnd
	 * @return List<PrefixReceiptNo>
	 */
	public List<PrefixReceiptNo> selectByPayDateInterval(String payDateStart, String payDateEnd) {// 2024/03/27 新增方法
		SQLJob sqljob = new SQLJob(SQL_SELECT_BY_PAYDATE_INTERVAL);
		sqljob.addParameter(payDateStart);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		sqljob.addParameter(payDateEnd);
		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
    	return list;
	}
	
	/**
	 * sql 根據付款方式跟預查編號尋找收據資料
	 */
	private static final String SQL_SELECT_BY_PAYTYPE_AND_PREFIXNOS = 
		    "SELECT * FROM PREFIX_RECEIPT_NO WHERE PAY_TYPE = ? AND PREFIX_NO IN ";//2024/04/02 新增

	/**
	 * 根據付款方式跟預查編號尋找收據資料
	 * @param payType
	 * @param prefixNos
	 * @return List<PrefixReceiptNo>
	 */
	public List<PrefixReceiptNo> selectByPayTypeAndPrefixNos(String payType, List<String> prefixNos) {//2024/04/02 新增    
		StringBuilder inClausePlaceholders = new StringBuilder("(");
		List<List<String>> prefixNosLists = partitionList(prefixNos, 1000);
		
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		inClausePlaceholders.append("?");
		        if (j < prefixNosLists.get(i).size() - 1) {
		            inClausePlaceholders.append(", ");
		        }
	    	}
	    	
	    	if (i == 0) {
	    		inClausePlaceholders.append(") or (PAY_TYPE = ? AND PREFIX_NO IN ( ");
	    	}else {
	    		inClausePlaceholders.append(")) or (PAY_TYPE = ? AND PREFIX_NO IN ( ");
	    	}
	    }
	    inClausePlaceholders.delete(inClausePlaceholders.length() - 37, inClausePlaceholders.length());
	    
	    String sql = SQL_SELECT_BY_PAYTYPE_AND_PREFIXNOS + inClausePlaceholders;
	    
	    SQLJob sqljob = new SQLJob(sql);
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	sqljob.addParameter(payType);
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		sqljob.addParameter(prefixNosLists.get(i).get(j));
	    	}
	    }
	    
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	sqljob.addSqltypes(java.sql.Types.CHAR);
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		sqljob.addSqltypes(java.sql.Types.NVARCHAR);
	    	}
	    }
	    
	    List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	    return list;
	}
	
	/**
	 * sql 根據預查編號尋找收據資料
	 */
	private static final String SQL_SELECT_BY_PREFIXNOS = 
			"SELECT * FROM PREFIX_RECEIPT_NO WHERE PREFIX_NO IN ";//2024/04/02 新增
	
	/**
	 * 根據預查編號尋找收據資料
	 * @param prefixNos
	 * @return List<PrefixReceiptNo>
	 */
	public List<PrefixReceiptNo> selectByPrefixNos(List<String> prefixNos) {//2024/04/02 新增
		StringBuilder inClausePlaceholders = new StringBuilder("(");
		List<List<String>> prefixNosLists = partitionList(prefixNos, 1000);
		
	    for (int i = 0; i < prefixNosLists.size(); i++) {
	    	for ( int j = 0; j < prefixNosLists.get(i).size(); j++) {
	    		inClausePlaceholders.append("?");
		        if (j < prefixNosLists.get(i).size() - 1) {
		            inClausePlaceholders.append(", ");
		        }
	    	}
	    	inClausePlaceholders.append(") or PREFIX_NO IN ( ");
	    }
	    inClausePlaceholders.delete(inClausePlaceholders.length() - 19, inClausePlaceholders.length());
	    
		String sql = SQL_SELECT_BY_PREFIXNOS + inClausePlaceholders;
	    
	    SQLJob sqljob = new SQLJob(sql);
	    for (String prefix : prefixNos) {
	    	sqljob.addParameter(prefix);
	    } 
	    
	    for (int i = 0; i < prefixNos.size(); i++) {
	    	sqljob.addSqltypes(java.sql.Types.NVARCHAR);
	    }
	    
	    List<PrefixReceiptNo> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	    return list;
	}
	
	private static final String SQL_INSERT = 
			"INSERT INTO PREFIX_RECEIPT_NO (PREFIX_NO, TELIX_NO, RECEIPT_NO, RECEIPT_TYPE, PAY_TYPE, PAY_DATE"
			+ ",CH_NO ,AMOUNT, REFUND, PAY_NAME, recipient_name, recipient_addr, return_no, return_date, return_user" 
			+ ", return_type ) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	public int insert(PrefixReceiptNo vo) {
		try {
			SQLJob sqljob = new SQLJob(SQL_INSERT);
			sqljob.addParameter(vo.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getTelixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReceiptNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReceiptType());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getPayType());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getPayDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getChNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getAmount());
			sqljob.addSqltypes(java.sql.Types.INTEGER);
			sqljob.addParameter(vo.getRefund());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getPayName());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getRecipientName());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getRecipientAddr());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReturnNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReturnDate());
			sqljob.addSqltypes(java.sql.Types.DATE);
			sqljob.addParameter(vo.getReturnUser());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(vo.getReturnType());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		} catch (Exception e) {
			e.printStackTrace();
			return 0;
		}
	}
	
	private static final String SQL_UPDATE_BY_PREFIX_NO =
			" UPDATE PREFIX_RECEIPT_NO SET TELIX_NO=?, RECEIPT_NO=?, RECEIPT_TYPE=?, PAY_TYPE=?, PAY_DATE=?, CH_NO=?, AMOUNT=?, REFUND=?, PAY_NAME=?"
			+ " , recipient_name=?, recipient_addr = ?, return_no = ?, return_date = ? , return_user = ? , return_type = ? WHERE PREFIX_NO = ?";
	public void updateByPrefixNo(PrefixReceiptNo vo) {
		SQLJob sqljob = new SQLJob(SQL_UPDATE_BY_PREFIX_NO);
		sqljob.addParameter(vo.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getChNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getAmount());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRefund());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnDate());
		sqljob.addSqltypes(java.sql.Types.DATE);
		sqljob.addParameter(vo.getReturnUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	private static final String SQL_UPDATE_BY_TELIX_NO =
			" UPDATE PREFIX_RECEIPT_NO SET PREFIX_NO=?, RECEIPT_NO=?, RECEIPT_TYPE=?, PAY_TYPE=?, PAY_DATE=?, CH_NO=?, AMOUNT=?, REFUND=?, PAY_NAME=?"
			+ " , recipient_name=?, recipient_addr = ?, return_no = ?, return_date = ?, return_user = ?, return_type = ? WHERE TELIX_NO = ?";
	public void updateByTelixNo(PrefixReceiptNo vo) {
		SQLJob sqljob = new SQLJob(SQL_UPDATE_BY_TELIX_NO);
		sqljob.addParameter(vo.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getChNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getAmount());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRefund());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnDate());
		sqljob.addSqltypes(java.sql.Types.DATE);
		sqljob.addParameter(vo.getReturnUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	private static final String SQL_UPDATE_BY_RECEIPT_NO =
			" UPDATE PREFIX_RECEIPT_NO SET PREFIX_NO=?, TELIX_NO=?, RECEIPT_TYPE=?, PAY_TYPE=?, PAY_DATE=?, CH_NO=?, AMOUNT=?, REFUND=?, PAY_NAME=?"
			+ " , recipient_name=?, recipient_addr = ?, return_no = ?, return_date = ?, return_user = ? , return_type = ? WHERE RECEIPT_NO = ?";
	public void updateByReceiptNo(PrefixReceiptNo vo) {
		SQLJob sqljob = new SQLJob(SQL_UPDATE_BY_RECEIPT_NO);
		sqljob.addParameter(vo.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getChNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getAmount());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRefund());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getPayName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getRecipientAddr());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnDate());
		sqljob.addSqltypes(java.sql.Types.DATE);
		sqljob.addParameter(vo.getReturnUser());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReturnType());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(vo.getReceiptNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	private static final String SQL_DELETE = 
			"DELETE FROM PREFIX_RECEIPT_NO WHERE PREFIX_NO = ?";
	public void delete(String receiptType) {
		SQLJob sqljob = new SQLJob(SQL_DELETE);
		sqljob.addParameter(receiptType);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	@Override
	public PrefixReceiptNo mapRow(ResultSet rs, int idx) throws SQLException {
		PrefixReceiptNo obj = null;
		if (null != rs) {
			obj = new PrefixReceiptNo();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setReceiptNo(rs.getString("RECEIPT_NO"));
			obj.setReceiptType(rs.getString("RECEIPT_TYPE"));
			obj.setPayType(rs.getString("PAY_TYPE"));
			obj.setChNo(rs.getString("CH_NO"));
			obj.setAmount(rs.getInt("AMOUNT"));
			obj.setRefund(rs.getString("REFUND"));
			obj.setPayDate(rs.getString("PAY_DATE"));
			obj.setPayName(rs.getString("PAY_NAME"));
			obj.setRecipientName(rs.getString("RECIPIENT_NAME"));
			obj.setRecipientAddr(rs.getString("RECIPIENT_ADDR"));
			obj.setReturnNo(rs.getString("RETURN_NO"));
			obj.setReturnDate(rs.getDate("RETURN_DATE"));
			obj.setReturnUser(rs.getString("RETURN_USER"));
			obj.setReturnType(rs.getString("RETURN_TYPE"));
		}
		return obj;
	}
	
	/**
	 * 2024/04/09 新增，針對oracle 使用in時不能超過1000個使用
	 * @param list
	 * @param partitionSize
	 * @return List<List<T>>
	 */
	public static <T> List<List<T>> partitionList(List<T> list, int partitionSize) {
	    List<List<T>> partitions = new ArrayList<>();
	    for (int i = 0; i < list.size(); i += partitionSize) {
	        partitions.add(list.subList(i, Math.min(i + partitionSize, list.size())));
	    }
	    return partitions;
	}
}
