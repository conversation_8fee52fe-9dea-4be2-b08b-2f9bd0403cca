package com.kangdainfo.tcfi.lucene.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import org.apache.log4j.Logger;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.DirectoryReader;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.queryparser.classic.MultiFieldQueryParser;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.BooleanClause;
import org.apache.lucene.search.BooleanQuery;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.ScoreDoc;
import org.apache.lucene.search.Sort;
import org.apache.lucene.search.SortField;
import org.apache.lucene.search.TopDocs;
import org.apache.lucene.store.FSDirectory;
import org.owasp.esapi.ESAPI;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.lucene.bo.Hit;
import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.lucene.dao.IndexDataDao;
import com.kangdainfo.tcfi.lucene.service.IndexSearchService;
import com.kangdainfo.tcfi.lucene.util.ChineseConverter;
import com.kangdainfo.tcfi.lucene.util.LuceneManager;
import com.kangdainfo.tcfi.model.eicm.bo.SearchLog;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.SearchLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.lang.CommonStringUtils;

public class IndexSearchServiceImpl implements IndexSearchService {
	private Logger logger = Logger.getLogger(this.getClass());

	//全文檢索欄位(公司名稱_基本)
	private static final String DEFAULT_FULLTXT_SEARCH_FIELD = "COMPANY_NAME_BAS";
	//輔助查詢檢索欄位(公司名稱, 特許名稱)
	private static final String DEFAULT_HELP_SEARCH_FIELD = "COMPANY_NAME_BASH,SPECIAL_NAME_BASH";	
	//重建索引欄位(公司名稱_基本, 特許名稱_基本)
	private static final String DEFAULT_QUERYALL_SEARCH_FIELD = "COMPANY_NAME_BAS,SPECIAL_NAME_BAS";
	/** 最大筆數 */
	private final int MAX_RESULTS = *********;
	/** 檢索類別 - (1)輔助查詢 */
	private final String SEARCH_TYPE_HELP = "1";
	/** 檢索類別 - (2)全文檢索 */
	private final String SEARCH_TYPE_FULL = "2";

	/**
	 * 預設排序
	 * (1)收文: [預查編號] 大 > 小
	 * (2)預查: [預查編號] 大 > 小
	 * (3)公司: [設立日期] 大 > 小
	 */
	private static final Sort DEFAULT_SORT = new Sort(new SortField[]{
			new SortField("SORTBY", SortField.Type.STRING)
			,new SortField("PREFIX_NO_SORT", SortField.Type.STRING, true)
			,new SortField("SETUP_DATE", SortField.Type.STRING, true)
			,new SortField("BAN_NO", SortField.Type.STRING)
			});

	public SearchResult searchCount(String searchType, String keyword) throws Exception {
		//查詢開始
		long startTime = System.currentTimeMillis();
		//過濾
		String decodedKeyword = ESAPI.encoder().decodeForHTML(keyword);
		//正規化
		String normalizeKeyword = ChineseConverter.normalization(decodedKeyword);
		//檢索結果
		SearchResult sr = new SearchResult();
		sr.setQueryKeyword(normalizeKeyword);
		//組合查詢語句
		BooleanQuery query = null;
		if(SEARCH_TYPE_HELP.equals(searchType)) {
			query = getHelpQuery(normalizeKeyword);

		} else if(SEARCH_TYPE_FULL.equals(searchType)) {
			query = getFullQuery(normalizeKeyword);
		}
		//查詢
		int numTotalHits = 0;
		IndexSearcher searcher = getIndexSearcher();
		if(null!=searcher) {
			TopDocs results = searcher.search(query, MAX_RESULTS);
			numTotalHits = results.totalHits;
		}
		sr.setRecordCount(numTotalHits);
		//查詢結束
 		long endTime = System.currentTimeMillis();
 		sr.setCost(endTime-startTime);
		return sr;
	}

	public SearchResult searchPage(String searchType, String keyword,
			Integer pageNum, Integer pageSize,
			String sortField, String sortReverse) throws Exception {
		//查詢開始
		long startTime = System.currentTimeMillis();
		//過濾
		String decodedKeyword = ESAPI.encoder().decodeForHTML(keyword);
		//是否檢查公司名稱
		boolean checkOrgType = true;
		//正規化
		String normalizeKeyword = ChineseConverter.normalization(decodedKeyword, checkOrgType);
		//檢索結果
		SearchResult sr = new SearchResult();
		sr.setQueryKeyword(normalizeKeyword);
		sr.setPageNum(pageNum);
		sr.setPageSize(pageSize);
		sr.setSortField(sortField);
		sr.setSortReverse(sortReverse);
		//組合查詢語句
		BooleanQuery query = null;
		if(SEARCH_TYPE_HELP.equals(searchType)) {
			query = getHelpQuery(normalizeKeyword);

		} else if(SEARCH_TYPE_FULL.equals(searchType)) {
			query = getFullQuery(normalizeKeyword);
		}
		//查詢
		sr = doPagingSearch(query, sr);
		//查詢結束
 		long endTime = System.currentTimeMillis();
 		sr.setCost(endTime-startTime);
		return sr;
	}

	public SearchResult searchAll(String searchType, String keyword,
			String sortField, String sortReverse) throws Exception {
		//是否檢查公司名稱
		boolean checkOrgType = true;
		return searchAll(searchType,keyword,sortField,sortReverse,checkOrgType);
	}

	public List<String> searchPrefixNos(String keyword) throws Exception {
		List<String> results = null;
		//過濾
		String decodedKeyword = ESAPI.encoder().decodeForHTML(keyword);
		//正規化
		String normalizeKeyword = ChineseConverter.normalization(decodedKeyword, true);
		//檢索結果
		SearchResult sr = new SearchResult();
		sr.setQueryKeyword(normalizeKeyword);
		sr.setPageNum(1);
		sr.setPageSize(*********);
		sr.setQueryAll(true);//查詢全部
		//組合查詢語句
		BooleanQuery query = getPrefixNoQuery(normalizeKeyword);
		//查詢
		sr = doPagingSearch(query, sr);
		if(null!=sr) {
			results = new ArrayList<String>();
			List<Hit> hits = sr.getHits();
			Document doc;
			String prefixNo;
			for (Hit hit : hits){
				doc = hit.getDoc();
				prefixNo = Common.get(doc.get("PREFIX_NO"));
				if(!"".equals(prefixNo)) {
					results.add( prefixNo );
				}
			}
		}
		return results;
	}

	public SearchResult searchAll(String searchType, String keyword,
			String sortField, String sortReverse, boolean checkOrgType) throws Exception {
		//查詢開始
		long startTime = System.currentTimeMillis();
		//過濾
		String decodedKeyword = ESAPI.encoder().decodeForHTML(keyword);
		//正規化
		String normalizeKeyword = ChineseConverter.normalization(decodedKeyword, checkOrgType);
		//檢索結果
		SearchResult sr = new SearchResult();
		sr.setQueryKeyword(normalizeKeyword);
		sr.setPageNum(1);
		sr.setPageSize(*********);
		sr.setQueryAll(true);//查詢全部
		sr.setSortField(sortField);
		sr.setSortReverse(sortReverse);
		//組合查詢語句
		BooleanQuery query = null;
		if(SEARCH_TYPE_HELP.equals(searchType)) {
			query = getHelpQuery(normalizeKeyword);

		} else if(SEARCH_TYPE_FULL.equals(searchType)) {
			query = getFullQuery(normalizeKeyword);
		}
		//查詢
		sr = doPagingSearch(query, sr);
		//查詢結束
 		long endTime = System.currentTimeMillis();
 		sr.setCost(endTime-startTime);
		return sr;
	}

	public SearchResult searchRebuild(String keyword) throws Exception {
		//查詢開始
		long startTime = System.currentTimeMillis();
		//過濾
		String decodedKeyword = ESAPI.encoder().decodeForHTML(keyword);
		//正規化
		//String normalizeKeyword = ChineseConverter.normalization(decodedKeyword);
		//重建時, 不用正規化
		String normalizeKeyword = decodedKeyword;
		//查詢結果
		SearchResult sr = new SearchResult();
		sr.setQueryKeyword(normalizeKeyword);
		sr.setPageNum(1);
		sr.setPageSize(*********);
		sr.setQueryAll(true);//查詢全部
		sr.setSortField("");
		sr.setSortReverse("");
		//QueryParser
		QueryParser parser = getQueryParser(DEFAULT_QUERYALL_SEARCH_FIELD);
		//組合查詢語句
		BooleanQuery query = new BooleanQuery();
		query.add(parser.parse(CommonStringUtils.append("\"",normalizeKeyword,"\"")), BooleanClause.Occur.MUST);
		if(logger.isInfoEnabled()) logger.info("Searching for: " + query.toString());
		sr = doPagingSearch(query, sr);
		//查詢結束
 		long endTime = System.currentTimeMillis();
 		sr.setCost(endTime-startTime);
		return sr;
	}

	private SearchResult doPagingSearch(BooleanQuery query, SearchResult sr) throws IOException {
		List<Hit> hitList = new LinkedList<Hit>();
		//排序
		Sort sort = getSort(sr.getSortField(), sr.getSortReverse());

		IndexSearcher searcher = getIndexSearcher();
		if(null!=searcher) {
			TopDocs results = searcher.search(query, (sr.getPageNum()+5) * sr.getPageSize(), sort);
			ScoreDoc[] hits = results.scoreDocs;

			int numTotalHits = results.totalHits;
			if(logger.isInfoEnabled()) logger.info(numTotalHits + " total matching documents");
			sr.setRecordCount(numTotalHits);
			sr.setPageCount((numTotalHits+sr.getPageSize()-1)/sr.getPageSize());
			if (numTotalHits!=0) {
				if (sr.getPageCount()<sr.getPageNum()) {
					sr.setPageNum(sr.getPageCount());
				}
				int start = (sr.getPageNum()-1)*sr.getPageSize();
				int end = Math.min(numTotalHits, sr.getPageNum()*sr.getPageSize());
				
				if (start>=end) start = end-1;
		
				if (end > hits.length) {
					hits = searcher.search(query, numTotalHits).scoreDocs;
				}
				
				end = Math.min(hits.length, start + sr.getPageSize());
				
				if(sr.isQueryAll()){
					start = 0;
					end = numTotalHits;
				}
		
				Document doc;
				Hit hit;
				String id, indexType, companyName;
				for (int i = start; i < end; i++) {
					if(logger.isDebugEnabled()) logger.debug("doc=" + hits[i].doc + " score=" + hits[i].score + "   ");
		
					doc = searcher.doc(hits[i].doc);
					hit = new Hit();
					hit.setDoc(doc);
					hit.setScoreDoc(hits[i]);
					id = doc.get("ID");
					
					if(logger.isDebugEnabled()) logger.debug(" doc = " + doc);
					if (null!=id && !"".equals(id)) {
						indexType = doc.get("INDEX_TYPE");
						companyName = doc.get("COMPANY_NAME");
						hit.setHtml(genHitHTML(sr.getQueryKeyword(), indexType, companyName));
						hitList.add(hit);
					} else {
						if(logger.isInfoEnabled()) logger.info((i + 1) + ". " + "No path for this document");
					}
				}
				
			}
		}
		sr.setHits(hitList);
		return sr;
	}

	private IndexSearcher getIndexSearcher() {
		IndexSearcher searcher = null;
		try {
			IndexReader reader = DirectoryReader.open(FSDirectory.open(new File(getRepositoryPath())));
			searcher = new IndexSearcher(reader);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return searcher;
	}

	private String getRepositoryPath() {
		SystemCode s = null;
		if(System.getProperty("os.name").toLowerCase().indexOf("win") >= 0)
			s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathWindows");
		else 
			s = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "IndexPathCentOS");
		
		String result = null;
		if(null!=s) {
			result = s.getCodeParam2();
		}
		return result;
	}

	/*
	 * 預查查詢查詢條件
	 * 1.IndexType == 2(已收文)
	 *   1-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   1-2. 審核結果為(A)審查中 (Approve_Result = 'A')(備註:錄製檢索檔時已排除)
	 *   1-3. 收文日期在一年內 Receive_Date > (SYSDATE - 1 YEAR)(備註:錄製檢索檔時已排除)
	 * 2.IndexType == 3(預查)
	 *   2-1. 公司名稱使用(LIKE 'AB%')查詢
	 *   2-2. 審核結果為(Y)核准保留 (Approve_Result = 'Y') (備註:錄製檢索檔時已排除)
	 *   2-3. 在公司系統登記且核准(03:核准設立)，就不用保留  (CMPY_STATUS != '03')
	 *   2-4. 一定要保留住 (CMPY_STATUS = '00' or CMPY_STATUS = '01') (備註:不需特別判斷)
	 *   2-5. 保留期限在15天內  RESERVE_DATE > (SYSDATE - 15 DAY)  (備註:錄製檢索檔時已排除)
	 * 3.IndexType == 4(預查否准)
	 *   3-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   3-2. 審核結果為(N)否准 (Approve_Result = 'N')(備註:錄製檢索檔時已排除)
	 *   3-3. 收文日期在一年內 Receive_Date > (SYSDATE - 1 YEAR)(備註:錄製檢索檔時已排除)
	 */
	private BooleanQuery getPrefixNoQuery(String normalizeKeyword) throws ParseException {
		QueryParser parser = getQueryParser(DEFAULT_HELP_SEARCH_FIELD);

		StringBuffer sb = new StringBuffer();
		//INDEX_TYPE:(2)已收文資料
		sb.append(" (");
		sb.append("  INDEX_TYPE:2");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append(" )");
		//INDEX_TYPE:(3)預查資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:3");
		//用LIKE AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  NOT CMPY_STATUS:03");//公司系統登記且核准不顯示
		sb.append(" )");
		//INDEX_TYPE:(4)預查否准資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:4");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append(" )");

		BooleanQuery query = new BooleanQuery();
		query.add(parser.parse(sb.toString()), BooleanClause.Occur.MUST);
		if(logger.isInfoEnabled()) logger.info("[getPrefixNoQuery][QUERY STRING:" + query.toString()+"]");
		return query;
	}

	/*
	 * 輔助查詢查詢條件
	 * 1.IndexType == 1(公司)
	 *   1-1. 公司名稱使用(LIKE 'AB%')查詢
	 *   1-2. 外國公司報備不顯示於輔助查詢  (ORGN_TYPE != '08')
	 *   1-3. 解散、廢止、撤銷、破產 廢止日期小於10年  REVOKE_APP_DATE > (SYSDATE - 10 YEAR)
	 *   1-4. 一定要保留住 (CMPY_STATUS = '00' or CMPY_STATUS = '01') (備註:不需特別判斷)
	 *   1-5. 排除清算完結 CMPY_STATUS not in ('07', '11', '12', '13', '14', '15', '16', '24', '29', '30') (備註:錄製檢索檔時已排除)
	 * 2.IndexType == 2(已收文)
	 *   2-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   2-2. 審核結果為(A)審查中 (Approve_Result = 'A')(備註:錄製檢索檔時已排除)
	 *   2-3. 收文日期在一年內 Receive_Date > (SYSDATE - 1 YEAR)(備註:錄製檢索檔時已排除)
	 * 3.IndexType == 3(預查)
	 *   3-1. 公司名稱使用(LIKE 'AB%')查詢
	 *   3-2. 審核結果為(Y)核准保留 (Approve_Result = 'Y') (備註:錄製檢索檔時已排除)
	 *   3-3. 在公司系統登記且核准(03:核准設立)，就不用保留  (CMPY_STATUS != '03')
	 *   3-4. 一定要保留住 (CMPY_STATUS = '00' or CMPY_STATUS = '01') (備註:不需特別判斷)
	 *   3-5. 保留期限在15天內  RESERVE_DATE > (SYSDATE - 15 DAY)  (備註:錄製檢索檔時已排除)
	 */
	private BooleanQuery getHelpQuery(String normalizeKeyword) throws ParseException {
		QueryParser parser = getQueryParser(DEFAULT_HELP_SEARCH_FIELD);
		//SYSDATE - 10 YEAR
		String dateBeforeTenYear = Datetime.getDateAdd("y", -10, Datetime.getYYYMMDD());
		
		StringBuffer sb = new StringBuffer();
		//INDEX_TYPE:(1)公司資料
		sb.append(" (");
		sb.append("  INDEX_TYPE:1 ");
		//用LIKE AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  AND (");
		sb.append("    (CMPY_REVOKE:n)");
		sb.append("    OR");
		sb.append("    (");
		sb.append("      CMPY_REVOKE:y");
		sb.append("      AND REVOKE_APP_DATE:[").append(dateBeforeTenYear).append(" TO 9999999]");
		sb.append("    )");
		sb.append("  )");
		sb.append("  NOT ORGN_TYPE:08 ");//外國公司報備不顯示
		sb.append(" )");
		//INDEX_TYPE:(2)已收文資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:2");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BAS:\"").append(insertSpace(normalizeKeyword)).append("\"");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append(" )");
		//INDEX_TYPE:(3)預查資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:3");
		//用LIKE AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  NOT CMPY_STATUS:03");//公司系統登記且核准不顯示
		sb.append(" )");
		//INDEX_TYPE:(5)有限合夥資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:5 ");
		//用LIKE AB%
		sb.append("  AND (");
		sb.append("    COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("    OR");
		sb.append("    SPECIAL_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  AND (");
		sb.append("    (CMPY_REVOKE:n)");
		sb.append("    OR");
		sb.append("    (");
		sb.append("      CMPY_REVOKE:y");
		sb.append("      AND REVOKE_APP_DATE:[").append(dateBeforeTenYear).append(" TO 9999999]");
		sb.append("    )");
		sb.append("  )");
		sb.append(" )");
		BooleanQuery query = new BooleanQuery();
		query.add(parser.parse(sb.toString()), BooleanClause.Occur.MUST);
		if(logger.isInfoEnabled()) logger.info("[getHelpQuery][QUERY STRING:" + query.toString()+"]");
		return query;
	}

	/*
	 * 全文檢索查詢條件
	 * 1.IndexType == 1(公司)
	 *   1-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   1-2. 解散、廢止、撤銷、破產 廢止日期小於10年  REVOKE_APP_DATE > (SYSDATE - 10 YEAR)
	 *   1-3. 一定要保留住 (CMPY_STATUS = '00' or CMPY_STATUS = '01') (備註:不需特別判斷)
	 *   1-4. 排除清算完結 CMPY_STATUS not in ('07', '11', '12', '13', '14', '15', '16', '24', '29', '30') (備註:錄製檢索檔時已排除)
	 * 2.IndexType == 2(已收文)
	 *   2-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   2-2. 審核結果為(A)審查中 (Approve_Result = 'A')(備註:錄製檢索檔時已排除)
	 *   2-3. 收文日期在一年內 Receive_Date > (SYSDATE - 1 YEAR)(備註:錄製檢索檔時已排除)
	 * 3.IndexType == 3(預查)
	 *   3-1. 公司名稱使用(LIKE '%AB%')查詢
	 *   3-2. 審核結果為(Y)核准保留 (Approve_Result = 'Y')(備註:錄製檢索檔時已排除)
	 *   3-3. 在公司系統登記且核准(03:核准設立)，就不用保留  (CMPY_STATUS != '03')
	 *   3-4. 一定要保留住 (CMPY_STATUS = '00' or CMPY_STATUS = '01') (備註:不需特別判斷)
	 *   3-5. 保留期限在15天內  RESERVE_DATE > (SYSDATE - 15 DAY)  (備註:錄製檢索檔時已排除)
	 */
	private BooleanQuery getFullQuery(String normalizeKeyword) throws ParseException {
		QueryParser parser = getQueryParser(DEFAULT_FULLTXT_SEARCH_FIELD);
		//SYSDATE - 10 YEAR
		String dateBeforeTenYear = Datetime.getDateAdd("y", -10, Datetime.getYYYMMDD());

		String fullQuery = " COMPANY_NAME_BAS:\"" + insertSpace(normalizeKeyword) +"\" ";

		StringBuffer sb = new StringBuffer();
		//INDEX_TYPE:(1)公司資料
		sb.append(" (");
		sb.append("  INDEX_TYPE:1 ");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append(fullQuery);
		sb.append("    OR COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  AND (");
		sb.append("    (CMPY_REVOKE:n)");
		sb.append("    OR");
		sb.append("    (");
		sb.append("      CMPY_REVOKE:y");
		sb.append("      AND REVOKE_APP_DATE:[").append(dateBeforeTenYear).append(" TO 9999999]");
		sb.append("    )");
		sb.append("  )");
		sb.append(" )");
		//INDEX_TYPE:(2)已收文資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:2");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append(fullQuery);
		sb.append("    OR COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append(" )");
		//INDEX_TYPE:(3)預查資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:3");

//卡近三年的資料
//		sb.append("  AND (");
//		sb.append("    PREFIX_NO_SORT:").append(Datetime.getYYYMMDD().substring(0,3)).append("*");
//		sb.append("    OR");
//		sb.append("    PREFIX_NO_SORT:").append(Datetime.getDateAdd("y", -1, Datetime.getYYYMMDD()).substring(0,3)).append("*");
//		sb.append("    OR");
//		sb.append("    PREFIX_NO_SORT:").append(Datetime.getDateAdd("y", -2, Datetime.getYYYMMDD()).substring(0,3)).append("*");
//		sb.append("  )");

		sb.append("  AND (");
		sb.append(fullQuery);
		sb.append("    OR COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  NOT CMPY_STATUS:03");//公司系統登記且核准不顯示
		sb.append(" )");
		//INDEX_TYPE:(5)有限合夥資料
		sb.append(" OR");
		sb.append(" (");
		sb.append("  INDEX_TYPE:5 ");
		//用LIKE %AB%
		sb.append("  AND (");
		sb.append(fullQuery);
		sb.append("    OR COMPANY_NAME_BASH:").append(normalizeKeyword).append("*");
		sb.append("  )");
		sb.append("  AND (");
		sb.append("    (CMPY_REVOKE:n)");
		sb.append("    OR");
		sb.append("    (");
		sb.append("      CMPY_REVOKE:y");
		sb.append("      AND REVOKE_APP_DATE:[").append(dateBeforeTenYear).append(" TO 9999999]");
		sb.append("    )");
		sb.append("  )");
		sb.append(" )");
		BooleanQuery query = new BooleanQuery();
		query.add(parser.parse(sb.toString()), BooleanClause.Occur.MUST);
		if(logger.isInfoEnabled()) logger.info("[getFullQuery][QUERY STRING:" + query.toString()+"]");
		return query;
	}

	private QueryParser getQueryParser(String queryField) {
		QueryParser parser = null;
		if(null!=queryField && !"".equals(queryField)) {
			Analyzer analyzer = getAnalyzer();
			if (queryField.indexOf(",")>0) {
				String[] fields = queryField.split(",");
				parser = new MultiFieldQueryParser(LuceneManager.LUCENE_VERSION, fields, analyzer);
			} else {
				parser = new QueryParser(LuceneManager.LUCENE_VERSION, queryField, analyzer);
			}
		}
		return parser;
	}
	
	private Analyzer getAnalyzer() {
		Analyzer analyzer = null;
		//analyzer = new org.apache.lucene.analysis.standard.StandardAnalyzer(LuceneManager.LUCENE_VERSION);
		//analyzer = new org.apache.lucene.analysis.core.KeywordAnalyzer();
		analyzer = new org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer(LuceneManager.LUCENE_VERSION);
		//analyzer = new org.apache.lucene.analysis.cn.ChineseAnalyzer();
		//analyzer = new org.apache.lucene.analysis.standard.ClassicAnalyzer(LuceneManager.LUCENE_VERSION);
		//analyzer = new org.apache.lucene.analysis.core.SimpleAnalyzer(LuceneManager.LUCENE_VERSION);
		return analyzer;
	}

	/** 排序 */
	private Sort getSort(String sortfield, String sortReverse) {
		if(logger.isInfoEnabled()) logger.info("[SORT][Field:"+sortfield+"][Reverse:"+sortReverse+"]");
		//排序
		Sort sort = new Sort();
		if(null==sortfield || "".equals(sortfield)) {
			//default sort
			sort = DEFAULT_SORT;
		} else {
			boolean reverse = ("Y".equalsIgnoreCase(sortReverse)?true:false);
			List<SortField> sortList = new ArrayList<SortField>();
			String[] sortFields = sortfield.split(",");
			for(String sortField : sortFields) {
				sortList.add(new SortField(sortField, SortField.Type.STRING,reverse));
			}
			sort =  new Sort(sortList.toArray(new SortField[sortList.size()]));
		}
		return sort;
	}

	private String genHitHTML(String queryString, String indexType, String companyName) {
		StringBuffer sb = new StringBuffer();
		sb.append("<font ");
		sb.append("color='").append(getColorByIndexType(indexType)).append("'");
		sb.append(">");
		sb.append(markQueryString(queryString, companyName));
		sb.append("</font>");
		return sb.toString();
	}

	/** 依索引資料類型分顏色顯示 */
	private String getColorByIndexType(String indexType) {
		String color = "";
		if(PrefixConstants.INDEX_TYPE_1.equals(indexType) || PrefixConstants.INDEX_TYPE_5.equals(indexType))
			color = PrefixConstants.COLOR_INDEX_TYPE_1;
		else if(PrefixConstants.INDEX_TYPE_2.equals(indexType))
			color = PrefixConstants.COLOR_INDEX_TYPE_2;
		else if(PrefixConstants.INDEX_TYPE_3.equals(indexType))
			color = PrefixConstants.COLOR_INDEX_TYPE_3;
		return color;
	}

	//換行
	private static final String SPLITW = "𪚕";
	private String markQueryString(String queryString, String orgString) {
		try {
			if (null==queryString) queryString = "";
			String[] queryArray = ChineseConverter.denormalization(queryString);;
			for (String query:queryArray){
				if (null != query && !"".equals(query)) {
					if ("\"".equals(query.substring(0,1)) && "\"".equals(query.substring(query.length()-1))) {
						query = query.substring(1, query.length()-1);
					}
					orgString = orgString.replaceAll(query, "<font color='#FF0000'>"+query+"</font>");
				}
			}
			queryArray = orgString.split(SPLITW);
			orgString = "";
			for (String query:queryArray){
				if (query.indexOf("</font>")>=0) {
					if (!"".equals(orgString)) orgString += "<br />";
					orgString += query;
				}
			}
			if ("".equals(orgString)) {
				for (String query:queryArray){
					if (!"".equals(orgString)) orgString += "<br />";
					orgString += query;
				}			
			}
		} catch (Exception e){}
		return orgString;
	}
	
	/**
	 * 插入空白
	 * @param str
	 * @return
	 */
	private String insertSpace(String str) {
		String space = "  ";
		StringBuffer sb = new StringBuffer("");
		if( null!=str && !"".equals(str) ) {
			int len = str.length();
			for(int i=0;i<len;i++){
				if(!"".equals(sb.toString()))
					sb.append(space);
				sb.append(str.substring(i, i+1));
			}
		}
		return sb.toString();
	}

	public void insertSearchLog(SearchLog obj) {
		searchLogDao.insert(obj);
	}

	private IndexDataDao indexDataDao;
	private SystemCodeDao systemCodeDao;
	private SearchLogDao searchLogDao;

	public IndexDataDao getIndexDataDao() {return indexDataDao;}
	public void setIndexDataDao(IndexDataDao dao) {this.indexDataDao = dao;}
	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}
	public SearchLogDao getSearchLogDao() {return searchLogDao;}
	public void setSearchLogDao(SearchLogDao dao) {this.searchLogDao = dao;}

}