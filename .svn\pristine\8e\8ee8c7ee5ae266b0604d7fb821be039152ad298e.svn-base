<!DOCTYPE html>
<%--
程式目的：預查審核作業
程式代號：PRE3001
撰寫日期：103.05.19
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
--%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<%
obj.initPrefixNos();
if("init".equals(obj.getState())) {
	obj.init();
} else if("saveBusiItem".equals(obj.getState())) {
	obj.saveBusiItem();
} else if("assignNewOne".equals(obj.getState())) {
	obj.assignNewOne();
} else if("tempSave".equals(obj.getState())) {
	obj.tempSave();
} else if("save".equals(obj.getState())) {
	obj.tempSave();
} else if("resetCloseDate".equals(obj.getState())) {
	obj.resetCloseDate();
}
%>
<html>
<head>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3001"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<g:compress>
<script type="text/javascript">
var prefixNos;
var prefixJson;
var ilocation = -1;
function setfocus(idx) {
	ilocation = idx;
	document.forms[0].focus.value = ilocation;
}

function addSpecialWord() {
    if(ilocation < 0) {
    	$(".cedb1002:last").find("[type=button]").click();
		ilocation = $(".cedb1002").size() + 1;
    } else if ( ilocation > $("#cedb1002s tr").length-1 ) {
    	$("#cedb1002s tr").eq(ilocation-1).find("[type=button]").click();
    } else {
    	$("#cedb1002s tr").eq(ilocation).find("[type=button]").click();
    }
    $("#cedb1002s tr").eq(ilocation).find("input[name=busiItemNo]").val("ZZ99999");
	$("#cedb1002s tr").eq(ilocation).find("input[name=busiItem]").val("除許可業務外，得經營法令非禁止或限制之業務");
}

function doReload(extendMark, extendReason, extendOther) {
	$("#extendMark").val(extendMark);
	$("#extendReason").val(extendReason);
	$("#extendOther").val(extendOther);
	$("#save").trigger("click");
	//window.location.href = window.location.href;
	//展期處理後不做頁面的reload, 只重新查詢案件流程
	/*
	$.post(getVirtualPath() + "tcfi/ajax/jsonCedb1010s.jsp?q=" + $("#prefixNo").val(), function( data ){
		if(data) {
			$("#cedb1010s tr").each(function(i, v) {
				if(i != 0)	$(v).remove();
			});
			setCedb1010Row(data.cedb1010s);
		}
	});
	*/
	// 103/11/21 現在展期完不做reload 也不重新查詢案件流程 改成直接存檔
}

function changeCase() {
	var e = jQuery.Event("keypress");
	e.which = 13;
	jQuery('#prefixNo').trigger(e);
}

$(document).ready(function() {
	var d = new Date().getTime();
	form1.responseTime1.value = d;
	$.blockUI({ message: '<h1> 資料載入中，請稍後  <img src="/prefix/images/jquery/busy.gif" /></h1>', overlayCSS: { backgroundColor: '#F3EFEF' }   });
	//user環境較慢,先將元素藏起直到tab排好再一次顯示
	//$('#form1').hide();
	if( $("#state").val() == "assignSuccess" ) { //分完文後的處理行為
		var assignPrefixNo = $("#assignPrefixNo").val();
		try {
			// prefixNos = JSON.parse($.cookie("prefixNos"));
			// prefixNos = JSON.parse($("#hiddenPrefixNos").val());
			prefixNos = $("#hiddenPrefixNos").val().split(",");
		}catch(e){}
		if($.inArray(assignPrefixNo, prefixNos) == -1) {
			prefixNos.push($("#assignPrefixNo").val());
		}
			
		//$.cookie("prefixNos", JSON.stringify(prefixNos));
		$("#hiddenPrefixNo").val(JSON.stringify(prefixNos));
		
		$("#prefixNo").val(prefixNos[prefixNos.length-1]);
		window.location = getVirtualPath() + 'tcfi/pre/pre3001_00.jsp?prefixNo=' + assignPrefixNo+"&hiddenPrefixNos="+prefixNos;
	} else {
		//上層頁籤
		$("#tabs").tabs();
		//下層頁籤
		$("#tabs2").tabs();
	}

	<% if(!"".equals(Common.get(obj.getErrorMsg()))){%>
		$('input[name="ERRMSG"]').val('<%=obj.getErrorMsg()%>');
	<% }%>
	
	$('#backToPrev3001').click(function() {
		window.close();
	});

	init();
	$(document).ajaxStop(function() {
		if(!$("#reserveDate").val()) {
			$("#reserveTip").text('本案件尚無保留期限');
		}else {
			$("#reserveTip").text('');
		}
	});

	$("#synonym").click(function() {
		window.open('pre8017.jsp', 'pre8017');
	});
	//外商\大陸商或合併(分割)消滅註記
	$("#btnForeign").click(function(){
		var prop = "width=800,height=300,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
		window.open("pre3001_05.jsp?view=first&prefixNo=" + $("#prefixNo").val() , "foreignWindow",prop);
	});
	//分文查詢
	$("#btnDispatchQuery").click(function() {
		var prop = "width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
		window.open("pre4017.jsp?progID=PRE4017", "dispatchQuery", prop);
	});
	//異動內容
	$("#btnHisttoryList").click(function() {
		if($("input[name=banNo]").val() != '') {
			var prop = "width=800,height=480,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0";
			window.open("pre3001_04.jsp?banNo=" + $("input[name=banNo]").val(), "pre3001_04", prop);
		} else {
			alert("查無資料");
		}
	});
	//備註歷史
	$("#btnHistor").click(function(){
		closeReturnWindow();
		returnWindow=window.open('pre3001_08.jsp?prefixNo='+$("#prefixNo").val(),'pre3001_08','top=300,left=100,width=800,height=300,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');
	});
	//片語-備註紀錄
	$("#btnExampleForSync-remark1").click(function(){
		popSystemCodeReturnName("15", "sync-remark1", "");
		$("#sync-remark1").focus();
	});
	//片語-審核結果
	$("#btnExampleForSync-approveRemark").click(function(){
		popSystemCodeReturnName("15", "sync-approveRemark", "2");
		$("#sync-approveRemark").focus();
	});
	//片語-同名註記1
	$("#btnExampleForRemarkId0").click(function(){
		popSystemCodeReturnName("15", "remarkId0", "1");
		$("#remarkId0").focus();
	});
	//片語-同名註記2
	$("#btnExampleForRemarkId1").click(function(){
		popSystemCodeReturnName("15", "remarkId1", "1");
		$("#remarkId1").focus();
	});
	//片語-同名註記3
	$("#btnExampleForRemarkId2").click(function(){
		popSystemCodeReturnName("15", "remarkId2", "1");
		$("#remarkId2").focus();
	});
	//片語-同名註記4
	$("#btnExampleForRemarkId3").click(function(){
		popSystemCodeReturnName("15", "remarkId3", "1");
		$("#remarkId3").focus();
	});
	//片語-同名註記5
	$("#btnExampleForRemarkId4").click(function(){
		popSystemCodeReturnName("15", "remarkId4", "1");
		$("#remarkId4").focus();
	});
	//展期處理
	$("#doExtDean").click(function() {
		var top = (window.screen.availHeight-30-150) / 2;
		var left = (window.screen.availWidth-10-550) / 2;
		var prop = "top="+top+",left="+left+",width=640,height=200,scrollbars=0,resizable=1,toolbar=0,menubar=0,directories=0,status=0,location=0";
		window.open("pre3001_06.jsp?prefixNo=" + $("#prefixNo").val(), "ExtDean", prop);
	});
	//分文
	$("#assignBtn").click(function(){
		$.blockUI({ message: '<h1>資料載入中，請稍後...</h1>' });
		$.cookie("activeTabIndex", 0);
		form1.state.value = "assignNewOne";
		form1.submit();
	});
	//分文 (改用ajax, 減少畫面reload次數)
	$("#assignBtnTest").click(function(){
		var prefixNos;
		var json = getRemoteData(getVirtualPath() + "tcfi/ajax/jsonAssign.jsp");
		var obj = JSON.parse(json);
		if (obj.result != 'assignSuccess') {
			$('input[name="ERRMSG"]').val(obj['result']);	
		} else {
		// ----------------------------------------------------------------------------------
			var assignPrefixNo = obj['assignPrefixNo'];
			try {
				// prefixNos = JSON.parse($.cookie("prefixNos"));
				// prefixNos = JSON.parse($("#hiddenPrefixNos").val());
				prefixNos = $("#hiddenPrefixNos").val().split(",");
			}catch(e){}
			if($.inArray(assignPrefixNo, prefixNos) == -1) {
				prefixNos.push(obj['assignPrefixNo']);
			}
			
			//$.cookie("prefixNos", JSON.stringify(prefixNos));
			$("#hiddenPrefixNo").val(JSON.stringify(prefixNos));
			$("#prefixNo").val(prefixNos[prefixNos.length-1]);
			
			//var temp = getVirtualPath() + 'tcfi/pre/pre3001_00.jsp?prefixNo=' + assignPrefixNo+"&hiddenPrefixNos="+prefixNos;
			window.location = getVirtualPath() + 'tcfi/pre/pre3001_00.jsp?prefixNo=' + assignPrefixNo+"&hiddenPrefixNos="+prefixNos;
		}
	});
	//下一筆
	$("#nextBtn").click(function(e) {
		
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if(++currentIndex >= prefixNos.length) {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		} else {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		$.cookie("activeTabIndex", 0);
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var currentIndex = prefixNos.indexOf($("#prefixNo").val());
		if((currentIndex-1) < 0) {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[(prefixNos.length-1)]+"&hiddenPrefixNos="+prefixNos;
		} else {
			window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[--currentIndex]+"&hiddenPrefixNos="+prefixNos;
		}
		$.cookie("activeTabIndex", 0);
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		var last = prefixNos.length - 1;
		window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[last]+"&hiddenPrefixNos="+prefixNos;
		$.cookie("activeTabIndex", 0);
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		
		e.preventDefault();
		prefixNos = $("#hiddenPrefixNos").val().split(",");
		window.location.href = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo=" + prefixNos[0]+"&hiddenPrefixNos="+prefixNos;
		$.cookie("activeTabIndex", 0);
	});
	//除許可業務外
	$("#zz9999").click(function(){
		addSpecialWord();
	});
	
	//馬上辦
	$( "#doAtonce" ).click(function() {
        $( "#dialog-form" ).dialog( "open" );
	});
	$( "#dialog-form" ).dialog({
	      autoOpen: false,
	      height: 280,
	      width: 750,
	      modal: true,
	      title: "馬上辦類型",
	      buttons: {
	        "確定": function() {
	        	$( this ).dialog( "close" );
	        }
	      },
	      close: function() {
		     var values = "";
		     $("input[name='atonceReason']:checked").map(function(){
				var reason = $(this).val() + ", ";
				values += reason;
			 });
			 
	    	 var data = new Date().toLocaleString() + " " + values.replace(/, $/g, "");
	    	 $("#remark1").val(data);
	      }
    });

	try {
		prefixNos = JSON.parse($.cookie("prefixNos"));
	} catch(e) {}

	$("#current").val($("#prefixNo").val());

	$("#cedb1002s").delegate('input[type=text],input[type=checkbox]', 'focus', function () {
		var index = $(this).closest('tr').index();
		setfocus(index);
	});
		
	$("#saveBusiItem").click(function() {
		if(!checkItem()) {
			return;
		}
		if (isExitRest() == "yes") {
			form1.state.value = "saveBusiItem";
			form1.submit();
		}
	});

	$("#delBuItem").click(function() {
		$("#cedb1002s input[name=cedb1002Chk]:checked").closest('tr').remove();
		resetCedb1002SeqNo();
	});

	/* 是否要同步案件狀態至一站式系統確認視窗 */
	function showSyncOssDialog(closeDate) {
		var def = $.Deferred();
		if(!!closeDate){
			var html = '<p>此案件已結案, 是否要同步案件狀態至一站式系統?</p>'
			  		  +'<p style="color:red;">(注意:同步一站式會發送通知給申請人)</p>';
			$("#chooseSyncOssDiv").append(html);
			// create and/or show the dialog box here
			// but in "OK" do 'def.resolve()'
			// and in "cancel" do 'def.reject()'
			$("#dialog-chooseSyncOss").dialog({
				title: "同步至一站式確認視窗",
				resizable: false,
	    	    closeOnEscape: false,
	    	    dialogClass: "noclose",
	    	    height: "auto",
	    	    width: 400,
	    	    modal: true,
	    	    position: "top",
	    	    buttons: {
	    	        "取消": function () {
	    	        	$(this).dialog("close");
	    	            def.reject();
					},
					"確定": function () {
	    	            $(this).dialog("close");
	    	            def.resolve();
	    	        }
	    	    },
	    	    open: function() { 
					$(this).parents('.ui-dialog-buttonpane button:eq(0)').focus();	//預設focus在取消按鈕
				}
	    	});
		} else {
			def.resolve();
		}
	   return def.promise();
	}
	
	function doSave(e) {
		var d = new Date().getTime();
		form1.requestTime1.value = d;
		form1.chooseSyncOss.value = 'Y';	//預設所有案件都要將狀態同步至一站式
		//QA10906100033 如果已經發文結案的案件, 則跳出視窗提醒承辦人
    	showSyncOssDialog(prefixJson.closeDate)
			  .done(function() {
	    	    // they pressed OK
	    		//alert('點擊確定 !chooseSyncOss:' + form1.chooseSyncOss.value);
	    	}).fail(function() {
	    	    // the pressed Cancel
	    	    form1.chooseSyncOss.value = 'N';
	    	    //alert('點擊取消! !chooseSyncOss:' + form1.chooseSyncOss.value);
	   		}).always(function() {
	    	    //alert('不論確定或取消, 都要執行此區塊!');
	    	    $("#chooseSyncOssDiv").empty();
	    	    
	    	    var json1 = $("#form1").serializeObject();
	    	    var data = $.extend(prefixJson, getPrefixVo(), json1);
	   			delete data.cedb1006s;
	   		    delete data.cedb1010s;
	   		    data.approveResult = $("select[name=approveResult]").val();
	   		    data.remark = $('[name=remark]').eq(0).val();
	   		    data.companyName = $("#fragment-1 input[name=companyName]").val();
	   			data.applyKind = getApplyKindSave();
	   		    if($("input[name=approveResultAllNo]:checked").val()) {
	   				data.approveResult = 'N';
	   			}
	   			if($("input[name=otherReason]").is(':checked')) {
	   				data.otherReason = 'Y';
	   			} else {
	   				data.otherReason = null;
	   			}
	   			//附件
	   			data = setAttachment(data);
	   			$("#json").val(JSON.stringify(data));
	   		    form1.submit();
	   		});
	};
	
	$("#tempSave").click(function(e) {
		form1.state.value = "tempSave";
		doSave(e);
	});

	$("#save").click(function(e) {
		form1.state.value = "save";
		// 103/10/02 當勾選"其他(非屬公司法第18條...)"時, 不顯示"經查公司名稱第x欄..."
		// 但是考量到使用者有可能打勾後不輸入, 因此加入此限制
 		if ( document.getElementById("otherReason").checked && document.getElementById("sync-approveRemark").value == "" ) {
 			alert('勾選"其他"時請輸入原因');
 			return false;
 		}
		//檢還用
		if (!chkAttachment()) {
			return;
		}
			
		var cedb1001s = [];
		var prefixNo = $("#prefixNo").val();
		var seqNo = "";
		var seq = 1;
		$("input[name=companyName]", "#cedb1001s").each(function(k, v) {

			var companyName = $(v).val();
			if(companyName) {
				var cedb1001 = {};
				cedb1001.prefixNo = prefixNo;
				cedb1001.companyName = companyName;
				cedb1001.seqNo = seqNo;
				var approveResult = $(v).closest('td').next('td').find("input[name=approveResult]").is(":checked");
				cedb1001.approveResult = approveResult ? "Y" : "N";
				cedb1001s.push(cedb1001);
			}
		});			
		var i = 0;
		var withdrawCompanyName = "";
		form1.withdrawCompanyName.value ="";
		for (i=0;i<cedb1001s.length;i++) {
			if (cedb1001s[i].approveResult == "Y" )
				form1.withdrawCompanyName.value = cedb1001s[i].companyName;
		}
		var isNeedWithdraw = "";
		var x = getRemoteData(getVirtualPath() + "tcfi/ajax/jsonPre3001_07.jsp?prefixNo="+$("#prefixNo").val()+"&withdrawCompanyName="+encodeURI(form1.withdrawCompanyName.value));
		isNeedWithdraw = x;
		var approveResult = $("select[name=approveResult]").val();
		if(isNeedWithdraw == 'true' && approveResult == 'Y') {
			if( !confirm( '請問要強迫存檔嗎 ?\n 按[確定]強迫存檔，\n[取消]進行檢還(一維)或撤件(一站式)' ) ) {
				window.open("pre3001_07.jsp?prefixNo="+$("#prefixNo").val()+"&withdrawCompanyName="+form1.withdrawCompanyName.value,"pre3001_07","width=1024,height=600,left=0,top=0,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0");
				return false;
			}
		}

		if(!checkCedb1002s() || checkDuplicateItemCode() || !checkReserve365() || !checkSpecialName()) {
			e.preventDefault();
		} else {
// 			$("#tempSave").trigger("click");
			doSave(e);
		}

	});

	$("#buItemSelectAll").click(function(){
		commonUtils.all("cedb1002Chk");
	});

	$("#buItemUnSelectAll").change(function(){
		if(this.checked)
			commonUtils.unAll("cedb1002Chk");
	});

	$("#companyNo").keypress(function(e){
		if(e.which == 13) {
			e.preventDefault();
			$.post(getVirtualPath() + "tcfi/ajax/jsonCedb2000.jsp?q=" + $(this).val(), function(data){
				$("input[name='companyName']", "#cedb1001s").val('').eq(0).val(data.COMPANY_NAME);
			});
		}
	});

		$("#prefixNo").keypress(function(e){
			if(e.which == 13) {
				var d = new Date().getTime();
				form1.requestTime2.value = d;
				$.post(getVirtualPath() + "tcfi/ajax/jsonPrefixVo.jsp?from=PRE3001&q=" + $("#prefixNo").val(), function( data ) {
					if(!data)
						return;

					commonUtils.mappingJsonByName(data, false);
					if (data.companyName != null && data.companyName != "" && data.companyName != "undefined") 
						$("#companyName").val(data.companyName) ;
				    else 
				    	$("#companyName").val("") ; 
				    
					selectApplyKind(data.applyKind);
					document.getElementById("xMainFileCompanyName").innerText = form1.mainFileCompanyName.value;
					$("#xCompanyName").html($("#companyName").val());
					
					var isSamePerson = $("input[name='applyName']").val() == $("input[name='contactName']").val();
					
					if(isSamePerson && $("input[name='applyAddr']").val()=="") {
						$("input[name='applyAddr']").val(data.CONTACT_ADDR);
					}

					var applyType = data.applyType;
					var contactGetKind = data.contactGetKind;

					if(data.cedb1023.applyLawName) {
						$("input[name=applyLawName]").val(data.cedb1023.applyLawName);
					}
					if(data.cedb1023.applyBanNo) {
						$("input[name=applyBanNo]").val(data.cedb1023.applyBanNo);
					}
					
					if( data.isPrefixForm == 'Y' || data.isOtherForm == 'Y' || data.isSpec == 'Y' || data.isOtherSpec == 'Y' ) {
						$('#appenixTdMark').css({'color':'red','font-size':'28px','font-weight':'bold', 'background-color':'yellow'});
					}
					
					if( $('#remark').val() ) {
						$('#remarkLabel').css({'color':'red', 'font-size':'16px','font-weight':'bold', 'background-color':'yellow'});
					}

					$("input[name=receiveName]").val(data.cedb1023.getName);
					$("input[name=receiveId]").val(data.cedb1023.getName);
					$("input[name=contactCel]").val(data.cedb1023.contactCel);
					$("input[name=receiveAddr]").val(data.cedb1023.getAddr);
					$("input[name=sms]").val(data.cedb1023.sms);
					$("input[name=changeType]").val(data.cedb1023.changeType);
					$("input[name=closed]").val(data.cedb1023.closed);
					$("input[name=orgType]").val(data.cedb1023.orgType);
					if ($("input[name=closed]").val() == 'Y') {
						$("#textClosed").html("閉鎖性股份有限公司");	
					}
					$('#cmpySetupDate').val(data.cmpySetupDate);

					// Mantis0044627:問題需求單-1120510001_預查(中辦)
					// 已審核之公司名稱不予變更
					if (data.closeDate) {
						$("input[name=companyName]").attr('disabled', true);
						$("input[name=approveResult]").attr('disabled', true);
					}
					
					//2014.11.22 replace ajax html row : only data
					var cedb1001Html = "";				
					for(var i=0; i<5; i++) {
						var seqNo = "";
						var companyName = "";
						var isApprove = "";
						var remark = "";							
						if(data.cedb1001s[i]) {
							seqNo = data.cedb1001s[i].seqNo;
							companyName = data.cedb1001s[i].companyName;
							isApprove = data.cedb1001s[i].approveResult; // == 'Y' ? "checked" : "";
							remark = commonUtils.trimUndefined(data.cedb1001s[i].remark);
						}else {
							seqNo = commonUtils.padZero(i+1, 2);
							companyName = "";
							isApprove = "";
							remark = "";
						}
						document.getElementById("seqNoId"+i).value = seqNo;
						document.getElementById("companyNameId"+i).value = companyName;						
						if (isApprove=='Y') {
							document.getElementById("approveResultId"+i).checked = true;
						} else {
							document.getElementById("approveResultId"+i).checked = false;
						}
						document.getElementById("remarkId"+i).value = remark;
					}

					//重構至approve.js
					if(data.cedb1001s && data.cedb1004s) {
						showSames(data.cedb1001s, data.cedb1004s);
						showPreSearchs(data.prefixNo);
						if( "A"==data.prefixStatus || "E"==data.prefixStatus ) {
							//撤件(A)或撤件退費(E)，不能再異動 預查名稱-審核結果
							$("input[name=approveResultAllNo]").prop('disabled', true);
							$("#approveResultId0").prop('disabled', true);
							$("#approveResultId1").prop('disabled', true);
							$("#approveResultId2").prop('disabled', true);
							$("#approveResultId3").prop('disabled', true);
							$("#approveResultId4").prop('disabled', true);
						}
					}
					
					//法人資訊
					setCedb1022(data.cedb1022);
					//營業項目
					setCedb1002Row(data.cedb1002s);
					//案件流程
					setCedb1010Row(data.cedb1010s);
					
					prefixJson = $.extend(prefixJson, data);
					syncRremark1();
					syncApproveRemark();
					
					$.unblockUI();
					$('#content').remove();
					
					var d = new Date().getTime();
					form1.responseTime2.value = d;
					
					$('input[name="forPMOdebug"]').val($('input[name="requestTime1"]').val()+', '+$('input[name="responseTime1"]').val()+', '+$('input[name="requestTime2"]').val()+', '+$('input[name="responseTime2"]').val());
				});

				if($("#companyNames").find("tr").size() == 1) {
					setTimeout(function(){
						var html = '';
						
						$.post( getVirtualPath() + "tcfi/ajax/jsonCedbc1001.jsp?q2=Y&q=" + $("#prefixNo").val(), function( data ) {
							html += '<tr><td><input name="seqNo" class="inputNoBorder" type="text" value="'+ data[0].SEQ_NO +'" readonly /></td>' +
							'<td><input name="applyCompanyNamesArray" class="inputNoBorder" type="text" value="'+ data[0].COMPANY_NAME +'" readonly /></td></tr>';
							
							$("#companyNames").append(html).show();
						});

					}, 500);
				}
		    }
		});
	});

	$(window).load(function(){
		changeCase();
		$("#cedb1001s").on("change", "input[name=approveResult]", function() {
		    $("input[name=approveResult]").not(this).prop("checked", false);
			commonUtils.unAll("approveResultAllNo");
		});
	});

	function getPrefixVo() {
		var prefixVo = {};
		var cedb1001s = [];
		var cedb1002s = [];
		var cedb1022 = {};
		var cedb1023 = {};
		var prefixNo = $("#prefixNo").val();
		var seqNo = "";
		var seq = 1;
		$("input[name=companyName]", "#cedb1001s").each(function(k, v) {
			var companyName = $(v).val();
			if(companyName) {
				var cedb1001 = {};
				//序號要檢查重排
				seqNo = commonUtils.padZero(seq++, 2);
				cedb1001.prefixNo = prefixNo;
				cedb1001.companyName = companyName;
				cedb1001.seqNo = seqNo;

				var approveResult = $(v).closest('td').next('td').find("input[name=approveResult]").is(":checked");
				cedb1001.approveResult = approveResult ? "Y" : "N";
				var remark = $(v).closest('tr').find("input[name=remark]").val();
				if(remark) {
					//cedb1001.remark = remark.replaceAll(' ', '').replaceAll('"', "''");
					//要保留空白
					cedb1001.remark = remark.replaceAll('"', "''");
				}
				cedb1001s.push(cedb1001);
			}
		});

		$("input[name=busiItem]").each(function(k, v) {
			var busiItem = $(v).val().replaceAll(' ', '');
			if(busiItem) {
				var cedb1002 = {};
				cedb1002.prefixNo = prefixNo;
				cedb1002.busiItem = busiItem;
				cedb1002.seqNo = $(v).closest('td').prev('td').prev('td').find("input[name=itemSeqNo]").val();
				cedb1002.busiItemNo = $(v).closest('td').prev('td').find("input[name=busiItemNo]").val();
				cedb1002s.push(cedb1002);
			}

		});

		cedb1022.prefixNo = prefixNo;
		cedb1022.applyLawName = $('input[name=applyLawName]').val();
		cedb1022.applyBanNo = $('input[name=applyBanNo]').val();
		
		cedb1023.prefixNo = prefixNo;
		cedb1023.getAddr = $("input[name=receiveAddr]").val();
		cedb1023.getName = $("input[name=receiveName]").val();
		cedb1023.sms = $("input[name=sms]").val();
		cedb1023.contactCel = $("input[name=contactCel]").val();
		cedb1023.changeType = getApplyKind();
		cedb1023.closed = $("input[name=closed]").val();
		cedb1023.orgType = $("input[name=orgType]").val();

		prefixVo.cedb1001s = cedb1001s;
		prefixVo.cedb1002s = cedb1002s;
		prefixVo.cedb1022 = cedb1022;
		prefixVo.cedb1023 = cedb1023;

		return prefixVo;
	}
		
	function approveResultAllNoClick(input){
		commonUtils.unAll("approveResult");
	}

	function saveData(printName) {

		if (document.forms[0].prefixFormNo.value.length != 9
				&& document.forms[0].prefixFormNo.value.length > 0) {
			alert("附件預查表編號長度不符，請重新輸入");
			return;
		}

		document.forms[0].method.value = "save";
		document.forms[0].printName.value = printName;
		document.forms[0].submit();
	}

</SCRIPT>
</g:compress>
<style>
.alignleft {
	float: left;
}
.alignright {
	float: right;
}

#content{ position:absolute; left: 0; right: 0; bottom: 0; top: 0px; background: #fff; height: expression(document.body.clientHeight); width: expression(document.body.clientWidth); }

#iframe {
	height: 580px;
	width: 100%;
}

.inputNoBorder {
	float: left;
	border: none !important;
	margin-top: 7px;
	background-color: White;
	background-repeat: no-repeat;
	background-position: center center;
}

.ui-dialog .ui-dialog-title {
    float: left;
    margin: .1em 16px .1em 0;
    text-align: center;
    width: 100%;
}

/* jquery-ui dialog: 不顯示右上角x按鈕 */
.noclose .ui-dialog-titlebar-close {
    display: none;
}

/* jquery-ui dialog: (確定/取消)按鈕置中 */
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
	text-align: center; float: none;
}
</style>

<g:compress>
	<script type="text/javascript" src="<%=contextPath%>/js/approve.js"></script>
</g:compress>

</head>
<body class="" onLoad="">
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
<c:param name="function" value='PRE3001'/>
</c:import>

<table id="shareBar" width="100%" border="0" cellpadding="2" cellspacing="0">
	<tr>
		<td >
			<input class="toolbar_default" type="button" id="resetClose" value="重設發文日期" />
			<input class="toolbar_default" type="button" id="previewApproveForm" value="電子核定書" />
			<input class="toolbar_default" type="button" name="btnQuery6" disabled="true" value="列印申請表">
			<input class="toolbar_default" type="button" id="tempSave" value="暫存" />
			<input class="toolbar_default" type="button" id="save" value="存檔" />
		</td>
		<td style="text-align: right;padding-right:15px;">
			<input class="toolbar_default" type="button" id="assignBtn" name="assignBtn" value="分文 ">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value='PRE4001,PRE3002,PRE3004,PRE3008,PRE3013'/>
				<c:param name="shortcut" value='N'/>
			</c:import>
			<input class="toolbar_default" type="button" value="離開" id="backToPrev3001">
		</td>
	</tr>
</table>

<table border="0" width="100%" style="background-color: #F3EFEF; color: black">
	<tr>
		<td class="title_form" style="text-align: right;color:#0000FF;">預查編號</td>
		<td class="title_form" style="text-align: left">
			<input type="text" class="field_RO" id="prefixNo" name="prefixNo" size="10" value="<%=obj.getPrefixNo()%>" />
			<font color="#008000"><%=obj.getApplyWay()%></font>
		</td>
		<td class="title_form" style="text-align: right;color:#0000FF;">預查種類</td>
		<td class="title_form" style="text-align: left">
			<input type="checkbox" name="setup" value="true">設立
			<input type="checkbox" name="changeName" value="true">名稱變更
			<input type="checkbox" name="changeItem" value="true">所營變更
			<input type="hidden" name="closed" value="">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<input type="hidden" name="orgType" value="">
			<!-- style="color:#F3EFEF" -->
			<input type="hidden" class="field_RO"  name="requestTime1" readonly  value="<%=obj.getRequestTime1()%>">
			<input type="hidden" class="field_RO"  name="responseTime1" readonly >
			<input type="hidden" class="field_RO"  name="requestTime2" readonly>
			<input type="hidden" class="field_RO"  name="responseTime2" readonly >
			<span id="textClosed" style="color:#FF0000;font-weight:bold;"></span>
		</td>
		<td class="title_form" style="text-align: right; width: 90px;color:#0000FF;">電子流水號</td>
		<td class="title_form" style="text-align: left">
			<input type="text" class="field_RO" id="telixNo" name="telixNo" size="20" value="" readonly />
		</td>
	</tr>
	<tr>
		<td class="title_form" style="text-align: right;color:#0000FF;">統一編號</td>
		<td class="title_form" style="text-align: left">
			<input class="field_RO" name="banNo" type="text" size="10" value="">
		</td>
		<td class="title_form" style="text-align: right;color:#0000FF;">領件方式</td>
		<td class="title_form">
		    <input type="radio" name="getKind" value="3">線上列印   <!--2024/03/17 新增線上列印 -->
			<input type="radio" name="getKind" value="1">自取
			<input type="radio" name="getKind" value="2">郵寄
			郵資<input type="text" class="field_RO" name="getKindRemark" maxlength="3" size="3" value="" />
			&nbsp;&nbsp;&nbsp;掛號號碼
			<input type="text" class="field" name="postNo" maxlength="10" size="10" value="" />
		</td>
		<td class="title_form" style="text-align: left" colspan="2">
			<table>
				<tr><td>
					<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
				</td>
				<td width="100" align="center">
					<input type="text" class="field_RO" id="current" size="10" value="" readonly />
 				</td>
				<td>
					<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
					<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
 				</td>
				</tr>
			</table>
		</td>
	</tr>
</table>

	<div id="tabs">
		<ul>
			<li><a href="#fragment-1">案件資料</a></li>
			<li><a href="#fragment-2">預查名稱</a></li>
			<li><a href="#fragment-3">營業項目</a></li>
			<li><a href="#fragment-4">案件流程</a></li>
			<li>
				<span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;訊息列:
					<input class="field_RO" type="text" name="ERRMSG" size="30" value="" style="border-bottom: 1px #000000 dashed;color:red;" readonly>
					<input class="field_RO" type="text" name="forPMOdebug" size="70" value="" style="border-bottom: 1px #000000 dashed;color:#F3EFEF;font-size:12px" readonly>
				</span>
			</li>
		</ul>
		<div id="fragment-1" style="display:none;">
			<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<tr>
					<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
				</tr>
				<tr>
					<td class="td_form" width="80px">特取名稱</td>
					<td class="td_form_white" width="160px">
						<input type="text" class="field_Q cmex" id="specialName" name="specialName" size="20" value="" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;"/>
					</td>
					<td class="td_form" width="100px">本次預查名稱</td>
					<td class="td_form_white" width="160px">
						<span id="xCompanyName" style="white-space: nowrap;"></span>
						<input type="hidden" class="field_RO" style="white-space: nowrap;" id="companyName" name="companyName" size="30" value="" readonly />
					</td>
					<td class="td_form">收件日期</td>
					<td class="td_form_white">
						<input type="text" class="field_RO" id="receiveDate" name="receiveDate" size="6" value="" readonly />
						<span id="reserveTip" style="color:#FF0000;font-weight:bold;"></span>
					</td>
				</tr>
				<tr>
					<td class="td_form">承辦人</td>
					<td class="td_form_white">
						<input type="text" class="field_RO" id="staffName" name="staffName" size="10" value="" readonly />
					</td>
					<td class="td_form">前次預查名稱</td>
					<td class="td_form_white">
						<input type="text" class="field_Q cmex" id="lastCompanyName" name="lastCompanyName" size="20" value="" />
					</td>
					<td class="td_form" width="80px">本日分文數</td>
					<td class="td_form_white">
						<input type="text" class="field_RO" id="distributedCountByDay" name="distributedCountByDay" size="6" value="<%=obj.getDistributedCountByDay() %>" readonly />
						<input type="button" class="toolbar_form" id="btnDispatchQuery" name="btnDispatchQuery" value="分文查詢" />
					</td>
				</tr>
				<tr>
					<td class="td_form" width="80px" >公司現況<br/>主檔名稱</td>
					<td class="td_form_white">
						<span id="xMainFileCompanyName" style="white-space: nowrap;"></span>
						<input type="hidden" class="field_RO" id="mainFileCompanyName" name="mainFileCompanyName" readonly />
					</td>
					<td class="td_form" id="appenixTdMark"><span class="alignleft">附</span><span class="alignRight">件</span></td>
					<td class="td_form_white" colspan="3">
						<input type="checkbox" id="isPrefixForm" name="isPrefixForm" value="Y">
						<select class="field" id="docType" name="docType">
							<option value="1">預查表正本</option>
							<!-- 103/10/30 依優仲轉述之使用者要求將 預查表影本 自下拉式選單中移除 -->
							<option value="3">線上申辦檢還申請書</option>
						</select>
						<input type="text" class="field" id="prefixFormNo" name="prefixFormNo" maxlength="9" size="9" value="" />
						<br/> 
						<input type="checkbox" id="isOtherForm" name="isOtherForm" value="Y" />其他機關核准函 
						<input type="checkbox" id="isSpec" name="isSpec" value="Y" />說明書 
						<input type="checkbox" id="isOtherSpec" name="isOtherSpec" value="Y" />其他 
						<input type="text" class="field" id="otherSpecRemark" name="otherSpecRemark" maxlength="500" size="20" value="" />
					</td>
				</tr>
				<tr>
					<td class="td_form">前次異動者</td>
					<td class="td_form_white" colspan="5">
						<input type="text" class="field_RO" id="updateName" name="updateName" size="20" value="" readonly />
						<input type="button" class="toolbar_form" id="btnHisttoryList" name="btnHisttoryList" value="異動內容" />
						<input type="button" class="toolbar_form" id="btnForeign" name="btnForeign" value="外商\大陸商或合併(分割)消滅註記" />
					</td>
				</tr>
				<tr>
					<td class="td_form" id="remarkLabel">註記說明</td>
					<td class="td_form_white" colspan="5">
						<input type="text" class="field_Q" id="remark" name="remark" size="100" value="" />(檢還,撤件專用)
					</td>
				</tr>
				<tr>
					<td class="td_form" width="80px">
						備註紀錄<br/>
						<input type="button" class="toolbar_form" id="btnHistor" name="btnHistor" value="備註歷史" />
					</td>
					<td class="td_form_white" colspan="3">
						<textarea class="content" id="remark1" name="remark1" cols="50" rows="5" ></textarea>
					</td>
					<td class="td_form" width="80px">審核結果</td>
					<td class="td_form_white">
						<textarea class="content" id="approveRemark" name="approveRemark" cols="40" rows="5"></textarea>
					</td>
				</tr>
				<tr>
					<td class="td_form">核覆結果</td>
					<td class="td_form_white">
						<select class="field" id="approveResult" name="approveResult" disabled>
							<option value="A">審查中</option>
							<option value="N">不予核准</option>
							<option value="Y">核准保留</option>
						</select>
					</td>
					<td class="td_form">案件狀態</td>
					<td class="td_form_white">
						<input type="text" class="field_RO" id="prefixStatusDesc" name="prefixStatusDesc" size="20" value="" readonly />
					</td>
					<td class="td_form">前次登打員</td>
					<td class="td_form_white">
						<input type="text"class="field_RO" id="lastKeyin" name="lastKeyin" size="10" value="" readonly />
					</td>
				</tr>
				<tr>
					<td class="td_form">保留期限</td>
					<td class="td_form_white" colspan="5">
						<input type="text" class="field_RO" id="reserveDate" name="reserveDate" maxlength="10" size="12" value="" readonly />
						<input type="radio" name="reserveDays" value="180">保留半年 
						<input type="radio" name="reserveDays" value="365">保留一年 
						<input type="checkbox" name="reserveMark" value="Y" disabled>延長期限一個月 
						<input type="button" class="toolbar_default" id="doExtDean" name="doExtDean" value="展期處理">&nbsp;
					</td>
				</tr>
			</TABLE>

			<div id="tabs2">
				<ul>
					<li><a href="#tabs2-f1"><span>申請人資料</span></a></li>
					<li><a href="#tabs2-f2"><span>代理人資料</span></a></li>
					<li><a href="#tabs2-f3"><span>收件人資料</span></a></li>
					<li><a href="#tabs2-f4"><span>自由填列事項</span></a></li>
				</ul>
				<div id="tabs2-f1">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="100px">申請人姓名</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q cmex" id="applyName" name="applyName" size="10" value="" />
							</td>
							<td class="td_form" width="100px">身分ID</td>
							<td class="td_form_white" width="170px">
								<input type="text" class="field_Q" id="applyId" name="applyId" size="15" value="" />
							</td>
							<td class="td_form" width="80px">申請人電話</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="applyTel" name="applyTel" size="15" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form">所代表法人</td>
							<td class="td_form_white" colspan="3">
								<input type="text" class="field_Q" id="applyLawName" name="applyLawName" size="30" value="" />
							</td>
							<td class="td_form">法人統編</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="applyBanNo" name="applyBanNo" size="15" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form">申請人地址</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="applyAddr" name="applyAddr" size="80" value="" />
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f2">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="100px">代理人姓名</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q cmex" id="attorName" name="attorName" size="10" value="" />
							</td>
							<td class="td_form" width="150px">證書號碼╱身分ID</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q" id="attorNo" name="attorNo" size="10" value="" />
							</td>
							<td class="td_form" width="100px">聯絡電話</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="attorTel" name="attorTel" size="15" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form" width="100px">事務所所在地</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="attorAddr" name="attorAddr" size="80" value="" />
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f3">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;" >&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form" width="100px">收件人姓名</td>
							<td class="td_form_white" width="150px">
								<input type="text" class="field_Q cmex" id="receiveName" name="receiveName" size="10" value="" />
							</td>
							<td class="td_form" width="150px">簡訊通知回覆電話</td>
							<td class="td_form_white" width="120px">
								<input type="text" class="field_Q" id="contactCel" name="contactCel" size="10" value="" />
							</td>
							<td class="td_form" width="80px">寄件日期</td>
							<td class="td_form_white">
								<input type="text" class="field_Q" id="getDate" name="getDate" size="7" value="" />
								<input type="text" class="field_Q" id="getTime" name="getTime" size="6" value="" />
							</td>
						</tr>
						<tr>
							<td class="td_form">聯絡地址</td>
							<td class="td_form_white" colspan="5">
								<input type="text" class="field_Q" id="receiveAddr" name="receiveAddr" size="80" value="" />
								<input type="hidden" id="changeType" name="changeType" value="" />
								<input type="hidden" id="sms" name="sms" value="" />
							</td>
						</tr>
					</TABLE>
				</div>
				<div id="tabs2-f4">
					<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
						<tr>
							<td class="td_form" colspan="6" style="font-size:1px;height:3px;">&nbsp;</td>
						</tr>
						<tr>
							<td class="td_form_white" colspan="6">（不納入預查審核項目）</td>
						</tr>
						<tr>
							<td class="td_form" width="180px">國外匯款使用英文名稱</td>
							<td class="td_form_white" colspan="5">
								<input class="field_Q" id="extRemitEname" name="extRemitEname" type="text"
								 size="50" value="" maxlength="120" />(僅提供銀行開戶使用)
						</tr>
					</TABLE>
				</div>
			</div>
		</div>
		<div id="fragment-2" style="display:none;">
			<TABLE id="cedb1001s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<thead>
					<tr>
						<td class="td_form" style="text-align: center;width:50px;">序號</td>
						<td class="td_form" style="text-align: center;width:240px;" >預查名稱</td>
						<td class="td_form" style="text-align: center;width:70px;" >審核結果</td>
						<td class="td_form" style="text-align: center;width:350px;" >同名組織/結果</td>
						<td class="td_form" style="text-align: center;width:350px;">同名註記</td>
						<td class="td_form" style="text-align: center;">智慧型預查</td>
					</tr>
				</thead>
				<tr>
					<td class="td_form_white" style="text-align: center">
						<input class="field_Q" type="hidden" name="_rowsOfCompanyName" value="0">
						<input class="field_RO" readonly type="text" size="2" style="text-align: center" id="seqNoId0" name="seqNo" value="">
					</td>
					<td class="td_form_white">
						<input class="field_Q" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;" type="text" size="30" id="companyNameId0" name="companyName" value="">
						<input class="field" type="text" size="15" value="">
						<input class="toolbar_default" type="button" value="查" onclick="helpSearch(this);">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input style="height:30px;width:30px" type="checkbox" id="approveResultId0" name="approveResult" value="Y">
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showSameId0" name="showSame01" size="35" value="">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input size="30" type="text" id="remarkId0" name="remark" class="" value="">
						<input type="button" class="toolbar_form" id="btnExampleForRemarkId0" name="btnExampleForRemarkId0" value="片語" />
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showPreSearchId0" name="showPreSearch01" size="35" value="">
					</td>
				</tr>
				<tr>
					<td class="td_form_white" style="text-align: center">
						<input class="field_Q" type="hidden" name="_rowsOfCompanyName" value="1">
						<input class="field_RO" readonly type="text" size="2" style="text-align: center" id="seqNoId1" name="seqNo" value="">
					</td>
					<td class="td_form_white">
						<input class="field_Q" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;" type="text" size="30" id="companyNameId1" name="companyName" value="">
						<input class="field" type="text" size="15" value="">
						<input class="toolbar_default" type="button"  value="查" onclick="helpSearch(this);">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input style="height:30px;width:30px" type="checkbox" id="approveResultId1" name="approveResult" value="Y">
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showSameId1" name="showSame02" size="35" value="">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input size="30" type="text" id="remarkId1" name="remark" class="" value="">
						<input type="button" class="toolbar_form" id="btnExampleForRemarkId1" name="btnExampleForRemarkId1" value="片語" />
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showPreSearchId1" name="showPreSearch02" size="35" value="">
					</td>
				</tr>
				<tr>
					<td class="td_form_white" style="text-align: center">
						<input class="field_Q" type="hidden" name="_rowsOfCompanyName" value="2">
						<input class="field_RO" readonly type="text" size="2" style="text-align: center" id="seqNoId2" name="seqNo" value="">
					</td>
					<td class="td_form_white">
						<input class="field_Q" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;" type="text" size="30" id="companyNameId2" name="companyName" value="">
						<input class="field" type="text" size="15" value="">
						<input class="toolbar_default" type="button" value="查" onclick="helpSearch(this);">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input style="height:30px;width:30px" type="checkbox" id="approveResultId2" name="approveResult" value="Y">
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showSameId2" name="showSame03" size="35" value="">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input size="30" type="text" id="remarkId2" name="remark" class="" value="">
						<input type="button" class="toolbar_form" id="btnExampleForRemarkId2" name="btnExampleForRemarkId2" value="片語" />
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showPreSearchId2" name="showPreSearch03" size="35" value="">
					</td>
				</tr>
				<tr>
					<td class="td_form_white" style="text-align: center">
						<input class="field_Q" type="hidden" name="_rowsOfCompanyName" value="3">
						<input class="field_RO" readonly type="text" size="2" style="text-align: center" id="seqNoId3" name="seqNo" value="">
					</td>
					<td class="td_form_white">
						<input class="field_Q" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;" type="text" size="30" id="companyNameId3" name="companyName" value="">
						<input class="field" type="text" size="15" value="">
						<input class="toolbar_default" type="button" value="查" onclick="helpSearch(this);">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input style="height:30px;width:30px" type="checkbox" id="approveResultId3" name="approveResult" value="Y">
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showSameId3" name="showSame04" size="35" value="">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input size="30" type="text" id="remarkId3" name="remark" class="" value="">
						<input type="button" class="toolbar_form" id="btnExampleForRemarkId3" name="btnExampleForRemarkId3" value="片語" />
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showPreSearchId3" name="showPreSearch04" size="35" value="">
					</td>
				</tr>
				<tr>
					<td class="td_form_white" style="text-align: center">
						<input class="field_Q" type="hidden" name="_rowsOfCompanyName" value="4">
						<input class="field_RO" readonly type="text" size="2" style="text-align: center" id="seqNoId4" name="seqNo" value="">
					</td>
					<td class="td_form_white">
						<input class="field_Q" style="font-family: '新細明體;細明體', '微軟正黑體', Verdana, Arial, Helvetica, sans-serif;" type="text" size="30" id="companyNameId4" name="companyName" value="">
						<input class="field" type="text" size="15" value="">
						<input class="toolbar_default" type="button" value="查" onclick="helpSearch(this);">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input style="height:30px;width:30px" type="checkbox" id="approveResultId4" name="approveResult" value="Y">
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showSameId4" name="showSame05" size="35" value="">
					</td>
					<td class="td_form_white" style="text-align: center">
						<input size="30" type="text" id="remarkId4" name="remark" class="" value="">
						<input type="button" class="toolbar_form" id="btnExampleForRemarkId4" name="btnExampleForRemarkId4" value="片語" />
					</td>
					<td class="td_form_white">
						<input class="field" type="text" id="showPreSearchId4" name="showPreSearch05" size="35" value="">
					</td>
				</tr>
				<tr>
					<td style="text-align: center; background-color: #F3EFEF;">
						備註<br/>紀錄
					</td>
					<td style="text-align: center; background-color: #F3EFEF;">
						<textarea id="sync-remark1" name="sync-remark1" cols="24" rows="7" class="content"></textarea>
						<input type="button" class="toolbar_form" id="btnExampleForSync-remark1" name="btnExampleForSync-remark1" value="片語" style="vertical-align: top;"/>
					</td>
					<td style="background-color: #F3EFEF;text-align: center; font-size: 14px;"><input style="height:30px;width:30px" onclick="approveResultAllNoClick(this);" type="checkbox" value="true" name="approveResultAllNo"><br/>不予核准</td>
					<td style="background-color: #F3EFEF;" colspan="2">
						<input type="checkbox" id="otherReason" name="otherReason" value="Y"><span style="font-size: 14px">其他(非屬公司法第18條同名公司之原因)</span><br/>
            			<textarea id="sync-approveRemark" name="sync-approveRemark" cols="30" rows="6" class="content"></textarea>
            			<input type="button" class="toolbar_form" id="btnExampleForSync-approveRemark" name="btnExampleForSync-approveRemark" value="片語" style="vertical-align: top;"/>
					</td>
				</tr>
			</TABLE>
			<TABLE class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: right;width:100px;">禁用名詞:</td>
					<td class="td_form_white" style="width:150px;">
						<select class="td_form_white" id="cedbc004s">
						<%=View.getOption("select seq_no as CODE, ban_word as NAME from cedbc004","002") %>
						</select>
					</td>
					<td class="td_form" style="text-align: right;width:100px;">國家名稱:</td>
					<td class="td_form_white" style="width:150px;">
						<select class="td_form_white" id="cedbc053s">
						<%=View.getOption("select country as CODE, country as NAME from cedbc053","中華民國") %>
						</select>
					</td>
					<td class="td_form_white">
						<input type="button" class="toolbar_default" id="synonym" value="同義詞" />
					</td>
				</tr>
			</TABLE>
		</div>
		<div id="fragment-3" style="display:none;">
			<TABLE id="cedb1002s" class="table_form" width="100%" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: center">&nbsp;</td>
					<td class="td_form" style="text-align: left" colspan="6">
						<input class="toolbar_default" type="button"  id="delBuItem" value="刪除營業項目">
						<input class="toolbar_default" type="button"  id="buItemSelectAll" value="全部選取">
						<input class="toolbar_default" id="buItemUnSelectAll" type="button" value="取消全選">
						<input class="toolbar_default" type="button" value="除許可.." id="zz9999">
					</td>
				</tr>
				<tr>
					<td class="td_form" style="text-align: center;width:50px;padding-top:50px;" rowspan="999">
					<input type="image" src="../../images/pre/btn_bi_query.gif" alt="所營項目輔助查詢"
						id="btnItemList" name="btnItemList"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_01.jsp', 'pre3001_01','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_import.gif" alt="匯入營業項目"
						id="btnImportCeItem" name="btnImportCeItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_02.jsp', 'pre3001_02','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					<br/>
					<input type="image" src="../../images/pre/btn_bi_select.gif" alt="線上申辦所營項目"
						id="btnImportEicmItem" name="btnImportEicmItem"
						onclick="javascript:closeReturnWindow();returnWindow=window.open('pre3001_03.jsp', 'pre3001_03','width=800,height=600,scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0');return false;" />
					</td>
					<td class="td_form" style="text-align:center;width:40px;">選取</td>
					<td class="td_form" style="text-align:center;width:60px;">序號</td>
					<td class="td_form" style="text-align:center;width:100px;">代碼</td>
					<td class="td_form" style="text-align:center;width:400px;">營業項目</td>
					<td class="td_form" style="text-align:left;width:50px;"><input class="toolbar_default" type="button"  value="  +  "></td>
					<td class="td_form" style="text-align:center">檢視訊息</td>
				</tr>
				
			</table>
		</div>
		<div id="fragment-4" style="display:none;">
			<TABLE id="cedb1010s" class="table_form" width="100%" cellpadding="2" cellspacing="0">
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">案件處理狀況</td>
					<td class="td_form" style="text-align: left">案件處理時間</td>
					<td class="td_form" style="text-align: left">處理人員</td>
					<td class="td_form" style="text-align: left">工作日數</td>
				</tr>
			</TABLE>
		</div>
	</div>
	
	<!-- QA10906100033 已結案案件, 選擇是否同步至一站式 -->
	<div id="dialog-chooseSyncOss" class="ui-widget">
		<DIV id="chooseSyncOssDiv">
			<!-- insert here -->
		</DIV>
	</div>
	
<input type="hidden" id="currentPrefixNo" name="currentPrefixNo" value="<%=obj.getCurrentPrefixNo()%>">
<input type="hidden" id="hiddenPrefixNos" name="hiddenPrefixNos" value="<%=obj.getHiddenPrefixNos()%>">	
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="resetCloseDateFlag" name="resetCloseDateFlag" value="">
<input type="hidden" id="focus" name="focus" value="">
<input type="hidden" id="functionName" name="functionName" value="approve">
<input type="hidden" id="json" name="json" value="">
<input type="hidden" id="assignPrefixNo" name="assignPrefixNo" value="<%=obj.getAssignPrefixNo()%>">
<input type="hidden" id="autoApprove" name="autoApprove" value="false">
<input type="hidden" id="prefixStatus" name="prefixStatus" value="">
<input type="hidden" id="shortcut" name="shortcut" value="<%=obj.getShortcut()%>" />
<input type="hidden" id="cmpySetupDate" name="cmpySetupDate" value="">
<input type="hidden" id="extendMark" name="extendMark" >
<input type="hidden" id="extendReason" name="extendReason">
<input type="hidden" id="extendOther" name="extendOther">
<input type="hidden" id="withdrawCompanyName" name="withdrawCompanyName">
<input type="hidden" id="chooseSyncOss" name="chooseSyncOss">

</form>

	<div id="content">
    	<iframe width="100%" height="100%" src="../html/3001.html" frameborder="0" scrolling="no" style="overflow: none;"/>
    </div>		

</body>
		
</html>

<%@ include file="../../home/<USER>" %>