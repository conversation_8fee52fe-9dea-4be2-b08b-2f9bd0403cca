<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*"%>
<%@ page import="com.kangdainfo.moea.bo.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE1003"%>


<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	Gson gson = new GsonBuilder().create();
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL("SELECT CODE_NAME AS codeName FROM SYSTEM_CODE WHERE CODE_KIND = 9 ORDER BY ID");
	List datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	out.write(gson.toJson(datas));
} catch (Exception e) {
	e.printStackTrace();
}
%>