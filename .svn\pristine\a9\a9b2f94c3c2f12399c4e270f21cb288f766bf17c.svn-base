package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1028;

public class Cedb1028Dao extends BaseDaoJdbc implements RowMapper<Cedb1028> {

	private static final String SQL_find = "SELECT * FROM CEDB1028 ";
	
	public Cedb1028 findByBanNo(String banNo) {
		if("".equals(Common.get(banNo)))	return null;
		SQLJob sqljob = new SQLJob(SQL_find + "WHERE BAN_NO = ?");
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		
		List<Cedb1028> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : (Cedb1028)list.get(0);
	}
	
	public Cedb1028 findByReceiveNo(String receiveNo) {
		if("".equals(Common.get(receiveNo)))	return null;
		SQLJob sqljob = new SQLJob(SQL_find + "WHERE RECEIVE_NO = ?");
		sqljob.addParameter(receiveNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		
		List<Cedb1028> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : (Cedb1028)list.get(0);
	}
	
	public Cedb1028 update(Cedb1028 obj) {
		if(obj == null || "".equals(Common.get(obj.getBanNo())))	return null;
		Cedb1028 t = findByBanNo(obj.getBanNo());
		if(t == null)		return null;
		
		SQLJob sqljob = new SQLJob("UPDATE CEDB1028 SET ");
		sqljob.appendSQL("RECEIVE_NO = ?, ");
		sqljob.appendSQL("CLEAR_DATE = ?, ");
		sqljob.appendSQL("CLEAR_UNIT = ?, ");
		sqljob.appendSQL("COMPANY_NAME = ?, ");
		sqljob.appendSQL("STATUS_CODE = ?, ");
		sqljob.appendSQL("REG_DATE = ?, ");
		sqljob.appendSQL("CLEAR_WORD = ?, ");
		sqljob.appendSQL("CLEAR_NO = ?, ");
		sqljob.appendSQL("REMARK = ? ");
		sqljob.appendSQL("WHERE BAN_NO = ? ");
		sqljob.addParameter(obj.getReceiveNo());
		sqljob.addParameter(obj.getClearDate());
		sqljob.addParameter(obj.getClearUnit());
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addParameter(obj.getStatusCode());
		sqljob.addParameter(obj.getRegDate());
		sqljob.addParameter(obj.getClearWord());
		sqljob.addParameter(obj.getClearNo());
		sqljob.addParameter(obj.getRemark());
		sqljob.addParameter(obj.getBanNo());
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR
				});
		return findByBanNo(obj.getBanNo());
	}
	
	public Cedb1028 insert(Cedb1028 obj) {
		if(obj == null || "".equals(Common.get(obj.getBanNo())))	return null;
		Cedb1028 t = findByBanNo(obj.getBanNo());
		if(t != null)		return null;
		
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1028 (");
		sqljob.appendSQL("BAN_NO, ");
		sqljob.appendSQL("RECEIVE_NO, ");
		sqljob.appendSQL("CLEAR_DATE, ");
		sqljob.appendSQL("CLEAR_UNIT, ");
		sqljob.appendSQL("COMPANY_NAME, ");
		sqljob.appendSQL("STATUS_CODE, ");
		sqljob.appendSQL("REG_DATE, ");
		sqljob.appendSQL("CLEAR_WORD, ");
		sqljob.appendSQL("CLEAR_NO, ");
		sqljob.appendSQL("REMARK ");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?,?,?,?)");
		sqljob.addParameter(obj.getBanNo());
		sqljob.addParameter(obj.getReceiveNo());
		sqljob.addParameter(obj.getClearDate());
		sqljob.addParameter(obj.getClearUnit());
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addParameter(obj.getStatusCode());
		sqljob.addParameter(obj.getRegDate());
		sqljob.addParameter(obj.getClearWord());
		sqljob.addParameter(obj.getClearNo());
		sqljob.addParameter(obj.getRemark());
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR
				});
		return findByBanNo(obj.getBanNo());
	}
	
	public void delete(String banNo) {
		if("".equals(Common.get(banNo)))	return;
		Cedb1028 t = findByBanNo(banNo);
		if(t == null)		return;
		
		SQLJob sqljob = new SQLJob("DELETE CEDB1028 WHERE BAN_NO = ? ");
		sqljob.addParameter(banNo);
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	@Override
	public Cedb1028 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1028 obj = null;
		if(null!=rs) {
			obj = new Cedb1028();
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setReceiveNo(rs.getString("RECEIVE_NO"));
			obj.setClearDate(rs.getString("CLEAR_DATE"));
			obj.setClearUnit(rs.getString("CLEAR_UNIT"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setStatusCode(rs.getString("STATUS_CODE"));
			obj.setRegDate(rs.getString("REG_DATE"));
			obj.setClearWord(rs.getString("CLEAR_WORD"));
			obj.setClearNo(rs.getString("CLEAR_NO"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}
