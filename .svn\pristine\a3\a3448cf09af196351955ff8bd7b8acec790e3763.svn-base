package com.kangdainfo.tcfi.scheduling;

import java.io.File;
import java.io.IOException;
import java.util.Calendar;
import java.util.HashSet;
import java.util.Set;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.CommonCompressUtils;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.io.CommonFileUtils;

/**
 * 排程(PRE0008)
 * 壓縮索引備份檔
 */
public class Pre0008QuartzJobBean extends BaseQuartzJobBean {
	
	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		if(log.isInfoEnabled()) log.info("[START]"+getDateTime());
		String indexPath = getIndexPath();
		if(null!=indexPath && !"".equals(indexPath)) {
			File indexFolder = new File(indexPath);
			if(null!=indexFolder) {
				File root = indexFolder.getParentFile();
				compress(root);
				clean(root);
				cleanZipFile(root);
			}
		}
		if(log.isInfoEnabled()) log.info("[END]"+getDateTime());
	}
	
	private void compress(File root) {
		long lastWeekTime = getLastWeekTime();
		if(null!=root && root.isDirectory()) {
			for(File f : root.listFiles()) {
				if( f.isDirectory() ) {
					if( f.getName().startsWith("diIndex_") && !checkZipFileExist(f) ) {
						if( f.lastModified() < lastWeekTime ) {
							CommonCompressUtils.targzip(f);
						}
					}
				}
			}
		}
	}
	
	private boolean checkZipFileExist(File inputFile) {
		boolean isExist = false;
		try {
			String fileName = inputFile.getName();
			String tarFileName = fileName+".tar.gz";
			if( fileName.indexOf(".") > -1 )
				tarFileName = fileName.substring(0, fileName.lastIndexOf("."))+".tar.gz";
			File f = new File(inputFile.getParentFile(), tarFileName);
			if( f.exists() )
				isExist = true;
		} catch (Exception e) {
		}
		return isExist;
	}

	private void clean(File root) {
		Set<String> fileNames = new HashSet<String>();
		Set<String> folderNames = new HashSet<String>();
		if(null!=root && root.isDirectory()) {
			for(File f : root.listFiles()) {
				if( f.isFile() ) {
					fileNames.add(f.getName().substring(0,f.getName().indexOf(".")));
				} else if( f.isDirectory() ) {
					folderNames.add(f.getAbsolutePath());
				}
			}
		}

		for(String folderName : folderNames) {
			for(String fileName : fileNames) {
				if( folderName.indexOf(fileName) > -1 ) {
					try {
						if(log.isInfoEnabled()) log.info("[DELETE]folder:"+folderName);
						CommonFileUtils.deleteDirectory(new File(folderName));
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
	}

	private void cleanZipFile(File root) {
		long lastMonthTime = getLastMonthTime();
		Set<String> fileNames = new HashSet<String>();
		if(null!=root && root.isDirectory()) {
			for(File f : root.listFiles()) {
				if( f.isFile() ) {
					if( f.getName().startsWith("diIndex_") ) {
						if( f.lastModified() < lastMonthTime ) {
							fileNames.add(f.getAbsolutePath());
						}
					}
				}
			}
		}
		for(String fileName : fileNames) {
			try {
				if(log.isInfoEnabled()) log.info("[DELETE]file:"+fileName);
				CommonFileUtils.forceDelete(new File(fileName));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	private long getLastWeekTime() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DATE, -7);
		return cal.getTimeInMillis();
	}
	
	private long getLastMonthTime() {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, -1);
		return cal.getTimeInMillis();
	}

	private String getIndexPath() {
		String result = null;
		SystemCode setting = null;
		if(System.getProperty("os.name").toLowerCase().indexOf("win") >= 0)
			setting = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, "IndexPathWindows");
		else 
			setting = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, "IndexPathCentOS");
		if(null!=setting) {
			result = setting.getCodeParam2();
		}
		return result;
	}

}