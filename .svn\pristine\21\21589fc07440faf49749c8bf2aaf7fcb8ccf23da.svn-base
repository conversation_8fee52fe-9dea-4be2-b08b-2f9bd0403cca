package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2013;

public class Cedb2013Dao extends BaseDaoJdbc implements RowMapper<Cedb2013> {

	private static String sql_findByBanNo = "SELECT * FROM CEDB2013";
	public List<Cedb2013> findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.appendSQLCondition(" BAN_NO = ? ");
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	@Override
	public Cedb2013 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb2013 obj = null;
		if(null!=rs) {
			obj = new Cedb2013();
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setManagerName(Common.get(rs.getString("MANAGER_NAME")));
			obj.setBranchNo(Common.get(rs.getString("BRANCH_NO")));
			obj.setManagerForName(Common.get(rs.getString("MANAGER_FOR_NAME")));
			obj.setIdNo(Common.get(rs.getString("ID_NO")));
			obj.setIdCode(Common.get(rs.getString("ID_CODE")));
			obj.setArriveDate(Common.get(rs.getString("ARRIVE_DATE")));
			obj.setManagerZoneCode(Common.get(rs.getString("MANAGER_ZONE_CODE")));
			obj.setNativeCode(Common.get(rs.getString("NATIVE_CODE")));
			obj.setAddress(Common.get(rs.getString("ADDRESS")));
			obj.setApproveDate(Common.get(rs.getString("APPROVE_DATE")));
			obj.setInvestAmt(Common.get(rs.getString("INVEST_AMT")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateUser(Common.get(rs.getString("UPDATE_USER")));
		}
		return obj;
	}

}