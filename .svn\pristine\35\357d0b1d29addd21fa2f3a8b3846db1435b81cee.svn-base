package com.kangdainfo.tcfi.view.pre;

/*
程式目的：有限合夥基本資料查詢
程式代號：PRE3013
撰寫日期：105.10.12
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;

public class PRE3013 extends SuperBean {

	//查詢條件
	/** 統一編號 */
	private String q_banNo;
	/** 公司名稱 */
	private String q_companyName;
	/** 預查編號 */
	private String q_prefixNo;
	/** 特取名稱 */
	private String q_specialName;
	/** 負責人姓名 */
	private String q_respName;
	/** 負責人身分ID */
	private String q_respIdNo;

	/** 勾選要顯示的明細 */
	private String[] banNos;
	/** 功能控制(若comefrom=PRE1001, 則顯示免繳註記維護相關按鈕) */
	private String comefrom;
	/** 目前顯示的統編 */
	private String currBanNo;
	/** 輸入免繳註記時的預查編號 */
	private String remarkPrefixNo;

	// ---------------------------- getters and setters -----------------------------------------------
	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {q_banNo = checkSet(s);}
	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {q_companyName = checkSet(s);}
	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	public String getQ_specialName() {return checkGet(q_specialName);}
	public void setQ_specialName(String s) {q_specialName = checkSet(s);}
	public String getComefrom() {return checkGet(comefrom);}
	public void setComefrom(String s) { comefrom = checkSet(s);}
	public String getQ_respName() {return checkGet(q_respName);}
	public void setQ_respName(String s) {this.q_respName = checkSet(s);}
	public String getQ_respIdNo() {return checkGet(q_respIdNo);}
	public void setQ_respIdNo(String s) {this.q_respIdNo = checkSet(s);}
	public String[] getBanNos() {return banNos;}
	public void setBanNos(String[] banNos) {this.banNos = banNos;}
	public String getRemarkPrefixNo() {return checkGet(remarkPrefixNo);}
	public void setRemarkPrefixNo(String s) {this.remarkPrefixNo = checkSet(s);}
	public String getCurrBanNo() {return checkGet(currBanNo);}
	public void setCurrBanNo(String s) {this.currBanNo = checkSet(s);}

	@SuppressWarnings("unchecked")
	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> dataList = new ArrayList<String[]>();	
		try {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" A.BAN_NO");
			sqljob.appendSQL(" ,(select max(reserve_no) from lms.lmsm_case_rcv b where b.ban_no = a.ban_no ) ");
			sqljob.appendSQL(" ,(Select");
			sqljob.appendSQL(" NAME FROM lms.LMSM_DIRECTOR h where rownum=1 and h.Ban_No=a.Ban_No and h.parpk=a.tbpk and h.duty_code = '01' ");
			sqljob.appendSQL(") AS RESP_NAME");
			sqljob.appendSQL(",A.BUSS_NAME");
			sqljob.appendSQL(",A.CURR_STATUS");
			sqljob.appendSQL("FROM LMS.LMSM_BUSS_MAIN A");
			sqljob.appendSQL("WHERE (A.Is_Newest='Y' or A.Is_Newest='X')");
			if(!"".equals(getQ_banNo())) {
				sqljob.appendSQL("AND A.BAN_NO=?");
				sqljob.addParameter(getQ_banNo());
				sqljob.addSqltypes(oracle.jdbc.OracleTypes.FIXED_CHAR);
			}
			if(!"".equals(getQ_companyName())) {
				sqljob.appendSQL("AND A.BUSS_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%"));
			}
			if(!"".equals(getQ_prefixNo())) {
				sqljob.appendSQL("AND A.BAN_NO IN (select ban_no from lms.lmsm_case_rcv b where a.ban_no = b.ban_no and reserve_no = ? and rownum = 1 )");
				sqljob.addParameter(getQ_prefixNo());
				sqljob.addSqltypes(oracle.jdbc.OracleTypes.FIXED_CHAR);
			}
			if(!"".equals(getQ_specialName())) {
				sqljob.appendSQL("AND A.PART_NAME=?");
				sqljob.addParameter(getQ_specialName());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if(!"".equals(getQ_respName())) {
				sqljob.appendSQL("AND A.BAN_NO IN ( Select BAN_NO From lms.LMSM_DIRECTOR h where rownum=1 and h.Ban_No=A.BAN_NO and h.duty_code = '01' AND NAME = ? ) ");
				sqljob.addParameter(getQ_respName());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			if(!"".equals(getQ_respIdNo())) {
				sqljob.appendSQL("AND A.BAN_NO IN ( Select BAN_NO From lms.LMSM_DIRECTOR h where rownum=1 and h.Ban_No=A.BAN_NO and h.duty_code = '01' AND ID_NO = ? ) ");
				sqljob.addParameter(getQ_respIdNo());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
			}
			sqljob.appendSQL("ORDER BY A.CURR_STATUS, A.BAN_NO");

			if(logger.isInfoEnabled()) logger.info(sqljob);
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getIcmsGeneralQueryDao().queryForList(sqljob);
			if(logger.isInfoEnabled()) logger.info("query complete");
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<?> it = objList.iterator();
				Map<String,Object> o;
				String cmpyStatus, cmpyStatusName;
				String[] rowArray = new String[6];
				while (it.hasNext()) {
					o = (Map<String,Object>) it.next();
					System.out.println(o.get("RESP_NAME"));
					cmpyStatus = Common.get(o.get("CURR_STATUS"));
					
					//公司狀態中文說明
					if("01".equals(cmpyStatus)) {
						cmpyStatusName = ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(cmpyStatus);
					} else {
						cmpyStatusName = ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(cmpyStatus);
					}

					rowArray = new String[6];
					rowArray[0] = Common.get(o.get("BAN_NO"));
					rowArray[1] = Common.get(o.get("RESERVE_NO"));
					rowArray[2] = Common.get(o.get("RESP_NAME"));
					rowArray[3] = Common.get(o.get("BUSS_NAME"));
					rowArray[4] = Common.get("");
					rowArray[5] = Common.get(cmpyStatusName);
					System.out.println(rowArray[0]+","+rowArray[1]+","+rowArray[2]+","+rowArray[3]);
					dataList.add(rowArray);	
				}
			} else {
				throw new MoeaException ( "查無資料，請變更查詢條件" ) ;	
			}
		} catch ( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		}
		return dataList;
	}

	/** 恢復免繳資格 */
	public void recoverNoNeedPay() {
		try {
			ServiceGetter.getInstance().getPre3008Service().doRecover(currBanNo);
		} catch(Exception e) {
			setErrorMsg("執行失敗!");
		}
	}

	/** 註銷免繳資格 */
	public void cancelNoNeedPay() {
		try {
			ServiceGetter.getInstance().getNoPayMarkService().updateCancel(currBanNo);
		} catch(Exception e) {
			setErrorMsg("執行失敗!");
		}
	}
	
	/** 免繳註記 */
	public void saveRemark() {
		try {
			ServiceGetter.getInstance().getNoPayMarkService().updatePrefixNo(currBanNo,remarkPrefixNo);
		} catch(Exception e) {
			setErrorMsg("執行失敗!");
		}
	}

	/** 列印 */
	public File doPrintPdf() throws Exception {
		File report = null ;  
		try {
		    report = ServiceGetter.getInstance().getPre3013Service().generateRptPdf(currBanNo);
		    setErrorMsg("報表印製成功");
		    return report;
		} catch (MoeaException e) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null;
		}
	}

	public void doCreate() throws Exception{}
	public void doUpdate() throws Exception{}
	public void doDelete() throws Exception{}
	public Object doQueryOne() throws Exception{return null;}
	
}