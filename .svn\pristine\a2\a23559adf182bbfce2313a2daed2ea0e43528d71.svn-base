<!-- 
程式目的：已審核未結案清單
程式代號：pre4018
程式日期：1030623
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4018">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4018" />
</jsp:include>
<%

if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} // end if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4018.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
else {
	// do nothing
} // end else
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init(){
	if ( form1.state.value == "queryAllSuccess" )
		document.getElementById("listContainer").style.display = 'block';
	else
	  document.getElementById("listContainer").style.display = 'none';
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_dateStart,"收文日期起");
	alertStr += checkEmpty(form1.q_dateEnd,"收文日期迄");
	alertStr += checkDate(form1.q_dateStart,"收文日期起") ;
	alertStr += checkDate(form1.q_dateEnd,"收文日期迄") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;	
				var target = 'PRE4018_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';	
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doClear":
				form1.q_dateStart.value = "";
				form1.q_dateEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck() {
	if (checkField()) {
		whatButtonFireEvent("doPrintPdf");
	} // if
}

function queryOne(prefixNo) {
	var prop="";
	var width = window.screen.width/2;
	var height = window.screen.height/2;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	var url = getVirtualPath() + "tcfi/pre/pre3001_00.jsp?prefixNo="+prefixNo+"&hiddenPrefixNos="+prefixNo;
	window.open(url,'pre3001',prop).focus();
}

function keyDown() {
	if (event.keyCode==13) {
		$("#doQueryAll").click();
	}
}
</script>
</head>
<!-- Form area -->

<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField();" >
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4018'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="10%">分文日期：</td>
			<td class="td_form_white">
				<%=View.getPopCalendar("field_Q","q_dateStart",obj.getQ_dateStart()) %>~<%=View.getPopCalendar("field_Q","q_dateEnd",obj.getQ_dateEnd()) %>
				&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
	          	&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
			</td>
			<td class="td_form_white" width="30%" align="right">
				<c:import url="../common/shortcut.jsp">
					<c:param name="functions" value='PRE4001,PRE3008,PRE3013'/>
				</c:import>
	    	</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td>
	<div id="listContainer" height = "200">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" >NO.</th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查名稱</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">預查種類</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">申請人</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">承辦工作天數</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">分文日期</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">承辦人</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">展期原因</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">審核時數</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = {true,false,false,false,false,false,false,false,false};
  			boolean displayArray[] = {true,true,true,true,true,true,true,true,true};
  			String[] alignArray = {"center", "left","center","center","center","center","center","center","center"};
  			out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",true));
  			%>
  		</tbody>
	</table>
	</div>
</td></tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
	<tr><td style="text-align:center;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		<!-- 新增按鈕區 -->
	</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>