package com.kangdainfo.tcfi.model.eedb.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * (EEDB3000)
 *
 */
public class Eedb3000 extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String telixNo;
	private String seqNo;
	private String companyName;
	private String approveResult;
	private String part1;
	private String part2;
	private String part3;
	private String part4;
	private String part5;
	private String part6;
	private String remark;


	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getSeqNo() {
		return seqNo;
	}
	public void setSeqNo(String seqNo) {
		this.seqNo = seqNo;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getApproveResult() {
		return approveResult;
	}
	public void setApproveResult(String approveResult) {
		this.approveResult = approveResult;
	}
	public String getPart1() {
		return part1;
	}
	public void setPart1(String part1) {
		this.part1 = part1;
	}
	public String getPart2() {
		return part2;
	}
	public void setPart2(String part2) {
		this.part2 = part2;
	}
	public String getPart3() {
		return part3;
	}
	public void setPart3(String part3) {
		this.part3 = part3;
	}
	public String getPart4() {
		return part4;
	}
	public void setPart4(String part4) {
		this.part4 = part4;
	}
	public String getPart5() {
		return part5;
	}
	public void setPart5(String part5) {
		this.part5 = part5;
	}
	public String getPart6() {
		return part6;
	}
	public void setPart6(String part6) {
		this.part6 = part6;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}