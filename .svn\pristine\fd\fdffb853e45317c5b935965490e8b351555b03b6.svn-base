<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4006"%>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4006" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	String checkResult = obj.checkForJsp();
	if (null!=checkResult && !"".equals(checkResult)) {
		out.write(checkResult);
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>