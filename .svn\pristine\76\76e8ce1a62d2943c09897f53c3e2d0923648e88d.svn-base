<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report1" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="10" uuid="361ac023-a352-48af-a0c0-f846fbd40add">
	<property name="ireport.zoom" value="1.7715610000000022"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<topPen lineWidth="1.0" lineColor="#000000"/>
			<bottomPen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="63cf7a2d-fbd4-4f95-8a55-2b0f987b2284">
		<field name="seqNo" class="java.lang.String"/>
		<field name="companyName" class="java.lang.String"/>
	</subDataset>
	<subDataset name="Table Dataset 2" uuid="a90fc984-36bc-46be-85f0-48130c921439">
		<field name="seqNo" class="java.lang.String"/>
		<field name="busiItemNo" class="java.lang.String"/>
		<field name="busiItem" class="java.lang.String"/>
	</subDataset>
	<subDataset name="Table Dataset 3" uuid="f27b3366-11d0-4d39-af50-e84f9798d75c"/>
	<subDataset name="Table Dataset 4" uuid="d6b91162-c359-4a51-8fa4-231051717dcb"/>
	<parameter name="approveString" class="java.lang.String"/>
	<parameter name="constantString" class="java.lang.String"/>
	<parameter name="nameOrOrgCorpName" class="java.lang.String"/>
	<parameter name="idOrOrgCorpNo" class="java.lang.String"/>
	<parameter name="addrOrCompanyAddr" class="java.lang.String"/>
	<parameter name="attorAddr" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\eclipse\\workspace\\prefix\\WebContent\\tcfi\\report\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUB_REPORT" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="LIST" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="nameOrOrgCorpNameSUB" class="java.lang.String"/>
	<parameter name="idOrOrgCorpNoSUB" class="java.lang.String"/>
	<parameter name="parameter1" class="java.lang.String"/>
	<parameter name="SUB_REPORT_APPROVESTRING" class="java.lang.String"/>
	<parameter name="LIST_APPROVESTRING" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
	<parameter name="parameter2" class="java.lang.String"/>
	<parameter name="SUB_REPORT_MAINREPORT" class="java.lang.String"/>
	<parameter name="LIST_MAINREPORT" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="waiting" class="java.lang.String"/>
	<field name="contactGetKind" class="java.lang.String"/>
	<field name="applyType" class="java.lang.String"/>
	<field name="docType" class="java.lang.String"/>
	<field name="caseCode" class="java.lang.String"/>
	<field name="prefixNo" class="java.lang.String"/>
	<field name="banNo" class="java.lang.String"/>
	<field name="zoneCode" class="java.lang.String"/>
	<field name="companyName" class="java.lang.String"/>
	<field name="telixNo" class="java.lang.String"/>
	<field name="processorName" class="java.lang.String"/>
	<field name="receiveDate" class="java.lang.String"/>
	<field name="receiveTime" class="java.lang.String"/>
	<field name="applyName" class="java.lang.String"/>
	<field name="applyId" class="java.lang.String"/>
	<field name="attorName" class="java.lang.String"/>
	<field name="attorNo" class="java.lang.String"/>
	<field name="attorTel" class="java.lang.String"/>
	<field name="attorAddr" class="java.lang.String"/>
	<field name="applyCompanyName" class="java.lang.String"/>
	<field name="seqNo" class="java.lang.String"/>
	<field name="companyAddr" class="java.lang.String"/>
	<field name="attorAreaCode" class="java.lang.String"/>
	<field name="applyCompanyNames" class="java.util.List"/>
	<field name="bussinessItems" class="java.util.List"/>
	<field name="contactName" class="java.lang.String"/>
	<field name="contactAreaCode" class="java.lang.String"/>
	<field name="contactTel" class="java.lang.String"/>
	<field name="contactAddr" class="java.lang.String"/>
	<field name="orgCorpNo" class="java.lang.String"/>
	<field name="orgCorpName" class="java.lang.String"/>
	<field name="originalflag" class="java.lang.String"/>
	<field name="idNo" class="java.lang.String"/>
	<field name="noteString" class="java.lang.String"/>
	<field name="isEicmEedbAlive" class="java.lang.String"/>
	<field name="reissue" class="java.lang.String"/>
	<field name="contactCel" class="java.lang.String"/>
	<field name="changeType" class="java.lang.String"/>
	<field name="setup" class="java.lang.Boolean"/>
	<field name="changeName" class="java.lang.Boolean"/>
	<field name="applyWay" class="java.lang.String"/>
	<field name="changeItem" class="java.lang.Boolean"/>
	<field name="approveResult" class="java.lang.String"/>
	<field name="bussinessItemsAfter5" class="java.lang.String"/>
	<field name="tempString" class="java.lang.String"/>
	<field name="extRemitEname" class="java.lang.String"/>
	<field name="takeMethod" class="java.lang.String"/>
	<group name="g1" isStartNewPage="true" minHeightToStartNewPage="1" keepTogether="true">
		<groupExpression><![CDATA[$F{prefixNo}]]></groupExpression>
		<groupHeader>
			<band height="101">
				<elementGroup/>
				<subreport>
					<reportElement uuid="76c7fa12-99df-4078-b023-80bd08247a33" isPrintRepeatedValues="false" x="0" y="0" width="554" height="100"/>
					<subreportParameter name="applyId">
						<subreportParameterExpression><![CDATA[$F{applyId}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="nameOrOrgCorpName">
						<subreportParameterExpression><![CDATA[$P{nameOrOrgCorpName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="idOrOrgCorpNo">
						<subreportParameterExpression><![CDATA[$P{idOrOrgCorpNo}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="companyAddr">
						<subreportParameterExpression><![CDATA[$F{companyAddr}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="addrOrCompanyAddr">
						<subreportParameterExpression><![CDATA[$P{addrOrCompanyAddr}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="attorTel">
						<subreportParameterExpression><![CDATA[$F{attorTel}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="contactCel">
						<subreportParameterExpression><![CDATA[$F{contactCel}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="attorAddr">
						<subreportParameterExpression><![CDATA[$P{attorAddr}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="changeName">
						<subreportParameterExpression><![CDATA[$F{changeName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="attorAddrF">
						<subreportParameterExpression><![CDATA[$F{attorAddr}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="contactTel">
						<subreportParameterExpression><![CDATA[$F{contactTel}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="companyName">
						<subreportParameterExpression><![CDATA[$F{companyName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="constantString">
						<subreportParameterExpression><![CDATA[$P{constantString}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="applyName">
						<subreportParameterExpression><![CDATA[$F{applyName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="attorName">
						<subreportParameterExpression><![CDATA[$F{attorName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="setup">
						<subreportParameterExpression><![CDATA[$F{setup}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="attorNo">
						<subreportParameterExpression><![CDATA[$F{attorNo}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="changeItem">
						<subreportParameterExpression><![CDATA[$F{changeItem}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$P{LIST_MAINREPORT}]]></dataSourceExpression>
					<subreportExpression><![CDATA[$P{SUB_REPORT_MAINREPORT}]]></subreportExpression>
				</subreport>
			</band>
			<band height="1"/>
		</groupHeader>
		<groupFooter>
			<band height="41" splitType="Stretch">
				<elementGroup>
					<subreport>
						<reportElement uuid="ffdab93f-048f-4c72-a865-c1294e9106e1" positionType="Float" x="1" y="10" width="554" height="31"/>
						<subreportParameter name="approveString">
							<subreportParameterExpression><![CDATA[$P{approveString}]]></subreportParameterExpression>
						</subreportParameter>
						<dataSourceExpression><![CDATA[$P{LIST_APPROVESTRING}]]></dataSourceExpression>
						<subreportExpression><![CDATA[$P{SUB_REPORT_APPROVESTRING}]]></subreportExpression>
					</subreport>
				</elementGroup>
			</band>
			<band height="53">
				<staticText>
					<reportElement uuid="9867e7fc-9e0c-46ac-b374-fd658898a596" positionType="Float" x="1" y="28" width="555" height="13"/>
					<textElement verticalAlignment="Top">
						<font fontName="標楷體" size="10" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
					</textElement>
					<text><![CDATA[       3. 對本處分如有不服，應於接到本處分書之次日起30日內繕具訴願書送由本部向行政院訴願。]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="196f4275-45f3-40d3-9803-ba840c3075ff" positionType="Float" x="1" y="16" width="555" height="13"/>
					<textElement verticalAlignment="Top">
						<font fontName="標楷體" size="10" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
					</textElement>
					<text><![CDATA[       2. 本預查申請案之代理人不以會計師、律師為限。]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="02c14308-0a06-4028-8faf-c6df967e4b58" positionType="Float" x="1" y="3" width="555" height="13"/>
					<textElement verticalAlignment="Top">
						<font fontName="標楷體" size="10" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
					</textElement>
					<text><![CDATA[備註 : 1. 預查申請案經核准者，自核准之日起算，其保留期限為6個月。]]></text>
				</staticText>
				<staticText>
					<reportElement uuid="c258d89b-7661-48b6-b7c8-5a8de3bf2d92" positionType="Float" x="1" y="40" width="555" height="13"/>
					<textElement verticalAlignment="Top">
						<font fontName="標楷體" size="10" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
					</textElement>
					<text><![CDATA[       4. 有限合夥之設立案，申請人為自然人者，請填戶籍地址。]]></text>
				</staticText>
			</band>
			<band height="72" splitType="Prevent">
				<subreport>
					<reportElement uuid="0cffd720-c86e-4207-9572-90f9c932cd35" positionType="Float" x="1" y="11" width="555" height="61"/>
					<subreportParameter name="applyId">
						<subreportParameterExpression><![CDATA[$F{applyId}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="applyName">
						<subreportParameterExpression><![CDATA[$F{applyName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="orgCorpName">
						<subreportParameterExpression><![CDATA[$F{orgCorpName}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="prefixNo">
						<subreportParameterExpression><![CDATA[$F{prefixNo}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="tempString">
						<subreportParameterExpression><![CDATA[$F{tempString}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="idOrOrgCorpNoSUB">
						<subreportParameterExpression><![CDATA[$P{idOrOrgCorpNoSUB}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="nameOrOrgCorpNameSUB">
						<subreportParameterExpression><![CDATA[$P{nameOrOrgCorpNameSUB}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="orgCorpNo">
						<subreportParameterExpression><![CDATA[$F{orgCorpNo}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="telixNo">
						<subreportParameterExpression><![CDATA[$F{telixNo}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[$P{LIST}]]></dataSourceExpression>
					<subreportExpression><![CDATA[$P{SUB_REPORT}]]></subreportExpression>
				</subreport>
				<break>
					<reportElement uuid="4e8043d9-f533-421a-a108-fb8de812368c" x="0" y="0" width="553" height="1"/>
				</break>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="78" splitType="Stretch">
			<staticText>
				<reportElement uuid="6243f675-4a62-41be-b30e-4bfafe53ff60" x="73" y="17" width="416" height="27"/>
				<textElement textAlignment="Center">
					<font fontName="標楷體" size="22" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[有限合夥名稱及所營事業登記預查申請表]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="dab8e747-52eb-4651-a5a5-fc93a6c6377e" mode="Transparent" x="370" y="58" width="64" height="20"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement uuid="46daea9e-d51a-4b51-9cfe-40fee6fceded" x="371" y="58" width="64" height="19"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contactGetKind}.equals("1") || $F{contactGetKind}.equals("3") ? "V" : ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="57da47ef-311f-4749-983a-ec1854e3712e" x="434" y="58" width="57" height="20"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[郵寄]]></text>
			</staticText>
			<textField>
				<reportElement uuid="55051d58-5119-48ae-b31b-f7d4661078dc" x="312" y="58" width="58" height="20"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{takeMethod}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="654b9c74-72ea-4798-8c4c-8cfb5ce3046a" x="491" y="58" width="64" height="20"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="8f0ff568-5c24-4f92-9420-64482866886f" x="1" y="58" width="41" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
				</textElement>
				<text><![CDATA[編號:]]></text>
			</staticText>
			<textField>
				<reportElement uuid="3654eadc-959e-4291-bf72-5ee37715cb95" x="42" y="58" width="75" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{prefixNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement uuid="6eb2fcc4-e95f-4857-bd0e-709ca22940f9" x="114" y="58" width="243" height="19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tempString}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement uuid="36f78480-3d3f-4f91-a485-c0457bb3b4fb" x="491" y="59" width="63" height="18"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{contactGetKind}.equals("2") ? "Ｖ" : ""]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="42" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement uuid="5d33cea1-b71c-4f78-bc05-00d823eeaffb" x="139" y="24" width="416" height="18" isRemoveLineWhenBlank="true"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement>
					<font fontName="標楷體" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{extRemitEname}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="d8d39e7e-80d4-4e24-a49c-18727eb0822a" x="0" y="6" width="555" height="18" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{extRemitEname}!=null]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="標楷體" size="12"/>
				</textElement>
				<text><![CDATA[自由填列事項(不納入預查審核項目)]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="0d29227b-7ec4-4b48-9040-a82ed028da58" x="0" y="24" width="139" height="18" isRemoveLineWhenBlank="true">
					<printWhenExpression><![CDATA[$F{extRemitEname}!=null]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement>
					<font fontName="標楷體" size="12"/>
				</textElement>
				<text><![CDATA[國外匯款使用英文名稱]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="9648b4f6-6066-4789-a899-cdbdfa55d4e4" x="436" y="24" width="120" height="18" isRemoveLineWhenBlank="true"/>
				<box>
					<pen lineWidth="0.0" lineStyle="Solid"/>
					<topPen lineWidth="0.0" lineStyle="Solid"/>
					<leftPen lineWidth="0.0" lineStyle="Solid"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid"/>
					<rightPen lineWidth="0.0" lineStyle="Solid"/>
				</box>
				<textElement>
					<font fontName="標楷體" size="12"/>
				</textElement>
				<text><![CDATA[(僅提供銀行開戶使用)]]></text>
			</staticText>
		</band>
		<band height="45">
			<componentElement>
				<reportElement uuid="ad475ed2-5ee0-4411-826e-63875ba1c523" key="table 1" style="table" positionType="Float" x="1" y="9" width="555" height="35"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 1" uuid="bbdfa463-9289-4cac-9505-90233a3a0572">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{applyCompanyNames})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="32" uuid="a16f3669-e1c8-49df-b13d-1636e2bd2f92">
						<jr:columnHeader style="table 3_TD" height="18" rowSpan="1">
							<staticText>
								<reportElement uuid="b1b3a755-abe7-4421-b17a-c03690e7b1b6" x="0" y="0" width="32" height="18"/>
								<box>
									<topPen lineWidth="1.75"/>
									<leftPen lineWidth="1.75"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<text><![CDATA[項次]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:columnFooter height="0" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
							</box>
						</jr:columnFooter>
						<jr:detailCell style="table 3_TD" height="18" rowSpan="1">
							<box>
								<leftPen lineWidth="1.75"/>
							</box>
							<textField>
								<reportElement uuid="864f2362-153f-4680-a853-71ac0f5c4729" x="0" y="0" width="32" height="18"/>
								<box>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center">
									<font fontName="標楷體" size="12"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{seqNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="523" uuid="ae28a4b8-f369-49f0-949c-d17399d45122">
						<jr:columnHeader style="table 3_TD" height="18" rowSpan="1">
							<staticText>
								<reportElement uuid="051080a0-0835-4a9e-b0c7-c1e41eba6e6a" x="0" y="0" width="523" height="18"/>
								<box>
									<pen lineWidth="2.0"/>
									<topPen lineWidth="1.75"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="1.75"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<text><![CDATA[預　　查　　之　　有　　限　　合　　夥　　名　　稱]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:columnFooter height="0" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
							</box>
						</jr:columnFooter>
						<jr:detailCell style="table 3_TD" height="18" rowSpan="1">
							<box>
								<pen lineWidth="1.75"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="1.75"/>
							</box>
							<textField>
								<reportElement uuid="54b110c0-405a-4522-a891-cb45acb42c3d" x="3" y="0" width="520" height="18"/>
								<box>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement>
									<font fontName="標楷體" size="12"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{companyName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
		<band height="59">
			<componentElement>
				<reportElement uuid="c8ce6d61-2342-49d9-bdda-07e009a54a60" key="table " style="table" positionType="Float" x="1" y="11" width="555" height="47"/>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="Table Dataset 2" uuid="45430569-b227-4189-91de-b4632155fbd6">
						<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{bussinessItems})]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="33" uuid="10a8fc87-b9ba-4295-b9ac-d89c55ac2bcd">
						<jr:columnHeader style="table 2_TD" height="19" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
								<leftPen lineWidth="1.75"/>
							</box>
							<staticText>
								<reportElement uuid="d5e65327-3d67-488d-a2f0-249073cf1582" x="0" y="0" width="33" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<text><![CDATA[項次]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:columnFooter height="0" rowSpan="1">
							<box>
								<pen lineWidth="1.75"/>
								<topPen lineWidth="1.75"/>
								<leftPen lineWidth="1.75"/>
								<bottomPen lineWidth="1.75"/>
								<rightPen lineWidth="1.75"/>
							</box>
						</jr:columnFooter>
						<jr:detailCell style="table 1_TD" height="21" rowSpan="1">
							<box>
								<leftPen lineWidth="1.75"/>
							</box>
							<textField>
								<reportElement uuid="eca64f2b-a5ec-4640-967f-ce28d2671b6f" x="0" y="0" width="33" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center">
									<font fontName="標楷體" size="12"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{seqNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="83" uuid="e4e429d8-c991-4580-8522-0ab440b0db25">
						<jr:columnHeader style="table 2_TD" height="19" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
							</box>
							<staticText>
								<reportElement uuid="dc325405-020f-45c4-9466-2f4707760bb3" x="0" y="1" width="83" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<text><![CDATA[營業項目代碼]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:columnFooter height="0" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
							</box>
						</jr:columnFooter>
						<jr:detailCell style="table 1_TD" height="21" rowSpan="1">
							<textField isBlankWhenNull="true">
								<reportElement uuid="dc07ac0a-2e93-4551-9b4e-3ddb1273f7c9" x="0" y="0" width="83" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center">
									<font fontName="標楷體" size="12"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{busiItemNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="439" uuid="45892354-e76a-4ad8-b162-02837f05841d">
						<jr:columnHeader height="19" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
								<rightPen lineWidth="1.75"/>
							</box>
							<staticText>
								<reportElement uuid="69c1671b-02d6-4c6b-8915-a59038b841ed" x="2" y="1" width="437" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<text><![CDATA[所  營  事  業  (  應  分  項  列  打  )]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:columnFooter height="0" rowSpan="1">
							<box>
								<topPen lineWidth="1.75"/>
							</box>
						</jr:columnFooter>
						<jr:detailCell style="table 1_TD" height="21" rowSpan="1">
							<box>
								<leftPen lineWidth="0.5"/>
								<rightPen lineWidth="1.75"/>
							</box>
							<textField isStretchWithOverflow="true">
								<reportElement uuid="20da7d01-bb3d-4401-8d84-c10ec2b7e038" stretchType="RelativeToTallestObject" x="2" y="0" width="437" height="18"/>
								<box>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement textAlignment="Left">
									<font fontName="標楷體" size="12" pdfFontName="kaiu.ttf" pdfEncoding="Identity-H"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{busiItem}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
