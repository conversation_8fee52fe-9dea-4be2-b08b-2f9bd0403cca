package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 線上申辦收辦記錄主檔(EEDB1000)
 *
 */
public class Eedb1000 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 申登機關代碼 */
	private String regUnitCode;
	/** 電子流水號 */
	private String telixNo;
	/** 預查編號 */
	private String prefixNo;
	/** 收文日期 */
	private String receiveDate;
	/** 收文時間 */
	private String receiveTime;
	/** 案由 */
	private String caseCode;
	/** 核准日期 */
	private String approveDate;
	/** 核准時間 */
	private String approveTime;
	/** 核准結果 */
	private String approveResult;
	/** 處理狀態 */
	private String processStatus;
	/** 保留期限 */
	private String reserveDate;
	/** 收文回覆郵件 */
	private String receiveMail;
	/** 核准回覆郵件 */
	private String approveMail;
	/** 來源 */
	private String comeFrom;

	public String getRegUnitCode() {return regUnitCode;}
	public void setRegUnitCode(String regUnitCode) {this.regUnitCode = regUnitCode;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String telixNo) {this.telixNo = telixNo;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String receiveDate) {this.receiveDate = receiveDate;}
	public String getReceiveTime() {return receiveTime;}
	public void setReceiveTime(String receiveTime) {this.receiveTime = receiveTime;}
	public String getCaseCode() {return caseCode;}
	public void setCaseCode(String caseCode) {this.caseCode = caseCode;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String approveDate) {this.approveDate = approveDate;}
	public String getApproveTime() {return approveTime;}
	public void setApproveTime(String approveTime) {this.approveTime = approveTime;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String approveResult) {this.approveResult = approveResult;}
	public String getProcessStatus() {return processStatus;}
	public void setProcessStatus(String processStatus) {this.processStatus = processStatus;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String reserveDate) {this.reserveDate = reserveDate;}
	public String getReceiveMail() {return receiveMail;}
	public void setReceiveMail(String receiveMail) {this.receiveMail = receiveMail;}
	public String getApproveMail() {return approveMail;}
	public void setApproveMail(String approveMail) {this.approveMail = approveMail;}
	public String getComeFrom() {return comeFrom;}
	public void setComeFrom(String comeFrom) {this.comeFrom = comeFrom;}

}