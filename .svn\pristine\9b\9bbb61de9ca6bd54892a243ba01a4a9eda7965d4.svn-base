package com.kangdainfo.tcfi.model.eedb.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eedb.bo.EedbV8000;

public class EedbV8000Dao extends BaseDaoJdbc implements RowMapper<EedbV8000> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB_V_8000 WHERE TELIX_NO = ?";
	public List<EedbV8000> findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<EedbV8000>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_findAll = "SELECT * FROM EEDB_V_8000";
	public List<EedbV8000> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<EedbV8000>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public EedbV8000 mapRow(ResultSet rs, int idx) throws SQLException {
		EedbV8000 obj = null;
		if(null!=rs) {
			obj = new EedbV8000();
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setTelixNo(rs.getString("CASE_CODE"));
			obj.setTelixNo(rs.getString("CASE_SHORTNAME"));
			obj.setTelixNo(rs.getString("UNIT_CODE"));
		}
		return obj;
	}

}