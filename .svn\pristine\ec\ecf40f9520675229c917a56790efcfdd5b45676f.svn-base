<!-- 
程式目的：當月發文統計資料查詢
程式代號：pre2006
程式日期：1030603
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->

<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2006" />
</jsp:include>

<%
if ( "update".equals(obj.getState()) ) {
	obj.update() ;
} // end if
else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE2006)obj.queryOne();
	// obj.queryAll() ;
} // end else if
else if ( "print".equals(obj.getState()) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE2006.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init(){
	if ( form1.state.value == "queryOneSuccess" )
		document.getElementById("formContainer1").style.display = '';
	else
	  document.getElementById("formContainer1").style.display = 'none';
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_yearMonth,"月份");
	alertStr += checkYYYMM(form1.q_yearMonth,"");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function doClear() {
	form1.q_yearMonth.value = "";
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("print") ;				
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doQueryOne":
				$('#state').val( "queryOne" ) ;
				break ;
			case "doUpdate":
				$('#state').val( "update" ) ;	
				break ;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
	
});

function keyDown() {
	if (event.keyCode == 13) {
		$("#doQueryOne").click();
	}
}
</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE2006'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%">  
		<tr>
			<td class="td_form" width="100px">查詢年月份：</td>
			<td class="td_form_white"> 
				<input class="field_Q" type="text" name="q_yearMonth" size="10" maxlength="5" value="<%=obj.getQ_yearMonth()%>" onKeyDown="keyDown()">
				<input class="toolbar_default" type="button" followPK="false" id="doRest" name="doReset" value="重新輸入" onClick="doClear();" >
				<input class="toolbar_default" type="submit" followPK="false" id="doQueryOne" name="doQueryOne" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>			
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>  
</td></tr>

<tr><td>
  <div id="formContainer1" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
  	<tr>
		<td class="td_form" style="text-align:center">案件種類：</td>
		<td class="td_form" style="text-align:center">案件數量：</td>
	</tr>
    <tr>
		<td class="td_form" style="text-align:center">一維條碼案件數：</td>
        <td class="td_form_white" style="text-align:center"> 
           <input class="field_RO" type="text" name="c1" size="10" maxlength="5" value="<%=obj.getC1()%>">
        </td>
	</tr>
	<tr>
		<td class="td_form" style="text-align:center">郵寄發文案件數：</td>
        <td class="td_form_white" style="text-align:center"> 
           <input class="field_RO" type="text" name="c2" size="10" maxlength="5" value="<%=obj.getC2()%>">
        </td>
	</tr>
	<tr>
		<td class="td_form" style="text-align:center">臨櫃自取案件數：</td>
        <td class="td_form_white" style="text-align:center"> 
           <input class="field_RO" type="text" name="c3" size="10" maxlength="5" value="<%=obj.getC3()%>">
        </td>
	</tr>
  </table>
  </div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:center;">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
<!-- 新增按鈕區 -->
</td></tr>
</table>
</td></tr>

</table>
</form>
</body>
</html>