package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.PostRecord;
import com.kangdainfo.tcfi.util.PrefixConstants;

/*
程式目的：郵寄掛號登錄及維護
程式代號：pre2003
撰寫日期：103.06.16
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE2008 extends SuperBean {
	private String q_prefixNo;
	private String cLetterNo;
	private String lLetterNo;
	private String postType;
	private String authLetterNo;
	
	private String q_caseNo;
	private String q_applyName;
	private String q_applyAddr;
	private String q_applyTel;
	private String q_companyName;
	private String q_receiveName;
	private String q_receiveAddr;
	
	
	
	// 不會顯示在畫面上但用的到的欄位
	private String sysTime;
	private String prefixNo;
	// 申請人資料
	private String applyName;
	private String applyAddr;
	private String applyTel;
	private String companyName;
	// 收件人資料
	private String receiveName;
	
	
	private String receiveAddr;
	// 申請案資料
	private String letterKind;
	private String letterNo;
	private String getKind;
	private String getKindRemark;
	
	private String approveResult;
	private String approveDate;
	private String approveTime;
	private String getDate;
	private String getTime;
	private String refundDate;   // 退費日期
	private String refundTime;   // 退費時間
	private String refundReason;  // 退費原因
	private String refundReasonOther;   //　其他退費原因
	private String caseHow;          // 處理方式
	private String caseHowOther;     // 其他處理方式
	
	private String total;
	private String now;
	
	static List<PostRecord> postRecordList = new ArrayList<PostRecord>();
	
	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {q_prefixNo = checkSet(s);}
	public String getCLetterNo() {return checkGet(cLetterNo);}
	public void setCLetterNo(String s) {cLetterNo = checkSet(s);}
	public String getLLetterNo() {return checkGet(lLetterNo);}
	public void setLLetterNo(String s) {lLetterNo = checkSet(s);}
	public String getPostType() {return checkGet(postType);}
	public void setPostType(String s) {postType = checkSet(s);}
	public String getAuthLetterNo() {return checkGet(authLetterNo);}
	public void setAuthLetterNo(String s) {authLetterNo = checkSet(s);}
	
	public String getQ_caseNo() {return checkGet(q_caseNo);}
	public void setQ_caseNo(String s) {q_caseNo = checkSet(s);}
	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {q_applyName = checkSet(s);}
	public String getQ_applyAddr() {return checkGet(q_applyAddr);}
	public void setQ_applyAddr(String s) {q_applyAddr = checkSet(s);}
	public String getQ_applyTel() {return checkGet(q_applyTel);}
	public void setQ_applyTel(String s) {q_applyTel = checkSet(s);}
	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {q_companyName = checkSet(s);}
	public String getQ_receiveName() {return checkGet(q_receiveName);}
	public void setQ_receiveName(String s) {q_receiveName = checkSet(s);}
	public String getQ_receiveAddr() {return checkGet(q_receiveAddr);}
	public void setQ_receiveAddr(String s) {q_receiveAddr = checkSet(s);}
	
	public String getSysTime() {return checkGet(sysTime);}
	public void setSysTime(String s) {sysTime = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	
	public String getApplyName() {return checkGet(applyName);}
	public void setApplyName(String s) {applyName = checkSet(s);}
	public String getApplyAddr() {return checkGet(applyAddr);}
	public void setApplyAddr(String s) {applyAddr = checkSet(s);}
	public String getApplyTel() {return checkGet(applyTel);}
	public void setApplyTel(String s) {applyTel = checkSet(s);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String s) {companyName = checkSet(s);}
	
	public String getReceiveName() {return checkGet(receiveName);}
	public void setReceiveName(String s) {receiveName = checkSet(s);}
	
	public String getReceiveAddr() {return checkGet(receiveAddr);}
	public void setReceiveAddr(String s) {receiveAddr = checkSet(s);}
	
	public String getLetterKind() {return checkGet(letterKind);}
	public void setLetterKind(String s) {letterKind = checkSet(s);}
	public String getLetterNo() {return checkGet(letterNo);}
	public void setLetterNo(String s) {letterNo = checkSet(s);}
	public String getGetKind() {return checkGet(getKind);}
	public void setGetKind(String s) {getKind = checkSet(s);}
	public String getGetKindRemark() {return checkGet(getKindRemark);}
	public void setGetKindRemark(String s) {getKindRemark = checkSet(s);}
	
	public String getApproveResult() {return checkGet(approveResult);}
	public void setApproveResult(String s) {approveResult = checkSet(s);}
	public String getApproveDate() {return checkGet(approveDate);}
	public void setApproveDate(String s) {approveDate = checkSet(s);}
	public String getApproveTime() {return checkGet(approveTime);}
	public void setApproveTime(String s) {approveTime = checkSet(s);}
	public String getGetDate() {return checkGet(getDate);}
	public void setGetDate(String s) {getDate = checkSet(s);}
	public String getGetTime() {return checkGet(getTime);}
	public void setGetTime(String s) {getTime = checkSet(s);}
	public String getRefundDate() {return checkGet(refundDate);}
	public void setRefundDate(String s) {refundDate = checkSet(s);}
	public String getRefundTime() {return checkGet(refundTime);}
	public void setRefundTime(String s) {refundTime = checkSet(s);}
	public String getRefundReason() {return checkGet(refundReason);}
	public void setRefundReason(String s) {refundReason = checkSet(s);}
	public String getRefundReasonOther() {return checkGet(refundReasonOther);}
	public void setRefundReasonOther(String s) {refundReasonOther = checkSet(s);}
	public String getCaseHow() {return checkGet(caseHow);}
	public void setCaseHow(String s) {caseHow = checkSet(s);}
	public String getCaseHowOther() {return checkGet(caseHowOther);}
	public void setCaseHowOther(String s) {caseHowOther = checkSet(s);}
	public String getTotal() {return checkGet(total);}
	public void setTotal(String s) {total = checkGet(s);}
	public String getNow() {return checkGet(now);}
	public void setNow(String s) {now = checkGet(s);}
	
	public int getIndex() {return index;}
	public void setIndex(int index) {this.index = index;}
	
	private int index;
	int index_2;
	
	public List<PostRecord> getPostReocrdList() {
		return postRecordList;
	}
	public void setPostRecordList(List<PostRecord> postRecordList) {
		// this.postRecordList = postRecordList;
	}

	public static String insertSysDate() {
		String date = Datetime.getYYYMMDD();
		String time = Datetime.getHHMMSS();
		date = date.substring(0,3) + "/" + date.substring(3,5) + "/" + date.substring(5);
		time = time.substring(0,2) + ":" + time.substring(2,4) + ":" + time.substring(4);
		return date+","+time;
	} // insertSysTime()
	
	
	public Object doAlternateNext() throws Exception {
		if (index >= 0)
			index--;
		
		if (index+1 > postRecordList.size())
			setNow("1");
		else if ( index+1 <= 0 )
			setNow(Integer.toString(postRecordList.size()));
		else
			setNow(Integer.toString(postRecordList.size() - index));
		PRE2008 pre2008 = this;
		if ( postRecordList == null || postRecordList.size() == 0 )
			return pre2008;
		if ( index >= postRecordList.size() || index < 0 ) {
			index = 0;
            setErrorMsg("已至最後一筆");
		} // if
		
		pre2008.setLetterNo(postRecordList.get(index).getPostNo());
		pre2008.setGetDate(Datetime.formatRocDate(postRecordList.get(index).getGetDate()));
		pre2008.setGetTime(Datetime.formatRocTime(postRecordList.get(index).getGetTime()));
		pre2008.setRefundDate(Datetime.formatRocDate(postRecordList.get(index).getBackDate()));
		pre2008.setRefundTime(Datetime.formatRocTime(postRecordList.get(index).getBackTime()));
		pre2008.setRefundReason(postRecordList.get(index).getBackReason());
		pre2008.setCaseHow(postRecordList.get(index).getOtherMethod());
		if (!"招領逾期".equals(pre2008.getRefundReason()) && !"查無此人".equals(pre2008.getRefundReason()) && !"遷移不明".equals(pre2008.getRefundReason()) && !"地址欠詳".equals(pre2008.getRefundReason()) && !"查無此址".equals(pre2008.getRefundReason()) && !"查無拒收".equals(pre2008.getRefundReason())) {
	    	pre2008.setRefundReasonOther(postRecordList.get(index).getBackReason());
	    } // if
	    if (!"不用寄".equals(pre2008.getCaseHow()) && !"原址寄".equals(pre2008.getCaseHow()) && !"改址".equals(pre2008.getCaseHow())) {
	    	pre2008.setCaseHowOther(postRecordList.get(index).getOtherMethod());
	    } // if
		if ( "01".equals( postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("普通掛號");
		} // if
		else if ( "02".equals( postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("限時掛號");
		} // else if
		else if ( "03".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("公文掛號");
		} // end else
		else if ( "04".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("平信");
		} // end else
		else if ( "05".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("限時");
		} // end else
		else {
		} // end else
		return pre2008;
	} // doAlternate()
	
	public Object doAlternateBack() throws Exception {
		if ( index < postRecordList.size() )
			index++;
		if (index+1 > postRecordList.size())
			setNow("1");
		else if ( index+1 <= 0 )
			setNow(Integer.toString(postRecordList.size()));
		else
			setNow(Integer.toString(postRecordList.size() - index));
		
		PRE2008 pre2008 = this;
		if ( postRecordList == null || postRecordList.size() == 0 )
			return pre2008;
		if ( index >= postRecordList.size() || index < 0 ) {
			index = postRecordList.size()-1;
			setErrorMsg("已至第一筆");
		} // i
		
		pre2008.setLetterNo(postRecordList.get(index).getPostNo());
		pre2008.setGetDate(Datetime.formatRocDate(postRecordList.get(index).getGetDate()));
		pre2008.setGetTime(Datetime.formatRocTime(postRecordList.get(index).getGetTime()));
		pre2008.setRefundDate(Datetime.formatRocDate(postRecordList.get(index).getBackDate()));
		pre2008.setRefundTime(Datetime.formatRocTime(postRecordList.get(index).getBackTime()));
		pre2008.setRefundReason(postRecordList.get(index).getBackReason());
		pre2008.setCaseHow(postRecordList.get(index).getOtherMethod());
		if (!"招領逾期".equals(pre2008.getRefundReason()) && !"查無此人".equals(pre2008.getRefundReason()) && !"遷移不明".equals(pre2008.getRefundReason()) && !"地址欠詳".equals(pre2008.getRefundReason()) && !"查無此址".equals(pre2008.getRefundReason()) && !"查無拒收".equals(pre2008.getRefundReason())) {
	    	pre2008.setRefundReasonOther(postRecordList.get(index).getBackReason());
	    } // if
	    if (!"不用寄".equals(pre2008.getCaseHow()) && !"原址寄".equals(pre2008.getCaseHow()) && !"改址".equals(pre2008.getCaseHow())) {
	    	pre2008.setCaseHowOther(postRecordList.get(index).getOtherMethod());
	    } // if
		if ( "01".equals( postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("普通掛號");
		} // if
		else if ( "02".equals( postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("限時掛號");
		} // else if
		else if ( "03".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("公文掛號");
		} // end else
		else if ( "04".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("平信");
		} // end else
		else if ( "03".equals(postRecordList.get(index).getPostType() ) ) {
			pre2008.setLetterKind("限時");
		} // end else
		else {
		} // end else
		return pre2008;
	} // doAlternate()
	
	public void findLetterNo() {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		//Cedb1017 useless = new Cedb1017() ;
		List<Cedb1017> cedb1017List = ServiceGetter.getInstance().getPrefixService().queryCedb1017(cedbc000.getStaffUnit()) ;
		PRE2008 pre2008 = this ;
		pre2008.setCLetterNo(cedb1017List.get(0).getUsedPostNo().substring(4));
		pre2008.setLLetterNo(cedb1017List.get(1).getUsedPostNo().substring(4));
		pre2008.setAuthLetterNo(cedb1017List.get(2).getUsedPostNo().substring(4));
	} // findLetterNo()
	
	public PRE2008 returnForm() throws Exception {
		PRE2008 pre2008 = this;
		pre2008.setPrefixNo("");
		pre2008.setApplyName("");
		pre2008.setApplyAddr("");
		pre2008.setApplyTel("");
		pre2008.setCompanyName("");	
		pre2008.setApproveDate("");
		pre2008.setApproveTime("");
		pre2008.setGetDate("");
		pre2008.setGetTime("");
		pre2008.setLetterNo("");
		pre2008.setLetterKind("");
		pre2008.setApproveResult("");
		pre2008.setGetKindRemark("");
		pre2008.setGetKind("");
		pre2008.setReceiveName("");
		pre2008.setReceiveAddr("");
		return pre2008;
	} // returnForm
	
	public void doCreate() throws Exception{
		
		PostRecord pr = new PostRecord();
		pr.setPrefixNo(getQ_caseNo());
		pr.setApplyName(getQ_applyName());
		pr.setApplyTel(getQ_applyTel());
		pr.setApplyAddr(getQ_applyAddr());
		pr.setCompanyName(getQ_companyName());
		pr.setGetName(getQ_receiveName());
		pr.setGetAddr(getQ_receiveAddr());
		
		ServiceGetter.getInstance().getPrefixService().insertPostRecord(pr);
		this.setQ_prefixNo(pr.getPrefixNo());
	} // end doCreate()
	
	public void doUpdate() throws Exception{
			
		if ( "其他".equals( getRefundReason() ) && (getRefundReasonOther() == null || "".equals(getRefundReasonOther()))) {
			setErrorMsg("請輸入詳細退件原因");
			throw new MoeaException("請輸入詳細退件原因");
		}
		
		if ( "其他".equals( getCaseHow() ) && (getCaseHowOther() == null || "".equals(getCaseHowOther()))) {
			setErrorMsg("請輸入詳細處理方式");
			throw new MoeaException("請輸入詳細處理方式");
		}
			
		String cedb1023Flag = "doNothing" ;
		
		List<PostRecord> postRecordList = ServiceGetter.getInstance().getPrefixService().getPostRecordByPrefixNo(getPrefixNo());
		PostRecord postRecord = postRecordList.get(0);
		
		if (postRecord==null){  //新增
			postRecord = new PostRecord();
			postRecord.setGetName(getReceiveName());
			postRecord.setGetAddr(getReceiveAddr());
			postRecord.setPrefixNo(getPrefixNo());
			cedb1023Flag = "insert";
		} // end if
		else {               // 更新
			postRecord.setGetName(getReceiveName());
			postRecord.setGetAddr(getReceiveAddr());
			postRecord.setPrefixNo(getPrefixNo());
			cedb1023Flag = "update";
		} // end else
		
		String getDate = Datetime.getYYYMMDD();
		String getTime = Datetime.getHHMMSS();
		String usedPostNo = "";
		
		getDate = Datetime.formatDate(getGetDate());
		getTime = Datetime.formatTime(getGetTime());
		usedPostNo = getLetterNo();
		
		postRecord.setPrefixNo(getPrefixNo());
		postRecord.setPostNo(usedPostNo);
		postRecord.setGetDate(Datetime.formatDate(getGetDate()));
		postRecord.setGetTime(Datetime.formatTime(getGetTime()));
		
		if ( "普通掛號".equals(getLetterKind()) )
			postRecord.setPostType("01");
		else if ( "限時掛號".equals(getLetterKind()) )
			postRecord.setPostType("02");
		else if ( "公文掛號".equals(getLetterKind()) )
			postRecord.setPostType("03");
		else if ( "平信".equals(getLetterKind()) )
			postRecord.setPostType("04");
		else if ( "限時".equals(getLetterKind()) )
			postRecord.setPostType("05");
		
		postRecord.setBackDate(Datetime.formatDate(getRefundDate()));
		postRecord.setBackTime(Datetime.formatTime(getRefundTime()));
		if ( "其他".equals( getRefundReason() ) ) 
			postRecord.setBackReason(getRefundReasonOther());
		else
			postRecord.setBackReason(getRefundReason());
		
		if ( "其他".equals( getCaseHow() ) )
			postRecord.setOtherMethod(getCaseHowOther());
		else
			postRecord.setOtherMethod(getCaseHow());
		 
		postRecord.setGetDate(getDate);
		postRecord.setGetTime(getTime);
		postRecord.setGetKind(getGetKind());
		postRecord.setGetKindRemark(getGetKindRemark());
		
		ServiceGetter.getInstance().getPre2008Service().doUpdate(postRecord, cedb1023Flag, PrefixConstants.FUN_CODE_2003);
		setState("assignAndSaveSuccess");
		setErrorMsg("新增成功!");
		
	} 
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object returnNullForm() {
		PRE2008 pre2008 = this;
		pre2008.setGetDate("");
		pre2008.setGetTime("");
		pre2008.setLetterNo("");
		pre2008.setLetterKind("");
		pre2008.setRefundDate("");
		pre2008.setRefundTime("");
		pre2008.setRefundReason("");
		pre2008.setCaseHow("");
	    return pre2008;
	}
	
	public Object doQueryOne() throws Exception{
		PostRecord postRecord = ServiceGetter.getInstance().getPrefixService().getLastOnePostRecordByPrefixNo(getQ_prefixNo()) != null ? 
				                ServiceGetter.getInstance().getPrefixService().getLastOnePostRecordByPrefixNo(getQ_prefixNo()) 
				                : ServiceGetter.getInstance().getPrefixService().getMainPostRecordByPrefixNo(getQ_prefixNo()) ;
		PRE2008 pre2008 = (PRE2008)returnNullForm() ;
		if ( postRecord == null ) {
			setErrorMsg("查無資料，請變更查詢條件");
			return returnForm();
		} // end if
		postRecordList = ServiceGetter.getInstance().getPre2008Service().getPostRecordList(getQ_prefixNo());
		if ( postRecordList == null || postRecordList.size() <= 0 ) {
			setTotal("0");
			setNow("0");
			pre2008.setLetterNo("");
			pre2008.setLetterKind("");
		} // if
		else {
			index = postRecordList.size()-1;
			setTotal(Integer.toString( postRecordList.size()));
			setNow(Integer.toString(postRecordList.size() - index));
			if ( "01".equals( postRecordList.get(postRecordList.size()-1).getPostType() ) ) {
				pre2008.setLetterKind("普通掛號");
				pre2008.setLetterNo(postRecordList.get(postRecordList.size()-1).getPostNo());
			} // if
			else if ( "02".equals( postRecordList.get(postRecordList.size()-1).getPostType() ) ) {
				pre2008.setLetterKind("限時掛號");
				pre2008.setLetterNo(postRecordList.get(postRecordList.size()-1).getPostNo());
			} // else if
			else if ( "03".equals(postRecordList.get(postRecordList.size()-1).getPostType() ) ) {
				pre2008.setLetterKind("公文掛號");
				pre2008.setLetterNo(postRecordList.get(postRecordList.size()-1).getPostNo());
			} // end else
			else if ( "04".equals(postRecordList.get(postRecordList.size()-1).getPostType() ) ) {
				pre2008.setLetterKind("平信");
				pre2008.setLetterNo(postRecordList.get(postRecordList.size()-1).getPostNo());
			} // end else
			else if ( "05".equals(postRecordList.get(postRecordList.size()-1).getPostType() ) ) {
				pre2008.setLetterKind("限時");
				pre2008.setLetterNo(postRecordList.get(postRecordList.size()-1).getPostNo());
			} // end else
			else {
			} // end else
		} // else
		
		pre2008.setPrefixNo(postRecord.getPrefixNo());
		// 將申請人資料依序填入變數內
		pre2008.setApplyName(postRecord.getApplyName());
		pre2008.setApplyAddr(postRecord.getApplyAddr());
		pre2008.setApplyTel(postRecord.getApplyTel());
		pre2008.setCompanyName(postRecord.getCompanyName());
		// 本案件已被領件 (不論自取或郵寄)
		// 查看是否有郵寄號碼
		// pre2003.setLetterKind(ServiceGetter.getInstance().getSystemCode07Loader().getCodeNameByCode(cedb1027.getPostType()));
		pre2008.setGetDate(Datetime.formatRocDate(postRecord.getGetDate()));
		pre2008.setGetTime(Datetime.formatRocTime(postRecord.getGetTime()));
		pre2008.setLetterNo(Common.get(postRecord.getPostNo()));
		pre2008.setRefundDate(Datetime.formatRocDate(postRecord.getBackDate()));
		pre2008.setRefundTime(Datetime.formatRocTime(postRecord.getBackTime()));
		pre2008.setRefundReason(postRecord.getBackReason());
		if (!"招領逾期".equals(pre2008.getRefundReason()) && !"查無此人".equals(pre2008.getRefundReason()) && !"遷移不明".equals(pre2008.getRefundReason()) && !"地址欠詳".equals(pre2008.getRefundReason()) && !"查無此址".equals(pre2008.getRefundReason()) && !"查無拒收".equals(pre2008.getRefundReason())) {
		  	pre2008.setRefundReasonOther(postRecord.getBackReason());
		} // if
		pre2008.setCaseHow(postRecord.getOtherMethod());
		if (!"不用寄".equals(pre2008.getCaseHow()) && !"原址寄".equals(pre2008.getCaseHow()) && !"改址".equals(pre2008.getCaseHow())) {
		  	pre2008.setCaseHowOther(postRecord.getOtherMethod());
		} // if				
		pre2008.setReceiveName(postRecord.getGetName());
		pre2008.setReceiveAddr(postRecord.getGetAddr());
			
		pre2008.setGetKindRemark(postRecord.getGetKindRemark());
		pre2008.setGetKind(postRecord.getGetKind());
		// 將收件人資料依序填入
	    //寫入個資軌跡 (查詢類)
			
		return pre2008;
	}
	
	public ArrayList<?> doQueryAll() throws Exception {		
		return null ;
	}
	
	public void assignAndSave() throws Exception {
		try {
			CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
			Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
			if ( "其他".equals( getRefundReason() ) && (getRefundReasonOther() == null || "".equals(getRefundReasonOther()))) {
				setErrorMsg("請輸入詳細退件原因");
				throw new MoeaException("請輸入詳細退件原因");
			}
			if ( "其他".equals( getCaseHow() ) && (getCaseHowOther() == null || "".equals(getCaseHowOther()))) {
				setErrorMsg("請輸入詳細處理方式");
				throw new MoeaException("請輸入詳細處理方式");
			}
			if ("01".equals(getPostType()) || "02".equals(getPostType()) || "03".equals(getPostType())) {
				List<Cedb1017> tempList1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017(cedbc000.getStaffUnit()) ;
				Cedb1017 cedb1017 = new Cedb1017();
				if ("01".equals(getPostType())) {   // 普掛
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(0).getUsedPostNo()));
					cedb1017.setPostType("01");
					cedb1017.setStartPostNo(tempList1017.get(0).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(0).getEndPostNo());
				} // end if
				else if ("02".equals(getPostType())) {  // 限掛
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(1).getUsedPostNo()));
					cedb1017.setPostType("02");
					cedb1017.setStartPostNo(tempList1017.get(1).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(1).getEndPostNo());
				} // end if
				else { // 公文
					cedb1017.setUsedPostNo(addLetterNo(tempList1017.get(2).getUsedPostNo()));
					cedb1017.setPostType("03");
					cedb1017.setStartPostNo(tempList1017.get(2).getStartPostNo());
					cedb1017.setEndPostNo(tempList1017.get(2).getEndPostNo());
				} // else
				ServiceGetter.getInstance().getPre2003Service().doAssign(cedb1017);
			} // end if
			
					
				// 取得取件日期時間 getTime & getDate
			String getDate = "" ;
			String getTime = "";
			String usedPostNo = "";
			String postType = getPostType();
			if ( "01".equals( postType ) || "02".equals( postType ) || "03".equals( postType ) ) {
					Cedb1017 tempCedb1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), postType);
					usedPostNo = tempCedb1017.getUsedPostNo();
					getDate = Datetime.getYYYMMDD();
					getTime = Datetime.getHHMMSS();
			} // end if
			else {
				usedPostNo = Datetime.getHHMMSS();
				getDate = Datetime.getYYYMMDD();
				getTime = Datetime.getHHMMSS();
			} // else
			
			String cedb1023Flag = "doNothing" ;
			PostRecord postRecord = ServiceGetter.getInstance().getPrefixService().getPostRecordByPrefixNoAndPostNo(getPrefixNo(), usedPostNo);
			if (postRecord==null){ 
				postRecord = new PostRecord();
				postRecord.setGetName(getReceiveName());
				postRecord.setGetAddr(getReceiveAddr());
				postRecord.setPrefixNo(getPrefixNo());
				cedb1023Flag = "insert";
			} // end if
			else {               // 更新
				postRecord.setGetName(getReceiveName());
				postRecord.setGetAddr(getReceiveAddr());
				postRecord.setPrefixNo(getPrefixNo());
				cedb1023Flag = "update";
			} // end else
			
			
			postRecord.setPrefixNo(getPrefixNo());
			postRecord.setPostNo(usedPostNo);
			postRecord.setGetDate(getDate);
			postRecord.setGetTime(getTime);
			postRecord.setPostType(postType);
			postRecord.setGetName(getReceiveName());
			postRecord.setGetAddr(getReceiveAddr());
			postRecord.setApplyName(getApplyName());
			postRecord.setApplyAddr(getApplyAddr());
			postRecord.setApplyTel(getApplyTel());
			postRecord.setCompanyName(getCompanyName());
		
			postRecord.setBackDate(Datetime.formatDate(getRefundDate()));
			postRecord.setBackTime(Datetime.formatTime(getRefundTime()));
			if ( "其他".equals( getRefundReason() ) ) 
				postRecord.setBackReason(getRefundReasonOther());
			else
				postRecord.setBackReason(getRefundReason());
			
			if ( "其他".equals( getCaseHow() ) )
				postRecord.setOtherMethod(getCaseHowOther());
			else
				postRecord.setOtherMethod(getCaseHow());
		    	
		    postRecord.setGetDate(getDate);
		    postRecord.setGetTime(getTime);
		    postRecord.setGetKind(getGetKind());
		    postRecord.setGetKindRemark(getGetKindRemark());
		    postRecord.setPrefixNo(getPrefixNo());
			System.out.println("==="+postRecord.getPostNo()+"===");
			ServiceGetter.getInstance().getPre2008Service().doSave(postRecord, cedb1023Flag, PrefixConstants.FUN_CODE_2003);
			// doUpdate();
			setState("assignAndSaveSuccess");
			setErrorMsg("存檔成功!");
		} // try
		catch( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
		} // catch
	} // assignAndSave()
	
	public String addLetterNo( String inputNo ) {
		String begin = inputNo.substring(0,1);
		String tempNo = inputNo.substring(1);
		tempNo = Integer.toString(Integer.parseInt(tempNo)+1);
		inputNo = begin + tempNo;
		return inputNo;
	} // addLetterNo
	
	public File doPrintPdf() throws Exception {
		return null ;
	} // doPrintPdf()
	
	public void rollBack() throws Exception {
		List<PostRecord> postRecordList = ServiceGetter.getInstance().getPre2008Service().getPostRecordList(getQ_prefixNo());
		if ( postRecordList != null && postRecordList.size() > 0 ) {
			PostRecord postRecord = new PostRecord();
			postRecord.setPrefixNo(getPrefixNo());
			postRecord.setGetDate(Datetime.formatDate(getGetDate()));
			postRecord.setGetTime(Datetime.formatTime(getGetTime()));
			postRecord.setGetKind("");
			postRecord.setPrefixNo(getQ_prefixNo());
			//備份
			ServiceGetter.getInstance().getPre2008Service().rollBack(postRecord);
			setErrorMsg("取消成功!");
		} // if
		else {
			setErrorMsg("這筆預查編號已經沒有可供刪除的郵寄資料!");
		} // else
		// 
	} // rollBack()	
	
}