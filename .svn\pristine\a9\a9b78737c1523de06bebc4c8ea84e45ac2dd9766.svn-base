<!-- 
程式目的：核准領件編號維護作業
程式代號：pre2004
程式日期：1030624
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2004">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2004" />
</jsp:include>
<%
if ("init".equals(obj.getState())) {
	//obj = (com.kangdainfo.tcfi.view.pre.PRE2004)obj.queryOne();
	//預設先查詢
	obj.setQueryAllFlag("true") ;
} else if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true") ;
} else if ("update".equals(obj.getState())) {
	obj.update();
	obj.setQueryAllFlag("true") ;
}
if ( "true".equals(obj.getQueryAllFlag()) ) {
	objList = (java.util.ArrayList) obj.queryAll();
	if( "".equals(obj.getErrorMsg()) ) {
		if(null!=objList && !objList.isEmpty())
			obj.setErrorMsg("查詢成功!");
		else
			obj.setErrorMsg("查無資料!");
	}
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE2004"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function ajaxGetLastClosedPrefixNo() {
	//查詢已發文編號
	$.post(getVirtualPath() + "tcfi/ajax/jsonPre4001GetPrefixNo.jsp", function( data ) {
		var no = "";
		try {
			no = data;
			if( data.length > 20 ) throw 'ERROR'; 
		} catch(e) {
			no = "連線異常";
		}
		$('#lastClosedPrefixNo').val(no);
	});
}

function checkQryField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_prefixStart,"預查編號起");
	alertStr += checkEmpty(form1.q_prefixEnd,"預查編號迄");

	if( form1.q_prefixStart.value != '' && form1.q_prefixStart.value.length != 9 ) {
		alertStr += "預查編號起不足9碼";
	}
	if( form1.q_prefixEnd.value != '' && form1.q_prefixEnd.value.length != 9 ) {
		alertStr += "預查編號迄不足9碼";
	}

	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function checkField(){
	var alertStr="";

	var checkboxs = document.getElementsByName("beChecked");
	var total = 0;
	for ( var i=0; i<checkboxs.length;i++ ) {	
		if ( checkboxs[i].checked == true )
			total++;
	} // for
	if ( total == 0 ) {
		alertStr += "請先勾選要修改的預查編號";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function checkRemark() {
	var checkboxs = document.getElementsByName("beChecked");
	for (var i = 0; i < checkboxs.length; i++) {
		if ( checkboxs[i].checked == true ){	
			// 呼叫判斷
			var x = getRemoteData(getVirtualPath() + "tcfi/ajax/jsonPre2004ChkRemark.jsp?q=" + checkboxs[i].value);
			if (x != 'N') {
				checkboxs[i].checked = !confirm(checkboxs[i].value + x + "，是否取消發文?");					
			}
		}
	}
	
}

$(document).ready(function() {
	ajaxGetLastClosedPrefixNo();
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doUpdate":
				checkRemark();
				if( checkField() ) {
					$('#state').val("update") ;	
					setBeforePageUnload(false) ;
					form1.submit();
				}
				break;
			case "updateError":
				setBeforePageUnload(false) ;
				break;
			case "doQueryAll":
				if(checkQryField()) {
					$('#state').val("queryAll") ;
					form1.submit();
				}
				break;
			case "doClear":
				form1.q_prefixStart.value = "";
				form1.q_prefixEnd.value = "";
				break;
			case "doReload":
				form1.q_prefixStart.value = "";
				form1.q_prefixEnd.value = "";
				$('#state').val("init") ;
				form1.submit();
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function queryOne(prefixNo) {
	var _url = getVirtualPath() + "tcfi/pre/pre4001_00.jsp?shortcut=Y&prefixNos="+prefixNo;
	var _target = 'PRE2004_'+randomUUID().replace(/\-/g,"");
	var _param = "left=0";
	_param += ",top=0";
	_param += ",scrollbars=1";
	_param += ",resizable=1";
	_param += ",toolbar=0";
	_param += ",menubar=0";
	_param += ",directories=0";
	_param += ",status=0";
	_param += ",location=0";
	_param += ",width="+screen.width;
	_param += ",height="+screen.height*0.96;
	window.open( _url, _target, _param);
}

function clickCheckAll(filter) 
{
    for (var i=0;i<document.forms[0].elements.length;i++)
    {
       var e = document.forms[0].elements[i];
       if (e.type == 'checkbox')
          e.checked = filter;
    }
} 
</script>
</head>
<body topmargin="5" >
<form id="form1" name="form1" method="post" >

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE2004'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="12%">預查編號領件</td>
			<td class="td_form_white">
				起<input class="field_Q" type="text" id="q_prefixStart" name="q_prefixStart" size="15" maxlength="9" value="<%=obj.getQ_prefixStart()%>">
				迄<input class="field_Q" type="text" id="q_prefixEnd" name="q_prefixEnd" size="15" maxlength="9" value="<%=obj.getQ_prefixEnd()%>">
				<input class="toolbar_default" type="button" followPK="false" id="doQueryAll" name="doQueryAll" value="查詢" onClick="whatButtonFireEvent(this.name)" >
				&nbsp<input class="toolbar_default" type="button" followPK="false" id="doReload" name="doReload" value="更新清單" onClick="whatButtonFireEvent(this.name)" >
				&nbsp<input class="toolbar_default" type="button" followPK="false" id="doUpdate" name="doUpdate" value="存檔" onClick="whatButtonFireEvent(this.name)" >
			</td>
			<td class="td_form_white" style="text-align:right">
				<c:import url="../common/shortcut.jsp">
					<c:param name="functions" value='PRE2002,PRE4001'/>
				</c:import>
			</td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
<!-- FORM AREA -->
<tr><td class="bg">
    <div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%" cellspacing="0" cellpadding="0">
		<td class="td_form" width="120px">已發文編號</td>
		<td class="td_form_white">
			<input type="text" class="field_RO" style="color:#FF0000" id="lastClosedPrefixNo" name="lastClosedPrefixNo" size="15" value="" />
		</td> 
	</table>
	</div>
	
</td></tr>

<!-- LIST AREA -->
<tr><td nowrap class="bgList">
	<div id="listContainer" height="200">
		<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
			<thead id="listTHEAD">
				<tr>
					<th class="listTH" style="width:50">
						<input type="checkbox" id="checkAll" name="checkAll" onclick="clickCheckAll(this.checked);" />
					</th>
					<th class="listTH" style="width:130"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
					<th class="listTH" style="width:130"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">申請方式</a></th>
					<th class="listTH" style="width:175"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">網路收文號</a></th>
					<th class="listTH" style="width:100"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">預查種類</a></th>
					<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">申請人</a></th>
					<th class="listTH" style="width:180"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">代理人</a></th>
					<th class="listTH" style="width:165"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">申請時間</a></th>
					<th class="listTH" style="width:80"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">領件方式</a></th>
					<th class="listTH" style="width:165"><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">分文時間</a></th>
				</tr>
			</thead>
			<tbody id="listTBODY">
<%
			boolean primaryArray[] = {true,false,false,false,false,false,false,false,false};
			boolean displayArray[] = {true,true,true,true,true,true,true,true,true};
			String[] alignArray = {"center","center", "center","center","center","center","center","center","center"};
			out.write(View.getCheckboxQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),"beChecked"));
%>
			</tbody>
		</table>
	</div>
</td></tr>
<!-- LIST AREA -->

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" style="text-align:center;">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>