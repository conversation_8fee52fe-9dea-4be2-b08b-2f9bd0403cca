package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;



public class PRE8009 extends SuperBean {
	
	private String specialPostType ;// 修正於2024/01/24 配合特約-普掛名稱
	private String specialStartPostNo ;// 修正於2024/01/24 配合特約-普掛名稱
	private String specialEndPostNo ;// 修正於2024/01/24 配合特約-普掛名稱
	private String specialUsedPostNo ;// 修正於2024/01/24 配合特約-普掛名稱
	private String specialLPostType ;// 修正於2024/01/24 配合特約-限掛名稱
	private String specialLStartPostNo ;// 修正於2024/01/24 配合特約-限掛名稱
    private String specialLEndPostNo ;// 修正於2024/01/24 配合特約-限掛名稱
    private String specialLUsedPostNo ;// 修正於2024/01/24 配合特約-限掛名稱
    private String authPostType;
    private String authStartPostNo;
    private String authEndPostNo;
    private String authUsedPostNo;
    private String postType;// 修正於2024/01/24 配合普掛名稱
    private String startPostNo;// 修正於2024/01/24 配合普掛名稱
    private String endPostNo;// 修正於2024/01/24 配合普掛名稱
    private String usedPostNo;// 修正於2024/01/24 配合普掛名稱
    private String lPostType;// 修正於2024/01/24 配合限掛名稱
    private String lStartPostNo;// 修正於2024/01/24 配合限掛名稱
    private String lEndPostNo;// 修正於2024/01/24 配合限掛名稱
    private String lUsedPostNo;// 修正於2024/01/24 配合限掛名稱

	// ----------------------------getters and setters of local variables bellow--------------------------------------------
	public String getSpecialPostType() {// 修正於2024/01/24 配合特約-普掛名稱
	 	return checkGet(specialPostType);
	} //  getSpecialPostType()
	public void setSpecialPostType(String s) {// 修正於2024/01/24 配合特約-普掛名稱
		specialPostType = checkSet(s);
	} // setSpecialPostType()
	public String getSpecialStartPostNo() {// 修正於2024/01/24 配合特約-普掛名稱
	 	return checkGet(specialStartPostNo);
	} //  getSpecialStartPostNoe()
	public void setSpecialStartPostNo(String s) {// 修正於2024/01/24 配合特約-普掛名稱
		specialStartPostNo = checkSet(s);
	} // setSpecialStartPostNo()
	public String getSpecialEndPostNo() {// 修正於2024/01/24 配合特約-普掛名稱
	 	return checkGet(specialEndPostNo);
	} //  getSpecialEndPostNo()
	public void setSpecialEndPostNo(String s) {// 修正於2024/01/24 配合特約-普掛名稱
		specialEndPostNo = checkSet(s);
	} // setSpecialEndPostNo()
	public String getSpecialUsedPostNo() {// 修正於2024/01/24 配合特約-普掛名稱
	 	return checkGet(specialUsedPostNo);
	} //  getSpecialUsedPostNo()
	public void setSpecialUsedPostNo(String s) {// 修正於2024/01/24 配合特約-普掛名稱
		specialUsedPostNo = checkSet(s);
	} // setSpecialUsedPostNo()
    public String getSpecialLPostType() {// 修正於2024/01/24 配合特約-限掛名稱
		return checkGet(specialLPostType);
	} // getSpecialLPostType()
	public void setSpecialLPostType(String lPostType) {// 修正於2024/01/24 配合特約-限掛名稱
		this.specialLPostType = checkSet(lPostType);
	} // setSpecialLPostType()
	public String getSpecialLStartPostNo() {// 修正於2024/01/24 配合特約-限掛名稱
	 	return checkGet(specialLStartPostNo);
	} //  getSpecialLStartPostNo()
	public void setSpecialLStartPostNo(String s) {// 修正於2024/01/24 配合特約-限掛名稱
		specialLStartPostNo = checkSet(s);
	} // setSpecialLStartPostNo()
	public String getSpecialLEndPostNo() {// 修正於2024/01/24 配合特約-限掛名稱
	 	return checkGet(specialLEndPostNo);
	} //  getSpecialLEndPostNo()
	public void setSpecialLEndPostNo(String s) {// 修正於2024/01/24 配合特約-限掛名稱
		specialLEndPostNo = checkSet(s);
	} // setSpecialLEndPostNo()
	public String getSpecialLUsedPostNo() {// 修正於2024/01/24 配合特約-限掛名稱
	 	return checkGet(specialLUsedPostNo);
	} //  getSpecialLUsedPostNo()
	public void setSpecialLUsedPostNo(String s) {// 修正於2024/01/24 配合特約-限掛名稱
		specialLUsedPostNo = checkSet(s);
	} // setSpecialLUsedPostNo()
	public String getAuthPostType() {
		return checkGet(authPostType);
	}
	public void setAuthPostType(String authPostType) {
		this.authPostType = checkSet(authPostType);
	}
	public String getAuthStartPostNo() {
	 	return checkGet(authStartPostNo);
	} //  getPostType()
	public void setAuthStartPostNo(String s) {
		authStartPostNo = checkSet(s);
	} // setPostType()
	public String getAuthEndPostNo() {
	 	return checkGet(authEndPostNo);
	} //  getPostType()
	public void setAuthEndPostNo(String s) {
		authEndPostNo = checkSet(s);
	} // setPostType()
	public String getAuthUsedPostNo() {
	 	return checkGet(authUsedPostNo);
	} //  getPostType()
	public void setAuthUsedPostNo(String s) {
		authUsedPostNo = checkSet(s);
	} // setPostType()
	public String getPostType() {// 修正於2024/01/24 配合普掛名稱
		return checkGet(postType);
	}
	public void setPostType(String s) {// 修正於2024/01/24 配合普掛名稱
		postType = checkSet(s);
	}
	public String getStartPostNo() {// 修正於2024/01/24 配合普掛名稱
		return checkGet(startPostNo);
	}
	public void setStartPostNo(String s) {// 修正於2024/01/24 配合普掛名稱
		startPostNo = checkSet(s);
	}
	public String getEndPostNo() {// 修正於2024/01/24 配合普掛名稱
		return checkGet(endPostNo);
	}
	public void setEndPostNo(String s) {// 修正於2024/01/24 配合普掛名稱
		endPostNo = checkSet(s);
	}
	public String getUsedPostNo() {// 修正於2024/01/24 配合普掛名稱
		return checkGet(usedPostNo);
	}
	public void setUsedPostNo(String s) {// 修正於2024/01/24 配合普掛名稱
		usedPostNo = checkSet(s);
	}
	public String getlPostType() {// 修正於2024/01/24 配合限掛名稱
		return checkGet(lPostType);
	}
	public void setlPostType(String s) {// 修正於2024/01/24 配合限掛名稱
		lPostType = checkSet(s);
	}
	public String getlStartPostNo() {// 修正於2024/01/24 配合限掛名稱
		return checkGet(lStartPostNo);
	}
	public void setlStartPostNo(String s) {// 修正於2024/01/24 配合限掛名稱
		lStartPostNo = checkSet(s);
	}
	public String getlEndPostNo() {// 修正於2024/01/24 配合限掛名稱
		return checkGet(lEndPostNo);
	}
	public void setlEndPostNo(String s) {// 修正於2024/01/24 配合限掛名稱
		lEndPostNo = checkSet(s);
	}
	public String getlUsedPostNo() {// 修正於2024/01/24 配合限掛名稱
		return checkGet(lUsedPostNo);
	}
	public void setlUsedPostNo(String s) {// 修正於2024/01/24 配合限掛名稱
		lUsedPostNo = checkSet(s);
	}
	// --------------------------------------------------------------------------------------
	
	public Object doQueryOne() throws Exception{ 
		PRE8009 obj = this ;                 // 使用者端
		//Cedb1017 cedb1017 = new Cedb1017();  // 伺服器端
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		java.util.List<Cedb1017> objList = ServiceGetter.getInstance().getPrefixService().queryCedb1017(cedbc000.getStaffUnit());
				
		if (objList != null && objList.size() > 0){
			for(Cedb1017 o : objList){
				if("01".equals(o.getPostType())){// 修正於2024/01/24  改為特約-普掛
					obj.setSpecialPostType(o.getPostType());
					obj.setSpecialStartPostNo(o.getStartPostNo());
					obj.setSpecialEndPostNo(o.getEndPostNo());
					obj.setSpecialUsedPostNo(o.getUsedPostNo());
				}else if("02".equals(o.getPostType())){// 修正於2024/01/24  改為特約-限掛
					obj.setSpecialLPostType(o.getPostType());
					obj.setSpecialLStartPostNo(o.getStartPostNo());
					obj.setSpecialLEndPostNo(o.getEndPostNo());
					obj.setSpecialLUsedPostNo(o.getUsedPostNo());
				}else if("03".equals(o.getPostType())){
					obj.setAuthPostType(o.getPostType());
					obj.setAuthStartPostNo(o.getStartPostNo());
					obj.setAuthEndPostNo(o.getEndPostNo());
					obj.setAuthUsedPostNo(o.getUsedPostNo());
				}else if("06".equals(o.getPostType())){// 修正於2024/01/24 新增普掛 
					obj.setPostType(o.getPostType());
					obj.setStartPostNo(o.getStartPostNo());
					obj.setEndPostNo(o.getEndPostNo());
					obj.setUsedPostNo(o.getUsedPostNo());
				}else if("07".equals(o.getPostType())){// 修正於2024/01/24 新增限掛 
					obj.setlPostType(o.getPostType());
					obj.setlStartPostNo(o.getStartPostNo());
					obj.setlEndPostNo(o.getEndPostNo());
					obj.setlUsedPostNo(o.getUsedPostNo());
				}
			}
		}else{
			this.setErrorMsg("查無資料！");
		} // end if
		return obj;
	} // end doQueryOne()
	

	public ArrayList<?> doQueryAll() throws Exception{
		return null;
	}
	
	public String diffPostNo(String inputPostNo) {
		if ( inputPostNo == null || "".equals(inputPostNo) ) 
			return "";
		String postNo = "";
		int tempPostNo = Integer.parseInt(inputPostNo.substring(1)) - 1;
		if (inputPostNo.substring(1,2).equals("0")) {			
			postNo = inputPostNo.substring(0,2) + tempPostNo;			
		} else {
			postNo = inputPostNo.substring(0,1) + tempPostNo;			
		}
		return postNo;
	}
		
	public void doUpdate() throws Exception {   
		try {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
		Cedb1017 target1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), "01");
		Cedb1017 cedb = new Cedb1017();
		cedb.setPostType("01");
	    cedb.setStartPostNo(getSpecialStartPostNo());// 修正於2024/01/24  改為特約-普掛 
	    cedb.setEndPostNo(getSpecialEndPostNo());
	    cedb.setUsedPostNo(diffPostNo(getSpecialStartPostNo())) ;
	    cedb.setStaffUnit(cedbc000.getStaffUnit());
	    if ( !Common.get(target1017.getStartPostNo()).equals(Common.get(cedb.getStartPostNo())) || !Common.get(target1017.getEndPostNo()).equals(Common.get(cedb.getEndPostNo())) ) {
	    	ServiceGetter.getInstance().getPrefixService().saveCedb1017(cedb);
	    } // if
		target1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), "02");
	    cedb = new Cedb1017();
	    cedb.setPostType("02");
		cedb.setStartPostNo(getSpecialLStartPostNo());// 修正於2024/01/24  改為特約-限掛 
		cedb.setEndPostNo(getSpecialLEndPostNo());
		cedb.setUsedPostNo(diffPostNo(getSpecialLStartPostNo())) ;
		cedb.setStaffUnit(cedbc000.getStaffUnit());
		if ( !Common.get(target1017.getStartPostNo()).equals(Common.get(cedb.getStartPostNo())) || !Common.get(target1017.getEndPostNo()).equals(Common.get(cedb.getEndPostNo())) ) {
			ServiceGetter.getInstance().getPrefixService().saveCedb1017(cedb);
		} // if
		target1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), "03");
		cedb = new Cedb1017();
		cedb.setPostType("03");
		cedb.setStartPostNo(getAuthStartPostNo());
		cedb.setEndPostNo(getAuthEndPostNo());
	    cedb.setUsedPostNo(diffPostNo(getAuthStartPostNo())) ;
	    cedb.setStaffUnit(cedbc000.getStaffUnit());
	    if ( !Common.get(target1017.getStartPostNo()).equals(Common.get(cedb.getStartPostNo())) || !Common.get(target1017.getEndPostNo()).equals(Common.get(cedb.getEndPostNo())) ) {
	    	ServiceGetter.getInstance().getPrefixService().saveCedb1017(cedb);
	    } // if
	    target1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), "06");// 修正於2024/01/24 新增普掛  
		cedb = new Cedb1017();
		cedb.setPostType("06");
		cedb.setStartPostNo(getStartPostNo());
		cedb.setEndPostNo(getEndPostNo());
	    cedb.setUsedPostNo(diffPostNo(getStartPostNo())) ;
	    cedb.setStaffUnit(cedbc000.getStaffUnit());
	    if ( !Common.get(target1017.getStartPostNo()).equals(Common.get(cedb.getStartPostNo())) || !Common.get(target1017.getEndPostNo()).equals(Common.get(cedb.getEndPostNo())) ) {
	    	ServiceGetter.getInstance().getPrefixService().saveCedb1017(cedb);
	    } // if
	    target1017 = ServiceGetter.getInstance().getPrefixService().queryCedb1017ByPostType(cedbc000.getStaffUnit(), "07");// 修正於2024/01/24 新增限掛 
		cedb = new Cedb1017();
		cedb.setPostType("07");
		cedb.setStartPostNo(getlStartPostNo());
		cedb.setEndPostNo(getlEndPostNo());
	    cedb.setUsedPostNo(diffPostNo(getlStartPostNo())) ;
	    cedb.setStaffUnit(cedbc000.getStaffUnit());
	    if ( !Common.get(target1017.getStartPostNo()).equals(Common.get(cedb.getStartPostNo())) || !Common.get(target1017.getEndPostNo()).equals(Common.get(cedb.getEndPostNo())) ) {
	    	ServiceGetter.getInstance().getPrefixService().saveCedb1017(cedb);
	    } // if
		} catch (Exception e) {
			e.printStackTrace();
		}
	} // end doUpdate()
	
	public void doCreate() throws Exception{
		
	} // end doCreate() 
	  
    public void doDelete() throws Exception{			
		   
    } // end doDelete()	
} //PRE8009()