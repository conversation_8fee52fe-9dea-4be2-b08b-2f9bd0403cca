package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司名稱預查申請記錄檔(CEDB1001)
 *
 */
public class Cedb1001 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 審查結果 */
	private String approveResult;
	/** 公司名稱 */
	private String companyName;
	/** 預查編號 */
	private String prefixNo;
	/** 同名公司註記 */
	private String remark;
	/** 序號 */
	private String seqNo;

	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String approveResult) {this.approveResult = approveResult;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}
	public String getSeqNo() {return seqNo;}
	public void setSeqNo(String seqNo) {this.seqNo = seqNo;}

}