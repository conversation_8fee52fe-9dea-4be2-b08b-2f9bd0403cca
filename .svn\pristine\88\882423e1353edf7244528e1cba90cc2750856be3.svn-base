<!--
程式目的：所營項目輔助查詢
程式代號：PRE3001_01
撰寫日期：103.05.23
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001_01">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("init".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
$(document).ready(function(){
	//事件註冊
	$('#btnReset').click(function(){
		form1.reset();
	});
	$('#btnSearch').click(function(){
		form1.state.value = "doSearch";
		form1.submit();
	});
	$('#keyword').keydown(function(e){
		var code = e.keyCode || e.which; 
		if (code  == 13) {               
	   		e.preventDefault();
			form1.state.value = "doSearch";
			form1.submit();
		}
	});
	//畫面初始
	$('#keyword').focus();
});

function addItem(itemCode, item) {
	window.opener.insertItem(itemCode, item);
}

</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='所營項目輔助查詢'/>
</c:import>

<!-- TOOLBAR AREA -->
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td style="text-align:left">
			<input class="toolbar_default" type="button" id="btnReset" name="btnReset" value="重新輸入" />&nbsp;
			<input class="toolbar_default" type="button" id="btnSearch" name="btnSearch" value="執行查詢" />&nbsp;
			<font color="#000000">(點選插入前，請先於營業項目清單中，選取插入的位置)</font>
		</td>
		<td align="right">
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" onclick="javascript:window.close();" />&nbsp;
		</td>
	</tr>
</table>
<!-- TOOLBAR AREA -->

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">所營項目關鍵字：</td>
		<td class="td_form_white">
			<input class="field" type="text" id="keyword" name="keyword" size="70" maxlength="250" value="<%=obj.getKeyword()%>" />
		</td>
	</tr>
	</table>
	</div>
</td></tr>

<tr><td class="bg">
	<div id="listContainer">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" style="width:80px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">選取</a></th>
    		<th class="listTH" style="width:100px;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">編號</a></th>
    		<th class="listTH" ><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">名稱</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			out.write(obj.getQuerylist(objList, obj.getQueryAllFlag()));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>