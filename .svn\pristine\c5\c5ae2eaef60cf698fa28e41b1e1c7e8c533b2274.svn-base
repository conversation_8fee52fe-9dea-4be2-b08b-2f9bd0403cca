<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(q)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",NVL(A.RECEIVE_DATE,' ') AS RECEIVE_DATE");
		sqljob.appendSQL(",NVL(A.BAN_NO,' ') AS BAN_NO");
		sqljob.appendSQL(",NVL(A.TELIX_NO,' ') AS TELIX_NO");
		sqljob.appendSQL(",NVL(A.APPLY_NAME,' ') AS APPLY_NAME");
		sqljob.appendSQL(",DECODE(B.CHANGE_TYPE,'0','設立','1','名稱變更','2','所營變更','3','名稱及所營變更','') AS APPLY_KIND");
		sqljob.appendSQL(",NVL(B.CHANGE_TYPE,' ') AS CHANGE_TYPE");
		sqljob.appendSQL(",(CASE");
		sqljob.appendSQL("   WHEN SUBSTR(NVL(A.TELIX_NO,' '),0,1)='O' THEN '一站式'");
		sqljob.appendSQL("   WHEN SUBSTR(NVL(A.TELIX_NO,' '),0,1)='Z' THEN '一維條碼'");
		sqljob.appendSQL("   WHEN SUBSTR(NVL(A.TELIX_NO,' '),0,1)='0' THEN '一維條碼'");
		sqljob.appendSQL("   WHEN SUBSTR(NVL(A.TELIX_NO,' '),0,1)='L' THEN '線上審核'");
		sqljob.appendSQL("   WHEN SUBSTR(NVL(A.TELIX_NO,' '),0,1)='A' THEN '線上申辦'");
		sqljob.appendSQL("   ELSE '紙本送件' END) AS APPLY_WAY");
		sqljob.appendSQL("FROM CEDB1000 A JOIN CEDB1023 B ON A.PREFIX_NO = B.PREFIX_NO");
		sqljob.appendSQL("WHERE A.RCV_CHECK=?");
		sqljob.appendSQL("AND A.ID_NO IS NULL");//尚未分文
		sqljob.appendSQL("ORDER BY 1");
		sqljob.addParameter(q);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>