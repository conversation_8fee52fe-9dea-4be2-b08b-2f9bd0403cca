<!--
程式目的：審核-預查審核-案件選擇
程式代號：PRE3001_00
撰寫日期：103.05.22
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3001" />
</jsp:include>
<%
if ("init".equals(obj.getState())){
	obj.init();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3001"/></jsp:include>
<%@ include file="../../home/<USER>"%>
<script>
$( document ).ready(function() {
	$("#prefixNo").focus();
	$("input[name=btnQuery4]").click(function(){
		commonUtils.all("selectCases");
	});
	$("input[name=btnQuery5]").click(function(){
		commonUtils.unAll("selectCases");
	});
	$("input[name=btnQuery6]").click(function(){
		var $checks = $("input[name=selectCases]").filter(":checked");
		if( $checks.size() > 0) {
			var prefixNo = "";
			for(var i=0; i< $checks.size(); i++) {
				if($checks.size() == 1) {
					prefixNo += $checks.eq(i).val();
				} else {
					prefixNo += $checks.eq(i).val() + "-";
				}
			}
			
			form1.method = "get";
			form1.hiddenPrefixNos.value = prefixNo;
			form1.prefixNo.value = prefixNo;
			$("input[name=state]").remove();
			form1.action = "pre3001_00.jsp";
			$.cookie("activeTabIndex", 0);
			form1.submit();
		}
	});
	$('#prefixNo, #banNo, #applyId, #applyName, #companyName, #telixNo').keydown(function(e){
		if ( e.which == 13 ) {
			var applyId = document.getElementById("applyId");
			var telixNo = document.getElementById("telixNo");
			toUpper(applyId);
			toUpper(telixNo);
			if( this.value.length > 0 ) {
				e.preventDefault();
				doSearch();
			}
		}
	});
});
	
function keyDown() {
	if (event.keyCode == 13) {
		//showDetail();
	}
}

function doSearch() {
	form1.state.value = "doSearch";
	form1.submit();
}

function doExit() {
	window.close();
}

function resetForm() {
	form1.reset();
}

function init() {
	setDisplayItem(
			"spanDoConfirm,spanQueryAll,spanInsert,spanClear,spanConfirm,spanUpdate,spanDelete,spanListPrint,spanListHidden",
			"H");
}
</script>
<script type="text/javascript" src="<%=contextPath%>/js/approve.js"></script>
</head>
<body topmargin="5" onLoad="showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3001'/>
</c:import>

<table class="bgToolbar" width="100%" cellpadding="2" cellspacing="0">
  <tr>
    <td align="left">
	  <span id="spanQuery1">
			<input class="toolbar_default" type="button"  name="btnQuery1" value="重新輸入" onClick="resetForm();">&nbsp;
		</span>
		<span id="spanQuery2">
			<input class="toolbar_default" type="button"  name="btnQuery2" value="執行查詢" onClick="doSearch();">&nbsp;
		</span>
    </td>
    <td align="right">
        		<span id="spanQuery5">
		<input class="toolbar_default" type="button"  name="btnQuery5" value="離開" onClick="doExit();">&nbsp;
		</span>
	</td>
  </tr>
</table>

<table width="100%" cellpadding="2" cellspacing="0">
	<tr >
		<td class="td_form" width="100px">預查編號</td>
		<td class="td_form_white" width="150px">
			<input type="text" id="prefixNo" name="prefixNo" maxlength="9" size="15" value="<%=obj.getPrefixNo() %>" />
		</td>
		<td class="td_form" width="100px">電子流水號</td>
		<td class="td_form_white">
			<input type="text" id="telixNo" name="telixNo" maxlength="16" size="20" value="<%=obj.getTelixNo() %>" onblur="toUpper(this);" />
		</td>
	</tr>
	<tr>
		<td class="td_form">申請人身分ID</td>
		<td class="td_form_white">
			<input type="text" id="applyId" name="applyId" maxlength="15" size="15" value="<%=obj.getApplyId() %>" onblur="toUpper(this);" />
		</td>
		<td class="td_form">申請人姓名</td>
		<td class="td_form_white">
			<input type="text" class="cmex" id="applyName" name="applyName" maxlength="27" size="27" value="<%=obj.getApplyName() %>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">統一編號</td>
		<td class="td_form_white">
			<input type="text" id="banNo" name="banNo" maxlength="8" size="15" value="<%=obj.getBanNo() %>" />
		</td>
		<td class="td_form">預查名稱</td>
		<td class="td_form_white">
			<input type="text" class="cmex" id="companyName" name="companyName" maxlength="50" size="50" value="<%=obj.getCompanyName() %>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">分文日期</td>
		<td class="td_form_white" colspan="3">
			<input type="text" id="assignDate" name="assignDate" maxlength="7" size="15" value="<%=obj.getAssignDate() %>" />
		</td>
	</tr>
</table>

<c:import url="../common/msgbar.jsp">
<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>

<TABLE width="100%" cellpadding="2" cellspacing="0">
	<tr><td>
	<span id="spanQuery4"><input class="toolbar_default" type="button" name="btnQuery4" value="全部選取">&nbsp;</span>
	<span id="spanQuery5"><input class="toolbar_default" type="button" name="btnQuery5" value="取消選取">&nbsp; </span>
	<span id="spanQuery6"><input class="toolbar_default" type="button" name="btnQuery6" value="確認送出">&nbsp;</span>
	</td></tr>
</TABLE>

<TABLE id="resultTable" class="table_form" border="0" width="100%" cellpadding="2" cellspacing="0">
	<TBODY id="detail">
		<tr>
			<td class=thead width=40>選取</td>
			<td class=thead width=100>預查編號</td>
			<td class=thead width=60>申請人</td>
			<td class=thead width=80>預查種類</td>
			<td class=thead width=80>核覆結果</td>
			<td class=thead width=160>預查名稱</td>
			<td class=thead width=80>特取名稱</td>
			<td class=thead width=80>保留期限</td>
			<td class=thead width=80>公司狀況</td>
			<td class=thead width=80>收文日期</td>
			<td class=thead width=60>承辦人</td>
		</tr>
		<c:forEach var="t" items="<%=obj.doSearch(obj)%>" varStatus="loopStatus">
			<tr class="${loopStatus.index % 2 == 0 ? 'tr_odd' : 'tr_even'}">
				<td style="text-align: center"><input name="selectCases" type="checkbox" value="${t.value.prefixNo}"></td>
				<td>${t.value.prefixNo}</td>
				<td>${t.value.applyName}</td>
				<td>${t.value.applyKind}</td>
				<td>${t.value.approveResult}</td>
				<td>${t.value.companyName}</td>
				<td>${t.value.specialName}</td>
				<td>${t.value.reserveDate}</td>
				<td>${t.value.companyStus}</td>
				<td>${t.value.receiveDate}</td>
				<td>${t.value.staffName}</td>
			</tr>
		</c:forEach>
	</TBODY>
</TABLE>

<input type="hidden" id="hiddenPrefixNos" name="hiddenPrefixNos" value="<%=obj.getHiddenPrefixNos()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<input type="hidden" id="functionName" name="functionName" value="approve">

<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />

</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>