<!DOCTYPE html>
<!-- 
程式目的：人工更改分文
程式代號：pre8005
程式日期：1030702
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8005" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
}else if ("update".equals(obj.getState())) {
	obj.update();
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function init(){
	if ( form1.state.value == "init" )
		document.getElementById("listContainer").style.display = 'none';
	else
		document.getElementById("listContainer").style.display = '';
}

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		form1.q_prefixStart.style.backgroundColor="";
		form1.q_prefixEnd.style.backgroundColor="";
		form1.q_receiveStart.style.backgroundColor="";
		form1.q_receiveEnd.style.backgroundColor="";
		form1.q_assignStart.style.backgroundColor="";
		form1.q_assignEnd.style.backgroundColor="";
		if(form1.q_type[0].checked){
			alertStr += checkEmpty(form1.q_prefixStart,"預查編號起");
			alertStr += checkEmpty(form1.q_prefixEnd,"預查編號迄");
		}else if(form1.q_type[1].checked){
			alertStr += checkEmpty(form1.q_receiveStart,"收文日起");
			alertStr += checkEmpty(form1.q_receiveEnd,"收文日迄");
		}else if(form1.q_type[2].checked){
			alertStr += checkEmpty(form1.q_assignStart,"收文日起");
			alertStr += checkEmpty(form1.q_assignEnd,"收文日迄");
		}else{
			alertStr += "請先點選查詢種類。";
		}
	}else if(form1.state.value=="update"){
		var $checks = $("input[name=checkedPrefixNo]").filter(":checked");
		if($checks.size() < 1)
			alertStr += "請先勾選想更改承辦人的預查案件編號";
	}
	
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

function changeRadio(type) {
    var typeRadio = document.getElementsByName("q_type");
    if(type == 1){
        typeRadio[0].checked = true;
        form1.q_receiveStart.value="";
        form1.q_receiveEnd.value="";
        form1.q_assignStart.value="";
        form1.q_assignEnd.value="";
    }    
    if(type == 2) {
        typeRadio[1].checked = true;
        form1.q_prefixStart.value="";
        form1.q_prefixEnd.value="";
        form1.q_assignStart.value="";
        form1.q_assignEnd.value="";
    }     
    if(type == 3){
    	typeRadio[2].checked = true;
    	form1.q_prefixStart.value="";
        form1.q_prefixEnd.value="";
        form1.q_receiveStart.value="";
        form1.q_receiveEnd.value="";
    }
}

function oneClickCheckAll(obj,cName) { 
    var checkboxs = document.getElementsByName(cName); 
    for(var i=0;i<checkboxs.length;i++){checkboxs[i].checked = obj.checked;} 
} 

function doChangeStaff(object,id) {
	var sel = document.getElementById("idNo");
	document.getElementById(id).innerHTML = object.value;
}

$(document).ready(function() {
	$('#doUpdate').click(function(){
		$('#state').val("update");
		setBeforePageUnload(false);
		if(checkField())	form1.submit();
	});
	$('#doQueryAll').click(function(){
		$('#state').val("queryAll");
		if(checkField())	form1.submit();
	});
});
</script>
</head>
<!-- Form area -->
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8005'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="15%">
				<input type="radio" name="q_type" value="prefix"  <%="prefix".equals( obj.getQ_type() )?"checked":""%>>預查編號起迄：
			</td>
			<td class="td_form_white" width="85%">
				<input class="field_Q" type="text" name="q_prefixStart" size="15" maxlength="9" value="<%=obj.getQ_prefixStart()%>" onFocus="changeRadio(1)">~
				<input class="field_Q" type="text" name="q_prefixEnd" size="15" maxlength="9" value="<%=obj.getQ_prefixEnd()%>" onFocus="changeRadio(1)">
	  		</td>
		</tr>
		<tr>
			<td class="td_form">
				<input type="radio" name="q_type" value="receive"  <%="receive".equals( obj.getQ_type() )?"checked":""%>>收文日期起迄：
			</td>
			<td class="td_form_white">
				<input class="field_Q" type="text" name="q_receiveStart" size="15" maxlength="7" value="<%=obj.getQ_receiveStart()%>" onFocus="changeRadio(2)">~
				<input class="field_Q" type="text" name="q_receiveEnd" size="15" maxlength="7" value="<%=obj.getQ_receiveEnd()%>" onFocus="changeRadio(2)">
		    </td>
		</tr>
		<tr>  
			<td class="td_form">
				<input type="radio" name="q_type" value="assign" <%="assign".equals( obj.getQ_type() )?"checked":""%>>分文日期起迄：
			</td>
			<td class="td_form_white">
				<input class="field_Q" type="text" name="q_assignStart" size="15" maxlength="7" value="<%=obj.getQ_assignStart()%>" onFocus="changeRadio(3)">~
				<input class="field_Q" type="text" name="q_assignEnd" size="15" maxlength="7" value="<%=obj.getQ_assignEnd()%>" onFocus="changeRadio(3)">
				&nbsp;
	   			<input class="toolbar_default" type="button" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)" >&nbsp;
				<input class="toolbar_default" type="button" followPK="false" id="doUpdate" name="doUpdate" value="存　檔" onClick="whatButtonFireEvent(this.name)" >
		    </td>
		</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg">
	<div id="listContainer" height = "200">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
  		    <th class="listTH" width="5%">
    			<input type="checkbox" id="checkAll" name="checkAll" value="Y" onClick="oneClickCheckAll(this,'checkedPrefixNo');">
    		</th>
    		<th class="listTH" width="19%"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">預查編號</a></th>
    		<th class="listTH" width="19%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">承辦人代碼</a></th>
    		<th class="listTH" width="19%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦人姓名</a></th>
    		<th class="listTH" width="19%"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">分文日期</a></th>
    		<th class="listTH" width="19%"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">分文時間</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = {true,false,false,false,false};
  			boolean displayArray[] = {true,true,true,true,true};
  			String[] alignArray = {"center", "center","center","center","center"};
  			out.write(obj.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",true, null, null, "",false, true,0, false, "checkedPrefixNo"));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>