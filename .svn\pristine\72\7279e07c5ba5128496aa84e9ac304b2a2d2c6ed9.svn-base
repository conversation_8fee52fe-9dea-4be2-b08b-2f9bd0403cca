package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmBussItem;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgName;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeDetailDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;
import com.kangdainfo.tcfi.service.Pre4020Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.view.pre.PRE4020;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 一站式案件查詢(PRE4019)
 *
 */
public class Pre4020ServiceImpl implements Pre4020Service {
	public Logger logger = Logger.getLogger(getClass());

	public PRE4020 getDataByTelixNo(String telixNo) {
		PRE4020 o = null;
		if(!"".equals(Common.get(telixNo))) {
			OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
			if(null!=ossmApplMain) {
				String banNo = Common.get(ossmApplMain.getBanNo());
				String processNo = "B";
				if( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossmApplMain.getApplyType()) ) {
					processNo = "I";
				} else {
					processNo = "B";
				}
				String prefixNo = Common.get(ossmApplMain.getPrefixNo());
				if("".equals(prefixNo)) {
					OssmApplFlow flow = ossmApplFlowDao.findByTelixNoAndProcessNo(telixNo, processNo);
					if(null!=flow) {
						prefixNo = Common.get(flow.getRcvCaseNo());
					}
				}
				ossmApplMain.setPrefixNo(prefixNo);

				o = new PRE4020();
				o.setOssmApplMain(ossmApplMain);
				o.setOssmApplFlows(ossmApplFlowDao.findByTelixNo(telixNo));
				o.setOssmFeeMain(ossmFeeMainDao.findByTelixNoAndProcessNo(telixNo, processNo));
				o.setOssmFeeDetail(ossmFeeDetailDao.findByTelixNoAndProcessNo(telixNo, processNo));
				o.setOssmBussItems(getOssmBussItems(telixNo, banNo));
				o.setOssmOrgChange(ossmOrgChangeDao.findByTelixNo(telixNo));
				o.setOssmOrgNames(getOssmOrgNames(telixNo, banNo));
			}
		}
		return o;
	}

	public List<OssmApplMain> getOssmApplMainsByUserid(String userid) {
		return ossmApplMainDao.findByUserid(userid);
	}
	
	private List<OssmBussItem> getOssmBussItems(String telixNo, String banNo) {
		List<OssmBussItem> results = null;
		if(CommonStringUtils.isNotEmpty(telixNo)) {
			results = ossmBussItemDao.findByTelixNo(telixNo);
		}
		if(null==results || results.isEmpty()) {

			if(CommonStringUtils.isNotEmpty(banNo)) {
				List<Cedb2002> list = cedb2002Dao.findByBanNo(banNo);
				if(null!=list && !list.isEmpty()) {
					results = new ArrayList<OssmBussItem>();

					OssmBussItem ossmBussItem = null;
					for(Cedb2002 o : list) {
						ossmBussItem = new OssmBussItem();
						ossmBussItem.setTelixNo(telixNo);
						ossmBussItem.setSeqNo(Common.formatFrontZero(o.getSeqNo(),3));
						ossmBussItem.setBusiItemNo(Common.get(o.getBusiItemNo()).replaceAll("　", ""));
						ossmBussItem.setBusiItem(Common.get(o.getBusiItem()));
						results.add(ossmBussItem);
					}
				}
			}

		}
		return results;
	}
	
	private List<OssmOrgName> getOssmOrgNames(String telixNo, String banNo) {
		List<OssmOrgName> results = null;
		if(CommonStringUtils.isNotEmpty(telixNo)) {
			results = ossmOrgNameDao.findByTelixNo(telixNo);
		}
		if(null==results || results.isEmpty()) {

			if(CommonStringUtils.isNotEmpty(banNo)) {
				Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
				if(null!=cedb2000) {
					results = new ArrayList<OssmOrgName>();
					
					OssmOrgName ossmOrgName = new OssmOrgName();
					ossmOrgName.setTelixNo(telixNo);
					ossmOrgName.setSeqNo(1);
					ossmOrgName.setOrgName(cedb2000.getCompanyName());
					
					results.add(ossmOrgName);
				}
			}

		}
		return results;
	}
	
	private OssmApplMainDao ossmApplMainDao;
	public OssmApplMainDao getOssmApplMainDao() {return ossmApplMainDao;}
	public void setOssmApplMainDao(OssmApplMainDao dao) {this.ossmApplMainDao = dao;}
	
	private OssmApplFlowDao ossmApplFlowDao;
	public OssmApplFlowDao getOssmApplFlowDao() {return ossmApplFlowDao;}
	public void setOssmApplFlowDao(OssmApplFlowDao dao) {this.ossmApplFlowDao = dao;}

	private OssmBussItemDao ossmBussItemDao;
	public OssmBussItemDao getOssmBussItemDao() {return ossmBussItemDao;}
	public void setOssmBussItemDao(OssmBussItemDao dao) {this.ossmBussItemDao = dao;}

	private OssmFeeMainDao ossmFeeMainDao;
	public OssmFeeMainDao getOssmFeeMainDao() {return ossmFeeMainDao;}
	public void setOssmFeeMainDao(OssmFeeMainDao dao) {this.ossmFeeMainDao = dao;}

	private OssmFeeDetailDao ossmFeeDetailDao;
	public OssmFeeDetailDao getOssmFeeDetailDao() {return ossmFeeDetailDao;}
	public void setOssmFeeDetailDao(OssmFeeDetailDao dao) {this.ossmFeeDetailDao = dao;}

	private OssmOrgChangeDao ossmOrgChangeDao;
	public OssmOrgChangeDao getOssmOrgChangeDao() {return ossmOrgChangeDao;}
	public void setOssmOrgChangeDao(OssmOrgChangeDao dao) {this.ossmOrgChangeDao = dao;}

	private OssmOrgNameDao ossmOrgNameDao;
	public OssmOrgNameDao getOssmOrgNameDao() {return ossmOrgNameDao;}
	public void setOssmOrgNameDao(OssmOrgNameDao dao) {this.ossmOrgNameDao = dao;}
	
	private Cedb2000Dao cedb2000Dao;
	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}

	private Cedb2002Dao cedb2002Dao;
	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}

}