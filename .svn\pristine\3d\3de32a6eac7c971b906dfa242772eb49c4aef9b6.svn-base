package com.kangdainfo.tcfi.lucene.bo;

import java.util.List;

public class SearchResult {

	/** 查詢字串(主鍵) */
	private String queryKeyword;
	/** 每頁筆數 */
	private Integer pageSize;
	/** 第幾頁 */
	private Integer pageNum;
	/** 花費時間 */
	private Long cost;
	/** 總筆數 */
	private Integer recordCount;
	/** 共幾頁 */
	private Integer pageCount;
	/** 檢索結果 */
	private List<Hit> hits;
	/** 是否查詢全部資料 */
	private boolean isQueryAll;
	/** 排序欄位 */
	private String sortField;
	/** 排序順序(Y:反向,N:正向) */
	private String sortReverse;

	public String getQueryKeyword() {return queryKeyword;}
	public void setQueryKeyword(String s) {this.queryKeyword = s;}
	public Long getCost() {return cost;}
	public void setCost(Long l) {this.cost = l;}
	public Integer getPageSize() {return pageSize;}
	public void setPageSize(Integer i) {this.pageSize = i;}
	public Integer getPageCount() {return pageCount;}
	public void setPageCount(Integer i) {this.pageCount = i;}
	public Integer getPageNum() {return pageNum;}
	public void setPageNum(Integer i) {this.pageNum = i;}
	public Integer getRecordCount() {return recordCount;}
	public void setRecordCount(Integer i) {this.recordCount = i;}
	public List<Hit> getHits() {return hits;}
	public void setHits(List<Hit> l) {this.hits = l;}
	public boolean isQueryAll() {return isQueryAll;}
	public void setQueryAll(boolean b) {this.isQueryAll = b;}
	public String getSortField() {return sortField;}
	public void setSortField(String s) {this.sortField = s;}
	public String getSortReverse() {return sortReverse;}
	public void setSortReverse(String s) {this.sortReverse = s;}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("SearchResult [");
		builder.append("queryKeyword=").append(queryKeyword);
		builder.append(", pageSize=").append(pageSize);
		builder.append(", pageNum=").append(pageNum);
		builder.append(", cost=").append(cost);
		builder.append(", recordCount=").append(recordCount);
		builder.append(", pageCount=").append(pageCount);
		builder.append(", hits=").append(hits);
		builder.append(", isQueryAll=").append(isQueryAll);
		builder.append(", sortField=").append(sortField);
		builder.append(", sortReverse=").append(sortReverse);
		builder.append("]");
		return builder.toString();
	}

}