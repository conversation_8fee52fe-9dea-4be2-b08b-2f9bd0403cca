<!--
程式目的：全文檢索查詢
程式代號：pre3003
撰寫日期：103.04.03
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3003" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var currentPage = 1;
var pageSize = 45;
var currFocus = -1;

function doSearchCount(tabId,keyword,needLog) {
	var lastTime = new Date().getTime();
	try{
		$.ajax({
			type:"POST",
			async:false,
			url: getVirtualPath() + 'tcfi/pre/pre3003_01.jsp',
			data:"tabId="+tabId+"&q_cmpyName="+encodeURI(keyword)+"&needLog="+needLog,
			cache: false,
			dataType: "json",
			success: function(json) {
				var clientSpendingSS = ' ';
			    var nowTime = new Date().getTime();
			    if(lastTime != ""){
			    	clientSpendingSS = (nowTime-lastTime)%60000;
			    	clientSpendingSS = clientSpendingSS/1000;
			    	var clientSpendingMM = Math.floor((nowTime-lastTime)/60000);
			    	clientSpendingSS = clientSpendingMM*60 + clientSpendingSS;
			    }
			    $('#recordCount').val(json.COUNT);
			    if(json.QUERY_STR.indexOf("系統尚在處理中, 請於隔日再行審核") > -1 )
			    	$('#searchTimeTd').html('<font size="4px" color="red"><b>'+json.QUERY_STR+'</b></font>');
			    else
					$('#searchTimeTd').html('<font color="#000000">搜尋共花費'+clientSpendingSS+'秒, 共<font size="4px" color="#FF0000"><b>'+json.COUNT+'</b></font>筆, 查詢名稱:<b>'+json.QUERY_STR+'</b>');
			}
		});
	}catch(e){
		alert(e.message);
	}
}

function doPagingSearch(tabId,keyword,currentPage,pageSize,sortField,sortReverse) {
	$.blockUI({message:'<img src="../../images/jquery/busy.gif" />',overlayCSS:{backgroundColor:'#ffffff'},css:{backgroundColor:'',border:'0px'}});
	try{
		$.ajax({
			type:"POST",
			async:true,
			url: getVirtualPath() + 'tcfi/pre/pre3003_02.jsp',
			data:"tabId="+tabId+"&q_cmpyName="+encodeURI(keyword)+"&currentPage="+currentPage+"&pageSize="+pageSize+"&sortField="+sortField+"&sortReverse="+sortReverse,
			cache: false,
			dataType: "json",
			success: function(json) {
				for(var i=0;i<json.length;i++) {
					addRow('luceneTable',(i+1)+((currentPage-1)*pageSize),json[i].ID,json[i].INDEX_TYPE,json[i].PREFIX_NO,json[i].BAN_NO,json[i].COMPANY_NAME,json[i].HTML,json[i].CMPY_STATUS,json[i].REVOKE_APP_DATE,json[i].RESERVE_DATE,json[i].APPLY_NAME);
				}
			}
		});
	}catch(e){
		alert(e.message);
	}
	$.unblockUI();
}

function addRow(tblId,seq,indexId,indexType,prefixNo,banNo,companyName,htmlCompanyName,cmpyStatus,revokeAppDate,reserveDate,applyName){
	var tbl = document.getElementById(tblId);
	var lastRow = tbl.rows.length;
	var row = tbl.insertRow(lastRow);
	var trClassName = (seq%2==1)?"listTREven":"listTROdd";
	var tdClassName = (seq%2==1)?"listTDEven":"listTDOdd";
	row.className = trClassName;
	//row.onmouseover = function(){focusRow('luceneTable', seq-1, -1);};
	//row.onmouseout = function(){this.className=trClassName;};
	$(row).bind({
		click: function() {
			currFocus=seq-1;clickRow('luceneTable',seq-1);
		},
		dblclick: function() {
			$('#item-'+(currFocus+1)).click();
		}
	});

	//序號
	var r0 = row.insertCell(0);
	r0.className = tdClassName;
	r0.align = "center";
	r0.width = "40px";
	r0.innerHTML = "<div id=\"row-"+seq+"\">"+seq+"</div><input style=\"display:none;\" type=button id=\"item-"+seq+"\" onclick=\"queryOne('"+seq+"','"+indexType+"','"+indexId+"','"+companyName+"','"+prefixNo+"','"+banNo+"');\" />";
	//預查編號
	var r1 = row.insertCell(1);
	r1.className = tdClassName;
	r1.align = "center";
	r1.width = "99px";
	r1.innerHTML = "<a href=\"#\" onclick=\"queryCase('"+prefixNo+"','prefixNo','"+indexType+"');\" style='text-decoration:none;color:black;'>" + prefixNo + "</a>";
	//統一編號
	var r2 = row.insertCell(2);
	r2.className = tdClassName;
	r2.align = "center";
	r2.width = "99px";
	r2.innerHTML = "<a href=\"#\" onclick=\"queryCase('"+banNo+"','banNo','"+indexType+"');\" style='text-decoration:none;color:black;'>" + banNo + "</a>";
	//公司名稱
	var r3 = row.insertCell(3);
	r3.className = tdClassName;
	r3.align = "left";
	r3.innerHTML = "<b>"+htmlCompanyName+"</b>";
	//公司狀況
	var r4 = row.insertCell(4);
	r4.className = tdClassName;
	r4.align = "left";
	r4.width = "93px";
	r4.innerHTML = cmpyStatus;
	//解/撤/廢止日
	var r5 = row.insertCell(5);
	r5.className = tdClassName;
	r5.align = "left";
	r5.width = "93px";
	r5.innerHTML = '&nbsp;'+revokeAppDate+'&nbsp;';
	//保留期限
	var r6 = row.insertCell(6);
	r6.className = tdClassName;
	r6.align = "left";
	r6.width = "93px";
	r6.innerHTML = '&nbsp;'+reserveDate+'&nbsp;';
	//申請人
	var r7 = row.insertCell(7);
	r7.className = tdClassName;
	r7.align = "left";
	r7.width = "93px";
	r7.innerHTML = applyName+'&nbsp;';
}

function queryOne(seq, indexType, indexId, companyName, prefixNo, banNo) {
	var prop="";
	var width = 800;
	var height = 600;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=0,resizable=1";
	var url = getVirtualPath() + "tcfi/ajax/popBusiItem.jsp?seq="+seq+"&indexType="+indexType+"&id="+indexId+"&companyName="+encodeURI(companyName)+"&prefixNo="+prefixNo+"&banNo="+banNo;
	window.open(url,'popBusiItem',prop);
}

function queryCase(valueNo, searchType, indexType) {
	if(valueNo == "" || searchType == "")	return;
	var prop="";
	var width = 1024;
	var height = 748;
	var top = (window.screen.height - height)/2;
	var left = (window.screen.width - width)/2;

	prop=prop+"width="+width+"px,height="+height+"px,";
	prop=prop+"top="+top+"px,";
	prop=prop+"left="+left+"px,";
	prop=prop+"scrollbars=1,resizable=1";
	var url = "";
	if(searchType == "prefixNo"){
		url = getVirtualPath() + "tcfi/pre/pre4001_00.jsp?prefixNos="+valueNo;
	}
	else if(searchType == "banNo"){
		//TODO 有限合夥公司查詢
		if(indexType == "5")
			url = getVirtualPath() + "tcfi/pre/pre3003_00.jsp?banNos="+valueNo;
		else
			url = getVirtualPath() + "tcfi/pre/pre3008_00.jsp?banNos="+valueNo;
	}
	else 	return;
	window.open(url,'queryCase',prop);
}

function sortLucene(sortField) {
	if( sortField == $('#sortField').val() ) {
		//同欄位點多次
		if( '' == $('#sortReverse').val() ) {
			$('#sortReverse').val("N");
		} else if( 'Y' == $('#sortReverse').val() ) {
			$('#sortReverse').val("N");
		} else if( 'N' == $('#sortReverse').val() ) {
			$('#sortReverse').val("Y");
		}
	} else {
		$('#sortReverse').val("N");
	}

	$('#sortField').val(sortField);

	//clear
	$('#luceneTable').empty();
	//checkfield
	var alertStr="";
	if($('#tabId').val() == "2"){
		alertStr += checkEmpty(form1.q_cmpyName, "預查名稱");
	}else {
		alertStr += checkEmpty(form1.q_cmpyName, "特取名稱／預查名稱");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//reset currentPage
	currentPage = 1;
	//re do search
	doPagingSearch($('#tabId').val(), $('#q_cmpyName').val(), currentPage, pageSize, $('#sortField').val(), $('#sortReverse').val());
}

function resetPage() {
	$('#searchTimeTd').html('');
	$('#luceneTable').empty();
	$('#sortField').val("");
	$('#sortReverse').val("");
	currentPage = 1;
	currFocus = -1;
}

$(document).ready(function() {
	//事件註冊
	$('#doExit').click(function(){
		parent.window.close();
	});
	$('#doPrint').click(function(){
		$('#doPrint').attr('disabled',true);
		//checkfield
		var alertStr="";
		if($('#tabId').val() == "2"){
			alertStr += checkEmpty(form1.q_cmpyName, "預查名稱");
		}else {
			alertStr += checkEmpty(form1.q_cmpyName, "特取名稱／預查名稱");
		}
		if(alertStr.length!=0){
			$('#doPrint').removeAttr('disabled');
			alert(alertStr);
			return false;
		}
		form1.target = "_blank";
		form1.action = "pre3003_03.jsp";
		form1.submit();
		form1.target = "_self";
		form1.action = "";
		$('#doPrint').removeAttr('disabled');
	});
	$('#doClear').click(function(){
		$('#q_cmpyName').val('');
		$('#q_cmpyName').focus();
		$('#doQueryAll').removeAttr('disabled');
		resetPage();
	});
	$('#doQueryAll').click(function(){
		//clear
		resetPage();
		//checkfield
		var alertStr="";
		if($('#tabId').val() == "2"){
			alertStr += checkEmpty(form1.q_cmpyName, "預查名稱");
		}else {
			alertStr += checkEmpty(form1.q_cmpyName, "特取名稱／預查名稱");
		}
		if(alertStr.length!=0){
			$('#doQueryAll').removeAttr('disabled');
			alert(alertStr);
			return false;
		}
		//reset currentPage
		currentPage = 1;
		doSearchCount($('#tabId').val(), $('#q_cmpyName').val());
		doPagingSearch($('#tabId').val(), $('#q_cmpyName').val(), currentPage, pageSize, $('#sortField').val(), $('#sortReverse').val());
		$('#doQueryAll').removeAttr('disabled');
	});

	$("#listContainer").scroll(function() {
		if($('#listContainer').scrollTop() >= ($('#listContainer').height() + (currentPage-1)*1000 ) ) {
			loadPage();
		}
	});

	$('#listContainer').css({"height":"460px"});

	if( $.browser.msie ) {
		if( $.browser.version < 9 ) {
			$("#NOT_IE_Table").hide();
			$("#IE_Table").show();
		} else {
			$("#NOT_IE_Table").show();
			$("#IE_Table").hide();
		}
	} else {
		$("#NOT_IE_Table").show();
		$("#IE_Table").hide();
	}

	document.onkeydown = function() {
		if (event.keyCode == 13) {
			//Enter鍵
			$('#doQueryAll').click();
			return false;
		}
		else if (event.keyCode == 38) {
			//方向鍵:上
			try{
				currFocus = currFocus-1;
				if(currFocus < 0){
					currFocus = 0;
				}
				clickRow('luceneTable',currFocus);
				$("#q_cmpyName").focus();
			}catch(e){alert(e.message);};
		}
		else if (event.keyCode == 40) {
			//方向鍵:下
			try{
				currFocus = currFocus+1;
				if(currFocus > ($('#recordCount').val()-1) ) {
					currFocus = ($('#recordCount').val()-1);
				}
				clickRow('luceneTable',currFocus);
				$("#q_cmpyName").focus();
			}catch(e){alert(e.message);};
		}
	};
});

function clickRow(tblId, curr) {
	try{
		if(isObj(document.getElementById("row-"+(curr+1)))) {
			document.getElementById("row-"+(curr+1)).scrollIntoView(false);
		}

		var tbl = document.getElementById(tblId);
		if(isObj(tbl.rows[curr])) {
			tbl.rows[curr].className='listTRMouseover';
		}
		for(var i=0;i<tbl.rows.length;i++) {
			if(i!=curr) {
				if(isObj(tbl.rows[i])) {
					tbl.rows[i].className=((i+1)%2==1)?"listTREven":"listTROdd";
				}
			}
		}
		if( curr > (currentPage*pageSize-10) ) {
			loadPage();
		}
	}catch(e){
		alert(e.message);
	}
}

function loadPage() {
	if( (currentPage*pageSize) < $('#recordCount').val() ) {
		currentPage++;
		doPagingSearch($('#tabId').val(), $('#q_cmpyName').val(), currentPage, pageSize, $('#sortField').val(), $('#sortReverse').val());
	}
}

function init(){
	if($('#tabId').val() == "2"){
		$('#keywordLabel').html("預查名稱：");
	}else {
		$('#keywordLabel').html("特取名稱／預查名稱：");
	}
	document.body.style.overflow='hidden';
	$('#q_cmpyName').focus();
}
</script>
</head>
<body topmargin="5" onLoad="init();showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post" autocomplete="off" >

<table width="100%" cellspacing="0" cellpadding="0" border="0">

<!-- FORM AREA -->
<tr><td >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%">  
       <tr>
            <td class="td_form" width="200px" id="keywordLabel" style="font-size: 14pt;">預查名稱：</td>
            <td class="td_form_left" >
            	<input class="field_Q cmex" type="text" id="q_cmpyName" name="q_cmpyName" size="25" maxlength="25" value="<%=obj.getQ_cmpyName()%>" />
            </td>
            <td class="td_form_left" >
              	<input class="toolbar_default" type="button" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" />&nbsp;
              	<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="清　除" />&nbsp;
            	<input class="toolbar_default" type="button" followPK="false" id="doPrint" name="doPrint" value="列印全部" />&nbsp;
				<input class="toolbar_default" type="button" followPK="false" id="doExit" name="doExit" value="離　開" />&nbsp;
            </td>
        </tr>
  </table>
  </div>
</td></tr>
<!-- FORM AREA -->

<!-- STATUS AREA -->
<tr><td>
	<table width="100%" height="30px">
	<tr><td><div id="searchTimeTd" style="height:40px;overflow:auto;"></div></td>
		<td style="text-align:right;white-space: nowrap;vertical-align: top;">
			<font size="4" color="<%=com.kangdainfo.tcfi.util.PrefixConstants.COLOR_INDEX_TYPE_3%>"><b>綠色為預查資料</b></font>&nbsp;
			<font size="4" color="<%=com.kangdainfo.tcfi.util.PrefixConstants.COLOR_INDEX_TYPE_1%>"><b>紫色為公司/有限合夥資料</b></font>&nbsp;
           	<font size="4" color="<%=com.kangdainfo.tcfi.util.PrefixConstants.COLOR_INDEX_TYPE_2%>"><b>黃色為已收文資料</b></font>&nbsp;
		</td>
	</tr>
	</table>
</td></tr>
<!-- STATUS AREA -->

<!-- LIST AREA -->
<tr><td class="bgList">
<table width="100%" cellspacing="0" cellpadding="0" id="NOT_IE_Table" style="display:none;" >
	<tr>
		<td class="listTH" width="40px" ><a class="text_link_w">序號</a></td>
		<td class="listTH" width="99px" ><a class="text_link_w" onclick="sortLucene('PREFIX_NO');" href="#">預查編號</a></td>
		<td class="listTH" width="99px" ><a class="text_link_w" onClick="sortLucene('BAN_NO');" href="#">統一編號</a></td>
		<td class="listTH" style="text-align:left;" ><a class="text_link_w" onClick="sortLucene('COMPANY_NAME');" href="#">預查名稱</a></td>
		<td class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('CMPY_STATUS');" href="#">公司狀況</a></td>
		<td class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('REVOKE_APP_DATE');" href="#">解/撤/廢日</a></td>
		<td class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('RESERVE_DATE');" href="#">保留期限</a></td>
		<td class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('APPLY_NAME');" href="#">申請人</a></td>
		<td class="listTH" style="text-align:left;" width="12px" >&nbsp;</td>
	</tr>
</table>
<div id="listContainer" >
<table width="100%" cellspacing="0" cellpadding="0" id="IE_Table">
	<thead id="listTHEAD">
		<tr>
			<th class="listTH" width="40px" ><a class="text_link_w">序號</a></td>
			<th class="listTH" width="99px" ><a class="text_link_w" onclick="sortLucene('PREFIX_NO');" href="#">預查編號</a></th>
			<th class="listTH" width="99px" ><a class="text_link_w" onClick="sortLucene('BAN_NO');" href="#">統一編號</a></th>
			<th class="listTH" style="text-align:left;" ><a class="text_link_w" onClick="sortLucene('COMPANY_NAME');" href="#">預查名稱</a></th>
			<th class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('CMPY_STATUS');" href="#">公司狀況</a></th>
			<th class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('REVOKE_APP_DATE');" href="#">解/撤/廢日</a></th>
			<th class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('RESERVE_DATE');" href="#">保留期限</a></th>
			<th class="listTH" style="text-align:left;" width="93px" ><a class="text_link_w" onClick="sortLucene('APPLY_NAME');" href="#">申請人</a></th>
		</tr>
	</thead>
</table>
<table width="100%" cellspacing="0" cellpadding="0" id="luceneTable" >
</table>
</div>
</td></tr>
<!-- LIST AREA -->

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" style="display:none;">
<input type="hidden" id="indexKind" name="indexKind" value="<%=obj.getIndexKind()%>"/>
<input type="hidden" id="id" name="id" value="<%=obj.getId()%>"/>
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>"/>
<input type="hidden" id="tabId" name="tabId" value="<%=obj.getTabId()%>"/>
<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>"/>
<input type="hidden" id="recordCount" name="recordCount" value="<%=obj.getRecordCount()%>"/>
<input type="hidden" id="sortField" name="sortField" value="<%=obj.getSortField()%>"/>
<input type="hidden" id="sortReverse" name="sortReverse" value="<%=obj.getSortReverse()%>"/>
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

</table>	
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>