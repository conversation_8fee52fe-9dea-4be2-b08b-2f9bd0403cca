package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.DateTimeFormatter;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.crmsmoea.bo.CsyssUser;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb1300;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.di.DiFileMaker;

/**
 * 案件退費資料登入作業下載
 * <AUTHOR>
 * 113/05/13
 */
public class PRE3014 extends SuperBean {
	// 前端
	private String q_prefixNo;
	private String returnChNo;// 退費支票號
	private String refundNo;// 退費公文號，目前沒用到，後續看情況
	private String popErrorMsg;
	private String processType;// 查詢得出的退費類型
	private String noPayMark;// 是否需退費
	private String prefixNo;// 查詢到的預查編號
	
	// 文件參數(DiSetting)
	private int amount;// 退還金
	private String staffName;// 承辦人姓名
	private String staffBranchNo;// 承辦人分機
	private String staffEmail;// 承辦人mail
	private String companyName;// 公司名稱
	private String applyName;// 申請人名稱
	private String sendDate;// 發文日期
	private String returnDate; // 退費日期
	private String attorName; // 代理人
	
	// 定義退費單位為中辦預查科
	private static final String PREFIX_RECEIPT_UNIT = "預查科";

	public String getQ_prefixNo() {
		return checkGet(q_prefixNo);
	}

	public void setQ_prefixNo(String q_prefixNo) {
		this.q_prefixNo = checkSet(q_prefixNo);
	}

	public String getReturnChNo() {
		return checkGet(returnChNo);
	}

	public void setReturnChNo(String returnChNo) {
		this.returnChNo = checkSet(returnChNo);
	}

	public String getRefundNo() {
		return checkGet(refundNo);
	}

	public void setRefundNo(String refundNo) {
		this.refundNo = checkSet(refundNo);
	}
	
	public String getPopErrorMsg() {return checkGet(popErrorMsg);}
	
	public void setPopErrorMsg(String s) {popErrorMsg = checkSet(s);}
	
	public String getProcessType() {return checkGet(processType);}
	
	public void setProcessType(String s) {processType = checkSet(s);}
	
	public String getNoPayMark() {return checkGet(noPayMark);}
	
	public void setNoPayMark(String s) {noPayMark = checkSet(s);}
	
	public String getPrefixNo() {return checkGet(prefixNo);}
	
	public void setPrefixNo(String s) {prefixNo = checkSet(s);}
	
	public int getAmount() {
		return checkGet(amount);
	}

	public void setAmount(int amount) {
		this.amount = checkSet(amount);
	}

	public String getStaffName() {
		return checkGet(staffName);
	}

	public void setStaffName(String staffName) {
		this.staffName = checkSet(staffName);
	}

	public String getStaffBranchNo() {
		return checkGet(staffBranchNo);
	}

	public void setStaffBranchNo(String staffBranchNo) {
		this.staffBranchNo = checkSet(staffBranchNo);
	}

	public String getStaffEmail() {
		return checkGet(staffEmail);
	}

	public void setStaffEmail(String staffEmail) {
		this.staffEmail = checkSet(staffEmail);
	}

	public String getCompanyName() {
		return checkGet(companyName);
	}

	public void setCompanyName(String companyName) {
		this.companyName = checkSet(companyName);
	}

	public String getApplyName() {
		return checkGet(applyName);
	}

	public void setApplyName(String applyName) {
		this.applyName = checkSet(applyName);
	}

	public String getSendDate() {
		return checkGet(sendDate);
	}

	public void setSendDate(String sendDate) {
		this.sendDate = checkSet(sendDate);
	}

	public String getReturnDate() {
		return checkGet(returnDate);
	}

	public void setReturnDate(String returnDate) {
		this.returnDate = checkSet(returnDate);
	}

	public String getAttorName() {
		return checkGet(attorName);
	}

	public void setAttorName(String attorName) {
		this.attorName = checkSet(attorName);
	}

	// 下載di檔
	public File downloadDiFile() {
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		File file = null;
		
		try {
			file = DiFileMaker.generateDIFile(true, user, this, "pre3014.di");
		} catch (MoeaException e) {
			e.printStackTrace();
			this.setErrorMsg(e.getMessage());
		}
		
		return file;
	}

	@Override
	public PRE3014 doQueryOne() throws Exception {
		Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(getQ_prefixNo());
		PRE3014 pre3014 = new PRE3014();
		
		if(cedb1000 == null) {
			pre3014.setErrorMsg("查無資料，請變更查詢條件");
		}else {
			PrefixReceiptNo prefixReceiptNo = null;
        	Eedb1300 eedb1300 = null;
        	
        	pre3014.setPrefixNo(cedb1000.getPrefixNo());
        	pre3014.setSendDate(DateTimeFormatter.ROCTaiwanDateFormatter(Datetime.getYYYMMDD(), true));
        	prefixReceiptNo = ServiceGetter.getInstance().getPre3005Service().selectPrefixReceiptNoByPrefixNo(cedb1000.getPrefixNo());
        	
        	if(prefixReceiptNo == null) {
        		pre3014.setErrorMsg("查無收據資料，請先確認該條件有申請付費收據紀錄");
        	}else {
        		if(prefixReceiptNo.getReturnDate() != null && prefixReceiptNo.getReturnType() != null && prefixReceiptNo.getReturnUser() != null) {
        			pre3014.setStaffName(prefixReceiptNo.getReturnUser());
            		List<Cedbc000> cedbcs = ServiceGetter.getInstance().getPrefixService().getCedbc000ByCondition(null, prefixReceiptNo.getReturnUser(), PREFIX_RECEIPT_UNIT);      		
            		
//    				if(staffMap.containsKey(cedb1000.getIdNo()) && staffMap.get(cedb1000.getIdNo()).equals(cedb1000.getStaffName())) {// 確保預查科沒有同名承辦人
    					pre3014.setStaffBranchNo(cedbcs.get(0).getBranchPhoneNo());
    					// mail部份去公司登記找系統工作人員資料，這部分做法較偏頗，暫時做法。
    					CsyssUser csyssUser = ServiceGetter.getInstance().getPrefixService().getStaffInfoById(cedbcs.get(0).getIdNo());
    					if(csyssUser != null) {
    						pre3014.setStaffEmail(csyssUser.getEmail());
    					}
//    				}
    				pre3014.setReturnDate(DateTimeFormatter.ROCTaiwanDateFormatter(Datetime.getYYYMMDD(prefixReceiptNo.getReturnDate()), false));
    				
    				String returnType = "";
    				if ("01".equals(prefixReceiptNo.getReturnType()))
    					returnType = "檢還";
    				else if ("02".equals(prefixReceiptNo.getReturnType()))
    					returnType = "撤件";
    				else if ("03".equals(prefixReceiptNo.getReturnType()))
    					returnType = "撤回退費";
    				else if ("04".equals(prefixReceiptNo.getReturnType())) 
    					returnType = "退費";
    				
    				pre3014.setProcessType(returnType);
        		}else {
        			pre3014.setErrorMsg("查無收據資料，請先確認該資料有先辦理檢還、撤件、撤回退費");
        		}
        	}
        	
        	if(!"".equals(Common.get(cedb1000.getTelixNo())) && (cedb1000.getTelixNo().startsWith("OSC") || cedb1000.getTelixNo().startsWith("OSS")) && ("".equals(Common.get(pre3014.getErrorMsg())))) {
        		// 一站式案件, 至ossmFeeMain撈取收據資料
				OssmApplMain ossmApplMain = ServiceGetter.getInstance().getPrefixService().getOssmApplMainByTelixNo(cedb1000.getTelixNo());
				if(null != ossmApplMain) {
					String processNo = "B";
				    if ( PrefixConstants.OSSS_APPLY_TYPE_L0100.equals( ossmApplMain.getApplyType()) ) {
				    	processNo = "I";
				    }
				    
	        		OssmFeeMain ossmFeeMainTmp = ServiceGetter.getInstance().getPrefixService().getOssmFeeMainByTelixNoAndProcessNo(cedb1000.getTelixNo(),processNo) ;
	        		
	        		if (ossmFeeMainTmp != null) {
	    				pre3014.setAmount(ossmFeeMainTmp.getAmount());
	    				if (ossmApplMain.getOrgName() == null || "".equals(ossmApplMain.getOrgName())) {
	    					pre3014.setCompanyName(ossmApplMain.getOrgName());
	    				}else {
	    					pre3014.setCompanyName(cedb1000.getOldCompanyName());
	    				}
	        		} 
				}
        	}else {
        		// 線上申辦, 從eedb1300中撈取資料
        		eedb1300 = ServiceGetter.getInstance().getPrefixService().getEedb1300ByTelixNo(cedb1000.getTelixNo());
        	}
        	
        	if(eedb1300 != null && "".equals(Common.get(pre3014.getErrorMsg()))) {
        		pre3014.setAmount(Integer.valueOf(eedb1300.getAmount()));
        		if (eedb1300.getCompanyName() == null || "".equals(eedb1300.getCompanyName())) {
        			pre3014.setCompanyName(cedb1000.getOldCompanyName());
        		}else {
        			pre3014.setCompanyName(eedb1300.getCompanyName());
        		}	
        	}
        	
        	pre3014.setApplyName(cedb1000.getApplyName());
        	pre3014.setAttorName(cedb1000.getAttorName());
        	pre3014.setPopErrorMsg(prefixErrorMsg(cedb1000));
    		//寫入個資軌跡 (查詢類)
         	ServiceGetter.getInstance().getTrackLogService().insertApplyPerson("PR3014", PrefixConstants.TRACK_LOG_SEARCH,
             		cedb1000.getPrefixNo(), cedb1000.getApplyId(), cedb1000.getApplyName(), cedb1000.getApplyTel(), cedb1000.getApplyAddr());
		}
		
		// 若沒有錯誤訊息才返回，否則一律過濾
		if (!"".equals(Common.get(pre3014.getErrorMsg()))) {
			pre3014.setCompanyName("");
			pre3014.setAmount(0);
			pre3014.setApplyName("");
			pre3014.setAttorName("");
			pre3014.setPrefixNo("");
			pre3014.setStaffName("");
			pre3014.setReturnDate("");
		}
		
		return pre3014;
		
	}
	
	/**
	 * 獲取承辦人資料
	 * @param cedbcs
	 * @return Map
	 */
	public Map<String, String> getStaffInfo(List<Cedbc000> cedbcs) {
	    Map<String, String> staffNameMap = new HashMap<>();

	    for (Cedbc000 cedbc : cedbcs) {
	        staffNameMap.put(cedbc.getIdNo(), cedbc.getStaffName());
	    }
	    
	    return staffNameMap;
	}
	
	public String prefixErrorMsg( Cedb1000 cedb1000) {
		if ( PrefixConstants.COMPANY_STATUS_01.equals( cedb1000.getCompanyStus() )
				|| PrefixConstants.COMPANY_STATUS_00.equals( cedb1000.getCompanyStus() )  )
            return "請注意，本案件尚在公司登記中";
        if ( PrefixConstants.COMPANY_STATUS_03.equals( cedb1000.getCompanyStus() ) )
            return "請注意，本案件已完成公司登記";
        // getPrePrefixNo() != null && 
        if ( "".equals(Common.get(cedb1000.getTelixNo()))) 
        	return "請注意，本案件繳費尚未過入帳日，無法取得收據資料!";
        String today = Datetime.getYYYMMDD();
        if (cedb1000.getReserveDate() != null && today.compareTo(cedb1000.getReserveDate()) > 0)
            return "請注意，本案件保留期限已過期，請檢查保留期限！";
        
        return "查詢成功";
	}

	@Override
	public ArrayList doQueryAll() throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void doCreate() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void doUpdate() throws Exception {
		// TODO Auto-generated method stub

	}

	@Override
	public void doDelete() throws Exception {
		// TODO Auto-generated method stub

	}

}
