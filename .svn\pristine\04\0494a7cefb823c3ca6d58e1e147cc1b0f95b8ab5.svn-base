package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesDao;
import com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesRcverDao;
import com.kangdainfo.tcfi.service.Pre8006Service;


public class Pre8006ServiceImpl implements Pre8006Service {
	DeclaratoryStatutesDao declaratoryStatutesDao;
	DeclaratoryStatutesRcverDao declaratoryStatutesRcverDao;
	public DeclaratoryStatutesDao getDeclaratoryStatutesDao() {
		return declaratoryStatutesDao;
	} 
	public void setDeclaratoryStatutesDao( DeclaratoryStatutesDao dao) {
		this.declaratoryStatutesDao = dao;
	}
	public DeclaratoryStatutesRcverDao getDeclaratoryStatutesRcverDao() {
		return declaratoryStatutesRcverDao;
	}
	public void setDeclaratoryStatutesRcverDao( DeclaratoryStatutesRcverDao dao) {
		this.declaratoryStatutesRcverDao = dao;
	}
	
	
	public int doInsert( DeclaratoryStatutes bo1, List<DeclaratoryStatutesRcver> list  ) {
		declaratoryStatutesDao.insert(bo1);
		if ( list != null && list.size() != 0 ) {
			for (int i = 0;i<list.size();i++ ) {
				declaratoryStatutesRcverDao.insert(list.get(i));
			} 
		} 
		return 1;
	}
	
	public int doUpdate( DeclaratoryStatutes oldBo1, DeclaratoryStatutes bo1, List<DeclaratoryStatutesRcver> list  ) {
		declaratoryStatutesDao.set(bo1);
		if (declaratoryStatutesRcverDao.delete(bo1) != 1 )
			declaratoryStatutesRcverDao.delete(oldBo1);
		if ( list != null && list.size() != 0 ) {
			for (int i = 0;i<list.size();i++ ) {
				declaratoryStatutesRcverDao.insert(list.get(i));
			} 
		} 
		return 1;
	}
	
	public int doDelete( DeclaratoryStatutes bo1 ) {
		declaratoryStatutesDao.delete(bo1);
		declaratoryStatutesRcverDao.delete(bo1);
		return 1;
	}
	
	public void doConvert() {
		DeclaratoryStatutes temp = new DeclaratoryStatutes();
		List<DeclaratoryStatutes> dsList = declaratoryStatutesDao.find(temp, null);
		List<DeclaratoryStatutesRcver> dsrList ;
		DeclaratoryStatutes ds;
		DeclaratoryStatutesRcver dsr;
		for ( int i = 0; i<dsList.size();i++ ) {
		    ds = dsList.get(i);
		    System.out.println(i+" "+ds.getRcvNo());
		    String[] rcver1 = Common.get(ds.getReceiveUnit()).split("、");
		    String[] rcver2 = Common.get(ds.getCcUnit()).split("、");
		    dsrList = new ArrayList<DeclaratoryStatutesRcver>();
		    for (int j = 0; j<rcver1.length;j++) {
			   dsr = new DeclaratoryStatutesRcver();
			   dsr.setRcvNo(Common.get(ds.getRcvNo()));
			   dsr.setRcverType("1");
			   dsr.setSeqNo(j);
			   dsr.setRcverOrg(rcver1[j]);
			   dsrList.add(dsr);
		    }
		    for (int j = 0; j<rcver2.length;j++) {
			   dsr = new DeclaratoryStatutesRcver();
			   dsr.setRcvNo(Common.get(ds.getRcvNo()));
			   dsr.setRcverType("2");
			   dsr.setSeqNo(j);
			   dsr.setRcverOrg(rcver2[j]);
			   dsrList.add(dsr);
		    }
		    for (int j =0; j<dsrList.size();j++) {
		    	declaratoryStatutesRcverDao.insert(dsrList.get(j));
		    } 
		}
	}
	
	public void doConvertInstruction() {
		DeclaratoryStatutes temp = new DeclaratoryStatutes();
		List<DeclaratoryStatutes> dsList = declaratoryStatutesDao.find(temp, null);
		List<DeclaratoryStatutesRcver> dsrList ;
		DeclaratoryStatutes ds;
		DeclaratoryStatutesRcver dsr;
		for ( int i = 0; i<dsList.size();i++ ) {
		    ds = dsList.get(i);
		    System.out.println(i+" "+ds.getRcvNo());
		    String[] instructions = Common.get(ds.getInstruction()).split("\n");
		    dsrList = new ArrayList<DeclaratoryStatutesRcver>();
		    for (int j = 0; j<instructions.length;j++) {
			   dsr = new DeclaratoryStatutesRcver();
			   dsr.setRcvNo(Common.get(ds.getRcvNo()));
			   dsr.setRcverType("0");
			   dsr.setSeqNo(j);
			   dsr.setRcverOrg(instructions[j]);
			   dsrList.add(dsr);
		    }
		   
		    for (int j =0; j<dsrList.size();j++) {
		    	declaratoryStatutesRcverDao.insert(dsrList.get(j));
		    } 
		}
	}
}
