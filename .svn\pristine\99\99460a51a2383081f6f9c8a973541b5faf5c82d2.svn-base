package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.RestrictionItem;
import com.kangdainfo.util.lang.CommonStringUtils;

public class RestrictionItemDao extends BaseDaoJdbc implements RowMapper<RestrictionItem> {

	private static final String SQL_findBy = "SELECT a.*, b.business_item FROM RESTRICTION_ITEM a left join busi_item b on a.item_code = b.item_code ";
	public List<RestrictionItem> findByRestrictionId(Integer id) {
		//check fk
		if(id == null) return null;
		SQLJob sqljob = new SQLJob(SQL_findBy + " WHERE a.RESTRICTION_ID = ? order by a.item_code");
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<RestrictionItem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public List<RestrictionItem> findByRestriction() {
		//check fk
		SQLJob sqljob = new SQLJob(SQL_findBy);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<RestrictionItem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public RestrictionItem findByCode(Integer id, String code) {
		//check fk
		if(id == null || "".equals(Common.get(code))) return null;
		SQLJob sqljob = new SQLJob(SQL_findBy + " WHERE a.RESTRICTION_ID = ? AND a.ITEM_CODE = ? ");
		sqljob.addParameter(id);
		sqljob.addParameter(code);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<RestrictionItem> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}

	public RestrictionItem insert(RestrictionItem bo) {
		//check pk
		if(null==bo) return null;
		//check exist
		RestrictionItem t = findByCode(bo.getRestrictionId(), bo.getItemCode());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO RESTRICTION_ITEM (");
		sqljob.appendSQL(" RESTRICTION_ID");
		sqljob.appendSQL(",ITEM_CODE");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(") VALUES (?,?,?,?,?)");
		sqljob.addParameter(bo.getRestrictionId());
		sqljob.addParameter(bo.getItemCode());
		sqljob.addParameter(bo.getModIdNo());
		sqljob.addParameter(bo.getModDate());
		sqljob.addParameter(bo.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
			,new int[]{
				java.sql.Types.NUMERIC,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
			});
		return findByCode(bo.getRestrictionId(), bo.getItemCode());
	}
	
	public void delete(RestrictionItem bo) {
		//check pk
		if(null!=bo && CommonStringUtils.isNotEmpty(Common.get(bo.getId()))) {
			SQLJob sqljob = new SQLJob("DELETE RESTRICTION_ITEM WHERE ID = ? ");
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	public void deleteByRestrictionId(Integer id) {
		//check pk
		if(null!=id) {
			SQLJob sqljob = new SQLJob("DELETE RESTRICTION_ITEM WHERE RESTRICTION_ID = ? ");
			sqljob.addParameter(id);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	public void deleteByRestrictionItemCode(String itemCode) {
		//check pk
		if(!"".equals(Common.get(itemCode))) {
			SQLJob sqljob = new SQLJob("DELETE RESTRICTION_ITEM WHERE ITEM_CODE = ? ");
			sqljob.addParameter(itemCode);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
		}
	}
	
	public void confirmRestrictionItem(String optype, String id, String[] item, String editId){
		RestrictionItem obj = null;
		if("add".equals(optype) && item!=null && item.length>0){
			//saveOrUpdate
			for (int i=0; i<item.length; i++) {
				obj = new RestrictionItem();
				obj.setRestrictionId(Common.getInteger(id));
				obj.setItemCode(item[i]);
				obj.setModIdNo(editId);
				obj.setModDate(Datetime.getYYYMMDD());
				obj.setModTime(Datetime.getHHMMSS());
				insert(obj);
			}
		}else if("remove".equals(optype) && item!=null && item.length>0){
			//delete
			for (int i=0; i<item.length; i++) {
				obj = new RestrictionItem();
				obj.setId(Common.getInteger(item[i]));
				delete(obj);
			}
		}
	}

	public RestrictionItem mapRow(ResultSet rs, int idx) throws SQLException {
		RestrictionItem obj = null;
		if(null!=rs) {
			obj = new RestrictionItem();
			obj.setId(rs.getInt("ID"));
			obj.setRestrictionId(rs.getInt("RESTRICTION_ID"));
			obj.setItemCode(rs.getString("ITEM_CODE"));
			obj.setBusinessItem(rs.getString("BUSINESS_item"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}
}
