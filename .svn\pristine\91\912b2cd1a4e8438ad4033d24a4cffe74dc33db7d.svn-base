package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.SearchLog;

public class SearchLogDao extends BaseDaoJdbc implements RowMapper<SearchLog>{
	
	private static final String SQL_findById = "SELECT * FROM SEARCH_LOG WHERE ID = ? ";	
	public SearchLog query(String id) {
		if(null==id || "".equals(id)) return null;
		SQLJob sqljob = new SQLJob(SQL_findById);
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (SearchLog) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static final String SQL_saveByObj = "INSERT INTO SEARCH_LOG(KIND, KEYWORD, RESULT_NUM, PROCESS_TIME, ID_NO, LOG_DATE, LOG_TIME) "
			+ "VALUES(?, ?, ?, ?, ?, ?, ?)";	
	public int insert(SearchLog obj) {
		if(null==obj) return 0;
		SQLJob sqljob = new SQLJob(SQL_saveByObj);
		sqljob.addParameter(obj.getKind());
		sqljob.addParameter(obj.getKeyWord());
		sqljob.addParameter(obj.getResultNum());
		sqljob.addParameter(obj.getProcessTime());
		sqljob.addParameter(obj.getIdNo());
		sqljob.addParameter(obj.getLogDate());
		sqljob.addParameter(obj.getLogTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}
	
	@Override
	public SearchLog mapRow(ResultSet rs, int arg1) throws SQLException {
		SearchLog obj = null;
		if(null!=rs) {
			obj = new SearchLog();
			obj.setId(rs.getString("ID"));
			obj.setKind(Common.get(rs.getString("KIND")));
			obj.setKeyWord(Common.get(rs.getString("KEYWORD")));
			obj.setResultNum(rs.getInt("RESULT_NUM"));
			obj.setProcessTime(rs.getLong("PROCESS_TIME"));
			obj.setIdNo(Common.get(rs.getString("ID_NO")));
			obj.setLogDate(Common.get(rs.getString("LOG_DATE")));
			obj.setLogTime(Common.get(rs.getString("LOG_TIME")));
		}
		return obj;
	}	
}
