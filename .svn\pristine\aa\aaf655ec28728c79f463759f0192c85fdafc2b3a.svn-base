<%
/**
程式目的：馬上辦更改申請人清冊
程式代號：pre4012
程式日期：1030505
程式作者：Pagan.Chen
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>

<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4012">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4012" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} // end if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report){
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4012.pdf");
		out.clear();
		out = pageContext.pushBody();
	}else{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }	
}
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function init() {
	if($('#state').val() != "init") {
		$('#listContainer').show();
	} 		
}

function queryOne() {
	
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_DateStart,"日期起始時間");
	alertStr += checkEmpty(form1.q_DateEnd,"日期結束時間");
	alertStr += checkDate(form1.q_DateStart,"日期起始時間");
	alertStr += checkDate(form1.q_DateEnd,"日期結束時間");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				if(checkField()){
					$('#state').val("preview") ;
					var target = 'PRE4012_'+randomUUID().replace(/\-/g,"");
					window.open("",target);
					form1.target = target;
					form1.submit();
					form1.target = '';	
				}				
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;
			case "doClear":
				form1.q_DateStart.value = "";
				form1.q_DateEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var dateStart = form1.q_DateStart.value;
		var dateEnd = form1.q_DateEnd.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4012.jsp?dateStart='+dateStart+'&dateEnd='+dateEnd);
		if ( x == 'ok'  )
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}

</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4012'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- Form area -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
		<table class="table_form" width="100%" height="100%">  
			<tr>
				<td class="td_form" width="15%"><font color="red">*</font>馬上辦日期：</td>
				<td class="td_form_white" width="85%">
					起：<%=View.getPopCalendar("field_Q","q_DateStart",obj.getQ_DateStart()) %>
					~迄：<%=View.getPopCalendar("field_Q","q_DateEnd",obj.getQ_DateEnd()) %>
					&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
					&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()">
					&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				</td>
			</tr>     
		</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
	<!--List區============================================================-->
	<tr><td nowrap class="bgList">
	<div id="listContainer" style="display:none;">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  	<thead id="listTHEAD">
  	<tr>
  		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">電子收文號</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查編號</a></th>
    	<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">申請人姓名</a></th>
  	</tr>
  	</thead>
  	<tbody id="listTBODY">
  	<%
  	boolean primaryArray[] = {true,false,false};
  	boolean displayArray[] = {true,true,true};
  	String[] alignArray = {"center", "center","center"};
  	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true"));
  	%>
  	</tbody>
	</table>
	</div>
	</td></tr>


<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>