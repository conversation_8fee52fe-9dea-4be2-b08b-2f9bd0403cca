package com.kangdainfo.common.util;

/**
 * <BR>
 * </i><BR>
 * <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
 * <TR>
 * <TD >簡介 :</TD>
 * <TD>關於字串的函式</TD>
 * </TR>
 * <TR>
 * <TD >使用方法:</TD>
 * <TD>不需建立物件. 直接呼叫 Method</TD>
 * </TR>
 * <TR>
 * <TD >&nbsp</TD>
 * <TD>&nbsp</TD>
 * </TR>
 * </TABLE>
 */
public class StringUtility {
	/**
	 * 將傳入的字串(可含英文字母)數值加一(不可超過99999999)
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>addSerno</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將傳入的字串(可含英文字母)數值加一(不可超過99999999)。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA225698") -> "AA225699"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA089") -> "AA090"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA999") -> "AB001"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >:</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param sSource
	 *            java.lang.String
	 */
	public static String addSerno(String sSource) {
		/* coundn't more than 99999999 已改成long */
		String sNumber = "";
		int iLen = sSource.length();
		boolean flag = true;
		for (int i = (iLen - 1); i >= 0 && flag; i--) {
			char c = sSource.charAt(i);
			if (((int) c >= 48) && ((int) c <= 57)) {
				sNumber = String.valueOf(c) + sNumber;
			} else {
				flag = false;
			}
		}
		if (sNumber.trim().equals("")) {
			return sSource;
		}
		int nNumLen = sNumber.length();
		String sOther = sSource.substring(0, (iLen - nNumLen));
		long lnum = Long.parseLong(sNumber) + 1;
		sNumber = StringUtility.strZero(Long.toString(lnum), nNumLen);
		if (sNumber.length() > nNumLen) {
			sNumber = StringUtility.strZero(1, nNumLen);
			if (!sOther.trim().equals("")) {
				sOther = StringUtility.left(sOther, sOther.length() - 1)
						+ String.valueOf((char) ((long) sOther.charAt(sOther
								.length() - 1) + 1));
			}
		}
		return (sOther + sNumber);
	}
	
	 /**
     * 轉換半型數字成全型
     * @param str String
     * @return String
     */
	public static String transHalf2All(String str) {
		if (null != str && !str.isEmpty()) {
			str = str.replaceAll("0", "０");
			str = str.replaceAll("1", "１");
			str = str.replaceAll("2", "２");
			str = str.replaceAll("3", "３");
			str = str.replaceAll("4", "４");
			str = str.replaceAll("5", "５");
			str = str.replaceAll("6", "６");
			str = str.replaceAll("7", "７");
			str = str.replaceAll("8", "８");
			str = str.replaceAll("9", "９");
		}
		return str;
	}

    /**
     * 轉換全型數字成半型
     * @param str String
     * @return String
     */
	public static String transAll2Half(String str) {
		if (null != str && !str.isEmpty()) {
			str = str.replaceAll("０", "0");
			str = str.replaceAll("１", "1");
			str = str.replaceAll("２", "2");
			str = str.replaceAll("３", "3");
			str = str.replaceAll("４", "4");
			str = str.replaceAll("５", "5");
			str = str.replaceAll("６", "6");
			str = str.replaceAll("７", "7");
			str = str.replaceAll("８", "8");
			str = str.replaceAll("９", "9");
		}
		return str;
	}

	/**
	 * 將傳入的字串(可含英文字母)數值減一(不可超過99999999)
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>addSerno</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將傳入的字串(可含英文字母)數值加一(不可超過99999999)。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA225698") -> "AA225699"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA089") -> "AA090"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>addOne("AA999") -> "AB001"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >:</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param sSource
	 *            java.lang.String
	 */
	public static String minusSerno(String sSource) {
		/* coundn't more than 99999999 已改成long */
		String sNumber = "";
		int iLen = sSource.length();
		boolean flag = true;
		for (int i = (iLen - 1); i >= 0 && flag; i--) {
			char c = sSource.charAt(i);
			if (((int) c >= 48) && ((int) c <= 57)) {
				sNumber = String.valueOf(c) + sNumber;
			} else {
				flag = false;
			}
		}
		if (sNumber.trim().equals("")) {
			return sSource;
		}
		int nNumLen = sNumber.length();
		String sOther = sSource.substring(0, (iLen - nNumLen));
		long lnum = Long.parseLong(sNumber) - 1;
		sNumber = StringUtility.strZero(Long.toString(lnum), nNumLen);
		if (sNumber.length() > nNumLen) {
			sNumber = StringUtility.strZero(1, nNumLen);
			if (!sOther.trim().equals("")) {
				sOther = StringUtility.left(sOther, sOther.length() - 1)
						+ String.valueOf((char) ((long) sOther.charAt(sOther
								.length() - 1) + 1));
			}
		}

		return (sOther + sNumber);
	}

	/**
	 * 將空白或空字串轉為Null
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>chgSpaceToNull</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將空白或空字串轉為Null。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>chgSpaceToNull("") -> null</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>chgSpaceToNull("  ") -> null</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >:</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pValue
	 *            java.lang.String
	 */
	public static String chgSpaceToNull(String pString) {
		if (pString == null) {
			return pString;
		} else {
			if (pString.trim().equals("")) {
				return null;
			} else {
				return pString;
			}
		}
	}

	/**
	 * 代換字串null成空字串
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>chgSpaceToNbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>代換字串null成空字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>chgNullToEmpty(null) -> ""</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >:</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pValue
	 *            java.lang.String
	 * <AUTHOR>
	 */
	public static String chgNullToEmpty(String pString) {
		if (pString != null)
			return pString;
		else
			return "";

	}

	/**
	 * 取回字串左邊某個數目的字元數(中文字算兩個字元)
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>doubleByteLeft</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>取回字串左邊某個數目的字元數(中文字算兩個字元)。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>doubleByteLeft("db2資料庫",5) -> "db2資"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int(要取回的字元數)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param sSource
	 *            java.lang.String
	 * @param iLen
	 *            int
	 */
	public static String doubleByteLeft(String sSource, int iLen) {
		String sReturn = "";
		int nLen = iLen;
		char[] cCharArray = sSource.toCharArray();
		for (int i = 0; i < cCharArray.length; i++) {
			int c = cCharArray[i];
			if ((c >= 0x0001) && (c <= 0x007F)) {
				nLen = nLen - 1;
			} else {
				nLen = nLen - 2;
				if (nLen == -1)
					return sReturn + " ";
			}

			sReturn = sReturn + String.valueOf(cCharArray[i]);
			if (nLen <= 0)
				return sReturn;
		}

		return sReturn;
	}

	/**
	 * 傳回字串長度(Byte數)
	 * 
	 * @return int <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>doubleByteLength</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>傳回字串長度(Byte數)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>doubleByteLength("db2資料庫") -> 9</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>int</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param n
	 *            java.util.String
	 */
	public static int doubleByteLength(String pString) {

		char[] val = pString.toCharArray();
		int utflen = 0;

		for (int i = 0; i < val.length; i++) {
			int c = val[i];
			if ((c >= 0x0001) && (c <= 0x007F)) {
				utflen++;
			} else if (c > 0x07FF) {
				utflen += 2;
			}
		}
		return utflen;
	}

	/**
	 * 取回字串左邊某個數目的字元數(請注意中文字算一個字元)
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>left</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>取回字串左邊某個數目的字元數。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>left("db2資料庫",4) -> "db2資"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int(要取回的字元數)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param sSource
	 *            java.lang.String
	 * @param iLen
	 *            int
	 */
	public static String left(String sSource, int iLen) {
		return (sSource.substring(0,
				iLen < sSource.length() ? iLen : sSource.length()));
	}

	/**
	 * 以指定字串補滿字串左邊至指定的長度(以byte計算)
	 * 
	 * @return java.lang.String[] <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>padL</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>以指定字元補滿字串左邊至指定的長度(以byte計算)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>padL("DB2資料庫","Z",12) -> "ZZZDB2資料庫"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String[]</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int(傳回的字串長度)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pStr
	 *            java.lang.String
	 * @param pSeparator
	 *            java.lang.String
	 * @param pCount
	 *            int
	 */
	public static String padL(String pValue, String pChar, int pCount) {
		int nValueLen = StringUtility.doubleByteLength(pValue);
		if (nValueLen >= pCount)
			return pValue;
		return replicate(pChar, pCount - nValueLen) + pValue;
	}

	/**
	 * 以指定字串補滿字串右邊至指定的長度(以byte計算)
	 * 
	 * @return java.lang.String[] <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>padR</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>以指定字元補滿字串右邊至指定的長度(以byte計算)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>padR("DB2資料庫","Z",12) -> "DB2資料庫ZZZ"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String[]</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int(傳回的字串長度)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pStr
	 *            java.lang.String
	 * @param pSeparator
	 *            java.lang.String
	 * @param pCount
	 *            int
	 */
	public static String padR(String pValue, String pChar, int pCount) {
		int nValueLen = StringUtility.doubleByteLength(pValue);
		if (nValueLen >= pCount)
			return pValue;
		return pValue + replicate(pChar, pCount - nValueLen);
	}

	/**
	 * 代換字串
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>replace</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>代換字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>replace("NAME=12345","NAME=","NAME=Add") -> "NAME=Add12345"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String(來源字串)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>String(欲代換字串)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>String(欲代換字串)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pSource
	 *            java.lang.String
	 * @param pFind
	 *            java.lang.String
	 * @param pReplace
	 *            java.lang.String
	 */
	public static String replace(String pSource, String pFind, String pReplace) {
		StringBuffer sRet = new StringBuffer();
		String sSrc = pSource;
		int n = pSource.indexOf(pFind);
		while (n >= 0) {
			sRet.append(sSrc.substring(0, n));
			sRet.append(pReplace);
			sSrc = sSrc.substring(pFind.length() + n, sSrc.length());
			n = sSrc.indexOf(pFind);
		}

		return sRet.append(sSrc).toString();
	}

	/**
	 * 重覆產生字串
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>replicate</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>重覆產生字串。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>replicate("db",3) -> "dbdbdb"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int(重覆的次數)</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pValue
	 *            java.lang.String
	 * @param pCount
	 *            int
	 */
	public static String replicate(String pChar, int pCount) {
		String sValue = "";
		for (int i = 0; i < pCount; i++) {
			sValue = sValue + pChar;
		}
		return sValue;
	}

	/**
	 * 轉換成字串並在前面補零
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>strZero</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>轉換成字串並在前面補零。</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>strZero(121,5) -> "00121"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>int</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param n
	 *            int
	 * @param length
	 *            int
	 */
	public static String strZero(int n, int length) {
		return strZero(Integer.toString(n), length);
	}

	/**
	 * 將傳入數值依格式轉換成字串
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>strZero</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>轉換成字串並在前面補零</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>strZero("121",5) -> "00121"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>int</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pValue
	 *            java.lang.String
	 * @param pCount
	 *            int
	 */
	public static String strZero(String pValue, int pCount) {
		int nValueLen = StringUtility.doubleByteLength(pValue);
		if (nValueLen >= pCount)
			return pValue;
		int nLength = pCount - nValueLen;
		if (nLength <= 0)
			return pValue;

		return (replicate("0", nLength) + pValue);
	}

	/**
	 * 將傳入數值依格式轉換成字串
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>trans</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將傳入數值依格式轉換成字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>trans(-12345.1268,"###,###.##") -> "-12,345.13"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>double</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param pValue
	 *            double
	 * @param pPic
	 *            java.lang.String
	 */
	public static String trans(double pValue, String pPic) {
		String sResult;
		StringBuffer sFormattedString = new StringBuffer();
		java.text.DecimalFormat mDecimalFormat;
		int iDotPosOfResult, iDotPosOfPic, iLenDiff;
		boolean fIsPercentage = pPic.charAt(pPic.length() - 1) == '%';

		// handle percentage
		if (fIsPercentage) {
			pValue = pValue / 100;
		}
		iDotPosOfPic = pPic.indexOf('.');
		pPic = pPic.replace('9', '#');
		pPic = pPic.replace('x', '#');
		pPic = pPic.replace('X', '#');
		mDecimalFormat = new java.text.DecimalFormat(pPic);
		sResult = ""
				+ mDecimalFormat.format(pValue, sFormattedString,
						new java.text.FieldPosition(0));

		// Handle leading blank
		iDotPosOfResult = sResult.indexOf('.');
		if (iDotPosOfPic == -1) {
			iLenDiff = pPic.length() - sResult.length();
		} else {
			iLenDiff = iDotPosOfResult == -1 ? iDotPosOfPic - sResult.length()
					: iDotPosOfPic - iDotPosOfResult;
			if (fIsPercentage && iDotPosOfResult == -1) {
				iLenDiff++;
			}
		}

		for (int i = 0; i < iLenDiff; i++) {
			sResult = " " + sResult;
		}

		// handle trailing zero
		if (iDotPosOfPic > -1) {
			iDotPosOfResult = sResult.indexOf('.');
			if (fIsPercentage) {
				pPic = pPic.substring(0, pPic.length() - 1);
				sResult = sResult.substring(0, sResult.length() - 1);
			}
			if (iDotPosOfResult == -1) {
				sResult = sResult + ".";
				iLenDiff = pPic.length() - 1 - iDotPosOfPic;
			} else {
				iLenDiff = pPic.length() - iDotPosOfPic - sResult.length()
						+ iDotPosOfResult;
			}
			for (int i = 0; i < iLenDiff; i++) {
				sResult = sResult + "0";
			}
			if (fIsPercentage) {
				pPic = pPic + "%";
				sResult = sResult + "%";
			}
		}
		return (sResult);
	}

	/**
	 * 將七碼的數字字串, 轉成日期格式的字串, 如0931102 ->093/11/02, 空白->空白
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2DateStr</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將七碼的數字字串, 轉成日期格式的字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2DateStr("0931102") -> "093/11/02"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param strSeven
	 *            String
	 */
	public static String str2DateStr(String strSeven)
			throws IllegalArgumentException {
		if (strSeven == null || strSeven.length() == 0) {
			return "";
		}

		StringBuffer sb = new StringBuffer(strSeven.trim());
		IllegalArgumentException iae = new IllegalArgumentException();
		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			throw iae;
		}
		if (sb.length() == 0) {
			return "";
		} else if (sb.length() == 7) {
			sb.insert(3, "/");
			sb.insert(6, "/");
			return sb.toString();
		} else {
			// 格式不合
			throw iae;
		}

	}

	public static String str2DateTwStr(String strSeven)
			throws IllegalArgumentException {
		if (strSeven == null || strSeven.length() == 0) {
			return "";
		}

		StringBuffer sb = new StringBuffer(strSeven.trim());

		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			throw new IllegalArgumentException();
		}
		if (sb.length() == 0) {
			return "";
		} else if (sb.length() == 7) {
			sb.insert(3, "年");
			sb.insert(6, "月");
			sb.append("日");
			return sb.toString();
		} else {
			// 格式不合
			throw new IllegalArgumentException();
		}

	}

	/**
	 * 將日期格式的字串, 轉成七碼的數字字串, 如 093/11/02->0931102
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2DateStr</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將日期格式的字串, 轉成七碼的數字字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>dateStr2Str("093/11/02") -> "0931102"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>dateStr2Str("93/11/02") -> "0931102"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>dateStr2Str("93/9/01") -> "0930901"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>dateStr2Str("93/9/1") -> "0930901"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param dateStr
	 *            String
	 */
	public static String dateStr2Str(String dateStr)
			throws IllegalArgumentException {
		if (dateStr == null || dateStr.length() == 0) {
			return "";
		}
		String str = dateStr.trim();
		IllegalArgumentException iae = new IllegalArgumentException();

		int s1, s2; // 斜線的所在位置

		String yyy = "";
		String mm = "";
		String dd = "";
		s1 = str.indexOf("/");
		s2 = str.lastIndexOf("/");
		// 沒有/傳回原字串

		if (s1 < 0) {
			return dateStr;
		}
		if ((s1 != -1) && (s1 < s2) && (str.length() > s2)) {
			yyy = str.substring(0, s1);
			mm = str.substring(s1 + 1, s2);
			dd = str.substring(s2 + 1);
			try {
				Integer.parseInt(yyy, 10);
				Integer.parseInt(mm, 10);
				Integer.parseInt(dd, 10);
			} catch (Exception ex) {
				// 拿掉/後, 若不是數字, 代表格式不合, 則傳回原字串
				throw iae;
			}
			yyy = strZero(yyy, 3);
			mm = strZero(mm, 2);
			dd = strZero(dd, 2);
			if ((yyy.length() == 3) && (mm.length() == 2) && (dd.length() == 2)) {
				return yyy + mm + dd;
			} else {
				// 長度格式不合
				throw iae;
			}

		} else {
			// 格式不合
			throw iae;
		}
	}

	/**
	 * 將六碼的數字字串, 轉成時間格式的字串, 如141022 ->14:10:22
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2TimeStr</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將六碼的數字字串, 轉成時間格式的字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2TimeStr("141022") -> "14:10:22"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param strSeven
	 *            String
	 */
	public static String str2TimeStr(String strSix)
			throws IllegalArgumentException {
		if (strSix == null || strSix.length() == 0) {
			return "";
		}

		StringBuffer sb = new StringBuffer(strSix.trim());
		IllegalArgumentException iae = new IllegalArgumentException();
		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			throw iae;
		}
		if (sb.length() == 6) {
			sb.insert(2, ":");
			sb.insert(5, ":");
			return sb.toString();
		} else {
			// 長度不合
			throw iae;
		}
	}

	/**
	 * 將時間格式的字串, 轉成六碼的數字字串, 如 14:10:22->141022
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>timeStr2Str</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將時間格式的字串, 轉成六碼的數字字串</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2TimeStr("14:10:22") -> "141022"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2TimeStr("14:9:5") -> "140905"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2TimeStr("6:5:2") -> "060502"</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param timeStr
	 *            String
	 */
	public static String timeStr2Str(String timeStr)
			throws IllegalArgumentException {
		if (timeStr == null || timeStr.length() == 0) {
			return "";
		}
		String str = timeStr.trim();
		IllegalArgumentException iae = new IllegalArgumentException();
		int s1, s2; // 冒號的所在位置

		String hh = "";
		String mm = "";
		String nn = "";
		s1 = str.indexOf(":");
		s2 = str.lastIndexOf(":");
		// 沒有:傳回原字串

		if (s1 < 0) {
			return timeStr;
		}
		if ((s1 != -1) && (s1 < s2) && (str.length() > s2)) {
			hh = str.substring(0, s1);
			mm = str.substring(s1 + 1, s2);
			nn = str.substring(s2 + 1);
			try {
				Integer.parseInt(hh, 10);
				Integer.parseInt(mm, 10);
				Integer.parseInt(nn, 10);
			} catch (Exception ex) {
				// 拿掉:後, 若不是數字, 代表格式不合, 則傳回原字串
				throw iae;
			}
			hh = strZero(hh, 2);
			mm = strZero(mm, 2);
			nn = strZero(nn, 2);
			if ((hh.length() == 2) && (mm.length() == 2) && (nn.length() == 2)) {
				return hh + mm + nn;
			} else {
				// 長度不合, 傳回原字串

				throw iae;
			}

		} else {
			// 格式不合, 傳回原字串

			throw iae;
		}
	}

	/**
	 * 將時間格式的字串, 轉成六碼的數字字串, 如 14:10:22->141022
	 * 
	 * @return java.lang.String <BR>
	 *         </i><BR>
	 */

	public static String str2DateTimeStr(String strOfDate, String strOfTime) {
		try {
			
			return Common.get(StringUtility.str2DateStr(strOfDate)) + "  "
					+ Common.get(StringUtility.str2TimeStr(strOfTime));
		} catch (Exception ex) {
			return "errorFormat";
		}
	}

	/**
	 * 將七碼的數字字串, 轉成整數的西元年份
	 * 
	 * @return int <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2intYear</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將七碼的數字字串, 轉成整數的西元年份</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2intYear("0931102") -> 2004</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param strSeven
	 *            String
	 */
	public static int str2intYear(String strSeven) {
		int year = 0;
		if (strSeven == null || strSeven.length() == 0) {
			return 0;
		}
		StringBuffer sb = new StringBuffer(strSeven.trim());
		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			e.printStackTrace();
		}
		if (sb.length() == 0) {
			return 0;
		} else if (sb.length() == 7) {
			year = 1911 + Integer.parseInt(sb.substring(0, 3), 10);
			return year;
		} else {
			// 格式不合
			return 0;
		}
	}

	/**
	 * 將七碼的數字字串, 轉成整數的月份
	 * 
	 * @return int <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2intMonth</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將七碼的數字字串, 轉成整數的西元月份</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2intMonth("0931102") ->11</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param strSeven
	 *            String
	 */
	public static int str2intMonth(String strSeven) {
		int month = 0;
		if (strSeven == null || strSeven.length() == 0) {
			return 0;
		}
		StringBuffer sb = new StringBuffer(strSeven.trim());
		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			e.printStackTrace();
		}
		if (sb.length() == 0) {
			return 0;
		} else if (sb.length() == 7) {
			// 格林威治時間的月份從0開始
			month = Integer.parseInt(sb.substring(3, 5), 10);
			return month - 1;
		} else {
			// 格式不合
			return 0;
		}

	}

	/**
	 * 將七碼的數字字串, 轉成整數的天
	 * 
	 * @return int <BR>
	 *         </i><BR>
	 *         <TABLE BORDER=5 CELLPADDING=5 CELLSPACING=0 bgColor=lightblue FONT SIZE=2 FACE=細明體 COLOR=Blue>
	 *         <TR>
	 *         <TD >Method :</TD>
	 *         <TD>str2intDay</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Usage :</TD>
	 *         <TD>將七碼的數字字串, 轉成整數的天</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Example :</TD>
	 *         <TD>str2intDay("0931102") ->2</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Parameter :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >&nbsp</TD>
	 *         <TD>&nbsp</TD>
	 *         </TR>
	 *         <TR>
	 *         <TD >Return :</TD>
	 *         <TD>String</TD>
	 *         </TR>
	 *         </TABLE>
	 * @param strSeven
	 *            String
	 */
	public static int str2intDay(String strSeven) {
		int day = 0;
		if (strSeven == null || strSeven.length() == 0) {
			return 0;
		}
		StringBuffer sb;
		try {
			// 為免日期格式是有斜線的, 先做一次轉換

			sb = new StringBuffer(dateStr2Str(strSeven.trim()));
		} catch (Exception ex) {
			ex.printStackTrace();
			return 0;
		}
		try {
			Integer.parseInt(sb.toString(), 10);
		} catch (Exception e) {
			// 包含非數字, 格式不合
			e.printStackTrace();
			return 0;
		}
		if (sb.length() == 0) {
			return 0;
		} else if (sb.length() == 7) {
			day = Integer.parseInt(sb.substring(5, 7), 10);
			return day;
		} else {
			// 格式不合
			return 0;
		}
	}

	/**
	 * 轉Unicode編碼字串(&#xxxxx;)回Unicode
	 * 
	 * @param s
	 *            String
	 * @return String
	 */
	public static String replaceCharacterEntityToUnicode(String s) {
		if (s == null)
			return s;

		s = replaceSomeHtmlCharacterEntity(s);
		StringBuffer source = new StringBuffer(s);
		StringBuffer result = new StringBuffer();

		int iIndex = 0;
		do {
			int iSign = iIndex;
			iIndex = source.indexOf("&#", iIndex);

			if (iIndex < 0) {
				result.append(source.substring(iSign));
				break;
			}

			if (iIndex > iSign) {
				result.append(source.substring(iSign, iIndex));
			}

			int iIndex2 = source.indexOf(";", iIndex + 2);
			try {
				int iWord = Integer.parseInt(source.substring((iIndex + 2),
						iIndex2));
				char c = (char) iWord;
				result.append(c);
			} catch (Exception e) {
			}
			iIndex = iIndex2 + 1;
		} while (true);

		return result.toString();
	}

	private static String replaceSomeHtmlCharacterEntity(String s) {
		String[] source = new String[] { "&nbsp;", "&lt;", "&gt;", "&amp;",
				"&quot;" };
		String[] result = new String[] { " ", "<", ">", "&", "\"" };

		for (int i = 0; i < source.length; i++) {
			if (s.indexOf(source[i]) >= 0) {
				s = s.replaceAll(source[i], result[i]);
			}
		}
		return s;
	}

	static final String[] asciiTable = { " ", "!", "\"", "#", "$", "%", "&",
			"'", "(", ")", "*", "+", ",", "-", ".", "/", "0", "1", "2", "3",
			"4", "5", "6", "7", "8", "9", ":", ";", "<", "=", ">", "?", "@",
			"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
			"N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
			"[", "\\", "]", "^", "_", "`", "a", "b", "c", "d", "e", "f", "g",
			"h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t",
			"u", "v", "w", "x", "y", "z", "{", "|", "}", "~" };
	static final String[] doubleByteTable = { "　", "！", "”", "＃", "＄", "％",
			"＆", "’", "（", "）", "＊", "＋", "，", "－", "‧", "／", "０", "１", "２",
			"３", "４", "５", "６", "７", "８", "９", "：", "；", "＜", "＝", "＞", "？",
			"＠", "Ａ", "Ｂ", "Ｃ", "Ｄ", "Ｅ", "Ｆ", "Ｇ", "Ｈ", "Ｉ", "Ｊ", "Ｋ", "Ｌ",
			"Ｍ", "Ｎ", "Ｏ", "Ｐ", "Ｑ", "Ｒ", "Ｓ", "Ｔ", "Ｕ", "Ｖ", "Ｗ", "Ｘ", "Ｙ",
			"Ｚ", "〔", "＼", "〕", "︿", "＿", "｀", "ａ", "ｂ", "ｃ", "ｄ", "ｅ", "ｆ",
			"ｇ", "ｈ", "ｉ", "ｊ", "ｋ", "ｌ", "ｍ", "ｎ", "ｏ", "ｐ", "ｑ", "ｒ", "ｓ",
			"ｔ", "ｕ", "ｖ", "ｗ", "ｘ", "ｙ", "ｚ", "｛", "｜", "｝", "～" };

	public static String convertFullorHalf(String originalStr, int option) {

		if (asciiTable.length == doubleByteTable.length) {
			if (originalStr == null || "".equalsIgnoreCase(originalStr)) {
				return "";
			}
			for (int i = 0; i < asciiTable.length; i++) {
				if (option == 0)// 半形
				{
					originalStr = originalStr.replace(doubleByteTable[i],
							asciiTable[i]);
				}
				if (option == 1) {
					originalStr = originalStr.replace(asciiTable[i],
							doubleByteTable[i]);
				}
			}
			return originalStr;
		} else {
			return originalStr;
		}
	}
}
