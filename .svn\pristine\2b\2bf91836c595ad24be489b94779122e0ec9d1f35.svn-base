<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE1008"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String yearMonth = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("yearMonth")));
try {
	if ( ( yearMonth != null && !"".equals(yearMonth) ) )
	{
		String checkResult = PRE1008.checkForjsp(yearMonth);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>