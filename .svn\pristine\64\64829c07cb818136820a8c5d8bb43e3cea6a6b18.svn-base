package com.kangdainfo.tcfi.view.pre;
/* 
程式目的：每月收文預查編號統計表
程式代號：pre1008
程式日期：1030528
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE1008 extends SuperBean {
	
	private String q_yearMonth ;
	
	private String total ;
	private String startNum ;
	private String endNum ;
	private String day ;
	
	public String getQ_yearMonth() {return checkGet(q_yearMonth);}
	public void setQ_yearMonth(String s) {q_yearMonth = checkSet(s);}
	
	public String getTotal() {return checkGet(total);}
	public void setTotal(String s) {total = checkSet(s);}
	public String getStartNum() {return checkGet(startNum);}
	public void setStartNum(String s) {startNum = checkSet(s);}
	public String getEndNum() {return checkGet(endNum);}
	public void setEndNum(String s) {endNum = checkSet(s);}
	public String getDay() {return checkGet(day);}
	public void setDay(String s) {day = checkSet(s);}
	//-----------------------------------------------------------------------------------------------------
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		return null ;
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
	    return null ;
    } // doQueryAll()

	public static SQLJob doCheck(String yearMonth) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(" select count(1) as ww from eicm.cedb1000 where prefix_no like '"
				+ yearMonth.substring(0, 3)
				+ "%' and receive_date like '"
				+ yearMonth + "%' ");
		return sqljob;
	} // doAppendSqljob()

	public File doPrintPdf() throws Exception {
		try {
	        File report = null ;
	        String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre1008_1.jasper");
		    Map<String, Object> parameters = new HashMap<String,Object>();
	        String yearMonth = getQ_yearMonth() ;
	        parameters.put("year", yearMonth.substring(0,3)  ) ;
	        parameters.put("month", yearMonth.substring(3)  ) ;
	        parameters.put("yearMonth", yearMonth.substring(0,3)+"/"+yearMonth.substring(3));
	        parameters.put("printDate", "列印日期："+Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印日期
		    parameters.put("printTime", "列印時間："+Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間

		    SQLJob sqljob = new SQLJob();
		    sqljob.appendSQL("SELECT");
		    sqljob.appendSQL("z.yyymmdd ,z.day ,min(z.prefix_no) as startNum, max(z.prefix_no) as endNum, to_char(count(1)) as total");
		    sqljob.appendSQL("from (");
		    sqljob.appendSQL("  select md.day ,md.yyymmdd ,a.prefix_no");
		    sqljob.appendSQL("  from (");
		    sqljob.appendSQL("    select");
		    sqljob.appendSQL("      substr(to_char(last_day(add_months(to_date(?+191100,'yyyyMM'), -1))+1+rownum-1,'ds'),6) as day");
		    sqljob.appendSQL("      ,to_char(last_day(add_months(to_date(?+191100,'yyyyMM'), -1))+1+rownum-1,'yyyyMMdd')-19110000 as yyymmdd");
		    sqljob.appendSQL("    from dual");
		    sqljob.appendSQL("    connect by");
		    sqljob.appendSQL("    rownum <= last_day(to_date(?+191100,'yyyyMM'))-(last_day(add_months(to_date(?+191100,'yyyyMM'), -1))+1)+1");
		    sqljob.appendSQL("  ) md, eicm.cedb1000 a");
		    sqljob.appendSQL("  where a.receive_date = md.yyymmdd");
		    sqljob.appendSQL(") z");
		    sqljob.appendSQL("group by yyymmdd, day");
		    sqljob.appendSQL("order by yyymmdd, day");
		    sqljob.addParameter(yearMonth);
		    sqljob.addParameter(yearMonth);
		    sqljob.addParameter(yearMonth);
		    sqljob.addParameter(yearMonth);

		    List<?> dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	        report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	        return report ;
		} // try
		catch( Exception e ) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<300) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("報表製作失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
			return null ;
		} // catch
	} // doPrintfPdf()		
	
	public static String checkForjsp(String yearMonth) throws Exception{
		List<Map<String,Object>> check = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList( doCheck( yearMonth ) );
		if ("0".equals( Common.get( check.get(0).get("ww") ) ) ) {
			return yearMonth+"月沒有預查收文資料!";
		} // if	
		else 
			return "ok";
	} // check
	
} // PRE1008