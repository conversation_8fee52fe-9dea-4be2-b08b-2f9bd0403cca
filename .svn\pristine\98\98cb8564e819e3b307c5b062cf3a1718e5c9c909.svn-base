package com.kangdainfo;

public class ServiceGetter {

	private static ServiceGetter instance;

    public ServiceGetter() {
        if(ServiceGetter.instance != null) {
            throw new RuntimeException(
                    this.getClass().getName()
                            + "is designed to be a Singleton, the instance already exist:"
                            + ServiceGetter.instance);
        }
        ServiceGetter.instance = this;
    }

    public static ServiceGetter getInstance() {
        return instance;
    }

	public com.kangdainfo.web.listener.MyServletContext getMyServletContext() {
		return com.kangdainfo.web.listener.MyServletContext.getInstance();
	}

	private String env;
	private com.kangdainfo.common.service.AuthenticationService authenticationService;
	private com.kangdainfo.web.util.WebContextInfo webContextInfo;
	private com.kangdainfo.tcfi.service.PrefixService prefixService;
	private com.kangdainfo.tcfi.service.Com0001Service com0001Service;
	private com.kangdainfo.tcfi.service.Pre0004Service pre0004Service;
	private com.kangdainfo.tcfi.service.Pre1001Service pre1001Service;
	private com.kangdainfo.tcfi.service.Pre2001Service pre2001Service;
	private com.kangdainfo.tcfi.service.Pre2003Service pre2003Service;
	private com.kangdainfo.tcfi.service.Pre2004Service pre2004Service;
	private com.kangdainfo.tcfi.service.Pre2008Service pre2008Service;
	private com.kangdainfo.tcfi.service.Pre3005Service pre3005Service;
	private com.kangdainfo.tcfi.service.Pre3007Service pre3007Service;
	private com.kangdainfo.tcfi.service.Pre3008Service pre3008Service;
	private com.kangdainfo.tcfi.service.Pre3013Service pre3013Service;
	private com.kangdainfo.tcfi.service.Pre4001Service pre4001Service;
	private com.kangdainfo.tcfi.service.Pre4006Service pre4006Service;
	private com.kangdainfo.tcfi.service.Pre4013Service pre4013Service;
	private com.kangdainfo.tcfi.service.Pre4020Service pre4020Service;
	private com.kangdainfo.tcfi.service.Pre4022Service pre4022Service;//2024/03/28 新增
	private com.kangdainfo.tcfi.service.Pre8005Service pre8005Service;
	private com.kangdainfo.tcfi.service.Pre8006Service pre8006Service;
	private com.kangdainfo.tcfi.service.Pre8010Service pre8010Service;
	private com.kangdainfo.tcfi.service.Pre8011Service pre8011Service;
	private com.kangdainfo.tcfi.service.Pre8012Service pre8012Service;
	private com.kangdainfo.tcfi.service.Pre8018Service pre8018Service;
	private com.kangdainfo.tcfi.service.Pre5001Service pre5001Service;
	private com.kangdainfo.tcfi.service.Pre5002Service pre5002Service;
	private com.kangdainfo.tcfi.service.Pre9005Service pre9005Service;
	private com.kangdainfo.tcfi.service.SameNameCompareService sameNameCompareService;
	private com.kangdainfo.tcfi.service.ApproveService approveService;
	private com.kangdainfo.tcfi.service.BackupService backupService;
	private com.kangdainfo.tcfi.service.EncapsulateService encapsulateService;
	private com.kangdainfo.tcfi.service.UpdateOsssStatusService updateOsssStatusService;
	private com.kangdainfo.tcfi.service.TrackLogService trackLogService;
	private com.kangdainfo.tcfi.service.CaseFlowService caseFlowService;
	private com.kangdainfo.tcfi.service.NoPayMarkService noPayMarkService;
	private com.kangdainfo.tcfi.service.MoeaicApproveService moeaicApproveService;
	private com.kangdainfo.tcfi.service.CorrectDataService correctDataService;

	//Query DAO
	private com.kangdainfo.tcfi.model.eedb.dao.EedbGeneralQueryDao eedbGeneralQueryDao;
	private com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao eicmGeneralQueryDao;
	private com.kangdainfo.tcfi.model.osss.dao.OsssGeneralQueryDao osssGeneralQueryDao;
	private com.kangdainfo.tcfi.model.icms.dao.IcmsGeneralQueryDao icmsGeneralQueryDao;

	//Loader
	private com.kangdainfo.tcfi.loader.SystemNewsLoader systemNewsLoader;
	private com.kangdainfo.tcfi.loader.Cedbc058CodeLoader cedbc058CodeLoader;
	private com.kangdainfo.tcfi.loader.SystemCode01Loader systemCode01Loader;
	private com.kangdainfo.tcfi.loader.SystemCode02Loader systemCode02Loader;
	private com.kangdainfo.tcfi.loader.SystemCode03Loader systemCode03Loader;
	private com.kangdainfo.tcfi.loader.SystemCode04Loader systemCode04Loader;
	private com.kangdainfo.tcfi.loader.SystemCode05Loader systemCode05Loader;
	private com.kangdainfo.tcfi.loader.SystemCode06Loader systemCode06Loader;
	private com.kangdainfo.tcfi.loader.SystemCode07Loader systemCode07Loader;
	private com.kangdainfo.tcfi.loader.SystemCode08Loader systemCode08Loader;
	private com.kangdainfo.tcfi.loader.SystemCode09Loader systemCode09Loader;
	private com.kangdainfo.tcfi.loader.SystemCode10Loader systemCode10Loader;
	private com.kangdainfo.tcfi.loader.SystemCode11Loader systemCode11Loader;
	private com.kangdainfo.tcfi.loader.SystemCode12Loader systemCode12Loader;
	private com.kangdainfo.tcfi.loader.SystemCode13Loader systemCode13Loader;
	private com.kangdainfo.tcfi.loader.SystemCode14Loader systemCode14Loader;
	private com.kangdainfo.tcfi.loader.SystemCode15Loader systemCode15Loader;
	private com.kangdainfo.tcfi.loader.Cedbc000CodeLoader cedbc000CodeLoader;
	private com.kangdainfo.tcfi.loader.FunctionMenuLoader functionMenuLoader;
	private com.kangdainfo.tcfi.loader.LmsdCodemappingOrgLoader lmsdCodemappingOrgLoader;// 2024/04/16 新增
	private com.kangdainfo.tcfi.loader.LmsdCodemappingStatLoader lmsdCodemappingStatLoader;// 2024/04/17 新增
	private com.kangdainfo.tcfi.loader.LmsdRegUnitLoader lmsdRegUnitLoader;// 2024/04/17 新增
	
	//Lucene
	private com.kangdainfo.tcfi.lucene.service.IndexSearchService indexSearchService;
	private com.kangdainfo.tcfi.lucene.service.IndexCreateService indexCreateService;
	private com.kangdainfo.tcfi.lucene.service.IndexUpdateService indexUpdateService;
	
	//test
	private com.kangdainfo.tcfi.service.TestService testService;

	public com.kangdainfo.common.service.AuthenticationService getAuthenticationService() {return authenticationService;}
	public void setAuthenticationService(com.kangdainfo.common.service.AuthenticationService service) {this.authenticationService = service;}
	public com.kangdainfo.web.util.WebContextInfo getWebContextInfo() {return webContextInfo;}
	public void setWebContextInfo(com.kangdainfo.web.util.WebContextInfo webContextInfo) {this.webContextInfo = webContextInfo;}		
	public com.kangdainfo.tcfi.service.PrefixService getPrefixService() {return prefixService;}
	public void setPrefixService(com.kangdainfo.tcfi.service.PrefixService service) {this.prefixService = service;}
	public com.kangdainfo.tcfi.service.Com0001Service getCom0001Service() {return com0001Service;}
	public void setCom0001Service(com.kangdainfo.tcfi.service.Com0001Service service) {this.com0001Service = service;}
	public com.kangdainfo.tcfi.service.Pre0004Service getPre0004Service() {return pre0004Service;}
	public void setPre0004Service(com.kangdainfo.tcfi.service.Pre0004Service service) {this.pre0004Service = service;}
	public com.kangdainfo.tcfi.service.Pre1001Service getPre1001Service() {return pre1001Service;}
	public void setPre1001Service(com.kangdainfo.tcfi.service.Pre1001Service service) {this.pre1001Service = service;}
	public com.kangdainfo.tcfi.service.Pre2001Service getPre2001Service() {return pre2001Service;}
	public void setPre2001Service(com.kangdainfo.tcfi.service.Pre2001Service service) {this.pre2001Service = service;}
	public com.kangdainfo.tcfi.service.Pre2003Service getPre2003Service() {return pre2003Service;}
	public void setPre2003Service(com.kangdainfo.tcfi.service.Pre2003Service service) {this.pre2003Service = service;}
	public com.kangdainfo.tcfi.service.Pre2004Service getPre2004Service() {return pre2004Service;}
	public void setPre2004Service(com.kangdainfo.tcfi.service.Pre2004Service service) {this.pre2004Service = service;}
	public com.kangdainfo.tcfi.service.Pre3005Service getPre3005Service() {return pre3005Service;}
	public void setPre3005Service(com.kangdainfo.tcfi.service.Pre3005Service service) {this.pre3005Service = service;}
	public com.kangdainfo.tcfi.service.Pre3007Service getPre3007Service() {return pre3007Service;}
	public void setPre3007Service(com.kangdainfo.tcfi.service.Pre3007Service service) {this.pre3007Service = service;}
	public com.kangdainfo.tcfi.service.Pre3008Service getPre3008Service() {return pre3008Service;}
	public void setPre3008Service(com.kangdainfo.tcfi.service.Pre3008Service service) {this.pre3008Service = service;}
	public com.kangdainfo.tcfi.service.Pre3013Service getPre3013Service() {return pre3013Service;}
	public void setPre3013Service(com.kangdainfo.tcfi.service.Pre3013Service service) {this.pre3013Service = service;}
	public com.kangdainfo.tcfi.service.Pre4001Service getPre4001Service() {return pre4001Service;}
	public void setPre4001Service(com.kangdainfo.tcfi.service.Pre4001Service service) {this.pre4001Service = service;}
	public com.kangdainfo.tcfi.service.Pre4006Service getPre4006Service() {return pre4006Service;}
	public void setPre4006Service(com.kangdainfo.tcfi.service.Pre4006Service service) {this.pre4006Service = service;}
	public com.kangdainfo.tcfi.service.Pre4013Service getPre4013Service() {return pre4013Service;}
	public void setPre4013Service(com.kangdainfo.tcfi.service.Pre4013Service service) {this.pre4013Service = service;}
	public com.kangdainfo.tcfi.service.Pre4020Service getPre4020Service() {return pre4020Service;}
	public void setPre4020Service(com.kangdainfo.tcfi.service.Pre4020Service service) {this.pre4020Service = service;}
	public com.kangdainfo.tcfi.service.Pre4022Service getPre4022Service() {return pre4022Service;}// 2024/03/27 新增
	public void setPre4022Service(com.kangdainfo.tcfi.service.Pre4022Service service) {this.pre4022Service = service;}// 2024/03/27 新增
	public com.kangdainfo.tcfi.service.Pre2008Service getPre2008Service() {return pre2008Service;}
	public void setPre2008Service(com.kangdainfo.tcfi.service.Pre2008Service service) {this.pre2008Service = service;}
	public com.kangdainfo.tcfi.service.Pre8005Service getPre8005Service() {return pre8005Service;}
	public void setPre8005Service(com.kangdainfo.tcfi.service.Pre8005Service service) {this.pre8005Service = service;}
	public com.kangdainfo.tcfi.service.Pre8006Service getPre8006Service() {return pre8006Service;}
	public void setPre8006Service(com.kangdainfo.tcfi.service.Pre8006Service service) {this.pre8006Service = service;}
	public com.kangdainfo.tcfi.service.Pre8010Service getPre8010Service() {return pre8010Service;}
	public void setPre8010Service(com.kangdainfo.tcfi.service.Pre8010Service service) {this.pre8010Service = service;}
	public com.kangdainfo.tcfi.service.Pre8011Service getPre8011Service() {return pre8011Service;}
	public void setPre8011Service(com.kangdainfo.tcfi.service.Pre8011Service service) {this.pre8011Service = service;}
	public com.kangdainfo.tcfi.service.Pre8012Service getPre8012Service() {return pre8012Service;}
	public void setPre8012Service(com.kangdainfo.tcfi.service.Pre8012Service service) {this.pre8012Service = service;}
	
	public com.kangdainfo.tcfi.service.Pre8018Service getPre8018Service() {return pre8018Service;}
	public void setPre8018Service(com.kangdainfo.tcfi.service.Pre8018Service service) {this.pre8018Service = service;}
	
	public com.kangdainfo.tcfi.service.Pre5001Service getPre5001Service() {return pre5001Service;}
	public void setPre5001Service(com.kangdainfo.tcfi.service.Pre5001Service service) {this.pre5001Service = service;}
	
	public com.kangdainfo.tcfi.service.Pre5002Service getPre5002Service() {return pre5002Service;}
	public void setPre5002Service(com.kangdainfo.tcfi.service.Pre5002Service service) {this.pre5002Service = service;}
	
	public com.kangdainfo.tcfi.service.Pre9005Service getPre9005Service() {return pre9005Service;}
	public void setPre9005Service(com.kangdainfo.tcfi.service.Pre9005Service service) {this.pre9005Service = service;}
	public com.kangdainfo.tcfi.service.SameNameCompareService getSameNameCompareService() {return sameNameCompareService;}
	public void setSameNameCompareService(com.kangdainfo.tcfi.service.SameNameCompareService service) {this.sameNameCompareService = service;}
	public com.kangdainfo.tcfi.service.TrackLogService getTrackLogService() {return trackLogService;}
	public void setTrackLogService(com.kangdainfo.tcfi.service.TrackLogService trackLogService) {this.trackLogService = trackLogService;}
	
	public com.kangdainfo.tcfi.model.eedb.dao.EedbGeneralQueryDao getEedbGeneralQueryDao() {return eedbGeneralQueryDao;}
	public void setEedbGeneralQueryDao(com.kangdainfo.tcfi.model.eedb.dao.EedbGeneralQueryDao dao) {this.eedbGeneralQueryDao = dao;}
	public com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao getEicmGeneralQueryDao() {return eicmGeneralQueryDao;}
	public void setEicmGeneralQueryDao(com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao dao) {this.eicmGeneralQueryDao = dao;}
	public com.kangdainfo.tcfi.model.osss.dao.OsssGeneralQueryDao getOsssGeneralQueryDao() {return osssGeneralQueryDao;}
	public void setOsssGeneralQueryDao(com.kangdainfo.tcfi.model.osss.dao.OsssGeneralQueryDao dao) {this.osssGeneralQueryDao = dao;}
	public com.kangdainfo.tcfi.model.icms.dao.IcmsGeneralQueryDao getIcmsGeneralQueryDao() {return icmsGeneralQueryDao;}
	public void setIcmsGeneralQueryDao(com.kangdainfo.tcfi.model.icms.dao.IcmsGeneralQueryDao dao) {this.icmsGeneralQueryDao = dao;}
	/** 系統公告 **/
	public com.kangdainfo.tcfi.loader.SystemNewsLoader getSystemNewsLoader() {return systemNewsLoader;}
	public void setSystemNewsLoader(com.kangdainfo.tcfi.loader.SystemNewsLoader loader) {this.systemNewsLoader = loader;}
	/** 系統功能 */
	public com.kangdainfo.tcfi.loader.FunctionMenuLoader getFunctionMenuLoader() {return functionMenuLoader;}
	public void setFunctionMenuLoader(com.kangdainfo.tcfi.loader.FunctionMenuLoader loader) {this.functionMenuLoader = loader;}
	/** 同音同義字 **/
	public com.kangdainfo.tcfi.loader.Cedbc058CodeLoader getCedbc058CodeLoader() {return cedbc058CodeLoader;}
	public void setCedbc058CodeLoader(com.kangdainfo.tcfi.loader.Cedbc058CodeLoader loader) {this.cedbc058CodeLoader = loader;}
	/** 系統代碼(01:系統參數資料) **/
	public com.kangdainfo.tcfi.loader.SystemCode01Loader getSystemCode01Loader() {return systemCode01Loader;}
	public void setSystemCode01Loader(com.kangdainfo.tcfi.loader.SystemCode01Loader loader) {this.systemCode01Loader = loader;}
	/** 系統代碼(02:使用者群組) **/
	public com.kangdainfo.tcfi.loader.SystemCode02Loader getSystemCode02Loader() {return systemCode02Loader;}
	public void setSystemCode02Loader(com.kangdainfo.tcfi.loader.SystemCode02Loader loader) {this.systemCode02Loader = loader;}
	/** 系統代碼(03:取件方式) **/
	public com.kangdainfo.tcfi.loader.SystemCode03Loader getSystemCode03Loader() {return systemCode03Loader;}
	public void setSystemCode03Loader(com.kangdainfo.tcfi.loader.SystemCode03Loader loader) {this.systemCode03Loader = loader;}
	/** 系統代碼(04:公司型態) **/
	public com.kangdainfo.tcfi.loader.SystemCode04Loader getSystemCode04Loader() {return systemCode04Loader;}
	public void setSystemCode04Loader(com.kangdainfo.tcfi.loader.SystemCode04Loader loader) {this.systemCode04Loader = loader;}
	/** 系統代碼(05:核覆結果) **/
	public com.kangdainfo.tcfi.loader.SystemCode05Loader getSystemCode05Loader() {return systemCode05Loader;}
	public void setSystemCode05Loader(com.kangdainfo.tcfi.loader.SystemCode05Loader loader) {this.systemCode05Loader = loader;}
	/** 系統代碼(06:案件狀態) **/
	public com.kangdainfo.tcfi.loader.SystemCode06Loader getSystemCode06Loader() {return systemCode06Loader;}
	public void setSystemCode06Loader(com.kangdainfo.tcfi.loader.SystemCode06Loader loader) {this.systemCode06Loader = loader;}
	/** 系統代碼(07:郵寄類別) **/
	public com.kangdainfo.tcfi.loader.SystemCode07Loader getSystemCode07Loader() {return systemCode07Loader;}
	public void setSystemCode07Loader(com.kangdainfo.tcfi.loader.SystemCode07Loader loader) {this.systemCode07Loader = loader;}
	/** 系統代碼(08:申登機關) **/
	public com.kangdainfo.tcfi.loader.SystemCode08Loader getSystemCode08Loader() {return systemCode08Loader;}
	public void setSystemCode08Loader(com.kangdainfo.tcfi.loader.SystemCode08Loader loader) {this.systemCode08Loader = loader;}
	/** 系統代碼(09:馬上辦案由) **/
	public com.kangdainfo.tcfi.loader.SystemCode09Loader getSystemCode09Loader() {return systemCode09Loader;}
	public void setSystemCode09Loader(com.kangdainfo.tcfi.loader.SystemCode09Loader loader) {this.systemCode09Loader = loader;}
	/** 系統代碼(10:公司狀態) **/
	public com.kangdainfo.tcfi.loader.SystemCode10Loader getSystemCode10Loader() {return systemCode10Loader;}
	public void setSystemCode10Loader(com.kangdainfo.tcfi.loader.SystemCode10Loader loader) {this.systemCode10Loader = loader;}
	/** 系統代碼(11:預查公司狀態) **/
	public com.kangdainfo.tcfi.loader.SystemCode11Loader getSystemCode11Loader() {return systemCode11Loader;}
	public void setSystemCode11Loader(com.kangdainfo.tcfi.loader.SystemCode11Loader loader) {this.systemCode11Loader = loader;}
	/** 系統代碼(12:身分證件別) **/
	public com.kangdainfo.tcfi.loader.SystemCode12Loader getSystemCode12Loader() {return systemCode12Loader;}
	public void setSystemCode12Loader(com.kangdainfo.tcfi.loader.SystemCode12Loader loader) {this.systemCode12Loader = loader;}
	/** 系統代碼(13:預查種類) **/
	public com.kangdainfo.tcfi.loader.SystemCode13Loader getSystemCode13Loader() {return systemCode13Loader;}
	public void setSystemCode13Loader(com.kangdainfo.tcfi.loader.SystemCode13Loader loader) {this.systemCode13Loader = loader;}
	/** 系統代碼(14:展期原因) **/
	public com.kangdainfo.tcfi.loader.SystemCode14Loader getSystemCode14Loader() {return systemCode14Loader;}
	public void setSystemCode14Loader(com.kangdainfo.tcfi.loader.SystemCode14Loader loader) {this.systemCode14Loader = loader;}
	/** 系統代碼(15:片語) **/
	public com.kangdainfo.tcfi.loader.SystemCode15Loader getSystemCode15Loader() {return systemCode15Loader;}
	public void setSystemCode15Loader(com.kangdainfo.tcfi.loader.SystemCode15Loader loader) {this.systemCode15Loader = loader;}
	/**
	 * 有限合夥各類代碼對照(組織對照)
	 * @return lmsdCodemappingOrgLoader
	 * 2024/04/16
	 */
	public com.kangdainfo.tcfi.loader.LmsdCodemappingOrgLoader getLmsdCodemappingOrgLoader() {
		return lmsdCodemappingOrgLoader;
	}
	/**
	 * 有限合夥各類代碼對照(組織對照)
	 * @param lmsdCodemappingOrgLoader
	 * 2024/04/16
	 */
	public void setLmsdCodemappingOrgLoader(com.kangdainfo.tcfi.loader.LmsdCodemappingOrgLoader lmsdCodemappingOrgLoader) {
		this.lmsdCodemappingOrgLoader = lmsdCodemappingOrgLoader;
	}
	/**
	 * 有限合夥各類代碼對照(狀態對照)
	 * @return
	 * 2024/04/17
	 */
	public com.kangdainfo.tcfi.loader.LmsdCodemappingStatLoader getLmsdCodemappingStatLoader() {
		return lmsdCodemappingStatLoader;
	}
	/**
	 * 有限合夥各類代碼對照(狀態對照)
	 * @return
	 * 2024/04/17
	 */
	public void setLmsdCodemappingStatLoader(com.kangdainfo.tcfi.loader.LmsdCodemappingStatLoader lmsdCodemappingStatLoader) {
		this.lmsdCodemappingStatLoader = lmsdCodemappingStatLoader;
	}
	/**
	 * 有限合夥申登機關代碼
	 * @return
	 * 2024/04/17
	 */
	public com.kangdainfo.tcfi.loader.LmsdRegUnitLoader getLmsdRegUnitLoader() {
		return lmsdRegUnitLoader;
	}
	/**
	 * 有限合夥申登機關代碼
	 * @return
	 * 2024/04/17
	 */
	public void setLmsdRegUnitLoader(com.kangdainfo.tcfi.loader.LmsdRegUnitLoader lmsdRegUnitLoader) {
		this.lmsdRegUnitLoader = lmsdRegUnitLoader;
	}

	/** 審核作業 **/
	public com.kangdainfo.tcfi.service.ApproveService getApproveService() {return approveService;}
	public void setApproveService(com.kangdainfo.tcfi.service.ApproveService service) {this.approveService = service;}

	/** 承辦人員 **/
	public com.kangdainfo.tcfi.loader.Cedbc000CodeLoader getCedbc000CodeLoader() {return cedbc000CodeLoader;}
	public void setCedbc000CodeLoader(com.kangdainfo.tcfi.loader.Cedbc000CodeLoader loader) {this.cedbc000CodeLoader = loader;}

	public com.kangdainfo.tcfi.lucene.service.IndexSearchService getIndexSearchService() {return indexSearchService;}
	public void setIndexSearchService(com.kangdainfo.tcfi.lucene.service.IndexSearchService service) {this.indexSearchService = service;}

	public com.kangdainfo.tcfi.lucene.service.IndexCreateService getIndexCreateService() {return indexCreateService;}
	public void setIndexCreateService(com.kangdainfo.tcfi.lucene.service.IndexCreateService service) {this.indexCreateService = service;}

	public com.kangdainfo.tcfi.lucene.service.IndexUpdateService getIndexUpdateService() {return indexUpdateService;}
	public void setIndexUpdateService(com.kangdainfo.tcfi.lucene.service.IndexUpdateService service) {this.indexUpdateService = service;}

	public com.kangdainfo.tcfi.service.UpdateOsssStatusService getUpdateOsssStatusService() {return updateOsssStatusService;}
	public void setUpdateOsssStatusService(com.kangdainfo.tcfi.service.UpdateOsssStatusService service) {this.updateOsssStatusService = service;}

	public com.kangdainfo.tcfi.service.BackupService getBackupService() {return backupService;}
	public void setBackupService(com.kangdainfo.tcfi.service.BackupService service) {this.backupService = service;}

	public com.kangdainfo.tcfi.service.EncapsulateService getEncapsulateService() {return encapsulateService;}
	public void setEncapsulateService(com.kangdainfo.tcfi.service.EncapsulateService service) {this.encapsulateService = service;}

	public com.kangdainfo.tcfi.service.CaseFlowService getCaseFlowService() {return caseFlowService;}
	public void setCaseFlowService(com.kangdainfo.tcfi.service.CaseFlowService service) {this.caseFlowService = service;}

	public com.kangdainfo.tcfi.service.TestService getTestService() {return testService;}
	public void setTestService(com.kangdainfo.tcfi.service.TestService service) {this.testService = service;}

	public com.kangdainfo.tcfi.service.NoPayMarkService getNoPayMarkService() {return noPayMarkService;}
	public void setNoPayMarkService(com.kangdainfo.tcfi.service.NoPayMarkService service) {this.noPayMarkService = service;}

	public com.kangdainfo.tcfi.service.MoeaicApproveService getMoeaicApproveService() {return moeaicApproveService;}
	public void setMoeaicApproveService(com.kangdainfo.tcfi.service.MoeaicApproveService service) {this.moeaicApproveService = service;}

	public com.kangdainfo.tcfi.service.CorrectDataService getCorrectDataService() {return correctDataService;}
	public void setCorrectDataService(com.kangdainfo.tcfi.service.CorrectDataService service) {this.correctDataService = service;}

	public String getEnv() {return env;}
	public void setEnv(String env) {this.env = env;}

}