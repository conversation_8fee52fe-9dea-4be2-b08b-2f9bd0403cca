<!DOCTYPE html>
<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3008">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<%
if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report) {
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE3008.pdf");
		out.clear();
		out = pageContext.pushBody();
	} else {
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
} else if("cancelNoNeedPay".equals(obj.getState())) {
	obj.cancelNoNeedPay();
} else if("recoverNoNeedPay".equals(obj.getState())) {
	obj.recoverNoNeedPay();
} else if("saveRemark".equals(obj.getState())) {
	obj.saveRemark();
}
%>
<html>
<head>
<!-- 使用跳出視窗方式的功能需指定 TITLE -->
<jsp:include page="../common/function_title.jsp"><jsp:param name="function" value="PRE3008"/></jsp:include>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//for IE8 Array indexOf
if (!Array.prototype.indexOf) {
	Array.prototype.indexOf = Array.prototype.indexOf || function(obj, start) {
		for (var i = (start || 0), j = this.length; i < j; i++) {
			if (this[i] === obj) {
				return i;
			}
		}
		return -1;
	};
}
/*顯示免繳資格註記視窗*/
function queryShow(queryName){
	var objHeight = $("#"+queryName).css("height");
	var objWidth = $("#"+queryName).css("width");	
	objHeight = objHeight.substring(0,objHeight.length-2);
	objWidth = objWidth.substring(0,objWidth.length-2);
	var _top = (document.body.clientHeight-Number(objHeight)-80)/2;
	var _left = (document.body.clientWidth-Number(objWidth))/2;
	$("#"+queryName).css("top", _top);
	$("#"+queryName).css("left", _left);
	$("#"+queryName).css("display", "block");
}

function queryHidden(queryName){
	$("#"+queryName).css("display", "none");
}

function popConfirm() {
    if (confirm("確定要註銷免繳資格嗎?")) {
    	form1.state.value = "cancelNoNeedPay";
		form1.submit();
    } // if
    else {
    } // else
}

function checkPrefixNo() {
	if ( form1.remarkPrefixNo.value.length != 9 ) {
		alert("請輸入正確的預查編號");
	}
	else {
		form1.state.value = "saveRemark";
		form1.submit();
	}
}

var cmpyVo;
var banNos = "";
$(document).ready(function() {
	//事件註冊
	$("#btn_banNo").click(function(){
		if ( form1.q_banNo.value.length != 8 ) {
			document.getElementById("ERRMSG").innerHTML = "請輸入正確的統一編號";
		} else {
			var _banNo = $("#q_banNo").val();
			var _comefrom = $("#comefrom").val();
			form1.banNos.value = _banNo;
			form1.comefrom.value = _comefrom;
			form1.state.value = "queryAll";
			form1.action = "pre3008.jsp";
			form1.submit();
		}
	});

	//恢復免繳資料 -> 尚未使用
	$("#recoverNoNeedPay").click(function() {
		if(confirm("您確定恢復免繳資格 ?") ) {
			form1.state.value = "recoverNoNeedPay";
			form1.submit();
		}
	});
	
	$("#q_banNo").keydown(function(e){
		if(e.which == 13) {
			$("#btn_banNo").click();
		}
	});

	//下一筆
	$("#nextBtn").click(function(e) {
		e.preventDefault();
		var pno = $("#currBanNo").val();
		var currentIndex = banNos.indexOf(pno);
		if(++currentIndex >= banNos.length) {
			pno = banNos[0];
		} else {
			pno = banNos[currentIndex];
		}
		$("#currBanNo").val(pno);
		enterCurrent();
	});
	//上一筆
	$("#prevBtn").click(function(e) {
		e.preventDefault();
		var pno = $("#currBanNo").val();
		var currentIndex = banNos.indexOf(pno);
		if((currentIndex-1) < 0) {
			pno = banNos[(banNos.length-1)];
		} else {
			pno = banNos[--currentIndex];
		}
		$("#currBanNo").val(pno);
		enterCurrent();
	});
	//最末筆
	$("#lastBtn").click(function(e) {
		e.preventDefault();
		var pno = banNos[banNos.length - 1];
		$("#currBanNo").val(pno);
		enterCurrent();
	});
	//第一筆
	$("#firstBtn").click(function(e) {
		e.preventDefault();
		var pno = banNos[0];
		$("#currBanNo").val(pno);
		enterCurrent();
	});
	//離開
	$('#doClose').click(function(){
		window.opener = null;  
		window.open('','_parent',''); 
		window.close();
	});
	//列印
	$('#doPrintPdf').click(function(){
		window.open("",'popReport','scrollbars=1,resizable=1,toolbar=0,menubar=0,directories=0,status=1,location=0' );
		$('#state').val("preview");
		form1.target = 'popReport';
		form1.submit();
		form1.target = '';
	});

	$("#currBanNo").keypress(function(e){
		$.blockUI({message:'<h1> 資料載入中，請稍後  <img src="/prefix/images/jquery/busy.gif" /></h1>',overlayCSS:{backgroundColor:'#F3EFEF'}});
		$("#tabs1").click();
		clearUI();
		showMsgBar("&nbsp;");
		if(e.which == 13) {
			$.post( getVirtualPath() + "tcfi/pre/pre3008_01.jsp?q="+$("#currBanNo").val(),function(data) {
				if(!data) {
					cmpyVo = null;
					showMsgBar("查無資料!");
					return;
				}
				cmpyVo = data;
				$('#banNo').html(cmpyVo.banNo);
				$('#current').text(cmpyVo.banNo);
				$('#companyName').html(cmpyVo.companyName);
				$('#prefixNo').html(cmpyVo.prefixNo);
				$('#specialName').html(cmpyVo.specialName);
				$('#orgTypeName').html(cmpyVo.orgType+' '+cmpyVo.orgName);
				$('#respName').html(cmpyVo.respName);
				$('#companyStatus').html(cmpyVo.companyStatus);
				$('#regUnitName').html(cmpyVo.regUnit+' '+cmpyVo.regUnitName);
				$('#companyTel').html(cmpyVo.companyTel);
				$('#companyAddr').html(cmpyVo.companyAddr);
				if (cmpyVo.closed == 'Y') {
					$('#closed').html('閉鎖性');	
				}
				$('#capitalAmount').html(cmpyVo.capitalAmount);
				$('#controlItem').html(cmpyVo.controlItem);
				$('#totBranch').html(cmpyVo.totBranch);
				if("Y" == cmpyVo.investmentCode) {
					$('#investmentCode').attr('checked',true);
				} else {
					$('#investmentCode').removeAttr('checked');
				}
				if("Y" == cmpyVo.chinaCode) {
					$('#chinaCode').attr('checked',true);
				} else {
					$('#chinaCode').removeAttr('checked');
				}
				$('#setupDate').html(cmpyVo.setupDate);
				$('#approveWord').html(cmpyVo.approveWord);
				$('#approveNo').html(cmpyVo.approveNo);
				$('#changeDate').html(cmpyVo.changeDate);
				$('#changeWord').html(cmpyVo.changeWord);
				$('#changeNo').html(cmpyVo.changeNo);
				$('#suspendDate').html(cmpyVo.suspendDate);
				$('#suspendWord').html(cmpyVo.suspendWord);
				$('#suspendNo').html(cmpyVo.suspendNo);
				$('#suspendUnit').html(cmpyVo.suspendUnit);
				$('#suspendBegDate').html(cmpyVo.suspendBegDate);
				$('#suspendEndDate').html(cmpyVo.suspendEndDate);
				$('#endDate').html(cmpyVo.endDate);
				if("1" == cmpyVo.ownerType) {
					$('input[name=ownerType][value=1]').attr('checked',true);
					$('input[name=ownerType][value=2]').removeAttr('checked');
				} else if("2" == cmpyVo.ownerType) {
					$('input[name=ownerType][value=1]').removeAttr('checked');
					$('input[name=ownerType][value=2]').attr('checked',true);
				} else {
					$('input[name=ownerType][value=1]').removeAttr('checked');
					$('input[name=ownerType][value=2]').removeAttr('checked');
				}
				$('#noNeedPay').val(cmpyVo.noNeedPay);

				showMsgBar("查詢成功!");
				$.unblockUI();
			});
	    }
		setTimeout(function(){$.unblockUI();},2000);
	});
	//頁籤
	$("#tabs").tabs();
	//畫面初始
	window.moveTo(0,0);
	window.resizeTo(screen.width,screen.height*0.96);
	var _banNo = $('input[name="banNos"]').map(function() {
		return $(this).val();
	}).get().join('-');
	banNos = _banNo.match(/[^-]+/g) || [];
	$("#currBanNo").val(banNos[0]);
	//觸發Enter
	enterCurrent();
});

function enterCurrent() {
	var e = jQuery.Event("keypress");
	e.which = 13;
	jQuery('#currBanNo').trigger(e);
}

function clearUI() {
	$('#banNo').html("&nbsp;");
	$('#current').html("&nbsp;");
	$('#companyName').html("&nbsp;");
	$('#prefixNo').html("&nbsp;");
	$('#specialName').html("&nbsp;");
	$('#orgTypeName').html("&nbsp;");
	$('#respName').html("&nbsp;");
	$('#companyStatus').html("&nbsp;");
	$('#regUnitName').html("&nbsp;");
	$('#companyTel').html("&nbsp;");
	$('#companyAddr').html("&nbsp;");
	$('#capitalAmount').html("&nbsp;");
	$('#controlItem').html("&nbsp;");
	$('#totBranch').html("&nbsp;");
	$('#investmentCode').removeAttr('checked');
	$('#chinaCode').removeAttr('checked');
	$('#setupDate').html("&nbsp;");
	$('#approveWord').html("&nbsp;");
	$('#approveNo').html("&nbsp;");
	$('#changeDate').html("&nbsp;");
	$('#changeWord').html("&nbsp;");
	$('#changeNo').html("&nbsp;");
	$('#suspendDate').html("&nbsp;");
	$('#suspendWord').html("&nbsp;");
	$('#suspendNo').html("&nbsp;");
	$('#suspendUnit').html("&nbsp;");
	$('#suspendBegDate').html("&nbsp;");
	$('#suspendEndDate').html("&nbsp;");
	$('#endDate').html("&nbsp;");
	$('input[name=ownerType][value=1]').removeAttr('checked');
	$('input[name=ownerType][value=2]').removeAttr('checked');
	$('#noNeedPay').val("");
}

//營業項目
function loadBusiItems() {
	//clear
	$("#busiItems > tbody").html("");
	if(cmpyVo && cmpyVo.cedb2002s) {
		var datas = cmpyVo.cedb2002s;
		//add
		for(var j=0; j<datas.length; j++) {
			addBusiItem((j%2==0)?"listTREven":"listTROdd",
					commonUtils.trimUndefined(datas[j].seqNo),
					commonUtils.trimUndefined(datas[j].busiItemNo),
					commonUtils.trimUndefined(datas[j].busiItem) );
		}
	}
}
//營業項目
function addBusiItem(tr_class, seqNo, busiItemNo, busiItem) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ seqNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItemNo +'</td>';
	txt += '<td style="text-align:left">'+ busiItem +'</td>';
	txt += '</tr>';
	$("#busiItems tbody").append(txt);
}
//董監事
function loadShareHolders() {
	//clear
	$("#shareHolders > tbody").html("");
	if(cmpyVo && cmpyVo.shareholders) {
		var datas = cmpyVo.shareholders;
		//add
		for(var j=0; j<datas.length; j++) {
			addShareHolder((j%2==0)?"listTREven":"listTROdd",
					commonUtils.trimUndefined(datas[j].sortNo),
					commonUtils.trimUndefined(datas[j].name),
					commonUtils.trimUndefined(datas[j].idNo),
					commonUtils.trimUndefined(datas[j].positionName),
					commonUtils.trimUndefined(datas[j].corpName) );
		}
	}
}
//董監事
function addShareHolder(tr_class, sortNo, name, idNo, positionName, corpName) {
	var txt = "";
	txt += '<tr class="'+tr_class+'">';
	txt += '<td style="text-align:left">'+ sortNo +'</td>';
	txt += '<td style="text-align:left">'+ name +'</td>';
	txt += '<td style="text-align:left">'+ idNo +'</td>';
	txt += '<td style="text-align:left">'+ positionName +'</td>';
	txt += '<td style="text-align:left">'+ corpName +'</td>';
	txt += '</tr>';
	$("#shareHolders tbody").append(txt);
}
</script>
</head>
<body topmargin="5" >
<form id="form1" name="form1" method="post" >
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="comefrom" name="comefrom" value="<%=obj.getComefrom()%>">
<input type="hidden" id="currBanNo" name="currBanNo" value="<%=obj.getCurrBanNo()%>" />

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE3008'/>
</c:import>

<div id="queryContainer" style="width:250px;height:100px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">免繳註記</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form">請輸入預查編號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="remarkPrefixNo" size="12" maxlength="9" value="" />
        </td>
    </tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="button" name="yes" value="確　　定" onClick="checkPrefixNo()">
			<input class="toolbar_default" type="button" name="no" value="取　　消" onClick="queryHidden('queryContainer')">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0" border="0">
<!-- TAB AREA -->
<tr><td class="bg">
	<div id="titleContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">
		<tr>
			<td class="td_form" >統一編號：</td>
			<td>
		    	<input id="q_banNo" type="text" size="10" maxlength="8" name="q_banNo" value="">
				<input class="toolbar_default" type="button" followPK="false" id="btn_banNo" name="doQuery" value="查　詢" >
			</td>
			<td>
				<table>
					<tr><td>
						<input type="image" src="../../images/pre/btn_first.gif" alt="第一筆" id="firstBtn" name="firstBtn" style="cursor:hand" />
						<input type="image" src="../../images/pre/btn_up.gif" alt="上一筆" id="prevBtn" name="prevBtn" style="cursor:hand" />
					</td>
					<td width="100" align="center" id="current">&nbsp;</td>
					<td>
						<input type="image" src="../../images/pre/btn_down.gif" alt="下一筆" id="nextBtn" name="nextBtn" style="cursor:hand" />
						<input type="image" src="../../images/pre/btn_last.gif" alt="最末筆" id="lastBtn" name="lastBtn" style="cursor:hand" />
	 				</td></tr>
				</table>
			</td>
			<td style="text-align:right">
				<c:import url="../common/shortcut.jsp">
					<c:param name="functions" value='PRE4001,PRE3002'/>
				</c:import>
				<input class="toolbar_default" type="button" id="doPrintPdf" name="doPrintPdf" value="列　印" >
				<input class="toolbar_default" type="button" id="doClose" name="doClose" value="離　開" />
			</td>
		</tr>
	</table>
	</div>
</td></tr>

</table>

<div id="tabs">
	<ul>
		<li><a href="#fragment-1" id="tabs1"><span>本國公司資料</span></a></li>
		<li><a href="#fragment-2" onclick="loadShareHolders()"><span>董監事</span></a></li>
		<li><a href="#fragment-3" onclick="loadBusiItems()"><span>營業項目</span></a></li>
	</ul>
	<div id="fragment-1" style="padding:0px;">
		<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
			<tr>
				<td class="td_form" width="20%">統一編號：</td>
				<td class="td_form_white" width="15%" id="banNo">&nbsp;</td>
				<td class="td_form" width="10%">公司名稱：</td>
				<td class="td_form_white" colspan="3" id="companyName">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">預查編號：</td>
				<td class="td_form_white" id="prefixNo">&nbsp;</td>
				<td class="td_form">特取名稱：</td>
				<td class="td_form_white" width="30%" id="specialName">&nbsp;</td>
				<td class="td_form" width="10%">組織型態：</td>
				<td class="td_form_white" width="15%" id="orgTypeName">&nbsp;</td>
			</tr>	
			<tr>
				<td class="td_form">負責人：</td>
				<td class="td_form_white" id="respName">&nbsp;</td>
				<td class="td_form">公司狀態：</td>
				<td class="td_form_white" id="companyStatus">&nbsp;</td>
				<td class="td_form">申登機關：</td>
				<td class="td_form_white" id="regUnitName">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >公司電話：</td>
				<td class="td_form_white" id="companyTel">&nbsp;</td>
				<td class="td_form">公司地址：</td>
				<td class="td_form_white" id="companyAddr">&nbsp;</td>
				<td class="td_form">閉鎖屬性：</td>
				<td class="td_form_white" id="closed">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form">資本總額：</td>
				<td class="td_form_white" id="capitalAmount">&nbsp;</td>
				<td class="td_form">管制項目：</td>
				<td class="td_form_white" id="controlItem">&nbsp;</td>
				<td class="td_form">分公司家數：</td>
				<td class="td_form_white" id="totBranch">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >核准設立日：</td>
				<td class="td_form_white" id="setupDate">&nbsp;</td>
				<td class="td_form">設立文號：</td>
				<td class="td_form_white"> 
				(字)<span id="approveWord">&nbsp;</span>
				(號)<span id="approveNo">&nbsp;</span>
				</td>
				<td class="td_form">僑外投資事業： </td>
				<td class="td_form_white">
					<input type="checkbox" id="investmentCode" name="investmentCode" value="Y" disabled
						style="height:20px;width:20px;" />
				</td>
			</tr>
			<tr>
				<td class="td_form" >核准變更日：</td>
				<td class="td_form_white" id="changeDate">&nbsp;</td>
				<td class="td_form">變更文號：</td>
				<td class="td_form_white"> 
				(字)<span id="changeWord">&nbsp;</span>
				(號)<span id="changeNo">&nbsp;</span>
				</td>
				<td class="td_form">陸資事業： </td>
				<td class="td_form_white">
					<input type="checkbox" id="chinaCode" name="chinaCode" value="Y" disabled
						style="height:20px;width:20px;" />
				</td>
			</tr>
			<tr>
				<td class="td_form" >核准停業日：</td>
				<td class="td_form_white" id="suspendDate">&nbsp;</td>
				<td class="td_form">停業文號：</td>
				<td class="td_form_white" colspan="3"> 
				(字)<span id="suspendWord">&nbsp;</span>
				(號)<span id="suspendNo">&nbsp;</span>
				</td>
			</tr>
			<tr>
				<td class="td_form" >核准停業機關：</td>
				<td class="td_form_white" id="suspendUnit">&nbsp;</td>
				<td class="td_form">停業/延長期間：</td>
				<td class="td_form_white" colspan="3"> 
				(起)<span id="suspendBegDate">&nbsp;</span>
				(迄)<span id="suspendEndDate">&nbsp;</span>
				</td>
			</tr>
			<tr>
				<td class="td_form">撤銷(破產/廢止/解散...)日期：</td>
				<td class="td_form_white" colspan="5" id="endDate">&nbsp;</td>
			</tr>
			<tr>
				<td class="td_form" >免繳註記：</td>
				<td class="td_form_white" colspan="5"> 
					<input class="field" type="text" id="noNeedPay" name="noNeedPay" size="50" value="" readonly>
					<input class="toolbar_default" type="button" id="recoverNoNeedPay" name="recoverNoNeedPay" value="恢復免繳資格" <%="pre1001".equals(obj.getComefrom())? "" :"style=\"display:none;\""%>>
					<input class="toolbar_default" type="button" id="cancelNoNeedPay" name="cancelNoNeedPay" value="註銷免繳資格"  <%="pre1001".equals(obj.getComefrom())? "" :"style=\"display:none;\""%> onclick="popConfirm()">
					<input class="toolbar_default" type="button" id="markNoNeedPay" name="markNoNeedPay" value="免繳註記"  <%="pre1001".equals(obj.getComefrom())? "" :"style=\"display:none;\""%> onclick="queryShow('queryContainer')">
				</td>
			</tr>
		</table>
	</div>
	<div id="fragment-2" style="padding:0px;">
		<TABLE id="shareHolders" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">姓名</td>
					<td class="td_form" style="text-align: left">身分ID</td>
					<td class="td_form" style="text-align: left">職務</td>
					<td class="td_form" style="text-align: left">所代表法人</td>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</table>
	</div>
	<div id="fragment-3" style="padding:0px;">
		<TABLE id="busiItems" class="table_form" width="100%" cellpadding="2" cellspacing="0">
			<thead>
				<tr>
					<td class="td_form" style="text-align: left">序號</td>
					<td class="td_form" style="text-align: left">代碼</td>
					<td class="td_form" style="text-align: left">營業項目</td>
				</tr>
			</thead>
			<tbody>
			</tbody>
		</table>
	</div>
</div>

<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<%
if(null!=obj.getBanNos()) {
	for(String p : obj.getBanNos()) {
out.write("<input type='hidden' name='banNos' value='"+p+"' />\n");
	}
}
%>

</form>
</body>
</html>