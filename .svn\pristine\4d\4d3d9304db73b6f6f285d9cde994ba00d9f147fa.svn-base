package com.kangdainfo.tcfi.service.impl;

import java.util.Calendar;

import org.apache.log4j.Logger;
import org.apache.lucene.document.Document;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.lucene.bo.Hit;
import com.kangdainfo.tcfi.lucene.bo.SearchResult;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1004;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.SameNameQueueDao;
import com.kangdainfo.tcfi.service.SameNameCompareService;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class SameNameCompareServiceImpl implements SameNameCompareService {
	
	private Logger logger = Logger.getLogger(this.getClass());

	public void createSameNameQueue(String prefixNo, String userId) {
		Queue sameQueue = new Queue();
		sameQueue.setPrefixNo(prefixNo);
		sameQueue.setStatus(PrefixConstants.INDEX_LOG_STATUS_0);		//0:待執行
		sameQueue.setModId(userId);
		sameQueue.setModDate(Datetime.getYYYMMDD());
		sameQueue.setModTime(Datetime.getHHMMSS());
		sameNameQueueDao.insert(sameQueue);
	}

	/** 取得 SAMENAME_QUEUE 待執行的資料，並回寫SAMENAME_QUEUE 執行中 */
	public synchronized Queue getSameNameQueueData(){
		Queue queueObj = sameNameQueueDao.query();
		if(queueObj != null){
			queueObj.setStatus(PrefixConstants.INDEX_LOG_STATUS_1);		//1:執行中
			queueObj.setModDate(Datetime.getYYYMMDD());
			queueObj.setModTime(Datetime.getHHMMSS());
			return sameNameQueueDao.update(queueObj);
		}
		return null;
	}
	
	/** 同名公司檢查 */
	public Queue doCheckCmpySameName(Queue queueObj){
		if(logger.isInfoEnabled()) logger.info("[Check CmpySameName][Start]");
		//開始時間
		long start = Calendar.getInstance().getTimeInMillis();
		String prefixNo = Common.get(queueObj.getPrefixNo());
		//執行比對
		try {
			doCheckCmpySameName(prefixNo);
			queueObj.setStatus(PrefixConstants.INDEX_LOG_STATUS_2);		//2:成功
			queueObj.setRemark("比對完成");
		} catch(Exception e) {
			//e.printStackTrace();
			if(logger.isInfoEnabled()) logger.info("[Check CmpySameName][Failure][Exception:"+ e.getMessage() + "]");
			queueObj.setStatus(PrefixConstants.INDEX_LOG_STATUS_3);		//3:失敗
			queueObj.setRemark(e.getMessage());
		}
		//結束時間
		long end = Calendar.getInstance().getTimeInMillis();
		long diff = end - start;
		queueObj.setProcessTime(diff);
		if(logger.isInfoEnabled()) logger.info("[Check CmpySameName][end]");
		return queueObj;
	}

	public void doCheckCmpySameName(String prefixNo) throws MoeaException {
		if(logger.isInfoEnabled()) logger.info("[doCheckCmpySameName][start][prefixNo:"+prefixNo+"]");
		//是否不比對已審核的案件
		boolean uncheckApproved = false;
		//1.檢查預查編號
		if(null==prefixNo || "".equals(prefixNo)) {
			throw new MoeaException("預查編號空白");
		}
		//2.查主檔
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if( null==cedb1000 ) {
			throw new MoeaException("主檔不存在");
		}
		if(uncheckApproved) {
			if( !PrefixConstants.APPROVE_RESULT_A.equals(cedb1000.getApproveResult()) ) {
				throw new MoeaException("主檔已審核，不再執行同名比對");
			}
		}
		//3.查公司名稱
		java.util.List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(prefixNo);
		if(null==cedb1001s || cedb1001s.isEmpty()) {
			throw new MoeaException("沒有公司名稱資料");
		} else {
			if(uncheckApproved) {
				Cedb1001 approvedCedb1001 = cedb1001Dao.findApprovedByPrefixNo(prefixNo);
				if(null!=approvedCedb1001) {
					throw new MoeaException("公司名稱資料已核准，不再執行同名比對");
				}
			}
		}
		//4.先刪除CEDB1004資料
		cedb1004Dao.deleteByPrefixNo(prefixNo);
		//5.逐筆比對，並存入CEDB1004
		for(Cedb1001 cedb1001 : cedb1001s) {
			doCheckCmpySameName(cedb1000, cedb1001);
		}
		if(logger.isInfoEnabled()) logger.info("[doCheckCmpySameName][end][prefixNo:"+prefixNo+"]");
	}
	
	/** 組織型態 */
	private static final String[] ORG_TYPE = {"股份有限公司", "有限公司", "無限公司", "兩合公司", "有限合夥"};
	private String replaceOrgType(String word) {
		word = word.trim();
		if(!"".equals(word) && word.length() > 4){
			for(String s: ORG_TYPE){
				if(word.endsWith(s))
					word = word.replace(s, "");
			}
		}
		return word;
	}
	private String[] addOrgType(String word) {
		String[] results = new String[ORG_TYPE.length];
		for(int i=0;i<ORG_TYPE.length;i++) {
			results[i] = word + ORG_TYPE[i];
		}
		return results;
	}
	
	private static final String[] SYMBOL = {"(", ")"};
	private String replaceSymbol(String word) {
		if(!"".equals(word)) {
			for(String s: SYMBOL){
				if(word.indexOf(s) > -1)
					word = word.replace(s, "");
			}
		}
		return word;
	}

	private void doCheckCmpySameName(Cedb1000 cedb1000, Cedb1001 cedb1001) {
		try {
			String banNo = Common.get(cedb1000.getBanNo());
			String prefixNo = Common.get(cedb1001.getPrefixNo());
			String companyName = Common.get(cedb1001.getCompanyName());
			String seqNo = Common.get(cedb1001.getSeqNo());
			//5.1.將公司名稱去除組織型態
			String keyword = replaceOrgType(companyName);
			//去除符號
			keyword = replaceSymbol(keyword);
			if(logger.isInfoEnabled()) logger.info("[Check CmpySameName]"+keyword);
			if( !"".equals(Common.get(keyword)) ) {
				String[] names = addOrgType(keyword);
				Document doc;
				SearchResult sr;
				String sameBanNo, samePrefixNo, sameCompanyName, indexType, revokeAppDate,
				       cmpyStatus, applyName, reserveDate;
				Cedb1004 cedb1004 = null;
				int idx = 0;
				for(String name : names) {
					//5.2.執行檢索
					sr = ServiceGetter.getInstance().getIndexSearchService().searchAll(PrefixConstants.SEARCH_TYPE_HELP, name, "", "N", false);
					if(null!=sr) {
						for (Hit hit : sr.getHits()){
							doc = hit.getDoc();
							sameBanNo = doc.get("BAN_NO");
							samePrefixNo = doc.get("PREFIX_NO");
							sameCompanyName = doc.get("COMPANY_NAME");
							indexType = doc.get("INDEX_TYPE");
							revokeAppDate = ("9999999".equals(Common.get(doc.get("REVOKE_APP_DATE")))?"":Common.get(doc.get("REVOKE_APP_DATE")));
							cmpyStatus = doc.get("CMPY_STATUS");
							applyName = doc.get("APPLY_NAME");
							reserveDate = ("9999999".equals(Common.get(doc.get("RESERVE_DATE")))?"":Common.get(doc.get("RESERVE_DATE")));
							//舊預查編號只有八碼(如:79002893), 要補一碼'0'
							if(Common.get(samePrefixNo).length() == 8) samePrefixNo = "0"+samePrefixNo;
							
							if( prefixNo.equals(samePrefixNo) ) {
								//同預查編號不算
								continue;
							} else if( prefixNo.compareTo(samePrefixNo) < 0 ) {
								//比對到後案時(samePrefixNo 比較新), 不管
								continue;
							} else if( !"".equals(banNo) && banNo.equals(sameBanNo) ) {
								//同統一編號不算
								continue;
							} else {
								idx++;
								if( idx <= 5 ) {
									//最多記5筆
									cedb1004 = new Cedb1004();
									cedb1004.setPrefixNo(prefixNo);
									cedb1004.setCompanyName(companyName);
									cedb1004.setSeqNo(seqNo);
									cedb1004.setSameSeqNo(Common.get(idx));
									cedb1004.setRevokeAppDate(revokeAppDate);
									cedb1004.setCmpyStatus(cmpyStatus);
									cedb1004.setApplyName(applyName);
									cedb1004.setReserveDate(reserveDate);
									
									if(PrefixConstants.INDEX_TYPE_1.equals(indexType) || PrefixConstants.INDEX_TYPE_5.equals(indexType)){
										cedb1004.setSameBanNo(sameBanNo);
										cedb1004.setSameCompanyName(sameCompanyName);
									} else {
										cedb1004.setSamePrefixNo(samePrefixNo);
										cedb1004.setSameCompanyName(sameCompanyName);
									}
									if(logger.isInfoEnabled()) logger.info(cedb1004);
									cedb1004Dao.insert(cedb1004);
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void doCompleteCheck(Queue queue) {
		queue.setModDate(Datetime.getYYYMMDD());
		queue.setModTime(Datetime.getHHMMSS());
		sameNameQueueDao.update(queue);
	}

	public void doBackupSameNameQueue() {
		if(logger.isInfoEnabled()) logger.info("[doBackupSameNameQueue][START]");
		sameNameQueueDao.backupSameNameQueue();
		if(logger.isInfoEnabled()) logger.info("[doBackupSameNameQueue][END]");
	}

	private Cedb1000Dao cedb1000Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1004Dao cedb1004Dao;
	private SameNameQueueDao sameNameQueueDao;

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}
	public Cedb1004Dao getCedb1004Dao() {return cedb1004Dao;}
	public void setCedb1004Dao(Cedb1004Dao dao) {this.cedb1004Dao = dao;}
	public SameNameQueueDao getSameNameQueueDao() {return sameNameQueueDao;}
	public void setSameNameQueueDao(SameNameQueueDao dao) {this.sameNameQueueDao = dao;}

}