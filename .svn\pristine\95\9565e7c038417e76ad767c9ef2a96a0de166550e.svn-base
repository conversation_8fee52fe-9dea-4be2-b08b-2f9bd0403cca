package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.util.lang.CommonStringUtils;
import com.kangdainfo.util.pdf.CommonPdfUtils;

/**
 * 列印預查申請表
 *
 */
public class PRE1004 extends SuperBean {
	
	private String prefixNo;
	private String prefixNoEnd;
	private String searchType;
	private String isAutoPrint;
	
	public String getPrefixNoEnd() {return checkGet(prefixNoEnd);}
	public void setPrefixNoEnd(String s) {this.prefixNoEnd = checkSet(s);}

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public String getSearchType() {return checkGet(searchType);}
	public void setSearchType(String s) {this.searchType = checkSet(s);}
	
	//如果未指定預設採用自動列印
	public String getIsAutoPrint() {return "".equals(checkGet(isAutoPrint))?"true":checkGet(isAutoPrint);}
	public void setIsAutoPrint(String s) {this.isAutoPrint = checkSet(s);}

	// METHODS 
	public File getPdfReport() {
		PRE1004 obj = this;
		File file = ServiceGetter.getInstance().getPrefixService().printApplyForm(obj.getPrefixNo(), obj.getSearchType());
		if(file != null) {
			this.setState("printSuccess");
		} else {
			this.setState("printFail");
		}
		return file;
	}
	
	public File makeMutiReportByPrefixNo() throws Exception {
		List<String> prefixNos = new ArrayList<String>();
		String startNo = String.format("%09d", Integer.parseInt(getPrefixNo()));
		if(CommonStringUtils.isNotEmpty(getPrefixNoEnd())) {
			String endNo = String.format("%09d", Integer.parseInt(getPrefixNoEnd()) + 1);
			while(!startNo.equals(endNo)) {
				prefixNos.add(startNo);
				startNo = String.format("%09d", Integer.parseInt((startNo)) + 1);
			}
		} else {
			prefixNos.add(startNo);
		}
	
		List<File> pdfFiles = new ArrayList<File>();
		for(String prefixNo : prefixNos) {
			pdfFiles.add( ServiceGetter.getInstance().getPrefixService().printApplyForm(prefixNo, getSearchType()) );
		}

		File mergeFile = null;
		if(null!=pdfFiles && !pdfFiles.isEmpty()) {
			mergeFile = CommonPdfUtils.concatPdfFiles(pdfFiles, true);
		}
		if(mergeFile != null) {
			this.setErrorMsg("製作成功");
		} else {
			this.setErrorMsg("製作失敗");
		}
		return mergeFile;
	}

	public static String checkForjsp( String prefixNo, String prefixNoEnd ) {
		int count = 0;
	    if ( prefixNo.equals(prefixNoEnd) || StringUtils.isEmpty(prefixNoEnd) ) {
		    Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(prefixNo);
		    if ( cedb1000 == null )
			    return "沒有這筆預查編號";
			else
			    return "ok";
		} else {
			int prefixNoStart = Integer.parseInt(prefixNo);
	        while ( prefixNoStart <= Integer.parseInt(prefixNoEnd) ) {
	    	    Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(Integer.toString(prefixNoStart));  
	    	    if (cedb1000 != null) {
	    		    count++;
	    	    }
	    	    prefixNoStart++;   
	        }

	        if ( count == 0 ) {
	        	return "沒有這些預查編號";
			} else {
	        	return "ok";
	        }
	    }
	} 
	
	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

}