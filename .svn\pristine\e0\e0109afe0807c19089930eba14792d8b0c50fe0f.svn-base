package com.kangdainfo.util.lang;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;

public class CommonStringUtils extends StringUtils
{
	/**
	 * 跳行字串，預設為\r\n。
	 */
	public static String NEXT_LINE = "\r\n";

	/**
	 * 字串合併
	 * @param strs
	 * @return
	 */
	public static String append(Object... strs)
	{
		StringBuffer sb = new StringBuffer("");
		for(Object str : strs)
		{
			if(null!=str)
				sb.append(str);
		}
		return sb.toString();
	}
	
	/**
	 * 分割字串為List
	 * @param str
	 * @param regex
	 * @return
	 */
	public static List<String> splitToList(String str, String regex)
	{
		List<String> results = new ArrayList<String>();
		if(null!=str)
		{
			for(String splitStr : str.split(regex))
			{
				results.add( splitStr );
			}
		}
		return results;
	}

	public static String limitLength(String s, int maximumLength) {
		if(s!=null) {
			if(s.length()>maximumLength) {
				s = s.substring(0,maximumLength);
			}
		}
		return s;
	}
	
	public static int indexOf(String content, char c, int fromIndex, int nthOccurence) {
		if(nthOccurence==0) {
			return -1;
		}
		int index = fromIndex;
		int start = fromIndex;
		while(true) {
			if(nthOccurence==0) {
				break;
			}
			index = content.indexOf(""+c,start);
			if(index==-1) {
				return -1;
			}
			start = index+1;
			nthOccurence--;
		}
		return index;
	}
	
	public static String[] splitByNewLine(String content) {
		content = content.replaceAll("\r\n", "\n");
		content = content.replaceAll("\r", "\n");
		content = content.replaceAll("\n", "\r\n");
		String lines[] = content.split("\r\n");
		return lines;
	}
	
	public static boolean isNullOrEmpty(String content) {
		return content==null || content.length()==0;
	}
	public static boolean isNullOrTrailingWhiteSpace(String content) {
		return content==null || content.length()==0 || content.trim().length()==0;
	}
	
	public static boolean isBetween(String target, String start, String end, boolean includeStartEnd) {
		int compareStart = target.compareTo(start);
		int compareEnd = target.compareTo(end);
		if(includeStartEnd) {
			return compareStart>=0 && compareEnd<=0;
		}
		return compareStart>0 && compareEnd<0;
	}
	
	public static String extractString(String s, int beginIndex, int endIndex) {
		if(s.length()>=endIndex) {
			return s.substring(beginIndex,endIndex);	
		}
		else {
			return null;
		}
	}
	public static String extractFirstLine(String s) {
		if(s==null) {
			return null;
		}
		else {
			int index = -1;
			
			index = s.indexOf('\r');
			if(index!=-1) {
				return s.substring(0,index);
			}
			index = s.indexOf('\n');
			if(index!=-1) {
				return s.substring(0,index);
			}
			return s;
		}
	}
	
	public static String[] split(String s, char c) {
		int separatorLength = 1;
		List<Integer> list = new ArrayList<Integer>();
		int index = 0;
		while(true) {
			index = s.indexOf(c,index);
			if(index==-1) {
				break;
			}
			list.add(index);
			index += separatorLength;
		}
		if(list.size()==0) {
			return null;
		}
		List<String> listOfString = new ArrayList<String>();
		int fromIndex = 0;
		for(int i=0,max_i=list.size(); i < max_i; i++) {
			index = list.get(i);
			String sub = s.substring(fromIndex,index);
			fromIndex = index+separatorLength;
			listOfString.add(sub);			
		}
		String lastSub = s.substring(fromIndex);
		listOfString.add(lastSub);
		return (String[]) listOfString.toArray(new String[listOfString.size()]);
	}
	
	public static String[] removeNullOrEmptyElements(String array[]) {
		if(array==null || array.length==0) {
			return null;
		}
		List<String> list = new ArrayList<String>();
		for(int i=0; i < array.length; i++) {
			if(array[i]!=null && array[i].length()!=0) {
				list.add(array[i]);
			}
		}
		return (String[]) list.toArray(new String[list.size()]);
	}

	public static Integer[] toIntegerArray(String array[]) {
		Integer newArray[] = new Integer[array.length];
		for(int i=0,max_i=array.length; i < max_i; i++) {
			newArray[i] = Integer.valueOf(array[i]);
		}
		return newArray;
	}
	
	public static String toNumberString(double d) {
		return NumberFormat.getNumberInstance().format((Number)d) ;
	}
	
	/**
	 * 由middle-gen抄來的function<br>
	 * 主要用途是將資料庫欄位名稱轉成java變數名稱<br>
	 * @param s
	 * @return
	 */
	public static String columnNameToVariableName(String s) {
		if ("".equals(s)) {
			return s;
		}
		StringBuilder result = new StringBuilder();
		boolean capitalize = true;
		boolean lastCapital = false;
		boolean lastDecapitalized = false;
		String p = null;
		for (int i = 0; i < s.length(); i++) {
			String c = s.substring(i, i + 1);
			if ("_".equals(c) || " ".equals(c)) {
				capitalize = true;
				continue;
			}
			if (c.toUpperCase().equals(c)) {
				if (lastDecapitalized && !lastCapital) {
					capitalize = true;
				}
				lastCapital = true;
			} else {
				lastCapital = false;
			}

			if (capitalize) {
				if (p == null || !p.equals("_")) {
					result.append(c.toUpperCase());
					capitalize = false;
					p = c;
				} else {
					result.append(c.toLowerCase());
					capitalize = false;
					p = c;
				}
			} else {
				result.append(c.toLowerCase());
				lastDecapitalized = true;
				p = c;
			}

		}
		String r = result.toString();
		return r;
	}	
	
	/**
	 * 字串合併 用","連接
	 * @param strs
	 * @return
	 */
	public static String addComma(String... strs)
	{
		String symbol = ",";
		StringBuffer sb = new StringBuffer();	
		for(String str: strs)
		{
			if( null!=str && !"".equals(str) )
			{
				if(!"".equals(sb.toString()))
					sb.append(symbol);
				sb.append(str.trim());
			}
		}	
		return sb.toString();
	}

	/**
	 * 字串合併 用" "連接
	 * @param strs
	 * @return
	 */
	public static String addSpace(String... strs)
	{
		String symbol = " ";
		StringBuffer sb = new StringBuffer();	
		for(String str: strs)
		{
			if( null!=str && !"".equals(str) )
			{
				if(!"".equals(sb.toString()))
					sb.append(symbol);
				sb.append(str.trim());
			}
		}	
		return sb.toString();
	}

	/** 解碼 */
	public static String unescape1(String escaped, String encoding)
			throws java.io.UnsupportedEncodingException {
		String str = escaped;
		
		// Here we split the 4 byte to 2 byte, so that decode won't fail
		String[] arr = str.split("%u");
		java.util.Vector<String> vec = new java.util.Vector<String>();
		if (!arr[0].isEmpty()) {
			vec.add(arr[0]);
		}
		for (int i = 1; i < arr.length; i++) {
			if (!arr[i].isEmpty()) {
				vec.add("%" + arr[i].substring(0, 2));
				vec.add("%" + arr[i].substring(2));
			}
		}
		str = "";
		for (String string : vec) {
			str += string;
		}
		// Here we return the decoded string
		String result = java.net.URLDecoder.decode(str, encoding);
		return result;
	}
	public static String unescape(String escaped, String encoding)
	throws java.io.UnsupportedEncodingException {
		StringBuffer tmp = new StringBuffer();  
        tmp.ensureCapacity(escaped.length());  
        int lastPos = 0, pos = 0;  
        char ch;  
        escaped = escaped.replaceAll("%25","@");
        while (lastPos < escaped.length()) {  
            pos = escaped.indexOf("%", lastPos);  
            if (pos == lastPos) {  
                if (escaped.charAt(pos + 1) == 'u') {  
                    ch = (char) Integer.parseInt(escaped  
                            .substring(pos + 2, pos + 6), 16);  
                    tmp.append(ch);  
                    lastPos = pos + 6;  
                } else {  
                    ch = (char) Integer.parseInt(escaped  
                            .substring(pos + 1, pos + 3), 16);  
                    tmp.append(ch);  
                    lastPos = pos + 3;  
                }  
            } else {  
                if (pos == -1) {  
                    tmp.append(escaped.substring(lastPos));  
                    lastPos = escaped.length();  
                } else {  
                    tmp.append(escaped.substring(lastPos, pos));  
                    lastPos = pos;  
                }  
            }  
        }  
        String tmp1 = tmp.toString().replaceAll("@", "%25");
        String result = java.net.URLDecoder.decode(tmp1, encoding);
		return result;
	}
	public static String replaceSpecialChar(String src)
	{
		String[] specialChars = new String[]{
				"0001","0002","0003","0004","0005"
				,"0006","0007","0008","0009","000b"
				,"000b","000c","000e","000f","0010"
				,"0011","0012","0013","0014","0015"
				,"0016","0017","0018","0019","001a"
				,"001b","001c","001d","001e","001f"
		};
		
		StringBuffer sb = new StringBuffer();
		char[] chars = src.toCharArray();
		for(int i=0;i<chars.length;i++)
		{
			String hex = charToHex(chars[i]);
			if( !ArrayUtils.contains(specialChars, hex) )
			{
				sb.append(chars[i]);
			}
		}
		return sb.toString();
	}

	public static String byteToHex(byte b) {
		// Returns hex String representation of byte b
		char hexDigit[] = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};
		char[] array = { hexDigit[(b >> 4) & 0x0f], hexDigit[b & 0x0f] };
		return new String(array);
	}

	public static String charToHex(char c) {
		// Returns hex String representation of char c
		byte hi = (byte) (c >>> 8);
		byte lo = (byte) (c & 0xff);
		return byteToHex(hi) + byteToHex(lo);
	}

}