<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %> 
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8016" />
</jsp:include>

<html>
<head>
<%@ include file="../../home/<USER>" %>

<script type="text/javascript">
var insertDefault;

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){}

function init() {
	changeTab('1');
}

function changeTab(tabId) {
	if(tabId == "1"){
		document.getElementById("t1").className = "tab_border1";
		document.getElementById("t2").className = "tab_border2";
		$('#pre8016Frame1').show();
		$('#pre8016Frame2').hide();
	}else{
		document.getElementById("t2").className = "tab_border1";
		document.getElementById("t1").className = "tab_border2";
		$('#pre8016Frame2').show();
		$('#pre8016Frame1').hide();
	}
}
</script>
</head>

<body topmargin="0" onLoad="init();">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8016'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0" >
	<tr><td>
		<table cellpadding=0 cellspacing=0 valign="top">
			<tr>
				<td>&nbsp;</td>
				<td nowrap ID=t1 CLASS="tab_border1" width="100" onClick="changeTab('1');">收文量統計</td>
				<td nowrap ID=t2 CLASS="tab_border2" width="100" onClick="changeTab('2');">案件分派及辦理進度</td>
			</tr>
			<tr>
				<td nowrap class="tab_line1"></td>
				<td nowrap class="tab_line2"></td>	
			</tr>
		</table>
	</td></tr>
	<tr><td>
		<iframe id="pre8016Frame1" name="pre8016Frame1" src='pre8016_00.jsp?tabId=1' width='100%' height='570' frameborder='0' scrolling='no' ></iframe>
	</td></tr>
	<tr><td>
		<iframe id="pre8016Frame2" name="pre8016Frame2" src='pre8016_00.jsp?tabId=2' width='100%' height='570' frameborder='0' scrolling='no' ></iframe>
	</td></tr>
</table>
</body>
</html>