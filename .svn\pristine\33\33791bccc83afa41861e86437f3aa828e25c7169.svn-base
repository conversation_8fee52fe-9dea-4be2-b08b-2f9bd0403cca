<!--
程式目的：審核-預查審核-展期
程式代號：PRE3001_06
撰寫日期：103.05.22
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<!doctype html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3001" />
</jsp:include>
<html>
<head>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/>
<%@ include file="../../home/<USER>"%>
<script>
	$( document ).ready(function() {
		var saveResult = '<%=obj.getErrorMsg()%>';
		if(saveResult == '儲存完成') {
			setTimeout('self.close()', 1000);
		}
		
		var prefixNo = commonUtils.getURLParameter("prefixNo");
		$.post( "/prefix/tcfi/ajax/jsonPrefixVo.jsp?q=" + prefixNo, function( data ) {
			commonUtils.mappingJsonByName(data, false);
		});

		$("input[name='extendReason']").click(function() {
			if(this.value != '90')
				$("input[name=extendOther]").val('');
		});

		$("input[name=extendOther]").focus(function() {
			$("input[name=extendReason][value=90]").prop('checked', true);
		});
		
		$("#doExpand").click(function() {
			var extendReason = $("input[name='extendReason']:checked").val() || '';
			if( checkExtOther(extendReason) ) {
				var extendMark = $("input[name='extendMark']:checked").val() || '';
				var extendOther = $("input[name='extendOther'").val();
				var parameter = 'prefixNo=' + prefixNo + '&extendMark='
							+ extendMark
							+ '&extendReason='
							+ extendReason
							+ '&extendOther=' + encodeURIComponent(extendOther);
				window.opener.doReload( extendMark, extendReason, extendOther );
				window.close();
			}
		});
	});

	function checkExtOther(extendReason) {
		if(extendReason == '90' && !$("input[name=extendOther]").val() ) {
			alert("展期原因的其他欄位必須輸入");
			return false;
		} else {
			return true;
		}
	}

	function doExit() {
		window.close();
	}

	function resetForm() {
		form1.reset();
	}
</script>
</head>
<body onLoad="showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='展期處理'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%">
	<tr>
		<td class="td_form" >展期狀態</td>
		<td class="td_form_white" >
			<input type="radio" name="extendMark" value="Y" checked>展期案件
			<input type="radio" name="extendMark" value="N">取消展期案件
		</td>
	</tr>
	<tr>
		<td class="td_form" >展期原因</td>
		<td class="td_form_white" >
			<input type="radio" name="extendReason" value="01" >撤件退費 &nbsp
			<input type="radio" name="extendReason" value="02" >撤件&nbsp
			<input type="radio" name="extendReason" value="03" >待補登撤銷解散廢止日 &nbsp
			<input type="radio" name="extendReason" value="04" >洽機關意見 &nbsp
			<br/>
			<input type="radio" name="extendReason" value="90" >其他 &nbsp
			<input type="text" name="extendOther" size="20" />
		</td>
	</tr>
	<tr>
		<td class="td_form" colspan="2" style="text-align:center">
			<input class="toolbar_default" type="button" value="存檔" id="doExpand" />
			<input class="toolbar_default" type="button" value="離開" onClick="window.close()" />
		</td>
	</tr>
	<c:import url="../common/msgbar.jsp">
  	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
	</table>
	</div>
</td></tr>
</table>
</form>
</body>
</html>