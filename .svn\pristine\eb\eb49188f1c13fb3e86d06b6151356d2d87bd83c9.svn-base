<!--
程式目的：測試中辦用的列印條碼api
撰寫日期：106.01.17
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean> 
<%
if ("rePrint".equals(obj.getState())){
	obj.rePrint();
} else if ("rePrintTCO".equals(obj.getState())) {
	obj.rePrintTCO();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<SCRIPT LANGUAGE="JAVASCRIPT">
function checkInput() {
	if (form1.prefixNo.value == '') {
		alert("預查編號不可空白!");
		return false;
	} 
	if (form1.receiveDate.value == '') {
		alert("收文日期不可空白!");
		return false;
	} else {
		if (form1.receiveDate.value.length != 7) {
			alert("收文日期格式應為:YYY/MM/DD");
			return false;
		}
	}
}

function sendToPrinter() {
	//if (!checkInput())
	//	return;
	var ret;
	try
   	{
		var myobject = new ActiveXObject("GoDEXATL.Function");
   		ret = myobject.openport("6");
 		var prefixNo = form1.prefixNo.value;
		var receiveDate = form1.receiveDate.value.substring(0,3)+'/'+form1.receiveDate.value.substring(3,5)+'/'+form1.receiveDate.value.substring(5);
		var applyType = document.getElementsByName('applyType')[0].checked?'設立':'變更';
 		ret = myobject.setup(93, 12, 4, 0, 3,0);
 		ret = myobject.sendcommand("^L\r\n");
 	   	ret = myobject.ecTextOut(40, 50, 30, "細明體", "經濟部商業發展署");
 	   	ret = myobject.ecTextOut(40, 100,24, "細明體", "預查編號 : "+ prefixNo);
 	   	ret = myobject.ecTextOut(40, 150,24, "細明體", "收文日期 : "+ receiveDate);
 	   	ret = myobject.ecTextOut(40, 200,24, "細明體", "申請種類 : "+ applyType);
 	   	ret = myobject.ecTextOut(40, 250,24, "細明體", "自取案件請向預查第3號櫃台領取");
 	   	ret = myobject.ecTextOut(40, 300,24, "細明體", "客服專線 : 4121166");
 	   	ret = myobject.ecTextOut(40, 350,24, "細明體", "收文櫃台電話 : (049)2359171 #2322");
 	   	ret = myobject.ecTextOut(40, 400,24, "細明體", "發文櫃台電話 : (049)2359171 #2323");
 	   	ret = myobject.sendcommand("BA3,60,480,2,6,80,0,0,"+prefixNo);
 	   	ret = myobject.sendcommand("BA3,60,600,2,6,80,0,3,"+prefixNo);
 	   	ret = myobject.sendcommand("E\r\n"); 	   
   	}
  	catch(e) {
  		alert("error:"+ ret); 
  	} 
  	finally {
      myobject.closeport();
  	}
}
function download() {
	if(!document.getElementsByName('byte')[0].checked && !document.getElementsByName('byte')[1].checked) {
		alert("請選擇作業系統版本");
		return false;
	}
	var url="";
	if (document.getElementsByName('byte')[0].checked)
		url = getVirtualPath() + 'tcfi/object/GoDEXATL_inst(32bit).exe';	
	else if (document.getElementsByName('byte')[1].checked)
		url = getVirtualPath() + 'tcfi/object/GoDEXATL_inst(64bit).exe';
	window.location.href = url;
}
</SCRIPT>
</head>
<body>
<form id="form1" name="printPrefixForm" method="post" action="">

<!--最上方BANNER-->

<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="right">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
			</c:import>
		</td>
	</tr>
</table>
<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<TD colspan="2" class="" align="left" width="100%">輸入條件</TD>
	</tr>
	<TR>
		<TD class="td_form">預查編號</TD>
		<TD class="td_form_white">
			<input type="text" class="field" id="prefixNo" name="prefixNo" maxlength="9" size="10" value="">
		</TD>
	</TR>
	<TR>
		<TD class="td_form">收文日期</TD>
		<TD class="td_form_white">
			<input type="text" class="field" id="receiveDate" name="receiveDate" maxlength="9" size="10" value="">
		</TD>
	</TR>
	<TR>
		<TD class="td_form">申請種類</TD>
		<TD class="td_form_white">
			<input type="radio" id="applyType" name="applyType" value="1" checked>申請&nbsp;&nbsp;&nbsp;
			<input type="radio" id="applyType" name="applyType" value="2">變更
			<input class="toolbar_default" type="button" id="rePrintTCO" value="測試" onclick="sendToPrinter();"/>
		</TD>
	</TR>
	<TR>
		<TD class="td_form">作業系統版本</TD>
		<TD class="td_form_white">
			<input type="radio" id="byte" name="byte" value="1">32位元&nbsp;&nbsp;&nbsp;
			<input type="radio" id="byte" name="byte" value="2">64位元
			<input class="toolbar_default" type="button" id="rePrintTCO" value="下載條碼元件" onclick="download()"/>
		</TD>
	</TR>
</table>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>

<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<input type="hidden" name="print" value="<%=obj.getPrint()%>">

</form>
</body>
</html>
