package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiveVo;

public class ReceiveDao extends BaseDaoJdbc implements RowMapper<ReceiveVo> {
	public int findOnlineDocNum() {
		String sql = "SELECT COUNT(1) AS ONLINE_DOC_COUNT FROM OSSS.OSSM_APPL_FLOW "
				+ "WHERE (TELIX_NO LIKE 'OSC%' OR TELIX_NO LIKE 'OSS%') "
				+ "AND (PROCESS_NO = 'B' OR PROCESS_NO = 'I') "
				+ "AND PROCESS_STATUS = '003'";

		List<ReceiveVo> list = getJdbcTemplate().query(sql, this);
		return list.isEmpty() ? 0 : Integer.parseInt(list.get(0).getOnlineDocCount());
	}

	@Override
	public ReceiveVo mapRow(ResultSet rs, int idx) throws SQLException {
		ReceiveVo obj = null;
		if (null != rs) {
			obj = new ReceiveVo();
			obj.setOnlineDocCount(rs.getString("ONLINE_DOC_COUNT"));
		}
		return obj;
	}
}
