<%
/**
程式目的：預查線上申辦電子收據
程式代號：pre4011
程式日期：103.05.12
程式作者：Kai.Cheng
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4011" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4011">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<%
if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report){
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4011.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(form1.q_type[0].checked){
		alertStr += checkEmpty(form1.q_telixNo,"電子流水號");
		form1.q_prefixNo.style.backgroundColor="";
	}else if(form1.q_type[1].checked){
		alertStr += checkEmpty(form1.q_prefixNo,"預查編號");
		form1.q_telixNo.style.backgroundColor="";
	}else{
		alertStr += "請先點選查詢種類。";
	}
	
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

function changeRadio(type) {
	var typeRadio = document.getElementsByName("q_type");
    if(type == 'telix'){
    	typeRadio[0].checked = true;
        form1.q_prefixNo.value="";
    }else if(type == 'prefix'){
    	typeRadio[1].checked = true;
       	form1.q_telixNo.value="";
    }
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE4011_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';	
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		document.getElementById("ERRMSG").innerHTML = "請稍後..."
		var prefixNo = form1.q_prefixNo.value;
		var telixNo = form1.q_telixNo.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4011.jsp?prefixNo='+prefixNo+'&telixNo='+telixNo, "");
		if ( x == 'ok'  ) 
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}
</script>
</head>
<!-- Form area -->

<body topmargin="5">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4011'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="15%">
			    <input type="radio" name="q_type" value="telix" checked onclick="changeRadio(this.value);">電子流水號：
			</td>
			<td class="td_form_white" width="85%">
				<input class="field_Q" type="text" name="q_telixNo" size="20" maxlength="16" value="<%=obj.getQ_telixNo()%>" onfocus="changeRadio('telix');">
		    </td>
		</tr>
		<tr>     
			<td class="td_form" >
				<input type="radio" name="q_type" value="prefix" onclick="changeRadio(this.value);">預查編號：
		    </td>
	        <td class="td_form_white"> 
				<input class="field_Q" type="text" name="q_prefixNo" size="20" maxlength="9" value="<%=obj.getQ_prefixNo()%>" onfocus="changeRadio('prefix');">
	       		&nbsp;<input class="toolbar_default" type="button" followPK="false" id="docheck" name="docheck" value="收據列印" onClick="doSomeCheck();" >
	        </td>
		</tr>	
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>