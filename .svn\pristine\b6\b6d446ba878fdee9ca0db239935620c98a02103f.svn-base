package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司基本資料檔(CSMM_CMPY_INFO)
 *
 */
public class CsmmCmpyInfo extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 公司統一編號 */
	private String banNo;
	/** 廠商代碼 */
	private String cmpyCode;
	/** 公司主檔更動流水號 */
	private Long cmpyModifNo;
	/** 公司預查編號 */
	private String preRegNo;
	/** 申登機關代碼 */
	private String regUnitCode;
	/** 本公司申登機關名稱 */
	private String regUnitName;
	/** 加工出口區局別 */
	private String subRegUnit;
	/** 公司名稱 */
	private String cmpyName;
	/** 特取名稱 */
	private String partName;
	/** 市鎮鄉村里代碼 */
	private String areaCode;
	/** 公司郵遞區號 */
	private String cmpyZipCode;
	/** 公司所在地 */
	private String cmpyAdd;
	/** 公司聯絡電話 */
	private String cmpyTelNo;
	/** 公司傳真號碼 */
	private String cmpyFaxNo;
	/** 公司電子郵件信箱 */
	private String cmpyEmail;
	/** 公司網址 */
	private String cmpyUrl;
	/** 舊公司統一編號 */
	private String oldBanNo;
	/** 原公司名稱 */
	private String oldCmpyName;
	/** 公司組織型態 */
	private String orgType;
	/** 公司組織型態名稱 */
	private String orgTypeName;
	/** 行業別 */
	private String busType;
	/** 一人公司註記 */
	private String singleCmpy;
	/** 特殊案件註記 */
	private String caseStatus;
	/** 特殊案件說明 */
	private String caseStatusDesc;
	/** 地址流水號 */
	private Long addSeqNo;
	/** 公司狀況 */
	private String cmpyStatus;
	/** 公司狀況說明 */
	private String cmpyStatusDesc;
	/** 公司管制項目 */
	private String ctrlItem;
	/** 僑外投資事業註記 */
	private String investCode;
	/** 分公司家數 */
	private Long brAmt;
	/** 資本總額 */
	private Long capAmt;
	/** 實收資本總額 */
	private Long realCapAmt;
	/** 股票性質 */
	private String stockType;
	/** 股票性質名稱 */
	private String stockTypeName;
	/** 股份總數 */
	private Long stockAmt;
	/** 每股金額 */
	private Long shareVal;
	/** 已發行普通股份總數 */
	private Long equityAmt;
	/** 已發行特別股份總數 */
	private Long prefStockAmt;
	/** 認股權憑證可認購股份數額 */
	private Long warrStockAmt;
	/** 公司債可轉換股份股數 */
	private Long bondStockAmt;
	/** 核准變更日期 */
	private Date chgAppDate;
	/** 核准變更文號(字) */
	private String chgAppWd;
	/** 核准變更文號(號) */
	private String chgAppNo;
	/** 公司撤銷日期 */
	private Date revokeAppDate;
	/** 公司撤銷文號(字) */
	private String revokeAppWd;
	/** 公司撤銷文號(號) */
	private String revokeAppNo;
	/** 董事任期起始日期 */
	private Date dtrBegDate;
	/** 董事任期屆滿日期 */
	private Date dtrEndDate;
	/** 檔案存放位置 */
	private String fileLocate;
	/** 檔案號碼 */
	private String fileNo;
	/** 會計期間 */
	private String fiscalDate;
	/** 會計師姓名 */
	private String accName;
	/** 會計師身份證字號 */
	private String accId;
	/** 核備清算日期 */
	private Date lqdnAppDate;
	/** 核備清算文號(字) */
	private String lqdnAppWd;
	/** 核備清算文號(號) */
	private String lqdnAppNo;
	/** 核備清算單位 */
	private String liquidator;
	/** 預定開業日期 */
	private Date openDate;
	/** 核准設立日期 */
	private Date estabAppDate;
	/** 核准設立文號(字) */
	private String estabAppWd;
	/** 核准設立文號(號) */
	private String estabAppNo;
	/** 監察人任期起始日期 */
	private Date spvrBegDate;
	/** 監察人任期屆滿日期 */
	private Date spvrEndDate;
	/** 停業核准日期 */
	private Date susAppDate;
	/** 停業核准機關 */
	private String susAppUnit;
	/** 停業核准文號(字) */
	private String susAppWd;
	/** 停業核准文號(號) */
	private String susAppNo;
	/** 停業/延展期間(起) */
	private Date susBegDate;
	/** 停業/延展期間(迄) */
	private Date susEndDate;
	/** 公司英文名稱 */
	private String cmpyEname;
	/** 公司英文地址 */
	private String cmpyEadd;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人身分證字號 */
	private String attorId;
	/** 是否發行股票 */
	private String stockIssue;
	/** 憑證申請 */
	private String certificate;
	/** 轉檔來源 */
	private String comeFrom;
	/** 保留未使用 */
	private String cmpyNameCns;
	/** 基地別(科學園區專用) */
	private String baseId;
	/** 事業類別(科學園區專用) */
	private String coType;
	/** 加工出口區園區別 */
	private String distCode;
	/** 營業項目說明(科學園區專用) */
	private String itemScrpt;
	/** 變更案由 */
	private String chgCaseCode;
	/** 入區核准日期 */
	private Date actAppDate;
	/** 入區核准文號(字) */
	private String actAppWd;
	/** 入區核准文號(號) */
	private String actAppNo;
	/** 現況紀錄旗標 */
	private String isNewest;
	/** 異動時間 */
	private Date updateDate;
	/** 異動人員 */
	private String updateUser;
	/** 董事人數 */
	private String dtrAmt;
	/** 監察人人數 */
	private String spvrAmt;
	/** 獨立董事人數 */
	private String indDtrAmt;
	/** 審計委員會 */
	private String committee;
	/** 是否顯示特區區名 */
	private String noZoneName;
	/** 陸資註記 */
	private String chinaCode;
	/** 公司章程訂定日期 */
	private Date constitutionEst;
	/** 公司章程修正日期 */
	private Date constitutionChg;
	/** 園區事業入區核准日期 */
	private Date sipaBusiAppDate;
	/** 登記表 (1.網路 2.紙本) */
	private String regSheet;
	/** 登記表註記 */
	private String regSheetRemark;
	/** CSMX_CMPY_SEQNO_MAP.異動方式(1:登記異動;2:補登異動;3:系統異動;4:修改中 9:其他) */
	private String modifType;

	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getCmpyCode() {return cmpyCode;}
	public void setCmpyCode(String cmpyCode) {this.cmpyCode = cmpyCode;}
	public Long getCmpyModifNo() {return cmpyModifNo;}
	public void setCmpyModifNo(Long cmpyModifNo) {this.cmpyModifNo = cmpyModifNo;}
	public String getPreRegNo() {return preRegNo;}
	public void setPreRegNo(String preRegNo) {this.preRegNo = preRegNo;}
	public String getRegUnitCode() {return regUnitCode;}
	public void setRegUnitCode(String regUnitCode) {this.regUnitCode = regUnitCode;}
	public String getRegUnitName() {return regUnitName;}
	public void setRegUnitName(String regUnitName) {this.regUnitName = regUnitName;}
	public String getSubRegUnit() {return subRegUnit;}
	public void setSubRegUnit(String subRegUnit) {this.subRegUnit = subRegUnit;}
	public String getCmpyName() {return cmpyName;}
	public void setCmpyName(String cmpyName) {this.cmpyName = cmpyName;}
	public String getPartName() {return partName;}
	public void setPartName(String partName) {this.partName = partName;}
	public String getAreaCode() {return areaCode;}
	public void setAreaCode(String areaCode) {this.areaCode = areaCode;}
	public String getCmpyZipCode() {return cmpyZipCode;}
	public void setCmpyZipCode(String cmpyZipCode) {this.cmpyZipCode = cmpyZipCode;}
	public String getCmpyAdd() {return cmpyAdd;}
	public void setCmpyAdd(String cmpyAdd) {this.cmpyAdd = cmpyAdd;}
	public String getCmpyTelNo() {return cmpyTelNo;}
	public void setCmpyTelNo(String cmpyTelNo) {this.cmpyTelNo = cmpyTelNo;}
	public String getCmpyFaxNo() {return cmpyFaxNo;}
	public void setCmpyFaxNo(String cmpyFaxNo) {this.cmpyFaxNo = cmpyFaxNo;}
	public String getCmpyEmail() {return cmpyEmail;}
	public void setCmpyEmail(String cmpyEmail) {this.cmpyEmail = cmpyEmail;}
	public String getCmpyUrl() {return cmpyUrl;}
	public void setCmpyUrl(String cmpyUrl) {this.cmpyUrl = cmpyUrl;}
	public String getOldBanNo() {return oldBanNo;}
	public void setOldBanNo(String oldBanNo) {this.oldBanNo = oldBanNo;}
	public String getOldCmpyName() {return oldCmpyName;}
	public void setOldCmpyName(String oldCmpyName) {this.oldCmpyName = oldCmpyName;}
	public String getOrgType() {return orgType;}
	public void setOrgType(String orgType) {this.orgType = orgType;}
	public String getOrgTypeName() {return orgTypeName;}
	public void setOrgTypeName(String orgTypeName) {this.orgTypeName = orgTypeName;}
	public String getBusType() {return busType;}
	public void setBusType(String busType) {this.busType = busType;}
	public String getSingleCmpy() {return singleCmpy;}
	public void setSingleCmpy(String singleCmpy) {this.singleCmpy = singleCmpy;}
	public String getCaseStatus() {return caseStatus;}
	public void setCaseStatus(String caseStatus) {this.caseStatus = caseStatus;}
	public String getCaseStatusDesc() {return caseStatusDesc;}
	public void setCaseStatusDesc(String caseStatusDesc) {this.caseStatusDesc = caseStatusDesc;}
	public Long getAddSeqNo() {return addSeqNo;}
	public void setAddSeqNo(Long addSeqNo) {this.addSeqNo = addSeqNo;}
	public String getCmpyStatus() {return cmpyStatus;}
	public void setCmpyStatus(String cmpyStatus) {this.cmpyStatus = cmpyStatus;}
	public String getCmpyStatusDesc() {return cmpyStatusDesc;}
	public void setCmpyStatusDesc(String cmpyStatusDesc) {this.cmpyStatusDesc = cmpyStatusDesc;}
	public String getCtrlItem() {return ctrlItem;}
	public void setCtrlItem(String ctrlItem) {this.ctrlItem = ctrlItem;}
	public String getInvestCode() {return investCode;}
	public void setInvestCode(String investCode) {this.investCode = investCode;}
	public Long getBrAmt() {return brAmt;}
	public void setBrAmt(Long brAmt) {this.brAmt = brAmt;}
	public Long getCapAmt() {return capAmt;}
	public void setCapAmt(Long capAmt) {this.capAmt = capAmt;}
	public Long getRealCapAmt() {return realCapAmt;}
	public void setRealCapAmt(Long realCapAmt) {this.realCapAmt = realCapAmt;}
	public String getStockType() {return stockType;}
	public void setStockType(String stockType) {this.stockType = stockType;}
	public String getStockTypeName() {return stockTypeName;}
	public void setStockTypeName(String stockTypeName) {this.stockTypeName = stockTypeName;}
	public Long getStockAmt() {return stockAmt;}
	public void setStockAmt(Long stockAmt) {this.stockAmt = stockAmt;}
	public Long getShareVal() {return shareVal;}
	public void setShareVal(Long shareVal) {this.shareVal = shareVal;}
	public Long getEquityAmt() {return equityAmt;}
	public void setEquityAmt(Long equityAmt) {this.equityAmt = equityAmt;}
	public Long getPrefStockAmt() {return prefStockAmt;}
	public void setPrefStockAmt(Long prefStockAmt) {this.prefStockAmt = prefStockAmt;}
	public Long getWarrStockAmt() {return warrStockAmt;}
	public void setWarrStockAmt(Long warrStockAmt) {this.warrStockAmt = warrStockAmt;}
	public Long getBondStockAmt() {return bondStockAmt;}
	public void setBondStockAmt(Long bondStockAmt) {this.bondStockAmt = bondStockAmt;}
	public Date getChgAppDate() {return chgAppDate;}
	public void setChgAppDate(Date chgAppDate) {this.chgAppDate = chgAppDate;}
	public String getChgAppWd() {return chgAppWd;}
	public void setChgAppWd(String chgAppWd) {this.chgAppWd = chgAppWd;}
	public String getChgAppNo() {return chgAppNo;}
	public void setChgAppNo(String chgAppNo) {this.chgAppNo = chgAppNo;}
	public Date getRevokeAppDate() {return revokeAppDate;}
	public void setRevokeAppDate(Date revokeAppDate) {this.revokeAppDate = revokeAppDate;}
	public String getRevokeAppWd() {return revokeAppWd;}
	public void setRevokeAppWd(String revokeAppWd) {this.revokeAppWd = revokeAppWd;}
	public String getRevokeAppNo() {return revokeAppNo;}
	public void setRevokeAppNo(String revokeAppNo) {this.revokeAppNo = revokeAppNo;}
	public Date getDtrBegDate() {return dtrBegDate;}
	public void setDtrBegDate(Date dtrBegDate) {this.dtrBegDate = dtrBegDate;}
	public Date getDtrEndDate() {return dtrEndDate;}
	public void setDtrEndDate(Date dtrEndDate) {this.dtrEndDate = dtrEndDate;}
	public String getFileLocate() {return fileLocate;}
	public void setFileLocate(String fileLocate) {this.fileLocate = fileLocate;}
	public String getFileNo() {return fileNo;}
	public void setFileNo(String fileNo) {this.fileNo = fileNo;}
	public String getFiscalDate() {return fiscalDate;}
	public void setFiscalDate(String fiscalDate) {this.fiscalDate = fiscalDate;}
	public String getAccName() {return accName;}
	public void setAccName(String accName) {this.accName = accName;}
	public String getAccId() {return accId;}
	public void setAccId(String accId) {this.accId = accId;}
	public Date getLqdnAppDate() {return lqdnAppDate;}
	public void setLqdnAppDate(Date lqdnAppDate) {this.lqdnAppDate = lqdnAppDate;}
	public String getLqdnAppWd() {return lqdnAppWd;}
	public void setLqdnAppWd(String lqdnAppWd) {this.lqdnAppWd = lqdnAppWd;}
	public String getLqdnAppNo() {return lqdnAppNo;}
	public void setLqdnAppNo(String lqdnAppNo) {this.lqdnAppNo = lqdnAppNo;}
	public String getLiquidator() {return liquidator;}
	public void setLiquidator(String liquidator) {this.liquidator = liquidator;}
	public Date getOpenDate() {return openDate;}
	public void setOpenDate(Date openDate) {this.openDate = openDate;}
	public Date getEstabAppDate() {return estabAppDate;}
	public void setEstabAppDate(Date estabAppDate) {this.estabAppDate = estabAppDate;}
	public String getEstabAppWd() {return estabAppWd;}
	public void setEstabAppWd(String estabAppWd) {this.estabAppWd = estabAppWd;}
	public String getEstabAppNo() {return estabAppNo;}
	public void setEstabAppNo(String estabAppNo) {this.estabAppNo = estabAppNo;}
	public Date getSpvrBegDate() {return spvrBegDate;}
	public void setSpvrBegDate(Date spvrBegDate) {this.spvrBegDate = spvrBegDate;}
	public Date getSpvrEndDate() {return spvrEndDate;}
	public void setSpvrEndDate(Date spvrEndDate) {this.spvrEndDate = spvrEndDate;}
	public Date getSusAppDate() {return susAppDate;}
	public void setSusAppDate(Date susAppDate) {this.susAppDate = susAppDate;}
	public String getSusAppUnit() {return susAppUnit;}
	public void setSusAppUnit(String susAppUnit) {this.susAppUnit = susAppUnit;}
	public String getSusAppWd() {return susAppWd;}
	public void setSusAppWd(String susAppWd) {this.susAppWd = susAppWd;}
	public String getSusAppNo() {return susAppNo;}
	public void setSusAppNo(String susAppNo) {this.susAppNo = susAppNo;}
	public Date getSusBegDate() {return susBegDate;}
	public void setSusBegDate(Date susBegDate) {this.susBegDate = susBegDate;}
	public Date getSusEndDate() {return susEndDate;}
	public void setSusEndDate(Date susEndDate) {this.susEndDate = susEndDate;}
	public String getCmpyEname() {return cmpyEname;}
	public void setCmpyEname(String cmpyEname) {this.cmpyEname = cmpyEname;}
	public String getCmpyEadd() {return cmpyEadd;}
	public void setCmpyEadd(String cmpyEadd) {this.cmpyEadd = cmpyEadd;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String attorName) {this.attorName = attorName;}
	public String getAttorId() {return attorId;}
	public void setAttorId(String attorId) {this.attorId = attorId;}
	public String getStockIssue() {return stockIssue;}
	public void setStockIssue(String stockIssue) {this.stockIssue = stockIssue;}
	public String getCertificate() {return certificate;}
	public void setCertificate(String certificate) {this.certificate = certificate;}
	public String getComeFrom() {return comeFrom;}
	public void setComeFrom(String comeFrom) {this.comeFrom = comeFrom;}
	public String getCmpyNameCns() {return cmpyNameCns;}
	public void setCmpyNameCns(String cmpyNameCns) {this.cmpyNameCns = cmpyNameCns;}
	public String getBaseId() {return baseId;}
	public void setBaseId(String baseId) {this.baseId = baseId;}
	public String getCoType() {return coType;}
	public void setCoType(String coType) {this.coType = coType;}
	public String getDistCode() {return distCode;}
	public void setDistCode(String distCode) {this.distCode = distCode;}
	public String getItemScrpt() {return itemScrpt;}
	public void setItemScrpt(String itemScrpt) {this.itemScrpt = itemScrpt;}
	public String getChgCaseCode() {return chgCaseCode;}
	public void setChgCaseCode(String chgCaseCode) {this.chgCaseCode = chgCaseCode;}
	public Date getActAppDate() {return actAppDate;}
	public void setActAppDate(Date actAppDate) {this.actAppDate = actAppDate;}
	public String getActAppWd() {return actAppWd;}
	public void setActAppWd(String actAppWd) {this.actAppWd = actAppWd;}
	public String getActAppNo() {return actAppNo;}
	public void setActAppNo(String actAppNo) {this.actAppNo = actAppNo;}
	public String getIsNewest() {return isNewest;}
	public void setIsNewest(String isNewest) {this.isNewest = isNewest;}
	public Date getUpdateDate() {return updateDate;}
	public void setUpdateDate(Date updateDate) {this.updateDate = updateDate;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getDtrAmt() {return dtrAmt;}
	public void setDtrAmt(String dtrAmt) {this.dtrAmt = dtrAmt;}
	public String getSpvrAmt() {return spvrAmt;}
	public void setSpvrAmt(String spvrAmt) {this.spvrAmt = spvrAmt;}
	public String getIndDtrAmt() {return indDtrAmt;}
	public void setIndDtrAmt(String indDtrAmt) {this.indDtrAmt = indDtrAmt;}
	public String getCommittee() {return committee;}
	public void setCommittee(String committee) {this.committee = committee;}
	public String getNoZoneName() {return noZoneName;}
	public void setNoZoneName(String noZoneName) {this.noZoneName = noZoneName;}
	public String getChinaCode() {return chinaCode;}
	public void setChinaCode(String chinaCode) {this.chinaCode = chinaCode;}
	public Date getConstitutionEst() {return constitutionEst;}
	public void setConstitutionEst(Date constitutionEst) {this.constitutionEst = constitutionEst;}
	public Date getConstitutionChg() {return constitutionChg;}
	public void setConstitutionChg(Date constitutionChg) {this.constitutionChg = constitutionChg;}
	public Date getSipaBusiAppDate() {return sipaBusiAppDate;}
	public void setSipaBusiAppDate(Date sipaBusiAppDate) {this.sipaBusiAppDate = sipaBusiAppDate;}
	public String getRegSheet() {return regSheet;}
	public void setRegSheet(String regSheet) {this.regSheet = regSheet;}
	public String getRegSheetRemark() {return regSheetRemark;}
	public void setRegSheetRemark(String regSheetRemark) {this.regSheetRemark = regSheetRemark;}
	public String getModifType() {return modifType;}
	public void setModifType(String modifType) {this.modifType = modifType;}

}