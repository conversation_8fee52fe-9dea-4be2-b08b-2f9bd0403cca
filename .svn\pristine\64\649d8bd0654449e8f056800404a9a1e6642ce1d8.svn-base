<%
/**
程式目的：審核作業流程/時間分析
程式代號：pre4007
程式日期：103.05.09
程式作者：Kai.Cheng
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4007">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4007" />
</jsp:include>
<%
if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE4007)obj.queryOne();	
} // end if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4007.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
else {
	// do nothing
} // end else
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init() {
	if ( form1.state.value == "init" ) {
		document.getElementById("formContainer2").style.display = 'none';
	}else {
  		document.getElementById("formContainer2").style.display = '';
	}  
} 

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_DateStart,"收件起始日期");
	alertStr += checkEmpty(form1.q_DateEnd,"收件結束日期");
	alertStr += checkDate(form1.q_DateStart,"收件起始日期") ;
	alertStr += checkDate(form1.q_DateEnd,"收件結束日期") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var dateStart = form1.q_DateStart.value;
		var dateEnd = form1.q_DateEnd.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4007.jsp?dateStart='+dateStart+'&dateEnd='+dateEnd);
		if ( x == 'ok'  )
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}

$(document).ready(function() {

	// commonUtils.clearInput('q_DateStart','q_DateEnd', 'focus');
	/*
	if($("#state").val() == 'init') {
		$(".bgList").hide();
	}
	*/
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE4007_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';	
				break;
			case "doQueryOne":
				$('#state').val("queryOne") ;
				break;	
			case "doClear":
				form1.q_DateStart.value = "";
				form1.q_DateEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>
<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4007'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="15%"><font color="red">*</font>收件日期：</td>
			<td class="td_form_white" width="85%">
				起：<%=View.getPopCalendar("field_Q","q_DateStart",obj.getQ_DateStart()) %> ~ 
				迄：<%=View.getPopCalendar("field_Q","q_DateEnd",obj.getQ_DateEnd()) %>
				<input class="toolbar_default" type="submit" followPK="false" id="doQueryOne" name="doQueryOne" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
				<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
				<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>		
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer2" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" colspan="3" style="text-align:center;">作業流程統計分析
		</tr>
		<tr>
     		<td class="td_form_white" colspan="2" style="text-align:center" width="65%"><font color="black">收件日期：</font><font color="darkblue"><%=obj.getReceiveDate()%></font></td>
     		<td class="td_form_white" style="text-align:left" width="35%"><font color="black">收文總件數：</font><font color="darkblue"><%=obj.getTotalReceive()%></font></td>
		</tr>
		<tr>
     		<td class="td_form_white" colspan="2" style="text-align:center" width="65%"></td>
     		<td class="td_form_white" style="text-align:left" width="35%"><font color="black">辦結總件數：</font><font color="darkblue"><%=obj.getTotalComplete()%></font></td>
		</tr>
		<tr>
			<td class="td_form_white" style="text-align:center;" width="15%"><font color="black">收文登打</font></td>
			<td class="td_form_white" width="50%"><font color="black">
				[起迄總工作天數</font>
				<font color="darkblue"><%=obj.getWorkDayReceive()%></font><font color="black">
				/件數</font>
				<font color="darkblue"><%=obj.getNumReceive()%></font><font color="black">
				]</font>
			</td>	
			<td class="td_form_white" width="35%"><font color="black">
				=</font>
				<font color="darkblue"><%=obj.getAverageReceive()%></font><font color="black">
				(四捨五入)</font>
			</td>
		</tr>
		<tr>
			<td class="td_form_white" style="text-align:center;"><font color="black">等待分文</font></td>	
			<td class="td_form_white"><font color="black">
				[起迄總工作天數</font>
				<font color="darkblue"><%=obj.getWorkDayWait()%></font><font color="black">
				/件數</font>
				<font color="darkblue"><%=obj.getNumWait()%></font><font color="black">
				]</font>
			</td>	
			<td class="td_form_white"><font color="black">
				=</font>
				<font color="darkblue"><%=obj.getAverageWait()%></font><font color="black">
				(四捨五入)</font>
			</td>
		</tr>
	 	<tr>
			<td class="td_form_white" style="text-align:center;"><font color="black">承辦決行</font></td>	
			<td class="td_form_white"><font color="black">
				[起迄總工作天數</font>
				<font color="darkblue"><%=obj.getWorkDayDeside()%></font><font color="black">
				/件數</font>
				<font color="darkblue"><%=obj.getNumDeside()%></font><font color="black">
				]</font>
			</td>
			<td class="td_form_white"><font color="black">
				=</font>
				<font color="darkblue"><%=obj.getAverageDeside()%></font><font color="black">
				(四捨五入)</font>
			</td>
		</tr>
		<tr>
			<td class="td_form_white" style="text-align:center;"><font color="black">發文登打</font></td>
			<td class="td_form_white"><font color="black">
				[起迄總工作天數</font>
				<font color="darkblue"><%=obj.getWorkDayClose()%></font><font color="black">
				/件數</font>
				<font color="darkblue"><%=obj.getNumClose()%></font><font color="black">
				]</font>
			</td>
			<td class="td_form_white"><font color="black">
				=</font>
				<font color="darkblue"><%=obj.getAverageClose()%></font><font color="black">
				(四捨五入)</font>
			</td>
		</tr>
		<tr>
			<td class="td_form_white" style="text-align:center;"><font color="black">發文</font></td>	
			<td class="td_form_white"><font color="black">
				[起迄總工作天數</font>
				<font color="darkblue"><%=obj.getWorkDayEnd()%></font><font color="black">
				/件數</font>
				<font color="darkblue"><%=obj.getNumEnd()%></font><font color="black">
				]</font>
			</td>
			<td class="td_form_white"><font color="black">
				=</font>
				<font color="darkblue"><%=obj.getAverageEnd()%></font><font color="black">
				(四捨五入)</font>
			</td>
	 	</tr>
	 	<tr>
			<td class="td_form_white" style="text-align:center;"><font color="black">總計</font></td>	
			<td class="td_form_white">
			</td>
			<td class="td_form_white"><font color="black">
				=</font><font color="darkBlue">
				<%=obj.getTotalSumary()%></font><font color="black">
				(加總)</font>
			</td>
	 	</tr>			
	</table>
	</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
</td></tr>

</table>
</form>
</body>
</html>