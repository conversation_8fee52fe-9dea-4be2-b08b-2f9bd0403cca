package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;

/**
 * 歷年預查編號查詢　
 *
 */
public class PRE4015 extends SuperBean {

	public ArrayList<?> doQueryAll() throws Exception{
		java.util.ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>() ;
		java.util.List<Cedb1019> datas = ServiceGetter.getInstance().getPrefixService().queryCedb1019();
		if (null != datas && datas.size() > 0){
			String[] rowArray = new String[2];
			for(Cedb1019 data : datas){
				rowArray = new String[2];
				rowArray[0] = Common.get(data.getYearNo());
				rowArray[1] = Common.get(data.getPrefixNo());
				arrayList.add(rowArray);
			} // for
		} // if
		return arrayList;
	} // doQueryAll()
		
	public Object doQueryOne() throws Exception{return this;}
	public void doCreate() throws Exception{}
	public void doUpdate() throws Exception{}
    public void doDelete() throws Exception{}

} // PRE8008