<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu"%>
<%@ page import="org.apache.commons.beanutils.BeanUtils"%>
<%@ include file="head.jsp" %>
<%
if (User!=null && User.getPermissions()!=null && User.getPermissions().size()>0) {
	boolean isAuth = false;
	String progCode = Common.get(request.getParameter(WebConstants.PROGRAM_CODE));
	String hackerState = Common.get(request.getParameter("state"));
	if (!"".equals(progCode)) {
		if( User.getPermissions().contains(progCode) ) {
			isAuth = true;
			ServiceGetter.getInstance().getAuthenticationService().setCurrentDtree(progCode);	
		}
		if (isAuth==false) {
			out.flush(); 
			out.close(); 
			return; 
		}			
	}
}
%>