--DROP TABLE EICM.SEARCH_LOG;
-- Create table
CREATE TABLE EICM.SEARCH_LOG (
	ID INTEGER not null,
	KIND VARCHAR2(20),
	KEYWORD VARCHAR2(100),
	RESULT_NUM NUMBER(10),
	PROCESS_TIME NUMBER(10),
	ID_NO VARCHAR2(20),
	LOG_DATE VARCHAR2(20),
	LOG_TIME VARCHAR2(20)
);

-- Add comments to the table 
comment on table EICM.SEARCH_LOG is '檢索紀錄檔';
-- Add comments to the columns 
comment on column EICM.SEARCH_LOG.ID is '主鍵值';
comment on column EICM.SEARCH_LOG.KIND is '類別(全文,輔助)';
comment on column EICM.SEARCH_LOG.KEYWORD is '檢索關鍵字';
comment on column EICM.SEARCH_LOG.RESULT_NUM is '檢索結果筆數';
comment on column EICM.SEARCH_LOG.PROCESS_TIME is '檢索所需時間(微秒)';
comment on column EICM.SEARCH_LOG.ID_NO is '使用者帳號';
comment on column EICM.SEARCH_LOG.LOG_DATE is '紀錄日期';
comment on column EICM.SEARCH_LOG.LOG_TIME is '紀錄時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.SEARCH_LOG
  add constraint PK_SEARCH_LOG primary key (ID)
  using index ;

-- Create sequence 
create sequence EICM.SEQ_SEARCH_LOG_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_SEARCH_LOG
Before Insert ON EICM.SEARCH_LOG Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_SEARCH_LOG_ID.NextVal into :nu.id From Dual;
End;


-- SYNONYM 
create or replace synonym EICM4AP.SEARCH_LOG for EICM.SEARCH_LOG;
create or replace synonym EICM4CMPY.SEARCH_LOG for EICM.SEARCH_LOG;
create or replace synonym EICM4PREFIX.SEARCH_LOG for EICM.SEARCH_LOG;

--GRANT
grant all on EICM.SEARCH_LOG to EICM4AP;
grant all on EICM.SEARCH_LOG to EICM4CMPY;
grant all on EICM.SEARCH_LOG to EICM4PREFIX;
