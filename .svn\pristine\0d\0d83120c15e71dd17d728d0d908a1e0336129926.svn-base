--DROP TABLE EICM.POST_RECORD;
-- Create table
CREATE TABLE EICM.POST_RECORD (
	ID NUMBER(15) not null,
	POST_NO NVARCHAR2(22),
	GET_NAME NVARCHAR2(120),
	GET_ADDR NVARCHAR2(300),
	GET_DATE NVARCHAR2(14),
	GET_TIME NVARCHAR2(14),
	GET_KIND NVARCHAR2(2),
	GET_KIND_REMARK NVARCHAR2(20),
	ATONCE NVARCHAR2(10),
	ATONCE_REMARK NVARCHAR2(10),
	BACK_DATE NVARCHAR2(14),
	BACK_TIME NVARCHAR2(14),
	BACK_REASON NVARCHAR2(10),
	BACK_REASON_REMARK NVARCHAR2(100),
	OTHER_METHOD NVARCHAR2(20),
	OTHER_METHOD_REMARK NVARCHAR2(100),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.POST_RECORD is '郵寄掛號紀錄檔';
-- Add comments to the columns 
comment on column EICM.POST_RECORD.ID is '主鍵值';
comment on column EICM.POST_RECORD.POST_NO is '掛號編號';
comment on column EICM.POST_RECORD.GET_NAME is '收件人姓名';
comment on column EICM.POST_RECORD.GET_ADDR is '收件人地址';
comment on column EICM.POST_RECORD.GET_DATE is '取件日期';
comment on column EICM.POST_RECORD.GET_TIME is '取件時間';
comment on column EICM.POST_RECORD.GET_KIND is '預查結果領取方式(1:自取,2:郵寄)';
comment on column EICM.POST_RECORD.GET_KIND_REMARK is '領件方式註記';
comment on column EICM.POST_RECORD.ATONCE is '馬上辦_勾選';
comment on column EICM.POST_RECORD.ATONCE_REMARK is '馬上辦_備註';
comment on column EICM.POST_RECORD.BACK_DATE is '退件日期';
comment on column EICM.POST_RECORD.BACK_TIME is '退件時間';
comment on column EICM.POST_RECORD.BACK_REASON is '退件原因(01:查無此人,02:遷移不明,03:地址欠詳,04:查無地址,05:拒收,90:其他)';
comment on column EICM.POST_RECORD.BACK_REASON_REMARK is '退件原因_其他';
comment on column EICM.POST_RECORD.OTHER_METHOD is '處理方式(01:不用寄,02:原址寄,03:改址,04:其他)';
comment on column EICM.POST_RECORD.OTHER_METHOD_REMARK is '處理方式_其他';
comment on column EICM.POST_RECORD.MOD_ID_NO is '異動人員';
comment on column EICM.POST_RECORD.MOD_DATE is '異動日期';
comment on column EICM.POST_RECORD.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.POST_RECORD
  add constraint PK_POST_RECORD primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_POST_RECORD_ID;
-- Create sequence 
create sequence EICM.SEQ_POST_RECORD_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_POST_RECORD
Before Insert ON EICM.POST_RECORD Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_POST_RECORD_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.POST_RECORD for EICM.POST_RECORD;

--GRANT
grant all on EICM.POST_RECORD to EICM4AP;

--103/11/28 於正式區做以下調整
alter table post_record add prefix_no nvarchar2(9)
alter table post_record add apply_name nvarchar2(30)
alter table post_record add apply_id nvarchar2(10)
alter table post_record add apply_tel nvarchar2(17)
alter table post_record add apply_addr nvarchar2(150)
alter table post_record add company_name nvarchar2(60)
alter table post_record add post_type nvarchar2(2)





