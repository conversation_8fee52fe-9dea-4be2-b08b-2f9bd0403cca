<%
/**
程式目的：核准公司名稱列表
程式代號：pre4008
程式日期：1030430
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4008">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="staffList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4008" />
</jsp:include>
<%

if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} // end if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4008.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
else {
	// do nothing
} // end else
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init() {
	if($('#state').val() != "init") {
		$('#listContainer').show();
	} 		
}


function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_approveDateStart,"審核日期起");
	alertStr += checkDate(form1.q_approveDateStart,"審核日期起") ;
	alertStr += checkDate(form1.q_approveDateEnd,"審核日期迄") ;
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}



$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;	
				var target = 'PRE4008_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';	
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doClear":
				form1.q_approveDateStart.value = "";
				form1.q_approveDateEnd.value = "";
				form1.q_idNo.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var dateStart = form1.q_approveDateStart.value;
		var dateEnd = form1.q_approveDateEnd.value;
		var staff = form1.q_idNo.value;
		var companyName = form1.q_companyName.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4008.jsp?dateStart='+dateStart+'&dateEnd='+dateEnd+'&staff='+staff+'&companyName='+companyName);
		if ( x == 'ok'  )
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}


</script>
</head>
<!-- Form area -->

<body topmargin="5" onload="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4008'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">
<tr>
<td class="bg" >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
       <tr>
		<td class="td_form"><font color="red">*</font>審核日期：</td>
		<td class="td_form_white" colspan="3">
			起：<%=View.getPopCalendar("field_Q","q_approveDateStart",obj.getQ_approveDateStart()) %>~迄：<%=View.getPopCalendar("field_Q","q_approveDateEnd",obj.getQ_approveDateEnd()) %>
			(如果只填第一個日期，可以查詢當天的核准預查名稱列表)
			</td>
		</tr>
		<tr>  
		  <td class="td_form" >預查名稱：</td>
		  <td class="td_form_white">
		      <input class="field_Q" type="text" name="q_companyName" size="20" maxlength="50" value="<%=obj.getQ_companyName()%>">
		  </td>
		  <td class="td_form" >承辦人：</td>
          <td class="td_form_white" align="left">
            <select name="q_idNo" id="q_idNo" value="<%=obj.getQ_idNo()%>">
              <%=View.getOption("select id_no as code, staff_name as name from eicm.cedbc000 where group_id = '11'", obj.getQ_idNo(), false, 1) %>
            </select>
                                        筆數：<input type="text" name="total" size="4" value="<%=obj.getTotal()%>" readonly="readonly" class="td_form_white">
			&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
			&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
			&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
		  </td>
		</tr>
				
  </table>
  </div>
  <c:import url="../common/msgbar.jsp">
  	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
  </c:import>
</td>
</tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>


	

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦人</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查名稱</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">特取名稱</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">核准日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">發文日</a></th>
    
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false,false,false,false,false};
  boolean displayArray[] = {true,true,true,true,true,true};
  String[] alignArray = {"center", "center","center","center","center","center"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true"));
  %>
  </tbody>
</table>
</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:center;">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
<!-- 新增按鈕區 -->

</table>
</form>
</body>
</html>