package com.kangdainfo.tcfi.model.osss.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 規費與種類資料檔(OSSM_FEE_DETAIL)
 *
 */

public class OssmFeeDetail extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String telixNo ;         // 電子文件號碼
	private String seqNo ;           // 序號
	private String processNo ;       // 申辦流程代碼
	private String feeType ;         // 規費代碼
	private String feeCost ;         // 規費費用
	
	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getSeqNo() {
		return seqNo;
	}
	public void setSeqNo(String seqNo) {
		this.seqNo = seqNo;
	}
	public String getProcessNo() {
		return processNo;
	}
	public void setProcessNo(String processNo) {
		this.processNo = processNo;
	}
	public String getFeeType() {
		return feeType;
	}
	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}
	public String getFeeCost() {
		return feeCost;
	}
	public void setFeeCost(String feeCost) {
		this.feeCost = feeCost;
	}
	
} // OssmFeeDetail()