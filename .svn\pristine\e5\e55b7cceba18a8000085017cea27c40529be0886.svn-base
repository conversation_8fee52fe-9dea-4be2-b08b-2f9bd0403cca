<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.kangdainfo.sys.common.Constants"%>
<%@ page import="com.google.gson.*"%>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	Gson gson = new GsonBuilder().create();
	HashMap datas = obj.assignNewOneAjax();
	if (null!=datas && !datas.equals("")) {
		out.write(gson.toJson(datas));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>