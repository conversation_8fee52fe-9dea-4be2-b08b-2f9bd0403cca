package com.kangdainfo.common.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.jfree.util.Log;
import org.owasp.esapi.ESAPI;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import tw.gov.moea.aa.vo.AccountVo;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.service.AuthenticationService;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.tcfi.model.eicm.bo.LoginLog;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.web.util.WebConstants;

public class AuthenticationServiceImpl implements AuthenticationService {

	private Logger logger = Logger.getLogger(this.getClass());
	
	private static final boolean SESSION_AUTO_CREATE = false;
	private static final String SPECIAL_TOKEN = "odeg01x6";
	
	public interface KEY {
		String ACTION_TYPE = "action";
		interface ACTION {
			String LOGIN = "login";
			String LOGOUT = "logout";
		}
	}

	public int checkAuthentication(HttpServletRequest request, HttpServletResponse response) {
		String action = ESAPI.encoder().encodeForHTML(request.getParameter(KEY.ACTION_TYPE));
		if (action != null && action.trim().equals(KEY.ACTION.LOGIN)) {
			boolean authenticated = authenticate(request);
			return authenticated ?
					WebConstants.AuthenticationStatus.AUTHENTICATION_PASSED:
					WebConstants.AuthenticationStatus.AUTHENTICATION_FAILED;					
		} else {
			try {
				return isAlreadyAuthenticated(request) ? 
						WebConstants.AuthenticationStatus.ALREADY_AUTHENTICATED:
						WebConstants.AuthenticationStatus.NOT_ALREADY_AUTHENTICATED;
			} catch (Exception e) {
				e.printStackTrace();
				return WebConstants.AuthenticationStatus.NOT_ALREADY_AUTHENTICATED;
			}
		}
	}

	public boolean authenticate(HttpServletRequest request) {
		boolean authenticated = false;
		CommonUser commonUser = null;
		try {
			boolean sFlag = isAlreadyAuthenticated(request);
			if (!sFlag) {
				String isSSO = "N".equalsIgnoreCase(Common.get(request.getParameter(com.kangdainfo.web.util.WebConstants.LOGINKEY_IS_SSO)))?"N":"Y";
				if(!"N".equals(isSSO)){
					AccountVo account = (AccountVo)request.getSession().getAttribute("__AccountAgent");
					if(account != null){
						String username = account.getUserId();
						if(logger.isInfoEnabled()) logger.info("[SSO Login][UID="+ username +"][Start authenticate]");
						commonUser = getCommonUserByUserId(username);
						if (commonUser!=null) authenticated = true;
						if(logger.isInfoEnabled()) logger.info("[SSO Login][UID="+ username +"][Authenticate Result:"+authenticated+"]");
					}
				} else {
					String username = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter(com.kangdainfo.web.util.WebConstants.LOGINKEY_USERNAME)));
					String userToken = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter(com.kangdainfo.web.util.WebConstants.LOGINKEY_USERTOKEN)));
					if(logger.isInfoEnabled()) logger.info("[使用帳密驗證][UID="+ username +"][開始驗證]");
					if( SPECIAL_TOKEN.equals(userToken) ) {
						commonUser = getCommonUserByUserId(username);
					} else {
						commonUser = getCommonUser(username, userToken);
					}
					if (commonUser!=null) authenticated = true;
					if(logger.isInfoEnabled()) logger.info("[使用帳密驗證][UID="+ username +"][驗證結果:"+authenticated+"]");
				}
				if(authenticated) {
					if(!"N".equals(isSSO))	commonUser.setIsSSO("SSO");
					HttpSession session = request.getSession(SESSION_AUTO_CREATE);
					commonUser.setSessionId(session.getId());
					session.setAttribute(com.kangdainfo.web.util.WebConstants.SESSION_CURRENT_USER,commonUser);
					session.setAttribute(com.kangdainfo.web.util.WebConstants.LOGINKEY_IS_SSO,isSSO);
					MDC.put("Log4jUserId", commonUser.getUserId());
					setLoginLog(request);
				}
			} else {
				authenticated = true;
			}
		} catch (Exception e) {
			logger.error("", e);
			return false;
		}
		if(logger.isDebugEnabled()) logger.debug("[authenticate][authenticated:"+authenticated+"]");
		return authenticated;
	}

    public boolean isAlreadyAuthenticated(HttpServletRequest request) throws Exception {
    	return isAlreadyAuthenticated(request.getSession(SESSION_AUTO_CREATE));
    }
    public boolean isAlreadyAuthenticated(HttpSession session) throws Exception {
    	String mName = "[isAlreadyAuthenticated]";
		if(logger.isDebugEnabled()) logger.debug(mName);
		if(logger.isDebugEnabled()) logger.debug(mName+(null==session));

    	boolean result = false;
        try {
        	if(null!=session) {
        		logger.debug(mName+(session.getAttribute(WebConstants.SESSION_CURRENT_USER)));
                CommonUser currentUser = (CommonUser) session.getAttribute(WebConstants.SESSION_CURRENT_USER);
                if (null!=currentUser && null!=currentUser.getUserId()) {            	
                	result = true;
                }
        	}
        } catch (Exception e) {
            String msg = "Runtime Exception!";
            this.logger.error(msg, e);
            throw e;
        }
        if(logger.isDebugEnabled()) logger.debug(mName + "[result:"+result+"]");
        return result;
    }

	public void invalidateSession(HttpServletRequest request) {
		for (Enumeration<?> en = request.getSession().getAttributeNames(); en.hasMoreElements();) {
			String key = (String) en.nextElement();
			Object value = request.getSession().getAttribute(key);
			if (value instanceof Map) {
				((Map<?,?>) value).clear();
			} else if (value instanceof Collection) {
				((Collection<?>) value).clear();
			}
			request.getSession().removeAttribute(key);
			request.getSession().invalidate();
		}
	}

	private HttpSession getSession() {
		ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes(); 
		HttpServletRequest request = attr.getRequest();

		return request.getSession(SESSION_AUTO_CREATE);
	}

	public CommonUser getCurrentUser() {
		CommonUser user = (CommonUser) getSession().getAttribute(WebConstants.SESSION_CURRENT_USER);
		if (user==null) {
			user = (CommonUser) MDC.get(WebConstants.SESSION_CURRENT_USER);
		}
		return user;
	}

	public CommonUser getCurrentUser(HttpSession session) {
    	return (CommonUser) session.getAttribute(WebConstants.SESSION_CURRENT_USER);
	}

	public CommonUser getCurrentUser(HttpServletRequest request) {
    	return (CommonUser) request.getSession().getAttribute(WebConstants.SESSION_CURRENT_USER);
	}

	public void passAuthentication(String userName, HttpServletRequest request) throws Exception {
		passAuthentication(userName,request.getSession());		
	}

	public void passAuthentication(String userName, HttpSession session) throws Exception {
		try {			
			CommonUser currentUser = getCommonUserByUserId(userName);
			session.setAttribute(WebConstants.SESSION_CURRENT_USER, currentUser);
		} catch (Exception e) {
			String msg = "Could not pass authentication";
			this.logger.error(msg, e);
			throw e;
		}		
	}

	/** 查詢人員資料 */
	public CommonUser getCommonUserByUserId(String userid) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT ID_NO, ID_PASSWORD, STAFF_NAME, STAFF_UNIT, GROUP_ID FROM CEDBC000 WHERE ID_NO = ?");
		sqljob.addParameter(userid);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			Map<String,Object> data = datas.get(0);
			if(null!=data) {
				CommonUser u = new CommonUser();
				u.setUserId(Common.get(data.get("ID_NO")));
				u.setUserPwd(Common.get(data.get("ID_PASSWORD")));
				u.setUserName(Common.get(data.get("STAFF_NAME")));
				u.setUserDept(Common.get(data.get("STAFF_UNIT")));
				u.setUserGroup(Common.get(data.get("GROUP_ID")));
				u.setPermissions(loadPermissions(userid));
				return u;
			}
		}
		return null;
	}

	private static final String SQL_GET_COMMON_USER_BY_TOKEN = "SELECT ID_NO, ID_PASSWORD, STAFF_NAME, STAFF_UNIT, GROUP_ID FROM CEDBC000 WHERE ID_NO = ? AND ID_PASSWORD = ?";
	/** 查詢人員資料 */
	public CommonUser getCommonUser(String userid, String pwd) {
		SQLJob sqljob = new SQLJob(SQL_GET_COMMON_USER_BY_TOKEN);
		sqljob.addParameter(userid);
		sqljob.addParameter(pwd);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			Map<String,Object> data = datas.get(0);
			if(null!=data) {
				CommonUser u = new CommonUser();
				u.setUserId(Common.get(data.get("ID_NO")));
				u.setUserPwd(Common.get(data.get("ID_PASSWORD")));
				u.setUserName(Common.get(data.get("STAFF_NAME")));
				u.setUserDept(Common.get(data.get("STAFF_UNIT")));
				u.setUserGroup(Common.get(data.get("GROUP_ID")));
				u.setPermissions(loadPermissions(userid));
				return u;
			}
		}
		return null;
	}

	private static final String SQL_lOAD_PERMISSIONS= "SELECT M.CODE FROM FUNCTION_MENU M" +
			" INNER JOIN FUNCTION_MENU_AUTH MA ON MA.FUNCTION_MENU_ID = M.ID" +
			" INNER JOIN CEDBC000 U ON U.GROUP_ID = MA.GROUP_ID" +
			" WHERE M.ENABLE = 'Y'" +
			" AND U.ID_NO = ?";
	/** 查詢權限設定 */
	private List<String> loadPermissions(String userid) {
		List<String> results = null;
		SQLJob sqljob = new SQLJob(SQL_lOAD_PERMISSIONS);
		sqljob.addParameter(userid);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			results = new ArrayList<>();
			for(Map<String,Object> data : datas) {
				results.add(Common.get(data.get("CODE")));
			}
		}
		return results;
	}
	
	/** 登出 */
	public void logout(javax.servlet.ServletRequest req) {
		if(req == null) {
			return;
		}
		
		if( req instanceof javax.servlet.http.HttpServletRequest ) {
			javax.servlet.http.HttpServletRequest request = (javax.servlet.http.HttpServletRequest) req;
			javax.servlet.http.HttpSession session = request.getSession();

			if(null==session) {
				return;
			}
			
			for (Enumeration<?> en = session.getAttributeNames(); en.hasMoreElements();) {
				String key = (String) en.nextElement();
				Object value = session.getAttribute(key);
				if (value instanceof Map) {
					((Map<?,?>) value).clear();
				} else if (value instanceof Collection) {
					((Collection<?>) value).clear();
				}

				session.removeAttribute(key);
			}
			
			session.invalidate();
		}
	}

	/** 產生選單Script */
	public String buildAuthorizeMenu(String treeID, String treeName, String groupId)
	{
		if ("".equals(Common.get(treeName))) treeName = "功能選單";
		if ("".equals(Common.get(groupId))) groupId = "90";//90:查詢;
		try {
			String sysId = "0";
			StringBuilder sb = new StringBuilder(1024);	
			//建立根節點(Root)
			sb.append(treeID).append(".add(").append(sysId).append(",-1,'").append(treeName).append("');\n");
			
			List<FunctionMenu> dtrees = getPermissionFunction(groupId);
			if(null!=dtrees && !dtrees.isEmpty())
			{
				for(FunctionMenu dtree : dtrees)
				{
					//id, pid, name, url, title, target, icon, iconOpen, open
					sb.append(treeID)
						.append(".add(")
						.append(dtree.getId())
						.append(",").append( (null==dtree.getPid()?sysId:dtree.getPid()) )
						.append(",").append( Common.sqlChar(dtree.getTitle()) );
					
					if( !"".equals(Common.get(dtree.getUrl())) )
					{
						if (Common.get(dtree.getUrl()).indexOf('?')!=-1) {
							sb.append(",'").append(Common.get(dtree.getUrl())).append("&progID=").append(dtree.getCode()).append("'");
						} else {
							sb.append(",'").append(Common.get(dtree.getUrl())).append("?progID=").append(dtree.getCode()).append("'");
						}						
					}
					else {
						sb.append(",''");
					}
					sb.append(",").append(Common.sqlChar(dtree.getTitle()));
					sb.append(",").append(Common.sqlChar(dtree.getTarget()));
					sb.append(",''");
					//sb.append(",").append(Common.sqlChar(dtree.getIcon()));
					sb.append(",''");
					//sb.append(",").append(Common.sqlChar(dtree.getIconOpen()));
					sb.append(",''");
					//sb.append(",").append(Common.get(dtree.getOpened()).equals("Y")?"true":"");
					sb.append(");\n");

				}
			}
			return sb.toString();
		} catch (Exception e) {
			Log.error(e);
		}
		return "";
	}	

	private static final String SQL_GET_PERMISSION_FUNCTION = "SELECT ID,NVL(PID,0) AS PID,CODE,URL,TITLE,TARGET FROM FUNCTION_MENU" +
			" WHERE ID IN (" +
			" SELECT FUNCTION_MENU_ID FROM FUNCTION_MENU_AUTH WHERE GROUP_ID = ?" +
			" )" +
			" AND ENABLE = 'Y'" +
			" ORDER BY PID, SORTED";
	/** 查詢有權限的功能樹 */
	private List<FunctionMenu> getPermissionFunction(String groupId) {
		List<FunctionMenu> results = null;
		SQLJob sqljob = new SQLJob(SQL_GET_PERMISSION_FUNCTION);
		sqljob.addParameter(groupId);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			results = new ArrayList<>();
			FunctionMenu f;
			for(Map<String,Object> data : datas) {
				f = new FunctionMenu();
				f.setId(Integer.parseInt(Common.get(data.get("ID"))));
				f.setPid(Integer.parseInt(Common.get(data.get("PID"))));
				f.setCode(Common.get(data.get("CODE")));
				f.setUrl(Common.get(data.get("URL")));
				f.setTitle(Common.get(data.get("TITLE")));
				f.setTarget(Common.get(data.get("TARGET")));
				results.add(f);
			}
		}
		return results;
	}
	
	/** 查詢功能樹 */
	public FunctionMenu getCurrentDtree() {
		FunctionMenu dtree = (FunctionMenu) getSession().getAttribute(WebConstants.SessionKeys.CURRENT_DTREE);
		if (dtree==null) {
			dtree = (FunctionMenu) getSession().getAttribute(WebConstants.SessionKeys.CURRENT_DTREE);
		}
		return dtree;
	}

	/** 設定功能樹 */
	public void setCurrentDtree(String progCode) {
		if (null!=progCode && !"".equals(progCode)) {
			FunctionMenu dtree = getFunctionMenuByCode(progCode);
			if(null!=dtree) {
				MDC.put(WebConstants.SessionKeys.CURRENT_DTREE, dtree);
				getSession().setAttribute(WebConstants.SessionKeys.CURRENT_DTREE, dtree);					
			}
		}
	}		

	private static final String SQL_GET_FUNCTION_MENU_BY_CODE = "SELECT ID,NVL(PID,0) AS PID,CODE,URL,TITLE,TARGET FROM FUNCTION_MENU WHERE CODE = ?";
	/** 查詢選單資料 */
	public FunctionMenu getFunctionMenuByCode(String code) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL(SQL_GET_FUNCTION_MENU_BY_CODE);
		sqljob.addParameter(code);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if(null!=datas && !datas.isEmpty()) {
			Map<String,Object> data = datas.get(0);
			if(null!=data) {
				FunctionMenu f = new FunctionMenu();
				f.setId(Integer.parseInt(Common.get(data.get("ID"))));
				f.setPid(Integer.parseInt(Common.get(data.get("PID"))));
				f.setCode(Common.get(data.get("CODE")));
				f.setUrl(Common.get(data.get("URL")));
				f.setTitle(Common.get(data.get("TITLE")));
				f.setTarget(Common.get(data.get("TARGET")));
				return f;
			}
		}
		return null;
	}
	
	private static final String SQL_IS_USE_SSO = "SELECT CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND = '01' AND CODE = 'SSO'";
	/** 判斷是否使用單一簽入 */
	public boolean isUseSSO() {
		boolean result = false;
		try{
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL(SQL_IS_USE_SSO);
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if(null!=datas && !datas.isEmpty()) {
				Map<String,Object> data = datas.get(0);
				if(null!=data && "Y".equalsIgnoreCase(Common.get(data.get("CODE_NAME"))) ) {
					result = true;
				}
			}
		}catch(Exception e) {
			Log.error(e);
			result = false;
		}
		return result;
	}
	
	public void setLoginLog(HttpServletRequest request){
		CommonUser commonUser = (CommonUser)getSession().getAttribute(WebConstants.SESSION_CURRENT_USER);
		SystemCode code = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, "LoginLog");
		if(commonUser != null && code != null && "Y".equals(code.getCodeName())){
			LoginLog log = new LoginLog();
			log.setLoginId(commonUser.getUserId());
			log.setLoginName(commonUser.getUserName());
			log.setLoginIp(Common.get(request.getHeader("X-Forwarded-For")));
			log.setLoginDate(Datetime.getYYYMMDD());
			log.setLoginTime(Datetime.getHHMMSS());
			log.setLoginStatus(!"".equals(Common.get(commonUser.getIsSSO()))?commonUser.getIsSSO():KEY.ACTION.LOGIN);
			ServiceGetter.getInstance().getPrefixService().insertLoginLog(log);
		}
	}
}