package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;
import com.kangdainfo.util.lang.CommonStringUtils;

public class GeneralityBusitemDao extends BaseDaoJdbc implements RowMapper<GeneralityBusitem> {

	private static final String SQL_findByBanNo = "SELECT * FROM GENERALITY_BUSITEM WHERE BAN_NO = ?";
	public GeneralityBusitem findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByBanNo);
		sqljob.addParameter(banNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<GeneralityBusitem> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}
	
	public GeneralityBusitem insert(GeneralityBusitem bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getBanNo())) return null;
		//check exist
		GeneralityBusitem t = findByBanNo(bo.getBanNo());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO GENERALITY_BUSITEM (");
		sqljob.appendSQL(" BAN_NO");
		sqljob.appendSQL(",TELIX_NO");
		sqljob.appendSQL(",PREFIX_NO");
		sqljob.appendSQL(",CMPY_MARK");
		sqljob.appendSQL(") VALUES (?,?,?,?)");
		sqljob.addParameter(bo.getBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(bo.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(bo.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(bo.getCmpyMark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		return findByBanNo(bo.getBanNo());
	}
	
	public GeneralityBusitem update(GeneralityBusitem bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getBanNo())) return null;
		//check exist
		GeneralityBusitem t = findByBanNo(bo.getBanNo());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE GENERALITY_BUSITEM");
			sqljob.appendSQL(" SET");
			sqljob.appendSQL("   TELIX_NO = ?");
			sqljob.appendSQL("  ,PREFIX_NO = ?");
			sqljob.appendSQL("  ,CMPY_MARK = ?");
			sqljob.appendSQL(" WHERE BAN_NO = ?");
			sqljob.addParameter(bo.getTelixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getPrefixNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getCmpyMark());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getBanNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
			return findByBanNo(bo.getBanNo());
		}
	}

	/** 把 網路流水號 換成 預查編號 */
	public void updateTelixNoToPrefixNo(String prefixNo, String banNo, String telixNo) {
		if("".equals(Common.get(prefixNo))) return;
		if("".equals(Common.get(banNo))) return;
		if("".equals(Common.get(telixNo))) return;

		SQLJob sqljob = new SQLJob("UPDATE GENERALITY_BUSITEM SET TELIX_NO=NULL, PREFIX_NO=? WHERE BAN_NO=? AND TELIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(banNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(telixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public void delete(GeneralityBusitem bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getBanNo()) ) {
			//delete
			SQLJob sqljob = new SQLJob("DELETE FROM GENERALITY_BUSITEM WHERE BAN_NO = ?");
			sqljob.addParameter(bo.getBanNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}

	public GeneralityBusitem mapRow(ResultSet rs, int idx) throws SQLException {
		GeneralityBusitem obj = null;
		if(null!=rs) {
			obj = new GeneralityBusitem();
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setTelixNo(rs.getString("TELIX_NO"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setCmpyMark(rs.getString("CMPY_MARK"));
		}
		return obj;
	}

}