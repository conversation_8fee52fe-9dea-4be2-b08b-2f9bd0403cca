package com.kangdainfo.tcfi.model.lms.dao;

import java.util.Date;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.NumberFormat;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.StringUtility;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.common.util.Datetime;


/** 有限合夥基本資料DAO */
public class LmsmBussMainDao extends BaseDaoJdbc implements RowMapper<LmsmBussMain>  {
	
	private static final String SELECT_LIST_SQL =
			"SELECT A.REG_UNIT_CODE, <PERSON><PERSON>, <PERSON><PERSON>_NO, <PERSON><PERSON>_<PERSON>, <PERSON><PERSON>_<PERSON>AME, A.CURR_STATUS,"+
	       	"	(SELECT DESCRIPTION FROM LMSD_CODEMAPPING WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND KIND = 'STAT' AND CODE = A.CURR_STATUS AND ROWNUM = 1) AS CURR_STATUS_NAME,"+
	       	"	A.RES_NAME, A.REGISTER_FUNDS, A.REAL_FUNDS, A.BUSS_ADDRESS, A.ORG_CODE, to_char(A.SET_APP_DATE,'yyyymmdd') as set_app_date, A.SET_APP_NO, to_char(A.LAST_CHG_DATE,'yyyymmdd') as last_chg_date, A.INPU_ITEM_OLD,"+
	       	"	A.CONTACT_TEL, A.ADDRESS_CODE, A.BUSS_ZIPCODE, A.BUSS_ITEM_OLD, A.CLOSE_APP_NO, A.CANCEL_APP_NO, to_char(A.REST_BEG_DATE,'yyyymmdd'),"+
	       	"	to_char(A.REST_END_DATE,'yyyymmdd') as rest_end_date, (SELECT NAME FROM LMSM_DIRECTOR WHERE BAN_NO = A.BAN_NO AND DUTY_CODE = '07' AND ROWNUM = 1) AS NAME,"+
	       	"	A.TBPK, to_char(A.CANCEL_APP_DATE, 'yyyymmdd') as cancel_app_date, A.BUSS_WORD_NO, A.INVEST_CODE, A.CHINA_CODE, "+
	       	"	(SELECT CODE_CONT FROM LMSD_AREA WHERE CODE = A.ADDRESS_CODE AND ROWNUM = 1) AS AREA_NAME, " +
	       	"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN A.CAPI_BAN_NO ELSE '' END) AS CAPI_BAN_NO, " +
	       	"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN (SELECT BUSS_NAME FROM LMSM_BUSS_MAIN WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND BAN_NO = A.CAPI_BAN_NO) ELSE '' END) AS CAPI_BUSS_NAME, " +
	       	"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN 'Y' ELSE 'N' END) AS IS_BRANCH, " +
	       			//"	ROWSTOSTRING('SELECT NAME FROM LMSM_DIRECTOR WHERE REG_UNIT_CODE='''||A.REG_UNIT_CODE||''' and BAN_NO='''||A.BAN_NO||''' AND PARPK='''||A.TBPK||''' AND DUTY_CODE=''02''',',',null) AS PARTNER_NAME, "+
	       	"	(SELECT AGENCYNAME FROM LMSD_REGUNIT WHERE REG_UNIT_CODE = A.REG_UNIT_CODE) AS AGENCYNAME, "+
	       	"	(SELECT DESCRIPTION FROM LMSD_CODEMAPPING WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND KIND = 'ORG' AND CODE = A.ORG_CODE) "+
	       	"		||(CASE WHEN (NVL(A.INVEST_CODE,'N') = 'Y' AND NVL(A.CHINA_CODE,'N') ='Y') THEN '(僑外資/陸資)'" +
	       	"				WHEN (NVL(A.INVEST_CODE,'N') = 'Y') THEN '(僑外資)'" +
	       	"				WHEN (NVL(A.CHINA_CODE,'N') = 'Y') THEN '(陸資)' ELSE '' END) AS ORG_CODE_NAME, " +
	       	"	A.LIVE_BEG_DATE, A.LIVE_END_DATE, A.LAST_CHG_NO, A.AGREED_DISBAND " +
	       	"FROM LMSM_BUSS_MAIN A "+
	       	"WHERE A.IS_NEWEST = 'Y' AND A.BAN_NO = ? ";
	
	/** 查詢有限合夥清單 */
    public List<?> queryLmsInfoList(String agencyCode, String banNo, String lmsName, String cityCode2, String cityCode1, String addr, String orgType) {
    	StringBuffer condition = new StringBuffer();
        if (StringUtils.isNotBlank(banNo)) {
        	condition.append("(BAN_NO = '" + banNo + "'");
        	condition.append(" OR CAPI_BAN_NO = '" + banNo + "' )");
        }
        if (StringUtils.isNotBlank(lmsName)) {
        	if (condition.length()>0) condition.append(" AND ");
        	condition.append("BUSS_NAME LIKE '%" + lmsName + "%'");
        }
        if (StringUtils.isNotBlank(cityCode2)) {
        	if (condition.length()>0) condition.append(" AND ");
        	condition.append("ADDRESS_CODE LIKE '" + cityCode2 + "%'");
        }else if (StringUtils.isNotBlank(cityCode1)) {
        	if (condition.length()>0) condition.append(" AND ");
        	condition.append("ADDRESS_CODE LIKE '" + cityCode1 + "%'");
        }else{
        	if(!"allbf".equals(agencyCode)) {
        		if (condition.length()>0) condition.append(" AND ");
        		condition.append("AGENCYCODE = '" + agencyCode + "'");
        	}
    	}
        if (StringUtils.isNotBlank(addr)) {
        	if (condition.length()>0) condition.append(" AND (");
        	condition.append("BUSS_ADDR_COMB LIKE '" + StringUtility.transHalf2All(addr) + "%'");
            //因為資料庫存的地址有時候是半形 所以需要考慮全形及半形的地址查詢條件
        	condition.append(" OR ");
        	condition.append("BUSS_ADDR_COMB LIKE '" + StringUtility.transAll2Half(addr) + "%'");
        	condition.append(" OR ");
        	condition.append("BUSS_ADDRESS LIKE '" + StringUtility.transHalf2All(addr) + "%'");
        	condition.append(" OR ");
        	condition.append("BUSS_ADDRESS LIKE '" + StringUtility.transAll2Half(addr) + "%'");
        	condition.append(" ) ");
        }
        if(condition.length()>0) condition.append(" AND ");
        if(!"2".equals(orgType)){
        	//有限合夥(外國有限合夥不需公示)
        	condition.append("ORG_CODE = '18'");
        }else{
        	//分支機構
        	condition.append("ORG_CODE IN ('19', '21')");
        }
        
        if(condition.length()>0) condition.append(" AND ");
        condition.append("IS_NEWEST = 'Y'");
        
    	List<?> list = getJdbcTemplate().query(SELECT_LIST_SQL+" WHERE "+condition.toString(), this);
    	return list;
    }
	
    
    /** 查詢有限合夥基本資料 */
   	public LmsmBussMain queryLmsInfoByBanNo(String banNo) {
   		List<?> list = null;
       	String sql = 
       			"SELECT A.REG_UNIT_CODE, A.AGENCYCODE, A.CASE_NO, A.BAN_NO, A.BUSS_NAME, A.CURR_STATUS,"+
       			"	(SELECT DESCRIPTION FROM lms.LMSD_CODEMAPPING WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND KIND = 'STAT' AND CODE = A.CURR_STATUS AND ROWNUM = 1) AS CURR_STATUS_NAME,"+
       			"	A.RES_NAME, A.REGISTER_FUNDS, A.REAL_FUNDS, A.BUSS_ADDRESS, A.ORG_CODE, to_char(A.SET_APP_DATE, 'yyyymmdd') as set_app_date, A.SET_APP_NO, to_char(A.LAST_CHG_DATE,'yyyymmdd') as last_chg_date, A.INPU_ITEM_OLD,"+
       			"	A.CONTACT_TEL, A.ADDRESS_CODE, A.BUSS_ZIPCODE, A.BUSS_ITEM_OLD, A.CLOSE_APP_NO, A.CANCEL_APP_NO, to_char(A.REST_BEG_DATE,'yyyymmdd') as rest_beg_date,"+
       			"	to_char(A.REST_END_DATE,'yyyymmdd') as rest_end_date, (SELECT NAME FROM lms.LMSM_DIRECTOR WHERE BAN_NO = A.BAN_NO AND DUTY_CODE = '07' AND ROWNUM = 1) AS NAME,"+
       			"	A.TBPK, to_char(A.CANCEL_APP_DATE,'yyyymmdd') as cancel_app_date, A.BUSS_WORD_NO, A.INVEST_CODE, A.CHINA_CODE, "+
       			"	(SELECT CODE_CONT FROM lms.LMSD_AREA WHERE CODE = A.ADDRESS_CODE AND ROWNUM = 1) AS AREA_NAME, " +
       			"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN A.CAPI_BAN_NO ELSE '' END) AS CAPI_BAN_NO, " +
       			"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN (SELECT BUSS_NAME FROM lms.LMSM_BUSS_MAIN WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND BAN_NO = A.CAPI_BAN_NO AND ROWNUM = 1) ELSE '' END) AS CAPI_BUSS_NAME, " +
       			"	(CASE WHEN (A.ORG_CODE = '19' OR A.ORG_CODE = '21') THEN 'Y' ELSE 'N' END) AS IS_BRANCH, " +
       			//"	ROWSTOSTRING('SELECT NAME FROM LMSM_DIRECTOR WHERE REG_UNIT_CODE='''||A.REG_UNIT_CODE||''' and BAN_NO='''||A.BAN_NO||''' AND PARPK='''||A.TBPK||''' AND DUTY_CODE=''02''',',',null) AS PARTNER_NAME, "+
       			"	(SELECT AGENCYNAME FROM lms.LMSD_REGUNIT WHERE REG_UNIT_CODE = A.REG_UNIT_CODE) AS AGENCYNAME, "+
       			"	(SELECT DESCRIPTION FROM lms.LMSD_CODEMAPPING WHERE REG_UNIT_CODE = A.REG_UNIT_CODE AND KIND = 'ORG' AND CODE = A.ORG_CODE) "+
       			"		||(CASE WHEN (NVL(A.INVEST_CODE,'N') = 'Y' AND NVL(A.CHINA_CODE,'N') ='Y') THEN '(僑外資/陸資)'" +
       			"				WHEN (NVL(A.INVEST_CODE,'N') = 'Y') THEN '(僑外資)'" +
       			"				WHEN (NVL(A.CHINA_CODE,'N') = 'Y') THEN '(陸資)' ELSE '' END) AS ORG_CODE_NAME, " +
       			"	A.LIVE_BEG_DATE, A.LIVE_END_DATE, A.LAST_CHG_NO, A.AGREED_DISBAND " + 
       			"   ,(select MAX(b.reserve_no) from lms.LMSM_CASE_RCV b where a.ban_no = b.ban_no) as prefix_no " +
       			" FROM lms.LMSM_BUSS_MAIN A "+
       			" WHERE A.IS_NEWEST = 'Y' AND A.BAN_NO = ? ";
   		
   			Object[] params = new Object[] {banNo};
   			int[] types = {Types.VARCHAR};
   			list = getJdbcTemplate().query(sql,params,types, this);
   		
   		if (null==list || list.size() == 0) return null;
   		
   		return (LmsmBussMain)list.get(0);
   	}
   	
   	private static final String SELECT_BRANCH_SQL =
			"SELECT AGENCYCODE, BAN_NO, BUSS_NAME, CURR_STATUS, RES_NAME, LAST_CHG_DATE, CLOSE_APP_NO, CANCEL_APP_NO," +
	    	"	(SELECT NAME FROM lms.LMSM_DIRECTOR WHERE BAN_NO = LMSM_BUSS_MAIN.BAN_NO AND DUTY_CODE = '07' AND ROWNUM = 1) as NAME, " +
	    	"	ADDRESS_CODE, BUSS_ADDRESS, " + 
	    	"	(SELECT CODE_CONT FROM lms.LMSD_AREA WHERE CODE = LMSM_BUSS_MAIN.ADDRESS_CODE AND ROWNUM = 1) AS AREA_NAME, " +
	    	"	(SELECT AGENCYNAME FROM lms.LMSD_REGUNIT WHERE REG_UNIT_CODE = LMSM_BUSS_MAIN.REG_UNIT_CODE) AS AGENCYNAME " +
	    	"FROM lms.LMSM_BUSS_MAIN ";
   	
    /** 查詢有限合夥分支機構清單 */
    public List<?> queryBranchByCapiBanNo(String banNo){
    	
    	Object[] params = new Object[] {banNo};
    	int[] types = {Types.VARCHAR};

    	List<?> list = getJdbcTemplate().query(SELECT_BRANCH_SQL + " WHERE CAPI_BAN_NO = ? AND ORG_CODE IN ('11','13')", params, types, new LmsInfoListRowMapper());
    	if(list.isEmpty() && list.size() == 0)	return null;

    	return list;
	}
      
    /**
     * 更新有限合夥資料
     * 113/04/22
     * @param lmsmBussMain
     * @return
     */
    public LmsmBussMain update(LmsmBussMain lmsmBussMain) {
    	if(lmsmBussMain == null || null == lmsmBussMain.getBanNo()) return null;
    	LmsmBussMain bo = queryLmsInfoByBanNo(lmsmBussMain.getBanNo());
    	if(bo == null) return null;
    	else {
//    		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//    		Date d1 = null;
//    		Date d2 = null;
    		try {
//	            d1= dateFormat.parse(Datetime.formatDateToDb(lmsmBussMain.getApproveDate()));
//	            d2= dateFormat.parse(Datetime.formatDateToDb(lmsmBussMain.getLastChangeDate()));
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
    		SQLJob sqljob = new SQLJob("UPDATE LMS.LMSM_BUSS_MAIN SET");
			sqljob.appendSQL("AGENCYCODE=?");
			sqljob.appendSQL(",REG_UNIT_CODE=?");
			sqljob.appendSQL(",CASE_NO=?");
			sqljob.appendSQL(",BUSS_NAME=?");
			sqljob.appendSQL(",CURR_STATUS=?");
			sqljob.appendSQL(",ADDRESS_CODE=?");
			sqljob.appendSQL(",BUSS_ADDRESS=?");
			sqljob.appendSQL(",RES_NAME=?");
			sqljob.appendSQL(",REGISTER_FUNDS=?");
			sqljob.appendSQL(",REAL_FUNDS=?");
			sqljob.appendSQL(",ORG_CODE=?");
//			sqljob.appendSQL(",SET_APP_DATE=?");// 版本過舊，JDBC無法支援Date轉換
			sqljob.appendSQL(",MANAGER_NAME=?");
			sqljob.appendSQL(",SET_APP_NO=?");
//			sqljob.appendSQL(",LAST_CHG_DATE=?");// 版本過舊，JDBC無法支援Date轉換
			sqljob.appendSQL(",LAST_CHG_NO=?");
			sqljob.appendSQL("WHERE BAN_NO=?");
			sqljob.addParameter(lmsmBussMain.getAgencyCode());
			sqljob.addParameter(lmsmBussMain.getRegUnitCode());
			sqljob.addParameter(lmsmBussMain.getCaseNo());
			sqljob.addParameter(lmsmBussMain.getLmsName());
			sqljob.addParameter(lmsmBussMain.getStatus());
			sqljob.addParameter(lmsmBussMain.getAreaCode());
			sqljob.addParameter(lmsmBussMain.getBusiAddr());
			sqljob.addParameter(lmsmBussMain.getRepName());
			sqljob.addParameter(Integer.parseInt(lmsmBussMain.getRegisterFunds().replaceAll(",", "")));
			sqljob.addParameter(Integer.parseInt(lmsmBussMain.getRealFunds().replaceAll(",", "")));
			sqljob.addParameter(lmsmBussMain.getOrganType());
//			sqljob.addParameter(d1);// 版本過舊，JDBC無法支援Date轉換
			sqljob.addParameter(lmsmBussMain.getManagerName());
			sqljob.addParameter(lmsmBussMain.getApproveNo());
//			sqljob.addParameter(d2);// 版本過舊，JDBC無法支援Date轉換
			sqljob.addParameter(lmsmBussMain.getLastChgNo());
			sqljob.addParameter(lmsmBussMain.getBanNo());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
					,new int[]{
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.INTEGER,
						java.sql.Types.INTEGER,
//						java.sql.Types.DATE,// 版本過舊，JDBC無法支援Date轉換
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
//						java.sql.Types.DATE,// 版本過舊，JDBC無法支援Date轉換
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR
					});
			return queryLmsInfoByBanNo(bo.getBanNo());
    	}
    }
   	
   	
   	public LmsmBussMain mapRow(ResultSet rs, int index) throws SQLException {
    	LmsmBussMain vo = new LmsmBussMain();
        vo.setRegUnitCode(StringUtils.trimToEmpty(rs.getString("REG_UNIT_CODE")));
        vo.setAgencyCode(StringUtils.trimToEmpty(rs.getString("AGENCYCODE")));
        vo.setCaseNo(StringUtils.trimToEmpty(rs.getString("CASE_NO")));
        vo.setBanNo(StringUtils.trimToEmpty(rs.getString("BAN_NO")));
        vo.setLmsName(StringUtils.trimToEmpty(rs.getString("BUSS_NAME")));
        vo.setStatus(StringUtils.trimToEmpty(rs.getString("CURR_STATUS")));
        vo.setStatusName(StringUtils.trimToEmpty(rs.getString("CURR_STATUS_NAME")));
        vo.setRepName(StringUtils.trimToEmpty(rs.getString("RES_NAME")));
        vo.setManagerName(StringUtils.trimToEmpty(rs.getString("NAME")));
        vo.setRegisterFunds((rs.getString("REGISTER_FUNDS")==null)?"":NumberFormat.moneyWithComma(rs.getString("REGISTER_FUNDS")));
        vo.setRealFunds((rs.getString("REAL_FUNDS")==null)?"":NumberFormat.moneyWithComma(rs.getString("REAL_FUNDS")));
        vo.setBusiAddr(StringUtils.trimToEmpty(rs.getString("BUSS_ADDRESS")));
        vo.setOrganType(StringUtils.trimToEmpty(rs.getString("ORG_CODE")));
        vo.setApproveDate(StringUtils.trimToEmpty(rs.getString("SET_APP_DATE")));
        vo.setApproveNo(StringUtils.trimToEmpty(rs.getString("SET_APP_NO")));
        vo.setLastChangeDate(StringUtils.trimToEmpty(rs.getString("LAST_CHG_DATE")));
        vo.setBusinessItemFlag((StringUtils.isBlank(rs.getString("INPU_ITEM_OLD")))?"1":rs.getString("INPU_ITEM_OLD"));
        vo.setTelno(StringUtils.trimToEmpty(rs.getString("CONTACT_TEL")));
        vo.setAreaCode(StringUtils.trimToEmpty(rs.getString("ADDRESS_CODE")));
        vo.setPostalcode(StringUtils.trimToEmpty(rs.getString("BUSS_ZIPCODE")));
        vo.setRestBegDate(StringUtils.trimToEmpty(rs.getString("REST_BEG_DATE")).replaceAll("-", ""));
        vo.setRestEndDate(StringUtils.trimToEmpty(rs.getString("REST_END_DATE")).replaceAll("-", ""));
        vo.setOldBusinessItems(StringUtils.trimToEmpty(rs.getString("BUSS_ITEM_OLD")));
        vo.setTbpk(StringUtils.trimToEmpty(rs.getString("TBPK")));
        vo.setCancelAppNo(StringUtils.trimToEmpty(rs.getString("CANCEL_APP_NO")));
        vo.setCancelAppDate(StringUtils.trimToEmpty(rs.getString("CANCEL_APP_DATE")).replaceAll("-", ""));
        vo.setBussWordNo(StringUtils.trimToEmpty(rs.getString("BUSS_WORD_NO")));
        vo.setInvestCode(StringUtils.trimToEmpty(rs.getString("INVEST_CODE")));
        vo.setChinaCode(StringUtils.trimToEmpty(rs.getString("CHINA_CODE")));
        vo.setAreaName(StringUtils.trimToEmpty(rs.getString("AREA_NAME")));
		vo.setCapiBanNo(StringUtils.trimToEmpty(rs.getString("CAPI_BAN_NO")));
		vo.setCapiBussName(StringUtils.trimToEmpty(rs.getString("CAPI_BUSS_NAME")));
        vo.setPartnerName(StringUtils.trimToEmpty(rs.getString("CHINA_CODE")));
        vo.setIsBranch(StringUtils.trimToEmpty(rs.getString("IS_BRANCH")));
        vo.setAgencyName(StringUtils.trimToEmpty(rs.getString("AGENCYNAME")));
        vo.setOrganName(StringUtils.trimToEmpty(rs.getString("ORG_CODE_NAME")));
        vo.setLiveBegDate(StringUtils.trimToEmpty(rs.getString("LIVE_BEG_DATE")));
        vo.setLiveEndDate(StringUtils.trimToEmpty(rs.getString("LIVE_END_DATE")));
        vo.setLastChgNo(StringUtils.trimToEmpty(rs.getString("LAST_CHG_NO")));
        vo.setAgreedDisBand(StringUtils.trimToEmpty(rs.getString("AGREED_DISBAND")));
        vo.setPrefixNo(StringUtils.trimToEmpty(rs.getString("PREFIX_NO")));
        return vo;
    }
   	
}

class LmsInfoListRowMapper implements RowMapper<LmsmBussMain> {
    public LmsmBussMain mapRow(ResultSet rs, int index) throws SQLException {
    	LmsmBussMain vo = new LmsmBussMain();
        vo.setAgencyCode(StringUtils.trimToEmpty(rs.getString("AGENCYCODE")));      
        vo.setBanNo(StringUtils.trimToEmpty(rs.getString("BAN_NO")));
        vo.setLmsName(StringUtils.trimToEmpty(rs.getString("BUSS_NAME")));
        vo.setStatus(StringUtils.trimToEmpty(rs.getString("CURR_STATUS")));        
        vo.setRepName(StringUtils.trimToEmpty(rs.getString("RES_NAME")));        
        vo.setManagerName(StringUtils.trimToEmpty(rs.getString("NAME")));
        vo.setLastChangeDate(StringUtils.trimToEmpty(rs.getString("LAST_CHG_DATE")));
        vo.setAreaCode(StringUtils.trimToEmpty(rs.getString("ADDRESS_CODE")));
        vo.setBusiAddr(StringUtils.trimToEmpty(rs.getString("BUSS_ADDRESS")));
        vo.setAreaName(StringUtils.trimToEmpty(rs.getString("AREA_NAME")));
        vo.setAgencyName(StringUtils.trimToEmpty(rs.getString("AGENCYNAME")));
        return vo;
    }
}

/**
 * 更新用內部類，後續有要添加自行修改。
 * 113/04/22
 * <AUTHOR>
 */
class LmsmBussMainUpdateRowMapper implements RowMapper<LmsmBussMain> {
	public LmsmBussMain mapRow(ResultSet rs, int index) throws SQLException {
		LmsmBussMain vo = new LmsmBussMain();
        vo.setAgencyCode(StringUtils.trimToEmpty(rs.getString("AGENCYCODE")));
        vo.setRegUnitCode(StringUtils.trimToEmpty(rs.getString("REG_UNIT_CODE")));
        vo.setRegUnitCode(StringUtils.trimToEmpty(rs.getString("CASE_NO")));
        vo.setBanNo(StringUtils.trimToEmpty(rs.getString("BAN_NO")));
        vo.setLmsName(StringUtils.trimToEmpty(rs.getString("BUSS_NAME")));
        vo.setStatus(StringUtils.trimToEmpty(rs.getString("CURR_STATUS")));        
        vo.setRepName(StringUtils.trimToEmpty(rs.getString("RES_NAME")));        
        vo.setManagerName(StringUtils.trimToEmpty(rs.getString("MANAGER_NAME")));
        vo.setLastChangeDate(StringUtils.trimToEmpty(rs.getString("LAST_CHG_DATE")));
        vo.setAreaCode(StringUtils.trimToEmpty(rs.getString("ADDRESS_CODE")));
        vo.setBusiAddr(StringUtils.trimToEmpty(rs.getString("BUSS_ADDRESS")));
        vo.setApproveDate(StringUtils.trimToEmpty(rs.getString("SET_APP_DATE")));
        vo.setApproveNo(StringUtils.trimToEmpty(rs.getString("SET_APP_NO")));
        vo.setLastChgNo(StringUtils.trimToEmpty(rs.getString("LAST_CHG_NO")));
        vo.setRegisterFunds(StringUtils.trimToEmpty(rs.getString("REGISTER_FUNDS")));
        vo.setRealFunds(StringUtils.trimToEmpty(rs.getString("REAL_FUNDS")));
        vo.setOrganType(StringUtils.trimToEmpty(rs.getString("ORG_CODE")));
        return vo;
	}
}