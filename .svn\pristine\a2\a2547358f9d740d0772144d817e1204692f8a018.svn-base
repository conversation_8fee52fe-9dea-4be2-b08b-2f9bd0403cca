package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutes;
import com.kangdainfo.tcfi.model.eicm.bo.DeclaratoryStatutesRcver;
import com.kangdainfo.util.report.JasperReportMaker;

/*
程式目的：解釋函查詢
程式代號：PRE4005
撰寫日期：103.05.22
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE4005 extends SuperBean {
	
	private String q_rcvNo ;
	private String q_rcvTime ;
	private String q_keynote ;
	private String q_instruction ;
	private String q_endRcvTime;
	
	private String rcvNo ;
	private String rcvTime ;
	private String keynote ;
	private String instruction ;
	private String receiveUnit;
	private String ccUnit;
	private String printWithUnit;
	
	// --------------------------------------------getter and setter of variables---------------------------------------
	
	public String getQ_endRcvTime() {return checkGet(q_endRcvTime);}
	public void setQ_endRcvTime(String s) {this.q_endRcvTime = checkSet(s);}
	public String getQ_rcvNo() {return checkGet(q_rcvNo);}
	public void setQ_rcvNo(String s) {q_rcvNo = checkSet(s);}
	public String getQ_rcvTime() {return checkGet(q_rcvTime);}
	public void setQ_rcvTime(String s) {q_rcvTime = checkSet(s);}
	public String getQ_keynote() {return checkGet(q_keynote);}
	public void setQ_keynote(String s) {q_keynote = checkSet(s);}
	public String getQ_instruction() {return checkGet(q_instruction);}
	public void setQ_instruction(String s) {q_instruction = checkSet(s);}
	public String getRcvNo() {return checkGet(rcvNo);}
	public void setRcvNo(String s) {rcvNo = checkSet(s);}
	public String getRcvTime() {return checkGet(rcvTime);}
	public void setRcvTime(String s) {rcvTime = checkSet(s);}
	public String getKeynote() {return checkGet(keynote);}
	public void setKeynote(String s) {keynote = checkSet(s);}
	public String getInstruction() {return checkGet(instruction);}
	public void setInstruction(String s) {instruction = checkSet(s);}
	public String getReceiveUnit() {return checkGet(receiveUnit);}
	public void setReceiveUnit(String s) {this.receiveUnit = checkSet(s);}
	public String getCcUnit() {return checkGet(ccUnit);}
	public void setCcUnit(String s) {this.ccUnit = checkSet(s);}
	public String getPrintWithUnit() {return checkGet(printWithUnit);}
	public void setPrintWithUnit(String s) {printWithUnit = checkSet(s);}
	
	// --------------------------------------------the 4 basic function-------------------------------------------------
	
	public void doCreate() throws Exception{}
	public void doUpdate() throws Exception{}
	public void doDelete() throws Exception{}
		  
	public Object doQueryOne() throws Exception{ 
		PRE4005 obj = this ;
		String pk = getRcvNo();
		pk = URLDecoder.decode(pk, "UTF-8");
		
		DeclaratoryStatutes bo = ServiceGetter.getInstance().getPrefixService().getDeclaratoryStatutes(pk);
		DeclaratoryStatutes bo2 = new DeclaratoryStatutes();
		bo2.setRcvNo(pk);
		List<DeclaratoryStatutesRcver> dsrList = ServiceGetter.getInstance().getPrefixService().queryDeclaratoryStatutesRcver(bo2);
		
		if ( bo == null ) {
			obj.setRcvNo("");
			obj.setRcvTime("");
			obj.setKeynote("");
			obj.setInstruction("");
			obj.setReceiveUnit("");
			obj.setCcUnit("");
			setErrorMsg( "查無資料" ) ;
		} // end if
		else {
			obj.setRcvNo(bo.getRcvNo());
			obj.setRcvTime(bo.getRcvTime());
			obj.setKeynote(bo.getKeynote());
			String instructions = "";
			String rcver1 = "";
			String rcver2 = "";
			if ( dsrList != null && dsrList.size() != 0 ) {
				for (int i = 0;i<dsrList.size();i++ ) {
					if ( "0".equals(Common.get(dsrList.get(i).getRcverType()))) {
						if ("".equals(instructions))
							instructions+=Common.get(dsrList.get(i).getRcverOrg());
						else
							instructions+="\n"+Common.get(dsrList.get(i).getRcverOrg());
					}
					else if ( "1".equals(Common.get(dsrList.get(i).getRcverType()))) {
						if ("".equals(rcver1))
							rcver1+=Common.get(dsrList.get(i).getRcverOrg());
						else
							rcver1+="、"+Common.get(dsrList.get(i).getRcverOrg());
					}
					else {
						if ("".equals(rcver2))
							rcver2+=Common.get(dsrList.get(i).getRcverOrg());
						else
							rcver2+="、"+Common.get(dsrList.get(i).getRcverOrg());
					}
				}
			}
			obj.setInstruction(instructions);
			obj.setReceiveUnit(rcver1);
			obj.setCcUnit(rcver2);
		} // else
		return obj;
	} // end doQueryOne() 	
	
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
		try {
			java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>() ;

			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" RCV_NO");
			sqljob.appendSQL(",RCV_TIME");
			sqljob.appendSQL(",KEYNOTE");
			sqljob.appendSQL("FROM DECLARATORY_STATUTES");
			sqljob.appendSQL("WHERE 1=1");
			if(!"".equals(Common.get(getQ_rcvNo()))){
				sqljob.appendSQLCondition("RCV_NO LIKE ?");
				sqljob.addLikeParameter(getQ_rcvNo());
			}
			if(!"".equals(Common.get(getQ_rcvTime()))){
				sqljob.appendSQLCondition("RCV_TIME >= ?");
				sqljob.addParameter(getQ_rcvTime());
			}
			if(!"".equals(Common.get(getQ_endRcvTime()))){
				sqljob.appendSQLCondition("RCV_TIME <= ?");
				sqljob.addParameter(getQ_endRcvTime());
			}
			if(!"".equals(Common.get(getQ_keynote()))){
				//主旨與內容2個地方都要查
				sqljob.appendSQL("AND (");
				sqljob.appendSQL("KEYNOTE LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("INSTRUCTION LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("RCV_NO IN (");
				sqljob.appendSQL("SELECT RCV_NO FROM DECLARATORY_STATUTES_RCVER WHERE RCVER_TYPE='0' AND RCVER_ORG LIKE ?");
				sqljob.addLikeParameter(getQ_keynote());
				sqljob.appendSQL(")");
				sqljob.appendSQL(")");
			}
			if(!"".equals(Common.get(getQ_instruction()))){
				//主旨與內容2個地方都要查
				sqljob.appendSQL("AND (");
				sqljob.appendSQL("KEYNOTE LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("INSTRUCTION LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL("OR");
				sqljob.appendSQL("RCV_NO IN (");
				sqljob.appendSQL("SELECT RCV_NO FROM DECLARATORY_STATUTES_RCVER WHERE RCVER_TYPE='0' AND RCVER_ORG LIKE ?");
				sqljob.addLikeParameter(getQ_instruction());
				sqljob.appendSQL(")");
				sqljob.appendSQL(")");
			}
			sqljob.appendSQL(" ORDER BY RCV_TIME DESC, RCV_NO ASC");
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[9];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[4];
					rowArray[0] = URLEncoder.encode(Common.get(o.get("RCV_NO")),"UTF-8");
					rowArray[1] = Common.get(o.get("RCV_NO"));
					rowArray[2] = Common.get(o.get("RCV_TIME"));
					rowArray[3] = Common.get(o.get("KEYNOTE")).length() > 60  ? Common.get(o.get("KEYNOTE")).substring(0,59) : Common.get(o.get("KEYNOTE"))  ;
					arrList.add(rowArray);	
				}
				return arrList;
			} else {
				throw new MoeaException( "查無資料(NO DATA FOUND)，請變更查詢條件" ) ;
			}
		} catch (MoeaException e) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null;
		}
	}
	
	public String DateFormat( String inputDate ) {
	  String tempDate = "" ;
	  String year = inputDate.substring(0, 3) ;
	  String month = inputDate.substring(4, 6) ;
	  String day = inputDate.substring(7) ;
	  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
      return tempDate ;
	} // DateFormat()
	  
	public String TimeFormat( String inputTime ) {
	  String tempTime = "" ;
	  String hour = inputTime.substring(0, 2) ;
	  String minute = inputTime.substring(2, 4) ;
	  String second = inputTime.substring(4) ;
	  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
	  return tempTime ;
	} // TimeFormat()
	
	public File doPrintPdf() throws Exception {
		File report = null ;  
		try {       
			boolean printWithUnit = false;
			if ( "true".equals(getPrintWithUnit()) )
			    printWithUnit = true;
	        String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre8006.jasper");
	        Map<String, Object> parameters = new HashMap<String,Object>();

	        parameters.put("printDate", Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印日期
			parameters.put("printTime", Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間                                                    
	        parameters.put("printWithUnit", printWithUnit);
			ArrayList<PRE4005> dataList = new ArrayList<PRE4005>();
			PRE4005 o = this;
			o.setRcvTime(Common.formatYYYMMDD(o.getRcvTime(),2));
			dataList.add(o);

			report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	        
	   	    return report ;
	    } // end try
	    catch( MoeaException e ) {
	    	e.printStackTrace();
	        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	        return null ;
		} // end catch	
    } // doPrintPdf
} // PRE4005