package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.icms.bo.CsmmCmpyInfo;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class PRE8001 extends SuperBean{

	private String q_banNo;
	
	private String id;
	private String banNo;
	private String companyName;
	private String companyStatus;
	private String partName;
	private String newPartName;
	
	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String q_banNo) {this.q_banNo = checkSet(q_banNo);}
	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String banNo) {this.banNo = checkSet(banNo);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String companyName) {this.companyName = checkSet(companyName);}
	public String getCompanyStatus() {return checkGet(companyStatus);}
	public void setCompanyStatus(String companyStatus) {this.companyStatus = checkSet(companyStatus);}
	public String getPartName() {return checkGet(partName);}
	public void setPartName(String partName) {this.partName = checkSet(partName);}
	public String getNewPartName() {return checkGet(newPartName);}
	public void setNewPartName(String newPartName) {this.newPartName = checkSet(newPartName);}

	@Override
	public Object doQueryOne() throws Exception {
		PRE8001 obj = this;
		Cedb2000 c = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(obj.getQ_banNo());
		if(c != null){
			obj.setId(c.getBanNo());
			obj.setBanNo(c.getBanNo());
			obj.setCompanyName(c.getCompanyName());
			obj.setCompanyStatus(Common.get(ServiceGetter.getInstance().getSystemCode10Loader().getCodeNameByCode(c.getStatusCode())));
			obj.setPartName(c.getPartName());
			obj.setNewPartName("");
		}else{
			obj.setId("");
			obj.setErrorMsg("查無此統一編號的公司資料");
		}		
		return obj;
	}

	@Override
	public void doUpdate() throws Exception {
		PRE8001 obj = this;
		List<CsmmCmpyInfo> objList = ServiceGetter.getInstance().getPrefixService().getCsmmCmpyInfoByBanNo(obj.getId());
		//檢核暫存檔(modifType = 4, 7, 8  代表有暫存檔)
		if(objList != null && objList.size() > 0){
			for(CsmmCmpyInfo info: objList){
				if("4".equals(info.getModifType())){
					throw new MoeaException("此統一編號有登記案件處理中");
				}else if("7".equals(info.getModifType())){
					throw new MoeaException("此統一編號有補登案件處理中");
				}else if("8".equals(info.getModifType())){
					throw new MoeaException("此統一編號有系統介接案件處理中");
				}
			}
		}
		//未檢核到異常，執行異動
		Cedb2000 info = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(obj.getId());
		if(null!=info) {
			//更新特取名稱
			ServiceGetter.getInstance().getPrefixService().updateCsmmCmpyInfoByPartName(obj.getId(), obj.getNewPartName());
			//寫入特取名稱更改記錄檔(PART_NAME_LOG)
			ServiceGetter.getInstance().getPrefixService().insertPartNameLog(obj.getId(), getLoginUserId(), info.getPartName(), obj.getNewPartName());
			//同步異動全文檢索索引
			ServiceGetter.getInstance().getCom0001Service().Fun_0010(PrefixConstants.JOB_WS10001,obj.getId(),null,null,Datetime.getYYYMMDDHHMISS(),getLoginUserId());
		}
	}

	@Override
	public void doDelete() throws Exception {}
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}

}