<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


	<!--=======================設定排程執行時間=================================================-->
	<!--  ==== 0 55 23 ? * *
	"cronExpression"屬性的指定，指定的格式是至少六個時間元素，最多七個時間元素，例如上面的指定是每天的19時要執行Job一次，
	"cronExpression"屬性指定的格式如下：
	秒（0-59） 
	分（0-59） 
	小時（0-23） 
	每月第幾天（1-31） 
	月（1-12或JAN-DEC） 
	每星期第幾天（1-7或SUN-SAT） 
	年（1970-2099） 
	
	其中「每月第幾天」與「每星期第幾天是互斥」的，兩個只能設定一個，不設定的以 ? 符號撰寫，如果有好幾個時間點，可以使用 , 
	符號，例如：「0 0 10,12,14 * * ?」表示每天的10時、12時、14時要執行Job；對於連續的時間可以使用 - 符號，
	例如「0 0 10,12,14 1-15 * ?」表示每月的1到15日每10時、12時、15時要執行Job，時間格式中的年指定可有可無，
	例如：「0 0 10,12,14 ? * MON 2006」表示2006年每星期一的10時、12時、14時要執行Job。
	-->	
	<!-- SimpleQuartzJob -->
	<bean id="simpleQuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.SimpleQuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="simpleSchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="simpleQuartzJob" />
		<property name="cronExpression">
			<value>0 0/5 * * * ?</value> <!-- 每隔5分鐘 執行 -->
		</property>
	</bean>
	<!-- SimpleQuartzJob -->

	<!-- Pre0001QuartzJob - 重建索引 -->
	<bean id="pre0001QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0001QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0001SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0001QuartzJob" />
		<property name="cronExpression">
			<value>0 0 1 * * ?</value> <!-- 每天凌晨1點 -->
		</property>
	</bean>
	<!-- Pre0001QuartzJob - 重建索引 -->

	<!-- Pre0002QuartzJob - 異動公司索引資料 -->
	<bean id="pre0002QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0002QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0002SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0002QuartzJob" />
		<property name="cronExpression">
			<value>1,5,9,13,17,21,25,29,33,37,41,45,49,53,57 0-59 7-22 * * ?</value> <!-- 每隔 4秒 執行 -->
		</property>
	</bean>

	<!-- Pre0003QuartzJob - 異動預查索引資料 -->
	<bean id="pre0003QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0003QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0003SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0003QuartzJob" />
		<property name="cronExpression">
			<value>0,3,4,7,8,11,12,15,16,19,20,23,24,27,28,31,32,35,36,39,40,43,44,47,48,51,52,55,56,59 0-59 7-22 * * ?</value> <!-- 每隔 4秒 執行 -->
		</property>
	</bean>
	<!-- Pre0003QuartzJob - 異動預查索引資料 -->

	<!-- Pre0004QuartzJob - 同音同義字異動同步異動索引 -->
	<bean id="pre0004QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0004QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0004SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0004QuartzJob" />
		<property name="cronExpression">
			<value>0 0/10 * * * ?</value> <!-- 每隔10分鐘 執行 -->
		</property>
	</bean>
	<!-- Pre0004QuartzJob - 同音同義字異動同步異動索引 -->

	<!-- Pre0005QuartzJob - 重新執行索引失敗排程 -->
	<bean id="pre0005QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0005QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0005SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0005QuartzJob" />
		<property name="cronExpression">
			<value>0 0/15 7-20 * * ?</value> <!-- 每隔15分鐘 執行 -->
		</property>
	</bean>
	<!-- Pre0005QuartzJob - 重新執行索引失敗排程 -->

	<!-- Pre0006QuartzJob - 備份IndexLog到IndexLogH -->
	<bean id="pre0006QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0006QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0006SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0006QuartzJob" />
		<property name="cronExpression">
			<value>0 0 2 * * ?</value> <!-- 每天凌晨2點 -->
		</property>
	</bean>
	<!-- Pre0006QuartzJob - 備份IndexLog到IndexLogH -->

	<!-- Pre0007QuartzJob - 重載代碼資料 -->
	<bean id="pre0007QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0007QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0007SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0007QuartzJob" />
		<property name="cronExpression">
			<value>0 0 3 * * ?</value> <!-- 每天凌晨3點 -->
		</property>
	</bean>
	<!-- Pre0007QuartzJob - 重載代碼資料 -->

	<!-- Pre0008QuartzJob - 壓縮索引備份檔 -->
	<bean id="pre0008QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0008QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0008SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0008QuartzJob" />
		<property name="cronExpression">
			<value>0 0 5 * * ?</value> <!-- 每天凌晨5點 -->
		</property>
	</bean>
	<!-- Pre0008QuartzJob - 壓縮索引備份檔 -->

	<!-- Pre0009QuartzJob - 更新執行失敗的排程,讓執行失敗的排程重做 -->
	<bean id="pre0009QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0009QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0009SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0009QuartzJob" />
		<property name="cronExpression">
			<value>0 5 7-20 * * ?</value> <!-- 每小時執行一次 -->
		</property>
	</bean>
	<!-- Pre0009QuartzJob - 更新執行失敗的排程,讓執行失敗的排程重做 -->

	<!-- Pre0011QuartzJob - 備份 SAMENAME_QUEUE 到 SAMENAME_QUEUE_H -->
	<bean id="pre0011QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0011QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0011SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0011QuartzJob" />
		<property name="cronExpression">
			<value>0 10 2 * * ?</value> <!-- 每天凌晨2點10分 -->
		</property>
	</bean>
	<!-- Pre0011QuartzJob - 備份 SAMENAME_QUEUE 到 SAMENAME_QUEUE_H -->

	<!-- Pre0012QuartzJob - 定時檢查重複辦理馬上辦延期一個月的案件, 將資料釐正 -->
	<bean id="pre0012QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0012QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0012SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0012QuartzJob" />
		<property name="cronExpression">
			<value>0 0/5 8-20 * * ?</value> <!-- 每隔5分鐘 執行 -->
		</property>
	</bean>
	<!-- Pre0012QuartzJob - 定時檢查重複辦理馬上辦延期一個月的案件, 將資料釐正 -->

	<!-- Pre0013QuartzJob - 異動有限合夥索引資料 -->
	<bean id="pre0013QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0013QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0013SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0013QuartzJob" />
		<property name="cronExpression">
			<value>0/5 * 7-22 * * ?</value> <!-- 每隔 5秒 執行 -->
		</property>
	</bean>
	<!-- Pre0013QuartzJob - 異動有限合夥索引資料 -->

	<!-- Pre0014QuartzJob - 一站式案件同步失敗資料補正 -->
	<bean id="pre0014QuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.Pre0014QuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="pre0014SchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="pre0014QuartzJob" />
		<property name="cronExpression">
			<value>0 2 7-20 * * ?</value> <!-- 每小時執行一次 -->
		</property>
	</bean>
	<!-- Pre0014QuartzJob - 一站式案件同步失敗資料補正 -->

	<!-- CmpySameNameQuartzJob - 同名比對 -->
	<bean id="cmpySameNameQuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.CmpySameNameQuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="cmpySameNameSchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="cmpySameNameQuartzJob" />
		<property name="cronExpression">
			<value>2/10 * * * * ?</value> <!-- 每隔10秒 執行 -->
		</property>
	</bean>
	<!-- CmpySameNameQuartzJob - 同名比對 -->
	
	<!-- UpdateOsssQuartzJob - 一站式同步 -->
	<bean id="updateOsssQuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.UpdateOsssQuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="updateOsssSchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="updateOsssQuartzJob" />
		<property name="cronExpression">
			<value>1/5 * * * * ?</value> <!-- 每隔5秒 執行 -->
		</property>
	</bean>
	<!-- UpdateOsssQuartzJob - 一站式同步 -->

	<!-- CompressLogFileQuartzJob - 壓縮系統紀錄檔 -->
	<bean id="compressLogFileQuartzJob"
		class="org.springframework.scheduling.quartz.JobDetailBean">
		<property name="jobClass" value="com.kangdainfo.tcfi.scheduling.CompressLogFileQuartzJobBean" />
		<property name="jobDataAsMap">
			<map>
				<entry key="enable" value="${quartz.enable}" />
			</map>
		</property>
	</bean>
  	<bean id="compressLogFileSchedTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean">
		<property name="jobDetail" ref="compressLogFileQuartzJob" />
		<property name="cronExpression">
			<value>0 10 0 * * ?</value> <!-- 每天凌晨0點10分 -->
		</property>
	</bean>
	<!-- CompressLogFileQuartzJob - 壓縮系統紀錄檔 -->

	<!--================== 啟動排程設定 scheduleRefreshScheduler ==================-->
	<bean id="scheduleRefreshScheduler" class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="triggers">
			<list>
				<!-- <ref bean="simpleSchedTrigger"/> -->
				<ref bean="pre0001SchedTrigger"/>
				<ref bean="pre0002SchedTrigger"/>
				<ref bean="pre0003SchedTrigger"/>
				<ref bean="pre0004SchedTrigger"/>
				<ref bean="pre0005SchedTrigger"/>
				<ref bean="pre0006SchedTrigger"/>
				<ref bean="pre0007SchedTrigger"/>
				<ref bean="pre0008SchedTrigger"/>
				<ref bean="pre0009SchedTrigger"/>
				<ref bean="pre0011SchedTrigger"/>
				<ref bean="pre0012SchedTrigger"/>
				<ref bean="pre0013SchedTrigger"/>
				<ref bean="pre0014SchedTrigger"/>
				<ref bean="cmpySameNameSchedTrigger"/>
				<ref bean="updateOsssSchedTrigger"/>
				<ref bean="compressLogFileSchedTrigger"/>
			</list>
		</property>
	</bean>

</beans>