<!--
程式目的：審核-預查審核-預查異動內容
程式代號：PRE3001_03
撰寫日期：103.05.26
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*"%>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3001" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001_04">
	<jsp:setProperty name='obj' property='*' />
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("init".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功!");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
function queryOne(v) {
}
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='審核-預查審核-預查異動內容'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg">
	<div id="listContainer">
    <table class="table_form" width="100%" cellspacing="0" cellpadding="0">
      	<thead id="listTHEAD">
  		<tr>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">統一編號</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">公司名稱</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">預查種類</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">公司狀況</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">核覆日期</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">保留期限</a></th>
    		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">申請人</a></th>
  		</tr>
  		</thead>
  		<tbody id="listTBODY">
  			<%
  			boolean primaryArray[] = { true,false,false,false,false,false,false,false,false};
  			boolean displayArray[] = { true, true, true, true, true, true, true, true,false};
  			String[] alignArray = {"left","left","left","left","left","left","left","left","left"};
  			out.write(obj.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
  			%>
  		</tbody>
	</table>
  	</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

<tr><td>
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="right">
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開"
				onclick="javascript:window.close();" />&nbsp;
		</td>
	</tr>
</table>
</td></tr>

</table>
</form>
</body>
</html>