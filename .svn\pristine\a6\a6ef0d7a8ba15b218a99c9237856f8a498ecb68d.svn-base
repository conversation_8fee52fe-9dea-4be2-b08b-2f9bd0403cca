package com.kangdainfo.tcfi.loader;

/**
 * 有限合夥各類代碼對照加載類--有限合夥狀態
 * <AUTHOR>
 * 113/04/17
 */
public class LmsdCodemappingStatLoader extends LmsdCodemappingLoader {
	private static final String CACHE_NAME = "CACHE_NAME_LMSD_CODEMAPPING_STAT";
	private static final String KIND = "STAT";// 有限合夥狀態
	
	private static LmsdCodemappingStatLoader Instance;
	
	public LmsdCodemappingStatLoader() {
		if(LmsdCodemappingStatLoader.Instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist: "
					+ LmsdCodemappingStatLoader.Instance);
		}
		LmsdCodemappingStatLoader.Instance = this;
	}

	@Override
	protected String getCacheName() {
		return CACHE_NAME;
	}

	@Override
	protected String getKind() {
		return KIND;
	}

}
