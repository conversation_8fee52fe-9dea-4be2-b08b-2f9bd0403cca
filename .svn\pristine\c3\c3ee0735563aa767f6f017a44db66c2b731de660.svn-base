package com.kangdainfo.tcfi.model.eicm.dao;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.util.lang.CommonStringUtils;

public class SystemCodeDao extends BaseDaoJdbc implements RowMapper<SystemCode> {

	private static final String SQL_findById = "SELECT * FROM SYSTEM_CODE WHERE ID = ? ";
	public SystemCode findById(Integer id) {
		//check pk
		if(id == null) return null;
		SQLJob sqljob = new SQLJob(SQL_findById);
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<SystemCode> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}
	
	private static final String SQL_findByPk = "SELECT * FROM SYSTEM_CODE WHERE CODE_KIND = ? AND CODE = ?";
	public SystemCode findByPk(String codeKind, String code) {
		//check pk
		if(CommonStringUtils.isEmpty(codeKind)) return null;
		if(CommonStringUtils.isEmpty(code)) return null;
		SQLJob sqljob = new SQLJob(SQL_findByPk);
		sqljob.addParameter(codeKind);
		sqljob.addParameter(code);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<SystemCode> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()? null : list.get(0);
	}

	private static final String SQL_findByCodeKind = "SELECT * FROM SYSTEM_CODE WHERE CODE_KIND = ? AND ENABLE = 'Y' ORDER BY SORTED, CODE_KIND, CODE";
	public List<SystemCode> findByCodeKind(String codeKind) {
		SQLJob sqljob = new SQLJob(SQL_findByCodeKind);
		sqljob.addParameter(codeKind);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<SystemCode>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	public Integer countByCondition(String codeKind, String codeName) {
		SQLJob sqljob = new SQLJob("SELECT COUNT(1) AS C FROM SYSTEM_CODE WHERE CODE_KIND NOT IN ('00','01') ");
		
		if(CommonStringUtils.isNotBlank(codeKind)) {
			sqljob.appendSQLCondition("CODE_KIND = ?");
			sqljob.addParameter(codeKind);
		}
		if(CommonStringUtils.isNotBlank(codeName)) {
			sqljob.appendSQLCondition("CODE_NAME LIKE ?");
			sqljob.addLikeParameter(codeName);
		}

		if(logger.isDebugEnabled()) logger.debug(sqljob);
		Map<String,Object> map = getJdbcTemplate().queryForMap(sqljob.getSQL(), sqljob.getParametersArray());
		if(null!=map)
			return ((BigDecimal)map.get("C")).intValue();
		return 0;
	}
	
	public List<SystemCode> findByCondition(String codeKind, String codeName){
		SQLJob sqljob = new SQLJob("SELECT * FROM SYSTEM_CODE WHERE CODE_KIND NOT IN ('00','01') ");
		
		if(CommonStringUtils.isNotBlank(codeKind)) {
			sqljob.appendSQLCondition("CODE_KIND = ?");
			sqljob.addParameter(codeKind);
		}
		if(CommonStringUtils.isNotBlank(codeName)) {
			sqljob.appendSQLCondition("CODE_NAME LIKE ?");
			sqljob.addLikeParameter(codeName);
		}
		//以異動日期排序
		sqljob.appendSQL(" ORDER BY MOD_DATE DESC, MOD_TIME DESC ");
		
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<SystemCode>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public SystemCode insert(SystemCode bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getCodeKind())) return null;
		if(CommonStringUtils.isEmpty(bo.getCode())) return null;
		//check exist
		SystemCode t = findByPk(bo.getCodeKind(), bo.getCode());
		if(null!=t) return t;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO SYSTEM_CODE (");
		sqljob.appendSQL(" CODE_KIND");
		sqljob.appendSQL(",CODE");
		sqljob.appendSQL(",CODE_NAME");
		sqljob.appendSQL(",CODE_DESC");
		sqljob.appendSQL(",CODE_PARAM1");
		sqljob.appendSQL(",CODE_PARAM2");
		sqljob.appendSQL(",CODE_PARAM3");
		sqljob.appendSQL(",REMARK");
		sqljob.appendSQL(",SORTED");
		sqljob.appendSQL(",ENABLE");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getCodeKind());
		sqljob.addParameter(bo.getCode());
		sqljob.addParameter(bo.getCodeName());
		sqljob.addParameter(bo.getCodeDesc());
		sqljob.addParameter(bo.getCodeParam1());
		sqljob.addParameter(bo.getCodeParam2());
		sqljob.addParameter(bo.getCodeParam3());
		sqljob.addParameter(bo.getRemark());
		sqljob.addParameter(bo.getSorted());
		sqljob.addParameter(bo.getEnable());
		sqljob.addParameter(bo.getModIdNo());
		sqljob.addParameter(bo.getModDate());
		sqljob.addParameter(bo.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
			,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.NUMERIC,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
			});
		return findByPk(bo.getCodeKind(), bo.getCode());
	}
	
	public SystemCode update(SystemCode bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getCodeKind())) return null;
		if(CommonStringUtils.isEmpty(bo.getCode())) return null;
		//check exist
		SystemCode t = findByPk(bo.getCodeKind(), bo.getCode());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE SYSTEM_CODE SET");
			sqljob.appendSQL("CODE_NAME = ?");
			sqljob.appendSQL(",CODE_DESC = ?");
			sqljob.appendSQL(",CODE_PARAM1 = ?");
			sqljob.appendSQL(",CODE_PARAM2 = ?");
			sqljob.appendSQL(",CODE_PARAM3 = ?");
			sqljob.appendSQL(",REMARK = ?");
			sqljob.appendSQL(",SORTED = ?");
			sqljob.appendSQL(",ENABLE = ?");
			sqljob.appendSQL(",MOD_ID_NO = ?");
			sqljob.appendSQL(",MOD_DATE = ?");
			sqljob.appendSQL(",MOD_TIME = ?");
			sqljob.appendSQL("WHERE CODE_KIND = ?");
			sqljob.appendSQL("AND CODE = ?");
			sqljob.addParameter(Common.get(bo.getCodeName()));
			sqljob.addParameter(Common.get(bo.getCodeDesc()));
			sqljob.addParameter(Common.get(bo.getCodeParam1()));
			sqljob.addParameter(Common.get(bo.getCodeParam2()));
			sqljob.addParameter(Common.get(bo.getCodeParam3()));
			sqljob.addParameter(Common.get(bo.getRemark()));
			sqljob.addParameter(bo.getSorted());
			sqljob.addParameter(Common.get(bo.getEnable()));
			sqljob.addParameter(Common.get(bo.getModIdNo()));
			sqljob.addParameter(Common.get(bo.getModDate()));
			sqljob.addParameter(Common.get(bo.getModTime()));
			sqljob.addParameter(bo.getCodeKind());
			sqljob.addParameter(bo.getCode());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.NUMERIC,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
				});
			return findByPk(bo.getCodeKind(), bo.getCode());
		}
	}
	
	public void delete(SystemCode bo) {
		//check pk
		if(null!=bo
				&& CommonStringUtils.isNotEmpty(bo.getCodeKind())
				&& CommonStringUtils.isNotEmpty(bo.getCode()) ) {
			//delete - 只設定停用, 不刪除
			SQLJob sqljob = new SQLJob("UPDATE SYSTEM_CODE SET");
			sqljob.appendSQL("ENABLE = 'N'");
			sqljob.appendSQL(",MOD_ID_NO = ?");
			sqljob.appendSQL(",MOD_DATE = ?");
			sqljob.appendSQL(",MOD_TIME = ?");
			sqljob.appendSQL("WHERE CODE_KIND = ?");
			sqljob.appendSQL("AND CODE = ?");
			sqljob.addParameter(bo.getModIdNo());
			sqljob.addParameter(bo.getModDate());
			sqljob.addParameter(bo.getModTime());
			sqljob.addParameter(bo.getCodeKind());
			sqljob.addParameter(bo.getCode());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
				,new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR
				});
		}
	}

	public SystemCode mapRow(ResultSet rs, int idx) throws SQLException {
		SystemCode obj = null;
		if(null!=rs) {
			obj = new SystemCode();
			obj.setId(rs.getInt("ID"));
			obj.setCodeKind(rs.getString("CODE_KIND"));
			obj.setCode(rs.getString("CODE"));
			obj.setCodeName(rs.getString("CODE_NAME"));
			obj.setCodeDesc(rs.getString("CODE_DESC"));
			obj.setCodeParam1(rs.getString("CODE_PARAM1"));
			obj.setCodeParam2(rs.getString("CODE_PARAM2"));
			obj.setCodeParam3(rs.getString("CODE_PARAM3"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setSorted(rs.getInt("SORTED"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
		}
		return obj;
	}

}