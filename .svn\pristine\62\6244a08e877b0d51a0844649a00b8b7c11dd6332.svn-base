package com.kangdainfo.tcfi.loader;

/**
 * 預查公司狀態
 *
 */
public class SystemCode11Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_11";
	private static final String CODE_KIND = "11";//11:預查公司狀態

	//singleton
	private static SystemCode11Loader instance;
	public SystemCode11Loader() {
		if (SystemCode11Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode11Loader.instance);
		}
		SystemCode11Loader.instance = this;
	}
	public static SystemCode11Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}

}