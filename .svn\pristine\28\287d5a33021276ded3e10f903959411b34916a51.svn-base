<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.ajax.PopSystemCode">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>
<%
objList = obj.queryAll();
obj = (com.kangdainfo.tcfi.view.ajax.PopSystemCode) obj.queryOne();
if(null==obj) {
	obj = new com.kangdainfo.tcfi.view.ajax.PopSystemCode();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(code){
	var codeName = '';
	if( ''!=code )
	{
		var q = '<%=ESAPI.encoder().encodeForHTML(obj.getCodeKind())%>&code='+code;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonSystemCode.jsp', q);
		if (x!=null && x.length>0)
		{
			var json = eval('(' + x + ')');
			if(null!=json && null!=json.CODE_NAME)
				codeName = json.CODE_NAME;
		}
	}
	if (window.opener != undefined) { //forchrome 
		window.opener.popSystemCodeReturnNameChrome("{'CODE':'"+code+"','CODE_NAME':'"+codeName+"'}");
	} 
	else {  
		window.returnValue = "{'CODE':'"+code+"','CODE_NAME':'"+codeName+"'}";
	}
	window.close();
}
function closeWindow(){
	window.returnValue = "";
	window.close();
}
</script>
</head>
<title>請選擇<%=ESAPI.encoder().encodeForHTML(obj.getCodeKindDesc()) %></title>
<body>
<form id="form1" name="form1" method="post" autocomplete="off">
<table width="100%" cellspacing="0" cellpadding="0">
<!-- TITLE AREA -->
<tr><td class="bgList">
	<%=obj.getCodeKindDesc() %>
	<input class="toolbar_default" type="button" name="close" value="關閉(Close)" onClick="closeWindow()">
</td></tr>
<tr><td class="bgList" align="right">
	選取時請以滑鼠左鍵點選一下
</td></tr>
<!-- TITLE AREA -->
<!-- QUERY ALL LIST AREA -->
<tr><td class="bgList">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" onclick="return sortTable('listTBODY',0,false);" href="#">代碼</a></th>
		<th class="listTH"><a class="text_link_w" onclick="return sortTable('listTBODY',1,false);" href="#">名稱</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true,false};
	boolean displayArray[] = {true,true};
	String[] arrAlign = {"center","left"};
	out.write(View.getQuerylist(primaryArray,displayArray,arrAlign,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- QUERY ALL LIST AREA -->
</table>
</form>
</body>
</html>