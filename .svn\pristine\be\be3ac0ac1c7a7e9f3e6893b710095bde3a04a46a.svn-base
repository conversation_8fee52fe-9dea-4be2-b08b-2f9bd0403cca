package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司法解釋函令(DECLARATORY_STATUTES)
 *
 */
public class DeclaratoryStatutes extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 文號 PK */
	private String id;
	/** 文號 */
	private String rcvNo;
	/** 收文日期 */
	private String rcvTime;
	/** 主旨 */
	private String keynote;
	/** 說明 */
	private String instruction;
	/** 營業項目 */
	private String busiItems;
	/** 是否公示 */
	private String isPublish;
	/** 輸入日期 */
	private String insertDate;
	/** 輸入時間 */
	private String insertTime;
	/** 輸入人員 */
	private String insertUser;
	/** 更新日期 */
	private String updateDate;
	/** 更新時間 */
	private String updateTime;
	/** 更新人員 */
	private String updateUser;
	/** 法規代碼 */
	private String ruleCode;
	/** 法規條號 */
	private String ruleNo;
	/** 正本收文 */
	private String receiveUnit;
	/** 副本收文 */
	private String ccUnit;

	public String getId() {return id;}
	public void setId(String q) {this.id = q;}
	public String getRcvNo() {return rcvNo;}
	public void setRcvNo(String rcvNo) {this.rcvNo = rcvNo;}
	public String getRcvTime() {return rcvTime;}
	public void setRcvTime(String rcvTime) {this.rcvTime = rcvTime;}
	public String getKeynote() {return keynote;}
	public void setKeynote(String keynote) {this.keynote = keynote;}
	public String getInstruction() {return instruction;}
	public void setInstruction(String instruction) {this.instruction = instruction;}
	public String getBusiItems() {return busiItems;}
	public void setBusiItems(String busiItems) {this.busiItems = busiItems;}
	public String getIsPublish() {return isPublish;}
	public void setIsPublish(String isPublish) {this.isPublish = isPublish;}
	public String getInsertDate() {return insertDate;}
	public void setInsertDate(String insertDate) {this.insertDate = insertDate;}
	public String getInsertTime() {return insertTime;}
	public void setInsertTime(String insertTime) {this.insertTime = insertTime;}
	public String getInsertUser() {return insertUser;}
	public void setInsertUser(String insertUser) {this.insertUser = insertUser;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String updateUser) {this.updateUser = updateUser;}
	public String getRuleCode() {return ruleCode;}
	public void setRuleCode(String ruleCode) {this.ruleCode = ruleCode;}
	public String getRuleNo() {return ruleNo;}
	public void setRuleNo(String ruleNo) {this.ruleNo = ruleNo;}
	public String getReceiveUnit() {return receiveUnit;}
	public void setReceiveUnit(String receiveUnit) {this.receiveUnit = receiveUnit;}
	public String getCcUnit() {return ccUnit;}
	public void setCcUnit(String ccUnit) {this.ccUnit = ccUnit;}
	
}