package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;

public class PRE9002 extends SuperBean{

	private String id;
	private String codeName;
	private String optype;
	private String[] auth;
	private String[] authed;

	public String getId() {return checkGet(id);}
	public void setId(String s) { id = checkSet(s);}	
	public String getCodeName() {return checkGet(codeName);}
	public void setCodeName(String codeName) {this.codeName = checkSet(codeName);}
	public String getOptype() {return checkGet(optype);}
	public void setOptype(String s) {optype = checkSet(s);}
	public void setAuth(String[] s) {auth = s;}
	public String[] getAuth() {return auth;}
	public void setAuthed(String[] s) {authed = s;}
	public String[] getAuthed() {return authed;}	
	
	@Override
	public Object doQueryOne() throws Exception {
		PRE9002 obj = this;
		SystemCode c = ServiceGetter.getInstance().getSystemCode02Loader().getSystemCodeByCode(getId());
		if(c != null){
			obj.setId(c.getCode());
			obj.setCodeName(c.getCodeName());
		}else{
			this.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}

	@Override
	public ArrayList<String[]> doQueryAll() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		java.util.List<SystemCode> objList = ServiceGetter.getInstance().getSystemCode02Loader().getCodes();
		if(objList != null && objList.size() > 0){
			java.util.Iterator<SystemCode> it = objList.iterator();
			SystemCode o;
			String[] rowArray = new String[2];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[2];
				rowArray[0] = Common.get(o.getCode());
				rowArray[1] = Common.get(o.getCodeName());
				arrList.add(rowArray);
			}
		}else{
			this.setErrorMsg("查無資料！");
		}
		return arrList;
	}

	@Override
	public void doUpdate() throws Exception {
		if ("add".equals(getOptype())) {			
			if(auth != null || auth.length > 1)
				ServiceGetter.getInstance().getPrefixService().confirmFunctionMenuAuth(getOptype(), getId(), auth, getLoginUserId());
			else 
				throw new MoeaException("你必須先選擇左邊功能選單某一節點或多重節點");
		} else if("remove".equals(getOptype())) {
			if(authed != null || authed.length > 1)
				ServiceGetter.getInstance().getPrefixService().confirmFunctionMenuAuth(getOptype(), getId(), authed, getLoginUserId());
			else 
				throw new MoeaException("你必須先選擇右邊功能選單某一節點或多重節點");		
		}
	}
	
	public String buildCheckBoxTree(String treeID, String treeName, String checkboxName, String checkboxPrefix, String jsFunctionName, 
			String sysId, String groupID, boolean bIncludeAll, boolean bUrl, boolean bRootCheckBox) throws Exception {
		
		StringBuilder sb = new StringBuilder(1024).append("");
		if (Common.get(treeName).equals("")) treeName = "功能選單";
		
		//建立根節點
		sb.append(treeID).append(".add(");
		sb.append(sysId).append(",-1,'");
		if (bRootCheckBox) {
			sb.append("<input type=checkbox id=").append(checkboxPrefix).append(sysId).append(" name=").append(checkboxName).append(" class=checkbox onclick=");
			sb.append(jsFunctionName).append("(this,\"").append(treeName).append("\") value=").append(sysId).append(">");			
		}
		sb.append(treeName).append("'");
		if (bUrl) sb.append(",'dTreeForm.jsp?sid=").append(sysId).append("&fid=-1'");
		sb.append(");\n");
		
		java.util.List<FunctionMenu> fMenu = null;
		if (bIncludeAll) {
			//所有權限
			//fMenu = ServiceGetter.getInstance().getPrefixService().getFunctionMenuAll();
			//所有權限(排除已有的權限)
			fMenu = ServiceGetter.getInstance().getPrefixService().getFunctionMenuByNotExistsAuth(getId());
		}else{
			//已有權限
			fMenu = ServiceGetter.getInstance().getPrefixService().getFunctionMenuByGroupId(getId());
		}
		
		if(fMenu != null && fMenu.size() > 0){
			for(FunctionMenu dtl: fMenu){
				sb.append(treeID).append(".add(");
				sb.append(dtl.getId()).append(",");
				
				if (dtl.getPid()!=0) sb.append(dtl.getPid()).append(",");
				else sb.append(sysId).append(",");
				
				sb.append("'<input type=checkbox id=").append(checkboxPrefix).append(dtl.getId()).append(" name=").append(checkboxName).append(" class=checkbox onclick=").append(jsFunctionName).append("(this,\"").append(Common.escapeJavaScript(dtl.getTitle())).append("\") value=").append(dtl.getId()).append(">");
				//if (bIncludeAll) 
					sb.append(dtl.getTitle());				
				//else 
					//sb.append("<span class=").append("auth2").append(">").append(Common.escapeJavaScript(dtl.getTitle())).append("</span>");
				sb.append("',");
				
				if (bUrl) sb.append("'").append("dTreeForm.jsp?sid=").append(dtl.getId()).append("&fid=").append(dtl.getPid()).append("'");
				else sb.append("''");
				sb.append(");\n");
			}
		}
		return sb.toString();
	}
	
	@Override
	public void doCreate() throws Exception {}
	
	@Override
	public void doDelete() throws Exception {}
}
