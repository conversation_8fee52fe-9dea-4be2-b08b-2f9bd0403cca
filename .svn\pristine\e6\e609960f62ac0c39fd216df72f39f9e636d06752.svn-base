package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;

/**
 * 排程(PRE0006)
 * 備份IndexLog到IndexLogH
 */
public class Pre0006QuartzJobBean extends BaseQuartzJobBean {

	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		ServiceGetter.getInstance().getIndexUpdateService().doBackupIndexLog();
	}

}