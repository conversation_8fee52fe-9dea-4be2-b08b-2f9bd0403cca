package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 同名公司名稱預查資料檔(CEDB1004)
 *
 */
public class Cedb1004 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 序號 */
	private String seqNo;
	/** 公司名稱 */
	private String companyName;
	/** 同名的序號 */
	private String sameSeqNo;
	/** 同名的預查編號 */
	private String samePrefixNo;
	/** 同名的公司統編 */
	private String sameBanNo;
	/** 同名的公司名稱 */
	private String sameCompanyName;
	/** 解/撤/廢日 **/
	private String revokeAppDate;
	/** 公司狀態 **/
	private String cmpyStatus;
	/** 申請人 **/
	private String applyName;
	/** 保留期限 **/
	private String reserveDate;
	
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getSeqNo() {return seqNo;}
	public void setSeqNo(String seqNo) {this.seqNo = seqNo;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getSameSeqNo() {return sameSeqNo;}
	public void setSameSeqNo(String sameSeqNo) {this.sameSeqNo = sameSeqNo;}
	public String getSamePrefixNo() {return samePrefixNo;}
	public void setSamePrefixNo(String samePrefixNo) {this.samePrefixNo = samePrefixNo;}
	public String getSameBanNo() {return sameBanNo;}
	public void setSameBanNo(String sameBanNo) {this.sameBanNo = sameBanNo;}
	public String getSameCompanyName() {return sameCompanyName;}
	public void setSameCompanyName(String sameCompanyName) {this.sameCompanyName = sameCompanyName;}
	public String getRevokeAppDate() { return revokeAppDate; }
	public void setRevokeAppDate(String revokeAppDate) {this.revokeAppDate = revokeAppDate;}
	public String getCmpyStatus() {return cmpyStatus;}
	public void setCmpyStatus(String cmpyStatus) {this.cmpyStatus = cmpyStatus;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String applyName) {this.applyName = applyName;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String reserveDate) {this.reserveDate = reserveDate;}

}