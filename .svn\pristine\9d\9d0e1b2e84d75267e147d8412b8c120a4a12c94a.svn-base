package com.kangdainfo.moea.bo;

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: </p>
 * <AUTHOR> attributable
 * @version 1.0
 */
public class SearchSpecialData {

	private String prefixNo;
	private String banNo;
	private String companyName;
	private String applyName;
	private java.util.Collection<?> cedb1002s;
	private String reserveDate;
	private String approveResult;

	public SearchSpecialData() {
	}

	public String getPrefixNo() {
		return prefixNo;
	}

	public void setPrefixNo(String prefixNo) {
		this.prefixNo = prefixNo;
	}

	public String getBanNo() {
		return banNo;
	}

	public void setBanNo(String banNo) {
		this.banNo = banNo;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}

	public java.util.Collection<?> getCedb1002s() {
		return cedb1002s;
	}

	public void setCedb1002s(java.util.Collection<?> cedb1002s) {
		this.cedb1002s = cedb1002s;
	}

	public String getReserveDate() {
		return reserveDate;
	}

	public void setReserveDate(String reserveDate) {
		this.reserveDate = reserveDate;
	}

	public String getApproveResult() {
		return approveResult;
	}

	public void setApproveResult(String approveResult) {
		this.approveResult = approveResult;
	}
}
