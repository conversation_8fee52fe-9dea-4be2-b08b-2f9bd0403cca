package com.kangdainfo.moea.bo;

import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;

/**
 * <p>Title: 試辦系統</p>
 * <p>Description: 公司名稱預查審核作業的案件資料, 包含1000, 2000</p>
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: III</p>
 * <AUTHOR>
 * @version 1.0
 */
public class SearchAllData{
	private Cedb1000 cedb1000;
	private Cedb2000 cedb2000;
	//Added by BryanLin 20080424
	private SearchSpecialData ssd;

	public SearchAllData() {
	}

	public Cedb1000 getCedb1000() {
		return cedb1000;
	}

	public Cedb2000 getCedb2000() {
		return cedb2000;
	}

	public void setCedb1000(Cedb1000 cedb1000) {
		this.cedb1000 = cedb1000;
	}

	public void setCedb2000(Cedb2000 cedb2000) {
		this.cedb2000 = cedb2000;
	}

	public void setCedb(Object cedb) {
		if (cedb instanceof Cedb1000) {
			this.setCedb1000((Cedb1000) cedb);
		} else if (cedb instanceof Cedb2000) {
			this.setCedb2000((Cedb2000) cedb);
		}
	}

	public SearchSpecialData getSsd() {
		return ssd;
	}

	public void setSsd(SearchSpecialData ssd) {
		this.ssd = ssd;
	}
}
