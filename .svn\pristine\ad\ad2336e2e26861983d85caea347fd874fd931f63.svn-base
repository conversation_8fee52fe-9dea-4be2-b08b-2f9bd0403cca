package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;


public class PRE8008 extends SuperBean {
	
	private String yearNo;
	private String addYearNo ;
	private String prefixNo ;
	
	private String q_yearNo ;
	private String q_prefixNo ;
	
  // ------------------getters and setters of local variables bellow--------------------------
	
    public String getYearNo() {
    	return checkGet(yearNo);
    } //  getYearNo()
	public void setYearNo(String s) {
		yearNo = checkSet(s);
	} // setYearNo()
	public String getAddyearNo() {
		return checkGet(addYearNo);
	} // getAddyearNo
	public void setAddyearNo(String s) {
		addYearNo = checkSet(s) ;
	} // getAddyearNo
	public String getPrefixNo() {
    	return checkGet(prefixNo);
    } //  getPrefixNo()
	public void setPrefixNo(String s) {
		prefixNo = checkSet(s);
	} // setrPrefixNo()
	
	public String getQ_yearNo() {
		return checkGet(q_yearNo);
	} // getQ_yearNo()
	public void setQ_yearNo(String s) {
		q_yearNo = checkSet(s) ;
	} // setQ_yearNo()
	public String getQ_prefixNo() {
		return checkGet(q_prefixNo) ;
	} // getQ_prefixNo()
	public void setQ_prefixNo( String s ) {
		q_prefixNo = checkSet(s) ;
	} // setQ_prefixNo()
	
	// --------------------------------------------------------------------------------------
	
	public Object doQueryOne() throws Exception{ 
		PRE8008 obj = this ;
		Cedb1019 s = new Cedb1019(); 
	    
		s.setYearNo(getYearNo()) ;
	    
	    java.util.List<Cedb1019> objList = ServiceGetter.getInstance().getPrefixService().queryCedb1019(s);
	    
	    if (objList != null && objList.size() > 0){
			s = objList.get(0);
			obj.setYearNo(s.getYearNo());
			obj.setPrefixNo(s.getPrefixNo());
		} // end if
	    
		return obj;
	} // end doQueryOne()
	  
	public void doUpdate() throws Exception{
    	if("".equals(Common.get(getYearNo()))) 
		    throw new Exception("資料有誤，請重新輸入!!");
    	if ( getPrefixNo().length() > 9 ) 
    		throw new Exception("預查編號長度最多9碼") ;

        
		Cedb1019 obj = new Cedb1019() ;
		obj.setPrefixNo(getPrefixNo());
		obj.setYearNo(getYearNo());
	    
		ServiceGetter.getInstance().getPrefixService().saveCedb1019(obj);	 
    } // end doUpdate()	
	
	
	public ArrayList<String[]> doQueryAll() throws Exception{
		Cedb1019 obj = new Cedb1019() ;
	    if(!"".equals(getQ_yearNo()))
			obj.setYearNo(getQ_yearNo());
	    if(!"".equals(getQ_prefixNo()))
		 	obj.setPrefixNo(getQ_prefixNo());	
	    
		java.util.ArrayList<String[]> arrayList = new java.util.ArrayList<String[]>() ;
		java.util.List<Cedb1019> tempList = ServiceGetter.getInstance().getPrefixService().queryCedb1019(obj);
		
		this.processCurrentPageAttribute(tempList.size());
		 
		if (tempList != null && tempList.size() > 0){
		   for(Cedb1019 dtl : tempList){
			 String[] rowArray = new String[4];
			 rowArray[0] = dtl.getYearNo();
			 rowArray[1] = dtl.getPrefixNo();	 
			 arrayList.add(rowArray);
			  
		   } // for
		   tempList.clear();
		} // if
		
		return arrayList;
	} // doQueryAll()
		
	public void doCreate() throws Exception{
		
	} // end doCreate() 
	  
    public void doDelete() throws Exception{			
		   
    } // end doDelete()
	
} // PRE8008