--SYST<PERSON>_CODE
insert into SY<PERSON><PERSON>_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','OSSS_WSDL','http://211.72.223.241/oss/ossws/UpdateCaseStatusService?wsdl','一站式同步路徑',null,null,null,6,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,REMARK,SORTED,ENABLE) values ('01','PRE8016_1','收發文預查編號差異','預查儀表板(PRE8016)',null,null,null,null,0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,<PERSON>EMAR<PERSON>,SORTED,ENABLE) values ('01','PRE8016_2','已收文未確認數','預查儀表板(PRE8016)','50','50~150','150','綠色小於@CODEPARAM1, 黃色@CODEPARAM2, 紅色大於@CODEPARAM3',0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,REMARK,SORTED,ENABLE) values ('01','PRE8016_3','待分文件數','預查儀表板(PRE8016)','150','150~300','300','綠色小於@CODEPARAM1, 黃色@CODEPARAM2, 紅色大於@CODEPARAM3',0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,REMARK,SORTED,ENABLE) values ('01','PRE8016_4','審核中件數','預查儀表板(PRE8016)','400','400~800','800','綠色小於@CODEPARAM1, 黃色@CODEPARAM2, 紅色大於@CODEPARAM3',0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,REMARK,SORTED,ENABLE) values ('01','PRE8016_5','待發文件數','預查儀表板(PRE8016)','100','100~200','200','綠色小於@CODEPARAM1, 黃色@CODEPARAM2, 紅色大於@CODEPARAM3',0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,CODE_PARAM3,REMARK,SORTED,ENABLE) values ('01','PRE8016_6','展期件數','預查儀表板(PRE8016)','100','100~250','250','綠色小於@CODEPARAM1, 黃色@CODEPARAM2, 紅色大於@CODEPARAM3',0,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','ICMS_WindingUp','http://211.72.223.241/moea/csm/report/runWindingUp.jsp?','公司清算完結同步路徑',null,null,null,7,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','LoginLog','N','是否啟用登入紀錄',null,null,null,null,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','PRE0004_QueueNum','待重建索引筆數限制',null,50,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','PRE0004_SearchMax','比對結果筆數限制',null,500,null,null,1,'Y');

update system_code set code_param1 = '對應清算類別',code_param2 = '回復清算類別' where code_kind = '00' and code = '10';
update system_code set code_param1 = '11' where code_kind = '10' and code in ('04','11');
update system_code set code_param1 = '14' where code_kind = '10' and code in ('08','14');
update system_code set code_param1 = '13' where code_kind = '10' and code in ('09','13');
update system_code set code_param1 = '16' where code_kind = '10' and code in ('10','16');
update system_code set code_param1 = '12' where code_kind = '10' and code in ('12');
update system_code set code_param1 = '15' where code_kind = '10' and code in ('15');
update system_code set code_param1 = '24' where code_kind = '10' and code in ('06', '24');
update system_code set code_param1 = '27' where code_kind = '10' and code in ('27', '29');
update system_code set code_param1 = '30' where code_kind = '10' and code in ('28', '30');
update system_code set code_param2 = '04' where code_kind = '10' and code = '11';
update system_code set code_param2 = '05' where code_kind = '10' and code = '12';
update system_code set code_param2 = '09' where code_kind = '10' and code = '13';
update system_code set code_param2 = '08' where code_kind = '10' and code = '14';
update system_code set code_param2 = '05' where code_kind = '10' and code = '15';
update system_code set code_param2 = '10' where code_kind = '10' and code = '16';
update system_code set code_param2 = '06' where code_kind = '10' and code = '24';
update system_code set code_param2 = '27' where code_kind = '10' and code = '29';
update system_code set code_param2 = '28' where code_kind = '10' and code = '30';

insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','14','展期原因',null,null,null,null,15,'Y');
--CODE_KIND:14
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('14','01','撤件退費',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('14','02','撤件',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('14','03','待補登撤銷解散廢止日',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('14','04','洽機關意見',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('14','90','其他',null,null,null,null,99,'Y');

--依103年度新預查系統問題反應紀錄表編號10 新增功能
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (4,'PRE4019','馬上辦案件查詢','../tcfi/pre/pre4017.jsp',null,2700,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9009','SameName_Queue 單檔維護','../tcfi/pre/pre9009.jsp',null,1800,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (6,'PRE9010','SyncOss_Queue 單檔維護','../tcfi/pre/pre9010.jsp',null,1900,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE2008','郵寄掛號登錄及維護(無預查編號案件)','../tcfi/pre/pre2008.jsp',null,1800,'Y');
INSERT INTO FUNCTION_MENU (PID,CODE,TITLE,URL,TARGET,SORTED,ENABLE) VALUES (2,'PRE4020','一站式案件查詢','../tcfi/pre/pre4020.jsp',null,2700,'Y');
