package com.kangdainfo.tcfi.view.pre;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.granule.json.JSONObject;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1028;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.LmsdCodemapping;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.util.DateUtil;

public class PRE3006 extends SuperBean{

	//查詢畫面欄位
	private String q_id;
	private String q_banNo;
	private String q_cmpyOrLmsName;
	private String q_receiveNo;
	private String q_searchType;
	
	public String getQ_id() {return checkGet(q_id);}
	public void setQ_id(String q_id) {this.q_id = checkSet(q_id);}
	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String q_banNo) {this.q_banNo = checkSet(q_banNo);}
	public String getQ_cmpyOrLmsName() {return checkGet(q_cmpyOrLmsName);}
	public void setQ_cmpyOrLmsName(String q_cmpyName) {this.q_cmpyOrLmsName = checkSet(q_cmpyName);}
	public String getQ_receiveNo() {return checkGet(q_receiveNo);}
	public void setQ_receiveNo(String q_receiveNo) {this.q_receiveNo = checkSet(q_receiveNo);}
	public String getQ_searchType() {return checkGet(q_searchType);}
	public void setQ_searchType(String q_searchType) {this.q_searchType = checkSet(q_searchType);}
	
	//表單畫面欄位
	private String id;
	private String banNo;
	private String statusCodeName;
	private String statusCode;
	private String respName;
	private String capitalAmt;
	private String companyName;
	private String companyAddr;
	private String orgnType;
	private String orgnTypeName;
	private String regUnit;
	private String receiveNo;
	private String clearDate;
	private String clearUnit;
	private String clearWord;
	private String clearNo;
	private String remark;
	private String clearStatusCode;
	public String getId() {return checkGet(id);}
	public void setId(String id) {this.id = checkSet(id);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String banNo) {this.banNo = checkSet(banNo);}
	public String getStatusCodeName() {return checkGet(statusCodeName);}
	public void setStatusCodeName(String statusCodeName) {this.statusCodeName = checkSet(statusCodeName);}
	public String getStatusCode() {return checkGet(statusCode);}
	public void setStatusCode(String statusCode) {this.statusCode = checkSet(statusCode);}
	public String getRespName() {return checkGet(respName);}
	public void setRespName(String respName) {this.respName = checkSet(respName);}
	public String getCapitalAmt() {return checkGet(capitalAmt);}
	public void setCapitalAmt(String capitalAmt) {this.capitalAmt = checkSet(capitalAmt);}
	public String getCompanyName() {return checkGet(companyName);}
	public void setCompanyName(String companyName) {this.companyName = checkSet(companyName);}
	public String getCompanyAddr() {return checkGet(companyAddr);}
	public void setCompanyAddr(String companyAddr) {this.companyAddr = checkSet(companyAddr);}
	public String getOrgnType() {return checkGet(orgnType);}
	public void setOrgnType(String orgnType) {this.orgnType = checkSet(orgnType);}
	public String getOrgnTypeName() {return checkGet(orgnTypeName);}
	public void setOrgnTypeName(String orgnTypeName) {this.orgnTypeName = checkSet(orgnTypeName);}
	public String getRegUnit() {return checkGet(regUnit);}
	public void setRegUnit(String regUnit) {this.regUnit = checkSet(regUnit);}
	public String getReceiveNo() {return checkGet(receiveNo);}
	public void setReceiveNo(String receiveNo) {this.receiveNo = checkSet(receiveNo);}
	public String getClearDate() {return checkGet(clearDate);}
	public void setClearDate(String clearDate) {this.clearDate = checkSet(clearDate);}
	public String getClearUnit() {return checkGet(clearUnit);}
	public void setClearUnit(String clearUnit) {this.clearUnit = checkSet(clearUnit);}
	public String getClearWord() {return checkGet(clearWord);}
	public void setClearWord(String clearWord) {this.clearWord = checkSet(clearWord);}
	public String getClearNo() {return checkGet(clearNo);}
	public void setClearNo(String clearNo) {this.clearNo = checkSet(clearNo);}
	public String getRemark() {return checkGet(remark);}
	public void setRemark(String remark) {this.remark = checkSet(remark);}	
	public String getClearStatusCode() {return checkGet(clearStatusCode);}
	public void setClearStatusCode(String clearStatusCode) {this.clearStatusCode = checkSet(clearStatusCode);}
	
	//紀錄選取的統一編號(多筆)
	private String sendBanNos;
	//紀錄現在的統一編號index
	private String thisRecord;
	private static String searchType;// 2024/04/17 新增
	public String getSendBanNos() {return checkGet(sendBanNos);}
	public void setSendBanNos(String sendBanNos) {this.sendBanNos = checkSet(sendBanNos);}
	public String getThisRecord() {return checkGet(thisRecord);}
	public void setThisRecord(String thisRecord) {this.thisRecord = checkSet(thisRecord);}
	public String getSearchType() {
		return searchType;// 2024/04/17 新增
	}
	public void setSearchType(String searchType) {
		this.searchType = searchType;// 2024/04/17 新增
	}

	private static final String CEDB2000_SQL = "select ban_no, company_name, company_addr, resp_name, " + 
			"(select receive_no from cedb1028 where cedb1028.ban_no = cedb2000.ban_no) as receive_no, " + 
			"status_code, orgn_type, reg_unit " + 
			"from cedb2000";
	
	private static final String LMSM_BUSS_MAIN_SQL = "select ban_no, buss_name, buss_address, res_name, " +
			"(select receive_no from cedb1028 where cedb1028.ban_no = lms.lmsm_buss_main.ban_no) as receive_no, " + 
			"curr_status, org_code, reg_unit_code " +
			"from lms.lmsm_buss_main";
	
	public void init() throws Exception {
		PRE3006 obj = this;
		String[] banNos = this.sendBanNos.split(",");
		obj.setSearchType(searchType);
		if(banNos != null && banNos.length > 0){
			String strBanNo = "";
			for(int i=0; i< banNos.length; i++){
				if(!"".equals(Common.get(banNos[i]))){
					if(i == 0){
						obj.setBanNo(banNos[i]);
						obj.setThisRecord(String.valueOf(i));
					}else{
						strBanNo += ",";
					}
					strBanNo += banNos[i];
				}
			}
			obj.setSendBanNos(strBanNo);
		}
	}
	
	@Override
	public Object doQueryOne() throws Exception {// 新增有限合夥 2024/04/23
		PRE3006 obj = this;
		if("".equals(Common.get(getBanNo()))){
			this.setErrorMsg("查無該筆資料！");
		}else if ("公司登記".equals(this.getSearchType())){
			obj = queryForCedb2000(obj);
		}else if ("有限合夥".equals(this.getSearchType())){
			obj = queryForLmsmBussMain(obj);
		}
		
		return obj;
	}
	
	/**
	 * 公司登記資料查詢
	 * 113/04/17
	 * @param obj
	 * @return PRE3006
	 */
	private PRE3006 queryForCedb2000(PRE3006 obj) {
		Cedb2000 cedb2000 = ServiceGetter.getInstance().getPrefixService().getCedb2000ByBanNo(getBanNo());
		if(cedb2000 != null){
			obj.setBanNo(cedb2000.getBanNo());
			obj.setStatusCode(cedb2000.getStatusCode());
			obj.setStatusCodeName(TcfiView.getStatusName("1", cedb2000.getStatusCode(), cedb2000.getOrgnType(), false));
			obj.setRespName(cedb2000.getRespName());
			obj.setCapitalAmt(Common.get(cedb2000.getCapitalAmt()));
			obj.setCompanyName(cedb2000.getCompanyName());
			obj.setCompanyAddr(cedb2000.getCompanyAddr());
			obj.setOrgnType(cedb2000.getOrgnType());
			obj.setOrgnTypeName(ServiceGetter.getInstance().getSystemCode04Loader().getCodeNameByCode(cedb2000.getOrgnType()));
			obj.setRegUnit(!"".equals(cedb2000.getRegUnitName())?cedb2000.getRegUnitName():ServiceGetter.getInstance().getSystemCode08Loader().getCodeNameByCode(cedb2000.getRegUnit()));
			
			Cedb1028 cedb1028 = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(cedb2000.getBanNo());
			if(cedb1028 != null){
				obj.setId(cedb1028.getBanNo());
				obj.setClearStatusCode(cedb1028.getStatusCode());
				obj.setClearUnit(cedb1028.getClearUnit());
				obj.setClearWord(cedb1028.getClearWord());
				obj.setClearNo(cedb1028.getClearNo());
				obj.setClearDate(cedb1028.getClearDate());
				obj.setReceiveNo(cedb1028.getReceiveNo());
				obj.setRemark(cedb1028.getRemark());
			}else{
				obj.setId("");
				obj.setClearStatusCode("");
				obj.setClearUnit("");
				obj.setClearWord("");
				obj.setClearNo("");
				obj.setClearDate("");
				obj.setReceiveNo("");
				obj.setRemark("");
			}
		}else{
			this.setErrorMsg("查無該筆公司登記資料！");
		}
		
		return obj;
	}
	
	/**
	 * 有限合夥資料查詢
	 * 113/04/17
	 * @param obj
	 * @return PRE3006
	 */
	private PRE3006 queryForLmsmBussMain(PRE3006 obj) {
		LmsmBussMain lmsMain = ServiceGetter.getInstance().getPrefixService().getLmsmBussMainByBanNo(getBanNo());
		if(lmsMain != null){
			obj.setBanNo(lmsMain.getBanNo());
			obj.setStatusCode(lmsMain.getStatus());
			obj.setStatusCodeName(TcfiView.getStatusNameForLmsmBussMain(lmsMain.getStatus(), false));
			obj.setRespName(lmsMain.getRepName());
			obj.setCapitalAmt(Common.get(lmsMain.getRegisterFunds()));
			obj.setCompanyName(lmsMain.getLmsName());
			obj.setCompanyAddr(lmsMain.getBusiAddr());
			obj.setOrgnType(lmsMain.getOrganType());
			obj.setOrgnTypeName(ServiceGetter.getInstance().getLmsdCodemappingOrgLoader().getDescByCode(lmsMain.getOrganType()));
			obj.setRegUnit(ServiceGetter.getInstance().getLmsdRegUnitLoader().getByRegUnitCode(lmsMain.getRegUnitCode()).getAgencyName());
			
			Cedb1028 cedb1028 = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(lmsMain.getBanNo());
			if(cedb1028 != null){
				obj.setId(cedb1028.getBanNo());
				obj.setClearStatusCode(transferStatusCodeFromPrefix(cedb1028.getStatusCode()));
				obj.setClearUnit(cedb1028.getClearUnit());
				obj.setClearWord(cedb1028.getClearWord());
				obj.setClearNo(cedb1028.getClearNo());
				obj.setClearDate(cedb1028.getClearDate());
				obj.setReceiveNo(cedb1028.getReceiveNo());
				obj.setRemark(cedb1028.getRemark());
			}else{
				obj.setId("");
				obj.setClearStatusCode("");
				obj.setClearUnit("");
				obj.setClearWord("");
				obj.setClearNo("");
				obj.setClearDate("");
				obj.setReceiveNo("");
				obj.setRemark("");
			}
		}else{
			this.setErrorMsg("查無該筆有限合夥資料！");
		}
		
		return obj;
	}

	/**
	 * 查詢全部資料--改寫為兼容有限合夥查詢
	 * 113/04/17
	 * @return ArrayList<?>
	 * @throws Exception
	 */
	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> cedb2000s = queryCedb2000s();
		ArrayList<String[]> lmsmBussMains =queryLmsmBussMains();
		
		if(!cedb2000s.isEmpty()) {
			this.setSearchType("公司登記");
			return cedb2000s;
		}else if (!lmsmBussMains.isEmpty()){
			this.setSearchType("有限合夥");
			return lmsmBussMains;
		}else {
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
			return new ArrayList<String[]>();
		}
	}
	
	/**
	 * 查詢公司登記資料
	 * 113/04/17
	 * @return ArrayList<String[]>
	 * @throws Exception
	 */
	private ArrayList<String[]> queryCedb2000s() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		
		com.kangdainfo.common.util.SQLJob sql = new com.kangdainfo.common.util.SQLJob(CEDB2000_SQL);
		StringBuffer condition = new StringBuffer();
		condition.append(" where 1 = 1");
		if(!"".equals(Common.get(getQ_banNo()))){
			condition.append(" and ban_no = ");
			condition.append(Common.sqlChar(getQ_banNo()));
		}
		if(!"".equals(Common.get(getQ_cmpyOrLmsName()))){
			condition.append(" and company_name like ");
			condition.append(Common.sqlChar(getQ_cmpyOrLmsName()+"%"));
		}
		if(!"".equals(Common.get(getQ_receiveNo())) && "".equals(Common.get(getQ_banNo()))){
			Cedb1028 cedb1028 = ServiceGetter.getInstance().getPrefixService().getCedb1028ByReceiveNo(getQ_receiveNo());
			if(cedb1028 != null && !"".equals(Common.get(cedb1028.getBanNo()))){
				condition.append(" and ban_no = ");
				condition.append(Common.sqlChar(cedb1028.getBanNo()));
			}
		}
		if("maintain".equals(Common.get(getQ_searchType())))
			//登錄清算案件
			// 03:重整 04:解散 05:撤銷 06:破產 08:撤回登記 09:廢止 10:廢止登記 27:撤銷許可 28:廢止許可 32:撤銷公司設立
			condition.append(" and status_code in ('03', '04', '05', '06', '08', '09', '10', '27', '28', '32') ");
		else
			//查詢清算案件
			// 11:解散已清算完結 12:撤銷已清算完結 13:廢止已清算完結 14:撤回登記已清算完結 15:撤銷登記已清算完結 16:廢止登記已清算完結 24:破產已清算完結 29:撤銷許可已清算完結 30:廢止許可已清算完結 34:重整完成暨清算完結
			condition.append(" and status_code in ('11', '12', '13', '14', '15', '16', '24', '29', '30', '34') ");
		
		condition.append(" order by ban_no"); 

		sql.appendSQL(condition.toString());
		List<Map<String,Object>> dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql);
		if(dataList != null && dataList.size() > 0){
			Map<String,Object> data = null;
			java.util.Iterator<Map<String,Object>> it = dataList.iterator();
			String[] rowArray = new String[8];
			while (it.hasNext()) {
				data = it.next();
				rowArray = new String[7];
				rowArray[0] = Common.get(data.get("ban_no"));
				rowArray[1] = Common.get(data.get("company_name"));
				rowArray[2] = Common.get(data.get("company_addr"));
				rowArray[3] = Common.get(data.get("resp_name"));
				rowArray[4] = Common.get(data.get("receive_no"));
				rowArray[5] = TcfiView.getStatusName("1", Common.get(data.get("status_code")), Common.get(data.get("orgn_type")), false);
				rowArray[6] = Common.get(ServiceGetter.getInstance().getSystemCode08Loader().getCodeDescByCode(Common.get(data.get("reg_unit"))));
				arrList.add(rowArray);
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}
	
	/**
	 * 查詢有限合夥資料
	 * 113/04/17
	 * @return ArrayList<String[]>
	 * @throws Exception
	 */
	private ArrayList<String[]> queryLmsmBussMains() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		
		com.kangdainfo.common.util.SQLJob sql = new com.kangdainfo.common.util.SQLJob(LMSM_BUSS_MAIN_SQL);
		StringBuffer condition = new StringBuffer();
		condition.append(" where 1 = 1");
		if(!"".equals(Common.get(getQ_banNo()))){
			condition.append(" and ban_no = ");
			condition.append(Common.sqlChar(getQ_banNo()));
		}
		if(!"".equals(Common.get(getQ_cmpyOrLmsName()))){
			condition.append(" and buss_name like ");
			condition.append(Common.sqlChar(getQ_cmpyOrLmsName()+"%"));
		}
		if(!"".equals(Common.get(getQ_receiveNo())) && "".equals(Common.get(getQ_banNo()))){
			Cedb1028 cedb1028 = ServiceGetter.getInstance().getPrefixService().getCedb1028ByReceiveNo(getQ_receiveNo());
			if(cedb1028 != null && !"".equals(Common.get(cedb1028.getBanNo()))){
				condition.append(" and ban_no = ");
				condition.append(Common.sqlChar(cedb1028.getBanNo()));
			}
		}
		if("maintain".equals(Common.get(getQ_searchType())))
			//登錄清算案件
			// 02:停業 03:解散／撤銷 06:列入廢止中 07:廢止 08:破產
			condition.append(" and curr_status in ('02', '03', '06', '07', '08') ");
		else
			//查詢清算案件
			// 09:解散已清算完結 10:撤銷已清算完結 11:廢止已清算完結
			condition.append(" and curr_status in ('09', '10', '11') ");
		
		condition.append(" order by ban_no"); 
		
		sql.appendSQL(condition.toString());
		List<Map<String,Object>> dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql);
		if(dataList != null && dataList.size() > 0){
			Map<String,Object> data = null;
			java.util.Iterator<Map<String,Object>> it = dataList.iterator();
			String[] rowArray = new String[8];
			while (it.hasNext()) {
				data = it.next();
				rowArray = new String[7];
				rowArray[0] = Common.get(data.get("ban_no"));
				rowArray[1] = Common.get(data.get("buss_name"));
				rowArray[2] = Common.get(data.get("buss_address"));
				rowArray[3] = Common.get(data.get("res_name"));
				rowArray[4] = Common.get(data.get("receive_no"));
				rowArray[5] = TcfiView.getStatusNameForLmsmBussMain(Common.get(data.get("curr_status")), false);
				rowArray[6] = Common.get(ServiceGetter.getInstance().getLmsdRegUnitLoader().getAgencyNameByRegUnitCode(Common.get(data.get("reg_unit_code"))));
				arrList.add(rowArray);
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}
	
	public void doWindingUp() throws Exception{// 新增有限合夥 2024/04/23
		try{
			if("有限合夥".equals(getSearchType())) {
				windingUpForLmsBussMain(getBanNo());
			}else if("公司登記".equals(getSearchType())) {
				windingUpForCedb2000(getBanNo());
			}
		}catch(Exception e){
			e.printStackTrace();
	        this.setState("updateError");
			this.setErrorMsg("更新失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
	}
	
	/**
	 * 公司登記清算完結
	 * 113/04/19
	 * @param banNo
	 */
	private void windingUpForCedb2000(String banNo) {
		String result = doSyncIcmsWindingUp(getClearStatusCode(), "update");
		if("success".equals(result)){
			Cedb1028 c = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(getBanNo());
			if(c == null){
				//insert
				c = new Cedb1028();
				c.setBanNo(getBanNo());
				c.setClearDate(getClearDate());
				c.setClearNo(getClearNo());
				c.setClearWord(getClearWord());
				c.setClearUnit(getClearUnit());
				c.setCompanyName(getCompanyName());
				c.setReceiveNo(getReceiveNo());
				c.setRegDate(Datetime.getYYYMMDD());
				c.setRemark(getRemark());
				c.setStatusCode(getClearStatusCode());
				c = ServiceGetter.getInstance().getPrefixService().insertCedb1028(c);
			}else{
				//update
				c.setClearDate(getClearDate());
				c.setClearNo(getClearNo());
				c.setClearWord(getClearWord());
				c.setClearUnit(getClearUnit());
				c.setCompanyName(getCompanyName());
				c.setReceiveNo(getReceiveNo());
				c.setRegDate(Datetime.getYYYMMDD());
				c.setRemark(getRemark());
				c.setStatusCode(getClearStatusCode());
				c = ServiceGetter.getInstance().getPrefixService().updateCedb1028(c);
			}
			this.setState("updateSuccess");
			this.setErrorMsg("存檔成功");
		}else{
			this.setState("updateError");
			this.setErrorMsg(result);
		}
	}
	
	/**
	 * 有限合夥清算完結
	 * 113/04/19
	 * @param banNo
	 */
	private void windingUpForLmsBussMain(String banNo) {
		String result = doSyncUpdateLmsBussMain(getClearStatusCode(), "update");
		Cedb1028 c = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(getBanNo());
		if("success".equals(result)){
			if(c == null){
				//insert
				c = new Cedb1028();
				c.setBanNo(getBanNo());
				c.setClearDate(getClearDate());
				c.setClearNo(getClearNo());
				c.setClearWord(getClearWord());
				c.setClearUnit(getClearUnit());
				c.setCompanyName(getCompanyName());
				c.setReceiveNo(getReceiveNo());
				c.setRegDate(Datetime.getYYYMMDD());
				c.setRemark(getRemark());
				c.setStatusCode(transferStatusCodeFromLms(getClearStatusCode()));// 修改有限合夥於cedb1028中的狀態 2024/05/17
				c = ServiceGetter.getInstance().getPrefixService().insertCedb1028(c);
			}else{
				//update
				c.setClearDate(getClearDate());
				c.setClearNo(getClearNo());
				c.setClearWord(getClearWord());
				c.setClearUnit(getClearUnit());
				c.setCompanyName(getCompanyName());
				c.setReceiveNo(getReceiveNo());
				c.setRegDate(Datetime.getYYYMMDD());
				c.setRemark(getRemark());
				c.setStatusCode(transferStatusCodeFromLms(getClearStatusCode()));// 修改有限合夥於cedb1028中的狀態 2024/05/17
				c = ServiceGetter.getInstance().getPrefixService().updateCedb1028(c);
			}
				this.setState("updateSuccess");
				this.setErrorMsg("存檔成功");
		}else{
			this.setState("updateError");
			this.setErrorMsg(result);
		}
	}

	@Override
	public void doDelete() throws Exception {// 修改，新增有限合夥 2024/04/23
		if("有限合夥".equals(getSearchType())) {
			deleteForLmsBussMain();
		}else if("公司登記".equals(getSearchType())) {
			deleteForCedb2000();
		}
	}
	
	/**
	 * 公司登記資料刪除
	 * 113/04/19
	 * @throws Exception
	 */
	private void deleteForCedb2000() throws Exception {
		Cedb1028 c = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(getBanNo());
		if(c!=null){
			//找出要回復的statusCode
			SystemCode code = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode(getStatusCode());
			if(code == null || "".equals(Common.get(code.getCodeParam2()))){
				throw new MoeaException("取消清算完結失敗!!");
			}
			String result = doSyncIcmsWindingUp(code.getCodeParam2(), "clear");
			if("success".equals(result)){
				ServiceGetter.getInstance().getPrefixService().deleteCedb1028(c.getBanNo());
			}else{
				throw new MoeaException(result);
				//this.setErrorMsg(result);
			}
		}else{
			throw new MoeaException("資料不存在!");
		}
	}
	
	/**
	 * 有限合夥資料刪除
	 * 113/04/22
	 */
	private void deleteForLmsBussMain() throws Exception {
		Cedb1028 c = ServiceGetter.getInstance().getPrefixService().getCedb1028ByBanNo(getBanNo());
		if(c!=null){
			LmsdCodemapping codeData = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode(getStatusCode());
			if(codeData == null || "".equals(Common.get(codeData.getCode()))){
				throw new MoeaException("取消清算完結失敗!!");
			} else {
				if ("09".equals(codeData.getCode()) || "10".equals(codeData.getCode())) {
					codeData.setCode("03");
				} else if ("11".equals(codeData.getCode())){
					codeData.setCode("07");
				}
			}
			
			String result = doSyncUpdateLmsBussMain(codeData.getCode(), "clear");
			
			if("success".equals(result)){
				ServiceGetter.getInstance().getPrefixService().deleteCedb1028(c.getBanNo());
			} else {
				throw new MoeaException(result);
			}
			//找出要回復的statusCode 舊版臨時做法，於2024/11/15 移除使用
//			LmsdCodemapping codeData = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode(getStatusCode());
//			LmsmBussMain lmsMain = ServiceGetter.getInstance().getPrefixService().getLmsmBussMainByBanNo(getBanNo());
//			LmsmBussMain result = null;
//			
//			if(codeData == null || "".equals(Common.get(codeData.getCode()))){
//				throw new MoeaException("取消清算完結失敗!!");
//			}
//			
//			if (lmsMain != null && !"".equals(getClearStatusCode())) {
//				if("09".equals(lmsMain.getStatus()) || "10".equals(lmsMain.getStatus())) {
//					lmsMain.setStatus("03");
//					result = ServiceGetter.getInstance().getPrefixService().updateLmsmBussMain(lmsMain);
//				}else if("11".equals(lmsMain.getStatus())) {
//					lmsMain.setStatus("07");
//					result = ServiceGetter.getInstance().getPrefixService().updateLmsmBussMain(lmsMain);
//				}
//			}
			
//			if(result == null) {
//				throw new MoeaException("資料不存在!");
//			}
//			ServiceGetter.getInstance().getPrefixService().deleteCedb1028(c.getBanNo());
		}else{
			throw new MoeaException("資料不存在!");
		}
	}
	
	private String doSyncIcmsWindingUp(String syncStatus, String syncType){
		if(logger.isInfoEnabled()) logger.info("[doSyncIcmsWindingUp]");
		if(logger.isInfoEnabled()) logger.info("[syncStatus]"+syncStatus);
		if(logger.isInfoEnabled()) logger.info("[syncType]"+syncType);
		try {
			StringBuffer sb = new StringBuffer();
			SystemCode obj = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, "ICMS_WindingUp");
			if(obj != null && !"".equals(Common.get(obj.getCodeName()))){
				sb.append(obj.getCodeName());
				sb.append("ban=").append(getBanNo());
				sb.append("&status=").append(syncStatus);
				sb.append("&date=").append(getClearDate());
				sb.append("&word=").append(URLEncoder.encode(getClearWord(),"UTF-8"));
				sb.append("&no=").append(URLEncoder.encode(getClearNo(),"UTF-8"));
				sb.append("&unit=").append(getClearUnit());
				sb.append("&rcv=").append(URLEncoder.encode(getReceiveNo(),"UTF-8"));
				sb.append("&id=").append(getLoginUserId());
				
				if(logger.isInfoEnabled()) logger.info("[url]"+sb.toString());
				
				org.apache.commons.httpclient.params.HttpClientParams param = new org.apache.commons.httpclient.params.HttpClientParams();
				param.setSoTimeout(240000);
				org.apache.commons.httpclient.HttpMethod method = new org.apache.commons.httpclient.methods.GetMethod(sb.toString());
				org.apache.commons.httpclient.HttpClient client = new org.apache.commons.httpclient.HttpClient(param);
				int statusCode = client.executeMethod(method);
				if (statusCode != org.apache.commons.httpclient.HttpStatus.SC_OK){
					return "清算完結動作發生失敗,與公司系統介接失敗!!";
				}
				
				String jsonString = method.getResponseBodyAsString().trim();
				JSONObject jsonObject = new JSONObject(jsonString);
				String result = jsonObject.getString("result");
				if(logger.isInfoEnabled()) logger.info("[result]"+result);

				if("A".equals(result))
					return "該公司於公司資料庫中存有暫存檔，請過幾天再試試看，或聯絡該公司所屬申登機關。";
				else if("0".equals(result) && "update".equals(syncType))
					return "RMI return 0";
				else if(!("0".equals(result)) && "clear".equals(syncType))
					return "RMI return 0";
			}else{
				return "無法取得公司清算完結介接路徑!!";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "清算完結動作發生失敗,若問題持續,請洽詢系統管理者或相關承辦人員!!";
		}
		return "success";
	}
	
	/**
	 * 更新有限合夥
	 * @return String
	 */
	private String doSyncUpdateLmsBussMain(String syncStatus, String syncType) {
		if(logger.isInfoEnabled()) logger.info("[doSyncUpdateLmsBussMain]");
		if(logger.isInfoEnabled()) logger.info("[syncStatus]"+syncStatus);
		if(logger.isInfoEnabled()) logger.info("[syncType]"+syncType);
		try {
			StringBuffer sb = new StringBuffer();
			SystemCode obj = ServiceGetter.getInstance().getPrefixService().getSystemCodeByCodeKindAndCode(PrefixConstants.CODE_KIND_01, "LMS_WindingUp");
			if(obj != null && !"".equals(Common.get(obj.getCodeName()))){
				sb.append(obj.getCodeName());
				sb.append("ban=").append(getBanNo());
				sb.append("&status=").append(syncStatus);
				sb.append("&date=").append(getClearDate());
				sb.append("&word=").append(URLEncoder.encode(getClearWord(),"UTF-8"));
				sb.append("&no=").append(URLEncoder.encode(getClearNo(),"UTF-8"));
				sb.append("&unit=").append(getClearUnit());
				sb.append("&rcv=").append(URLEncoder.encode(getReceiveNo(),"UTF-8"));
				sb.append("&id=").append(getLoginUserId());
				
				if(logger.isInfoEnabled()) logger.info("[url]"+sb.toString());
				
				org.apache.commons.httpclient.params.HttpClientParams param = new org.apache.commons.httpclient.params.HttpClientParams();
				param.setSoTimeout(240000);
				org.apache.commons.httpclient.HttpMethod method = new org.apache.commons.httpclient.methods.GetMethod(sb.toString());
				org.apache.commons.httpclient.HttpClient client = new org.apache.commons.httpclient.HttpClient(param);
				int statusCode = client.executeMethod(method);
				if (statusCode != org.apache.commons.httpclient.HttpStatus.SC_OK){
					return "有限合夥清算完結動作發生失敗,與有限合夥系統介接失敗!!";
				}
				
				String jsonString = method.getResponseBodyAsString().trim();
				if(logger.isInfoEnabled()) logger.info("[返回結果]"+jsonString);
				JSONObject jsonObject = new JSONObject(jsonString);
				String status = jsonObject.getString("status");
				String message = jsonObject.getString("message");
				if(logger.isInfoEnabled()) logger.info("[狀態]" + status);

				if(!"200".equals(status) || !"ok".equals(message)) {
					return "有限合夥資料同步失敗: " + message;
				}
			}else{
				return "無法取得有限合夥清算完結介接路徑!!";
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "有限合夥清算完結動作發生失敗,若問題持續,請洽詢系統管理者或相關承辦人員!!";
		}
		return "success";
		
		// 舊版臨時做法，於2024/11/15 移除使用
//		LmsmBussMain lmsMain = ServiceGetter.getInstance().getPrefixService().getLmsmBussMainByBanNo(getBanNo());
//		if(lmsMain != null && !"".equals(getClearStatusCode())) {
//			lmsMain.setStatus(getClearStatusCode());
//			LmsmBussMain result = ServiceGetter.getInstance().getPrefixService().updateLmsmBussMain(lmsMain);
//			if (result != null && result.getStatus().equals(getClearStatusCode())) {
//				return "success";
//			}else {
//				return "fail";
//			}
//		} else {
//			return "fail";
//		}
	}
	
	/**
	 * 獲取清算種類
	 * @param statusCode
	 * @param orgCode
	 * @param clearStatusCode
	 * @return String
	 */
	public String getOptionClearStatusCode(String statusCode, String orgCode, String clearStatusCode){
		StringBuilder sb = new StringBuilder();
		sb.append("");
		
		if("有限合夥".equals(getSearchType())) {
			LmsdCodemapping obj = null;
			ArrayList<LmsdCodemapping> objList = new ArrayList<LmsdCodemapping>();
			
			if(!"".equals(Common.get(clearStatusCode))){
				obj = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode(clearStatusCode);
			}else {
				obj = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode(statusCode);
				if (obj != null) {
					if ("03".equals(Common.get(obj.getCode()))) {// 解散／撤銷 --> 解散已清算完結、撤銷已清算完結
						LmsdCodemapping obj01 = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode("09");
						LmsdCodemapping obj02 = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode("10");
						
						objList.add(obj01);
						objList.add(obj02);
					}else if("07".equals(Common.get(obj.getCode()))) {// 廢止 --> 廢止已清算完結
						obj = ServiceGetter.getInstance().getLmsdCodemappingStatLoader().getDataByCode("11");
					}
				}
			}
			
			if(!objList.isEmpty()) {
				for(LmsdCodemapping lms : objList) {
					sb.append("<option value='").append(lms.getCode()).append("' selected ");
					sb.append(">").append(lms.getDescription()).append("</option>\n"); 
				}
			}else {
				if(obj != null){
					sb.append("<option value='").append(obj.getCode()).append("' selected ");
					sb.append(">").append(obj.getDescription()).append("</option>\n"); 
				}else{
		    		sb.append("<option value=''>查詢錯誤</option>");
		    	}
			}
			
			return sb.toString();
		}else if ("公司登記".equals(getSearchType())) {
			SystemCode obj = null;
			
			//05 要判斷orgCode, 先寫死
			if(!"".equals(Common.get(clearStatusCode))){
				obj = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode(clearStatusCode);
			}else if("05".equals(statusCode) || "32".equals(statusCode)){
				if("07".equals(orgCode) || "08".equals(orgCode))
					obj = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode("15");
				else
					obj = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode("12");
			}else{
				obj = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode(statusCode);
				if(obj != null && !"".equals(Common.get(obj.getCodeParam1()))){
					obj = ServiceGetter.getInstance().getSystemCode10Loader().getSystemCodeByCode(obj.getCodeParam1());	
				}else{
					obj = null;
				}
			}
			
			if(obj != null){
				sb.append("<option value='").append(obj.getCode()).append("' selected ");
				sb.append(">").append(obj.getCodeName()).append("</option>\n"); 
			}else{
	    		sb.append("<option value=''>查詢錯誤</option>");
	    	}
			
			return sb.toString();
		}
		
		return sb.toString();
	}
	
	/**
	 * 有限合夥狀態碼轉換預查系統狀態碼
	 * @param lmsStatusCode
	 * @return String
	 * 2024/05/17
	 */
	public String transferStatusCodeFromLms(String lmsStatusCode) {
		switch (lmsStatusCode){
		case "09":
			return "11";
		case "10":
			return "12";
		case "11":
			return "13";
		default :
			return lmsStatusCode;
		}
	}
	
	/**
	 * 預查系統狀態碼轉換有限合夥狀態碼
	 * @param lmsStatusCode
	 * @return String
	 * 2024/05/17
	 */
	public String transferStatusCodeFromPrefix(String prefixCode) {
		switch (prefixCode){
		case "11":
			return "09";
		case "12":
			return "10";
		case "13":
			return "11";
		default :
			return prefixCode;
		}
	}
	
	/**
	 * 將時間轉為有限合夥適用格式，(當天又棄用...，目前未用到)
	 * @param originalDateStr
	 * @return
	 */
	private String toLmsDate(String originalDateStr) {
		if (originalDateStr.length() == 7) {
			String TWYearStr = originalDateStr.substring(0, 3);
			String dateStr = originalDateStr.substring(3, originalDateStr.length());
			Integer year = 1911 + Integer.valueOf(TWYearStr);
			return year.toString() + dateStr;
		} else {
			return DateUtil.getSystemDate(); // 直接返回系統時間
		}
	}
	
	@Override
	public void doCreate() throws Exception {}

	@Override
	public void doUpdate() throws Exception {}
}
