<!--
程式目的：審核-額外註記 (PRE3001_04)
程式代號：PRE3001_04
撰寫日期：103.07.04
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3001" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3001_05">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
if ("init".equals(obj.getState())){
	obj.extraMarkQuery();
} else if("extraMarkQuery".equals(obj.getState())) {
	obj.extraMarkQuery();
} else if("extraMarkSave".equals(obj.getState())) {
	obj.extraMarkSave();
} else if("extraMarkDelete".equals(obj.getState())) {
	obj.extraMarkDelete();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>"%>
<script>
$(document).ready(function() {
	//事件註冊
	$('#btnQuery').click(function(){
		form1.state.value = 'extraMarkQuery';
		form1.submit();
	});
	$('#btnSave').click(function(){
		form1.state.value = 'extraMarkSave';
		form1.submit();
	});
	$('#btnDelete').click(function(){
		form1.state.value = 'extraMarkDelete';
		form1.submit();
	});
});
</script>
</head>
<body>
<form id="form1" name="form1" method="post">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='審核-額外註記'/>
</c:import>

<!-- TOOLBAR AREA -->
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td style="text-align:center">
			<input type="button" class="toolbar_default" id="btnQuery" name="btnQuery" value="查詢" />&nbsp;
			<input type="button" class="toolbar_default" id="btnSave" name="btnSave" value="存檔" >&nbsp;
			<input type="button" class="toolbar_default" id="btnDelete" name="btnDelete" value="刪除" >&nbsp;
			<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開" onclick="javascript:window.close();" />&nbsp;
		</td>
	</tr>
</table>
<!-- TOOLBAR AREA -->


<table width="100%" cellspacing="0" cellpadding="0">

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form" width="150px">預查編號：</td>
		<td class="td_form_white">
			<input class="field_RO" readonly name="prefixNo" value=<%=obj.getPrefixNo() %> size="10" maxlength="9" />
		</td>
	</tr>
	<tr>
		<td class="td_form_white">外商註記</td>
		<td class="td_form_white">
			非外國公司不得使用「
			<input class="content" name="foreignMark" value="<%=obj.getForeignMark() %>" size="10" maxlength="10" />
			商」字樣
		</td>
	</tr>
	<tr>
		<td class="td_form_white">
			<input type="checkbox" id="chinaMark" name="chinaMark" value="Y" <%=("Y".equals(obj.getChinaMark())?"checked":"") %> />
			大陸商註記
		</td>
		<td class="td_form_white">
			非大陸商許可公司，不得使用大陸商字樣。
		</td>
	</tr>
	<tr>
		<td class="td_form_white">合併(分割)消滅註記</td>
		<td class="td_form_white">
			此件預查表應於
			<input class="content" name="companyName" value="<%=obj.getCompanyName() %>" size="20" maxlength="60" />
			【統一編號：
			<input class="content" name="banNo" value="<%=obj.getBanNo() %>" size="9" maxlength="8" />
			】合併(分割)消滅後始生效力
		</td>
	</tr>
	</table>
	</div>
<c:import url="../common/msgbar.jsp">
	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
</td></tr>

<!-- TOOLBAR AREA -->
<tr style="display:none;"><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:left;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
		<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>" />
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>