package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 禁用名稱(CEDBC004)
 *
 */
public class Cedbc004 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 禁用名稱序號 */
	private String seqNo;
	/** 禁用名稱 */
	private String banWord;
	/**	異動人員 */
	private String modIdNo;
	/**	異動日期  */
	private String modDate;
	/**	異動時間  */
	private String modTime;
	/** 相關函號單位 */
	private String unit;
	/** 相關函號 */
	private String letterNo;
	/** 相關函號日期 */
	private String letterDate;
	/** 相關函號說明 */
	private String letterDesc;

	/** 禁用名稱序號 */
	public String getSeqNo() {return seqNo;}
	/** 禁用名稱序號 */
	public void setSeqNo(String s) {this.seqNo = s;}
	/** 禁用名稱 */
	public String getBanWord() {return banWord;}
	/** 禁用名稱 */
	public void setBanWord(String s) {this.banWord = s;}
	/**	異動人員 */
	public String getModIdNo() {return modIdNo;}
	/**	異動人員 */
	public void setModIdNo(String s) {this.modIdNo = s;}
	/**	異動日期  */
	public String getModDate() {return modDate;}
	/**	異動日期  */
	public void setModDate(String s) {this.modDate = s;}
	/**	異動時間  */
	public String getModTime() {return modTime;}
	/**	異動時間  */
	public void setModTime(String s) {this.modTime = s;}
	/** 相關函號單位 */
	public String getUnit() {return unit;}
	/** 相關函號單位 */
	public void setUnit(String s) {this.unit = s;}
	/** 相關函號 */
	public String getLetterNo() {return letterNo;}
	/** 相關函號 */
	public void setLetterNo(String s) {this.letterNo = s;}
	/** 相關函號日期 */
	public String getLetterDate() {return letterDate;}
	/** 相關函號日期 */
	public void setLetterDate(String s) {this.letterDate = s;}
	/** 相關函號說明 */
	public String getLetterDesc() {return letterDesc;}
	/** 相關函號說明 */
	public void setLetterDesc(String s) {this.letterDesc = s;}

}