package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1021;


public class Cedb1021Dao extends BaseDaoJdbc implements RowMapper<Cedb1021> {
	
	public List<Cedb1021> findList(String prefixStart, String prefixEnd) {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT * FROM Cedb1021 WHERE 1=1 ");
		if (!"".equals(prefixStart)) {
			sql.append("and prefix_No >= '");
			sql.append(prefixStart);
			sql.append("'");
		} // end if
		if (!"".equals(prefixEnd)) {
			sql.append("and prefix_No <= '");
			sql.append(prefixEnd);
			sql.append("'");
		} // end if
		sql.append(" order by prefix_no");
		List<Cedb1021> list = getJdbcTemplate().query(sql.toString(), this);
		return list;
	}
	
	public Cedb1021 findCedb1021ByPrefixNo( String prefixNo ) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1021 WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
        List<Cedb1021> list = getJdbcTemplate().query(sqljob.getSQL(),sqljob.getParametersArray(), this);
        return list.isEmpty()?  null : list.get(0); 
	} 
	
	public int insert(Cedb1021 obj ) {
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1021 ( PREFIX_NO, UPDATE_ID_NO, UPDATE_DATE, UPDATE_TIME) VALUES(?,?,?,?)");
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addParameter(obj.getUpdateTime());
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	public int update(Cedb1021 obj) {
		SQLJob sqljob = new SQLJob("UPDATE CEDB1021 SET( UPDATE_ID_NO = ?, UPDATE_DATE = ?, UPDATE_TIME = ? ) WHERE PREFIX_NO = ?");
		sqljob.addParameter(obj.getUpdateIdNo());
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addParameter(obj.getPrefixNo());
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), getSqlTypes());
	}
	
	
	public int[] getSqlTypes() {
		return new int[] { 
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR,
			java.sql.Types.VARCHAR
		};
	}
	
	@Override
	public Cedb1021 mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1021 obj = null;
		if (null != rs) {
			obj = new Cedb1021();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setUpdateIdNo(rs.getString("UPDATE_ID_NO"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
		}
		return obj;
	}
}
