<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<bean id="authenticationService"
		class="com.kangdainfo.common.service.impl.AuthenticationServiceImpl" />

	<bean id="serviceGetter" class="com.kangdainfo.ServiceGetter"
		autowire="byName">
		<property name="env" value="${env}" />
		<property name="webContextInfo" ref="webContextInfo" />
		<property name="authenticationService" ref="authenticationService" />
		<property name="prefixService" ref="prefixService" />
		<property name="com0001Service" ref="com0001Service" />
		<property name="pre0004Service" ref="pre0004Service" />
		<property name="pre1001Service" ref="pre1001Service" />
		<property name="pre2001Service" ref="pre2001Service" />
		<property name="pre2003Service" ref="pre2003Service" />
		<property name="pre2004Service" ref="pre2004Service" />
		<property name="pre2008Service" ref="pre2008Service" />
		<property name="pre3005Service" ref="pre3005Service" />
		<property name="pre3007Service" ref="pre3007Service" />
		<property name="pre3008Service" ref="pre3008Service" />
		<property name="pre4001Service" ref="pre4001Service" />
		<property name="pre4006Service" ref="pre4006Service" />
		<property name="pre4013Service" ref="pre4013Service" />
		<property name="pre4020Service" ref="pre4020Service" />
		<property name="pre4022Service" ref="pre4022Service" />
		<property name="pre8005Service" ref="pre8005Service" />
		<property name="pre8006Service" ref="pre8006Service" />
		<property name="pre8010Service" ref="pre8010Service" />
		<property name="pre8011Service" ref="pre8011Service" />
		<property name="pre8018Service" ref="pre8018Service" />
		<property name="pre5001Service" ref="pre5001Service" />
		<property name="pre5002Service" ref="pre5002Service" />
		<property name="pre9005Service" ref="pre9005Service" />
		<property name="sameNameCompareService" ref="sameNameCompareService" />
		<property name="approveService" ref="approveService" />
		<property name="backupService" ref="backupService" />
		<property name="encapsulateService" ref="encapsulateService" />
		<property name="updateOsssStatusService" ref="updateOsssStatusService" />
		<property name="trackLogService" ref="trackLogService" />
		<property name="caseFlowService" ref="caseFlowService" />
		<property name="noPayMarkService" ref="noPayMarkService" />
		<property name="moeaicApproveService" ref="moeaicApproveService" />
		<property name="correctDataService" ref="correctDataService" />
		<!-- Query Dao -->
		<property name="eedbGeneralQueryDao" ref="eedbGeneralQueryDao" />
		<property name="eicmGeneralQueryDao" ref="eicmGeneralQueryDao" />
		<property name="osssGeneralQueryDao" ref="osssGeneralQueryDao" />
		<property name="icmsGeneralQueryDao" ref="icmsGeneralQueryDao" />
		<!-- loader -->
		<property name="systemNewsLoader" ref="systemNewsLoader" />
		<property name="cedbc000CodeLoader" ref="cedbc000CodeLoader" />
		<property name="cedbc058CodeLoader" ref="cedbc058CodeLoader" />
		<property name="systemCode01Loader" ref="systemCode01Loader" />
		<property name="systemCode02Loader" ref="systemCode02Loader" />
		<property name="systemCode03Loader" ref="systemCode03Loader" />
		<property name="systemCode04Loader" ref="systemCode04Loader" />
		<property name="systemCode05Loader" ref="systemCode05Loader" />
		<property name="systemCode06Loader" ref="systemCode06Loader" />
		<property name="systemCode07Loader" ref="systemCode07Loader" />
		<property name="systemCode08Loader" ref="systemCode08Loader" />
		<property name="systemCode09Loader" ref="systemCode09Loader" />
		<property name="systemCode10Loader" ref="systemCode10Loader" />
		<property name="systemCode11Loader" ref="systemCode11Loader" />
		<property name="systemCode12Loader" ref="systemCode12Loader" />
		<property name="systemCode13Loader" ref="systemCode13Loader" />
		<!-- 2024/04/16 新增 -->
		<property name="lmsdCodemappingOrgLoader" ref="lmsdCodemappingOrgLoader" />
		<!-- 2024/04/17 新增 -->
		<property name="lmsdCodemappingStatLoader" ref="lmsdCodemappingStatLoader" />
		<!-- 2024/04/17 新增 -->
		<property name="lmsdRegUnitLoader" ref="lmsdRegUnitLoader" />
		<!-- lucene -->
		<property name="indexSearchService" ref="indexSearchService" />
		<property name="indexCreateService" ref="indexCreateService" />
		<property name="indexUpdateService" ref="indexUpdateService" />
		<!-- test -->
		<property name="testService" ref="testService" />
	</bean>

	<!-- ======================= util ======================= -->
	<bean id="webContextInfo" class="com.kangdainfo.web.util.WebContextInfo" />

</beans>