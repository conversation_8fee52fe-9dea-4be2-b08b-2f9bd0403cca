package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 個資軌跡紀錄檔(TRACK_LOG)
 *
 */
public class TrackLog  extends BaseModel {

	private static final long serialVersionUID = 1L;
	
	/** 主鍵值 */
	private Integer id;
	/** 功能代碼 */
	private String funcCode;
	/** 功能名稱 */
	private String funcName;
	/** 連線IP */
	private String ip;
	/** 操作種類(E:修改,Q:查詢) */
	private String type;
	/** 操作狀態 */
	private String status;
	/** 使用者帳號 */
	private String idNo;
	/** 使用者名稱 */
	private String name;
	/** 操作日期 */
	private String date;
	/** 操作時間 */
	private String time;
	/** 查詢條件/個資欄位 */
	private String remark;
	
	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}
	/** 功能代碼 */
	public String getFuncCode() {return funcCode;}
	/** 功能代碼 */
	public void setFuncCode(String funcCode) {this.funcCode = funcCode;}
	/** 功能名稱 */
	public String getFuncName() {return funcName;}
	/** 功能名稱 */
	public void setFuncName(String funcName) {this.funcName = funcName;}
	/** 連線IP */
	public String getIp() {return ip;}
	/** 連線IP */
	public void setIp(String ip) {this.ip = ip;}
	/** 操作種類(E:修改,Q:查詢) */
	public String getType() {return type;}
	/** 操作種類(E:修改,Q:查詢) */
	public void setType(String type) {this.type = type;}
	/** 操作狀態 */
	public String getStatus() {return status;}
	/** 操作狀態 */
	public void setStatus(String status) {this.status = status;}
	/** 使用者帳號 */
	public String getIdNo() {return idNo;}
	/** 使用者帳號 */
	public void setIdNo(String idNo) {this.idNo = idNo;}
	/** 使用者名稱 */
	public String getName() {return name;}
	/** 使用者名稱 */
	public void setName(String name) {this.name = name;}
	/** 操作日期 */
	public String getDate() {return date;}
	/** 操作日期 */
	public void setDate(String date) {this.date = date;}
	/** 操作時間 */
	public String getTime() {return time;}
	/** 操作時間 */
	public void setTime(String time) {this.time = time;}
	/** 查詢條件/個資欄位 */
	public String getRemark() {return remark;}
	/** 查詢條件/個資欄位 */
	public void setRemark(String remark) {this.remark = remark;}
}
