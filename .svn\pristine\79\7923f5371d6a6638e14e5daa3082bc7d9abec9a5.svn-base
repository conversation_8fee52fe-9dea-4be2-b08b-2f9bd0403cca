package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.ZsmxEmptyWord;

public class ZsmxEmptyWordDao extends BaseDaoJdbc implements RowMapper<ZsmxEmptyWord> {

	private static final String SQL_findAll = "SELECT * FROM ICMS.ZSMX_EMPTY_WORD";
	public List<ZsmxEmptyWord> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<ZsmxEmptyWord>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public ZsmxEmptyWord mapRow(ResultSet rs, int idx) throws SQLException {
		ZsmxEmptyWord obj = null;
		if(null!=rs) {
			obj = new ZsmxEmptyWord();
			obj.setPos(rs.getString("POS"));
			obj.setWord(rs.getString("WORD"));
		}
		return obj;
	}

}