package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;;

public class PRE8018 extends SuperBean {
	
	private String receiptType0 ;
	private String startReceiptNo0 ;
	private String endReceiptNo0 ;
	private String usedReceiptNo0 ;
	private String receiptType1 ;
	private String startReceiptNo1 ;
	private String endReceiptNo1 ;
    private String usedReceiptNo1;

	// ----------------------------getters and setters of local variables bellow--------------------------------------------
	
	// --------------------------------------------------------------------------------------
	
	public String getReceiptType0() {
		return receiptType0;
	}
	public void setReceiptType0(String receiptType0) {
		this.receiptType0 = receiptType0;
	}
	public String getStartReceiptNo0() {
		return startReceiptNo0;
	}
	public void setStartReceiptNo0(String startReceiptNo0) {
		this.startReceiptNo0 = startReceiptNo0;
	}
	public String getEndReceiptNo0() {
		return endReceiptNo0;
	}
	public void setEndReceiptNo0(String endReceiptNo0) {
		this.endReceiptNo0 = endReceiptNo0;
	}
	public String getUsedReceiptNo0() {
		return usedReceiptNo0;
	}
	public void setUsedReceiptNo0(String usedReceiptNo0) {
		this.usedReceiptNo0 = usedReceiptNo0;
	}
	public String getReceiptType1() {
		return receiptType1;
	}
	public void setReceiptType1(String receiptType1) {
		this.receiptType1 = receiptType1;
	}
	public String getStartReceiptNo1() {
		return startReceiptNo1;
	}
	public void setStartReceiptNo1(String startReceiptNo1) {
		this.startReceiptNo1 = startReceiptNo1;
	}
	public String getEndReceiptNo1() {
		return endReceiptNo1;
	}
	public void setEndReceiptNo1(String endReceiptNo1) {
		this.endReceiptNo1 = endReceiptNo1;
	}
	public String getUsedReceiptNo1() {
		return usedReceiptNo1;
	}
	public void setUsedReceiptNo1(String usedReceiptNo1) {
		this.usedReceiptNo1 = usedReceiptNo1;
	}


	public Object doQueryOne() throws Exception{ 
		PRE8018 obj = this ;                 // 使用者端
		java.util.List<ReceiptNoSetup> objList = ServiceGetter.getInstance().getPre8018Service().selectAll();
		if (objList != null && objList.size() > 0){
			for(ReceiptNoSetup o : objList){
				if("0".equals(o.getReceiptType())){
					obj.setReceiptType0(o.getReceiptType());
					obj.setStartReceiptNo0(o.getStartReceiptNo());
					obj.setEndReceiptNo0(o.getEndReceiptNo());
					obj.setUsedReceiptNo0(o.getUsedReceiptNo());
				}else if("1".equals(o.getReceiptType())){
					obj.setReceiptType1(o.getReceiptType());
					obj.setStartReceiptNo1(o.getStartReceiptNo());
					obj.setEndReceiptNo1(o.getEndReceiptNo());
					obj.setUsedReceiptNo1(o.getUsedReceiptNo());
				}
			}
		}else{
			this.setErrorMsg("查無資料！");
		} // end if
	    
		return obj;
	} // end doQueryOne()
	

	public ArrayList<?> doQueryAll() throws Exception{
		return null;
	}
		
	public void doUpdate() throws Exception {   
		PRE8018 obj = this;
		CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
		ReceiptNoSetup vo = ServiceGetter.getInstance().getPre8018Service().selectByReceiptType("0");
		if (vo == null) {
			throw new Exception("查無資料!");
		} else {
			vo.setReceiptType("0");
			vo.setStartReceiptNo(obj.getStartReceiptNo0());
			vo.setEndReceiptNo(obj.getEndReceiptNo0());
			vo.setUsedReceiptNo(obj.getUsedReceiptNo0());
			vo.setUpdateDate(Datetime.getDateTimeFromNTPServer(null));
			vo.setUpdateUser(user.getUserId());
			ServiceGetter.getInstance().getPre8018Service().doUpdate(vo);
		}
		
		vo = ServiceGetter.getInstance().getPre8018Service().selectByReceiptType("1");
		if (vo == null) {
			throw new Exception("查無資料!");
		} else {
			vo.setReceiptType("1");
			vo.setStartReceiptNo(obj.getStartReceiptNo1());
			vo.setEndReceiptNo(obj.getEndReceiptNo1());
			vo.setUsedReceiptNo(obj.getUsedReceiptNo1());
			vo.setUpdateDate(Datetime.getDateTimeFromNTPServer(null));
			vo.setUpdateUser(user.getUserId());
			ServiceGetter.getInstance().getPre8018Service().doUpdate(vo);
		}

	} // end doUpdate()
	
	public void doCreate() throws Exception{
		
	} // end doCreate() 
	  
    public void doDelete() throws Exception{			
		   
    } // end doDelete()	
} //PRE8009()