package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.TrackLog;

public class Track<PERSON>ogDao extends BaseDaoJdbc implements RowMapper<TrackLog>{

	private static final String SQL_query = "SELECT * FROM TRACK_LOG ";
	public List<TrackLog> query(String funcCode, String type, String idNo, String dateS, String dateE){
		SQLJob sqljob = new SQLJob(SQL_query);
		if(!"".equals(funcCode)){
			sqljob.appendSQLCondition(" FUNC_CODE = ? ");
			sqljob.addParameter(funcCode);
		}
		if(!"".equals(type)){
			sqljob.appendSQLCondition(" OP_TYPE = ? ");
			sqljob.addParameter(type);
		}
		if(!"".equals(idNo)){
			sqljob.appendSQLCondition(" OP_ID_NO = ? ");
			sqljob.addParameter(idNo);
		}
		if(!"".equals(dateS)){
			sqljob.appendSQLCondition(" OP_DATE >= ? ");
			sqljob.addParameter(dateS);
		}
		if(!"".equals(dateE)){
			sqljob.appendSQLCondition(" OP_DATE <= ? ");
			sqljob.addParameter(dateE);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<TrackLog>)getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(),  this);
	}
	
	private static final String SQL_insert = "INSERT INTO TRACK_LOG(FUNC_CODE, FUNC_NAME, OP_IP, OP_TYPE, OP_STATUS, OP_ID_NO, OP_NAME, OP_DATE, OP_TIME, OP_REMARK) "
			+ "VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	public void insert(TrackLog obj){
		if(obj != null){
			SQLJob sqljob = new SQLJob(SQL_insert);
			sqljob.addParameter(obj.getFuncCode());
			sqljob.addParameter(obj.getFuncName());
			sqljob.addParameter(obj.getIp());
			sqljob.addParameter(obj.getType());
			sqljob.addParameter(obj.getStatus());
			sqljob.addParameter(obj.getIdNo());
			sqljob.addParameter(obj.getName());
			sqljob.addParameter(obj.getDate());
			sqljob.addParameter(obj.getTime());
			sqljob.addParameter(obj.getRemark());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
					,new int[]{
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR,
						java.sql.Types.VARCHAR
				});
		}
	}
	
	@Override
	public TrackLog mapRow(ResultSet rs, int arg1) throws SQLException {
		TrackLog obj = null;
		if(null!=rs) {
			obj = new TrackLog();
			obj.setId(rs.getInt("ID"));
			obj.setFuncCode(rs.getString("FUNC_CODE"));
			obj.setFuncName(rs.getString("FUNC_NAME"));
			obj.setIp(rs.getString("OP_IP"));
			obj.setType(rs.getString("OP_TYPE"));
			obj.setStatus(rs.getString("OP_STATUS"));
			obj.setIdNo(rs.getString("OP_ID_NO"));
			obj.setName(rs.getString("OP_NAME"));
			obj.setDate(rs.getString("OP_DATE"));
			obj.setTime(rs.getString("OP_TIME"));
			obj.setRemark(rs.getString("OP_REMARK"));
		}
		return obj;
	}
}
