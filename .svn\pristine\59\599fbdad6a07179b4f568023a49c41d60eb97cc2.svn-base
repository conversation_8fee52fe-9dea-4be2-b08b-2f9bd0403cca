--grant all on EICM.CEDBC058 to EICM4AP;

alter table EICM.CEDBC058
add(
     ID varchar2(20),
     BASE_NAME varchar2(4),  
     CAN_USE char(1) default 'Y',
     BE_USED char(1) default 'Y',
     STATUS char(1),
     CREATE_USER varchar2(20),
     CREATE_DATE varchar2(20),
     CREATE_TIME varchar2(20),
     UPDATE_USER varchar2(20),
     UPDATE_DATE varchar2(20),
     UPDATE_TIME varchar2(20)
     );
     
-- Create sequence 
create sequence EICM.SEQ_CEDBC058_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

--update id
update CEDBC058 set id = EICM.SEQ_CEDBC058_ID.NextVal;
commit;

--add constraint
alter table EICM.CEDBC058
add(
     constraint PK_CEDBC058_ID primary key (ID)
);
     
-- Create Trigger
Create Or Replace Trigger EICM.TG_CEDBC058
Before Insert ON EICM.CEDBC058 Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_CEDBC058_ID.NextVal into :nu.id From Dual;
End;
/

--comment
comment on column CEDBC058.BASE_NAME is '基本字';
comment on column CEDBC058.CAN_USE is '是否可使用(Y:可使用/N:不可使用(預設Y))';
comment on column CEDBC058.BE_USED is '是否已使用(Y:已使用/N:未使用(預設N))';
comment on column CEDBC058.STATUS is '狀態(1:新增(預設)/2:修改/3:刪除/9:已更新)';
comment on column CEDBC058.CREATE_DATE is '建立日期';
comment on column CEDBC058.CREATE_TIME is '建立時間';
comment on column CEDBC058.CREATE_USER is '建立者';
comment on column CEDBC058.UPDATE_DATE is '異動日期';
comment on column CEDBC058.UPDATE_TIME is '異動時間';
comment on column CEDBC058.UPDATE_USER is '異動者';

-DATA
update eicm.cedbc058 set BASE_NAME = '雲', CAN_USE='Y' where same_name_1='云' and same_name='雲';
update eicm.cedbc058 set BASE_NAME = '雲', CAN_USE='Y' where same_name_1='雲' and same_name='云';
update eicm.cedbc058 set BASE_NAME = '璽', CAN_USE='Y' where same_name_1='璽' and same_name='壐';
update eicm.cedbc058 set BASE_NAME = '璽', CAN_USE='Y' where same_name_1='壐' and same_name='璽';
update eicm.cedbc058 set BASE_NAME = '達', CAN_USE='Y' where same_name_1='逹' and same_name='達';
update eicm.cedbc058 set BASE_NAME = '達', CAN_USE='Y' where same_name_1='達' and same_name='逹';
update eicm.cedbc058 set BASE_NAME = '魅', CAN_USE='Y' where same_name_1='鬽' and same_name='魅';
update eicm.cedbc058 set BASE_NAME = '魅', CAN_USE='Y' where same_name_1='魅' and same_name='鬽';
update eicm.cedbc058 set BASE_NAME = '魅', CAN_USE='Y' where same_name_1='魅' and same_name='鬽';
update eicm.cedbc058 set BASE_NAME = '魅', CAN_USE='Y' where same_name_1='鬽' and same_name='魅';
update eicm.cedbc058 set BASE_NAME = '對', CAN_USE='Y' where same_name_1='對' and same_name='对';
update eicm.cedbc058 set BASE_NAME = '對', CAN_USE='Y' where same_name_1='对' and same_name='對';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='點' and same_name='奌';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='點' and same_name='点';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='奌' and same_name='點';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='奌' and same_name='点';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='点' and same_name='點';
update eicm.cedbc058 set BASE_NAME = '點', CAN_USE='Y' where same_name_1='点' and same_name='奌';
update eicm.cedbc058 set BASE_NAME = '牆', CAN_USE='Y' where same_name_1='牆' and same_name='墻';
update eicm.cedbc058 set BASE_NAME = '牆', CAN_USE='Y' where same_name_1='墻' and same_name='牆';
update eicm.cedbc058 set BASE_NAME = '聯', CAN_USE='Y' where same_name_1='聯' and same_name='联';
update eicm.cedbc058 set BASE_NAME = '聯', CAN_USE='Y' where same_name_1='联' and same_name='聯';
update eicm.cedbc058 set BASE_NAME = '雞', CAN_USE='Y' where same_name_1='雞' and same_name='鷄';
update eicm.cedbc058 set BASE_NAME = '雞', CAN_USE='Y' where same_name_1='鷄' and same_name='雞';
update eicm.cedbc058 set BASE_NAME = '銢', CAN_USE='Y' where same_name_1='' and same_name='銢';
update eicm.cedbc058 set BASE_NAME = '銢', CAN_USE='Y' where same_name_1='銢' and same_name='';
update eicm.cedbc058 set BASE_NAME = '俞', CAN_USE='Y' where same_name_1='兪' and same_name='俞';
update eicm.cedbc058 set BASE_NAME = '俞', CAN_USE='Y' where same_name_1='俞' and same_name='兪';
update eicm.cedbc058 set BASE_NAME = '雋', CAN_USE='Y' where same_name_1='雋' and same_name='隽';
update eicm.cedbc058 set BASE_NAME = '雋', CAN_USE='Y' where same_name_1='隽' and same_name='雋';
update eicm.cedbc058 set BASE_NAME = '準', CAN_USE='Y' where same_name_1='準' and same_name='凖';
update eicm.cedbc058 set BASE_NAME = '準', CAN_USE='Y' where same_name_1='凖' and same_name='準';
update eicm.cedbc058 set BASE_NAME = '沖', CAN_USE='Y' where same_name_1='沖' and same_name='冲';
update eicm.cedbc058 set BASE_NAME = '沖', CAN_USE='Y' where same_name_1='冲' and same_name='沖';
update eicm.cedbc058 set BASE_NAME = '村', CAN_USE='Y' where same_name_1='村' and same_name='邨';
update eicm.cedbc058 set BASE_NAME = '村', CAN_USE='Y' where same_name_1='邨' and same_name='村';
update eicm.cedbc058 set BASE_NAME = '柏', CAN_USE='Y' where same_name_1='柏' and same_name='栢';
update eicm.cedbc058 set BASE_NAME = '柏', CAN_USE='Y' where same_name_1='栢' and same_name='柏';
update eicm.cedbc058 set BASE_NAME = '塗', CAN_USE='Y' where same_name_1='塗' and same_name='';
update eicm.cedbc058 set BASE_NAME = '塗', CAN_USE='Y' where same_name_1='' and same_name='塗';
update eicm.cedbc058 set BASE_NAME = '鹽', CAN_USE='Y' where same_name_1='塩' and same_name='鹽';
update eicm.cedbc058 set BASE_NAME = '鹽', CAN_USE='Y' where same_name_1='鹽' and same_name='塩';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靘' and same_name='靚';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靚' and same_name='靘';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靓' and same_name='靚';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靚' and same_name='靓';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靘' and same_name='靓';
update eicm.cedbc058 set BASE_NAME = '靚', CAN_USE='Y' where same_name_1='靓' and same_name='靘';
update eicm.cedbc058 set BASE_NAME = '線', CAN_USE='Y' where same_name_1='線' and same_name='綫';
update eicm.cedbc058 set BASE_NAME = '線', CAN_USE='Y' where same_name_1='綫' and same_name='線';
update eicm.cedbc058 set BASE_NAME = '貌', CAN_USE='Y' where same_name_1='貌' and same_name='貎';
update eicm.cedbc058 set BASE_NAME = '貌', CAN_USE='Y' where same_name_1='貎' and same_name='貌';
update eicm.cedbc058 set BASE_NAME = '館', CAN_USE='Y' where same_name_1='館' and same_name='舘';
update eicm.cedbc058 set BASE_NAME = '館', CAN_USE='Y' where same_name_1='舘' and same_name='館';
update eicm.cedbc058 set BASE_NAME = '舉', CAN_USE='Y' where same_name_1='舉' and same_name='擧';
update eicm.cedbc058 set BASE_NAME = '舉', CAN_USE='Y' where same_name_1='擧' and same_name='舉';
update eicm.cedbc058 set BASE_NAME = '千', CAN_USE='Y' where same_name_1='仟' and same_name='千';
update eicm.cedbc058 set BASE_NAME = '千', CAN_USE='Y' where same_name_1='千' and same_name='仟';
update eicm.cedbc058 set BASE_NAME = '臺', CAN_USE='Y' where same_name_1='台' and same_name='臺';
update eicm.cedbc058 set BASE_NAME = '臺', CAN_USE='Y' where same_name_1='臺' and same_name='台';
update eicm.cedbc058 set BASE_NAME = '洲', CAN_USE='Y' where same_name_1='州' and same_name='洲';
update eicm.cedbc058 set BASE_NAME = '洲', CAN_USE='Y' where same_name_1='洲' and same_name='州';
update eicm.cedbc058 set BASE_NAME = '采', CAN_USE='Y' where same_name_1='采' and same_name='釆';
update eicm.cedbc058 set BASE_NAME = '采', CAN_USE='Y' where same_name_1='釆' and same_name='采';
update eicm.cedbc058 set BASE_NAME = '崗', CAN_USE='Y' where same_name_1='岡' and same_name='崗';
update eicm.cedbc058 set BASE_NAME = '崗', CAN_USE='Y' where same_name_1='崗' and same_name='岡';
update eicm.cedbc058 set BASE_NAME = '晉', CAN_USE='Y' where same_name_1='晉' and same_name='晋';
update eicm.cedbc058 set BASE_NAME = '晉', CAN_USE='Y' where same_name_1='晋' and same_name='晉';
update eicm.cedbc058 set BASE_NAME = '證', CAN_USE='Y' where same_name_1='證' and same_name='証';
update eicm.cedbc058 set BASE_NAME = '證', CAN_USE='Y' where same_name_1='証' and same_name='證';
update eicm.cedbc058 set BASE_NAME = '潢', CAN_USE='Y' where same_name_1='潢' and same_name='璜';
update eicm.cedbc058 set BASE_NAME = '潢', CAN_USE='Y' where same_name_1='璜' and same_name='潢';
update eicm.cedbc058 set BASE_NAME = '藝', CAN_USE='Y' where same_name_1='藝' and same_name='埶';
update eicm.cedbc058 set BASE_NAME = '藝', CAN_USE='Y' where same_name_1='埶' and same_name='藝';
update eicm.cedbc058 set BASE_NAME = '蟲', CAN_USE='Y' where same_name_1='蟲' and same_name='虫';
update eicm.cedbc058 set BASE_NAME = '蟲', CAN_USE='Y' where same_name_1='虫' and same_name='蟲';
update eicm.cedbc058 set BASE_NAME = '腳', CAN_USE='Y' where same_name_1='脚' and same_name='腳';
update eicm.cedbc058 set BASE_NAME = '腳', CAN_USE='Y' where same_name_1='腳' and same_name='脚';
update eicm.cedbc058 set BASE_NAME = '觀', CAN_USE='Y' where same_name_1='觀' and same_name='覌';
update eicm.cedbc058 set BASE_NAME = '觀', CAN_USE='Y' where same_name_1='覌' and same_name='觀';
update eicm.cedbc058 set BASE_NAME = '際', CAN_USE='Y' where same_name_1='際' and same_name='际';
update eicm.cedbc058 set BASE_NAME = '際', CAN_USE='Y' where same_name_1='际' and same_name='際';
update eicm.cedbc058 set BASE_NAME = '鐵', CAN_USE='Y' where same_name_1='鐵' and same_name='鉄';
update eicm.cedbc058 set BASE_NAME = '鐵', CAN_USE='Y' where same_name_1='鉄' and same_name='鐵';
update eicm.cedbc058 set BASE_NAME = '迪', CAN_USE='Y' where same_name_1='迪' and same_name='廸';
update eicm.cedbc058 set BASE_NAME = '迪', CAN_USE='Y' where same_name_1='廸' and same_name='迪';
update eicm.cedbc058 set BASE_NAME = '種', CAN_USE='Y' where same_name_1='種' and same_name='种';
update eicm.cedbc058 set BASE_NAME = '種', CAN_USE='Y' where same_name_1='种' and same_name='種';
update eicm.cedbc058 set BASE_NAME = '萬', CAN_USE='Y' where same_name_1='万' and same_name='萬';
update eicm.cedbc058 set BASE_NAME = '萬', CAN_USE='Y' where same_name_1='萬' and same_name='万';
update eicm.cedbc058 set BASE_NAME = '奔', CAN_USE='Y' where same_name_1='犇' and same_name='奔';
update eicm.cedbc058 set BASE_NAME = '奔', CAN_USE='Y' where same_name_1='奔' and same_name='犇';
update eicm.cedbc058 set BASE_NAME = '灩', CAN_USE='Y' where same_name_1='灩' and same_name='灔';
update eicm.cedbc058 set BASE_NAME = '灩', CAN_USE='Y' where same_name_1='灔' and same_name='灩';
update eicm.cedbc058 set BASE_NAME = '比', CAN_USE='Y' where same_name_1='夶' and same_name='比';
update eicm.cedbc058 set BASE_NAME = '比', CAN_USE='Y' where same_name_1='比' and same_name='夶';
update eicm.cedbc058 set BASE_NAME = '並', CAN_USE='Y' where same_name_1='竝' and same_name='並';
update eicm.cedbc058 set BASE_NAME = '並', CAN_USE='Y' where same_name_1='並' and same_name='竝';
update eicm.cedbc058 set BASE_NAME = '厚', CAN_USE='Y' where same_name_1='垕' and same_name='厚';
update eicm.cedbc058 set BASE_NAME = '厚', CAN_USE='Y' where same_name_1='厚' and same_name='垕';
update eicm.cedbc058 set BASE_NAME = '暵', CAN_USE='Y' where same_name_1='熯' and same_name='暵';
update eicm.cedbc058 set BASE_NAME = '暵', CAN_USE='Y' where same_name_1='暵' and same_name='熯';
update eicm.cedbc058 set BASE_NAME = '秋', CAN_USE='Y' where same_name_1='' and same_name='秋';
update eicm.cedbc058 set BASE_NAME = '秋', CAN_USE='Y' where same_name_1='秋' and same_name='';
update eicm.cedbc058 set BASE_NAME = '睿', CAN_USE='Y' where same_name_1='睿' and same_name='叡';
update eicm.cedbc058 set BASE_NAME = '睿', CAN_USE='Y' where same_name_1='叡' and same_name='睿';
update eicm.cedbc058 set BASE_NAME = '佇', CAN_USE='Y' where same_name_1='佇' and same_name='竚';
update eicm.cedbc058 set BASE_NAME = '佇', CAN_USE='Y' where same_name_1='竚' and same_name='佇';
update eicm.cedbc058 set BASE_NAME = '恆', CAN_USE='Y' where same_name_1='恆' and same_name='恒';
update eicm.cedbc058 set BASE_NAME = '恆', CAN_USE='Y' where same_name_1='恒' and same_name='恆';
update eicm.cedbc058 set BASE_NAME = '飆', CAN_USE='Y' where same_name_1='飆' and same_name='飈';
update eicm.cedbc058 set BASE_NAME = '飆', CAN_USE='Y' where same_name_1='飈' and same_name='飆';
update eicm.cedbc058 set BASE_NAME = '窯', CAN_USE='Y' where same_name_1='窯' and same_name='窑';
update eicm.cedbc058 set BASE_NAME = '窯', CAN_USE='Y' where same_name_1='窑' and same_name='窯';
update eicm.cedbc058 set BASE_NAME = '衛', CAN_USE='Y' where same_name_1='衛' and same_name='衞';
update eicm.cedbc058 set BASE_NAME = '衛', CAN_USE='Y' where same_name_1='衞' and same_name='衛';
update eicm.cedbc058 set BASE_NAME = '眾', CAN_USE='Y' where same_name_1='衆' and same_name='眾';
update eicm.cedbc058 set BASE_NAME = '眾', CAN_USE='Y' where same_name_1='眾' and same_name='衆';
update eicm.cedbc058 set BASE_NAME = '昂', CAN_USE='Y' where same_name_1='昻' and same_name='昂';
update eicm.cedbc058 set BASE_NAME = '昂', CAN_USE='Y' where same_name_1='昂' and same_name='昻';
update eicm.cedbc058 set BASE_NAME = '鶴', CAN_USE='Y' where same_name_1='' and same_name='鶴';
update eicm.cedbc058 set BASE_NAME = '鶴', CAN_USE='Y' where same_name_1='鶴' and same_name='';
update eicm.cedbc058 set BASE_NAME = '免', CAN_USE='Y' where same_name_1='免' and same_name='兎';
update eicm.cedbc058 set BASE_NAME = '免', CAN_USE='Y' where same_name_1='兎' and same_name='免';
update eicm.cedbc058 set BASE_NAME = '爐', CAN_USE='Y' where same_name_1='爐' and same_name='炉';
update eicm.cedbc058 set BASE_NAME = '爐', CAN_USE='Y' where same_name_1='炉' and same_name='爐';
update eicm.cedbc058 set BASE_NAME = '濱', CAN_USE='Y' where same_name_1='浜' and same_name='濱';
update eicm.cedbc058 set BASE_NAME = '濱', CAN_USE='Y' where same_name_1='濱' and same_name='浜';
update eicm.cedbc058 set BASE_NAME = '貓', CAN_USE='Y' where same_name_1='貓' and same_name='猫';
update eicm.cedbc058 set BASE_NAME = '貓', CAN_USE='Y' where same_name_1='猫' and same_name='貓';
update eicm.cedbc058 set BASE_NAME = '匯', CAN_USE='Y' where same_name_1='滙' and same_name='匯';
update eicm.cedbc058 set BASE_NAME = '匯', CAN_USE='Y' where same_name_1='匯' and same_name='滙';
update eicm.cedbc058 set BASE_NAME = '群', CAN_USE='Y' where same_name_1='群' and same_name='羣';
update eicm.cedbc058 set BASE_NAME = '群', CAN_USE='Y' where same_name_1='羣' and same_name='群';
update eicm.cedbc058 set BASE_NAME = '繡', CAN_USE='Y' where same_name_1='繡' and same_name='綉';
update eicm.cedbc058 set BASE_NAME = '繡', CAN_USE='Y' where same_name_1='綉' and same_name='繡';
update eicm.cedbc058 set BASE_NAME = '麵', CAN_USE='Y' where same_name_1='麵' and same_name='麪';
update eicm.cedbc058 set BASE_NAME = '麵', CAN_USE='Y' where same_name_1='麪' and same_name='麵';
update eicm.cedbc058 set BASE_NAME = '穎', CAN_USE='Y' where same_name_1='穎' and same_name='頴';
update eicm.cedbc058 set BASE_NAME = '穎', CAN_USE='Y' where same_name_1='頴' and same_name='穎';
update eicm.cedbc058 set BASE_NAME = '霸', CAN_USE='Y' where same_name_1='霸' and same_name='覇';
update eicm.cedbc058 set BASE_NAME = '霸', CAN_USE='Y' where same_name_1='覇' and same_name='霸';
update eicm.cedbc058 set BASE_NAME = '凌', CAN_USE='Y' where same_name_1='凌' and same_name='淩';
update eicm.cedbc058 set BASE_NAME = '凌', CAN_USE='Y' where same_name_1='淩' and same_name='凌';
update eicm.cedbc058 set BASE_NAME = '市', CAN_USE='Y' where same_name_1='市' and same_name='巿';
update eicm.cedbc058 set BASE_NAME = '市', CAN_USE='Y' where same_name_1='巿' and same_name='市';
update eicm.cedbc058 set BASE_NAME = '朵', CAN_USE='Y' where same_name_1='朵' and same_name='朶';
update eicm.cedbc058 set BASE_NAME = '朵', CAN_USE='Y' where same_name_1='朶' and same_name='朵';
update eicm.cedbc058 set BASE_NAME = '百', CAN_USE='Y' where same_name_1='百' and same_name='佰';
update eicm.cedbc058 set BASE_NAME = '百', CAN_USE='Y' where same_name_1='佰' and same_name='百';
update eicm.cedbc058 set BASE_NAME = '峰', CAN_USE='Y' where same_name_1='峯' and same_name='峰';
update eicm.cedbc058 set BASE_NAME = '峰', CAN_USE='Y' where same_name_1='峰' and same_name='峯';
update eicm.cedbc058 set BASE_NAME = '逢', CAN_USE='Y' where same_name_1='逢' and same_name='夆';
update eicm.cedbc058 set BASE_NAME = '逢', CAN_USE='Y' where same_name_1='夆' and same_name='逢';
update eicm.cedbc058 set BASE_NAME = '絨', CAN_USE='Y' where same_name_1='絨' and same_name='羢';
update eicm.cedbc058 set BASE_NAME = '絨', CAN_USE='Y' where same_name_1='羢' and same_name='絨';
update eicm.cedbc058 set BASE_NAME = '爾', CAN_USE='Y' where same_name_1='爾' and same_name='尔';
update eicm.cedbc058 set BASE_NAME = '爾', CAN_USE='Y' where same_name_1='尔' and same_name='爾';
update eicm.cedbc058 set BASE_NAME = '雙', CAN_USE='Y' where same_name_1='雙' and same_name='双';
update eicm.cedbc058 set BASE_NAME = '雙', CAN_USE='Y' where same_name_1='双' and same_name='雙';
update eicm.cedbc058 set BASE_NAME = '藥', CAN_USE='Y' where same_name_1='藥' and same_name='葯';
update eicm.cedbc058 set BASE_NAME = '藥', CAN_USE='Y' where same_name_1='葯' and same_name='藥';
update eicm.cedbc058 set BASE_NAME = '禮', CAN_USE='Y' where same_name_1='禮' and same_name='礼';
update eicm.cedbc058 set BASE_NAME = '禮', CAN_USE='Y' where same_name_1='礼' and same_name='禮';
update eicm.cedbc058 set BASE_NAME = '姐', CAN_USE='Y' where same_name_1='姐' and same_name='姊';
update eicm.cedbc058 set BASE_NAME = '姐', CAN_USE='Y' where same_name_1='姊' and same_name='姐';
update eicm.cedbc058 set BASE_NAME = '泛', CAN_USE='Y' where same_name_1='泛' and same_name='汎';
update eicm.cedbc058 set BASE_NAME = '泛', CAN_USE='Y' where same_name_1='汎' and same_name='泛';
update eicm.cedbc058 set BASE_NAME = '優', CAN_USE='Y' where same_name_1='优' and same_name='優';
update eicm.cedbc058 set BASE_NAME = '優', CAN_USE='Y' where same_name_1='優' and same_name='优';
update eicm.cedbc058 set BASE_NAME = '機', CAN_USE='Y' where same_name_1='機' and same_name='机';
update eicm.cedbc058 set BASE_NAME = '機', CAN_USE='Y' where same_name_1='机' and same_name='機';
update eicm.cedbc058 set BASE_NAME = '亞', CAN_USE='Y' where same_name_1='亞' and same_name='亜';
update eicm.cedbc058 set BASE_NAME = '亞', CAN_USE='Y' where same_name_1='亜' and same_name='亞';
update eicm.cedbc058 set BASE_NAME = '漁', CAN_USE='Y' where same_name_1='魚' and same_name='漁';
update eicm.cedbc058 set BASE_NAME = '漁', CAN_USE='Y' where same_name_1='漁' and same_name='魚';
update eicm.cedbc058 set BASE_NAME = '強', CAN_USE='Y' where same_name_1='強' and same_name='强';
update eicm.cedbc058 set BASE_NAME = '強', CAN_USE='Y' where same_name_1='强' and same_name='強';
update eicm.cedbc058 set BASE_NAME = '掉', CAN_USE='Y' where same_name_1='' and same_name='掉';
update eicm.cedbc058 set BASE_NAME = '掉', CAN_USE='Y' where same_name_1='掉' and same_name='';
update eicm.cedbc058 set BASE_NAME = '華', CAN_USE='Y' where same_name_1='華' and same_name='崋';
update eicm.cedbc058 set BASE_NAME = '華', CAN_USE='Y' where same_name_1='崋' and same_name='華';
update eicm.cedbc058 set BASE_NAME = '靜', CAN_USE='Y' where same_name_1='竫' and same_name='靜';
update eicm.cedbc058 set BASE_NAME = '靜', CAN_USE='Y' where same_name_1='靜' and same_name='竫';
update eicm.cedbc058 set BASE_NAME = '是', CAN_USE='Y' where same_name_1='昰' and same_name='是';
update eicm.cedbc058 set BASE_NAME = '是', CAN_USE='Y' where same_name_1='是' and same_name='昰';
update eicm.cedbc058 set BASE_NAME = '韻', CAN_USE='Y' where same_name_1='韵' and same_name='韻';
update eicm.cedbc058 set BASE_NAME = '韻', CAN_USE='Y' where same_name_1='韻' and same_name='韵';
update eicm.cedbc058 set BASE_NAME = '堯', CAN_USE='Y' where same_name_1='垚' and same_name='堯';
update eicm.cedbc058 set BASE_NAME = '堯', CAN_USE='Y' where same_name_1='堯' and same_name='垚';
update eicm.cedbc058 set BASE_NAME = '集', CAN_USE='Y' where same_name_1='亼' and same_name='集';
update eicm.cedbc058 set BASE_NAME = '集', CAN_USE='Y' where same_name_1='集' and same_name='亼';
update eicm.cedbc058 set BASE_NAME = '菘', CAN_USE='Y' where same_name_1='菘' and same_name='蘴';
update eicm.cedbc058 set BASE_NAME = '菘', CAN_USE='Y' where same_name_1='蘴' and same_name='菘';
update eicm.cedbc058 set BASE_NAME = '泉', CAN_USE='Y' where same_name_1='湶' and same_name='泉';
update eicm.cedbc058 set BASE_NAME = '泉', CAN_USE='Y' where same_name_1='泉' and same_name='湶';
update eicm.cedbc058 set BASE_NAME = '崑', CAN_USE='Y' where same_name_1='崑' and same_name='崐';
update eicm.cedbc058 set BASE_NAME = '崑', CAN_USE='Y' where same_name_1='崐' and same_name='崑';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='艷' and same_name='艶';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='艷' and same_name='豔';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='艶' and same_name='艷';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='艶' and same_name='豔';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='豔' and same_name='艶';
update eicm.cedbc058 set BASE_NAME = '艷', CAN_USE='Y' where same_name_1='豔' and same_name='艷';
update eicm.cedbc058 set BASE_NAME = '粧', CAN_USE='Y' where same_name_1='粧' and same_name='妝';
update eicm.cedbc058 set BASE_NAME = '粧', CAN_USE='Y' where same_name_1='妝' and same_name='粧';
update eicm.cedbc058 set BASE_NAME = '豐', CAN_USE='Y' where same_name_1='豐' and same_name='丰';
update eicm.cedbc058 set BASE_NAME = '豐', CAN_USE='Y' where same_name_1='丰' and same_name='豐';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='協' and same_name='恊';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='協' and same_name='劦';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='恊' and same_name='協';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='恊' and same_name='劦';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='劦' and same_name='協';
update eicm.cedbc058 set BASE_NAME = '協', CAN_USE='Y' where same_name_1='劦' and same_name='恊';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='贏' and same_name='嬴';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='贏' and same_name='';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='嬴' and same_name='贏';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='嬴' and same_name='';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='' and same_name='贏';
update eicm.cedbc058 set BASE_NAME = '贏', CAN_USE='Y' where same_name_1='' and same_name='嬴';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='璿' and same_name='璇';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='璿' and same_name='琁';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='璇' and same_name='璿';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='璇' and same_name='琁';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='琁' and same_name='璿';
update eicm.cedbc058 set BASE_NAME = '璇', CAN_USE='Y' where same_name_1='琁' and same_name='璇';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='寳' and same_name='寶';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='寶' and same_name='寳';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='寶' and same_name='';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='寶' and same_name='宝';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='' and same_name='寶';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='' and same_name='宝';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='宝' and same_name='寶';
update eicm.cedbc058 set BASE_NAME = '寶', CAN_USE='Y' where same_name_1='宝' and same_name='';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='壹' and same_name='一';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='一' and same_name='壹';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='一' and same_name='壱';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='壱' and same_name='一';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='壹' and same_name='壱';
update eicm.cedbc058 set BASE_NAME = '一', CAN_USE='Y' where same_name_1='壱' and same_name='壹';
update eicm.cedbc058 set BASE_NAME = '二', CAN_USE='Y' where same_name_1='貳' and same_name='二';
update eicm.cedbc058 set BASE_NAME = '二', CAN_USE='Y' where same_name_1='二' and same_name='貳';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='叁' and same_name='三';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='叁' and same_name='';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='三' and same_name='叁';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='三' and same_name='';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='' and same_name='叁';
update eicm.cedbc058 set BASE_NAME = '三', CAN_USE='Y' where same_name_1='' and same_name='三';
update eicm.cedbc058 set BASE_NAME = '四', CAN_USE='Y' where same_name_1='肆' and same_name='四';
update eicm.cedbc058 set BASE_NAME = '四', CAN_USE='Y' where same_name_1='四' and same_name='肆';
update eicm.cedbc058 set BASE_NAME = '五', CAN_USE='Y' where same_name_1='伍' and same_name='五';
update eicm.cedbc058 set BASE_NAME = '五', CAN_USE='Y' where same_name_1='五' and same_name='伍';
update eicm.cedbc058 set BASE_NAME = '六', CAN_USE='Y' where same_name_1='陸' and same_name='六';
update eicm.cedbc058 set BASE_NAME = '六', CAN_USE='Y' where same_name_1='六' and same_name='陸';
update eicm.cedbc058 set BASE_NAME = '七', CAN_USE='Y' where same_name_1='柒' and same_name='七';
update eicm.cedbc058 set BASE_NAME = '七', CAN_USE='Y' where same_name_1='七' and same_name='柒';
update eicm.cedbc058 set BASE_NAME = '八', CAN_USE='Y' where same_name_1='捌' and same_name='八';
update eicm.cedbc058 set BASE_NAME = '八', CAN_USE='Y' where same_name_1='八' and same_name='捌';
update eicm.cedbc058 set BASE_NAME = '九', CAN_USE='Y' where same_name_1='玖' and same_name='九';
update eicm.cedbc058 set BASE_NAME = '九', CAN_USE='Y' where same_name_1='九' and same_name='玖';
update eicm.cedbc058 set BASE_NAME = '十', CAN_USE='Y' where same_name_1='拾' and same_name='十';
update eicm.cedbc058 set BASE_NAME = '十', CAN_USE='Y' where same_name_1='十' and same_name='拾';
update eicm.cedbc058 set BASE_NAME = '奇', CAN_USE='Y' where same_name_1='奇' and same_name='竒';
update eicm.cedbc058 set BASE_NAME = '奇', CAN_USE='Y' where same_name_1='竒' and same_name='奇';
update eicm.cedbc058 set BASE_NAME = '灣', CAN_USE='Y' where same_name_1='湾' and same_name='灣';
update eicm.cedbc058 set BASE_NAME = '灣', CAN_USE='Y' where same_name_1='灣' and same_name='湾';
update eicm.cedbc058 set BASE_NAME = '數', CAN_USE='Y' where same_name_1='數' and same_name='数';
update eicm.cedbc058 set BASE_NAME = '數', CAN_USE='Y' where same_name_1='数' and same_name='數';
update eicm.cedbc058 set BASE_NAME = '橋', CAN_USE='Y' where same_name_1='橋' and same_name='桥';
update eicm.cedbc058 set BASE_NAME = '橋', CAN_USE='Y' where same_name_1='桥' and same_name='橋';
update eicm.cedbc058 set BASE_NAME = '徠', CAN_USE='Y' where same_name_1='倈' and same_name='徠';
update eicm.cedbc058 set BASE_NAME = '徠', CAN_USE='Y' where same_name_1='徠' and same_name='倈';
update eicm.cedbc058 set BASE_NAME = '麗', CAN_USE='Y' where same_name_1='丽' and same_name='麗';
update eicm.cedbc058 set BASE_NAME = '麗', CAN_USE='Y' where same_name_1='麗' and same_name='丽';
update eicm.cedbc058 set BASE_NAME = '麗', CAN_USE='Y' where same_name_1='' and same_name='麗';
update eicm.cedbc058 set BASE_NAME = '麗', CAN_USE='Y' where same_name_1='麗' and same_name='';
update eicm.cedbc058 set BASE_NAME = '體', CAN_USE='Y' where same_name_1='体' and same_name='體';
update eicm.cedbc058 set BASE_NAME = '體', CAN_USE='Y' where same_name_1='體' and same_name='体';
update eicm.cedbc058 set BASE_NAME = '翊', CAN_USE='Y' where same_name_1='翊' and same_name='翌';
update eicm.cedbc058 set BASE_NAME = '翊', CAN_USE='Y' where same_name_1='翌' and same_name='翊';
update eicm.cedbc058 set BASE_NAME = '畫', CAN_USE='Y' where same_name_1='畫' and same_name='画';
update eicm.cedbc058 set BASE_NAME = '畫', CAN_USE='Y' where same_name_1='画' and same_name='畫';
update eicm.cedbc058 set BASE_NAME = '果', CAN_USE='Y' where same_name_1='果' and same_name='菓';
update eicm.cedbc058 set BASE_NAME = '果', CAN_USE='Y' where same_name_1='菓' and same_name='果';
update eicm.cedbc058 set BASE_NAME = '醫', CAN_USE='Y' where same_name_1='醫' and same_name='医';
update eicm.cedbc058 set BASE_NAME = '醫', CAN_USE='Y' where same_name_1='医' and same_name='醫';
update eicm.cedbc058 set BASE_NAME = '龍', CAN_USE='Y' where same_name_1='竜' and same_name='龍';
update eicm.cedbc058 set BASE_NAME = '龍', CAN_USE='Y' where same_name_1='龍' and same_name='竜';
update eicm.cedbc058 set BASE_NAME = '富', CAN_USE='Y' where same_name_1='冨' and same_name='富';
update eicm.cedbc058 set BASE_NAME = '富', CAN_USE='Y' where same_name_1='富' and same_name='冨';
update eicm.cedbc058 set BASE_NAME = '樂', CAN_USE='Y' where same_name_1='楽' and same_name='樂';
update eicm.cedbc058 set BASE_NAME = '樂', CAN_USE='Y' where same_name_1='樂' and same_name='楽';
update eicm.cedbc058 set BASE_NAME = '炮', CAN_USE='Y' where same_name_1='炮' and same_name='砲';
update eicm.cedbc058 set BASE_NAME = '炮', CAN_USE='Y' where same_name_1='砲' and same_name='炮';
update eicm.cedbc058 set BASE_NAME = '陽', CAN_USE='Y' where same_name_1='昜' and same_name='陽';
update eicm.cedbc058 set BASE_NAME = '陽', CAN_USE='Y' where same_name_1='陽' and same_name='昜';
update eicm.cedbc058 set BASE_NAME = '脈', CAN_USE='Y' where same_name_1='脈' and same_name='脉';
update eicm.cedbc058 set BASE_NAME = '脈', CAN_USE='Y' where same_name_1='脉' and same_name='脈';
update eicm.cedbc058 set BASE_NAME = '潔', CAN_USE='Y' where same_name_1='潔' and same_name='絜';
update eicm.cedbc058 set BASE_NAME = '潔', CAN_USE='Y' where same_name_1='絜' and same_name='潔';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勛' and same_name='勳';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勳' and same_name='勛';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勲' and same_name='勳';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勳' and same_name='勲';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勲' and same_name='勛';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勛' and same_name='勲';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勲' and same_name='勛';
update eicm.cedbc058 set BASE_NAME = '勳', CAN_USE='Y' where same_name_1='勛' and same_name='勲';
update eicm.cedbc058 set BASE_NAME = '茲', CAN_USE='Y' where same_name_1='兹' and same_name='玆';
update eicm.cedbc058 set BASE_NAME = '茲', CAN_USE='Y' where same_name_1='玆' and same_name='兹';
update eicm.cedbc058 set BASE_NAME = '茲', CAN_USE='Y' where same_name_1='玆' and same_name='茲';
update eicm.cedbc058 set BASE_NAME = '茲', CAN_USE='Y' where same_name_1='茲' and same_name='玆';
update eicm.cedbc058 set BASE_NAME = '曉', CAN_USE='Y' where same_name_1='暁' and same_name='曉';
update eicm.cedbc058 set BASE_NAME = '曉', CAN_USE='Y' where same_name_1='曉' and same_name='暁';
update eicm.cedbc058 set BASE_NAME = '哲', CAN_USE='Y' where same_name_1='喆' and same_name='哲';
update eicm.cedbc058 set BASE_NAME = '哲', CAN_USE='Y' where same_name_1='哲' and same_name='喆';
update eicm.cedbc058 set BASE_NAME = '簾', CAN_USE='Y' where same_name_1='帘' and same_name='簾';
update eicm.cedbc058 set BASE_NAME = '簾', CAN_USE='Y' where same_name_1='簾' and same_name='帘';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啟' and same_name='啟';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啟' and same_name='啟';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啟' and same_name='启';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='启' and same_name='啟';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啓' and same_name='啟';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啟' and same_name='啓';
update eicm.cedbc058 set BASE_NAME = '啟', CAN_USE='Y' where same_name_1='啟' and same_name='啓';
update eicm.cedbc058 set BASE_NAME = '廚', CAN_USE='Y' where same_name_1='厨' and same_name='廚';
update eicm.cedbc058 set BASE_NAME = '廚', CAN_USE='Y' where same_name_1='廚' and same_name='厨';
update eicm.cedbc058 set BASE_NAME = '廣', CAN_USE='Y' where same_name_1='広' and same_name='廣';
update eicm.cedbc058 set BASE_NAME = '廣', CAN_USE='Y' where same_name_1='廣' and same_name='広';
update eicm.cedbc058 set BASE_NAME = '萍', CAN_USE='Y' where same_name_1='萍' and same_name='苹';
update eicm.cedbc058 set BASE_NAME = '萍', CAN_USE='Y' where same_name_1='苹' and same_name='萍';
update eicm.cedbc058 set BASE_NAME = '為', CAN_USE='Y' where same_name_1='為' and same_name='爲';
update eicm.cedbc058 set BASE_NAME = '為', CAN_USE='Y' where same_name_1='爲' and same_name='為';
update eicm.cedbc058 set BASE_NAME = '關', CAN_USE='Y' where same_name_1='關' and same_name='関';
update eicm.cedbc058 set BASE_NAME = '關', CAN_USE='Y' where same_name_1='関' and same_name='關';
update eicm.cedbc058 set BASE_NAME = '製', CAN_USE='Y' where same_name_1='製' and same_name='制';
update eicm.cedbc058 set BASE_NAME = '製', CAN_USE='Y' where same_name_1='制' and same_name='製';
update eicm.cedbc058 set BASE_NAME = '背', CAN_USE='Y' where same_name_1='揹' and same_name='背';
update eicm.cedbc058 set BASE_NAME = '背', CAN_USE='Y' where same_name_1='背' and same_name='揹';
update eicm.cedbc058 set BASE_NAME = '穩', CAN_USE='Y' where same_name_1='穩' and same_name='穏';
update eicm.cedbc058 set BASE_NAME = '穩', CAN_USE='Y' where same_name_1='穏' and same_name='穩';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='真' and same_name='';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='' and same_name='真';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='真' and same_name='眞';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='眞' and same_name='真';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='眞' and same_name='';
update eicm.cedbc058 set BASE_NAME = '真', CAN_USE='Y' where same_name_1='' and same_name='眞';
update eicm.cedbc058 set BASE_NAME = '獻', CAN_USE='Y' where same_name_1='獻' and same_name='献';
update eicm.cedbc058 set BASE_NAME = '獻', CAN_USE='Y' where same_name_1='献' and same_name='獻';
update eicm.cedbc058 set BASE_NAME = '皓', CAN_USE='Y' where same_name_1='皓' and same_name='晧';
update eicm.cedbc058 set BASE_NAME = '皓', CAN_USE='Y' where same_name_1='晧' and same_name='皓';
update eicm.cedbc058 set BASE_NAME = '彥', CAN_USE='Y' where same_name_1='彥' and same_name='彦';
update eicm.cedbc058 set BASE_NAME = '彥', CAN_USE='Y' where same_name_1='彦' and same_name='彥';
update eicm.cedbc058 set BASE_NAME = '諗', CAN_USE='Y' where same_name_1='讅' and same_name='諗';
update eicm.cedbc058 set BASE_NAME = '諗', CAN_USE='Y' where same_name_1='諗' and same_name='讅';
update eicm.cedbc058 set BASE_NAME = '鳳', CAN_USE='Y' where same_name_1='鳳' and same_name='鳯';
update eicm.cedbc058 set BASE_NAME = '鳳', CAN_USE='Y' where same_name_1='鳯' and same_name='鳳';
update eicm.cedbc058 set BASE_NAME = '寧', CAN_USE='Y' where same_name_1='寧' and same_name='甯';
update eicm.cedbc058 set BASE_NAME = '寧', CAN_USE='Y' where same_name_1='甯' and same_name='寧';
update eicm.cedbc058 set BASE_NAME = '莊', CAN_USE='Y' where same_name_1='庄' and same_name='莊';
update eicm.cedbc058 set BASE_NAME = '莊', CAN_USE='Y' where same_name_1='莊' and same_name='庄';
update eicm.cedbc058 set BASE_NAME = '繪', CAN_USE='Y' where same_name_1='繪' and same_name='絵';
update eicm.cedbc058 set BASE_NAME = '繪', CAN_USE='Y' where same_name_1='絵' and same_name='繪';
update eicm.cedbc058 set BASE_NAME = '喜', CAN_USE='Y' where same_name_1='囍' and same_name='喜';
update eicm.cedbc058 set BASE_NAME = '喜', CAN_USE='Y' where same_name_1='喜' and same_name='囍';
update eicm.cedbc058 set BASE_NAME = '樓', CAN_USE='Y' where same_name_1='樓' and same_name='楼';
update eicm.cedbc058 set BASE_NAME = '樓', CAN_USE='Y' where same_name_1='楼' and same_name='樓';
update eicm.cedbc058 set BASE_NAME = '翱', CAN_USE='Y' where same_name_1='翶' and same_name='翱';
update eicm.cedbc058 set BASE_NAME = '翱', CAN_USE='Y' where same_name_1='翱' and same_name='翶';
update eicm.cedbc058 set BASE_NAME = '學', CAN_USE='Y' where same_name_1='學' and same_name='学';
update eicm.cedbc058 set BASE_NAME = '學', CAN_USE='Y' where same_name_1='学' and same_name='學';
update eicm.cedbc058 set BASE_NAME = '二', CAN_USE='Y' where same_name_1='貳' and same_name='貮';
update eicm.cedbc058 set BASE_NAME = '二', CAN_USE='Y' where same_name_1='貮' and same_name='貳';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='叙' and same_name='敍';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='敍' and same_name='叙';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='敘' and same_name='叙';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='叙' and same_name='敘';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='敘' and same_name='敍';
update eicm.cedbc058 set BASE_NAME = '敘', CAN_USE='Y' where same_name_1='敍' and same_name='敘';
update eicm.cedbc058 set BASE_NAME = '國', CAN_USE='Y' where same_name_1='國' and same_name='囯';
update eicm.cedbc058 set BASE_NAME = '國', CAN_USE='Y' where same_name_1='囯' and same_name='國';
update eicm.cedbc058 set BASE_NAME = '耘', CAN_USE='Y' where same_name_1='耘' and same_name='';
update eicm.cedbc058 set BASE_NAME = '耘', CAN_USE='Y' where same_name_1='' and same_name='耘';
update eicm.cedbc058 set BASE_NAME = '鑽', CAN_USE='Y' where same_name_1='鑚' and same_name='鑽';
update eicm.cedbc058 set BASE_NAME = '鑽', CAN_USE='Y' where same_name_1='鑽' and same_name='鑚';
update eicm.cedbc058 set BASE_NAME = '坤', CAN_USE='Y' where same_name_1='堃' and same_name='坤';
update eicm.cedbc058 set BASE_NAME = '坤', CAN_USE='Y' where same_name_1='堃' and same_name='坤';
update eicm.cedbc058 set BASE_NAME = '坤', CAN_USE='Y' where same_name_1='坤' and same_name='堃';
update eicm.cedbc058 set BASE_NAME = '夢', CAN_USE='Y' where same_name_1='夢' and same_name='梦';
update eicm.cedbc058 set BASE_NAME = '夢', CAN_USE='Y' where same_name_1='梦' and same_name='夢';
update eicm.cedbc058 set BASE_NAME = '膚', CAN_USE='Y' where same_name_1='膚' and same_name='肤';
update eicm.cedbc058 set BASE_NAME = '膚', CAN_USE='Y' where same_name_1='肤' and same_name='膚';
update eicm.cedbc058 set BASE_NAME = '渝', CAN_USE='Y' where same_name_1='' and same_name='渝';
update eicm.cedbc058 set BASE_NAME = '渝', CAN_USE='Y' where same_name_1='渝' and same_name='';
update eicm.cedbc058 set BASE_NAME = '壽', CAN_USE='Y' where same_name_1='壽' and same_name='寿';
update eicm.cedbc058 set BASE_NAME = '壽', CAN_USE='Y' where same_name_1='寿' and same_name='壽';
update eicm.cedbc058 set BASE_NAME = '溫', CAN_USE='Y' where same_name_1='溫' and same_name='温';
update eicm.cedbc058 set BASE_NAME = '溫', CAN_USE='Y' where same_name_1='温' and same_name='溫';
update eicm.cedbc058 set BASE_NAME = '溫', CAN_USE='Y' where same_name_1='温' and same_name='溫';
update eicm.cedbc058 set BASE_NAME = '溫', CAN_USE='Y' where same_name_1='溫' and same_name='温';
