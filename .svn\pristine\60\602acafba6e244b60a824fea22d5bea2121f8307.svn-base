package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 公司名稱預查申請異動記錄檔(CEDB1007)
 *
 */
public class Cedb1007 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 序號 */
	private String seqNo;
	/** 公司名稱 */
	private String companyName;
	/** 審查結果 */
	private String approveResult;
	/** 異動日期 */
	private String updateDate;
	/** 異動時間 */
	private String updateTime;
	/** 異動人員識別碼 */
	private String updateIdNo;
	/** 同名公司註記 */
	private String remark;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getSeqNo() {return seqNo;}
	public void setSeqNo(String seqNo) {this.seqNo = seqNo;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String approveResult) {this.approveResult = approveResult;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getUpdateIdNo() {return updateIdNo;}
	public void setUpdateIdNo(String updateIdNo) {this.updateIdNo = updateIdNo;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}

}