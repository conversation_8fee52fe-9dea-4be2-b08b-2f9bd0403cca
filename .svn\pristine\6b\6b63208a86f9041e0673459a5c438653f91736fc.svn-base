package com.kangdainfo.tcfi.model.eicm.dao;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.SystemNews;
import com.kangdainfo.util.lang.CommonStringUtils;

public class SystemNewsDao extends BaseDaoJdbc implements RowMapper<SystemNews> {

	private static final String SQL_findById = "SELECT * FROM SYSTEM_NEWS WHERE ID = ?";
	public SystemNews findById(Integer id) {
		//check pk
		if(null==id) return null;
		SQLJob sqljob = new SQLJob(SQL_findById);
		sqljob.addParameter(id);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (SystemNews) getJdbcTemplate().queryForObject(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public Integer countByCondition(String id, String subject, String content, String startDate, String endDate) {
		SQLJob sqljob = new SQLJob("SELECT COUNT(1) AS C FROM SYSTEM_NEWS");
		if(CommonStringUtils.isNotBlank(id)) {
			sqljob.appendSQLCondition("ID = ?");
			sqljob.addParameter(id);
		}
		if(CommonStringUtils.isNotBlank(subject)) {
			sqljob.appendSQLCondition("SUBJECT LIKE ?");
			sqljob.addLikeParameter(subject);
		}
		if(CommonStringUtils.isNotBlank(content)) {
			sqljob.appendSQLCondition("CONTENT LIKE ?");
			sqljob.addLikeParameter(content);
		}
		if(CommonStringUtils.isNotBlank(startDate)) {
			sqljob.appendSQLCondition("START_DATE >= ?");
			sqljob.addParameter(startDate);
		}
		if(CommonStringUtils.isNotBlank(endDate)) {
			sqljob.appendSQLCondition("END_DATE <= ?");
			sqljob.addParameter(endDate);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		Map<String,Object> map = getJdbcTemplate().queryForMap(sqljob.getSQL(), sqljob.getParametersArray());
		if(null!=map)
			return ((BigDecimal)map.get("C")).intValue();
		return 0;
	}

	public List<SystemNews> findByCondition(String id, String subject, String content, String startDate, String endDate) {
		SQLJob sqljob = new SQLJob("SELECT * FROM SYSTEM_NEWS");
		if(CommonStringUtils.isNotBlank(id)) {
			sqljob.appendSQLCondition("ID = ?");
			sqljob.addParameter(id);
		}
		if(CommonStringUtils.isNotBlank(subject)) {
			sqljob.appendSQLCondition("SUBJECT LIKE ?");
			sqljob.addLikeParameter(subject);
		}
		if(CommonStringUtils.isNotBlank(content)) {
			sqljob.appendSQLCondition("CONTENT LIKE ?");
			sqljob.addLikeParameter(content);
		}
		if(CommonStringUtils.isNotBlank(startDate)) {
			sqljob.appendSQLCondition("START_DATE >= ?");
			sqljob.addParameter(startDate);
		}
		if(CommonStringUtils.isNotBlank(endDate)) {
			sqljob.appendSQLCondition("END_DATE <= ?");
			sqljob.addParameter(endDate);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<SystemNews>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public List<SystemNews> findNews() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT * FROM SYSTEM_NEWS");
		sqljob.appendSQL("WHERE ? BETWEEN START_DATE AND END_DATE");
		sqljob.appendSQL("AND ENABLE = 'Y'");
		sqljob.appendSQL("ORDER BY NVL(IS_IMPORTANT,'N') DESC, START_DATE");
		sqljob.addParameter(Datetime.getYYYMMDD());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<SystemNews>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public SystemNews insert(SystemNews bo) {
		//check pk
		if(null==bo) return null;
		//insert
		SQLJob sqljob = new SQLJob("INSERT INTO SYSTEM_NEWS (");
		sqljob.appendSQL(" SUBJECT");
		sqljob.appendSQL(",CONTENT");
		sqljob.appendSQL(",START_DATE");
		sqljob.appendSQL(",END_DATE");
		sqljob.appendSQL(",IS_IMPORTANT");
		sqljob.appendSQL(",ENABLE");
		sqljob.appendSQL(",MOD_ID_NO");
		sqljob.appendSQL(",MOD_DATE");
		sqljob.appendSQL(",MOD_TIME");
		sqljob.appendSQL(") VALUES (?,?,?,?,?,?,?,?,?)");
		sqljob.addParameter(bo.getSubject());
		sqljob.addParameter(bo.getContent());
		sqljob.addParameter(bo.getStartDate());
		sqljob.addParameter(bo.getEndDate());
		sqljob.addParameter(bo.getIsImportant());
		sqljob.addParameter(bo.getEnable());
		sqljob.addParameter(bo.getModIdNo());
		sqljob.addParameter(bo.getModDate());
		sqljob.addParameter(bo.getModTime());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(),
			new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR
			} );
		return (SystemNews) getJdbcTemplate().queryForObject("SELECT * FROM SYSTEM_NEWS WHERE ID = (SELECT MAX(ID) FROM SYSTEM_NEWS)", this);
	}

	public SystemNews update(SystemNews bo) {
		//check pk
		if(null==bo) return null;
		if(null==bo.getId()) return null;
		//check exist
		SystemNews t = findById(bo.getId());
		if(null==t) return null;
		else {
			//update
			SQLJob sqljob = new SQLJob("UPDATE SYSTEM_NEWS SET");
			sqljob.appendSQL(" SUBJECT=?");
			sqljob.appendSQL(",CONTENT=?");
			sqljob.appendSQL(",START_DATE=?");
			sqljob.appendSQL(",END_DATE=?");
			sqljob.appendSQL(",IS_IMPORTANT=?");
			sqljob.appendSQL(",ENABLE=?");
			sqljob.appendSQL(",MOD_ID_NO=?");
			sqljob.appendSQL(",MOD_DATE=?");
			sqljob.appendSQL(",MOD_TIME=?");
			sqljob.appendSQL("WHERE ID=?");
			sqljob.addParameter(bo.getSubject());
			sqljob.addParameter(bo.getContent());
			sqljob.addParameter(bo.getStartDate());
			sqljob.addParameter(bo.getEndDate());
			sqljob.addParameter(bo.getIsImportant());
			sqljob.addParameter(bo.getEnable());
			sqljob.addParameter(bo.getModIdNo());
			sqljob.addParameter(bo.getModDate());
			sqljob.addParameter(bo.getModTime());
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(),
				new int[]{
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.VARCHAR,
					java.sql.Types.NUMERIC
				});
			return findById(bo.getId());
		}
	}
	
	public void delete(SystemNews bo) {
		//check pk
		if(null!=bo && null!=bo.getId() ) {
			//delete - 只設定停用, 不刪除
			SQLJob sqljob = new SQLJob("DELETE FROM SYSTEM_NEWS");
			sqljob.appendSQL("WHERE ID = ?");
			sqljob.addParameter(bo.getId());
			if(logger.isDebugEnabled()) logger.debug(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), new int[]{java.sql.Types.NUMERIC});
		}
	}

	public SystemNews mapRow(ResultSet rs, int idx) throws SQLException {
		SystemNews obj = null;
		if(null!=rs) {
			obj = new SystemNews();
			obj.setId(rs.getInt("ID"));
			obj.setSubject(Common.get(rs.getString("SUBJECT")));
			obj.setContent(Common.get(rs.getString("CONTENT")));
			obj.setStartDate(Common.get(rs.getString("START_DATE")));
			obj.setEndDate(Common.get(rs.getString("END_DATE")));
			obj.setIsImportant(Common.get(rs.getString("IS_IMPORTANT")));
			obj.setEnable(Common.get(rs.getString("ENABLE")));
			obj.setModIdNo(Common.get(rs.getString("MOD_ID_NO")));
			obj.setModDate(Common.get(rs.getString("MOD_DATE")));
			obj.setModTime(Common.get(rs.getString("MOD_TIME")));
		}
		return obj;
	}

}