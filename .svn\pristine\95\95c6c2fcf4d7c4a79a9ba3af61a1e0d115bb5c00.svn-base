package com.kangdainfo.tcfi.model.lms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.LmsdCodemapping;

/**
 * 有限合夥各類代碼對照檔Dao
 * <AUTHOR>
 * 113/04/16
 */
public class LmsdCodemappingDao extends BaseDaoJdbc implements RowMapper<LmsdCodemapping> {
	
	private static final String SQL_findByKind = "SELECT * FROM LMS.LMSD_CODEMAPPING WHERE ENABLE = 'Y' AND KIND = ? ";
	
	public List<LmsdCodemapping> findByKind(String kind) {
		SQLJob sqljob = new SQLJob(SQL_findByKind);
		sqljob.addParameter(kind);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<LmsdCodemapping>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	@Override
	public LmsdCodemapping mapRow(ResultSet rs, int idx) throws SQLException {
		LmsdCodemapping obj = null;
		if(null!=rs) {
			obj = new LmsdCodemapping();
			obj.setAgencyCode(rs.getString("AGENCYCODE"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setKind(rs.getString("KIND"));
			obj.setCode(rs.getString("CODE"));
			obj.setDescription(rs.getString("DESCRIPTION"));
			obj.setRemark(rs.getString("REMARK"));
			obj.setKeyinStaffCode(rs.getString("KEYIN_STAFF_CODE"));
			obj.setKeyinStaffName(rs.getString("KEYIN_STAFF_NAME"));
			obj.setKeyinDate(rs.getString("KEYIN_DATE"));
			obj.setEnable(rs.getString("ENABLE"));
		}
		return obj;
	}
}
