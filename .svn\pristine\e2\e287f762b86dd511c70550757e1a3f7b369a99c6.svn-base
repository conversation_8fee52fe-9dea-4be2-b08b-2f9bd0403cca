package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 功能選單檔(FUNCTION_MENU)
 *
 */
public class FunctionMenu extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
	/** 上層選單鍵值 */
	private Integer pid;
	/** 功能代碼 */
	private String code;
	/** 功能名稱 */
	private String name;
	/** 功能路徑 */
	private String url;
	/** 功能標題 */
	private String title;
	/** 連結開啟目標 */
	private String target;
	/** 排序數字 */
	private Integer sorted;
	/** 是否啟用(Y:啟動,N:停用 ) */
	private String enable;

	/** 主鍵值 */
	public Integer getId() {	return id;	}
	/** 主鍵值 */
	public void setId(Integer id) {	this.id = id;	}

	/** 上層選單鍵值 */
	public Integer getPid() {	return pid;	}
	/** 上層選單鍵值 */
	public void setPid(Integer pid) {	this.pid = pid;	}

	/** 功能代碼 */
	public String getCode() {	return code;	}
	/** 功能代碼 */
	public void setCode(String code) {	this.code = code;	}

	/** 功能名稱 */
	public String getName() {	return name;	}
	/** 功能名稱 */
	public void setName(String name) {	this.name = name;	}

	/** 功能路徑 */
	public String getUrl() {	return url;	}
	/** 功能路徑 */
	public void setUrl(String url) {	this.url = url;	}

	/** 功能標題 */
	public String getTitle() {	return title;	}
	/** 功能標題 */
	public void setTitle(String title) {	this.title = title;	}

	/** 連結開啟目標 */
	public String getTarget() {	return target;	}
	/** 連結開啟目標 */
	public void setTarget(String target) {	this.target = target;	}

	/** 排序數字 */
	public Integer getSorted() {	return sorted;	}
	/** 排序數字 */
	public void setSorted(Integer sorted) {	this.sorted = sorted;	}

	/** 是否啟用(Y:啟動,N:停用 ) */
	public String getEnable() {return enable;}
	/** 是否啟用(Y:啟動,N:停用 ) */
	public void setEnable(String enable) {this.enable = enable;}

}