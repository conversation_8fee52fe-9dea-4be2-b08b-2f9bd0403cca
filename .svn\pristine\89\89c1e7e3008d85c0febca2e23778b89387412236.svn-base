package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.IndexLogH;

public class IndexLogHDao extends BaseDaoJdbc implements RowMapper<IndexLogH>{

	public void backupIndexLog() {
		SQLJob ins = new SQLJob("");
		ins.appendSQL(" INSERT INTO INDEX_LOG_H (");
		ins.appendSQL(" ID,WS_ID,PARAM1,PARAM2,PARAM3,EXECUTE_DATE,CREATE_DATE,CREATE_USER,START_TIME,FINISH_TIME,STATUS,REMARK");
		ins.appendSQL(" )");
		ins.appendSQL(" SELECT ");
		ins.appendSQL(" ID,WS_ID,PARAM1,PARAM2,PARAM3,EXECUTE_DATE,CREATE_DATE,CREATE_USER,START_TIME,FINISH_TIME,STATUS,REMARK");
		ins.appendSQL(" FROM INDEX_LOG");
		ins.appendSQL(" WHERE FINISH_TIME < to_char(to_char(trunc(sysdate-7),'yyyyMMddHH24MIss')-19110000000000)");
		ins.appendSQL(" OR (FINISH_TIME IS NULL AND STATUS='1' AND START_TIME < to_char(to_char(trunc(sysdate-7),'yyyyMMddHH24MIss')-19110000000000) )");
		if(logger.isDebugEnabled()) logger.debug(ins);
		getJdbcTemplate().update(ins.getSQL());
		
		SQLJob del = new SQLJob("");
		del.appendSQL(" DELETE FROM INDEX_LOG ");
		del.appendSQL(" WHERE FINISH_TIME < to_char(to_char(trunc(sysdate-7),'yyyyMMddHH24MIss')-19110000000000)");
		ins.appendSQL(" OR (FINISH_TIME IS NULL AND STATUS='1' AND START_TIME < to_char(to_char(trunc(sysdate-7),'yyyyMMddHH24MIss')-19110000000000) )");
		if(logger.isDebugEnabled()) logger.debug(del);
		getJdbcTemplate().update(del.getSQL());
	}

	@Override
	public IndexLogH mapRow(ResultSet rs, int arg1) throws SQLException {
		IndexLogH obj = null;
		if(null!=rs) {
			obj = new IndexLogH();
			obj.setId(rs.getLong("ID"));
			obj.setWsId(rs.getString("WS_ID"));
			obj.setParam1(rs.getString("PARAM1"));
			obj.setParam2(rs.getString("PARAM2"));
			obj.setParam3(rs.getString("PARAM3"));
			obj.setExecuteDate(rs.getString("EXECUTE_DATE"));
			obj.setCreateDate(rs.getString("CREATE_DATE"));
			obj.setCreateUser(rs.getString("CREATE_USER"));
			obj.setStartTime(rs.getString("START_TIME"));
			obj.setFinishTime(rs.getString("FINISH_TIME"));
			obj.setStatus(rs.getString("STATUS"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}