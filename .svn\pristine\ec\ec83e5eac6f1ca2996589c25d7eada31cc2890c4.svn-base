package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiptNoSetupDAO;
import com.kangdainfo.tcfi.service.Pre8018Service;


public class Pre8018ServiceImpl implements Pre8018Service {
	ReceiptNoSetupDAO receiptNoSetupDAO;
	
	public ReceiptNoSetupDAO getReceiptNoSetupDAO() {
		return receiptNoSetupDAO;
	} 
	public void setReceiptNoSetupDAO( ReceiptNoSetupDAO dao) {
		this.receiptNoSetupDAO = dao;
	}
	
	public List<ReceiptNoSetup> selectAll() {
		return receiptNoSetupDAO.selectAll();
	}
	
	public ReceiptNoSetup selectByReceiptType(String receiptType) {
		return receiptNoSetupDAO.selectByReceiptType(receiptType);
	}
	
	public void doInsert( ReceiptNoSetup vo  ) {
		receiptNoSetupDAO.insert(vo);
	}
	
	public void doUpdate( ReceiptNoSetup vo ) {
		receiptNoSetupDAO.update(vo);
	}
	
	public void doDelete( String receiptType ) {
		receiptNoSetupDAO.delete(receiptType);
	}
}