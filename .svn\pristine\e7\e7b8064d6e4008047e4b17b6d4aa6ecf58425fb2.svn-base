package com.kangdainfo.tcfi.loader;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc058Dao;

public class Cedbc058CodeLoader extends BaseLoader {
	private static final String CACHE_NAME = "CACHE_NAME_CEDBC058";
	//singleton
	private static Cedbc058CodeLoader instance;
	public Cedbc058CodeLoader() {
		if (Cedbc058CodeLoader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ Cedbc058CodeLoader.instance);
		}
		Cedbc058CodeLoader.instance = this;
	}
	public static Cedbc058CodeLoader getInstance() {return instance;}

	/**
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Cedbc058> getCedbc058Codes() {
		if(getServletContext().getAttribute(CACHE_NAME) == null)
			reload();
		return (List<Cedbc058>) getServletContext().getAttribute(CACHE_NAME);
	}

	public Cedbc058 getCedbc058ObjByBaseName(String word) {
		if(!"".equals(Common.get(word))) {
			List<Cedbc058> datas = getCedbc058Codes();
			for(Cedbc058 data : datas) {
				if( word.equals(data.getSameName1()) || word.equals(data.getSameName()) )
					return data;
			}
		}
		return null;
	}

	public List<Cedbc058> getCedbc058ObjsByBaseName(String word) {
		List<Cedbc058> results = new ArrayList<Cedbc058>();
		if(!"".equals(Common.get(word))) {
			List<Cedbc058> datas = getCedbc058Codes();
			for(Cedbc058 data : datas) {
				if( word.equals(data.getSameName1()) || word.equals(data.getSameName()) ) {
					results.add(data);
				}
			}
		}
		return results;
	}

	public String getCedbc058ByBaseName(String word) {
		if(!"".equals(Common.get(word))) {
			List<Cedbc058> datas = getCedbc058Codes();
			for(Cedbc058 data : datas) {
				if( word.equals(data.getSameName1()) || word.equals(data.getSameName()) )
					return data.getBaseName();
			}
		}
		return null;
	}
	
	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	/** 重新載入 */
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(CACHE_NAME, cedbc058Dao.queryAll());
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}

	private Cedbc058Dao cedbc058Dao;
	public Cedbc058Dao getCedbc058Dao() {return cedbc058Dao;}
	public void setCedbc058Dao(Cedbc058Dao cedbc058Dao) {this.cedbc058Dao = cedbc058Dao;}

}