package com.kangdainfo.tcfi.view.pre;
/* 
程式目的：收文預查編號年度統計表
程式代號：pre1007
程式日期：1030528
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.util.report.JasperReportMaker;

public class PRE1007 extends SuperBean {
	
	private String q_year ;
	
	private String total ;
	private String startNum ;
	private String endNum ;
	private String month ;
	
	public String getQ_year() {return checkGet(q_year);}
	public void setQ_year(String s) {q_year = checkSet(s);}
	
	public String getTotal() {return checkGet(total);}
	public void setTotal(String s) {total = checkSet(s);}
	public String getStartNum() {return checkGet(startNum);}
	public void setStartNum(String s) {startNum = checkSet(s);}
	public String getEndNum() {return checkGet(endNum);}
	public void setEndNum(String s) {endNum = checkSet(s);}
	public String getMonth() {return checkGet(month);}
	public void setMonth(String s) {month = checkSet(s);}
	//-----------------------------------------------------------------------------------------------------
	public void doCreate() throws Exception{	  
	} // end doCreate()
			  
	public void doUpdate() throws Exception{
	} // end doQueryOne()
	
	public void doDelete() throws Exception{			
	} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		return null ;
	} // end doQueryOne()
		  
	@SuppressWarnings("rawtypes")
	public ArrayList doQueryAll() throws Exception {
	    return null ;
    } // doQueryAll()

	public static SQLJob check( String year ) {
	  SQLJob sqljob = new SQLJob() ;
	  sqljob.appendSQL("select count(1) as ww from eicm.cedb1000 where prefix_no like '"
			           + year + "%' " ) ;
	  return sqljob ;
	} // check

	/** 檢核(FOR jsonPre1007.jsp */
	public static String checkForjsp(String year) throws Exception{
		List<Map<String,Object>> check = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList( check( year ) );
		if ("0".equals( Common.get( check.get(0).get("ww") ) ) ) {
			return year+"年度沒有預查收文資料!";
		} // if	
		else 
			return "ok";
	} // check
	
	public File doPrintPdf() throws Exception {
		try {
	        File report = null ;
	        String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre1007_1.jasper");
		    Map<String, Object> parameters = new HashMap<String,Object>();
	        String year = getQ_year() ;
	        parameters.put("year", "年度："+year ) ;
	        parameters.put("printDate", Common.formatYYYMMDD(Datetime.getYYYMMDD(),2));//列印日期
		    parameters.put("printTime", Common.formatHHMMSS(Datetime.getHHMMSS(),2));//列印時間

		    SQLJob sqljob = new SQLJob();
		    sqljob.appendSQL("SELECT");
		    sqljob.appendSQL(" md.mm as month");
		    sqljob.appendSQL(",nvl((select min(prefix_no) from eicm.cedb1000 where prefix_no like substr(md.ym,0,3)||'%' and receive_date like md.ym||'%'),' ') as startNum");
		    sqljob.appendSQL(",nvl((select max(prefix_no) from eicm.cedb1000 where prefix_no like substr(md.ym,0,3)||'%' and receive_date like md.ym||'%'),' ') as endNum");
		    sqljob.appendSQL(",to_char((select count(1)       from eicm.cedb1000 where prefix_no like substr(md.ym,0,3)||'%' and receive_date like md.ym||'%')) as total");
		    sqljob.appendSQL("from (");
		    sqljob.appendSQL("  select");
		    sqljob.appendSQL("   to_char(level,'00') mm");
		    sqljob.appendSQL("  ,?||substr(to_char(level,'00'),2,3) as ym");
		    sqljob.appendSQL("  from dual");
		    sqljob.appendSQL("  where level between 0 and 12 connect by level<15");
		    sqljob.appendSQL(") md");
		    sqljob.addParameter(year);

		    List<?> dataList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);	        
	        report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
	        return report ;
		} // try
		catch( MoeaException e ) {
			e.printStackTrace();
			setErrorMsg(e.getMessage());
			return null ;
		} // catch
	} // doPrintfPdf()		
	
} // PRE1007