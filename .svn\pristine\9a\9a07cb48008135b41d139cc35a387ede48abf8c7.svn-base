<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.kangdainfo.common.util.Common"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	if (!"".equals(q)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT decode(count(*),0,'未繳費',null) as payOrNot from osss.ossm_fee_main where telix_no = (select telix_no from eicm.cedb1000 where prefix_no = ?) and process_no = 'B'");
		sqljob.addParameter(q);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getOsssGeneralQueryDao().queryForList(sqljob);
		if ( datas != null && !datas.isEmpty() ) {
			String payOrNot = Common.get(datas.get(0).get("payOrNot"));
			out.write("{\"prefixNo\":\""+q+"\",\"payOrNot\":\""+payOrNot+"\"}");
		} 
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>