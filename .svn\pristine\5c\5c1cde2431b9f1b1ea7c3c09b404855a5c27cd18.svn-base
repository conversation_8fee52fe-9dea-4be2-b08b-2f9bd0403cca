<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<!-- Configurations -->
	<import resource="applicationContext-config.xml" />
	<import resource="applicationContext-persistence.xml" />
	<import resource="applicationContext-serviceGetter.xml" />
	
	<!-- TCFI -->
	<import resource="applicationContext-jdbc-eedb.xml" />
	<import resource="applicationContext-jdbc-eicm.xml" />
	<import resource="applicationContext-jdbc-osss.xml" />
	<import resource="applicationContext-jdbc-icms.xml" />
	<import resource="applicationContext-jdbc-eicmQD.xml" />
	<import resource="modules/applicationContext-tcfi.xml" />
	<import resource="modules/applicationContext-scheduling.xml" />
	<import resource="modules/applicationContext-loader.xml" />
	<import resource="modules/applicationContext-lucene.xml" />

</beans>