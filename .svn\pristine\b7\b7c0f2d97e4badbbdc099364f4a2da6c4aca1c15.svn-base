<!DOCTYPE html>
<!--
程式目的：解釋函登打維護
程式代號：pre8006
撰寫日期：103.03.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8006" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
   obj.setQueryAllFlag("true") ;
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();
		obj.setQueryAllFlag("true");
		// obj.setRcvNo(obj.getRcvNo());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if("deleteError".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();
	}
}else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		obj.outputFile(response, report, "PRE8006.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
    }
	
} else if ( "publishCode".equals( obj.getState() ) ) {
	obj.doPublishCode();
	if ( "publishCodeSuccess".equals( obj.getState() ) || "publishCodeError".equals( obj.getState() ) )
		obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();	
} else if ( "publishLaw".equals( obj.getState() ) ) {
	obj.doPublishLaw();
	if ( "publishLawSuccess".equals( obj.getState() ) || "publishLawError".equals( obj.getState() ) ) 
	obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();	
} else if ( "batchPublishCode".equals( obj.getState() )  ) {
	obj.doBatchPublishCode();
	obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();
} else if ( "batchPublishLaw".equals( obj.getState() ) ) {
	obj.doBatchPublishLaw();
	obj = (com.kangdainfo.tcfi.view.pre.PRE8006)obj.queryOne();
}
else if ("convert".equals( obj.getState() )) {
	obj.convert();
}
else if ("convertInstruction".equals( obj.getState() )) {
	obj.convertInstruction();
}

if ( "true".equals(obj.getQueryAllFlag()) ) 
  objList = (java.util.ArrayList) obj.queryAll();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<style>
div#printContainer {
	position: absolute;
	z-index: 30;
	left: 0;
	top: 0;
}

iframe#printContainerFrame {
	position: absolute;
	z-index: -1;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

	
</style>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.rcvNo, "公文文號");
	   alertStr += checkEmpty(form1.rcvTime, "收文日期");  
	   alertStr += checkEmpty(form1.keynote, "主旨");
	   alertStr += checkDate(form1.rcvTime,"收文日期");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;	
}

function init(){
	setDisplayItem("spanListPrint,spanListHidden","H");
	if ( form1.state.value == "init" )
		document.getElementById("listContainer").style.display = 'none';
	else
	    document.getElementById("listContainer").style.display = '';
	if ($("#clickRcvNo").val() == null || $("#clickRcvNo").val() == "" ) {
		$("#doPrintPdf").prop( "disabled", true );
		$("#doPublishCode").prop( "disabled", true );
		$("#doPublishLaw").prop( "disabled", true );
	} else {
		$("#doPrintPdf").prop( "disabled", false );
		$("#doPublishCode").prop( "disabled", false );
		$("#doPublishLaw").prop( "disabled", false );
	}
	
}

function queryOne(rcvNo){
    form1.clickRcvNo.value = rcvNo;
	// $('#rcvNo').val(rcvNo); 
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
} 

function printPdf() {
	if (form1.state.value != "queryOneSuccess" && form1.state.value != "preview") {
		alert("請先選擇一筆解釋函文號");
	}
	else {
		queryShow("printContainer");
	}
}

function publish(name) {
	if ( name == "doPublishCode" ) {
		if ( form1.busiItems.value == "" ) {
			alert("請先填入營業項目");	
		}
		else {
			form1.state.value = "publishCode";
			form1.submit();
		}
	}
	else if ( name == "doPublishLaw" ) {
		if ( form1.ruleCode.value == "" ) {
			alert("請先選擇法規名稱");
		}
		else {
			if ( form1.ruleNo.value == "" ) {
				alert("請先輸入法規條號");
			}
			else  {
				form1.state.value = "publishLaw";
				form1.submit();	
			}
		} // else
	}
}

function batchPublish(name) {
	var checkboxs = document.getElementsByName("rcvNoListWillChange");
	var count = 0;
	for (var i=0;i<checkboxs.length;i++) {
		if ( checkboxs[i].checked == true )
			count++;
	}
	if (count > 0) {
		if (name == "doBatchPublishCode") {
			form1.state.value = "batchPublishCode";
			form1.submit();	
		}
		else if ( name == "doBatchPublishLaw" ) {
			form1.state.value = "batchPublishLaw";
			form1.submit();
		}
	} // if
	else {
		alert("請先勾選要批次處理的解釋函");
	} // else
	
}

function oneClickCheckAll(obj,cName) 
{ 
    var checkboxs = document.getElementsByName(cName); 
    for(var i=0;i<checkboxs.length;i++){checkboxs[i].checked = obj.checked;} 
}

$(document).ready(function() {
	$("input[name=confirmSave]").click(function(){
		$.blockUI({ message: '<h1>資料載入中，請稍後...</h1>' });
	});
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "batchPublishCodeSuccess":
				setAllReadonly();				
				break;
			case "batchPublishCodeError":
				setAllReadonly();
				break;	
			case "batchPublishLawSuccess":
				setAllReadonly();
				break;
			case "batchPublishLawError":
				setAllReadonly();
				break;	
			case "insert":  // 按下新增或修改後只能按取消或存檔 以避免使用者修改後沒存檔案就按公示導致代碼沒寫進資料庫卻公示出去的情形
				$("#doPrintPdf").prop("disabled", true);
				$("#doPublishCode").prop("disabled", true);
				$("#doPublishLaw").prop("disabled", true);
				$("#doBatchPublishCode").prop("disabled", true);
				$("#doBatchPublishLaw").prop("disabled", true);
				break;
			case "update": 
				$("#doPrintPdf").prop("disabled", true);
				$("#doPublishCode").prop("disabled", true);
				$("#doPublishLaw").prop("disabled", true);
				$("#doBatchPublishCode").prop("disabled", true);
				$("#doBatchPublishLaw").prop("disabled", true);
				break;
			case "clear":
				$("#doBatchPublishCode").prop("disabled", false);
				$("#doBatchPublishLaw").prop("disabled", false);
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});



function isReceiveTooLong()
{
	var str = document.getElementById("receiveUnit").value;
	var len = str.length;
	if( len > 1000 )
	{
		return "正本單位字數過長，請調整為1000字以內，謝謝\n";
	}
	return "";
}

function isCcTooLong()
{
	var str = document.getElementById("ccUnit").value;
	var len = str.length;
	if( len > 2000 )
	{
		return "副本單位字數過長，請調整為2000字以內，謝謝\n";
	}
	return "";
}

function btnYes() {
	form1.printWithUnit.value = "true";
	form1.state.value = "preview";
	window.open("",'popReport');
	form1.target = 'popReport';
	queryHidden("printContainer");
	form1.submit();
	form1.target = '';
}

function btnNo() {
	form1.printWithUnit.value = "false";
	form1.state.value = "preview";
	window.open("",'popReport');
	form1.target = 'popReport';
	queryHidden("printContainer");
	form1.submit();
	form1.target = '';	
}

function doConvert() {
	form1.state.value = "convert";
	form1.submit();
}

function doConvertInstruction() {
	form1.state.value = "convertInstruction";
	form1.submit();
}

</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');localButtonFireListener.whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8006'/>
</c:import>


<!--Query區============================================================-->
<div id="queryContainer" style="top:200px;width:600px;height:200px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form">公文文號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_rcvNo" size="30" maxlength="30" value="<%=obj.getQ_rcvNo()%>">
        </td>
    </tr>
    <tr> 
    <td class="td_form">公文日期：</td>
        <td class="td_form_white"> 
           <%=View.getPopCalendar("field_Q", "q_rcvTime", obj.getQ_rcvTime())%>至 <%=View.getPopCalendar("field_Q","q_endRcvTime",obj.getQ_endRcvTime()) %>
        </td>
    </tr>
    
    <tr>
    <td class="td_form">函令主旨：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_keynote" size="60" maxlength="1000" value="<%=obj.getQ_keynote()%>">
        </td>
    </tr> 
    <tr>
        <td nowrap class="td_form">函令說明：</td>
        <td nowrap colspan="3" class="td_form_white">
            <textarea rows = "2" cols = "60" class="field_Q" type="text" name="q_instruction" maxlength="2000" style="white-space: pre-wrap"><%=obj.getQ_instruction() %></textarea>
        </td>  
    </tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="submit" name="querySubmit" value="確　　定" >
			<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<div id="printContainer" style="width:300px;height:100px;display:none" >
    <iframe id="printContainerFrame"></iframe>
	<div class="queryTitle">&nbsp</div>
	<table class="queryTable"  border="1">
	<tr>
		<td nowrap class="td_form_white" colspan="2" style="text-align:center;">
    	列印時要印出正副本嗎？
		</td>
	</tr>
    <tr>
		<td class="queryTDInput" style="text-align:center;">
			<input class="toolbar_default" type="button" id="buttonYes" name="buttonYes" value="是" onclick="btnYes()">
		</td>
		<td class="queryTDInput" style="text-align:center;">
			<input class="toolbar_default" type="button" id="buttonNo" name="buttonNo" value="否" onclick="btnNo()">
		</td>
	</tr>
	</table>
</div>

<!--Toolbar區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="bgToolbar" style="text-align:left">
	<input type="hidden" name="printWithUnit" value="<%=obj.getPrintWithUnit()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
    <input type="hidden" id="clickRcvNo" name = "clickRcvNo" value="<%=obj.getClickRcvNo()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
	  <jsp:param name="btnInsert" value="Y" />
	  <jsp:param name="btnQueryAll" value="Y" />
	  <jsp:param name="btnUpdate" value="Y" />
	  <jsp:param name="btnDelete" value="Y" />
	  <jsp:param name="btnClear" value="Y" />
	  <jsp:param name="btnConfirm" value="N" />
	  <jsp:param name="btnConfirmSave" value="Y" />
	  <jsp:param name="btnListHidden" value="N" />
	  <jsp:param name="btnPreview" value="N" />
	  <jsp:param name="btnCancel" value="N" />
	  <jsp:param name="btnListPrint" value="N" />
    </jsp:include>
    <span id="addButtonSpan">
		<input class="toolbar_default" type="button" id="doPrintPdf" name="doPrintPdf" value="列　印" onClick="printPdf(this.name);">
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" id="doPublishCode" name="doPublishCode" value="代碼公示" onClick="publish(this.name);">
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" id="doPublishLaw" name="doPublishLaw" value="法規公示" onClick="publish(this.name);">
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doBatchPublishCode" name="doBatchPublishCode" value="批次代碼公示" onClick="batchPublish(this.name);">
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doBatchPublishLaw" name="doBatchPublishLaw" value="批次法規公示" onClick="batchPublish(this.name);">
	</span>
</td></tr>

<!--Form區============================================================-->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
            <td nowrap class="td_form" width="10%">公文文號：</td>
            <td nowrap class="td_form_white" width="20%">
              <input class="field" type="text" name="rcvNo" size="30" maxlength="30" value="<%=obj.getRcvNo()%>">
            </td>  
            <td nowrap class="td_form" width="10%">公文日期：</td>
            <td nowrap class="td_form_white" width="30%">
              <%=View.getPopCalendar("field", "rcvTime", obj.getRcvTime())%>
            </td> 
        </tr>
        <tr>
            <td nowrap class="td_form">函令主旨：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" name="keynote" size="90" maxlength="1000" value="<%=obj.getKeynote()%>">
            </td>  
        </tr>    
        <tr>
            <td nowrap class="td_form" >函令說明：</td>
            <td nowrap colspan="3" class="td_form_white">
              <textarea rows = "12" cols = "90" class="field" type="text" name="instruction" maxlength="2000" style="white-space: pre-wrap"><%=obj.getInstruction() %></textarea>
            </td>  
        </tr>
        <tr>
            <td class="td_form">正本單位：</td>
			<td class="td_form_white" colspan="3" width="80%"> 
				<textarea rows="2" cols="90" class="field" type="text" id="receiveUnit" name="receiveUnit" style="white-space: pre-wrap"><%=obj.getReceiveUnit() %></textarea><br>
				(請以全形頓號"、"分開)
			</td>
        </tr>
        <tr>
        	<td class="td_form">副本單位：</td>
			<td class="td_form_white" colspan="3" width="80%">
				<textarea rows="2" cols="90" class="field" type="text" id="ccUnit" name="ccUnit" style="white-space: pre-wrap"><%=obj.getCcUnit() %></textarea><br>
				(請以全形頓號"、"分開)
			</td>
        </tr>
        <tr>
            <td nowrap class="td_form">法規名稱：</td>
            <td nowrap colspan="3" class="td_form_white">
            	<select name="ruleCode" id="ruleCode" value="<%=obj.getRuleCode()%>">
            		<%=obj.getOption(obj.buildLawList(), obj.getRuleCode(), false, 1, "") %>
            	</select>
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form">法規條號：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" name="ruleNo" size="50" maxlength="60" value="<%=obj.getRuleNo()%>"><br>
              (請以,分開。例：317-3,52,48,29)
            </td>  
        </tr>    
        <tr>
            <td nowrap class="td_form">營業項目：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" name="busiItems" size="50" maxlength="100" value="<%=obj.getBusiItems()%>"><br>
              (請以,分開。例：F107050肥料批發業,F113020電器批發業)
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form">公示狀態：</td>                               
            <td nowrap colspan="3" class="td_form_white">
              	<select name="isPublish" id="isPublish" value="<%=obj.getIsPublish()%>">
            		<%=obj.getOption(obj.buildPublishList(), obj.getIsPublish(), false, 3, "") %>
            	</select>
            </td>  
        </tr>    
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" height="200" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
  	<th class="listTH" style="width:30px;">
    	<input class="field_Q" type="checkbox" id="checkAll" name="checkAll" value="Y" onClick="oneClickCheckAll(this,'rcvNoListWillChange');">
    </th>
    <th class="listTH" style="text-align:left;width:300px;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">公文文號</a></th>
    <th class="listTH" style="text-align:left;width:80px;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">公文日期</a></th>
    <th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">函令主旨</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false, false};
  boolean displayArray[] = {true, true, true};
  String[] alignArray = {"left;width:300px;", "left;width:80px;", "left"};
  out.write(obj.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true, null, null, "",false, true,0, false, "rcvNoListWillChange"));
  %>
  </tbody>
</table>
</div>
</td></tr>

</table>	
</form>
</body>
</html>