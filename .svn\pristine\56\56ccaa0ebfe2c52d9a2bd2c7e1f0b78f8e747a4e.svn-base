package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;

import com.ekera.presearch.Examine;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;

/**
 * 預查申請收文確認(PRE1003)
 *
 */
public class PRE1003 extends SuperBean {

	private String[] prefixNos;
	
	private String prefixNo;
	private String isPrefixForm;
	private String docType;
	private String prefixFormNo;
	private String isOtherForm;
	private String isSpec;
	private String isOtherSpec;
	private String otherSpecRemark;

	/** 收文確認 */
	public void save() throws Exception {
		if(null!=prefixNos && prefixNos.length>0) {
			Cedb1000 cedb1000;
			//預查編號不是連號時即停止存檔，並提示訊息
			long tPrefixNo = Common.getLong(prefixNos[0]);
			for(String prefixNo : prefixNos) {
				if(tPrefixNo == Common.getLong(prefixNo)){
					//1.收文確認
					ServiceGetter.getInstance().getPrefixService().confirmRcvCheck(prefixNo, getLoginUserId());
					//2.更新免繳註記
					//改回到收文確認前就要先註記免繳
					//ServiceGetter.getInstance().getNoPayMarkService().checkNoPayMark(prefixNo, true);
					//3.同步一站式狀態
					cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(prefixNo);
					if(null!=cedb1000 && ( Common.get(cedb1000.getTelixNo()).startsWith("OSC") || Common.get(cedb1000.getTelixNo()).startsWith("OSS") )) {
						ServiceGetter.getInstance().getUpdateOsssStatusService().insertQueue(prefixNo, getLoginUserId());
					}				
					//4.新增 同名比對排程
					ServiceGetter.getInstance().getSameNameCompareService().createSameNameQueue(prefixNo, getLoginUserId());
				}else{
					this.setErrorMsg("所勾選的預查編號沒有連號，預查編號：" + Common.get(tPrefixNo));
					break;
				}
				tPrefixNo ++;
			}
		}
	}
	
	/** 附件維護查詢  */
	@Override
	public Object doQueryOne() throws Exception {
		PRE1003 obj = this;
		if("".equals(Common.get(obj.getPrefixNo())))	obj.setErrorMsg("查無預查資料，預查編號不得為空");
		else{
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(obj.getPrefixNo());
			if(cedb1000 == null)	obj.setErrorMsg("查無預查資料");	
			else{
				obj.setPrefixNo(cedb1000.getPrefixNo());
				obj.setIsPrefixForm(cedb1000.getIsPrefixForm());
				obj.setDocType(cedb1000.getDocType());
				obj.setPrefixFormNo(cedb1000.getPrefixFormNo());
				obj.setIsOtherForm(cedb1000.getIsOtherForm());
				obj.setIsSpec(cedb1000.getIsSpec());
				obj.setIsOtherSpec(cedb1000.getIsOtherSpec());
				obj.setOtherSpecRemark(cedb1000.getOtherSpecRemark());
			}
		}
		return obj;
	}
	
	/** 附件維護  */
	@Override
	public void doUpdate() throws Exception {
		if("".equals(Common.get(getPrefixNo())))	throw new MoeaException("查無預查資料，預查編號不得為空");
		else{
			Cedb1000 cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(getPrefixNo());
			if(cedb1000 == null)	throw new MoeaException("查無預查資料");
			else{
				String otherSpecRemark = getOtherSpecRemark();
				otherSpecRemark = otherSpecRemark.replaceAll("&amp;", "＆");

				ServiceGetter.getInstance().getPrefixService()
						.updateCedb1000Appendix(getPrefixNo(), getIsPrefixForm(),
								getDocType(), getPrefixFormNo(), getIsOtherForm(),
								getIsSpec(), getIsOtherSpec(), otherSpecRemark, getLoginUserId());
			}
		}
	}
	
	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}
	
	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] a) {this.prefixNos = a;}
	
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getIsPrefixForm() {return checkGet(isPrefixForm);}
	public void setIsPrefixForm(String s) {this.isPrefixForm = checkSet(s);}
	public String getDocType() {return checkGet(docType);}
	public void setDocType(String s) {this.docType = checkSet(s);}
	public String getPrefixFormNo() {return checkGet(prefixFormNo);}
	public void setPrefixFormNo(String s) {this.prefixFormNo = checkSet(s);}
	public String getIsOtherForm() {return checkGet(isOtherForm);}
	public void setIsOtherForm(String s) {this.isOtherForm = checkSet(s);}
	public String getIsSpec() {return checkGet(isSpec);}
	public void setIsSpec(String s) {this.isSpec = checkSet(s);}
	public String getIsOtherSpec() {return checkGet(isOtherSpec);}
	public void setIsOtherSpec(String s) {this.isOtherSpec = checkSet(s);}
	public String getOtherSpecRemark() {return checkGet(otherSpecRemark);}
	public void setOtherSpecRemark(String s) {this.otherSpecRemark = checkSet(s);}

}