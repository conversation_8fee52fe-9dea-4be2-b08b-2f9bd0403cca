package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;

public class Eedb1000Dao extends BaseDaoJdbc implements RowMapper<Eedb1000> {

	private static final String SQL_findByTelixNo = "SELECT * FROM EEDB1000 WHERE TELIX_NO = ?";
	public Eedb1000 findByTelixNo(String telixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByTelixNo);
		sqljob.addParameter(telixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
        List<Eedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	public List<Eedb1000> getEedb1000ListWhenPRE2004(String prefixStart, String prefixEnd, String[] prefixList) {
	    SQLJob sqljob = new SQLJob();
	    sqljob.appendSQL("select t0.* from eedb1000 t0, cedb1000 t1 where (t0.prefix_no = t1.prefix_no)" );
	    sqljob.appendSQL(" and (t0.telix_no = t1.telix_no)");
	    sqljob.appendSQL(" and (t1.approve_date is not null)" ); 
        sqljob.appendSQL(" and (t1.approve_date <> '')");
        sqljob.appendSQL(" and (t1.approve_result = 'BB') " );
	    sqljob.appendSQL(" and (t0.prefix_no >= ? ) ");
	    sqljob.appendSQL(" and (t0.prefix_no <= ?)");
	    for(int i = 0; i<prefixList.length; i++) {
	    	sqljob.appendSQL(" and (t0.prefix_no = ?)");	
	    } // for
	    sqljob.addParameter(prefixStart);
	    sqljob.addParameter(prefixEnd);
	    for(int j = 0; j<prefixList.length;j++) {
	    	sqljob.addParameter(prefixList[j]);
	    } // for
	    List<Eedb1000> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list;
	} 
	
	private static String sql_saveByObj = "INSERT INTO Eedb1000(REG_UNIT_CODE, TELIX_NO, PREFIX_NO, RECEIVE_DATE, RECEIVE_TIME, CASE_CODE, APPROVE_DATE, APPROVE_TIME, APPROVE_RESULT, PROCESS_STATUS, RESERVE_DATE, RECEIVE_MAIL, APPROVE_MAIL, COME_FROM) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
	public int insert(Eedb1000 eedb1000) {
		if (eedb1000 == null)
			return 0;
		SQLJob sqljob = new SQLJob(sql_saveByObj);
		sqljob.addParameter(eedb1000.getRegUnitCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getTelixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getReceiveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getReceiveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getCaseCode());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getApproveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getApproveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getProcessStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getReserveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getReceiveMail());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getApproveMail());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(eedb1000.getComeFrom());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	
	public int update( Eedb1000 obj ) throws SQLException {
		if ( obj == null )
			return 0;
		SQLJob sqljob = new SQLJob("update EEDB1000 set");
		sqljob.appendSQL(" approve_result=?");
		sqljob.appendSQL(",process_status=?");
		sqljob.appendSQL(",approve_date=?");
		sqljob.appendSQL(",approve_time=?");
		sqljob.appendSQL("where prefix_no = ?");
		sqljob.addParameter(obj.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getProcessStatus());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveDate());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveTime());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}
	
	public void updateClosed(String prefixNo) {
		SQLJob sqljob = new SQLJob("");
		sqljob.appendSQL("UPDATE EEDB1000 SET ");
		sqljob.appendSQL(" APPROVE_DATE = (SELECT approve_date FROM cedb1000 WHERE prefix_no = eedb1000.prefix_no)");
		sqljob.appendSQL(",APPROVE_TIME = (SELECT approve_time FROM cedb1000 WHERE prefix_no = eedb1000.prefix_no)");
		sqljob.appendSQL(",APPROVE_RESULT = (SELECT approve_result FROM cedb1000 WHERE prefix_no = eedb1000.prefix_no)");
		sqljob.appendSQL(",PROCESS_STATUS = 'ZZ'");
		sqljob.appendSQL(",APPROVE_MAIL = null");
		sqljob.appendSQL("WHERE EEDB1000.prefix_no = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	public Eedb1000 mapRow(ResultSet rs, int idx) throws SQLException {
		Eedb1000 obj = null;
		if(null!=rs) {
			obj = new Eedb1000();
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setRegUnitCode(Common.get(rs.getString("REG_UNIT_CODE")));
			obj.setTelixNo(Common.get(rs.getString("TELIX_NO")));
			obj.setReceiveDate(Common.get(rs.getString("RECEIVE_DATE")));
			obj.setReceiveTime(Common.get(rs.getString("RECEIVE_TIME")));
			obj.setCaseCode(Common.get(rs.getString("CASE_CODE")));
			obj.setApproveDate(Common.get(rs.getString("APPROVE_DATE")));
			obj.setApproveTime(Common.get(rs.getString("APPROVE_TIME")));
			obj.setApproveTime(Common.get(rs.getString("APPROVE_TIME")));
			obj.setApproveResult(Common.get(rs.getString("APPROVE_RESULT")));
			obj.setProcessStatus(Common.get(rs.getString("PROCESS_STATUS")));
			obj.setReserveDate(Common.get(rs.getString("RESERVE_DATE")));
			obj.setReceiveMail(Common.get(rs.getString("RECEIVE_MAIL")));
			obj.setApproveMail(Common.get(rs.getString("APPROVE_MAIL")));
			obj.setComeFrom(Common.get(rs.getString("COME_FROM")));
		}
		return obj;
	}

}