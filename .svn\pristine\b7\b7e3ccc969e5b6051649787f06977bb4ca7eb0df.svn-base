package com.kangdainfo.tcfi.service.impl;

import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.GeneralityBusitem;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.GeneralityBusitemDao;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao;
import com.kangdainfo.tcfi.service.NoPayMarkService;

/**
 * 免繳註記
 *
 */
public class NoPayMarkServiceImpl
	implements NoPayMarkService
{
	public GeneralityBusitem getByBanNo(String banNo) {
		return generalityBusitemDao.findByBanNo(banNo);
	}

	public String checkNoPayMark(String prefixNo, boolean canMod ) {
		String noNeedPay = "N";
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo);
		if(null!=cedb1000) {
			prefixNo = cedb1000.getPrefixNo();
			String telixNo = Common.get(cedb1000.getTelixNo());
			//有申請所營事業變更的才檢查
			Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1023 && ("2".equals(cedb1023.getChangeType()) || "3".equals(cedb1023.getChangeType())) ) {
				//有統一編號才檢查(申請所營事業變更都會有)
				String banNo = cedb1000.getBanNo();
				if(!"".equals(Common.get(banNo))) {
					GeneralityBusitem gb = generalityBusitemDao.findByBanNo(banNo);
					if(null!=gb) {
						if( !"".equals(prefixNo) && Common.get(gb.getPrefixNo()).equals(prefixNo) ) {
							//已免繳一次，預查編號為本次案件的預查編號
							noNeedPay = "Y";
						} else if( !"".equals(telixNo) && Common.get(gb.getTelixNo()).equals(telixNo) ) {
							//已免繳一次，電子流水號為本次案件的電子流水號
							noNeedPay = "Y";
							//改成預查編號
							updatePrefixNo(banNo, prefixNo);
						} else if( "".equals(Common.get(gb.getPrefixNo())) && "".equals(Common.get(gb.getTelixNo())) ) {
							//尚未使用免繳, 要判斷是否符合免繳資格

							//一站式要確定沒繳
							boolean hasPaid = false;
							if( telixNo.startsWith("OSC") || telixNo.startsWith("OSS") ) {
								OssmFeeMain ossmFeeMain = ossmFeeMainDao.findByTelixNoAndProcessNo(telixNo,"B");
								if(null!=ossmFeeMain) {
									//OssmFeeMain 有資料, 表示有繳, 應該要走退費
									hasPaid = true;
								}
							}

							if(!hasPaid) {
								//1.設立日期須在0980702(含)之前設立，> 0 代表setupDate在0980702以前
								Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
								if(null!=cedb2000 && !"".equals(Common.get(cedb2000.getSetupDate())) && ("0980702".compareTo(cedb2000.getSetupDate()) >= 0) ) {
									//2.原本登的營業項目有ZZ99999
									SQLJob zz99999Sql = new SQLJob("select busi_item_no from cedb2002 where ban_no = ? and (busi_item_no = 'ZZ99999' or busi_item like '除許可業務外，得經營法令非禁止或限制之業務%')");
									zz99999Sql.addParameter(banNo);
									List<Map<String, Object>> listZZ99999 = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(zz99999Sql);
									if (listZZ99999 != null && listZZ99999.size() > 0 ) {
										//3.這次加的營業項目有0結尾的
										//三個條件都需要符合
										SQLJob sqljob = new SQLJob();
										sqljob.appendSQL("SELECT BUSI_ITEM_NO");
										sqljob.appendSQL("FROM CEDB1002 WHERE prefix_no = ?");
										sqljob.appendSQL("AND busi_item_no NOT IN (");
										sqljob.appendSQL("  SELECT BUSI_ITEM_NO FROM CEDB2002");
										sqljob.appendSQL("  WHERE BAN_NO = ?");
										sqljob.appendSQL("  AND BUSI_ITEM_NO != 'ZZ99999'");
										sqljob.appendSQL(")");
										sqljob.appendSQL("AND BUSI_ITEM_NO like '%0'");
										sqljob.appendSQL("AND ROWNUM = 1");
										sqljob.addParameter(prefixNo);
										sqljob.addParameter(banNo);
										List<Map<String, Object>> list = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
										if (null!=list && !list.isEmpty()) {
											if ( canMod )
												insertNoPayMark(cedb1000);
											noNeedPay = "Y";
										}
									}
								}
							}
						}
					} else {
						//無可免繳註記, 不管
					}
				}
			}
		}
		return noNeedPay;
	}

	public String getNoPayMark(String banNo) {
		String result = "";
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			result = "無可免繳註記";
		} else {
			if( "0000".equals(o.getPrefixNo()) || "0000".equals(o.getTelixNo()) ) {
				result = "免繳資格已被手動註銷";
			} else if( !"".equals(Common.get(o.getPrefixNo())) ) {
				result = "已免繳一次，預查編號："+o.getPrefixNo();
			} else if( !"".equals(Common.get(o.getTelixNo())) ) {
				result = "已免繳一次，電子流水號："+o.getTelixNo();
			} else {
				result = "尚未使用";
			}
		}
		return result;
	}
	
	public String getNoPayMark4Pre3005(String banNo, String prefixNo, String telixNo) {
		String result = "";
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			result = "無可免繳註記";
		} else {
			if( "0000".equals(o.getPrefixNo()) || "0000".equals(o.getTelixNo()) ) {
				result = "免繳資格已被手動註銷";
			} else if( !"".equals(Common.get(o.getPrefixNo())) && Common.get(o.getPrefixNo()).equals(prefixNo) ) {
				result = "已免繳一次，預查編號："+o.getPrefixNo();
			} else if( !"".equals(Common.get(o.getTelixNo())) && Common.get(o.getTelixNo()).equals(telixNo) ) {
				result = "已免繳一次，電子流水號："+o.getTelixNo();
			} else {
				result = "尚未使用";
			}
		}
		return result;
	}

	public void updateNotUsed(String banNo) {
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			o = new GeneralityBusitem();
			o.setBanNo(banNo);
			o.setCmpyMark("N");
			o.setPrefixNo(null);
			o.setTelixNo(null);
			generalityBusitemDao.insert(o);
		} else {
			o.setPrefixNo(null);
			o.setTelixNo(null);
			o.setCmpyMark("N");
			generalityBusitemDao.update(o);
		}
	}

	public void updateNoMark(String banNo) {
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null!=o) {
			generalityBusitemDao.delete(o);
		}
	}

	public void updatePrefixNo(String banNo, String prefixNo) {
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			o = new GeneralityBusitem();
			o.setBanNo(banNo);
			o.setCmpyMark("N");
			o.setPrefixNo(prefixNo);
			o.setTelixNo(null);
			generalityBusitemDao.insert(o);
		} else {
			o.setCmpyMark("N");
			o.setPrefixNo(prefixNo);
			o.setTelixNo(null);
			generalityBusitemDao.update(o);
		}
	}

	public void updateTelixNo(String banNo, String telixNo) {
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			o = new GeneralityBusitem();
			o.setBanNo(banNo);
			o.setCmpyMark("N");
			o.setPrefixNo(null);
			o.setTelixNo(telixNo);
			generalityBusitemDao.insert(o);
		} else {
			o.setCmpyMark("N");
			o.setPrefixNo(null);
			o.setTelixNo(telixNo);
			generalityBusitemDao.update(o);
		}
	}

	public void updateCancel(String banNo) {
		GeneralityBusitem o = generalityBusitemDao.findByBanNo(banNo);
		if(null==o) {
			o = new GeneralityBusitem();
			o.setBanNo(banNo);
			o.setCmpyMark("Y");
			o.setPrefixNo("0000");
			o.setTelixNo("0000");
			generalityBusitemDao.insert(o);
		} else {
			o.setCmpyMark("Y");
			o.setPrefixNo("0000");
			o.setTelixNo("0000");
			generalityBusitemDao.update(o);
		}
	}
	
	@Override
	public void insertNoPayMark(Cedb1000 cedb1000) {
		GeneralityBusitem gb = generalityBusitemDao.findByBanNo(cedb1000.getBanNo());
		if (gb == null) {
			gb = new GeneralityBusitem();
			gb.setBanNo(cedb1000.getBanNo());

			if( !"".equals(Common.get(cedb1000.getPrefixNo())) ) {
				gb.setPrefixNo(Common.get(cedb1000.getPrefixNo()));
				gb.setTelixNo(null);
			} else if( !"".equals(Common.get(cedb1000.getTelixNo())) ){
				gb.setPrefixNo(null);
				gb.setTelixNo(Common.get(cedb1000.getTelixNo()));
			}
			generalityBusitemDao.insert(gb);
		} else {

			if( !"".equals(Common.get(cedb1000.getPrefixNo())) ) {
				gb.setPrefixNo(Common.get(cedb1000.getPrefixNo()));
				gb.setTelixNo(null);
			} else if( !"".equals(Common.get(cedb1000.getTelixNo())) ){
				gb.setPrefixNo(null);
				gb.setTelixNo(Common.get(cedb1000.getTelixNo()));
			}

			generalityBusitemDao.update(gb);
		}
	}

	private GeneralityBusitemDao generalityBusitemDao;
	public GeneralityBusitemDao getGeneralityBusitemDao() {return generalityBusitemDao;}
	public void setGeneralityBusitemDao(GeneralityBusitemDao dao) {this.generalityBusitemDao = dao;}
	
	private Cedb1000Dao cedb1000Dao;
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	
	private Cedb1023Dao cedb1023Dao;
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	
	private OssmFeeMainDao ossmFeeMainDao;
	public OssmFeeMainDao getOssmFeeMainDao() {return ossmFeeMainDao;}
	public void setOssmFeeMainDao(OssmFeeMainDao dao) {this.ossmFeeMainDao = dao;}

	private Cedb2000Dao cedb2000Dao;
	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}

}