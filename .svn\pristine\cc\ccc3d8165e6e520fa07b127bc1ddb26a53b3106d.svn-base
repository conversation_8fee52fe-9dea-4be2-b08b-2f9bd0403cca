package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 檢索檔執行紀錄(INDEX_LOG)
 *
 */
public class IndexLog extends BaseModel {
	private static final long serialVersionUID = 1L;

	private Long id;
	/** 執行WS的程式代碼 */
	private String wsId;
	/** 參數1 */
	private String param1;
	/** 參數2 */
	private String param2;
	/** 參數3 */
	private String param3;
	/** 預計執行日期(預設空值表示立即執行;有設定日期,要比對日期小於等於sysDate才執行) */
	private String executeDate;
	/** 建立日期 */
	private String createDate;
	/** 建立者 */
	private String createUser;
	/** 開始時間 */
	private String startTime;
	/** 完成時間 */
	private String finishTime;
	/** 狀態(0:待執行/1:執行中/2:執行成功/3:執行失敗) */
	private String status;
	/** 備註 */
	private String remark;

	public Long getId() {return id;}
	public void setId(Long id) {this.id = id;}
	public String getWsId() {return wsId;}
	public void setWsId(String wsId) {this.wsId = wsId;}
	public String getParam1() {return param1;}
	public void setParam1(String param1) {this.param1 = param1;}
	public String getParam2() {return param2;}
	public void setParam2(String param2) {this.param2 = param2;}
	public String getParam3() {return param3;}
	public void setParam3(String param3) {this.param3 = param3;}
	public String getCreateUser() {return createUser;}
	public void setCreateUser(String createUser) {this.createUser = createUser;}
	public String getStatus() {return status;}
	public void setStatus(String status) {this.status = status;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}
	public String getExecuteDate() {return executeDate;}
	public void setExecuteDate(String executeDate) {this.executeDate = executeDate;}
	public String getCreateDate() {return createDate;}
	public void setCreateDate(String createDate) {this.createDate = createDate;}
	public String getStartTime() {return startTime;}
	public void setStartTime(String startTime) {this.startTime = startTime;}
	public String getFinishTime() {return finishTime;}
	public void setFinishTime(String finishTime) {this.finishTime = finishTime;}

}