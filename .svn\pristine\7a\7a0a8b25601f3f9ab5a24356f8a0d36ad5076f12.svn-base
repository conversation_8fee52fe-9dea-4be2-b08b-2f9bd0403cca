<!-- ------
程式目的：預查編號維護
程式代號：pre4015
撰寫日期：103.03.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4015">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4015" />
</jsp:include>
<%
if ("init".equals(obj.getState())) {
	objList = (java.util.ArrayList) obj.queryAll();
	if(null!=objList && !objList.isEmpty()) {
		obj.setErrorMsg("查詢成功！");
	} else {
		obj.setErrorMsg("查無資料！");
	}
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(yearNo){
} 
</script>
</head>
<body topmargin="5" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4015'/>
</c:import>

<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<form id="form1" name="form1" method="post"  >
<table width="100%" cellspacing="0" cellpadding="0">

<tr><td nowrap class="bgList">
	<div id="listContainer" style="height:auto;">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
		<thead id="listTHEAD">
			<tr>
				<th class="listTH" width="30%"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">年度</a></th>
				<th class="listTH" width="70%" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">預查編號</a></th>
			</tr>
		</thead>
		<tbody id="listTBODY">
<%
	boolean primaryArray[] = {true,false };
	boolean displayArray[] = {true,true };
	String[] alignArray = {"center", "left"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
%>
		</tbody>
	</table>
	</div>
</td></tr>

</table>
</table>	
</form>
</body>
</html>
