package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1122;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 公司預查封存申請案法人資料檔(CEDB1122)
 *
 */
public class Cedb1122Dao
	extends BaseDaoJdbc
	implements RowMapper<Cedb1122>
{
	public Cedb1122 findByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM CEDB1122 WHERE PREFIX_NO=?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		List<?> list = getJdbcTemplate().query(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray(), this);
		return list.isEmpty() ? null : (Cedb1122) list.get(0);
	}

	public int insert(Cedb1122 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("INSERT INTO CEDB1122(PREFIX_NO, APPLY_BAN_NO, APPLY_LAW_NAME) VALUES (?,?,?)");
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyLawName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public int update(Cedb1022 o) {
		if (null==o) return 0;
		SQLJob sqljob = new SQLJob("UPDATE CEDB1122 SET");
		sqljob.appendSQL("  APPLY_BAN_NO=?");
		sqljob.appendSQL(" ,APPLY_LAW_NAME=?");
		sqljob.appendSQL(" WHERE PREFIX_NO=?");
		sqljob.addParameter(o.getApplyBanNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getApplyLawName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(o.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(),sqljob.getParametersArray(),sqljob.getSqltypesArray());
	}

	public void deleteByPrefixNo(String prefixNo) {
		if(CommonStringUtils.isEmpty(prefixNo)) return;
		SQLJob sqljob = new SQLJob("DELETE FROM CEDB1122 WHERE PREFIX_NO = ?");
		sqljob.addParameter(prefixNo);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1122 mapRow(ResultSet rs, int rowNum) throws SQLException {
		Cedb1122 obj = null;
		if (null != rs) {
			obj = new Cedb1122();
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setApplyBanNo(rs.getString("APPLY_BAN_NO"));
			obj.setApplyLawName(rs.getString("APPLY_LAW_NAME"));
		}
		return obj;
	}

}