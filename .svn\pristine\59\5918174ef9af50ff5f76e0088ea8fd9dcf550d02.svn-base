package com.kangdainfo.tcfi.model.osss.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * (OSSM_ORG_BRANCH)
 *
 */
public class OssmOrgBranch extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 電子案號 */
	private String telixNo;
	/** 序號 */
	private Integer seqNo;
	/** 案由代碼 */
	private String caseCode;
	/** 分公司統編 */
	private String brBanNo;
	/** 分公司名稱 */
	private String brName;
	/** 案由類別(1:設立; 2:變更登記; 3:撤銷) */
	private String caseType;

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public Integer getSeqNo() {
		return seqNo;
	}
	public void setSeqNo(Integer seqNo) {
		this.seqNo = seqNo;
	}
	public String getCaseCode() {
		return caseCode;
	}
	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}
	public String getBrBanNo() {
		return brBanNo;
	}
	public void setBrBanNo(String brBanNo) {
		this.brBanNo = brBanNo;
	}
	public String getBrName() {
		return brName;
	}
	public void setBrName(String brName) {
		this.brName = brName;
	}
	public String getCaseType() {
		return caseType;
	}
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

}