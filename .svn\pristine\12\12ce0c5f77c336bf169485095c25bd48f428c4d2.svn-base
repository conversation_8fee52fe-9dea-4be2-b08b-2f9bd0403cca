<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- USE JNDI CONNECTION -->
	<!-- <bean id="eicmDataSource" class="org.springframework.jndi.JndiObjectFactoryBean"> 
		<property name="jndiName"> <value>java:comp/env/jdbc/eicm</value> </property> 
		</bean> -->
	<bean id="eicmDataSource" class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${eicm.dataSource.driverClassName}" />
		<property name="url" value="${eicm.dataSource.url}" />
		<property name="username" value="${eicm.dataSource.username}" />
		<property name="password" value="${eicm.dataSource.password}" />
		<property name="validationQuery" value="${eicm.dataSource.validationQuery}" />
		<property name="poolPreparedStatements" value="${eicm.dataSource.poolPreparedStatements}" />
		<property name="maxOpenPreparedStatements" value="${eicm.dataSource.maxOpenPreparedStatements}" />
		<property name="maxActive" value="${eicm.dataSource.maxActive}" />
		<property name="maxIdle" value="${eicm.dataSource.maxIdle}" />
	</bean>

	
	<bean id="eicmGeneralQueryDao" class="com.kangdainfo.tcfi.model.eicm.dao.EicmGeneralQueryDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	
	<bean id="atonceRecordDao" class="com.kangdainfo.tcfi.model.eicm.dao.AtonceRecordDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1000Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1001Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1002Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1003Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1003Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1004Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1004Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1006Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1007Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1007Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1008Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1009Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1009Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1010Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1011Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1011Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1019Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1019Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1017Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1017Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1021Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1021Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1022Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1023Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1023LDao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1023LDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1027Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1027Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1028Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1028Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1100Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1100Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1101Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1101Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1102Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1102Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1110Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1110Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1122Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1122Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb1123Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb1123Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc000Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc003Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc003Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc004Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc004Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="postRecordDao" class="com.kangdainfo.tcfi.model.eicm.dao.PostRecordDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="receiptNoSetupDAO" class="com.kangdainfo.tcfi.model.eicm.dao.ReceiptNoSetupDAO">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="prefixReceiptNoDAO" class="com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="busiItemDao" class="com.kangdainfo.tcfi.model.eicm.dao.BusiItemDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc058Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc058Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cmpyStatusDao" class="com.kangdainfo.tcfi.model.eicm.dao.CmpyStatusDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="declaratoryStatutesDao"
		class="com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="declaratoryStatutesRcverDao"
		class="com.kangdainfo.tcfi.model.eicm.dao.DeclaratoryStatutesRcverDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="generalityBusitemDao" class="com.kangdainfo.tcfi.model.eicm.dao.GeneralityBusitemDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="partNameLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.PartNameLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cmpyMemoInfoDao" class="com.kangdainfo.tcfi.model.eicm.dao.CmpyMemoInfoDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="indexLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.IndexLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="indexLogHDao" class="com.kangdainfo.tcfi.model.eicm.dao.IndexLogHDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="searchLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.SearchLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="systemCodeDao" class="com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="systemNewsDao" class="com.kangdainfo.tcfi.model.eicm.dao.SystemNewsDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="flowLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.FlowLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="functionMenuDao" class="com.kangdainfo.tcfi.model.eicm.dao.FunctionMenuDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="functionMenuAuthDao" class="com.kangdainfo.tcfi.model.eicm.dao.FunctionMenuAuthDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="sameNameQueueDao" class="com.kangdainfo.tcfi.model.eicm.dao.SameNameQueueDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="restrictionDao" class="com.kangdainfo.tcfi.model.eicm.dao.RestrictionDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="restrictionItemDao" class="com.kangdainfo.tcfi.model.eicm.dao.RestrictionItemDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="loginLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.LoginLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
		<bean id="syncOssQueueDao" class="com.kangdainfo.tcfi.model.eicm.dao.SyncOssQueueDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="synonymWordDao" class="com.kangdainfo.tcfi.model.eicm.dao.SynonymWordDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="trackLogDao" class="com.kangdainfo.tcfi.model.eicm.dao.TrackLogDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="eedb1002Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Eedb1002Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="eedb1000DaoEicm" class="com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="umsMtDao" class="com.kangdainfo.tcfi.model.eicm.dao.UmsMtDao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	
	<bean id="cedb2000Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb2002Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb2004Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb2006Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2006Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedb2013Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedb2013Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc050Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc050Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc053Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc054Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="cedbc055Dao" class="com.kangdainfo.tcfi.model.eicm.dao.Cedbc055Dao">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
</beans>