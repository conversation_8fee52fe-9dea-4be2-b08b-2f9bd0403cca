<%@page import="com.kangdainfo.tcfi.model.eicm.bo.Cedbc055"%>
<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>"%>
<%@ page import="com.google.gson.*"%>
<%
	response.addHeader("Pragma", "No-cache");
	response.addHeader("Cache-Control", "no-cache");

	try {
		Gson gson = new GsonBuilder().create();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select item_code, business_item from cedbc055");
		List datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(sqljob, new BeanPropertyRowMapper(Cedbc055.class));
		if (null != datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	} catch (Exception e) {
		e.printStackTrace();
	}
%>