package com.kangdainfo.tcfi.view.pre;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.util.report.JasperReportMaker;

/*
程式目的：審核作業流程/時間分析
程式代號：pre4009
程式日期：103.05.09
程式作者：Kai.Cheng
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE4007 extends SuperBean {
	
	private String q_DateStart ;        // 查詢條件：起始日
	private String q_DateEnd ;          // 查詢條件：終結日
	
	private String receiveDate ;        // 收文日期
    private String totalReceive ;       // 收文總件數
    private String totalComplete ;      // 辦結總件數
    private String workDayReceive ;     // 工作天數
    private String workDayWait ;        // 工作天數
    private String workDayDeside ;      // 工作天數
    private String workDayClose ;       // 工作天數
    private String workDayEnd ;         // 工作天數
    private String numReceive ;         // 案件數
    private String numWait ;            // 案件數
    private String numDeside ;          // 案件數
    private String numClose ;           // 案件數
    private String numEnd ;             // 案件數
    private String averageReceive ;     // 平均工作天數
    private String averageWait ;        // 平均工作天數
    private String averageDeside ;      // 平均工作天數
    private String averageClose ;       // 平均工作天數
    private String averageEnd ;        	// 平均工作天數
    
    private String totalSumary;         // 加總
    
	// ----------------------------------getters and setters of variable bellow ---------------------------
	public String getQ_DateStart() {return checkGet(q_DateStart);}
	public void setQ_DateStart(String s) {q_DateStart = checkSet(s);}
	public String getQ_DateEnd() {return checkGet(q_DateEnd);}
	public void setQ_DateEnd(String s) {q_DateEnd = checkSet(s);}
	
	public String getReceiveDate() {return checkGet(receiveDate);}
	public void setReceiveDate(String s) {receiveDate = checkSet(s);}
	public String getTotalReceive() {return checkGet(totalReceive);}
	public void setTotalReceive(String s) {totalReceive = checkSet(s);}
	public String getTotalComplete() {return checkGet(totalComplete);}
	public void setTotalComplete(String s) {totalComplete = checkSet(s);}
	public String getWorkDayReceive() {return checkGet(workDayReceive);}
	public void setWorkDayReceive(String s) {workDayReceive = checkSet(s);}
	public String getWorkDayWait() {return checkGet(workDayWait);}
	public void setWorkDayWait(String s) {workDayWait = checkSet(s);}
	public String getWorkDayDeside() {return checkGet(workDayDeside);}
	public void setWorkDayDeside(String s) {workDayDeside = checkSet(s);}
	public String getWorkDayClose() {return checkGet(workDayClose);}
	public void setWorkDayClose(String s) {workDayClose = checkSet(s);}
	public String getWorkDayEnd() {return checkGet(workDayEnd);}
	public void setWorkDayEnd(String s) {workDayEnd = checkSet(s);}
	public String getNumReceive() {return checkGet(numReceive);}
	public void setNumReceive(String s) {numReceive = checkSet(s);}
	public String getNumWait() {return checkGet(numWait);}
	public void setNumWait(String s) {numWait = checkSet(s);}
	public String getNumDeside() {return checkGet(numDeside);}
	public void setNumDeside(String s) {numDeside = checkSet(s);}
	public String getNumClose() {return checkGet(numClose);}
	public void setNumClose(String s) {numClose = checkSet(s);}
	public String getNumEnd() {return checkGet(numEnd);}
	public void setNumEnd(String s) {numEnd = checkSet(s);}
	public String getAverageReceive() {return checkGet(averageReceive);}
	public void setAverageReceive(String s) {averageReceive = checkSet(s);}
	public String getAverageWait() {return checkGet(averageWait);}
	public void setAverageWait(String s) {averageWait = checkSet(s);}
	public String getAverageDeside() {return checkGet(averageDeside);}
	public void setAverageDeside(String s) {averageDeside = checkSet(s);}
	public String getAverageClose() {return checkGet(averageClose);}
	public void setAverageClose(String s) {averageClose = checkSet(s);}
	public String getAverageEnd() {return checkGet(averageEnd);}
	public void setAverageEnd(String s) {averageEnd = checkSet(s);}
	
	public String getTotalSumary() {return checkGet(totalSumary);}
	public void setTotalSumary(String s) {totalSumary = checkSet(s);}
	// --------------------getters and setters of variables -------------------------------------
	
    // --------------------functions never used bellow----------------------------------------------------
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	public void doCreate() throws Exception{} // end doCreate()
	public void doUpdate() throws Exception{} // end doUpdate()		
	public void doDelete() throws Exception{} // end doDelete()	
	
	public Object doQueryOne() throws Exception{
		java.text.DecimalFormat df = new java.text.DecimalFormat("#0.00");
		doFillWithEmptyValue();		//cleanup form
		PRE4007 pre4007 = this;
		//收文總件數 與 辦結總件數
		List<Map<String,Object>> total = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobForTotalReceive(getQ_DateStart(), getQ_DateEnd()));
		if(total != null && total.size() > 1){
			//收件日期
			pre4007.setReceiveDate( Common.formatYYYMMDD(getQ_DateStart(), 2) + "~" + Common.formatYYYMMDD(getQ_DateEnd(), 2)) ;
			//收文總件數
			pre4007.setTotalReceive(Common.get(total.get(0).get("total")));
			//辦結總件數
			pre4007.setTotalComplete(Common.get(total.get(1).get("total")));
			total.clear();
			//流程的工作天數/件數/平均值
			total = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobForTotalStatus());
			if(total != null && total.size() > 0){
				java.util.Iterator<Map<String,Object>> it = total.iterator();
				Map<String,Object> data = null;
				String workDay = "", num = "", average = "";
				while(it.hasNext()){
					data = it.next();
					workDay = Common.get(data.get("workday"));
					num = Common.get(data.get("num"));
					average = df.format(Double.parseDouble(Common.get(data.get("averageDay"))));
					if("收文登打".equals(data.get("typeName"))){
						pre4007.setWorkDayReceive(workDay);
						pre4007.setNumReceive(num);
						pre4007.setAverageReceive(average);
					}else if("等待分文".equals(data.get("typeName"))){
						pre4007.setWorkDayWait(workDay);
						pre4007.setNumWait(num);
						pre4007.setAverageWait(average);
					}else if("承辦決行".equals(data.get("typeName"))){
						pre4007.setWorkDayDeside(workDay);
						pre4007.setNumDeside(num);
						pre4007.setAverageDeside(average);
					}else if("發文登打".equals(data.get("typeName"))){
						pre4007.setWorkDayClose(workDay);
						pre4007.setNumClose(num);
						pre4007.setAverageClose(average);
					}else if("發文".equals(data.get("typeName"))){
						pre4007.setWorkDayEnd(workDay);
						pre4007.setNumEnd(num);
						pre4007.setAverageEnd(average);
					}	
				} // while
				
				setTotalSumary(df.format(Double.parseDouble(pre4007.getAverageReceive())+Double.parseDouble(pre4007.getAverageWait())
						      			 +Double.parseDouble(pre4007.getAverageDeside())+Double.parseDouble(pre4007.getAverageClose())
						      			 +Double.parseDouble(pre4007.getAverageEnd())));
			}
			pre4007.setErrorMsg("查詢成功");
		}else{
			pre4007.setErrorMsg("查無資料，請變更查詢條件！");
			//throw new MoeaException("查無資料，請變更查詢條件！");
		}
		return pre4007;
	} // end doQueryOne()
	
	public File doPrintPdf() throws Exception {
		
		File report = null ;       
		try {
			//收文總件數 與 辦結總件數
			List<Map<String,Object>> total = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobForTotalReceive(getQ_DateStart(), getQ_DateEnd()));
			if(total != null && total.size() > 1){
				ArrayList<Map<String,String>> dataList = new ArrayList<Map<String,String>>();
				String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4007.jasper");
				Map<String, Object> parameters = new HashMap<String,Object>();
				//收件日期
				parameters.put("receiveDate", Common.formatYYYMMDD(getQ_DateStart(), 2) + "~" + Common.formatYYYMMDD(getQ_DateEnd(), 2));
				parameters.put("totalReceive", Common.get(total.get(0).get("total")));			//收文總件數
				parameters.put("totalComplete", Common.get(total.get(1).get("total")));			//辦結總件數
				parameters.put("printDate", Common.formatYYYMMDD(Datetime.getYYYMMDD(), 3));	//列印時間
				parameters.put("printTime", Common.formatHHMMSS(Datetime.getHHMMSS(), 2));		//列印時間
				total.clear();
				//流程的工作天數/件數/平均值
				total = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobForTotalStatus());
				if(total != null && total.size() > 0){
					Map<String,Object> totalData = null;
					Map<String,String> data = null;
					double sum = 0.0;
					java.util.Iterator<Map<String,Object>> it = total.iterator();
					while(it.hasNext()){
						totalData = it.next();
						data = new HashMap<String,String>();
						data.put("type", Common.get(totalData.get("typeName")));
						data.put("workDay", Common.get(totalData.get("workday")));
						data.put("num", Common.get(totalData.get("num")));
						data.put("averageDay", Common.get(totalData.get("averageDay")));
						dataList.add(data);
						sum += Double.parseDouble(Common.get(totalData.get("averageDay"))) ;
					}
					//總計至小數點第二位
					java.text.DecimalFormat df = new java.text.DecimalFormat("#0.00");
					parameters.put("sum", df.format(sum));
				}
				report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
			}else{
				throw new Exception("查無資料，請變更查詢條件！");
			}
		}catch( Exception e ) {
			e.printStackTrace();
	        if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(e.getMessage());
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
	        return null ;
		} // end catch
		
		return report;
    } // doPrintfPdf()
	
	//---------------------functions append SQL strings bellow------------------------------------
	/** 收文總件數 與 辦結總件數 */
	public SQLJob doAppendSQLJobForTotalReceive(String dateStart, String dateEnd) {
		SQLJob sqljob = new SQLJob();
		//收文總件數
		sqljob.appendSQL("select count(1) as total from cedb1023 a join cedb1000 b on a.prefix_no = b.prefix_no");
		sqljob.appendSQL("where b.receive_date between ? and ?");
		sqljob.appendSQL("and LENGTH(TRIM(TRANSLATE(b.prefix_no, '0123456789', ' '))) is null");
		sqljob.appendSQL("union all");
		//辦結總件數
		sqljob.appendSQL("select count(1) as total from cedb1023 a join cedb1000 b on a.prefix_no = b.prefix_no");
		sqljob.appendSQL("where b.receive_date between ? and ?");
		sqljob.appendSQL("and LENGTH(TRIM(TRANSLATE(b.prefix_no, '0123456789', ' '))) is null");
		sqljob.appendSQL("and (select count(1) from cedb1010 where prefix_no = a.prefix_no and process_status in ('8','E') ) > 0");
		sqljob.addParameter(dateStart);
		sqljob.addParameter(dateEnd);
		sqljob.addParameter(dateStart);
		sqljob.addParameter(dateEnd);
		if(logger.isInfoEnabled()) logger.info(sqljob);
	    return sqljob ;
	} // doAppendSQLJobForTotalReceive()

	/** 流程的工作天數/件數/平均值 */
	public SQLJob doAppendSQLJobForTotalStatus() {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("select");
		sqljob.appendSQL("  substr(type, 2) as typeName");
		sqljob.appendSQL("  ,sum(work_day) as workday");
		sqljob.appendSQL("  ,count(1) as num");
		sqljob.appendSQL("  ,round(sum(work_day)/count(1),2) as averageDay");
		sqljob.appendSQL("from (");
		sqljob.appendSQL("  select ");
		sqljob.appendSQL("    a.prefix_no");
		sqljob.appendSQL("    ,decode(process_status,'2','1收文登打','3','2等待分文','4','3承辦決行','5','3承辦決行','6','4發文登打','7','4發文登打','8','5發文') as type");
		sqljob.appendSQL("    ,sum(a.work_day) as work_day");
		sqljob.appendSQL("   from eicm.cedb1010 a inner join eicm.cedb1000 b on b.prefix_no = a.prefix_no ");
		sqljob.appendSQL("  where b.receive_date between ");
		sqljob.appendSQL(Common.sqlChar(getQ_DateStart()));
		sqljob.appendSQL("    and ");
		sqljob.appendSQL(Common.sqlChar(getQ_DateEnd()));
		sqljob.appendSQL("    and process_status in ('2','3','4','5','6','7','8')");
		sqljob.appendSQL("    and LENGTH(TRIM(TRANSLATE(b.prefix_no, '0123456789', ' '))) is null");
		sqljob.appendSQL("  group by a.prefix_no, decode(process_status,'2','1收文登打','3','2等待分文','4','3承辦決行','5','3承辦決行','6','4發文登打','7','4發文登打','8','5發文')");
		sqljob.appendSQL(")");
		sqljob.appendSQL("group by type");
		sqljob.appendSQL("order by type");
		if(logger.isInfoEnabled()) logger.info(sqljob);
		return sqljob;
	}

	public String checkForOutputPdf(String dateStart, String dateEnd) {
		List<Map<String,Object>> total = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSQLJobForTotalReceive(dateStart, dateEnd));
		if ( total != null && total.size() > 0 )                                                             
			return "ok";
		else
			return "查無資料，請變更查詢條件！";
	} 
	
	public void doFillWithEmptyValue() {
		this.setReceiveDate("");
		this.setTotalReceive("");
		this.setTotalComplete("");
		this.setWorkDayReceive("");
		this.setNumReceive("");
		this.setAverageReceive("");
		this.setWorkDayWait("");
		this.setNumWait("");
		this.setAverageWait("");
 		this.setWorkDayDeside("");
 		this.setNumDeside("");
 		this.setAverageDeside("");
 		this.setWorkDayClose("");
 		this.setNumClose("");
 		this.setAverageClose("");
 		this.setWorkDayEnd("");
 		this.setNumEnd("");
 		this.setAverageEnd("");
	} // doFillWithEmptyValue()
	
} // PRE4007