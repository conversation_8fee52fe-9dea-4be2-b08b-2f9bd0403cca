package com.kangdainfo.tcfi.lucene.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.lucene.bo.IndexId;

public class IndexIdDao extends BaseDaoJdbc implements RowMapper<IndexId> {

	private static final String SQL_queryKind2ByBanNo = "SELECT B.PREFIX_NO||'_'||A.SEQ_NO as ID FROM CEDB1001 A INNER JOIN CEDB1000 B on B.PREFIX_NO = A.PREFIX_NO AND B.BAN_NO = ?";
	public List<IndexId> queryKind2ByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_queryKind2ByBanNo);
		sqljob.addParameter(banNo);
    	if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_queryKind3ByBanNo = "SELECT B.PREFIX_NO as ID FROM CEDB1000 B WHERE B.BAN_NO = ?";
	public List<IndexId> queryKind3ByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_queryKind3ByBanNo);
		sqljob.addParameter(banNo);
    	if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_queryKind2ByPrefixNo = "SELECT B.PREFIX_NO||'_'||A.SEQ_NO as ID FROM CEDB1001 A INNER JOIN CEDB1000 B on B.PREFIX_NO = A.PREFIX_NO AND B.PREFIX_NO = ?";
	public List<IndexId> queryKind2ByPrefixNo(String prefixNo) {
		if(null==prefixNo || "".equals(prefixNo)) return null;
		SQLJob sqljob = new SQLJob(SQL_queryKind2ByPrefixNo);
		sqljob.addParameter(prefixNo);
    	if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public IndexId mapRow(ResultSet rs, int index) throws SQLException {
		IndexId obj = null;
		if(null!=rs) {
			obj = new IndexId();
			obj.setId(Common.get(rs.getString("ID")));
		}
		return obj;
	}

}