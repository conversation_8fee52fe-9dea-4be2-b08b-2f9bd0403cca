<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.test.SameNameCompare">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(c1,c2,c3,c4,c5,c6,c7){
	form1.prefixNo.value=c1;
	form1.seqNo.value=c2;
	form1.companyName.value=c3;
	form1.sameSeqNo.value=c4;
	form1.samePrefixNo.value=c5;
	form1.sameBanNo.value=c6;
	form1.sameCompanyName.value=c7;
}
function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();showErrorMsg('<%=obj.getErrorMsg()%>');">

<form id="form1" name="form1" method="post" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:400px;height:250px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">預查編號：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_prefixNo" size="10" maxlength="10" value="<%=obj.getQ_prefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getPrefixNo()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">預查編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="prefixNo" value="<%=obj.getPrefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">序號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="seqNo" value="<%=obj.getSeqNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">公司名稱：</td>
		<td class="td_form_white">
			<input class="field cmex" type="text" name="companyName" value="<%=obj.getCompanyName()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">同名的序號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="sameSeqNo" value="<%=obj.getSameSeqNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">同名預查編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="samePrefixNo" value="<%=obj.getSamePrefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">同名公司統編：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="sameBanNo" value="<%=obj.getSameBanNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">同名公司名稱：</td>
		<td class="td_form_white">
			<input class="field cmex" type="text" name="sameCompanyName" value="<%=obj.getSameCompanyName()%>">
		</td>
	</tr>
	</table>
	</div>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" href="#">預查編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">序號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">公司名稱</a></th>
		<th class="listTH"><a class="text_link_w" href="#">同名的序號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">同名預查編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">同名公司統編</a></th>
		<th class="listTH"><a class="text_link_w" href="#">同名公司名稱</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true, true, true, true, true, true, true};
	boolean displayArray[] = {true, true, true, true, true, true, true};
	String[] alignArray = {"center","center","left","center","center","center","left"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>