<%@ page import="com.kangdainfo.sys.common.Constants"%>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.util.TcfiView"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>

<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));

try {
	Gson gson = new GsonBuilder().create();
	List<Cedb2002> cedb2002s = ServiceGetter.getInstance().getApproveService().getCedb2002ByBanNo(q);
	HashMap map = new HashMap();
	for(Cedb2002 cedb2002 : cedb2002s) {
		map.put(cedb2002.getBusiItemNo(), cedb2002.getBusiItem());
	}
	out.write(gson.toJson(map));
} catch (Exception e) {
	e.printStackTrace();
}
%>