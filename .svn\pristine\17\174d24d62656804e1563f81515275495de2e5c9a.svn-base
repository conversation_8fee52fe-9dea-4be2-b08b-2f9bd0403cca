
/*
 * 
 */

package tw.org.moea.online.oss;

import java.net.URL;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;

/**
 * This class was generated by Apache CXF 2.2.5
 * Mon Jun 17 16:05:27 CST 2013
 * Generated source version: 2.2.5
 * 
 */


@WebServiceClient(name = "CaseStatusService", 
                  wsdlLocation = "http://127.0.0.1:8080/oss/ossws/UpdateCaseStatusService?wsdl",
                  targetNamespace = "http://tw/org/moea/online/oss") 
public class CaseStatusService extends Service {

    public static final URL WSDL_LOCATION;
    public static final QName SERVICE = new QName("http://tw/org/moea/online/oss", "CaseStatusService");
    public static final QName CaseStatusPort = new QName("http://tw/org/moea/online/oss", "CaseStatusPort");
    static {
        WSDL_LOCATION = null;
    }

    public CaseStatusService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public CaseStatusService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public CaseStatusService() {
        super(WSDL_LOCATION, SERVICE);
    }

    /**
     * 
     * @return
     *     returns CaseStatus
     */
    @WebEndpoint(name = "CaseStatusPort")
    public CaseStatus getCaseStatusPort() {
        return super.getPort(CaseStatusPort, CaseStatus.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns CaseStatus
     */
    @WebEndpoint(name = "CaseStatusPort")
    public CaseStatus getCaseStatusPort(WebServiceFeature... features) {
        return super.getPort(CaseStatusPort, CaseStatus.class, features);
    }

}
