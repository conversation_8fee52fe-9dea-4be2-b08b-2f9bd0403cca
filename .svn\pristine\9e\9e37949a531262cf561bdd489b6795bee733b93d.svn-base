package com.kangdainfo.tcfi.view.test;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.UmsMt;

/**
 * 查詢簡訊發送紀錄
 *
 */
public class QueryUmsMt extends SuperBean {

	/** 查詢條件 */
	private String q_prefixNo;
	/** 資料欄位 */
	private String umsNo;
	private String dstaddr;
	private String smbody;
	private String mtTime;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<UmsMt> objList = ServiceGetter.getInstance().getTestService().getUmsMtsByPrefixNo(q_prefixNo);			
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[4];
			for(UmsMt o : objList) {
				rowArray = new String[4];
				rowArray[0] = Common.get(o.getUmsNo());
				rowArray[1] = Common.get(o.getDstaddr());
				rowArray[2] = Common.get(o.getSmbody());
				rowArray[3] = Common.get(o.getMtTime());
				arrList.add(rowArray);
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {
		QueryUmsMt obj = this;
		UmsMt o = ServiceGetter.getInstance().getTestService().getUmsMtByUmsNo(umsNo);
		if (null!=o) {
	        obj.setUmsNo(o.getUmsNo());
	        obj.setDstaddr(o.getDstaddr());
	        obj.setSmbody(o.getSmbody());
	        obj.setMtTime(o.getMtTime());
		} else throw new Exception("查無該筆資料！");
		return obj;
	}

	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}

	public String getUmsNo() {return checkGet(umsNo);}
	public void setUmsNo(String s) {this.umsNo = checkSet(s);}
	public String getDstaddr() {return checkGet(dstaddr);}
	public void setDstaddr(String s) {this.dstaddr = checkSet(s);}
	public String getSmbody() {return checkGet(smbody);}
	public void setSmbody(String s) {this.smbody = checkSet(s);}
	public String getMtTime() {return checkGet(mtTime);}
	public void setMtTime(String s) {this.mtTime = checkSet(s);}

}