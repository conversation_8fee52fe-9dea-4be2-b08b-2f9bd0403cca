package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * VIEW
 * 公司股東董監事資料檔(CEDB2004)
 *
 */
public class Cedb2004 extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	/** 統一編號 */
	private String banNo;
	/** 核准設立/變更日期 */
	private String approveDate ;
	/** 序號 */
	private String seqNo ;
	/** 身分證字號/法人統一編號 */
	private String idNo ;
	/** 職稱代碼 */
	private String positionCode ;
	/** 職稱 */
	private String positionName ;
	/** 經理人註記 */
	private String managerCode ;
	/** 姓名(法人姓名) */
	private String name ;
	/** 外文姓名 */
	private String forName ;
	/** 國籍別代碼 */
	private String nativeCode ;
	/** 國籍別 */
	private String nativeName ;
	/** 郵遞區號 */
	private String zoneCode ;
	/** 地址 */
	private String address ;
	/** 外國地址 */
	private String forAddress ;
	/** 到職日期 */
	private String arriveDate ;
	/** 持有股份/出資額 */
	private String investAmt ;
	/** 責任種類 */
	private String respType ;
	/** 資料異動者 */
	private String updateUser ;
	/** 資料異動日期 */
	private String updateDate;
	/** 資料異動時間 */
	private String updateTime;
	/** 抵押股票 */
	private String hypothecStock ;
	/** 抵押日期 */
	private String hypothecDate ;
	/** 發行股票 */
	private String releaseStock ;
	/** 發行日期 */
	private String releaseDate ;
	/** 總抵押股票 */
	private String totalHypothecStock ;
	/** 移除日期 */
	private String removeDate ;
	/** 移除文號(字) */
	private String removeWord ;
	/** 移除文號(號) */
	private String removeNo ;
	/** 移除生效日期 */
	private String removeEffectiveDate ;
	/** 抵押生效日期 */
	private String hypothecGrantDate ;
	/** 抵押文號(字) */
	private String hypothecGrantWord ;
	/** 抵押文號(號) */
	private String hypothecGrantNo ;
	/** 取消日期 */
	private String cancelDate ;
	/** 取消文號(字) */
	private String cancelWord ;
	/** 取消文號(號) */
	private String cancelNo ;
	/** 取消設立法律 */
	private String cancelLaw ;
	
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String s) {this.approveDate = s;}
	public String getSeqNo() {return seqNo;}
	public void setSeqNo(String s) {this.seqNo = s;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String s) {this.idNo = s;}
	public String getPositionCode() {return positionCode;}
	public void setPositionCode(String s) {this.positionCode = s;}
	public String getPositionName() {return positionName;}
	public void setPositionName(String s) {this.positionName = s;}
	public String getManagerCode() {return managerCode;}
	public void setManagerCode(String s) {this.managerCode = s;}
	public String getName() {return name;}
	public void setName(String s) {this.name = s;}
	public String getForName() {return forName;}
	public void setForName(String s) {this.forName = s;}
	public String getNativeCode() {return nativeCode;}
	public void setNativeCode(String s) {this.nativeCode = s;}
	public String getNativeName() {return nativeName;}
	public void setNativeName(String s) {this.nativeName = s;}
	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String s) {this.zoneCode = s;}
	public String getAddress() {return address;}
	public void setAddress(String s) {this.address = s;}
	public String getForAddress() {return forAddress;}
	public void setForAddress(String s) {this.forAddress = s;}
	public String getArriveDate() {return arriveDate;}
	public void setArriveDate(String s) {this.arriveDate = s;}
	public String getInvestAmt() {return investAmt;}
	public void setInvestAmt(String s) {this.investAmt = s;}
	public String getRespType() {return respType;}
	public void setRespType(String s) {this.respType = s;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String s) {this.updateUser = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}
	public String getHypothecStock() {return hypothecStock;}
	public void setHypothecStock(String s) {this.hypothecStock = s;}
	public String getHypothecDate() {return hypothecDate;}
	public void setHypothecDate(String s) {this.hypothecDate = s;}
	public String getReleaseStock() {return releaseStock;}
	public void setReleaseStock(String s) {this.releaseStock = s;}
	public String getReleaseDate() {return releaseDate;}
	public void setReleaseDate(String s) {this.releaseDate = s;}
	public String getTotalHypothecStock() {return totalHypothecStock;}
	public void setTotalHypothecStock(String s) {this.totalHypothecStock = s;}
	public String getRemoveDate() {return removeDate;}
	public void setRemoveDate(String s) {this.removeDate = s;}
	public String getRemoveWord() {return removeWord;}
	public void setRemoveWord(String s) {this.removeWord = s;}
	public String getRemoveNo() {return removeNo;}
	public void setRemoveNo(String s) {this.removeNo = s;}
	public String getRemoveEffectiveDate() {return removeEffectiveDate;}
	public void setRemoveEffectiveDate(String s) {this.removeEffectiveDate = s;}
	public String getHypothecGrantDate() {return hypothecGrantDate;}
	public void setHypothecGrantDate(String s) {this.hypothecGrantDate = s;}
	public String getHypothecGrantWord() {return hypothecGrantWord;}
	public void setHypothecGrantWord(String s) {this.hypothecGrantWord = s;}
	public String getHypothecGrantNo() {return hypothecGrantNo;}
	public void setHypothecGrantNo(String s) {this.hypothecGrantNo = s;}
	public String getCancelDate() {return cancelDate;}
	public void setCancelDate(String s) {this.cancelDate = s;}
	public String getCancelWord() {return cancelWord;}
	public void setCancelWord(String s) {this.cancelWord = s;}
	public String getCancelNo() {return cancelNo;}
	public void setCancelNo(String s) {this.cancelNo = s;}
	public String getCancelLaw() {return cancelLaw;}
	public void setCancelLaw(String s) {this.cancelLaw = s;}

}