package com.kangdainfo.tcfi.loader;

/**
 * 身分證件別
 *
 */
public class SystemCode12Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_12";
	private static final String CODE_KIND = "12";//12:身分證件別

	//singleton
	private static SystemCode12Loader instance;
	public SystemCode12Loader() {
		if (SystemCode12Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode12Loader.instance);
		}
		SystemCode12Loader.instance = this;
	}
	public static SystemCode12Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}

}