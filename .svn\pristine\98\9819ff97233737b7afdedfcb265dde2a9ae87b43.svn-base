package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;


/** 有限合夥基本資料VO */
public class LmsmBussMain  extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	/** 主鍵 */
	private String tbpk;
	public String getTbpk() {return tbpk;}
	public void setTbpk(String s) {tbpk = s;}
	
	/** 機關代碼 */
    private String agencyCode;
	public String getAgencyCode() {return agencyCode;}
	public void setAgencyCode(String s) {this.agencyCode = s;}
	
	/** 機關代碼中文 */
    private String agencyName;
	public String getAgencyName() {return agencyName;}
	public void setAgencyName(String s) {this.agencyName = s;}

    /** 縣市機關代碼 */
    private String regUnitCode;
	public String getRegUnitCode() {return regUnitCode;}
	public void setRegUnitCode(String s) {this.regUnitCode = s;}
	
    /** 案件編號 */
    private String caseNo;
	public String getCaseNo() {return caseNo;}
	public void setCaseNo(String s) {this.caseNo = s;}
	
    /** 統一編號 */
    private String banNo;
	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}

    /** 有限合夥名稱 */
    private String lmsName;
	public String getLmsName() {return lmsName;}
	public void setLmsName(String s) {this.lmsName = s;}
    
	/** 現況代碼 */
    private String status;
	public String getStatus() {return status;}
	public void setStatus(String s) {this.status = s;}
	
	/** 現況中文 */
    private String statusName;
	public String getStatusName() {return statusName;}
	public void setStatusName(String s) {this.statusName = s;}
	
	/** 負責人 */
    private String repName;
	public String getRepName() {return repName;}
	public void setRepName(String s) {this.repName = s;}
	
	/** 最近一次異動核准日期 */
    private String lastChangeDate;
	public String getLastChangeDate() {return lastChangeDate;}
	public void setLastChangeDate(String s) {this.lastChangeDate = s;}
	
    private String dn;
	public String getDn() {return dn;}
	public void setDn(String s) {this.dn = s;}
	
    private String approveDate;
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String s) {this.approveDate = s;}
	
	private String approveNo;
	public String getApproveNo() {return approveNo;}
	public void setApproveNo(String s) {this.approveNo = s;}
	
    /** 登記資本額 */
    private String registerFunds;
	public String getRegisterFunds() {return registerFunds;}
	public void setRegisterFunds(String s) {this.registerFunds = s;}
	
	public String realFunds;
	public String getRealFunds() {return realFunds;}
	public void setRealFunds(String s) {this.realFunds = s;}
	
    private String capitalAmount;
	public String getCapitalAmount() {return capitalAmount;}
	public void setCapitalAmount(String s) {this.capitalAmount = s;}
	
    /** 組織型態 */
    private String organType;
	public String getOrganType() {return organType;}
	public void setOrganType(String s) {this.organType = s;}
	
    /** 組織型態中文 */
    private String organName;
	public String getOrganName() {return organName;}
	public void setOrganName(String s) {this.organName = s;}
	
    private String businessItemFlag;
	public String getBusinessItemFlag() {return businessItemFlag;}
	public void setBusinessItemFlag(String s) {this.businessItemFlag = s;}
    
    /** 營業項目(舊版) */
    private String oldBusinessItems;
	public String getOldBusinessItems() {return oldBusinessItems;}
	public void setOldBusinessItems(String s) {this.oldBusinessItems = s;}
	
    private String bregiId;
	public String getBregiId() {return bregiId;}
	public void setBregiId(String s) {this.bregiId = s;}
	
    /** 聯絡電話 */
    private String telno;
	public String getTelno() {return telno;}
	public void setTelno(String s) {this.telno = s;}

    private String areaCode;
	public String getAreaCode() {return areaCode;}
	public void setAreaCode(String s) {this.areaCode = s;}
	
    private String areaName;
	public String getAreaName() {return areaName;}
	public void setAreaName(String s) {this.areaName = s;}
	
    private String busiAddr;
	public String getBusiAddr() {return busiAddr;}
	public void setBusiAddr(String s) {this.busiAddr = s;}

	/** 經理人 */
    private String managerName;
	public String getManagerName() {return managerName;}
	public void setManagerName(String s) {this.managerName = s;}
	
    private String partnerName;
	public String getPartnerName() {return partnerName;}
	public void setPartnerName(String s) {this.partnerName = s;}
    
    private String postalcode;
	public String getPostalcode() {return postalcode;}
	public void setPostalcode(String s) {this.postalcode = s;}
    
    /** 停業起日 */
    private String restBegDate;
	public String getRestBegDate() {return restBegDate;}
	public void setRestBegDate(String restBegDate) {this.restBegDate = restBegDate;}

    /** 停業迄日 */
    private String restEndDate;
	public String getRestEndDate() {return restEndDate;}
	public void setRestEndDate(String restEndDate) {this.restEndDate = restEndDate;}	
	
    /** 廢止日期 */
    private String cancelAppDate;
    public String getCancelAppDate() {return cancelAppDate;}
	public void setCancelAppDate(String cancelAppDate) {this.cancelAppDate = cancelAppDate;}

	/** 廢止文號 */
	private String cancelAppNo;
	public String getCancelAppNo() {return cancelAppNo;}
	public void setCancelAppNo(String cancelAppNo) {this.cancelAppNo = cancelAppNo;}

	/** 營利事業登記證號府號 */
	private String bussWordNo;
	public String getBussWordNo() {return bussWordNo;}
	public void setBussWordNo(String bussWordNo) {this.bussWordNo = bussWordNo;}
	
	/** 僑外資 */
    private String investCode;
    public String getInvestCode() {return investCode;}
	public void setInvestCode(String s) {this.investCode = s;}

    /** 大陸資 */
    private String chinaCode;
	public String getChinaCode() {return chinaCode;}
	public void setChinaCode(String s) {this.chinaCode = s;}
	
	/** 總機構統編 */
	private String capiBanNo;
	public String getCapiBanNo() {return capiBanNo;}
	public void setCapiBanNo(String s) {this.capiBanNo = s;}
	
	/** 總機構名稱 */
	private String capiBussName;
	public String getCapiBussName() {return capiBussName;}
	public void setCapiBussName(String s) {this.capiBussName = s;}
	
	/** 分支機構(Y:是,N:否) */
	private String isBranch;
	public String getIsBranch() {return isBranch;}
	public void setIsBranch(String s) {this.isBranch = s;}
	
	/** 存續期間起 */
	private String liveBegDate;
	public String getLiveBegDate() {return liveBegDate;}
	public void setLiveBegDate(String s) {this.liveBegDate = s;}
	
	/** 存續期間訖 */
	private String liveEndDate;
	public String getLiveEndDate() {return liveEndDate;}
	public void setLiveEndDate(String s) {this.liveEndDate = s;}
	
	/** 存續期間訖 */
	private String lastChgNo;
	public String getLastChgNo() {return lastChgNo;}
	public void setLastChgNo(String s) {this.lastChgNo = s;}
	
	/** 約定解散事由 */
	private String agreedDisBand;
	public String getAgreedDisBand() {return agreedDisBand;}
	public void setAgreedDisBand(String s) {this.agreedDisBand = s;}
	
	private String prefixNo;
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
}
