package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 預查案件過程記錄檔(CEDB1010)
 *
 */
public class Cedb1010 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 承辦人員識別碼 */
	private String idNo;
	/** 處理日期 */
	private String processDate;
	/** 處理時間 */
	private String processTime;
	/** 案件流程代號 */
	private String processStatus;
	/** 工作日 */
	private float workDay;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	public String getProcessDate() {return processDate;}
	public void setProcessDate(String processDate) {this.processDate = processDate;}
	public String getProcessTime() {return processTime;}
	public void setProcessTime(String processTime) {this.processTime = processTime;}
	public String getProcessStatus() {return processStatus;}
	public void setProcessStatus(String processStatus) {this.processStatus = processStatus;}
	public float getWorkDay() {return workDay;}
	public void setWorkDay(float workDay) {this.workDay = workDay;}

}