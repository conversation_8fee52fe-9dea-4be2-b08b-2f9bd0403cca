<!DOCTYPE html>
<!--
程式目的：預查編號維護
程式代號：pre8008
撰寫日期：103.03.27
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8008">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8008" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = (java.util.ArrayList) obj.queryAll();
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8008)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setYearNo(obj.getYearNo());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if("deleteError".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE8008)obj.queryOne();
	}
}
objList = (java.util.ArrayList) obj.queryAll();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">




//二維陣列, 新增時, 設定預設值
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){

	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.addYearNo, "年度");    
	   
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;	
}

function init(){
	setDisplayItem("spanInsert,spanDelete,spanListPrint,spanListHidden","H");
}

function queryOne(yearNo){
    //alert(rcvNo);
    //form1.id.value = yearNo ;
	$('#yearNo').val(yearNo);
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
} 
</script>

</head>
<body topmargin="5" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();showErrorMsg('<%=obj.getErrorMsg()%>');">
<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='預查編號維護'/>
</c:import>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >
<table width="100%" cellspacing="0" cellpadding="0">
<!--Query區============================================================-->
  <tr>
    <td>
      <div id="queryContainer" style="width:600px;height:200px;display:none">
	  <iframe id="queryContainerFrame"></iframe>
	  <div class="queryTitle">查詢視窗</div>
	        <table class="queryTable"  border="1">
	          <tr>
                <td class="td_form">年度：</td>
                <td class="td_form_white"> 
                  <input class="field_Q" type="text" name="q_yearNo" size="30" maxlength="30" value="<%=obj.getQ_yearNo()%>">
                </td>
              </tr>
	          <tr>
		        <td class="queryTDInput" colspan="2" style="text-align:center;">
			      <input class="toolbar_default" type="submit" name="querySubmit" value="確　　定" >
			      <input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		        </td>
	          </tr>
	        </table>
      </div>
    </td>
  </tr>
<!--Toolbar區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr>
<td class="bgToolbar" style="text-align:left">
    <input type="hidden" id="yearNo" name = "yearNo" value="<%=obj.getYearNo()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" />
</td>
</tr>
</table>
<!--Form區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr>
<td class="bg" >
  <div id="formContainer" style="height:100%">
  <table class="table_form" width="90%" height="100%">  
       <tr>
            <td nowrap class="td_form" width="20%">年度：</td>
            <td nowrap class="td_form_white" width="30%">
              <input class="field" type="text" name="addYearNo" size="10" maxlength="10" value="<%=obj.getYearNo()%>" readonly>
            </td>  
        </tr>
        <tr>
            <td nowrap class="td_form" width="20%">預查文號：</td>
            <td nowrap colspan="3" class="td_form_white">
              <input class="field" type="text" name="prefixNo" size="50" maxlength="100" value="<%=obj.getPrefixNo()%>">
            </td>  
        </tr>       
  </table>
  </div>
</td>
</tr>
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>

<!--List區============================================================-->
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td nowrap class="bgList">
<div id="listContainer" height = "200">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH" ><a class="text_link_w">NO.</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">年度</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">預查文號</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false };
  boolean displayArray[] = {true,true };
  String[] alignArray = {"center", "center"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag()));
  %>
  </tbody>
</table>
</div>
</td></tr>
</table>
</table>	
</form>
</body>
</html>
