<!DOCTYPE html>
<!-- 
程式目的：營業項目代碼維護
程式代號：pre8011
程式日期：1030611
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8011">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8011" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8011)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}

if ("true".equals(obj.getQueryAllFlag())) {
	objList = (java.util.ArrayList) obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
//二維陣列, 新增時, 設定預設值
var insertDefault;

function init(){
	if ( form1.state.value == "init" )
		document.getElementById("listContainer").style.display = 'none';
	else
		document.getElementById("listContainer").style.display = '';
}

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkQuery();
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.itemCode,"營業項目代碼");
		alertStr += checkEmpty(form1.businessItem, "營業項目");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function queryOne(clickItemCode){
    form1.clickItemCode.value = clickItemCode;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function popWindow(no){
	if (no== 1)
		window.open("https://gcis.nat.gov.tw/cod/");
	else if (no==2) {
		if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
			alert("新增或修改狀態不允許變更限制條件，請先儲存資料");
		}else{
			if(form1.itemCode.value == "")	form1.clickItemCode.value = "";
			var tempString = form1.clickItemCode.value;
			if (tempString != "") {
				if(tempString.length == 7){
					var prop="";
					var width = window.screen.width/2;
					var height = window.screen.height/2;
					var top = (window.screen.height - height)/2;
					var left = (window.screen.width - width)/2;
					prop=prop+"width="+width+"px,height="+height+"px,";
					prop=prop+"top="+top+"px,";
					prop=prop+"left="+left+"px,";
					prop=prop+"scrollbars=0,resizable=1";
					var url = getVirtualPath() + "tcfi/pre/pre8011_1.jsp?itemCode="+tempString;
					window.open(url,'pre8011_1',prop);
				}else {
					alert("此營業項目類別不允許變更限制條件");
				}
			}else {
				alert("請先選擇一筆營業項目代碼");
			}
		}
	} 
}
</script>
</head>
<body topmargin="5" onload="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<!-- Query Area  -->
	<div id="queryContainer" style="width:470px;height:100px;display:none">
		<iframe id="queryContainerFrame"></iframe>
		<div class="queryTitle">查詢視窗</div>
		<table class="query_table" border="1">  
			<tr>
				<td class="td_form" width="30%">營業項目代碼：</td>
				<td class="td_form_white" width="70%" > 
					<input class="field_Q" type="text" name="q_itemCode" size="7" maxlength="12" value="<%=obj.getQ_itemCode()%>">
				</td>
			</tr>
			<tr>
				<td class="td_form">營業項目：</td>
				<td class="td_form_white"> 
					<input class="field_Q" type="text" name="q_businessItem" size="40" maxlength="40" value="<%=obj.getQ_businessItem()%>">
				</td>
			</tr>				
			<tr>
				<td class="td_form">特許事業許可&nbsp;&nbsp;&nbsp;&nbsp;<br>中央主管機關代碼：</td>
				<td class="td_form_white"> 
					<input class="field_Q" type="text" name="q_masterCode" size="12" maxlength="12" value="<%=obj.getQ_masterCode()%>">
				</td>
			</tr>
			<tr>
				<td class="queryTDInput" colspan="2" style="text-align:center;">
					<input class="toolbar_default" type="submit" name="querySubmit" value="確　　定" >
					<input class="toolbar_default" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
				</td>
			</tr>	
		</table>
	</div>
	
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE8011'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- 新增按鈕區 -->
<tr><td style="text-align:left">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="clickItemCode" name="clickItemCode" value="<%=obj.getClickItemCode()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- form Area  -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="20%">營業項目代碼：</td>
	        <td class="td_form_white" width="80%"> 
				<input class="field_P" type="text" name="itemCode" size="7" maxlength="7" value="<%=obj.getItemCode()%>" onblur="toUpper(this);">
				<input class="toolbar_default" type="button" followPK="false" id="itemSys" name="itemSys" value="營業項目代碼檢索系統" onClick="popWindow(1)">&nbsp;
				<input class="toolbar_default" type="button" followPK="false" id="itemRule" name="itemRule" value="營業項目規則" onClick="popWindow(2)">
	        </td>
	   </tr>
	   <tr>
			<td class="td_form">營業項目：</td>
			<td class="td_form_white"> 
				<input class="field" type="text" name="businessItem" size="40" maxlength="40" value="<%=obj.getBusinessItem()%>">
			</td>
		</tr>				
		<tr>
			<td class="td_form">中央目的事業主管機關：</td>
			<td class="td_form_white"> 
				<input class="field" type="text" name="masterCode" size="12" maxlength="12" value="<%=obj.getMasterCode()%>">
			</td>
		</tr>
		<tr>
			<td class="td_form">經濟部公告函號：</td>
	        <td class="td_form_white"> 
	           	日期：<input class="field" type="text" name="moeaPostDate" size="10" maxlength="7" value="<%=obj.getMoeaPostDate()%>">
	           <input class="field" type="text" name="moeaPostNo" size=15" maxlength="15" value="<%=obj.getMoeaPostNo()%>">號函
	        </td>
		</tr>	
		<tr>
			<td class="td_form">保留期限一年註記：</td>
	        <td class="td_form_white"> 
	        	<input class="field" type="checkbox" id="reserve365" name="reserve365" value="Y" <%="Y".equals( obj.getReserve365() )?"checked":""%>>
	        </td>
		</tr>
		<tr>
			<td class="td_form">異動人員：</td>
	        <td class="td_form_white"> 
	           <input class="field_RO" type="text" name="editID" size="10" maxlength="20" value="<%=obj.getEditID()%>">
	        </td>
		</tr>
		<tr>
			<td class="td_form">異動日期：</td>
	        <td class="td_form_white"> 
	           <input class="field_RO" type="text" name="editDate" size="10" maxlength="20" value="<%=obj.getEditDate()%>">
	        </td>
		</tr>	
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bg">
	<div id="listContainer" height = "200">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  	<thead id="listTHEAD">
	  	<tr>
	    	<th width="20%" style="text-align:left" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">營業項目代碼</a></th>
	    	<th width="50%" style="text-align:left" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">營業項目</a></th>
	    	<th width="30%" style="text-align:left" class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">中央目的事業主管機關</a></th>
	  	</tr>
  	</thead>
  	<tbody id="listTBODY">
  	<%
 		boolean primaryArray[] = {true,false,false};
  		boolean displayArray[] = {true,true,true};
  		String[] alignArray = {"left", "left","left"};
  		out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false, null, null, "", true, false));
  	%>
  	</tbody>
	</table>
	</div>
</td></tr>

</table>
</form>
</body>
</html>