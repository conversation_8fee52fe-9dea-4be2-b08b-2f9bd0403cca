<!-- 中推會造字: 進入點 -->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/home/<USER>"%>
<%@ page import="com.kangdainfo.common.util.Common"%>
<%@ taglib uri="http://granule.com/tags" prefix="g" %>

<%
String contextPath = Common.getCurrentContextPath();
%>

<g:compress>
	<!-- 顯示造字 START -->
	<!-- MODIFIED: 改為直接讀入ttf -->
	<%-- 	<script type="text/javascript" src="<%=contextPath%>/js/cmex/cmexwebfont.js"></script> --%>
	<!-- 顯示造字 END -->
	<!-- 輸入造字 START -->
	<script type="text/javascript" src="<%=contextPath%>/js/cmex/cmexwebime.js"></script>
	<!-- 輸入造字 END -->
</g:compress>

<script type="text/javascript">

	/** 初始化 */
	function initCmex() {
		//console.log('initCmex start');
		// 取得所有cmex class物件
		var ls = document.getElementsByClassName('cmex');
		if (ls && ls.length > 0) {
			for (var i = 0; i < ls.length; i++) {
				processCmex(ls[i]);
			}
		}
	}
	
	/** 初始化目標物件 */
	function processCmex(el) {
		if (el) {
			// 取得ID
			el.id = geneId(el);
			// 改為直接讀入ttf, 添加EUDC
			processFontFamily(el);
			// 分流
			if (el.tagName && ( el.tagName.toUpperCase() == 'INPUT' || el.tagName.toUpperCase() == 'TEXTAREA' )) {
				if (el.readOnly || el.disabled) {					
					// 唯讀
					// MODIFIED: 改為直接讀入ttf
					// processInputReadOnly(el);
				} else {					
					// 輸入
					processInput(el);
				}
			} else {
				// 顯示
				// MODIFIED: 改為直接讀入ttf
				// processText(el);
			}
		}
	}
	
	/**
	 * 產生物件ID
	 *
	 * 若物件已有id -> 沿用
	 * 若無  -> 創建亂數id
	 */
	function geneId(el) {
		return el.id ? el.id : "cmex_" + Math.ceil(Math.random() * 1000000);
	}
	
	/** 取得物件ID */
	function getId(el) {
		return escapeId(geneId(el));
	}
	
	/** 
	 * 物件ID跳脫字元
	 *
	 * jquery及document.querySelector處理含有特定符號(例如.)的id時, 需先跳脫
	 */
	function escapeId(rawId) {
		// regex可擴充
		return (rawId || '').replace(/\./g, '\\.');
	}
	
	/** 初始化目標輸入框物件 */
	function processInput(el) {
		if (el) {	
			// MODIFIED: 改為直接讀入ttf
			// 監聽輸入事件
			el.addEventListener("input", function(e) { cmexHandleOnInput(e) });
			// 建立開啟按鈕
			createOpenButton(el);
			// 輸入框取得中推會造字
			// cmexInputSubset(el);
		}
	}
	
	/** 初始化目標輸入框物件: 唯讀欄位 */
	function processInputReadOnly(el) {
		if (el) {	
			// 輸入框取得中推會造字
			cmexInputSubset(el);
		}
	}
	
	/** 初始化目標顯示物件 */
	function processText(el) {
		if (el) {		
			var id = getId(el);
			// subset僅給有text()的物件使用
			// 取得中推會造字
			subset("#" + el.id, "sung");
		}
	}
	
	/** 處理輸入事件 */
	function cmexHandleOnInput(e) {
		if (e && e.target) {
			// MODIFIED: 改為直接讀入ttf
			processFontFamily(e.target)
			// 必須不斷呼叫, 以取得中推會造字
			// cmexInputSubset(e.target);
		}
	}
	
	/** 輸入框取得中推會造字 */
	function cmexInputSubset(el) {
		if (el) {
			var id = getId(el);
			// 呼叫webFont_subset而非subset, 因subset僅給有text()的物件使用
			// 取得中推會造字
			// subset("#" + el.id, "sung");
			webFont_subset(el.value, "#" + id, "sung");
		}
	}
	
	/** 直接讀ttf, 需處理font-family */
	function processFontFamily(el) {
		if (el) {
			if (el.style && el.style.fontFamily) {
				// 去除cmex開頭的字型
				el.style.fontFamily = el.style.fontFamily.replace(/(^|\W)cmex(\w+)/g, '');
				// EUDC
				if (el.style.fontFamily.indexOf('EUDC') == -1) {					
					el.style.fontFamily = el.style.fontFamily + ', EUDC';
				}
			} else {
				el.style.fontFamily = 'EUDC';
			}
		}
	}
	
	/** 
	 * 建立全新的按鈕
	 */
	function createNewButton(btnId) {
		// 客製化: 按鈕的style
		btn = document.createElement('button');
		btn.setAttribute('id', btnId);
		btn.style.marginLeft = '3px';
		// change it: 自訂button style
		// btn.classList.add("myBtn");
		return btn;
	}
	
	/** 
	 * 建立開啟按鈕
	 *
	 * 參數: 需要添加按鈕的輸入框物件
	 */
	function createOpenButton(el) {
		if (el) {
			var inputId = geneId(el);
			var btnId = "cmexBtn_" + inputId;
			
			var btn = document.getElementById(btnId);
			if (btn != null) {
				// 已有按鈕
				// 刪除
				btn.parentNode.removeChild(btn);
			}
				
			// 全新的按鈕
			btn = createNewButton(btnId);
			// 按鈕文字
			btn.innerHTML = '造字';
			// 按下按鈕, 開啟輸入鍵盤
			btn.addEventListener("click", function(e) { cmexBtnHandleOnClick(e, inputId) });
			// 顯示到畫面
			el.after(btn);
		}
	}
	
	/** 按鈕事件: 開啟鍵盤 */
	function cmexBtnHandleOnClick(e, inputId) {
		if (e && e.target && inputId) {
	 		// sample 01. 生成網頁輸入法元件
	 		// 處理重複載入script問題??
	 		loadWebImeScript(function () {
	 			createWebIme('#' + escapeId(inputId));
	 		});
	 		// 建立關閉按鈕
			createCloseButton(document.getElementById(inputId));
		}
	}
	
	/** 
	 * 建立關閉按鈕
	 *
	 * 參數: 需要添加按鈕的輸入框物件
	 */
	function createCloseButton(el) {
		if (el) {
			var inputId = geneId(el);
			var btnId = "cmexBtn_" + inputId;
			
			var btn = document.getElementById(btnId);
			if (btn != null) {
				// 已有按鈕
				// 刪除
				btn.parentNode.removeChild(btn);
			}
			
			// 全新的按鈕
			btn = createNewButton(btnId);
			// 按鈕文字
			btn.innerHTML = '關閉造字';
			// 按下按鈕, 開啟輸入鍵盤
			btn.addEventListener("click", function(e) { cmexBtnHandleOnClose(e, inputId) });
			// 顯示到畫面
			el.after(btn);
			// 先停用
			btn.disabled = true;
			// 停頓後再啟用
			setTimeout(function () {
				btn.disabled = false;				
			}, 2000);
		}
	}
	
	/** 按鈕事件: 關閉鍵盤 */
	function cmexBtnHandleOnClose(e, inputId) {
		if (e && e.target && inputId) {
			// 原輸入框
			var input = document.getElementById(inputId);
			// 上級物件
			var pt = input.parentNode;
			
			// 處理圖示icon
			// next sibling
			var img = document.querySelector("#" + escapeId(inputId) + " + .imethodIcon");
			// 刪除
			if (img) {				
				pt.removeChild(img);
			}
			
			// 複製一份輸入框成為新物件
			var new_input = input.cloneNode(true);
			// 刪除無用class
			new_input.classList.remove("ui-keyboard-input");
			new_input.classList.remove("cht-custom-input");
			// 新物件取代舊物件
			pt.replaceChild(new_input, input);
			
			// 重新初始化輸入框
			processInput(new_input);
			// MODIFIED: 改為直接讀入ttf
			processFontFamily(new_input);
		}
	}
	
	// 開始
	initCmex();
</script>
