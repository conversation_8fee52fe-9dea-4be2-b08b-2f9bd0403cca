<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(telixNo)) {
		List<Map<String,Object>> datas = null;
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT A.SEQ_NO, A.COMPANY_NAME FROM EEDB3000 A WHERE A.TELIX_NO =? ORDER BY SEQ_NO");
		sqljob.addParameter(telixNo);
		datas = ServiceGetter.getInstance().getEedbGeneralQueryDao().queryForList(sqljob);
		if (null==datas || datas.isEmpty()) {
			//申請"所營事業變更"時, EEDB3000 會沒資料, 要補一筆
			sqljob = new SQLJob();
			sqljob.appendSQL("SELECT '01' AS SEQ_NO, COMPANY_NAME FROM EEDB1000 where TELIX_NO = ?");
			sqljob.addParameter(telixNo);
			datas = ServiceGetter.getInstance().getEedbGeneralQueryDao().queryForList(sqljob);
		}
		System.out.println(gson.toJson(datas));
		out.write(gson.toJson(datas));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>