<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8017">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8017" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true") ;
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8017)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		// obj.setRcvNo(obj.getRcvNo());
	}else{
		obj.setQueryAllFlag("false");
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
	if ("updateSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
	}
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if("deleteError".equals(obj.getState())){
		obj = (com.kangdainfo.tcfi.view.pre.PRE8017)obj.queryOne();
	}
}

if ( "true".equals(obj.getQueryAllFlag()) ) 
	objList = (java.util.ArrayList) obj.queryAll();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		//alertStr += checkQuery();
	}else if(form1.state.value=="insert" || form1.state.value=="insertError" || form1.state.value=="update" || form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.word, "字詞");	
	   alertStr += checkEmpty(form1.synonymWord, "同義詞");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	
	return true;	
}

function queryOne(id){
	//form1.id.value=id;
	$('#id').val(id);
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}
</script>
</head>
<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->
<div id="queryContainer" style="width:640px;height:140px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
        <td class="queryTDLable">字詞/同義詞：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_word" size="30" maxlength="40" value="<%=obj.getQ_word()%>">
        </td>
    </tr>
    <!-- 
	<tr>
		<td class="queryTDLable">同義詞：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_synonymWord" size="30" maxlength="40" value="<%= obj.getQ_synonymWord()%>">
		</td>
	</tr>
	 -->
	<tr>
		<td class="queryTDLable">詞源依據：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_source" size="30" maxlength="40" value="<%= obj.getQ_source()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">			
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8017'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" style="text-align:left">
	<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form" width="15%"><font color="red">*</font>字詞：</td>		
		<td class="td_form_white" width="85%">
			<input class="field" type="text" id="word" name="word" size="40" maxlength="50" value="<%=obj.getWord()%>" />
		</td>
	</tr>
	<tr>
		<td class="td_form"><font color="red">*</font>同義詞：</td>
		<td class="td_form_white">
			<input class="field" type="text" id="synonymWord" name="synonymWord" size="40" maxlength="50" value="<%=obj.getSynonymWord()%>" />
		</td>
	</tr>
	<tr>
		<td class="td_form"><font color="red"></font>詞源依據：</td>
		<td class="td_form_white">
			<input class="field" type="text" id="source" name="source" size="40"  maxlength="100" value="<%=obj.getSource()%>">
		</td>
	</tr>
	<tr>
	  	<td nowrap class="td_form">異動資訊：</td>
	  	<td nowrap class="td_form_white"> [
	    	<input class="field_RO" type="text" name="editID" size="10" value="<%=obj.getEditID()%>">
	    	/
	    	<input class="field_RO" type="text" name="editDate" size="7" value="<%=obj.getEditDate()%>">
	    	] 
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!-- PAGE AREA -->

<!-- PAGE AREA -->

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>		
		<th class="listTH" width="8%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">序號</a></th>
		<th class="listTH" width="20%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">字詞</a></th>
		<th class="listTH" width="20%"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">同義詞</a></th>		
		<th class="listTH" width="27%"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">詞源依據</a></th>
		<th class="listTH" width="15%"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">異動人員</a></th>
		<th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">異動日期</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true,false,false,false,false,false};
	boolean displayArray[] = {false,true,true,true,true,true};
	String[] alignArray = {"center","left","left","left","center","center",};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>