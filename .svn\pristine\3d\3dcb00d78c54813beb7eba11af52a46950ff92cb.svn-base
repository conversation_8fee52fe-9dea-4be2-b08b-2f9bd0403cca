package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 系統公告檔(SYSTEM_NEWS)
 *
 */
public class SystemNews extends BaseModel {
	private static final long serialVersionUID = 1L;

	/** 主鍵值 */
	private Integer id;
	/** 主旨 */
	private String subject;
	/** 內容 */
	private String content;
	/** 開始日期 */
	private String startDate;
	/** 結束日期 */
	private String endDate;
	/** 重要公告註記(Y:重要,N:一般) */
	private String isImportant;
	/** 是否啟用(Y:啟動,N:停用 ) */
	private String enable;
	/** 異動人員 */
	private String modIdNo;
	/** 異動日期 */
	private String modDate;
	/** 異動時間 */
	private String modTime;

	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}

	/** 主旨 */
	public String getSubject() {return subject;}
	/** 主旨 */
	public void setSubject(String subject) {this.subject = subject;}

	/** 內容 */
	public String getContent() {return content;}
	/** 內容 */
	public void setContent(String content) {this.content = content;}

	/** 開始日期 */
	public String getStartDate() {return startDate;}
	/** 開始日期 */
	public void setStartDate(String startDate) {this.startDate = startDate;}

	/** 結束日期 */
	public String getEndDate() {return endDate;}
	/** 結束日期 */
	public void setEndDate(String endDate) {this.endDate = endDate;}

	/** 重要公告註記(Y:重要,N:一般) */
	public String getIsImportant() {return isImportant;}
	/** 重要公告註記(Y:重要,N:一般) */
	public void setIsImportant(String isImportant) {this.isImportant = isImportant;}

	/** 是否啟用(Y:啟動,N:停用 ) */
	public String getEnable() {return enable;}
	/** 是否啟用(Y:啟動,N:停用 ) */
	public void setEnable(String enable) {this.enable = enable;}

	/** 異動人員 */
	public String getModIdNo() {return modIdNo;}
	/** 異動人員 */
	public void setModIdNo(String modIdNo) {this.modIdNo = modIdNo;}

	/** 異動日期 */
	public String getModDate() {return modDate;}
	/** 異動日期 */
	public void setModDate(String modDate) {this.modDate = modDate;}

	/** 異動時間 */
	public String getModTime() {return modTime;}
	/** 異動時間 */
	public void setModTime(String modTime) {this.modTime = modTime;}

}