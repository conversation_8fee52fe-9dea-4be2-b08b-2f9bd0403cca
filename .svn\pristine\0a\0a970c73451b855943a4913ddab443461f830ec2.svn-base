<!--
程式目的：申請案郵寄資料列印
程式代號：pre2005
撰寫日期：103.05.26
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->

<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2005" />
</jsp:include>

<%
if ("queryAll".equals(obj.getState())) {
	// obj.setQueryAllFlag("true") ;
} // end if
else if ("queryOne".equals(obj.getState())) {
	// obj = (com.kangdainfo.tcfi.view.pre.PRE4005)obj.queryOne();
} //end else if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE2005.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
} // end else if

if ( "true".equals(obj.getQueryAllFlag()) ) 
	//  objList = (java.util.ArrayList) obj.queryAll();
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function checkField(){
	var alertStr="";
	form1.q_postStart.style.backgroundColor="";
	form1.q_postEnd.style.backgroundColor="";
	form1.q_prefixStart.style.backgroundColor="";
	form1.q_prefixEnd.style.backgroundColor="";
	if ( form1.q_type[0].checked ) {
		alertStr += checkEmpty(form1.q_prefixStart,"預查編號起");
		alertStr += checkEmpty(form1.q_prefixEnd,"預查編號迄");
	}
	else if ( form1.q_type[1].checked ) {
		alertStr += checkEmpty(form1.q_postStart,"掛號編號起");
		alertStr += checkEmpty(form1.q_postEnd,"掛號編號迄");
	}
	else {
		alertStr += "請先點選查詢種類。";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	// beforeSubmit();
	return true;
}

function changeRadio(type) {
    var typeRadio = document.getElementsByName("q_type");
    if(type == 1) {
       typeRadio[0].checked = true;
       form1.q_postStart.value="";
       form1.q_postEnd.value="";
    }
    if(type == 2) {
       typeRadio[1].checked = true;
       form1.q_prefixStart.value="";
       form1.q_prefixEnd.value="";
    }
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE2005_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doClear":
				form1.q_prefixStart.value = "";
				form1.q_prefixEnd.value = "";
				form1.q_postStart.value = "";
				form1.q_postEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var type;
		var printType = form1.q_printType.value;
		var start;
		var end;
		if ( form1.q_type[0].checked ) {
			type = "prefix";
			start = form1.q_prefixStart.value;
			end = form1.q_prefixEnd.value;
		} 
		else {
			type = "post";
			start = form1.q_postStart.value;
			end = form1.q_postEnd.value;
		}
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre2005.jsp?start='+start+'&end='+end+'&type='+type+'&printType='+printType);
		if ( x == 'ok'  ) {
			document.getElementById("ERRMSG").innerHTML = "請等候報表產製...";
			whatButtonFireEvent("doPrintPdf");
		} 	
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}

function keyDown() {
	if ( event.keyCode == 13 ) {
		doSomeCheck();
	}
}


</script>
</head>
<body topmargin="5" onload="showErrorMsg('<%=obj.getErrorMsg()%>');">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE2005'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR -->
<tr><td>
	<table class="table_form" width="100%">
		<tr>
			<td>
	<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表產製" onClick="doSomeCheck()">
	<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>
	</table>
</td></tr>


<tr><td class="bg" >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
  	  <tr>  
		  <td class="td_form" >列印清單：</td>
          <td class="td_form_white" width="85%">
            <select id="q_printType" name="q_printType" value="<%=obj.getQ_printType()%>">
            <option value="01" selected="selected">特約-普通掛號函件執據</option><!-- 修正於2024/01/23 修改掛號規則 -->
            <option value="02">特約-限時掛號函件執據</option><!-- 修正於2024/01/23 修改掛號規則 -->
            <option value="06">普通掛號函件執據</option><!-- 修正於2024/01/23 修改掛號規則 -->
            <option value="07">限時掛號函件執據</option><!-- 修正於2024/01/23 修改掛號規則 -->
            <option value="03">公文(有掛號號碼)</option></select>
          </td>
	   </tr>
       <tr>
         <td class="td_form" width="10%">
		    <input type="radio" name="q_type" value="prefix"> 預查編號:
		</td>
		<td class="td_form_white">
			<input class="field_Q" type="text" name="q_prefixStart" size="15" maxlength="9" value="<%=obj.getQ_prefixStart()%>" onfocus="changeRadio(1);" onKeyDown="keyDown()">~
			<input class="field_Q" type="text" name="q_prefixEnd" size="15" maxlength="9" value="<%=obj.getQ_prefixEnd()%>" onfocus="changeRadio(1);" onKeyDown="keyDown()">
	    </td>
	   </tr>	
	   <tr>
         <td class="td_form" width="200">
		    <input type="radio" name="q_type" value="post"> 掛號編號:
		</td>
		<td class="td_form_white">
			<input class="field_Q" type="text" name="q_postStart" size="15" maxlength="10" value="<%=obj.getQ_postStart()%>" onfocus="changeRadio(2);">~
			<input class="field_Q" type="text" name="q_postEnd" size="15" maxlength="10" value="<%=obj.getQ_postEnd()%>" onfocus="changeRadio(2);">
	    </td>
	   </tr>	
  </table>
  </div>
  <c:import url="../common/msgbar.jsp">
  	<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
  </c:import>  
</td>
</tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:center;">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
<!-- 新增按鈕區 -->


</table>
</form>
</body>
</html>