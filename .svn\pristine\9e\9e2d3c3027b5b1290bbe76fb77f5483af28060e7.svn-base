-- Create table
create table EICM.INDEX_LOG_H
(
     ID NUMBER,
     WS_ID VARCHAR2(20),  
     PARAM1 VARCHAR2(20),
     PARAM2 VARCHAR2(20),
     PARAM3 VARCHAR2(20),
     EXECUTE_DATE VARCHAR2(20),
     CREATE_DATE VARCHAR2(20),
     CREATE_USER VARCHAR2(20),
     START_TIME VARCHAR2(20),
     FINISH_TIME VARCHAR2(20),
     STATUS char(1),
     REMARK VARCHAR2(2000)
);

-- Add comments to the table 
comment on table EICM.INDEX_LOG_H is '索引紀錄歷史檔';
-- Add comments to the columns 
comment on column EICM.INDEX_LOG_H.WS_ID is '執行WS的程式代碼';
comment on column EICM.INDEX_LOG_H.PARAM1 is '參數1';
comment on column EICM.INDEX_LOG_H.PARAM2 is '參數2';
comment on column EICM.INDEX_LOG_H.PARAM3 is '參數3';
comment on column EICM.INDEX_LOG_H.EXECUTE_DATE is '預計執行日期(預設空值表示立即執行;有設定日期,要比對日期小於等於sysDate才執行)';
comment on column EICM.INDEX_LOG_H.CREATE_DATE is '建立日期';
comment on column EICM.INDEX_LOG_H.CREATE_USER is '建立者';
comment on column EICM.INDEX_LOG_H.START_TIME is '開始時間';
comment on column EICM.INDEX_LOG_H.FINISH_TIME is '完成時間';
comment on column EICM.INDEX_LOG_H.STATUS is '狀態(0:待執行/1:執行中/2:執行成功/3:執行失敗)';
comment on column EICM.INDEX_LOG_H.REMARK is '備註';      

-- Create the synonym 
create or replace synonym EICM4AP.INDEX_LOG_H for EICM.INDEX_LOG_H;
create or replace synonym EICM4CMPY.INDEX_LOG_H for EICM.INDEX_LOG_H;
create or replace synonym EICM4PREFIX.INDEX_LOG_H for EICM.INDEX_LOG_H;

-- Grant/Revoke object privileges 
grant all on EICM.INDEX_LOG_H to EICM4AP;
