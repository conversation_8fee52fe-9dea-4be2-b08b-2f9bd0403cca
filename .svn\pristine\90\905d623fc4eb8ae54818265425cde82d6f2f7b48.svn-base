package com.kangdainfo.tcfi.loader;

import java.util.HashMap;
import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc000Dao;

public class Cedbc000CodeLoader extends BaseLoader {
	private static final String CACHE_NAME = "CACHE_NAME_CEDBC000";
	private static HashMap<String, String> C_ID_NAME = new HashMap<String, String>();

	// singleton
	private static Cedbc000CodeLoader instance;

	public Cedbc000CodeLoader() {
		if (Cedbc000CodeLoader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
							+ "is designed to be a Singleton, the instance already exist:"
							+ Cedbc000CodeLoader.instance);
		}
		Cedbc000CodeLoader.instance = this;
	}

	
	public static Cedbc000CodeLoader getInstance() {
		return instance;
	}

	public String getNameById(String id) {
		return C_ID_NAME.get(id) == null ? id : C_ID_NAME.get(id);
	}
	/**
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Cedbc000> getCedbc000Codes() {
		Object object = getServletContext().getAttribute(CACHE_NAME);
		if (object == null)
			reload();
		
		C_ID_NAME.clear();
		List<Cedbc000> cedbc000s = (List<Cedbc000>) object;
        Cedbc000 cedbc000;
        
		for (int i = 0; i < cedbc000s.size(); i++) {
            cedbc000 = (Cedbc000) (cedbc000s.get(i));
            C_ID_NAME.put(cedbc000.getIdNo(), cedbc000.getStaffName());
        }
		
		return (List<Cedbc000>) getServletContext().getAttribute(CACHE_NAME);
	}

	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	/** 重新載入 */
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(CACHE_NAME, cedbc000Dao.queryAll());
		getCedbc000Codes();
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}
	
	private Cedbc000Dao cedbc000Dao;
	public Cedbc000Dao getCedbc000Dao() {return cedbc000Dao;}
	public void setCedbc000Dao(Cedbc000Dao dao) {this.cedbc000Dao = dao;}

}