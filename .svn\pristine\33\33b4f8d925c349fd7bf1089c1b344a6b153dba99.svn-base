--DROP TABLE EICM.SAMENAME_QUEUE_H;
-- Create table
CREATE TABLE EICM.SAMENAME_QUEUE_H (
	ID INTEGER,
	PREFIX_NO VARCHAR2(18),
	REMARK VARCHAR2(200),
	PROCESS_TIME NUMBER(10),
	STATUS char(1),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);

-- Add comments to the table 
comment on table EICM.SAMENAME_QUEUE_H is '同名公司比對排程歷史檔';
-- Add comments to the columns 
comment on column EICM.SAMENAME_QUEUE_H.ID is '主鍵值';
comment on column EICM.SAMENAME_QUEUE_H.PREFIX_NO is '預查編號';
comment on column EICM.SAMENAME_QUEUE_H.REMARK is '處理結果備註';
comment on column EICM.SAMENAME_QUEUE_H.PROCESS_TIME is '處理時間(微秒)';
comment on column EICM.SAMENAME_QUEUE_H.STATUS is '狀態(0:待執行/1:執行中/2:執行成功/3:執行失敗)';
comment on column EICM.SAMENAME_QUEUE_H.MOD_ID_NO is '異動人員';
comment on column EICM.SAMENAME_QUEUE_H.MOD_DATE is '異動日期';
comment on column EICM.SAMENAME_QUEUE_H.MOD_TIME is '異動時間';

-- Create the synonym 
create or replace synonym EICM4AP.SAMENAME_QUEUE_H for EICM.SAMENAME_QUEUE_H;
create or replace synonym EICM4CMPY.SAMENAME_QUEUE_H for EICM.SAMENAME_QUEUE_H;
create or replace synonym EICM4PREFIX.SAMENAME_QUEUE_H for EICM.SAMENAME_QUEUE_H;

-- Grant/Revoke object privileges 
grant all on EICM.SAMENAME_QUEUE_H to EICM4AP;
