package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

public class LoginLog  extends BaseModel{
	private static final long serialVersionUID = 1L;
	
	/** 主鍵值 */
	private Integer id;
	/** 使用者帳號 */
	private String loginId;
	/** 使用者名稱 */
	private String loginName;
	/** 登入日期 */
	private String loginDate;
	/** 登入時間 */
	private String loginTime;
	/** 連線IP */
	private String loginIp;
	/** 登入狀態 */
	private String loginStatus;
	
	/** 主鍵值 */
	public Integer getId() {return id;}
	/** 主鍵值 */
	public void setId(Integer id) {this.id = id;}
	/** 使用者帳號 */
	public String getLoginId() {return loginId;}
	/** 使用者帳號 */
	public void setLoginId(String loginId) {this.loginId = loginId;}
	/** 使用者名稱 */
	public String getLoginName() {return loginName;}
	/** 使用者名稱 */
	public void setLoginName(String loginName) {this.loginName = loginName;}
	/** 登入日期 */
	public String getLoginDate() {return loginDate;}
	/** 登入日期 */
	public void setLoginDate(String loginDate) {this.loginDate = loginDate;}
	/** 登入時間 */
	public String getLoginTime() {return loginTime;}
	/** 登入時間 */
	public void setLoginTime(String loginTime) {this.loginTime = loginTime;}
	/** 連線IP */
	public String getLoginIp() {return loginIp;}
	/** 連線IP */
	public void setLoginIp(String loginIp) {this.loginIp = loginIp;}
	/** 登入狀態 */
	public String getLoginStatus() {return loginStatus;}
	/** 登入狀態 */
	public void setLoginStatus(String loginStatus) {this.loginStatus = loginStatus;}
}
