<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

	<!-- 系統公告 -->
	<bean id="systemNewsLoader" class="com.kangdainfo.tcfi.loader.SystemNewsLoader">
		<property name="systemNewsDao" ref="systemNewsDao" />
	</bean>
	<!-- 同音同義字 -->
	<bean id="cedbc058CodeLoader" class="com.kangdainfo.tcfi.loader.Cedbc058CodeLoader">
		<property name="cedbc058Dao" ref="cedbc058Dao" />
	</bean>
	<!-- 系統代碼(01:系統參數資料) -->
	<bean id="systemCode01Loader" class="com.kangdainfo.tcfi.loader.SystemCode01Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(02:使用者群組) -->
	<bean id="systemCode02Loader" class="com.kangdainfo.tcfi.loader.SystemCode02Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(03:取件方式) -->
	<bean id="systemCode03Loader" class="com.kangdainfo.tcfi.loader.SystemCode03Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(04:公司型態) -->
	<bean id="systemCode04Loader" class="com.kangdainfo.tcfi.loader.SystemCode04Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(05:核覆結果) -->
	<bean id="systemCode05Loader" class="com.kangdainfo.tcfi.loader.SystemCode05Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(06:案件狀態) -->
	<bean id="systemCode06Loader" class="com.kangdainfo.tcfi.loader.SystemCode06Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(07:郵寄類別) -->
	<bean id="systemCode07Loader" class="com.kangdainfo.tcfi.loader.SystemCode07Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(08:申登機關) -->
	<bean id="systemCode08Loader" class="com.kangdainfo.tcfi.loader.SystemCode08Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(09:馬上辦案由) -->
	<bean id="systemCode09Loader" class="com.kangdainfo.tcfi.loader.SystemCode09Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(10:公司狀態) -->
	<bean id="systemCode10Loader" class="com.kangdainfo.tcfi.loader.SystemCode10Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(11:預查公司狀態) -->
	<bean id="systemCode11Loader" class="com.kangdainfo.tcfi.loader.SystemCode11Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(12:身分證件別) -->
	<bean id="systemCode12Loader" class="com.kangdainfo.tcfi.loader.SystemCode12Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(13:預查種類) -->
	<bean id="systemCode13Loader" class="com.kangdainfo.tcfi.loader.SystemCode13Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(14:展期原因) -->
	<bean id="systemCode14Loader" class="com.kangdainfo.tcfi.loader.SystemCode14Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 系統代碼(15:片語) -->
	<bean id="systemCode15Loader" class="com.kangdainfo.tcfi.loader.SystemCode15Loader">
		<property name="systemCodeDao" ref="systemCodeDao" />
	</bean>
	<!-- 人員基本資料 -->
	<bean id="cedbc000CodeLoader" class="com.kangdainfo.tcfi.loader.Cedbc000CodeLoader">
		<property name="cedbc000Dao" ref="cedbc000Dao" />
	</bean>
	<!-- 系統功能 -->
	<bean id="functionMenuLoader" class="com.kangdainfo.tcfi.loader.FunctionMenuLoader">
		<property name="functionMenuDao" ref="functionMenuDao" />
	</bean>
	<!-- 有限合夥代碼 ORG對照 --> <!-- 2024//04/16 新增 -->
	<bean id="lmsdCodemappingOrgLoader" class="com.kangdainfo.tcfi.loader.LmsdCodemappingOrgLoader">
		<property name="lmsdCodemappingDao" ref="lmsdCodemappingDao" />
	</bean>
	<!-- 有限合夥代碼 STAT對照 --> <!-- 2024//04/17 新增 -->
	<bean id="lmsdCodemappingStatLoader" class="com.kangdainfo.tcfi.loader.LmsdCodemappingStatLoader">
		<property name="lmsdCodemappingDao" ref="lmsdCodemappingDao" />
	</bean>
	<!-- 有限合夥申登機關代碼 --> <!-- 2024//04/17 新增 -->
	<bean id="lmsdRegUnitLoader" class="com.kangdainfo.tcfi.loader.LmsdRegUnitLoader">
		<property name="lmsdRegUnitDao" ref="lmsdRegUnitDao" />
	</bean>
</beans>