<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE4004"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String date = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("date")));
String idNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("idNo")));

try {
	if ( ( date != null && !"".equals(date) ) )
	{
		String checkResult = PRE4004.checkForjsp(date,idNo);
		if (null!=checkResult && !"".equals(checkResult)) {
			out.write(checkResult);
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>