--grant create any procedure,alter any procedure to eicm4ap;
--grant all on EICM4AP.INDEX_LOG to EICM;

CREATE OR REPLACE PROCEDURE EICM.Full_Text_Index (
	pBan_No			Varchar2	--統一編號
,	pPrefix_No		Varchar2	--預查編號
,	pCompany_Name	Nvarchar2	--公司名稱
,	pStatus_Code	Varchar2	--公司狀況
,	pReserve_Date	Varchar2	--保留期限
,	pApply_Name		Nvarchar2	--申請人
) Is
	textLen			PLS_Integer;	--原始字串總長度
	nTimes			PLS_Integer;	--切出這樣長度的子字串,迴圈共要跑幾次
	rCount			PLS_Integer;
	n				PLS_Integer;
	m				PLS_Integer;
	vCompanyName	Nvarchar2(60);
	vBanNo			Varchar2(10) := 'B';
	vPrefixNo		Varchar2(10) := 'P';
	vStr			Nvarchar2(5);
	vStrForCheck	Nvarchar2(200);
	stmt			Varchar2(2000);
	Type insTable Is Table Of Nvarchar2(60) Index By Binary_Integer;
	tab1 insTable;
Begin

	-- 判斷一定要有資訊的欄位
	If	pCompany_Name is null or (pBan_No is null and pPrefix_No is null) Then
		return;
	End If;
	-- 設定預設值
	If	pBan_No is not null Then
		vBanNo := pBan_No;
	End If;
	If	pPrefix_No is not null Then
		vPrefixNo := pPrefix_No;
	End If;

	--new prefix addon 20140505
	INSERT INTO EICM4AP.INDEX_LOG (WS_ID,PARAM1,EXECUTE_DATE,CREATE_DATE,CREATE_USER,STATUS,REMARK)
	VALUES ('WS10001',vBanNo,(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),(to_char(sysdate,'yyyyMMddHH24miss')-19110000000000),'SYS','0','From EICM.Full_Text_Index');

--	重建全部索引時, 略過檢查, onLine 建索引時才啟動以提昇效能
--	檢查這家公司是否已經存在？有無變更公司名稱
	If	vBanNo = 'B' Then	--如果沒有統編時,以預查編號為主
		Select /*+ rule*/ Count(1) Into rCount From Ftxt_Search_Mas Where Prefix_No=vPrefixNo;
		If	rCount>0 Then
			Select	/*+ rule*/ Company_Name,Status_Code Into vCompanyName, vStr
			From	Ftxt_Search_Mas Where Prefix_No=vPrefixNo;
			If	vCompanyName=Trim(pCompany_Name) Then	-- 此公司已經存在且公司名稱沒有變動
				If	NVL(vStr,'YY')<>NVL(pStatus_Code,'ZZ') Then	--更新公司狀態
					Update	Ftxt_Search_Mas Set Status_Code=pStatus_Code Where Prefix_No=vPrefixNo;
				End If;
				return;
			Else	-- 此公司已經存在 但 公司名稱有變動
				Delete /*+ rule*/ From Ftxt_Search_Mas Where Ban_No='B' and Prefix_No=vPrefixNo;
			End If;
		End If;
	Else
		Select /*+ rule*/ Count(1) Into rCount From Ftxt_Search_Mas Where Ban_No=vBanNo;
		If	rCount>0 Then
			Select	/*+ rule*/ Company_Name,Status_Code Into vCompanyName, vStr
			From	Ftxt_Search_Mas Where rownum=1 and Ban_No=vBanNo;
			If	vCompanyName=Trim(pCompany_Name) Then	-- 此公司已經存在且公司名稱沒有變動
				If	NVL(vStr,'YY')<>NVL(pStatus_Code,'ZZ') Then	--更新公司狀態
					Update	Ftxt_Search_Mas Set Status_Code=pStatus_Code Where Ban_No=vBanNo;
				End If;
				return;
			Else	-- 此公司已經存在 但 公司名稱有變動
				Delete /*+ rule*/ From Ftxt_Search_Mas Where Ban_No=vBanNo;
			End If;
		End If;
	End If;

	-- 記住原始公司名稱長度
	vCompanyName := Trim(pCompany_Name);
	textLen := Length(vCompanyName);
	-- 去掉組織名稱
	vCompanyName := Replace(vCompanyName,'股份有限公司');
	If	Length(vCompanyName)=textLen Then	--長度沒變
		vCompanyName := Replace(vCompanyName,'有限公司');
		If	Length(vCompanyName)=textLen Then	--長度沒變
			vCompanyName := Replace(vCompanyName,'無限公司');
			If	Length(vCompanyName)=textLen Then	--長度沒變
				vCompanyName := Replace(vCompanyName,'兩合公司');
			End If;
		End If;
	End If;
	textLen := NVL(Length(vCompanyName),0);	-- 要建索引的字串長度
	If	textLen<=1 Then		--有公司它的名字叫做 "有限公司" , 真是哇咧
		return;
	End If;
	-- 將要建索引的關鍵字擷取出來存到陣列中
	rCount := 0;
	For i in 1..textLen Loop
		vStrForCheck := ',';	--比對是否重複使用的變數,歸零
		For j in 1..i Loop		--１個字跑１圈,不超過長度下２個字跑２圈
			nTimes := Trunc( (textLen-j+1) / i, 0 ); --扣掉開始字串剩下的長度,迴圈需要幾圈
			For k in 1..nTimes Loop
				If	i<6 Then	--1~5個字先判斷是否重複以避開違反 PK 的錯誤
					n := j+(k-1)*i;
					vStr := Substr(vCompanyName, n, i);
					If	InStr(vStrForCheck,','||vStr||',')=0 Then
						If	Length(vStr)>1 Then	--	排除只有一個字的部份
							rCount := rCount+1;
							tab1(rCount) := vStr;
						End If;
						vStrForCheck := vStrForCheck || vStr || ',';
					End If;
				Else
					rCount := rCount+1;
					tab1(rCount) := Substr(vCompanyName, j+(k-1)*i, i);
				End If;
			End Loop;
		End Loop;
	End Loop;

	Insert Into Ftxt_Search_Mas (Ban_No,Prefix_No,Company_Name,Status_Code,Reserve_Date,Apply_Name)
		Values (vBanNo,vPrefixNo,Trim(pCompany_Name),pStatus_Code,pReserve_Date,pApply_Name);

	-- full text index detail rec.
	ForAll j In 1..rCount
		Insert Into Ftxt_Search_Dea (Company_Name,Ban_No,Prefix_No) Values (tab1(j),vBanNo,vPrefixNo);

	return;

Exception
	When Others Then
		Declare
			Err_Num Number := Sqlcode;
			Err_Msg Varchar2(512) := Sqlerrm;
		Begin
			Insert Into Ftxt_Err_Log Values (vBanNo,'Error ORA-'||Err_Num||';'||Err_Msg);
			commit;
		End;
End;
