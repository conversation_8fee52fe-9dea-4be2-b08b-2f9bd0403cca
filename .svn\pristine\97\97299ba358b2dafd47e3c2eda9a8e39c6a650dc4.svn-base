package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdCtrlitem;

public class CsmdCtrlitemDao extends BaseDaoJdbc implements RowMapper<CsmdCtrlitem> {

	private static final String SQL_defaultOrder = "order by CTRL_ITEM";

	private static final String SQL_findAll = "SELECT * FROM ICMS.CSMD_CTRLITEM WHERE ENABLE = 'Y'";
	public List<CsmdCtrlitem> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<CsmdCtrlitem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdCtrlitem findByCtrlItem(String ctrlItem) {
		SQLJob sqljob = new SQLJob("SELECT * FROM ICMS.CSMD_CTRLITEM");
		sqljob.appendSQL("WHERE CTRL_ITEM=?");
		sqljob.addParameter(ctrlItem);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<CsmdCtrlitem> datas = (List<CsmdCtrlitem>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=datas && !datas.isEmpty())
			return datas.get(0);
		return null;
	}

	public CsmdCtrlitem mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdCtrlitem obj = null;
		if(null!=rs) {
			obj = new CsmdCtrlitem();
			obj.setCtrlItem(rs.getString("CTRL_ITEM"));
			obj.setItemName(rs.getString("ITEM_NAME"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
		}
		return obj;
	}

}