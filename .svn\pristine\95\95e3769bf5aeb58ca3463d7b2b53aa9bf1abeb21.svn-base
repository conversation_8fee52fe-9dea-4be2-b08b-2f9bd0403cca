<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1001">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
if ("init".equals(obj.getState())){
	//do nothing
} else if("onLineCnt".equals(obj.getState())) {
	//線上收文
	obj.makeOnlineCnt();
}
%>
<html>
<head>
<title>列印申請表</title>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
$( document ).ready(function() {
	//window.focus();
});
</script>
</head>
<body>
<form id="form1" name="form1" method="post" action="pre1001_01.jsp">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>" />
<input type="hidden" id="onLineCnt" name="onLineCnt" value="<%=obj.getOnLineCnt()%>" />
<input type="hidden" id="onlineFilePath" name="onlineFilePath" value="<%=obj.getOnlineFilePath() %>" />
</form>
<%
if( "makeOnlineCntSuccess".equalsIgnoreCase(obj.getState()) ) {
	//線上收文成功後, 開啟申請表
%>
<font size="5" color="#ff0000">
<%=obj.getErrorMsg()%>，預查申請表產製中，請稍後...
</font>
<iframe id="onLineCntFrame" height='100%' width='100%' src='pre1001_02.jsp?onlineFilePath=<%=obj.getOnlineFilePath()%>'></iframe>
<%
} else if ( "init".equalsIgnoreCase(obj.getState()) ) {
	//每次收文件數
	int limit = ServiceGetter.getInstance().getPre1001Service().findReceiveLimit();
	//線上收文件數
	int onlineCnt = Integer.parseInt(obj.getOnLineCnt());
	if( limit == 99999 || limit > onlineCnt ) {
		limit = onlineCnt;
	}
%>
<script type="text/javascript">
try {
    $.blockUI({ message: '<h1>本次收文<%=limit%>件，預查申請表產製中，請稍後...</h1>', css: { width:'80%',left:'10%' } });
	form1.state.value = "onLineCnt";
	form1.submit();
} catch(e) {
}
</script>
<%
} else {
%>
<script type="text/javascript">
try {
	window.close();
} catch(e) {
}
</script>
<%
}
%>
<script type="text/javascript">
try {
	window.opener.showMsgBar("<%=obj.getErrorMsg()%>");
} catch(e) {
}
</script>
</body>
</html>