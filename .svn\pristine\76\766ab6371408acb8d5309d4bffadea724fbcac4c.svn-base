package com.kangdainfo.tcfi.loader;

import java.util.List;

import org.springframework.context.ApplicationContextException;

import com.kangdainfo.common.loader.BaseLoader;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;

public abstract class SystemCodeLoader extends BaseLoader
{
	protected abstract String getCacheName();
	protected abstract String getCodeKind();

	/**
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<SystemCode> getCodes() {
		if(getServletContext().getAttribute(getCacheName()) == null)
			reload();
		return (List<SystemCode>) getServletContext().getAttribute(getCacheName());
	}

	public SystemCode getSystemCodeByCode(String code) {
		List<SystemCode> datas = getCodes();
		for(SystemCode data : datas)
		{
			if( null!=data.getCode() && data.getCode().equals(code) )
				return data;
		}
		return null;
	}
	
	public String getCodeNameByCode(String code) {
		SystemCode data = getSystemCodeByCode(code);
		if( null!=data )
			return data.getCodeName();
		return null;
	}
	
	public String getCodeDescByCode(String code) {
		SystemCode data = getSystemCodeByCode(code);
		if( null!=data )
			return data.getCodeDesc();
		return null;
	}
	
	protected void initApplicationContext() throws ApplicationContextException {
		super.initApplicationContext();
		reload();
	}

	/** 重新載入 */
	public void reload() {
		if(logger.isInfoEnabled()) logger.info("[start][reload]");
		getServletContext().setAttribute(getCacheName(), systemCodeDao.findByCodeKind(getCodeKind()));
		if(logger.isInfoEnabled()) logger.info("[end][reload]");
	}

	private SystemCodeDao systemCodeDao;
	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}

}