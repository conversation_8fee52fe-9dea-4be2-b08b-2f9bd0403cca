package com.kangdainfo.tcfi.service;

import java.util.List;
import java.util.TreeMap;

import com.kangdainfo.tcfi.model.eedb.bo.Eedb1100;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb5000;
import com.kangdainfo.tcfi.model.eedb.bo.EedbV8000;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb1100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb5000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.EedbV8000Dao;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1017;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2004;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixVo;
import com.kangdainfo.tcfi.model.eicm.bo.UpdateHistoryData;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1003Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1009Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1011Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1100Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc004Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc053Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc055Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeDetail;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;
import com.kangdainfo.tcfi.view.pre.IApprove;

public interface ApproveService {

	public Cedbc000 getCedbc000ByIdNoAndPwd(String idNo, String pwd);

	public Cedbc000 getCedbc000ByIdNo(String idNo);

	public Cedb1000 getCedb1000ByPrefixNo(String prefixNo);

	public Cedb1000 getCedb1000ByTelixNo(String telixNo);

	public List<Cedb1001> getCedb1001ByPrefixNo(String telisNo);

	public Eedb1000 getEedb1000ByTelixNo(String telixNo);

	public Eedb1100 getEedb1100ByTelixNo(String telixNo);

	public Eedb3300 getEedb3300ByTelixNo(String telixNo);

	public Eedb5000 getEedb5000ByTelixNo(String telixNo);

	public List<EedbV8000> getEedbV8000ByTelixNo(String telixNo);

	public OssmApplMain getOssmApplMainByTelixNo(String telixNo);

	public OssmApplFlow getOssmApplFlowByTelixNoAndProcessNo(String telixNo,
			String ProcessNo);

	public OssmFeeMain getOssmFeeMainByTelixNoAndProcessNo(String telixNo,
			String ProcessNo);

	public OssmFeeDetail getOssmFeeDetailByTelixNoAndProcessNo(String telixNo,
			String ProcessNo);

	public List<Cedb1002> getCedb1002ByPrefixNo(String prefixNo);

	public Cedb1002 getCedb1002ByPrefixNoAndSeqNo(String prefixNo, String seqNo);

	public List<Cedb1002> getCedb1002ByBanNo(String banNo);

	public Cedb2000 getCedb2000ByBanNo(String banNo);

	public List<Cedb2002> getCedb2002ByBanNo(String banNo);

	public List<Cedb2004> getCedb2004ByBanNo(String banNo);

	public void saveCedb1017(Cedb1017 obj);
	
	public Cedbc000 selectCedbc000ById(String id);

	EedbV8000Dao getEedbV8000Dao();

	Eedb1000Dao getEedb1000DaoEicm();

	Eedb1100Dao getEedb1100Dao();

	Eedb3000Dao getEedb3000Dao();

	Eedb3300Dao getEedb3300Dao();

	Eedb5000Dao getEedb5000Dao();

	OssmApplFlowDao getOssmApplFlowDao();

	OssmApplMainDao getOssmApplMainDao();

	OssmOrgNameDao getOssmOrgNameDao();

	OssmBussItemDao getOssmBussItemDao();

	OssmOrgChangeDao getOssmOrgChangeDao();

	Cedb1000Dao getCedb1000Dao();

	Cedb1001Dao getCedb1001Dao();

	Cedb1023Dao getCedb1023Dao();

	Cedb1002Dao getCedb1002Dao();

	Cedb2000Dao getCedb2000Dao();

	Cedb2002Dao getCedb2002Dao();

	Cedb2004Dao getCedb2004Dao();

	Eedb3100Dao getEedb3100Dao();

	public Cedb1022Dao getCedb1022Dao();

	Cedb1011Dao getCedb1011Dao();

	Cedb1003Dao getCedb1003Dao();

	void insertCedb1002s(List<Cedb1002> cedb1002s);

	List<Cedbc000> queryAllcedbc000s();

	/** 分文 */
	String doAssign(String userId, String userName);

	TreeMap<String, PrefixVo> getQueryResultByPrefixNo(String prefixNo, boolean isAppendTenRecs, String functionName, String approveResult);

	Cedbc055Dao getCedbc055Dao();

	List<Eedb1002> getEedb1002ByTelixNo(String telixNo);

	List<Eedb1002> getEedb1002ByPrefixNo(String prefixNo);

	boolean doVerifyCmpyNames(PrefixVo prefixVo);

	boolean doVerifyBusiItem(PrefixVo prefixVo, String functionName);

	public String saveToPrefixDataBase(PrefixVo prefixVo, IApprove approve, String userId, String funCode);

	public String saveToPrefixDataBaseNoApprove(PrefixVo prefixVo, IApprove approve, String userId, String funCode);

	Cedbc004Dao getCedbc004Dao();

	Cedbc053Dao getCedbc053Dao();

	TreeMap<String, PrefixVo> getCaseSelection(IApprove formBean, String functionName, boolean isPlusTenTecs, String approveResult);

	Cedb1010Dao getCedb1010Dao();

	TreeMap<String, PrefixVo> getWaitForCloseKeyinQueryResult();

	List<UpdateHistoryData> getUpdateHistoryDatas(String banNo);

	Cedb1100Dao getCedb1100Dao();
	
	Cedb1006Dao getCedb1006Dao();

	Cedb1008Dao getCedb1008Dao();

	Cedb1009Dao getCedb1009Dao();

	Integer getDistributedCountByDayForUser(String idNo);

	/**
	 * 查詢 - 發文登打日期時間
	 * @param prefixNo
	 * @return
	 */
	public String getIssueKeyinDateTime(String prefixNo);

	/**
	 * 查詢 - 收文登打日期時間
	 * @param prefixNo
	 * @return
	 */
	public String getReceiveKeyinDateTime(String prefixNo);

	String resetCloseDate(String prefixNo);

	List<Cedb1000> findWithdraw(String applyId, String prefixNo);
	
	List<Cedb1000> findWithdrawWithIdAndCompanyName(String applyId, String companyName, String prefixNo);

	/** 檢還、徹件 */
	List<String> doWithdraw(String prefixNo, List<String> pnos, String withdrawType) throws Exception;
	/** 智慧型預查回傳預查審核結果 */
	public Boolean doPreSearchVerified(String prefixNo);
	
	void convertJsonToObject(PrefixVo prefixVo);

}