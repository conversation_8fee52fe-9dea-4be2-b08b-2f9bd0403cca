package com.kangdainfo.moea.bo;

import java.util.Comparator;

import com.kangdainfo.tcfi.model.eedb.bo.Eedb3100;

public class SeqNoComparatorFor3100
  implements Comparator<Object> {

  public SeqNoComparatorFor3100() {}

  public int compare(Object o1, Object o2) {
    Eedb3100 co1 = (Eedb3100) o1;
    Eedb3100 co2 = (Eedb3100) o2;
    return co1.getSeqNo().compareTo(co2.getSeqNo());
  }

  public boolean equals(Object obj) {
    throw new java.lang.UnsupportedOperationException(
      "Method equals() not yet implemented.");
  }
}
