<%
/**
程式目的：公司名稱預查規費日報表
程式代號：pre4022
程式日期：113.03.22
程式作者：Jackie.Huang
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4022" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4022">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<%
if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report){
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4022.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	var payTypeRadios = document.getElementsByName("payType");
	var payTypeChecked = false;

    // 檢查是否至少有一個支付選擇被選取
    for (var i = 0; i < payTypeRadios.length; i++) {
        if (payTypeRadios[i].checked) {
            payTypeChecked = true;
            break;
        }
    }

    if (!payTypeChecked) {
        alertStr += "至少需選擇一項支付方式。\n";
    }
	
	if(form1.q_Type[0].checked){
		alertStr += checkEmpty(form1.q_DateStart,"起始日期");
		alertStr += checkEmpty(form1.q_DateEnd,"結束日期");
		alertStr += checkEmpty(form1.q_TimeStart,"起始時間");
		alertStr += checkEmpty(form1.q_TimeEnd,"結束時間");
		form1.q_DateStart.style.backgroundColor="";
		form1.q_DateEnd.style.backgroundColor="";
		form1.q_TimeStart.style.backgroundColor="";
		form1.q_TimeEnd.style.backgroundColor="";
	}else if (form1.q_Type[1].checked){
		alertStr += checkEmpty(form1.q_DateStart,"起始日期");
		alertStr += checkEmpty(form1.q_DateEnd,"結束日期");
		form1.q_DateStart.style.backgroundColor="";
		form1.q_DateEnd.style.backgroundColor="";
	} else {
		alertStr += "請先點選查詢種類。";
	}
	
	if(alertStr.length!=0){ alert(alertStr); return false; }
	
	//beforeSubmit();
	return true;
}

function changeRadio(type) {
	var typeRadio = document.getElementsByName("q_Type");
	var dateName = document.getElementById("dateTypeName");
	var timeName = document.getElementById("timeTypeName");
	var timeStartInput = document.getElementById("q_TimeStart");
	var timeEndInput = document.getElementById("q_TimeEnd");
	
	whatButtonFireEvent("doClear");
	
    if(type == 'receive'){
    	$('#type').val("receive") ;
    	typeRadio[0].checked = true;
    	typeRadio[1].checked = false;
    	dateName.textContent = "收文";
    	timeName.textContent = "收文";
    	timeStartInput.disabled = false;
    	timeEndInput.disabled = false;
    }else if(type == 'pay'){
    	$('#type').val("pay") ;
    	typeRadio[1].checked = true;
    	typeRadio[0].checked = false;
    	dateName.textContent = "繳款";
    	timeName.textContent = "繳款";
    	timeStartInput.disabled = true;
    	timeEndInput.disabled = true;
    }
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				if(checkField()){
					$('#state').val("preview") ;
					var target = 'PRE4022_'+randomUUID().replace(/\-/g,"");
					window.open("",target);
					form1.target = target;
					form1.submit();
					form1.target = '';	
				}				
				break;
			case "doClear":
				form1.q_DateStart.value = "";
				form1.q_DateEnd.value = "";
				form1.q_TimeStart.value = "";
				form1.q_TimeEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck() {
	
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var dateStart = form1.q_DateStart.value;
		var dateEnd = form1.q_DateEnd.value;
		var timeStart = form1.q_TimeStart.value;
		var timeEnd = form1.q_TimeEnd.value;
		var staffIdNo = form1.q_idNo.value;
		var changeType = form1.q_changeType.value;
		var payTypelist = document.getElementsByName("payType");
		var typeRadio = document.getElementsByName("q_Type");
		var payType = "";
		var x = "";
		console.log("dateStart= " + dateStart);
		console.log("dateEnd= " + dateEnd);
		var typeRadio = $('input[name="q_Type"]');

		if (typeRadio.eq(1).prop('checked')) {
		    
		} else {
		    
		}
		
		for(var i = 0; i < payTypelist.length; i++) {
			if (payTypelist[i].checked) {
	            payType = payTypelist[i].value;
	            break;
	        }
		}
		
		if (typeRadio[0].checked && !typeRadio[1].checked) {
				x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4022.jsp?dateStart='+ dateStart + '&dateEnd=' + dateEnd + '&timeStart=' + timeStart + '&timeEnd=' + timeEnd + '&type=receive');
		} else if (typeRadio[1].checked && !typeRadio[0].checked){
			    x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4022.jsp?dateStart='+ dateStart + '&dateEnd=' + dateEnd + '&timeStart=' + timeStart + '&timeEnd=' + timeEnd + '&type=pay');
		}
		console.log("x= " + x);
		if (x == 'ok') {
			whatButtonFireEvent("doPrintPdf");
		} else {
			document.getElementById("ERRMSG").innerHTML = x;
		}
	} 	
}

function doUploadReceipt() {
	var x = "";
	x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4022UploadReceipt.jsp?');
	console.log("x= " + x);
	alert(x)
}

function init() {
	changeRadio("receive");
}

</script>
</head>

<body onLoad="init();">
<form>

</form>
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4022'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
    		<td class="td_form" width="20%">
        	<input type="radio" name="q_Type" value="receive" onclick="changeRadio(this.value);">以收文日期查詢：
        	<br>
        	<input type="radio" name="q_Type" value="pay" onclick="changeRadio(this.value);">以繳款日期查詢：
    		</td>
    		<td class="td_form_white" width="80%">
        	<span id = "dateTypeName"></span>日期起：
        	<%= View.getPopCalendar("field_Q", "q_DateStart", obj.getQ_DateStart()) %>
        	~ 迄：<%= View.getPopCalendar("field_Q", "q_DateEnd", obj.getQ_DateEnd()) %> (YYYMMDD)
        	<br>
        	<span id = "timeTypeName"></span>時間區間起：
        	<input class="field_Q" type="text" id="q_TimeStart" name="q_TimeStart" size="10" maxlength="10" value="<%= obj.getQ_TimeStart() %>">
        	~ 迄：<input class="field_Q" type="text" id="q_TimeEnd" name="q_TimeEnd" size="10" maxlength="10" value="<%= obj.getQ_TimeEnd() %>"> (hhmmss)
    		</td>
		</tr>
		<tr>
			<td class="td_form" width="10%">承辦人：</td>
			<td class="td_form_white" width="90%">
				<select name="q_idNo" id="q_idNo" value="<%=obj.getQ_idNo()%>">
					<%=View.getOptionStaffName( obj.getQ_idNo(), false, 1)%>
				</select>
			</td>
		</tr>
		<tr>
			<td class="td_form" width="10%">預查種類：</td>
			<td class="td_form_white" width="90%">
				<select name="q_changeType" id="q_changeType" value="<%=obj.getQ_changeType()%>">
					<%=View.getOptionPrefixTypeMenu( obj.getQ_changeType(), false, 1) %>
				</select>
			</td>
		</tr>
		<tr>
        	<td class="td_form">繳款方式：</td>
        	<td class="td_form_white">
				<input type="radio" class="field_Q" name="payType" value="0"/>現金
				<input type="radio" class="field_Q" name="payType" value="1"/>匯票
				<input type="radio" class="field_Q" name="payType" value="5"/>支票
				<input type="radio" class="field_Q" name="payType" value="6"/>悠遊卡
				<input type="radio" class="field_Q" name="payType" value="7"/>台灣Pay
				<input type="radio" class="field_Q" name="payType" value="A"/>不限
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="規費日報表列印" onClick="doSomeCheck()" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" value="上傳收據" onClick="doUploadReceipt()" >
            </td>
        </tr>	
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<input type="hidden" id="type" name="type" value="<%=obj.getQ_Type()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>


</table>
</form>
</body>
</html>