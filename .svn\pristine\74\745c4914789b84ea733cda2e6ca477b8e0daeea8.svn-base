// ### TopLink Mapping Workbench 9.0.3 generated source code ###

package com.kangdainfo.tcfi.model.eedb.bo;

public class EedbV8000 {
	private java.lang.String caseCode;
	private java.lang.String caseShortname;
	private java.lang.String telixNo;
	private String unitCode;

	public java.lang.String getCaseCode() {return caseCode;}
	public void setCaseCode(java.lang.String caseCode) {this.caseCode = caseCode;}

	public java.lang.String getCaseShortname() {return caseShortname;}
	public void setCaseShortname(java.lang.String caseShortname) {this.caseShortname = caseShortname;}

	public java.lang.String getTelixNo() {return telixNo;}

	public void setTelixNo(java.lang.String telixNo) {this.telixNo = telixNo;}
	public String getUnitCode() {
		return unitCode;
	}
	public void setUnitCode(String unitCode) {
		this.unitCode = unitCode;
	}
	
}