/*-----------------------------------------------------------------------
 * Copyright(c) 2025 Aceraeb Inc. All Rights Reserved.
 * This software is proprietary to and embodies the confidential technology
 * of Acer Inc.. Possession, use or copying of this software
 * and media is authorized only pursuant to a valid written license from Acer
 * Inc. or an authorized sublicensor.
-----------------------------------------------------------------------*/

/*-----------------------------------------------------------------------
 * ProductName      : 需導入中推會字型之系統
 * File Code        : CmexFontServlet
 * File Name        : CmexFontServlet.java
 * Description      : 前端中推會字型ttf檔Server端
 * Dev Ver          : JDK 8
 * Author           : <PERSON>
 * Create Date      : 2025/04/21
-----------------------------------------------------------------------*/
package tw.gov.moea.font.servlet;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CmexFontServlet extends HttpServlet {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	// 字體檔案目錄
	private String fontDirPath;

	@Override
	public void init() throws ServletException {
		// 正式環境應將路徑設定在環境變數-DprojectName.fontDirPath
		fontDirPath = System.getProperty("prefix.fontDirPath");
		if (fontDirPath == null || fontDirPath.trim().length() == 0) {
			// 預設開發用
			fontDirPath = "D:/fonts";
		}
	}

	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response)
			throws ServletException, IOException {
		try {
			// 請求來源
			String requestURI = request.getRequestURI();
			// System.out.println(requestURI);

			// 請求字體檔名
			String fontFileName = null;

			if (requestURI != null) {
				// 根據請求 URI 決定要回傳的檔案名稱
				if (requestURI.endsWith("/eudc_kai.ttf")) {
					fontFileName = "eudc_kai.ttf";
				} else if (requestURI.endsWith("/eudc.ttf")) {
					fontFileName = "eudc.ttf";
				} else {
					response.sendError(HttpServletResponse.SC_NOT_FOUND);
					return;
				}
			}
			// System.out.println(fontFileName);

			// 取得字體檔案的實際路徑
			// String filePath = getServletContext().getRealPath(FONT_DIRECTORY
			// + fontFileName);
			String filePath = fontDirPath + File.separator + fontFileName;
			File fontFile = new File(filePath);

			if (!fontFile.exists()) {
				response.sendError(HttpServletResponse.SC_NOT_FOUND);
				return;
			}

			// 設定回傳的內容類型
			response.setContentType("font/ttf");
			response.setContentLength((int) fontFile.length());

			// 避免快取字體檔
			response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=1800");
			// response.setHeader("Pragma", "no-cache");
			response.setDateHeader("Expires", 0);

			// 傳送檔案內容給 client
			try (BufferedInputStream in = new BufferedInputStream(new FileInputStream(fontFile));
					OutputStream out = response.getOutputStream()) {
				byte[] buffer = new byte[4096];
				int bytesRead;

				while ((bytesRead = in.read(buffer)) != -1) {
					out.write(buffer, 0, bytesRead);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
