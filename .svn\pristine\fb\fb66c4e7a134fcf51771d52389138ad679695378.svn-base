package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 檢還、撤件
 *
 */
public class PRE3001_07 extends SuperBean {

	private String prefixNo;
	private String[] prefixNos;
	private String withdrawType;
	private String withdrawCompanyName;

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] a) {this.prefixNos = a;}
	public String getWithdrawType() {return checkGet(withdrawType);}
	public void setWithdrawType(String s) {this.withdrawType = checkSet(s);}
	public String getWithdrawCompanyName() {return checkGet(withdrawCompanyName);}
	public void setWithdrawCompanyName(String s) {this.withdrawCompanyName = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public void doWithdraw() {
		try {
			List<String> pnos = new ArrayList<String>();
			for(String pno : prefixNos) {
				pnos.add(pno);
				//備份
				ServiceGetter.getInstance().getBackupService().doBackup(pno, getLoginUserId());
			}
			//檢還、撤件
			ServiceGetter.getInstance().getApproveService().doWithdraw(getPrefixNo(), pnos, this.withdrawType);
			//智慧型預查回傳預查審核結果
			ServiceGetter.getInstance().getApproveService().doPreSearchVerified(getPrefixNo());
			this.setState("updateSuccess");
		} catch (Exception e) {
			e.printStackTrace();
			this.setState("updateError");
			this.setErrorMsg("更新失敗!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		if(!"".equals(getPrefixNo())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" A.PREFIX_NO");
			sqljob.appendSQL(",A.BAN_NO");
			sqljob.appendSQL(",nvl(");
			sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
			sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
			sqljob.appendSQL("  )");
			sqljob.appendSQL(" ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE_NAME");
			sqljob.appendSQL(",DECODE(SUBSTR(A.TELIX_NO,0,1),'O','一站式','Z','一維條碼','0','一維條碼','A','線上申辦','L','線上審核','紙本送件') AS APPLY_WAY");
			sqljob.appendSQL(",A.COMPANY_NAME");
			sqljob.appendSQL(",A.PREFIX_STATUS");
			sqljob.appendSQL(",A.APPLY_NAME");
			sqljob.appendSQL(",A.RESERVE_DATE");
			sqljob.appendSQL("FROM CEDB1000 A");
			sqljob.appendSQL("WHERE ( A.APPLY_ID=(SELECT APPLY_ID FROM CEDB1000 WHERE PREFIX_NO=?)");
			sqljob.appendSQL(" or A.COMPANY_NAME = ? ) ");
			sqljob.appendSQL("AND A.PREFIX_NO!=?");
			sqljob.appendSQL("AND A.APPROVE_RESULT='Y'");
			sqljob.appendSQL("AND A.RESERVE_DATE > ?");
			sqljob.appendSQL("AND (A.COMPANY_STUS IS NULL OR A.COMPANY_STUS != '03')");
			sqljob.appendSQL("ORDER BY A.PREFIX_NO ASC");
			sqljob.addParameter(getPrefixNo());
			sqljob.addParameter(getWithdrawCompanyName());
			sqljob.addParameter(getPrefixNo());
			sqljob.addParameter(Datetime.getDateAdd("d",-90,Datetime.getYYYMMDD()));
			System.out.println(sqljob);
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[7];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[7];
					//預查編號
					rowArray[0] = Common.get(o.get("PREFIX_NO"));
					//統一編號
					rowArray[1] = Common.get(o.get("BAN_NO"));
					//申請方式
					rowArray[2] = Common.get(o.get("APPLY_WAY"));
					//公司名稱
					rowArray[3] = Common.get(o.get("COMPANY_NAME"));
					//案件狀態
					rowArray[4] = ServiceGetter.getInstance().getSystemCode06Loader().getCodeNameByCode(Common.get(o.get("PREFIX_STATUS")));
					//申請人
					rowArray[5] = Common.get(o.get("APPLY_NAME"));
					//保留期限
					rowArray[6] = Common.get(o.get("RESERVE_DATE"));
					arrList.add(rowArray);	
				}
			}
		}
		return arrList;
	}
	
	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

}