package com.kangdainfo.tcfi.view.test;

import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 查詢投審會異動資料傳輸紀錄檔
 *
 */
public class QueryMoeaic extends SuperBean {

	/** 查詢條件 */
	private String q_prefixNo;
	/** 資料欄位 */
	private String idntpk;
	private String banNo;
	private String prefixNo;
	private String updCode;
	private String updDate;

	@SuppressWarnings("unchecked")
	public ArrayList<?> doQueryAll() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL("IDNTPK");
		sqljob.appendSQL(",PREFIX_NO");
		sqljob.appendSQL(",BAN_NO");
		sqljob.appendSQL(",UPD_CODE");
		sqljob.appendSQL(",(to_char(UPD_DATE,'yyyy')-1911)||'/'||to_char(UPD_DATE,'MM')||'/'||to_char(UPD_DATE,'dd')||'　'||to_char(UPD_DATE,'HH24:MI:SS') AS UPD_DATE");
		sqljob.appendSQL("FROM CSML_CMPY_TRAN_MOEAIC");
		sqljob.appendSQL("WHERE PREFIX_NO = ?");
		sqljob.addParameter(q_prefixNo);
		List<QueryMoeaic> objList = ServiceGetter.getInstance().getIcmsGeneralQueryDao().query(sqljob, BeanPropertyRowMapper.newInstance(QueryMoeaic.class));			
		if (objList != null && objList.size() > 0) {
			String[] rowArray = new String[5];
			for(QueryMoeaic o : objList) {
				rowArray = new String[5];
				rowArray[0] = Common.get(o.getIdntpk());
				rowArray[1] = Common.get(o.getPrefixNo());
				rowArray[2] = Common.get(o.getBanNo());
				rowArray[3] = Common.get(o.getUpdCode());
				rowArray[4] = Common.get(o.getUpdDate());
				arrList.add(rowArray);
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}
	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}
	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}
	public String getIdntpk() {return checkGet(idntpk);}
	public void setIdntpk(String s) {this.idntpk = checkSet(s);}
	public String getUpdCode() {return checkGet(updCode);}
	public void setUpdCode(String s) {this.updCode = checkSet(s);}
	public String getUpdDate() {return checkGet(updDate);}
	public void setUpdDate(String s) {this.updDate = checkSet(s);}

}