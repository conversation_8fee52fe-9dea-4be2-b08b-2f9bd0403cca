package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 簡訊傳送服務記錄檔(UMS_MT)
 *
 */
public class UmsMt extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** PK */
	private String umsNo;
	/** 訊息代號 */
	private String msgid;
	/** 受話者手機號碼 */
	private String dstaddr;
	/** 簡訊內容 */
	private String smbody;
	/** 簡訊發送時間 */
	private String mtTime;
	/** 回覆狀態代碼 */
	private String mtCode;
	/** 回覆狀態字串 */
	private String mtStr;
	/** 服務端回覆時間 */
	private String mxTime;
	/** 服務端回覆代碼 */
	private String mxCode;
	/** 服務端回覆字串 */
	private String mxStr;
	/** 重試次數 */
	private Integer retry;
	/** 使用系統 */
	private String system;

	public String getUmsNo() {return umsNo;}
	public void setUmsNo(String umsNo) {this.umsNo = umsNo;}
	public String getMsgid() {return msgid;}
	public void setMsgid(String msgid) {this.msgid = msgid;}
	public String getDstaddr() {return dstaddr;}
	public void setDstaddr(String dstaddr) {this.dstaddr = dstaddr;}
	public String getSmbody() {return smbody;}
	public void setSmbody(String smbody) {this.smbody = smbody;}
	public String getMtTime() {return mtTime;}
	public void setMtTime(String mtTime) {this.mtTime = mtTime;}
	public String getMtCode() {return mtCode;}
	public void setMtCode(String mtCode) {this.mtCode = mtCode;}
	public String getMtStr() {return mtStr;}
	public void setMtStr(String mtStr) {this.mtStr = mtStr;}
	public String getMxTime() {return mxTime;}
	public void setMxTime(String mxTime) {this.mxTime = mxTime;}
	public String getMxCode() {return mxCode;}
	public void setMxCode(String mxCode) {this.mxCode = mxCode;}
	public String getMxStr() {return mxStr;}
	public void setMxStr(String mxStr) {this.mxStr = mxStr;}
	public Integer getRetry() {return retry;}
	public void setRetry(Integer retry) {this.retry = retry;}
	public String getSystem() {return system;}
	public void setSystem(String system) {this.system = system;}

}