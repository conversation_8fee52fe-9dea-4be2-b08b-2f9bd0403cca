<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

			
	<!-- ======================= Transaction Manager ======================= -->	 
	<bean id="osssJdbcTransactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="osssDataSource" />
	</bean>
	<bean id="eicmJdbcTransactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="eicmDataSource" />
	</bean>
	<bean id="eedbJdbcTransactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="eedbDataSource" />
	</bean>
	<bean id="icmsJdbcTransactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="icmsDataSource" />
	</bean>
	<!-- 
	<bean id="eicmQDJdbcTransactionManager"
		class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="eicmQDDataSource" />
	</bean>
	-->

	<tx:advice id="osssTransactionAdvice" transaction-manager="osssJdbcTransactionManager">
		<tx:attributes>
			<tx:method name="get*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="load*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />	
			<tx:method name="find*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="query*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />					
			<tx:method name="save*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="insert*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="update*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="delete*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="bulk*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="test*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="check*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="authenticate*"			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
			<tx:method name="import*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="export*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="parse*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="generate*"  			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="confirm*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="cancel*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reject*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reCreate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>					
			<tx:method name="trans*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="change*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="print*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="validate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="do*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="make*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
		</tx:attributes>
	</tx:advice>

	<tx:advice id="eicmTransactionAdvice" transaction-manager="eicmJdbcTransactionManager">
		<tx:attributes>
			<tx:method name="get*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="load*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />	
			<tx:method name="find*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="query*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />					
			<tx:method name="save*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="insert*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="update*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="delete*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="bulk*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="test*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="check*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="authenticate*"			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
			<tx:method name="import*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="export*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="parse*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="generate*"  			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="confirm*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="cancel*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reject*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reCreate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>					
			<tx:method name="trans*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="change*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="print*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="validate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="do*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="make*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
		</tx:attributes>
	</tx:advice>

	<tx:advice id="eedbTransactionAdvice" transaction-manager="eedbJdbcTransactionManager">
		<tx:attributes>
			<tx:method name="get*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="load*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />	
			<tx:method name="find*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="query*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />					
			<tx:method name="save*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="insert*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="update*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="delete*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="bulk*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="test*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="check*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="authenticate*"			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
			<tx:method name="import*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="export*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="parse*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="generate*"  			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="confirm*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="cancel*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reject*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reCreate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>					
			<tx:method name="trans*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="change*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="print*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="validate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="do*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="make*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
		</tx:attributes>
	</tx:advice>

	<tx:advice id="icmsTransactionAdvice" transaction-manager="icmsJdbcTransactionManager">
		<tx:attributes>
			<tx:method name="get*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="load*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />	
			<tx:method name="find*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />
			<tx:method name="query*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable" read-only="true" />					
			<tx:method name="save*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="insert*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="update*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="delete*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="bulk*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="test*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="check*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="authenticate*"			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>			
			<tx:method name="import*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="export*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="parse*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="generate*"  			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="confirm*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="cancel*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reject*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="reCreate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>					
			<tx:method name="trans*"	 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="change*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="print*" 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="validate*" 			propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="do*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>
			<tx:method name="make*"	 				propagation="REQUIRED" rollback-for="java.lang.Throwable"/>		
		</tx:attributes>
	</tx:advice>

	<aop:config>
		<aop:pointcut id="service" expression="execution(* com..*Service.*(..))" />
		<aop:pointcut id="DaoImpl" expression="execution(* com..*DaoImpl.*(..))" />
		<aop:advisor advice-ref="osssTransactionAdvice" pointcut-ref="service" />
		<aop:advisor advice-ref="osssTransactionAdvice" pointcut-ref="DaoImpl" />
		<aop:advisor advice-ref="eicmTransactionAdvice" pointcut-ref="service" />
		<aop:advisor advice-ref="eicmTransactionAdvice" pointcut-ref="DaoImpl" />
		<aop:advisor advice-ref="eedbTransactionAdvice" pointcut-ref="service" />
		<aop:advisor advice-ref="eedbTransactionAdvice" pointcut-ref="DaoImpl" />
		<aop:advisor advice-ref="icmsTransactionAdvice" pointcut-ref="service" />
		<aop:advisor advice-ref="icmsTransactionAdvice" pointcut-ref="DaoImpl" />
	</aop:config>

</beans>