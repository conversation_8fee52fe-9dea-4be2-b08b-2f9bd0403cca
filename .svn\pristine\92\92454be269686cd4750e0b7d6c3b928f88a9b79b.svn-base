--DROP TABLE EICM.TRACK_LOG;
-- Create table
CREATE TABLE EICM.TRACK_LOG (
	ID NUMBER(15) not null,
	FUNC_CODE VARCHAR2(20),
	FUNC_NAME VARCHAR2(200),
	OP_IP VARCHAR2(50),
	OP_TYPE VARCHAR2(1),
	OP_STATUS VARCHAR2(20),
	OP_ID_NO VARCHAR2(20),
	OP_NAME VARCHAR2(50),
	OP_DATE VARCHAR2(20),
	OP_TIME VARCHAR2(20),
	OP_REMARK VARCHAR2(400)
);
-- Add comments to the table 
comment on table EICM.TRACK_LOG is '個資軌跡紀錄檔';
-- Add comments to the columns 
comment on column EICM.TRACK_LOG.ID is '主鍵值';
comment on column EICM.TRACK_LOG.FUNC_CODE is '功能代碼';
comment on column EICM.TRACK_LOG.FUNC_NAME is '功能名稱';
comment on column EICM.TRACK_LOG.OP_IP is '連線IP';
comment on column EICM.TRACK_LOG.OP_TYPE is '操作種類(E:修改,Q:查詢)';
comment on column EICM.TRACK_LOG.OP_STATUS is '操作狀態';
comment on column EICM.TRACK_LOG.OP_ID_NO is '使用者帳號';
comment on column EICM.TRACK_LOG.OP_NAME is '使用者名稱';
comment on column EICM.TRACK_LOG.OP_DATE is '操作日期';
comment on column EICM.TRACK_LOG.OP_TIME is '操作時間';
comment on column EICM.TRACK_LOG.OP_REMARK is '查詢條件/個資欄位';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.TRACK_LOG
  add constraint PK_TRACK_LOG primary key (ID)
  using index ;

-- Drop sequence
--DROP sequence EICM.SEQ_TRACK_LOG_ID;
-- Create sequence 
create sequence EICM.SEQ_TRACK_LOG_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_TRACK_LOG
Before Insert ON EICM.TRACK_LOG Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_TRACK_LOG_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.TRACK_LOG for EICM.TRACK_LOG;

--GRANT
grant all on EICM.TRACK_LOG to EICM4AP;
