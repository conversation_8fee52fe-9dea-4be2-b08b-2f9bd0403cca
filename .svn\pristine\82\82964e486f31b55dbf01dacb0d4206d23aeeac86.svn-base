<%
/**
程式目的：承辦人員處理案件統計
程式代號：pre4006
程式日期：1030429
程式作者：Pagan.Chen
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4006" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>
<jsp:useBean id="notAssignList"  scope="page" class="java.util.ArrayList"/>         
<%
if( "init".equals(obj.getState()) ) {
	//預設[分文日期]
	obj.setQ_Type("assign");
} else if( "preview".equals(obj.getState()) ){ 
	//產製 PDF 檔
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4006.pdf", false);
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
	    //obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
} // end if
else if("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true") ;
} // end else if 
if ( "true".equals(obj.getQueryAllFlag()) ) {
	  objList = (java.util.ArrayList) obj.queryAll();
	  notAssignList = obj.queryNotAssign();
} // if
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function checkField(){
	var alertStr="";
	form1.q_DateStart.style.backgroundColor="";
	form1.q_DateEnd.style.backgroundColor="";
	if ( form1.q_Type[0].checked ) {
		alertStr += checkEmpty(form1.q_DateStart,"收文日期起");
		alertStr += checkEmpty(form1.q_DateEnd,"收文日期迄");
	}
	else if ( form1.q_Type[1].checked ) {
		alertStr += checkEmpty(form1.q_DateStart,"分文日期");
		alertStr += checkEmpty(form1.q_DateEnd,"分文日期");
	}
	else {
		alertStr += "請先點選查詢種類。";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	// beforeSubmit();
	return true;
}

function init() {
	if($('#state').val() != "init") {
		$('#listContainer').show();
	} 		
	if ( $('#state').val() == "queryAllSuccess" ) {
		$('#listContainer2').show();
	} 
}

function queryOne(id){}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				if ( checkField() ) {
					showMsgBar("");
					$.blockUI({message:'<h1> 資料載入中，請稍後  <img src="/prefix/images/jquery/busy.gif" /></h1>',overlayCSS:{backgroundColor:'#F3EFEF'}});
					$.post(getVirtualPath() + '/tcfi/ajax/jsonPre4006.jsp'
						,{
							q_DateStart:form1.q_DateStart.value
							,q_DateEnd:form1.q_DateEnd.value
							,q_Type:form1.q_Type.value
						}
						,function(data) {
							if(data) {
								if ( data.trim() == 'ok' ) {
									$('#state').val("preview") ;
									var target = 'PRE4006_'+randomUUID().replace(/\-/g,"");
									window.open("",target);
									form1.target = target;
									form1.submit();
									form1.target = '';	
									showMsgBar("執行成功!");
								} else {
									showMsgBar(data);
								}
							} else {
								showMsgBar("執行失敗!");
							}
						}
					);
					$.unblockUI();
				} 	
				break;
			case "doQueryAll" :
				$('#state').val("queryAll") ;
				break ;
			case "doClear":
				form1.q_DateStart.value = "";
				form1.q_DateEnd.value = "";
				break;
		} // sｗitch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>
<body onLoad="init();">
<form id="form1" name="form1" method="post" autocomplete="off" onsubmit="return checkField();" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4006'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto">
		<table class="table_form" width="100%" height="100%">
			<tr>
				<td class="td_form" width="12%">
					<input type="radio" name="q_Type" value="receive" <%="receive".equals( obj.getQ_Type() )?"checked":""%>>收文日期     
					<br>
					<input type="radio" name="q_Type" value="assign" <%="assign".equals( obj.getQ_Type() )?"checked":""%>>分文日期
		        </td>
				<td class="td_form_white" width="88%">
					(起)：<%=View.getPopCalendar("field_Q","q_DateStart",obj.getQ_DateStart()) %>
					~(迄)：<%=View.getPopCalendar("field_Q","q_DateEnd",obj.getQ_DateEnd()) %>
					
					<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
					<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="whatButtonFireEvent(this.name)" >
					<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				</td>
			</tr>
		</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
<!-- FORM AREA -->

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">承辦人</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">承辦件數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">平均處理日數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">完成件數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">未結件數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">1日內完成件數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">1到2日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',8,false);" href="#">2到2.5日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',9,false);" href="#">2.5到3日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',10,false);" href="#">3到3.5日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',11,false);" href="#">3.5到4日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',12,false);" href="#">4到5日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',13,false);" href="#">5到10日</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',14,false);" href="#">10日以上</a></th>
    
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false,false,false,false,false,false,false,false,false,false,false,false,false};
  boolean displayArray[] = {true,true,true,true,true,true,true ,true ,true ,true ,true ,true ,true ,true  };
  String[] alignArray = {"center", "right;padding-right:20px;","center","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;","right;padding-right:20px;"};
  out.write(View.getQuerylist(primaryArray, displayArray, alignArray, objList, "true", false, null, null, "", true, true));
  %>
  </tbody>
</table>
</div>
</td></tr>
<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>
<tr><td nowrap class="bgList">
<div id="listContainer2" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">未分文件數</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">0-1日未分文</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">1-2日未分文</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">2-3日未分文</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">3-4日未分文</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',6,false);" href="#">4-10日未分文</a></th>
    <th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',7,false);" href="#">10日以上未分文</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray2[] = {true,false,false,false,false,false,false};
  boolean displayArray2[] = {true,true,true,true,true,true,true};
  String[] alignArray2 = {"center", "center","center","center","center","center","center"};
  out.write(View.getQuerylist(primaryArray2, displayArray2, alignArray2, notAssignList, "true", false, null, null, "", true, true));
  %>
  </tbody>
</table>
</div>
</td></tr>

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>