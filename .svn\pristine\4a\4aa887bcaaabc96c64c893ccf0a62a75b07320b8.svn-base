package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.view.pre.PRE4013;

/**
 * 預查線上申辦電子核定書
 */
public interface Pre4013Service {
	
	/**
	 * 檢核
	 * @param prefixNo
	 * @param prefixNoEnd
	 * @param telixNo
	 * @param type
	 * @return
	 */
	public String precheck(String prefixNo, String prefixNoEnd, String telixNo, String type);
	
	/**
	 * 產製 列印資料
	 * @param prefixNo - String
	 * @return PRE4013
	 */
	public PRE4013 queryPrintDataByPrefixNo(String prefixNo);
	
	/**
	 * 取得 列印日期
	 * @return String
	 */
	public String getPrintDate();

	/**
	 * 取得 列印時間
	 * @return String
	 */
	public String getPrintTime();

}