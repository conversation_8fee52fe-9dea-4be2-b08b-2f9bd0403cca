package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * VIEW
 * 公司現況檔(CEDB2000)
 *
 */
public class Cedb2000 extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	/** 統一編號 */
	private String banNo;
	/** 申登機關代碼 */
	private String regUnit;
	/** 申登機關名稱 */
	private String regUnitName;
	/** 加工出口局別 */
	private String subRegUnit;
	/** 組織型態代碼 */
	private String orgnType;
	/** 組織型態名稱 */
	private String orgnName;
	/** 行業別代碼 */
	private String busiType;
	/** 行業別名稱 */
	private String busiTypeName;
	/** 公/民營 */
	private String ownerType;
	/** 公司名稱 */
	private String companyName;
	/** 特取名稱 */
	private String partName;
	/** 預查編號 */
	private String prefixNo;
	/** 公司電話 */
	private String telNo;
	/** 公司市、鎮、鄉、村、里代碼 */
	private String areaCode;
	/** 郵遞區號 */
	private String zoneCode;
	/** 公司地址 */
	private String companyAddr;
	/** 資本總額 */
	private Long capitalAmt;
	/** 僑外投資事業註記(Y:是) */
	private String investmentCode;
	/** 董事人數 */
	private String totDir;
	/** 董事任期起始日期 */
	private String dirBegDate;
	/** 董事任期屆滿日期 */
	private String dirEndDate;
	/** 監察人數 */
	private String totSup;
	/** 監察人任期起始日期 */
	private String supBegDate;
	/** 監察人任期屆滿日期 */
	private String supEndDate;
	/** 負責人名稱 */
	private String respName;
	/** 公司狀況代碼 */
	private String statusCode;
	/** 停業/延展期間(起) */
	private String suspendBegDate;
	/** 停業/延展期間(迄) */
	private String suspendEndDate;
	/** 核准設立日期 */
	private String setupDate;
	/** 核准設立文號(字) */
	private String approveWord;
	/** 核准設立文號(號) */
	private String approveNo;
	/** 核准變更日期 */
	private String changeDate;
	/** 核准變更文號(字) */
	private String changeWord;
	/** 核准變更文號(號) */
	private String changeNo;
	/** 會計期間 */
	private String fiscalDate;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人身分證字號 */
	private String attorId;
	/** 簽證會計師姓名 */
	private String accountName;
	/** 簽證會計師身分證字號 */
	private String accountId;
	/** 分公司家數 */
	private Integer totBranch;
	/** 舊統一編號 */
	private String oldBanNo;
	/** 舊公司名稱 */
	private String oldCompanyName;
	/** 公司管制項目 */
	private String controlItem;
	/** 特殊案件註記(1:停業;2:延展開業;3:復業) */
	private String caseStatus;
	/** 股票性質(1:公開發行;2:上市;3:上櫃;4:未發行) */
	private String stockType;
	/** 股份總數 */
	private Long totStock;
	/** 每股金額 */
	private Long stockAmt;
	/** 公司債可轉換股份股數 */
	private Long bondStock;
	/** 實收資本總額 */
	private Long realAmt;
	/** 資料異動者 */
	private String updateUser;
	/** 資料異動日期 */
	private String updateDate;
	/** 資料異動時間 */
	private String updateTime;
	/** 已發行普通股份總數 */
	private Long commonStock;
	/** 已發行特別股份總數 */
	private Long speciallyStock;
	/** 認股權憑證可認購股份數額 */
	private Long warrantStock;
	/** 清算單位 */
	private String clearUnit;
	/** 清算日期 */
	private String clearDate;
	/** 清算字 */
	private String clearWord;
	/** 清算號 */
	private String clearNo;
	/** 是否發行股票 */
	private String stockIssue;
	/** 核准停業機關 */
	private String suspendUnit;
	/** 核准停業日期 */
	private String suspendDate;
	/** 核准停業文號（字） */
	private String suspendWord;
	/** 核准停業文號（號） */
	private String suspendNo;
	/** 預定開業日期 */
	private String openDate;
	/** 一人公司註記 */
	private String singleCompany;
	/** 公司撤銷日期 */
	private String endDate;
	/** 公司撤銷文號(字) */
	private String endWord;
	/** 公司撤銷文號(號) */
	private String endNo;
	/** 檔案號碼 */
	private String fileNo;
	/** 公司狀況說明 */
	private String statusDescr;
	/** 公司傳真號碼 */
	private String faxNo;
	/** 是否為閉鎖性 */
	private String closed;

	public String getBanNo() {return banNo;}
	public void setBanNo(String s) {this.banNo = s;}
	public String getRegUnit() {return regUnit;}
	public void setRegUnit(String s) {this.regUnit = s;}
	public String getRegUnitName() {return regUnitName;}
	public void setRegUnitName(String s) {this.regUnitName = s;}
	public String getSubRegUnit() {return subRegUnit;}
	public void setSubRegUnit(String s) {this.subRegUnit = s;}
	public String getOrgnType() {return orgnType;}
	public void setOrgnType(String s) {this.orgnType = s;}
	public String getOrgnName() {return orgnName;}
	public void setOrgnName(String s) {this.orgnName = s;}
	public String getBusiType() {return busiType;}
	public void setBusiType(String s) {this.busiType = s;}
	public String getBusiTypeName() {return busiTypeName;}
	public void setBusiTypeName(String s) {this.busiTypeName = s;}
	public String getOwnerType() {return ownerType;}
	public void setOwnerType(String s) {this.ownerType = s;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String s) {this.companyName = s;}
	public String getPartName() {return partName;}
	public void setPartName(String s) {this.partName = s;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String s) {this.prefixNo = s;}
	public String getTelNo() {return telNo;}
	public void setTelNo(String s) {this.telNo = s;}
	public String getAreaCode() {return areaCode;}
	public void setAreaCode(String s) {this.areaCode = s;}
	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String s) {this.zoneCode = s;}
	public String getCompanyAddr() {return companyAddr;}
	public void setCompanyAddr(String s) {this.companyAddr = s;}
	public Long getCapitalAmt() {return capitalAmt;}
	public void setCapitalAmt(Long s) {this.capitalAmt = s;}
	public String getInvestmentCode() {return investmentCode;}
	public void setInvestmentCode(String s) {this.investmentCode = s;}
	public String getTotDir() {return totDir;}
	public void setTotDir(String s) {this.totDir = s;}
	public String getDirBegDate() {return dirBegDate;}
	public void setDirBegDate(String s) {this.dirBegDate = s;}
	public String getDirEndDate() {return dirEndDate;}
	public void setDirEndDate(String s) {this.dirEndDate = s;}
	public String getTotSup() {return totSup;}
	public void setTotSup(String s) {this.totSup = s;}
	public String getSupBegDate() {return supBegDate;}
	public void setSupBegDate(String s) {this.supBegDate = s;}
	public String getSupEndDate() {return supEndDate;}
	public void setSupEndDate(String s) {this.supEndDate = s;}
	public String getRespName() {return respName;}
	public void setRespName(String s) {this.respName = s;}
	public String getStatusCode() {return statusCode;}
	public void setStatusCode(String s) {this.statusCode = s;}
	public String getSuspendBegDate() {return suspendBegDate;}
	public void setSuspendBegDate(String s) {this.suspendBegDate = s;}
	public String getSuspendEndDate() {return suspendEndDate;}
	public void setSuspendEndDate(String s) {this.suspendEndDate = s;}
	public String getSetupDate() {return setupDate;}
	public void setSetupDate(String s) {this.setupDate = s;}
	public String getApproveWord() {return approveWord;}
	public void setApproveWord(String s) {this.approveWord = s;}
	public String getApproveNo() {return approveNo;}
	public void setApproveNo(String s) {this.approveNo = s;}
	public String getChangeDate() {return changeDate;}
	public void setChangeDate(String s) {this.changeDate = s;}
	public String getChangeWord() {return changeWord;}
	public void setChangeWord(String s) {this.changeWord = s;}
	public String getChangeNo() {return changeNo;}
	public void setChangeNo(String s) {this.changeNo = s;}
	public String getFiscalDate() {return fiscalDate;}
	public void setFiscalDate(String s) {this.fiscalDate = s;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String s) {this.attorName = s;}
	public String getAttorId() {return attorId;}
	public void setAttorId(String s) {this.attorId = s;}
	public String getAccountName() {return accountName;}
	public void setAccountName(String s) {this.accountName = s;}
	public String getAccountId() {return accountId;}
	public void setAccountId(String s) {this.accountId = s;}
	public Integer getTotBranch() {return totBranch;}
	public void setTotBranch(Integer s) {this.totBranch = s;}
	public String getOldBanNo() {return oldBanNo;}
	public void setOldBanNo(String s) {this.oldBanNo = s;}
	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String s) {this.oldCompanyName = s;}
	public String getControlItem() {return controlItem;}
	public void setControlItem(String s) {this.controlItem = s;}
	public String getCaseStatus() {return caseStatus;}
	public void setCaseStatus(String s) {this.caseStatus = s;}
	public String getStockType() {return stockType;}
	public void setStockType(String s) {this.stockType = s;}
	public Long getTotStock() {return totStock;}
	public void setTotStock(Long s) {this.totStock = s;}
	public Long getStockAmt() {return stockAmt;}
	public void setStockAmt(Long s) {this.stockAmt = s;}
	public Long getBondStock() {return bondStock;}
	public void setBondStock(Long s) {this.bondStock = s;}
	public Long getRealAmt() {return realAmt;}
	public void setRealAmt(Long s) {this.realAmt = s;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String s) {this.updateUser = s;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String s) {this.updateDate = s;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String s) {this.updateTime = s;}
	public Long getCommonStock() {return commonStock;}
	public void setCommonStock(Long s) {this.commonStock = s;}
	public Long getSpeciallyStock() {return speciallyStock;}
	public void setSpeciallyStock(Long s) {this.speciallyStock = s;}
	public Long getWarrantStock() {return warrantStock;}
	public void setWarrantStock(Long s) {this.warrantStock = s;}
	public String getClearUnit() {return clearUnit;}
	public void setClearUnit(String s) {this.clearUnit = s;}
	public String getClearDate() {return clearDate;}
	public void setClearDate(String s) {this.clearDate = s;}
	public String getClearWord() {return clearWord;}
	public void setClearWord(String s) {this.clearWord = s;}
	public String getClearNo() {return clearNo;}
	public void setClearNo(String s) {this.clearNo = s;}
	public String getStockIssue() {return stockIssue;}
	public void setStockIssue(String s) {this.stockIssue = s;}
	public String getSuspendUnit() {return suspendUnit;}
	public void setSuspendUnit(String s) {this.suspendUnit = s;}
	public String getSuspendDate() {return suspendDate;}
	public void setSuspendDate(String s) {this.suspendDate = s;}
	public String getSuspendWord() {return suspendWord;}
	public void setSuspendWord(String s) {this.suspendWord = s;}
	public String getSuspendNo() {return suspendNo;}
	public void setSuspendNo(String s) {this.suspendNo = s;}
	public String getOpenDate() {return openDate;}
	public void setOpenDate(String s) {this.openDate = s;}
	public String getSingleCompany() {return singleCompany;}
	public void setSingleCompany(String s) {this.singleCompany = s;}
	public String getEndDate() {return endDate;}
	public void setEndDate(String s) {this.endDate = s;}
	public String getEndWord() {return endWord;}
	public void setEndWord(String s) {this.endWord = s;}
	public String getEndNo() {return endNo;}
	public void setEndNo(String s) {this.endNo = s;}
	public String getFileNo() {return fileNo;}
	public void setFileNo(String s) {this.fileNo = s;}
	public String getStatusDescr() {return statusDescr;}
	public void setStatusDescr(String s) {this.statusDescr = s;}
	public String getFaxNo() {return faxNo;}
	public void setFaxNo(String s) {this.faxNo = s;}
	public String getClosed() {return closed;}
	public void setClosed(String s) {this.closed = s;}

}