--DROP TABLE EICM.SYSTEM_CODE;
-- Create table
CREATE TABLE EICM.SYSTEM_CODE (
	ID INTEGER not null,
	CODE_KIND VARCHAR2(10) not null,
	CODE VARCHAR2(20) not null,
	CODE_NAME VARCHAR2(200) not null,
	CODE_DESC VARCHAR2(200),
	CODE_PARAM1 VARCHAR2(200),
	CODE_PARAM2 VARCHAR2(200),
	CODE_PARAM3 VARCHAR2(200),
	REMARK VARCHAR2(400),
	SORTED INTEGER,
	ENABLE CHAR(1) not null,
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);
-- Add comments to the table 
comment on table EICM.SYSTEM_CODE is '系統參數代碼檔';
-- Add comments to the columns 
comment on column EICM.SYSTEM_CODE.ID is '主鍵值';
comment on column EICM.SYSTEM_CODE.CODE_KIND is '代碼類別';
comment on column EICM.SYSTEM_CODE.CODE is '代碼';
comment on column EICM.SYSTEM_CODE.CODE_NAME is '代碼名稱';
comment on column EICM.SYSTEM_CODE.CODE_DESC is '代碼說明';
comment on column EICM.SYSTEM_CODE.CODE_PARAM1 is '代碼參數1';
comment on column EICM.SYSTEM_CODE.CODE_PARAM2 is '代碼參數2';
comment on column EICM.SYSTEM_CODE.CODE_PARAM3 is '代碼參數3';
comment on column EICM.SYSTEM_CODE.REMARK is '備註';
comment on column EICM.SYSTEM_CODE.SORTED is '排序';
comment on column EICM.SYSTEM_CODE.ENABLE is '是否啟用(Y:啟動,N:停用 )';
comment on column EICM.SYSTEM_CODE.MOD_ID_NO is '異動人員';
comment on column EICM.SYSTEM_CODE.MOD_DATE is '異動日期';
comment on column EICM.SYSTEM_CODE.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.SYSTEM_CODE
  add constraint PK_SYSTEM_CODE primary key (ID)
  using index ;
-- Create/Recreate indexes 
create unique index EICM.UK_SYSTEM_CODE on EICM.SYSTEM_CODE (CODE_KIND, CODE);

-- Drop sequence
--DROP sequence EICM.SEQ_SYSTEM_CODE_ID;
-- Create sequence 
create sequence EICM.SEQ_SYSTEM_CODE_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_SYSTEM_CODE
Before Insert ON EICM.SYSTEM_CODE Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_SYSTEM_CODE_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.SYSTEM_CODE for EICM.SYSTEM_CODE;
create or replace synonym EICM4CMPY.SYSTEM_CODE for EICM.SYSTEM_CODE;
create or replace synonym EICM4PREFIX.SYSTEM_CODE for EICM.SYSTEM_CODE;

--GRANT
grant all on EICM.SYSTEM_CODE to EICM4AP;
grant all on EICM.SYSTEM_CODE to EICM4CMPY;
grant all on EICM.SYSTEM_CODE to EICM4PREFIX;

--DATA
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','00','代碼種類',null,null,null,null,1,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','01','系統參數資料',null,null,null,null,2,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','02','使用者群組',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','03','取件方式',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','04','公司型態',null,null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','05','核覆結果',null,null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','06','案件狀態',null,null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','07','郵寄類別',null,null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','08','申登機關',null,null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','09','馬上辦案由',null,null,null,null,10,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','10','公司狀態',null,null,null,null,11,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','11','預查公司狀態',null,null,null,null,12,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','12','身分證件別',null,null,null,null,13,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('00','13','預查種類',null,null,null,null,14,'Y');
--CODE_KIND:01
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','SSO','N','是否啟用SSO',null,null,null,1,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','OSSSync','N','是否啟用一站式同步',null,null,null,2,'N');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','IndexPathWindows','N','檢索檔路徑Windows','c:\\indexs\\diIndex','c:\\indexs\\diIndex_1030506',null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','IndexPathCentOS','N','檢索檔路徑CentOS','opt/diIndex','opt/diIndex',null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('01','ReceiveNum','1','線上收文每次收文件數',null,null,null,5,'Y');
--CODE_KIND:02
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','00','系統管理者',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','17','科長主管',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','14','收文',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','12','收文登打',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','11','審核',null,null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','13','發文',null,null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','18','檢還撤件',null,null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','16','馬上辦',null,null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','19','客服',null,null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','90','查詢',null,null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('02','XX','離職',null,null,null,null,9,'Y');
--CODE_KIND:03
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('03','1','自取',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('03','2','郵寄',null,null,null,null,2,'Y');
--CODE_KIND:04
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','01','股份有限公司',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','02','有限公司',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','03','無限公司',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','04','兩合公司',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','07','外國公司認許',null,null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('04','08','外國公司報備',null,null,null,null,6,'Y');
--CODE_KIND:05
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('05','Y','核准保留',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('05','N','不予核准',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('05','A','審查中',null,null,null,null,3,'Y');
--CODE_KIND:06
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','1','已收文',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','2','收文登打完成',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','3','已分文',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','4','承辦決行中',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','5','已審核',null,null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','6','發文登打中',null,null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','7','發文登打完成',null,null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','8','發文結案',null,null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','9','檢還',null,null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','A','撤件',null,null,null,null,10,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','B','遺失補發',null,null,null,null,11,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','C','馬上辦',null,null,null,null,12,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','D','刪檔',null,null,null,null,13,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','E','撤回退費',null,null,null,null,14,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','Q','查閱',null,null,null,null,15,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','Z','最近一次異動日期及時間',null,null,null,null,16,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','W','電子核定書寄送時間',null,null,null,null,17,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','X','電子核定書收取時間',null,null,null,null,18,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('06','Y','電子核定書查閱時間',null,null,null,null,19,'Y');
--CODE_KIND:07
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('07','01','普掛',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('07','02','限掛',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('07','03','公文掛號',null,null,null,null,3,'Y');
--CODE_KIND:08
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','01','經濟部商業司','商業司',null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','02','臺北市政府建設局','台北市府',null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','03','經濟部中部辦公室','中辦',null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','04','高雄市政府經濟發展局','高雄市府',null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','05','科技部新竹科學工業園區管理局','竹科',null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','06','經濟部加工出口區管理處','加工區',null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','07','經濟部商業司','投審會',null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','08','科技部南部科學工業園區管理局','南科',null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','09','科技部中部科學工業園區管理局','中科',null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','10','屏東農業生物技術園區籌備處','屏科',null,null,null,10,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','17','臺中市政府','臺中市府',null,null,null,11,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','21','臺南市政府','臺南市府',null,null,null,12,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','31','新北市政府','新北市府',null,null,null,13,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','72','臺北市政府建設局承辦僑外投資案件','北市-僑外',null,null,null,14,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','73','經濟部中部辦公室承辦僑外投資案件','中辦-僑外',null,null,null,15,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('08','74','高雄市政府經濟發展局承辦僑外投資案件','高雄-僑外',null,null,null,16,'Y');
--CODE_KIND:09
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','0','更正地址',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','1','更正姓名(法人名稱)',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','2','更改身份證',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','3','更換申請人',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','4','延期',null,null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','5','遺失補發',null,null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','6','增加所營',null,null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','7','刪除所營',null,null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','8','增刪所營',null,null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','9','變更組織',null,null,null,null,10,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','10','變更國籍別',null,null,null,null,11,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','11','改派訴訟及非訴訟代理人',null,null,null,null,12,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','12','公司名稱簡繁變更',null,null,null,null,13,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('09','13','法人代表人改派',null,null,null,null,14,'Y');
--CODE_KIND:10
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','01','核淮設立','核淮設立',null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','02','核淮設立但已命令解散','命令解散',null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','03','重整','重整',null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','04','解散','解散',null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','05','撤銷','撤銷',null,null,null,5,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','06','破產','破產',null,null,null,6,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','07','合併解散','合併解散',null,null,null,7,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','08','撤回認許','撤回認許',null,null,null,8,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','09','廢止','廢止',null,null,null,9,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','10','廢止認許','廢止認許',null,null,null,10,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','11','解散已清算完結','清算完結',null,null,null,11,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','12','撤銷已清算完結','清算完結',null,null,null,12,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','13','廢止已清算完結','清算完結',null,null,null,13,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','14','撤回認許已清算完結','清算完結',null,null,null,14,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','15','撤銷認許已清算完結','清算完結',null,null,null,15,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','16','廢止認許已清算完結','清算完結',null,null,null,16,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','17','撤銷認許','撤銷認許',null,null,null,17,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','18','分割解散','分割解散',null,null,null,18,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','19','終止破產','終止破產',null,null,null,19,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','20','中止破產','中止破產',null,null,null,20,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','21','塗銷破產','塗銷破產',null,null,null,21,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','22','破產程序終結(終止)','破產終結',null,null,null,22,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','23','破產程序終結(終止)清算中','破產清算中',null,null,null,23,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','24','破產已清算完結','清算完結',null,null,null,24,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','25','接管','接管',null,null,null,25,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','26','撤銷無需清算','撤銷免清算',null,null,null,26,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','27','撤銷許可','撤銷許可',null,null,null,27,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','28','廢止許可','廢止許可',null,null,null,28,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','29','撤銷許可已清算完結','清算完結',null,null,null,29,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','30','廢止許可已清算完結','清算完結',null,null,null,30,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','0801','核准報備','核准報備',null,null,'轉換外國公司及陸商狀態',31,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','0701','核准認許','核准認許',null,null,'轉換外國公司及陸商狀態',32,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','1501','核准許可登記','核准許可',null,null,'轉換外國公司及陸商狀態',33,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('10','1601','核准許可報備','核准許可報備',null,null,'轉換外國公司及陸商狀態',34,'Y');
--CODE_KIND:11
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('11','00','代收申請中',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('11','01','申請中',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('11','02','撤銷申請案',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('11','03','已核准',null,null,null,null,4,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('11','04','申請案退件',null,null,null,null,5,'Y');
--CODE_KIND:12
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('12','1','身分證',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('12','2','外僑居留證',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('12','3','華僑身分證明',null,null,null,null,3,'Y');
--CODE_KIND:13
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('13','0','設立',null,null,null,null,1,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('13','1','名稱變更',null,null,null,null,2,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('13','2','所營變更',null,null,null,null,3,'Y');
insert into SYSTEM_CODE (CODE_KIND,CODE,CODE_NAME,CODE_DESC,CODE_PARAM1,CODE_PARAM2,REMARK,SORTED,ENABLE) values ('13','3','名稱及所營變更',null,null,null,null,4,'Y');



commit;

