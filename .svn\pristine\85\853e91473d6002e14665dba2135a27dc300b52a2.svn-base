<%@ page contentType="text/html;charset=UTF-8" %>
<%@ page import="com.kangdainfo.common.util.*" %>
<jsp:useBean id="obj" scope="page" class="com.kangdainfo.common.view.home.PopBoard"/>
<%
long newsID = Common.getLong(request.getParameter("newsID"));
if (newsID>0) obj.setId(String.valueOf(newsID));
else return;

obj = (com.kangdainfo.common.view.home.PopBoard) obj.queryOne();
%>
<html>
<head>
<title>系統公告</title>
<meta http-equiv="Expires" content="-1"/>
<meta http-equiv="pragma" content="no-cache"/>
<meta http-equiv="Cache-Control" content="private"/>
<link rel="stylesheet" href="../css/default.css" type="text/css">
<script type="text/javascript" src="../js/validate.js"></script>
<script type="text/javascript" src="../js/function.js"></script>
</head>
<body>
<center>
<br>
<table class="table_form" width="96%">
	<tr>
		<td colspan="2" class="td_default_banner" >【系統公告】</td>
	</tr>
	<tr>		
		<td class="td_form" width="20%" >&nbsp;公告主旨：</td>
		<td class="td_form_left" style="text-align:left"><%=obj.getSubject() %></td>
	</tr>	
	<tr>		
		<td class="td_form">&nbsp;公告期限：</td>
		<td class="td_form_left"  style="text-align:left">
			<%=Common.formatYYYMMDD(obj.getStartDate(),4) + " ~ " + Common.formatYYYMMDD(obj.getEndDate(),4)%>
		</td>
	</tr>		
	<tr>		
		<td class="td_form" >&nbsp;公告內容：</td>
		<td class="td_form_left" valign="top"  style="text-align:left" height="150">
			<%=Common.FormatStr(obj.getContent(),"N")%>
		</td>
	</tr>	
	<tr >
		<td class="td_form_left" colspan="2" style="text-align:center">
			<input class="toolbar_default" type="button" name="btnClose" value="關閉視窗" onClick="window.close();">
		</td>
	</tr>	
</table>
</center>
</body>
</html>