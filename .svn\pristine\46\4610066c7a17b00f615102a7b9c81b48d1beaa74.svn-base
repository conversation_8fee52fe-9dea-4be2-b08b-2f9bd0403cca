package com.kangdainfo.tcfi.model.eedb.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * (EEDB1100)
 *
 */
public class Eedb1100 extends BaseModel {
	private static final long serialVersionUID = 1L;
	
	private String telixNo;
	private String caseCode;
	private String caseType;
	private String ticketNo;
	private String regUnit;

	public String getTelixNo() {
		return telixNo;
	}
	public void setTelixNo(String telixNo) {
		this.telixNo = telixNo;
	}
	public String getCaseCode() {
		return caseCode;
	}
	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}
	public String getCaseType() {
		return caseType;
	}
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}
	public String getTicketNo() {
		return ticketNo;
	}
	public void setTicketNo(String ticketNo) {
		this.ticketNo = ticketNo;
	}
	public String getRegUnit() {
		return regUnit;
	}
	public void setRegUnit(String regUnit) {
		this.regUnit = regUnit;
	}

}