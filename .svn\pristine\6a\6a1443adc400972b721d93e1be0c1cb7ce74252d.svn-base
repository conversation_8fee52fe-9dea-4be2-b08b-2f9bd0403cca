<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.util.TcfiView"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String codeKind = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("codeKind")));
String code = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("code")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(codeKind) || !"".equals(code))
	{
		List<Map<String,Object>> datas = TcfiView.getJsonBusiItemCodeKind(codeKind, code);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>