<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	if (!"".equals(q)) {
		String noNeedPay = ServiceGetter.getInstance().getNoPayMarkService().checkNoPayMark(q, true);
		out.write("{\"prefixNo\":\""+q+"\",\"noNeedPay\":\""+noNeedPay+"\"}");
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>