package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc058;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc058Dao;
import com.kangdainfo.tcfi.service.Pre8010Service;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class Pre8010ServiceImpl implements Pre8010Service{
	
	public Cedbc058 getCedbc058ById(Integer id) {
		return cedbc058Dao.queryById(id);
	}
	public List<Cedbc058> getCedbc058AllCodes() {
		return cedbc058Dao.queryAll();
	}
	public List<Cedbc058> getCedbc058BySameName(String sameName) {
		return cedbc058Dao.queryBySameName(sameName);
	}
	public Cedbc058 getCedbc058BySameName(String sameName, String sameName1) {
		return cedbc058Dao.queryBySameName(sameName, sameName1);
	}
	public Cedbc058 getCedbc058BySameNameForDelete(String sameName) {
		return cedbc058Dao.queryBySameNameForDelete(sameName);
	}
	public List<Cedbc058> getCedbc058ByStatus(String id, String... status) {
		return cedbc058Dao.queryByStatus(id, status);
	}
	public List<Cedbc058> getCedbc058ByCondition(String sameName1, String source){
		return cedbc058Dao.queryByCondition(sameName1, source);
	}
	public Cedbc058 getCedbc058ByCheck(Cedbc058 obj){
		return cedbc058Dao.queryByObj(obj);
	}
	public Cedbc058 updateCedbc058(Cedbc058 obj) {
		return cedbc058Dao.update(obj);
	}
	public void updateCedbc058BeUsed(Integer id) {
		Cedbc058 obj = cedbc058Dao.queryById(id);
		obj.setBeUsed("Y");//註記為已使用
		obj.setStatus("9");
		obj.setUpdateUser(PrefixConstants.SYS);
		obj.setUpdateDate(Datetime.getYYYMMDD());
		obj.setUpdateTime(Datetime.getHHMMSS());
		cedbc058Dao.update(obj);
	}
	public Cedbc058 insertCedbc058(Cedbc058 obj) {
		return cedbc058Dao.insert(obj);
	}
	public void deleteCedbc058(Integer id) {
		cedbc058Dao.delete(id);
	}
	public void deleteCedbc058Both(Integer id, Integer id2) {
		cedbc058Dao.delete(id);
		cedbc058Dao.delete(id2);
	}
	public void updateAllCanUseToY() {
		cedbc058Dao.updateAllCanUseToY();
	}
	
	private Cedbc058Dao cedbc058Dao;
	public Cedbc058Dao getCedbc058Dao() {return cedbc058Dao;}
	public void setCedbc058Dao(Cedbc058Dao dao) {this.cedbc058Dao = dao;}

}