--DROP TABLE EICM.SAMENAME_QUEUE;
-- Create table
CREATE TABLE EICM.SAMENAME_QUEUE (
	ID INTEGER not null,
	PREFIX_NO VARCHAR2(18) not null,
	REMARK VARCHAR2(200),
	PROCESS_TIME NUMBER(10),
	STATUS char(1),
	MOD_ID_NO VARCHAR2(20),
	MOD_DATE VARCHAR2(20),
	MOD_TIME VARCHAR2(20)
);

-- Add comments to the table 
comment on table EICM.SAMENAME_QUEUE is '同名公司比對排程檔';
-- Add comments to the columns 
comment on column EICM.SAMENAME_QUEUE.ID is '主鍵值';
comment on column EICM.SAMENAME_QUEUE.PREFIX_NO is '預查編號';
comment on column EICM.SAMENAME_QUEUE.REMARK is '處理結果備註';
comment on column EICM.SAMENAME_QUEUE.PROCESS_TIME is '處理時間(微秒)';
comment on column EICM.SAMENAME_QUEUE.STATUS is '狀態(0:待執行/1:執行中/2:執行成功/3:執行失敗)';
comment on column EICM.SAMENAME_QUEUE.MOD_ID_NO is '異動人員';
comment on column EICM.SAMENAME_QUEUE.MOD_DATE is '異動日期';
comment on column EICM.SAMENAME_QUEUE.MOD_TIME is '異動時間';

-- Create/Recreate primary, unique and foreign key constraints 
alter table EICM.SAMENAME_QUEUE
  add constraint PK_SAMENAME_QUEUE primary key (ID)
  using index ;

-- Create sequence 
create sequence EICM.SEQ_SAMENAME_QUEUE_ID
minvalue 1
maxvalue 999999999999999999999999999
start with 1
increment by 1
cache 20;

-- Create Trigger
Create Or Replace Trigger EICM.TG_SAMENAME_QUEUE
Before Insert ON EICM.SAMENAME_QUEUE Referencing New As nu For Each Row
Begin
    Select EICM.SEQ_SAMENAME_QUEUE_ID.NextVal into :nu.id From Dual;
End;

-- SYNONYM 
create or replace synonym EICM4AP.SAMENAME_QUEUE for EICM.SAMENAME_QUEUE;

-- Grant/Revoke object privileges 
grant all on EICM.SAMENAME_QUEUE to EICM4AP;


