<!-- 
程式目的：郵寄掛號登錄及維護(for 沒預查編號的案件)
程式代號：pre2008
程式日期：1030616
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2008">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2008"/>
</jsp:include>

<%

obj.findLetterNo();

if ( "save".equals(obj.getState()) ) {
	obj.update() ;
	obj = (com.kangdainfo.tcfi.view.pre.PRE2008)obj.queryOne();
	obj.findLetterNo();
} // end if
else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE2008)obj.queryOne();
	obj.findLetterNo();
} // end if
else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj = (com.kangdainfo.tcfi.view.pre.PRE2008)obj.queryOne();
		obj.setQueryAllFlag("true");
		// obj.setRcvNo(obj.getRcvNo());
	}
}
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE2008.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
else if ("assignAndSave".equals(obj.getState())) {
	obj.assignAndSave();
	obj = (com.kangdainfo.tcfi.view.pre.PRE2008)obj.queryOne();
	obj.findLetterNo();
} // end else if
else if ("rollBack".equals(obj.getState())) {
	obj.rollBack();
	obj = (com.kangdainfo.tcfi.view.pre.PRE2008)obj.queryOne();
	obj.findLetterNo();
} // else if
else if ( "alterNext".equals(obj.getState()) ) {
	obj.doAlternateNext();
	obj.findLetterNo();
} // else if
else if ( "alterBack".equals(obj.getState()) ) {
	obj.doAlternateBack();
	obj.findLetterNo();
} // else if
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>

<style>
div#insertContainer {
	position: absolute;
	z-index: 30;
	left: 0;
	top: 0;
}

iframe#insertContainerFrame {
	position: absolute;
	z-index: -1;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

	
</style>

<script type="text/javascript">

var insertDefault;
insertDefault = new Array();


function init() {
	// ---------------------------------------設定公文類別--------------------------------------------
	
	if ( form1.postType.value == "03" ) {
		document.getElementById('getDate').disabled=false;
		document.getElementById('getTime').disabled=false;
	} 
	else { 
		document.getElementById('getDate').disabled=true;
		document.getElementById('getTime').disabled=true;
	}
	
	// ---------------------------------------設定處理方法--------------------------------------------
	if ( form1.caseHow.value != "" && form1.caseHow.value != "不用寄" && form1.caseHow.value != "原址寄" && form1.caseHow.value != "改址"  )
		form1.caseHowOther.disabled=false;
	else {
		form1.caseHowOther.value="";
		form1.caseHowOther.disabled=true;
	} 
	// ---------------------------------------設定退件原因--------------------------------------------
	if ( form1.refundReason.value != "" && form1.refundReason.value != "招領逾期" && form1.refundReason.value != "查無此人" && form1.refundReason.value != "遷移不明" && form1.refundReason.value != "地址欠詳" && form1.refundReason.value != "查無此址" && form1.refundReason.value != "查無拒收"  ) 
		form1.refundReasonOther.disabled=false;
	else {
		form1.refundReasonOther.value="";
		form1.refundReasonOther.disabled=true;
	} 
	// ---------------------------------------設定領件方式--------------------------------------------
	if ( form1.getKind[0].checked || form1.getKind[1].checked) {//2024/03/17 新增線上列印
		document.getElementById('getKindRemark').disabled=true;
		form1.getKindRemark.value = "";
	} // if
	else if ( form1.getKind[2].checked ) { //2024/03/17 新增線上列印
		document.getElementById('getKindRemark').disabled=false;
	} // else if
	else {
		document.getElementById('getKindRemark').disabled=true;
	}
	
	if ( $("#letterKind").val() == "限時" || $("#letterKind").val() == "平信" ) {
		$("#letterNo").hide();
	} // if
	else {
		$("letterNo").show();
	} // else
	
	$("input[name=clear]").attr("disabled",true);
	$("input[name=confirm]").attr("disabled",true);
}


function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_prefixNo,"案件編號");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doNext":
				$('#state').val("alterNext");
				form1.submit();
				break;
			case "doBack":
				$('#state').val("alterBack");
				form1.submit();
				break;
			case "doRollBack":
				$('#state').val("rollBack");
				form1.submit();
				break;
		    case "doAssignAndSave":
		    	$('#state').val("assignAndSave");
		    	form1.submit();
		    	break;
			case "doPrintPdf":
				$('#state').val("preview") ;				
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doQueryOne":
				$('#state').val( "queryOne" ) ;
				break ;
			case "doUpdate":
				$('#state').val( "save" ) ;
				form1.submit();
				break ;
			case "doInsert":
				queryShow("insertContainer");
				break;
			case "init":
				setAllReadonly();
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function insertSystemTime(){
}

function changePostType() {
	if ( form1.postType.value == "03" ) {
		document.getElementById('getDate').disabled=false;
		document.getElementById('getTime').disabled=false;
	} 
	else { 
		document.getElementById('getDate').disabled=true;
		document.getElementById('getTime').disabled=true;
	}
}

function changeRefundReason() {
	if ( form1.refundReason.value == "其他" ) {
		document.getElementById('refundReasonOther').disabled=false;
	}
	else {
		form1.refundReasonOther.value = "";
		document.getElementById('refundReasonOther').disabled=true;
	}
}

function changeCaseHow() {
	if ( form1.caseHow.value == "其他" )
		document.getElementById('caseHowOther').disabled=false;
	else {
		form1.caseHowOther.value = "";
		document.getElementById('caseHowOther').disabled=true;
	}
}

function changeGetKind() {
	if ( form1.getKind[0].checked || form1.getKind[1].checked) {//2024/03/17 新增線上列印
		document.getElementById('getKindRemark').value="";
		document.getElementById('getKindRemark').disabled=true;
	}
	else {
		document.getElementById('getKindRemark').disabled=false;
	}
}


function keyDown() {
	if ( event.keyCode == 13 ) {
		$("#doQueryOne").click();
	}
}

function insertOK(){
    queryHidden("insertContainer");	
    $("#state").val("insert");
    setBeforePageUnload(false);
    form1.submit();
}

function insertCancel() {
	queryHidden("insertContainer");
}

function insertSystemTime(){ 
	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre2008.jsp');
	var datetime = x.split(",");
	form1.refundDate.value = datetime[0];
	form1.refundTime.value = datetime[1];
}



</script>
</head>


<body topmargin="5" onload="init()">

<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE2008'/>
</c:import>

<div id="insertContainer" style="width:600px;height:200px;display:none">
	<iframe id="insertContainerFrame"></iframe>
	<div class="queryTitle">新增案件</div>
	<table class="queryTable"  border="1">
	<tr>
        <td class="td_form">案件編號：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_caseNo" size="30" maxlength=9" value="<%=obj.getQ_caseNo()%>">
        </td>
    </tr>
    <tr> 
    <td class="td_form">申請人姓名：</td>
        <td class="td_form_white"> 
           <input class="field_Q cmex" type="text" name="q_applyName" size="30" maxlength="30" value="<%=obj.getQ_applyName()%>">
        </td>
    </tr>
    <tr>
    <td class="td_form">申請人地址：</td>
        <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_applyAddr" size="50" maxlength="150" value="<%=obj.getQ_applyAddr()%>">
        </td>
    </tr> 
    <tr>
        <td nowrap class="td_form">電話：</td>
        <td nowrap colspan="3" class="td_form_white">
            <input class="field_Q" type="text" name="q_applyTel" size="50" maxlength="17" value="<%=obj.getQ_applyTel()%>">
        </td>  
    </tr>
    <tr>
        <td nowrap class="td_form">公司名稱：</td>
        <td nowrap colspan="3" class="td_form_white">
            <input class="field_Q cmex" type="text" name="q_companyName" size="50" maxlength="60" value="<%=obj.getQ_companyName()%>">
        </td>  
    </tr>
    <tr>
        <td nowrap class="td_form">收件人姓名：</td>
        <td nowrap colspan="3" class="td_form_white">
            <input class="field_Q cmex" type="text" name="q_receiveName" size="50" maxlength="30" value="<%=obj.getQ_receiveName()%>">
        </td>  
    </tr>
    <tr>
        <td nowrap class="td_form">收件人地址：</td>
        <td nowrap colspan="3" class="td_form_white">
            <input class="field_Q" type="text" name="q_receiveAddr" size="50" maxlength="150" value="<%=obj.getQ_receiveAddr()%>">
        </td>  
    </tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" type="button" name="btnInsertOK" value="確　　定" onClick="insertOK()">
			<input class="toolbar_default" type="button" name="btnInsertCancel" value="取　　消" onClick="insertCancel()">
		</td>
	</tr>
	</table>
</div>

<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
<tr><td style="text-align:left;">
<input type="hidden" id="index" name="index" value="<%=obj.getIndex()%>">
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" id="sysTime" name="sysTime" value="<%=obj.getSysTime()%>">
<input type="hidden" id="prefixNo" name="prefixNo" value="<%=obj.getPrefixNo()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListHidden" value="N" />
	<jsp:param name="btnPreview" value="N" />
	<jsp:param name="btnCancel" value="N" />
	<jsp:param name="btnListPrint" value="N" />
</jsp:include>
<!-- 新增按鈕區 -->


	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doInsert" name="doInsert" value="新增" onClick="whatButtonFireEvent(this.name)" >
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="submit" followPK="false" id="doQueryOne" name="doQueryOne" value="查　詢" onClick="whatButtonFireEvent(this.name)" >
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doAssignAndSave" name="doAssignAndSave" value="新增並存檔">
		<script> 
    	document.getElementById('doAssignAndSave').onclick = function() {
    		if ( form1.state.value != "queryOneSuccess" && form1.state.value != "alterNext" && form1.state.value != "alterBack" ) {
        		alert("請先選擇正確的案件再給號");
    		} else {
    			if ( form1.refundReason.value == "其他" && form1.refundReasonOther.value == "" ) {
    				alert("請填寫其他退件原因");
    			}
    			else if ( form1.caseHow.value == "其他" && form1.caseHowOther.value == "" ) {
    				alert("請填寫其他處理方式");
    			}
    			else {
    				form1.getDate.disabled = false;
    				form1.getTime.disabled = false;
        			whatButtonFireEvent(this.name);
    			} 
    		}
    		
		}
		</script>
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doUpdate" name="doUpdate" value="修改並存檔" >
		<script> 
    	document.getElementById('doUpdate').onclick = function() {
    		if ( form1.state.value != "queryOneSuccess" && form1.state.value != "alterNext" && form1.state.value != "alterBack" ) {
        		alert("請先選擇正確的案件再存檔");
    		} else {
    		    if ( form1.refundReason.value == "其他" && form1.refundReasonOther.value == "" ) {
    				alert("請填寫其他退件原因");
    			}
    			else if ( form1.caseHow.value == "其他" && form1.caseHowOther.value == "" ) {
    				alert("請填寫其他處理方式");
    			}
    			else {
    				form1.getDate.disabled = false;
    				form1.getTime.disabled = false;
        			whatButtonFireEvent(this.name);
    			} // else
    		}
    		
		}
		</script>
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doRollBack" name="doRollBack" value="取消掛號">
		<script>
		document.getElementById('doRollBack').onclick = function() {
			if ( form1.state.value != "queryOneSuccess" && form1.state.value != "alterNext" && form1.state.value != "alterBack" ) {
        		alert("請先選擇正確的案件");
    		} else {
    			if ( confirm("是否取消本筆掛號資料?") ) {
    				form1.getDate.disabled = false;
					form1.getTime.disabled = false;
        			whatButtonFireEvent(this.name);
        		}
    		}
		}
		</script>
	</span>
</td>
</tr>
<tr><td class="tab_line1"> </td></tr>
<tr><td class="tab_line1"> </td></tr>
<!-- Query Area  -->



<tr><td class="bg" >
  <div id="formContainer" style="height:auto">
  <table class="table_form" width="100%" height="100%">  
       <tr>
		 <td class="td_form">案件編號：</td>
         <td class="td_form_white"> 
           <input class="field_Q" type="text" name="q_prefixNo" size="15" maxlength="9" value="<%=obj.getQ_prefixNo()%>" onKeyDown="keyDown()">
         </td>
         <td class="td_form">普掛編號：</td>
         <td class="td_form_white"> 
           <input class="field_RO" type="text" name="cLetterNo" size="15" maxlength="12" value="<%=obj.getCLetterNo()%>">           
         </td>
         <td class="td_form">限掛編號：</td>
         <td class="td_form_white"> 
           <input class="field_RO" type="text" name="lLetterNo" size="15" maxlength="12" value="<%=obj.getLLetterNo()%>">           
         </td>
       </tr>
       <tr>
         <td class="td_form">郵寄種類[C]</td>
         <td class="td_form_white">
           <select name="postType" onchange="changePostType()">
           <option value="01" <%="01".equals( obj.getPostType() )?"selected":""%>>普掛</option>
           <option value="02" <%="02".equals( obj.getPostType() )?"selected":""%>>限掛</option>
           <option value="03" <%="03".equals( obj.getPostType() )?"selected":""%>>公文掛號</option>
           <option value="04" <%="04".equals( obj.getPostType() )?"selected":""%>>平信</option>
           <option value="05" <%="05".equals( obj.getPostType() )?"selected":""%>>限時</option></select>
         </td>
         <td class="td_form">公文掛號：</td>
         <td colspan="3" class="td_form_white"> 
           <input class="field_RO" type="text" id="authLetterNo" name="authLetterNo" size="15" maxlength="12" value="<%=obj.getAuthLetterNo()%>">
         </td>
	   </tr>			
  </table>
  </div>
</td></tr>

<!-- Form area -->
<tr><td class="bg" >
  <div id="formContainer" style="height:auto">
    <table cellpadding=0 cedlsapcing=0>
      <tr>
        <td align="left" class="tab_border1" id="selMAN1" >申請人資料 </td>
      </tr>
    </table>  
    <table class="table_form" border="0" width="100%" cellpadding="2" cellspacing="0">
      <tr>
        <td class="td_form" width="15%">姓名：</td>
        <td class="td_form_white" width="30%"> 
          <input class="field cmex" type="text" name="applyName" size="30" maxlength="30" value="<%=obj.getApplyName()%>">
        </td>
        <td class="td_form">地址：</td>
        <td class="td_form_white"> 
          <input class="field" type="text" name="applyAddr" size="60" maxlength="60" value="<%=obj.getApplyAddr()%>">
        </td>
      </tr>
      <tr>
        <td class="td_form">電話：</td>
        <td class="td_form_white" > 
          <input class="field" type="text" name="applyTel" size="30" maxlength="30" value="<%=obj.getApplyTel()%>">
        </td>
        <td class="td_form">公司名稱：</td>
        <td class="td_form_white" > 
          <input class="field cmex" type="text" name="companyName" size="60" maxlength="60" value="<%=obj.getCompanyName()%>">
        </td>
      </tr>
    </table>
    <table cellpadding=0 cedlsapcing=0>
      <tr>
        <TD align="left" class="tab_border1" id="selMAN1">收件人資料</TD>
      </tr>
    </table>
    <table class="table_form" width="100%" height="100%">  
      <tr>
        <td class="td_form" width="15%"><font color="red">*</font>姓名：</td>
        <td class="td_form_white"> 
          <input class="field cmex" type="text" name="receiveName" size="15" maxlength="15" value="<%=obj.getReceiveName()%>">
        </td>
      </tr>
      <tr>  
        <td class="td_form" ><font color="red">*</font>地址：</td>
        <td class="td_form_white"> 
          <input class="field" type="text" name="receiveAddr" size="80" maxlength="30" value="<%=obj.getReceiveAddr()%>">
        </td>
      </tr>
    </table>
    <table cellpadding=0 cedlsapcing=0>
      <tr>
        <TD align="left" class="tab_border1" id="selMAN1">申請案資料</TD>
        <td>
        <span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doBack" name="doBack" value="上一筆" >
		<script>
		document.getElementById('doBack').onclick = function() {
    		if ( form1.state.value != "queryOneSuccess" && form1.state.value != "alterNext" && form1.state.value != "alterBack" ) {
        		alert("請先選擇正確的案件");
    		} else {
        		whatButtonFireEvent(this.name);
    		}
    		
		}
		</script>
	</span>
	<span id="addButtonSpan">
		<input class="toolbar_default" type="button" followPK="false" id="doNext" name="doNext" value="下一筆">
		<script>
		document.getElementById('doNext').onclick = function() {
    		if ( form1.state.value != "queryOneSuccess" && form1.state.value != "alterNext" && form1.state.value != "alterBack" ) {
        		alert("請先選擇正確的案件");
    		} else {
        		whatButtonFireEvent(this.name);
    		}
		}
		</script>
	</span>第
	<input class="field_RO" type="text" name="now" size="3" maxlength="3" value="<%=obj.getNow()%>">筆，共
	<input class="field_RO" type="text" name="total" size="3" maxlength="3" value="<%=obj.getTotal()%>">筆
        </td>
        
      </tr>
    </table>
    <table class="table_form" width="100%" height="100%">  
      <tr>
        <td class="td_form" width="15%"><font color="red">*</font>掛號號碼： </td>
        <td class="td_form_white" width="30%">
		    <input class="field_RO" type="text" id="letterKind" name="letterKind" size="08" maxlength="08" value="<%=obj.getLetterKind()%>" readOnly>
		    <input class="field_RO" type="text" id="letterNo" name="letterNo" size="15" maxlength="15" value="<%=obj.getLetterNo()%>" readOnly>
		</td> 
        <td class="td_form" width="15%">領件方式： </td>
        <td class="td_form_white">
            <input type="radio" name="getKind" value="3"  <%="3".equals( obj.getGetKind() )?"checked":""%> onclick="changeGetKind()">線上列印  <!--2024/03/17 新增線上列印 -->
		    <input type="radio" name="getKind" value="1"  <%="1".equals( obj.getGetKind() )?"checked":""%> onclick="changeGetKind()">自取
		    <input type="radio" name="getKind" value="2"  <%="2".equals( obj.getGetKind() )?"checked":""%> onclick="changeGetKind()">郵寄
		    <input class="field" type="text" id="getKindRemark" name="getKindRemark" size="10" maxlength="15" value="<%=obj.getGetKindRemark()%>">
		</td>  
      </tr>
      <tr>
        <td class="td_form" width="15%"><font color="red">*</font>取件日期：</td>
        <td colspan="3" class="td_form_white"> 
          <input class="field" type="text" id="getDate" name="getDate" size="10" maxlength="9" value="<%=obj.getGetDate()%>">
          <input class="field" type="text" id="getTime" name="getTime" size="10" maxlength="8" value="<%=obj.getGetTime()%>">
        </td>
      </tr>
      <tr>
        <td class="td_form" width="15%"><font color="red">*</font>退件日期：</td>
        <td colspan="3" class="td_form_white"> 
          <input class="field_RO" type="text" name="refundDate" size="10" maxlength="15" value="<%=obj.getRefundDate()%>" readOnly>
          <input class="field_RO" type="text" name="refundTime" size="10" maxlength="15" value="<%=obj.getRefundTime()%>" readOnly>
          <span id="addButtonSpan">
         	<input class="toolbar_default" type="button" followPK="false" id="doGetSysTime" name="doGetSysTime" value="帶入系統日期" onClick="insertSystemTime();" >
		  </span>
        </td>
      </tr>
      <tr>
        <td class="td_form"><font color="red">*</font>退件原因：</td>
        <td colspan="3" class="td_form_white"> 
            <select name="refundReason" id="refundReason" value="<%=obj.getRefundReason()%>" onchange="changeRefundReason();">
              <option></option>
              <option value="招領逾期" <%="招領逾期".equals( obj.getRefundReason() )?"selected":""%>>招領逾期</option>
              <option value="查無此人" <%="查無此人".equals( obj.getRefundReason() )?"selected":""%>>查無此人</option>
              <option value="遷移不明" <%="遷移不明".equals( obj.getRefundReason() )?"selected":""%>>遷移不明</option>
              <option value="地址欠詳" <%="地址欠詳".equals( obj.getRefundReason() )?"selected":""%>>地址欠詳</option>
              <option value="查無此址" <%="查無此址".equals( obj.getRefundReason() )?"selected":""%>>查無此址</option>
              <option value="查無拒收" <%="查無拒收".equals( obj.getRefundReason() )?"selected":""%>>查無拒收</option>
              <option value="其他"    <%=!"".equals(obj.getRefundReason()) && !"招領逾期".equals(obj.getRefundReason()) && !"查無此人".equals(obj.getRefundReason()) && !"遷移不明".equals(obj.getRefundReason()) && !"地址欠詳".equals(obj.getRefundReason())&& !"查無此址".equals(obj.getRefundReason())&& !"查無拒收".equals(obj.getRefundReason())?"selected":""%>>其他</option>
            </select>
          <input class="field" type="text" id="refundReasonOther" name="refundReasonOther" size="40" maxlength="20" value="<%=obj.getRefundReasonOther()%>" disabled>
        </td>
      </tr>
      <tr>
        <td class="td_form" width="15%">處理方式：</td>
        <td colspan="3" class="td_form_white"> 
          <select name="caseHow" id="caseHow" value="<%=obj.getCaseHow()%>" onchange="changeCaseHow();">
          	  <option	<%="".equals(obj.getCaseHow())?"selected":"" %>></option>
              <option value="不用寄" <%="不用寄".equals( obj.getCaseHow() )?"selected":""%>>不用寄</option>
              <option value="原址寄" <%="原址寄".equals( obj.getCaseHow() )?"selected":""%>>原址寄</option>
              <option value="改址" <%="改址".equals( obj.getCaseHow() )?"selected":""%>>改址</option>
              <option value="其他" <%=!"".equals(obj.getCaseHow()) && !"不用寄".equals(obj.getCaseHow())&& !"原址寄".equals(obj.getCaseHow())&& !"改址".equals(obj.getCaseHow())?"selected":""%>>其他</option>
          </select>
          <input class="field" type="text" id="caseHowOther" name="caseHowOther" size="40" maxlength="400" value="<%=obj.getCaseHowOther()%>" disabled>
        </td>
      </tr>
    </table>
  </div>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
</td></tr>

<tr><td nowrap class="bgPagging">
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>



</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>