package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import org.apache.log4j.Logger;

import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1006;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1007;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023L;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1006Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1007Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1008Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023LDao;
import com.kangdainfo.tcfi.service.BackupService;

public class BackupServiceImpl implements BackupService {

	private Logger logger = Logger.getLogger(this.getClass());

	public void doBackup(String prefixNo, String idNo) {
		if(logger.isInfoEnabled()) logger.info("[doBackup][prefixNo:"+prefixNo+"][idNo:"+idNo+"]");
		try {
			Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
			if(null!=cedb1000) {
				// 異動日期
				String updateDate = Datetime.getYYYMMDD();
				// 異動時間
				String updateTime = Datetime.getHHMMSS();
				//備份 - 基本資料
				doBackup1000To1006(cedb1000.getPrefixNo(), idNo, updateDate, updateTime);
				//備份 - 公司名稱資料
				doBackup1001To1007(cedb1000.getPrefixNo(), idNo, updateDate, updateTime);
				//備份 - 營業項目資料
				doBackup1002To1008(cedb1000.getPrefixNo(), idNo, updateDate, updateTime);
				//備份 - 收件人資料
				doBackup1023To1023L(cedb1000.getPrefixNo(), idNo, updateDate, updateTime);
			}
		} catch(Exception e) {
			e.printStackTrace();
		}
	}

	private void doBackup1000To1006(String prefixNo, String idNo, String updateDate, String updateTime) throws Exception {
		// 由資料庫中取得所有符合條件的資料
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo, null);
		if(null!=cedb1000) {
			Cedb1006 cedb1006 = new Cedb1006();
			cedb1006.setPrefixNo(cedb1000.getPrefixNo());
			cedb1006.setBanNo(cedb1000.getBanNo());
			cedb1006.setApplyKind(cedb1000.getApplyKind());
			cedb1006.setApplyName(cedb1000.getApplyName());
			cedb1006.setApplyId(cedb1000.getApplyId());
			cedb1006.setApplyAddr(cedb1000.getApplyAddr());
			cedb1006.setApplyTel(cedb1000.getApplyTel());
			cedb1006.setAttorName(cedb1000.getAttorName());
			cedb1006.setAttorNo(cedb1000.getAttorNo());
			cedb1006.setAttorAddr(cedb1000.getAttorAddr());
			cedb1006.setAttorTel(cedb1000.getAttorTel());
			cedb1006.setGetKind(cedb1000.getGetKind());
			cedb1006.setApplyType(cedb1000.getApplyType());
			cedb1006.setCompanyName(cedb1000.getCompanyName());
			cedb1006.setTelixNo(cedb1000.getTelixNo());
			cedb1006.setReceiveDate(cedb1000.getReceiveDate());
			cedb1006.setReceiveTime(cedb1000.getReceiveTime());
			cedb1006.setApproveDate(cedb1000.getApproveDate());
			cedb1006.setApproveTime(cedb1000.getApproveTime());
			cedb1006.setApproveResult(cedb1000.getApproveResult());
			cedb1006.setReserveMark(cedb1000.getReserveMark());
			cedb1006.setReserveDate(cedb1000.getReserveDate());
			cedb1006.setGetDate(cedb1000.getGetDate());
			cedb1006.setGetTime(cedb1000.getGetTime());
			cedb1006.setSpecialName(cedb1000.getSpecialName());
			cedb1006.setCompanyStus(cedb1000.getCompanyStus());
			cedb1006.setRegUnit(cedb1000.getRegUnit());
			cedb1006.setRemark(cedb1000.getRemark());
			cedb1006.setAssignDate(cedb1000.getAssignDate());
			cedb1006.setAssignTime(cedb1000.getAssignTime());
			cedb1006.setIdNo(cedb1000.getIdNo());
			cedb1006.setStaffName(cedb1000.getStaffName());
			cedb1006.setUpdateCode(cedb1000.getUpdateCode());
			cedb1006.setCodeNo(cedb1000.getCodeNo());
			cedb1006.setCodeName(cedb1000.getCodeName());
			cedb1006.setRegDate(cedb1000.getRegDate());
			cedb1006.setControlCd1(cedb1000.getControlCd1());
			cedb1006.setControlCd2(cedb1000.getControlCd2());
			cedb1006.setZoneCode(cedb1000.getZoneCode());
			cedb1006.setApproveMark(cedb1000.getApproveMark());
			cedb1006.setOldCompanyName(cedb1000.getOldCompanyName());
			cedb1006.setRemark1(cedb1000.getRemark1());
			cedb1006.setPrefixStatus(cedb1000.getPrefixStatus());
			cedb1006.setReserveDays(cedb1000.getReserveDays());
			cedb1006.setApproveRemark(cedb1000.getApproveRemark());
			cedb1006.setIsPrefixForm(cedb1000.getIsPrefixForm());
			cedb1006.setPrefixFormNo(cedb1000.getPrefixFormNo());
			cedb1006.setIsOtherForm(cedb1000.getIsOtherForm());
			cedb1006.setIsSpec(cedb1000.getIsSpec());
			cedb1006.setGetKindRemark(cedb1000.getGetKindRemark());
			cedb1006.setIsOtherSpec(cedb1000.getIsOtherSpec());
			cedb1006.setOtherSpecRemark(cedb1000.getOtherSpecRemark());
			cedb1006.setDocType(cedb1000.getDocType());
			cedb1006.setExtendMark(cedb1000.getExtendMark());
			cedb1006.setRcvCheck(cedb1000.getRcvCheck());
			cedb1006.setAtonceType(cedb1000.getAtonceType());
			cedb1006.setRefundNo(cedb1000.getRefundNo());
			cedb1006.setExtendReason(cedb1000.getExtendReason());
			cedb1006.setExtendOther(cedb1000.getExtendOther());
			cedb1006.setExtendDate(cedb1000.getExtendDate());
			cedb1006.setOtherReason(cedb1000.getOtherReason());
			cedb1006.setCloseDate(cedb1000.getCloseDate());
			cedb1006.setCloseTime(cedb1000.getCloseTime());
			cedb1006.setExtRemitEname(cedb1000.getExtRemitEname());
			//異動人員與異動時間
			cedb1006.setUpdateDate(updateDate);
			cedb1006.setUpdateTime(updateTime);
			cedb1006.setUpdateIdNo(idNo);
			cedb1006Dao.insert(cedb1006);
		}
	}

	private void doBackup1001To1007(String prefixNo, String idNo, String updateDate, String updateTime) throws Exception {
		// 由資料庫中取得所有符合條件的資料
		List<Cedb1001> cedb1001s = cedb1001Dao.findByPrefixNo(prefixNo);
		if (cedb1001s.size() < 1)
			return;

		Cedb1007 cedb1007 = null;
		for (Cedb1001 cedb1001 : cedb1001s) {
			cedb1007 = new Cedb1007();
			cedb1007.setApproveResult(cedb1001.getApproveResult());
			cedb1007.setCompanyName(cedb1001.getCompanyName());
			cedb1007.setPrefixNo(cedb1001.getPrefixNo());
			cedb1007.setRemark(cedb1001.getRemark());
			cedb1007.setSeqNo(cedb1001.getSeqNo());
			//異動人員與異動時間
			cedb1007.setUpdateDate(updateDate);
			cedb1007.setUpdateTime(updateTime);
			cedb1007.setUpdateIdNo(idNo);
			cedb1007Dao.insert(cedb1007);
		}
	}

	private void doBackup1002To1008(String prefixNo, String idNo, String updateDate, String updateTime) throws Exception {
		// 由資料庫中取得所有符合條件的資料
		List<Cedb1002> cedb1002s = cedb1002Dao.findByPrefixNo(prefixNo);
		if (cedb1002s.size() < 1)
			return;
		
		cedb1008Dao.insertFromCedb1002(updateDate, updateTime, idNo, prefixNo);
	}

	private void doBackup1023To1023L(String prefixNo, String idNo, String updateDate, String updateTime) throws Exception {
		Cedb1023L cedb1023L = null;
		// 由資料庫中取得所有符合條件的資料
		Cedb1023 cedb1023 = cedb1023Dao.findByPrefixNo(prefixNo);
		if(null!=cedb1023) {
			cedb1023L = new Cedb1023L();
			cedb1023L.setPrefixNo(cedb1023.getPrefixNo());
			cedb1023L.setGetAddr(cedb1023.getGetAddr());
			cedb1023L.setGetName(cedb1023.getGetName());
			cedb1023L.setSms(cedb1023.getSms());
			cedb1023L.setContactCel(cedb1023.getContactCel());
			cedb1023L.setChangeType(cedb1023.getChangeType());
			cedb1023L.setClosed(cedb1023.getClosed());
			cedb1023L.setOrgType(cedb1023.getOrgType());
			//異動人員與異動時間
			cedb1023L.setUpdateDate(updateDate);
			cedb1023L.setUpdateTime(updateTime);
			cedb1023L.setUpdateIdNo(idNo);
			cedb1023LDao.insert(cedb1023L);
		}
	}

	private Cedb1000Dao cedb1000Dao;
	private Cedb1001Dao cedb1001Dao;
	private Cedb1002Dao cedb1002Dao;
	private Cedb1006Dao cedb1006Dao;
	private Cedb1007Dao cedb1007Dao;
	private Cedb1008Dao cedb1008Dao;
	private Cedb1023Dao cedb1023Dao;
	private Cedb1023LDao cedb1023LDao;

	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}

	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}

	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}

	public Cedb1006Dao getCedb1006Dao() {return cedb1006Dao;}
	public void setCedb1006Dao(Cedb1006Dao dao) {this.cedb1006Dao = dao;}

	public Cedb1007Dao getCedb1007Dao() {return cedb1007Dao;}
	public void setCedb1007Dao(Cedb1007Dao dao) {this.cedb1007Dao = dao;}

	public Cedb1008Dao getCedb1008Dao() {return cedb1008Dao;}
	public void setCedb1008Dao(Cedb1008Dao dao) {this.cedb1008Dao = dao;}

	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {cedb1023Dao = dao;}

	public Cedb1023LDao getCedb1023LDao() {return cedb1023LDao;}
	public void setCedb1023LDao(Cedb1023LDao dao) {cedb1023LDao = dao;}

}