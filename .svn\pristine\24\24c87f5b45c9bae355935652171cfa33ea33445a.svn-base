package com.kangdainfo.tcfi.model.eicm.bo;

import com.kangdainfo.persistence.BaseModel;

/**
 * 預查案件基本資料異動記錄檔(CEDB1006)
 *
 */
public class Cedb1006 extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 預查編號 */
	private String prefixNo;
	/** 統一編號 */
	private String banNo;
	/** 預查種類 */
	private String applyKind;
	/** 申請人姓名 */
	private String applyName;
	/** 申請人身分證統一編號 */
	private String applyId;
	/** 申請人地址 */
	private String applyAddr;
	/** 申請人聯絡電話 */
	private String applyTel;
	/** 代理人姓名 */
	private String attorName;
	/** 代理人證書號碼 */
	private String attorNo;
	/** 代理人事務所所在地 */
	private String attorAddr;
	/** 代理人聯絡電話 */
	private String attorTel;
	/** 預查結果領取方式 */
	private String getKind;
	/** 案件性質 */
	private String applyType;
	/** 公司名稱 */
	private String companyName;
	/** 網路收文號 */
	private String telixNo;
	/** 收文日期 */
	private String receiveDate;
	/** 收文時間 */
	private String receiveTime;
	/** 核覆日期 */
	private String approveDate;
	/** 核覆時間 */
	private String approveTime;
	/** 核覆結果 */
	private String approveResult;
	/** 延展保留期限註記 */
	private String reserveMark;
	/** 保留期限 */
	private String reserveDate;
	/** 取件日期 */
	private String getDate;
	/** 取件時間 */
	private String getTime;
	/** 特取名稱 */
	private String specialName;
	/** 公司狀況 */
	private String companyStus;
	/** 申登機關代碼 */
	private String regUnit;
	/** 檢還/撤件註記說明 */
	private String remark;
	/** 分文日期 */
	private String assignDate;
	/** 分文時間 */
	private String assignTime;
	/** 承辦人員代碼 */
	private String idNo;
	/** 承辦人員姓名 */
	private String staffName;
	/** 異動原因 */
	private String updateCode;
	/** 異動內容代碼 */
	private String codeNo;
	/** 異動內容名稱 */
	private String codeName;
	/** 異動人員識別碼 */
	private String updateIdNo;
	/** 異動日期 */
	private String updateDate;
	/** 異動時間 */
	private String updateTime;
	/** 申請／撤銷申請登記日期 */
	private String regDate;
	/** 提辦案件 */
	private String controlCd1;
	/** 抽換案件 */
	private String controlCd2;
	/** 郵遞區號 */
	private String zoneCode;
	/** 檢還/撤件註記 */
	private String approveMark;
	/** 原公司名稱 */
	private String oldCompanyName;
	/** 備註 */
	private String remark1;
	/** 案件狀態 */
	private String prefixStatus;
	/** 保留天數 */
	private Integer reserveDays;
	/** 審核備註 */
	private String approveRemark;
	/** 是否有預查表附件 */
	private String isPrefixForm;
	/** 附件-預查表編號 */
	private String prefixFormNo;
	/** 是否有其他機關核准函附件 */
	private String isOtherForm;
	/** 是否有說明書附件 */
	private String isSpec;
	/** 領件方式註記 */
	private String getKindRemark;
	/** 是否有其他附件 */
	private String isOtherSpec;
	/** 其他附件註記 */
	private String otherSpecRemark;
	/** 正副本別 */
	private String docType;
	/** 展期案件註記 */
	private String extendMark;
	/** 收文確認(Y:已確認 N:未確認) */
	private String rcvCheck;
	/** 馬上辦案由 */
	private String atonceType;
	/**　退費公文號 */
	private String refundNo;
	/** 展期原因 */
	private String extendReason;
	/** 展期其他原因 */
	private String extendOther;
	/** 展期日期 */
	private String extendDate;
    /** 非屬公司法第18條同名公司之原因  */
    private String otherReason;
	/** 結案日期 */
	private String closeDate;
	/** 結案時間 */
	private String closeTime;
	
    // 自由填列事項（不納入預查審核項目）
    /** 國外匯款使用英文名稱(僅提供銀行開戶使用)  */
    private String extRemitEname;

	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getApplyAddr() {return applyAddr;}
	public void setApplyAddr(String applyAddr) {this.applyAddr = applyAddr;}
	public String getApplyId() {return applyId;}
	public void setApplyId(String applyId) {this.applyId = applyId;}
	public String getApplyKind() {return applyKind;}
	public void setApplyKind(String applyKind) {this.applyKind = applyKind;}
	public String getApplyName() {return applyName;}
	public void setApplyName(String applyName) {this.applyName = applyName;}
	public String getApplyTel() {return applyTel;}
	public void setApplyTel(String applyTel) {this.applyTel = applyTel;}
	public String getApplyType() {return applyType;}
	public void setApplyType(String applyType) {this.applyType = applyType;}
	public String getApproveDate() {return approveDate;}
	public void setApproveDate(String approveDate) {this.approveDate = approveDate;}
	public String getApproveMark() {return approveMark;}
	public void setApproveMark(String approveMark) {this.approveMark = approveMark;}
	public String getApproveResult() {return approveResult;}
	public void setApproveResult(String approveResult) {this.approveResult = approveResult;}
	public String getApproveTime() {return approveTime;}
	public void setApproveTime(String approveTime) {this.approveTime = approveTime;}
	public String getAssignDate() {return assignDate;}
	public void setAssignDate(String assignDate) {this.assignDate = assignDate;}
	public String getAssignTime() {return assignTime;}
	public void setAssignTime(String assignTime) {this.assignTime = assignTime;}
	public String getAttorAddr() {return attorAddr;}
	public void setAttorAddr(String attorAddr) {this.attorAddr = attorAddr;}
	public String getAttorName() {return attorName;}
	public void setAttorName(String attorName) {this.attorName = attorName;}
	public String getAttorNo() {return attorNo;}
	public void setAttorNo(String attorNo) {this.attorNo = attorNo;}
	public String getAttorTel() {return attorTel;}
	public void setAttorTel(String attorTel) {this.attorTel = attorTel;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getCodeName() {return codeName;}
	public void setCodeName(String codeName) {this.codeName = codeName;}
	public String getCodeNo() {return codeNo;}
	public void setCodeNo(String codeNo) {this.codeNo = codeNo;}
	public String getCompanyName() {return companyName;}
	public void setCompanyName(String companyName) {this.companyName = companyName;}
	public String getCompanyStus() {return companyStus;}
	public void setCompanyStus(String companyStus) {this.companyStus = companyStus;}
	public String getControlCd1() {return controlCd1;}
	public void setControlCd1(String controlCd1) {this.controlCd1 = controlCd1;}
	public String getControlCd2() {return controlCd2;}
	public void setControlCd2(String controlCd2) {this.controlCd2 = controlCd2;}
	public String getGetDate() {return getDate;}
	public void setGetDate(String getDate) {this.getDate = getDate;}
	public String getGetKind() {return getKind;}
	public void setGetKind(String getKind) {this.getKind = getKind;}
	public String getGetTime() {return getTime;}
	public void setGetTime(String getTime) {this.getTime = getTime;}
	public String getIdNo() {return idNo;}
	public void setIdNo(String idNo) {this.idNo = idNo;}
	public String getOldCompanyName() {return oldCompanyName;}
	public void setOldCompanyName(String oldCompanyName) {this.oldCompanyName = oldCompanyName;}
	public String getPrefixStatus() {return prefixStatus;}
	public void setPrefixStatus(String prefixStatus) {this.prefixStatus = prefixStatus;}
	public String getReceiveDate() {return receiveDate;}
	public void setReceiveDate(String receiveDate) {this.receiveDate = receiveDate;}
	public String getReceiveTime() {return receiveTime;}
	public void setReceiveTime(String receiveTime) {this.receiveTime = receiveTime;}
	public String getRegDate() {return regDate;}
	public void setRegDate(String regDate) {this.regDate = regDate;}
	public String getRegUnit() {return regUnit;}
	public void setRegUnit(String regUnit) {this.regUnit = regUnit;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}
	public String getRemark1() {return remark1;}
	public void setRemark1(String remark1) {this.remark1 = remark1;}
	public String getReserveDate() {return reserveDate;}
	public void setReserveDate(String reserveDate) {this.reserveDate = reserveDate;}
	public Integer getReserveDays() {return reserveDays;}
	public void setReserveDays(Integer reserveDays) {this.reserveDays = reserveDays;}
	public String getReserveMark() {return reserveMark;}
	public void setReserveMark(String reserveMark) {this.reserveMark = reserveMark;}
	public String getSpecialName() {return specialName;}
	public void setSpecialName(String specialName) {this.specialName = specialName;}
	public String getStaffName() {return staffName;}
	public void setStaffName(String staffName) {this.staffName = staffName;}
	public String getTelixNo() {return telixNo;}
	public void setTelixNo(String telixNo) {this.telixNo = telixNo;}
	public String getUpdateCode() {return updateCode;}
	public void setUpdateCode(String updateCode) {this.updateCode = updateCode;}
	public String getUpdateDate() {return updateDate;}
	public void setUpdateDate(String updateDate) {this.updateDate = updateDate;}
	public String getUpdateIdNo() {return updateIdNo;}
	public void setUpdateIdNo(String updateIdNo) {this.updateIdNo = updateIdNo;}
	public String getUpdateTime() {return updateTime;}
	public void setUpdateTime(String updateTime) {this.updateTime = updateTime;}
	public String getZoneCode() {return zoneCode;}
	public void setZoneCode(String zoneCode) {this.zoneCode = zoneCode;}
	public String getApproveRemark() {return approveRemark;}
	public void setApproveRemark(String approveRemark) {this.approveRemark = approveRemark;}
	public String getIsPrefixForm() {return isPrefixForm;}
	public void setIsPrefixForm(String isPrefixForm) {this.isPrefixForm = isPrefixForm;}
	public String getPrefixFormNo() {return prefixFormNo;}
	public void setPrefixFormNo(String prefixFormNo) {this.prefixFormNo = prefixFormNo;}
	public String getIsOtherForm() {return isOtherForm;}
	public void setIsOtherForm(String isOtherForm) {this.isOtherForm = isOtherForm;}
	public String getIsSpec() {return isSpec;}
	public void setIsSpec(String isSpec) {this.isSpec = isSpec;}
	public String getGetKindRemark() {return getKindRemark;}
	public void setGetKindRemark(String getKindRemark) {this.getKindRemark = getKindRemark;}
	public String getIsOtherSpec() {return isOtherSpec;}
	public void setIsOtherSpec(String isOtherSpec) {this.isOtherSpec = isOtherSpec;}
	public String getOtherSpecRemark() {return otherSpecRemark;}
	public void setOtherSpecRemark(String otherSpecRemark) {this.otherSpecRemark = otherSpecRemark;}
	public String getDocType() {return docType;}
	public void setDocType(String docType) {this.docType = docType;}
	public String getExtendMark() {return extendMark;}
	public void setExtendMark(String extendMark) {this.extendMark = extendMark;}
	public String getRcvCheck() {return rcvCheck;}
	public void setRcvCheck(String rcvCheck) {this.rcvCheck = rcvCheck;}
	public String getAtonceType() {return atonceType;}
	public void setAtonceType(String atonceType) {this.atonceType = atonceType;}

	public String getRefundNo() {return refundNo;}
	public void setRefundNo(String s) {this.refundNo = s;}
	public String getExtendReason() {return extendReason;}
	public void setExtendReason(String s) {this.extendReason = s;}
	public String getExtendOther() {return extendOther;}
	public void setExtendOther(String s) {this.extendOther = s;}
	public String getExtendDate() {return extendDate;}
	public void setExtendDate(String s) {this.extendDate = s;}
	public String getOtherReason() {return otherReason;}
	public void setOtherReason(String s) {this.otherReason = s;}

	public String getCloseDate() {return closeDate;}
	public void setCloseDate(String s) {this.closeDate = s;}
	public String getCloseTime() {return closeTime;}
	public void setCloseTime(String s) {this.closeTime = s;}
	
	public String getExtRemitEname() {return extRemitEname;}
	public void setExtRemitEname(String s) {this.extRemitEname = s;}
}
