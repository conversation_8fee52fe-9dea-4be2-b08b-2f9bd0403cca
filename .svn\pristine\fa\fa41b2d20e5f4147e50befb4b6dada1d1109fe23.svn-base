package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc004;
import com.kangdainfo.util.lang.CommonStringUtils;

public class Cedbc004Dao extends BaseDaoJdbc implements RowMapper<Cedbc004> {

	private static final String DEFAULT_ORDER = "ORDER BY SEQ_NO";
	private static final String SQL_findAll = "SELECT * FROM CEDBC004";
	
	public List<Cedbc004> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public java.util.List<Cedbc004> find(Cedbc004 bo) {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if ( !"".equals(Common.get(bo.getSeqNo())) ) {
			sqljob.appendSQLCondition("SEQ_NO LIKE ?");
			sqljob.addLikeParameter(bo.getSeqNo());
		}
		if ( !"".equals(Common.get(bo.getBanWord())) ) {
			sqljob.appendSQLCondition("BAN_WORD LIKE ?");
			sqljob.addLikeParameter(bo.getBanWord());
		}

		if ( !"".equals(Common.get(bo.getLetterNo())) ) {
			sqljob.appendSQLCondition("LETTER_NO LIKE ?");
			sqljob.addLikeParameter(bo.getLetterNo());
		}
		
		if ( !"".equals(Common.get(bo.getLetterDesc())) ) {
			sqljob.appendSQLCondition("LETTER_DESC LIKE ?");
			sqljob.addLikeParameter(bo.getLetterDesc());
		}
		
		if ( !"".equals(Common.get(bo.getModIdNo())) ) {
			sqljob.appendSQLCondition("MOD_ID_NO = ?");
			sqljob.addParameter(bo.getModIdNo());
		}
		
		if ( !"".equals(Common.get(bo.getModDate())) ) {
			sqljob.appendSQLCondition("MOD_DATE = ?");
			sqljob.addParameter(bo.getModDate());
		}
		sqljob.appendSQL(DEFAULT_ORDER);
		if(logger.isInfoEnabled()) logger.info(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public void insert(Cedbc004 bo) {
		if(null!=bo) {
			//check exist
			Cedbc004 t = findByBanWord(bo.getBanWord());
			if(null==t) {
				//insert
				SQLJob sqljob = new SQLJob();
				sqljob.appendSQL("INSERT INTO CEDBC004 (");
				sqljob.appendSQL("SEQ_NO,BAN_WORD,MOD_ID_NO,MOD_DATE,MOD_TIME,UNIT,LETTER_DATE,LETTER_NO,LETTER_DESC");
				sqljob.appendSQL(")");
				sqljob.appendSQL("SELECT trim(to_char(max(seq_no)+1,'000')),?,?,?,?,?,?,?,? from CEDBC004");
				sqljob.addParameter(bo.getBanWord());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getModIdNo());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getModDate());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getModTime());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getUnit());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getLetterDate());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getLetterNo());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				sqljob.addParameter(bo.getLetterDesc());
				sqljob.addSqltypes(java.sql.Types.VARCHAR);
				if(logger.isInfoEnabled()) logger.info(sqljob);
				getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
			}
		}
	}
	
	/**
	 * 依特定禁用名稱序號查詢
	 * @param seqNo
	 * @return
	 */
	public Cedbc004 findBySeqNo(String seqNo) {
		if(null==seqNo || "".equals(seqNo)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM Cedbc004 where trim(seq_no) = ?");
		sqljob.addParameter(seqNo);
		List<Cedbc004> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	/**
	 * 依特定禁用名稱查詢
	 * @param banWord
	 * @return 禁用名稱
	 */
	public Cedbc004 findByBanWord(String banWord) {
		if(null==banWord || "".equals(banWord)) return null;
		SQLJob sqljob = new SQLJob("SELECT * FROM Cedbc004 where trim(ban_Word) = ?");
		sqljob.addParameter(banWord);
		List<Cedbc004> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		if(null!=list && !list.isEmpty())
			return list.get(0);
		return null;
	}

	public Cedbc004 update(Cedbc004 bo) {
		//check pk
		if(null==bo) return null;
		if(CommonStringUtils.isEmpty(bo.getSeqNo())) return null;
		//check exist
		Cedbc004 t = findByBanWord(bo.getBanWord());
		if(null==t) return t;
		else {
			//update
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("UPDATE CEDBC004 SET");
			sqljob.appendSQL(" BAN_WORD = ?");
			sqljob.appendSQL(",MOD_ID_NO = ?");
			sqljob.appendSQL(",MOD_DATE = ?");
			sqljob.appendSQL(",MOD_TIME = ?");
			sqljob.appendSQL(",UNIT = ?");
			sqljob.appendSQL(",LETTER_DATE = ?");
			sqljob.appendSQL(",LETTER_NO = ?");
			sqljob.appendSQL(",LETTER_DESC = ?");
			
			sqljob.appendSQL(" WHERE SEQ_NO = ?");
			sqljob.addParameter(bo.getBanWord());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getModIdNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getModDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getModTime());
			
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getUnit());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getLetterDate());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getLetterNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getLetterDesc());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			sqljob.addParameter(bo.getSeqNo());
			
			if(logger.isInfoEnabled()) logger.info(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
			return findBySeqNo(bo.getSeqNo());
		}
	}

	public void delete(Cedbc004 bo) {
		//check pk
		if(null!=bo && CommonStringUtils.isNotEmpty(bo.getSeqNo()) ) {
			//delete
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("DELETE FROM CEDBC004 WHERE SEQ_NO = ?");
			sqljob.addParameter(bo.getSeqNo());
			sqljob.addSqltypes(java.sql.Types.VARCHAR);
			if(logger.isInfoEnabled()) logger.info(sqljob);
			getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
		}
	}
	
	public Cedbc004 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc004 obj = null;
		if(null!=rs) {
			obj = new Cedbc004();
			obj.setSeqNo(rs.getString("SEQ_NO"));
			obj.setBanWord(rs.getString("BAN_WORD"));
			obj.setModIdNo(rs.getString("MOD_ID_NO"));
			obj.setModDate(rs.getString("MOD_DATE"));
			obj.setModTime(rs.getString("MOD_TIME"));
			
			obj.setUnit(rs.getString("UNIT"));
			obj.setLetterDate(rs.getString("LETTER_DATE"));
			obj.setLetterNo(rs.getString("LETTER_NO"));
			obj.setLetterDesc(rs.getString("LETTER_DESC"));
		}
		return obj;
	}

}