package com.kangdainfo.tcfi.model.crmsmoea.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.crmsmoea.bo.CsyssUser;
/**
 * 公司登記系統使用者
 * <AUTHOR>
 * 2024/05/17
 */
public class CsyssUserDao extends BaseDaoJdbc implements RowMapper<CsyssUser> {
	
	public CsyssUser getSatffInfoByUserIdAndStaffName(String userId) {
		SQLJob sqljob = new SQLJob("SELECT * FROM CRMSMOEA.CSYSS_USER WHERE USER_ID = ?");
		sqljob.addParameter(userId);
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		List<CsyssUser> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty() ? null : list.get(0);
	}

	@Override
	public CsyssUser mapRow(ResultSet rs, int idx) throws SQLException {
		CsyssUser obj = null;
		
		if(rs != null) {
			obj = new CsyssUser();
			obj.setUserId(rs.getString("USER_ID"));
			obj.setUserName(rs.getString("USERNAME"));
			obj.setPassword(rs.getString("PASSWORD"));
			obj.setEmail(rs.getString("EMAIL"));
			obj.setWorkzone(rs.getString("WORKZONE"));
			obj.setUserstatus(rs.getString("USERSTATUS"));
			obj.setResetpass(rs.getString("RESETPASS"));
			obj.setStaffCode(rs.getString("STAFF_CODE"));
			obj.setBirthday(rs.getString("BIRTHDAY"));
			obj.setOrgizationalcode(rs.getString("ORGANIZATIONALCODE"));
			obj.setTitle(rs.getString("TITLE"));
			obj.setPostalcode(rs.getString("POSTALCODE"));
			obj.setPostaladdress(rs.getString("POSTALADDRESS"));
			obj.setTelephonenumber(rs.getString("TELEPHONENUMBER"));
			obj.setMobile(rs.getString("MOBILE"));
			obj.setPwddate(rs.getString("PWDDATE"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
		}
		
		return obj;
	}
}
