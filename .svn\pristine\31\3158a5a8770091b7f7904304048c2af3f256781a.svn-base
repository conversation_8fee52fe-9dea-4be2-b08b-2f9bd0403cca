package com.kangdainfo.tcfi.service.impl;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.apache.log4j.Logger;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.FlowLog;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.FlowLogDao;
import com.kangdainfo.tcfi.model.icms.dao.CsmdWorkDayDao;
import com.kangdainfo.tcfi.service.CaseFlowService;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.util.lang.CommonStringUtils;

public class CaseFlowServiceImpl implements CaseFlowService {
	private Logger logger = Logger.getLogger(this.getClass());

	public void addCaseFlow(Cedb1000 cedb1000, String processStatus) {
		if(logger.isInfoEnabled()) logger.info("[addCaseFlow]");
		if(null!=cedb1000) {
			String prefixNo = cedb1000.getPrefixNo();
			Float workDay = null;
			boolean canUpdate = true;
			if( PrefixConstants.PREFIX_STATUS_W.equals(processStatus) ) {
				canUpdate = false;//電子核定書寄送時間(W) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_X.equals(processStatus) ) {
				canUpdate = false;//電子核定書收取時間(X) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_Y.equals(processStatus) ) {
				canUpdate = false;//電子核定書查閱時間(Y) - 時間不要覆蓋
			}

			Cedb1010 cedb1010 = cedb1010Dao.findByUniqueKey(prefixNo, cedb1000.getUpdateIdNo(), processStatus);
			if(null==cedb1010) {
				if( PrefixConstants.PREFIX_STATUS_Q.equals(processStatus) ) {
					//查閱 (Q)可寫多筆
					//insert
					cedb1010 = new Cedb1010();
					cedb1010.setPrefixNo(prefixNo);
					cedb1010.setIdNo(cedb1000.getUpdateIdNo());
					cedb1010.setProcessDate(cedb1000.getUpdateDate());
					cedb1010.setProcessTime(cedb1000.getUpdateTime());
					cedb1010.setProcessStatus(processStatus);
					workDay = getWorkDay(cedb1010);
					cedb1010.setWorkDay(workDay);
					cedb1010Dao.insert(cedb1010);
				} else {
					cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, processStatus);
					if(null==cedb1010) {
						//insert
						cedb1010 = new Cedb1010();
						cedb1010.setPrefixNo(prefixNo);
						cedb1010.setIdNo(cedb1000.getUpdateIdNo());
						cedb1010.setProcessDate(cedb1000.getUpdateDate());
						cedb1010.setProcessTime(cedb1000.getUpdateTime());
						cedb1010.setProcessStatus(processStatus);
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.insert(cedb1010);
					} else {
						if (canUpdate) {
							//update
							cedb1010.setIdNo(cedb1000.getUpdateIdNo());
							cedb1010.setProcessDate(cedb1000.getUpdateDate());
							cedb1010.setProcessTime(cedb1000.getUpdateTime());
							workDay = getWorkDay(cedb1010);
							cedb1010.setWorkDay(workDay);
							cedb1010Dao.updateByPrefixNoAndStatus(cedb1010);
						} // if
					}
				}
			} else {
				if (canUpdate) {
					//update
					cedb1010.setProcessDate(cedb1000.getUpdateDate());
					cedb1010.setProcessTime(cedb1000.getUpdateTime());
					workDay = getWorkDay(cedb1010);
					cedb1010.setWorkDay(workDay);
					cedb1010Dao.updateByPrefixNoAndIdNoAndStatus(cedb1010);
				}
			}
			//多寫案件歷程FLOW_LOG
			insertFlowLog(prefixNo, cedb1000.getUpdateIdNo(), processStatus, workDay);
		}
	}

	public void addCaseFlow(String prefixNo, String idNo, String processStatus) {
		if(logger.isInfoEnabled()) logger.info("[addCaseFlow]");
		if( null!=prefixNo && !"".equals(prefixNo) ) {
			Float workDay = null;
			boolean canUpdate = true;
			if( PrefixConstants.PREFIX_STATUS_W.equals(processStatus) ) {
				canUpdate = false;//電子核定書寄送時間(W) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_X.equals(processStatus) ) {
				canUpdate = false;//電子核定書收取時間(X) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_Y.equals(processStatus) ) {
				canUpdate = false;//電子核定書查閱時間(Y) - 時間不要覆蓋
			}
			Cedb1010 cedb1010 = null;
			if( PrefixConstants.PREFIX_STATUS_Q.equals(processStatus) ) {
				cedb1010 = cedb1010Dao.findByUniqueKey(prefixNo, idNo, processStatus);
				if(null==cedb1010) {
					//insert
					cedb1010 = new Cedb1010();
					cedb1010.setPrefixNo(prefixNo);
					cedb1010.setIdNo(idNo);
					cedb1010.setProcessDate(Datetime.getYYYMMDD());
					cedb1010.setProcessTime(Datetime.getHHMMSS());
					cedb1010.setProcessStatus(processStatus);
					workDay = getWorkDay(cedb1010);
					cedb1010.setWorkDay(workDay);
					cedb1010Dao.insert(cedb1010);
				} else {
					if (canUpdate) {
						//update
						cedb1010.setIdNo(idNo);
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.updateByPrefixNoAndIdNoAndStatus(cedb1010);
					} // if
				}
			} else {
				cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, processStatus);
				if(null==cedb1010) {
					//insert
					cedb1010 = new Cedb1010();
					cedb1010.setPrefixNo(prefixNo);
					cedb1010.setIdNo(idNo);
					cedb1010.setProcessDate(Datetime.getYYYMMDD());
					cedb1010.setProcessTime(Datetime.getHHMMSS());
					cedb1010.setProcessStatus(processStatus);
					workDay = getWorkDay(cedb1010);
					cedb1010.setWorkDay(workDay);
					cedb1010Dao.insert(cedb1010);
				} else {
					if (canUpdate) {
						//update
						cedb1010.setIdNo(idNo);
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.updateByPrefixNoAndStatus(cedb1010);
					} // if
				}
			}
			//多寫案件歷程FLOW_LOG
			insertFlowLog(prefixNo, idNo, processStatus, workDay);
		}
	}
	
	public void addCaseFlowForApprove(String prefixNo, String idNo, String processStatus, boolean mod1010) {
		// 20160518
		// 改為進function時先判斷processStatus是否為Q; 否的話走正常流程, 是的話則看該使用者之前有無查閱的紀錄;
		// 有則update以前的紀錄, 否則表示這次查閱者為另一個使用者, 可以insert新的紀錄;
		// 如此processStatus != Q 的情況可減少一次Query的次數
		if(logger.isInfoEnabled()) logger.info("[addCaseFlow]");
		if( null!=prefixNo && !"".equals(prefixNo) ) {
			Float workDay = null;
			boolean canUpdate = true;
			if( PrefixConstants.PREFIX_STATUS_W.equals(processStatus) ) {
				canUpdate = false;//電子核定書寄送時間(W) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_X.equals(processStatus) ) {
				canUpdate = false;//電子核定書收取時間(X) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_Y.equals(processStatus) ) {
				canUpdate = false;//電子核定書查閱時間(Y) - 時間不要覆蓋
			}
			Cedb1010 cedb1010 = null;
			if( PrefixConstants.PREFIX_STATUS_Q.equals(processStatus) ) {
				cedb1010 = cedb1010Dao.findByUniqueKey(prefixNo, idNo, processStatus);
				if (cedb1010 == null) {
					if (mod1010) {
						cedb1010 = new Cedb1010();
						cedb1010.setPrefixNo(prefixNo);
						cedb1010.setIdNo(idNo);
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						cedb1010.setProcessStatus(processStatus);
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.insert(cedb1010);
					}
				} else {
					if(canUpdate) {
						if (mod1010) {
							cedb1010.setIdNo(idNo);
							cedb1010.setProcessDate(Datetime.getYYYMMDD());
							cedb1010.setProcessTime(Datetime.getHHMMSS());
							workDay = getWorkDay(cedb1010);
							cedb1010.setWorkDay(workDay);
							cedb1010Dao.updateByPrefixNoAndIdNoAndStatus(cedb1010);
						}
					}
				}
			} else {
				cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, processStatus);
				if(null==cedb1010) {
					//insert
					if (mod1010) {
						cedb1010 = new Cedb1010();
						cedb1010.setPrefixNo(prefixNo);
						cedb1010.setIdNo(idNo);
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						cedb1010.setProcessStatus(processStatus);
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.insert(cedb1010);
					}
				} else {
					if(canUpdate) {
						//update
						if (mod1010) {
							cedb1010.setIdNo(idNo);
							cedb1010.setProcessDate(Datetime.getYYYMMDD());
							cedb1010.setProcessTime(Datetime.getHHMMSS());
							workDay = getWorkDay(cedb1010);
							cedb1010.setWorkDay(workDay);
							cedb1010Dao.updateByPrefixNoAndStatus(cedb1010);
						} 
					}
				}
			}
			//多寫案件歷程FLOW_LOG
			insertFlowLog(prefixNo, idNo, processStatus, workDay);
		}
	}
	
	public void addCaseFlowForAtonce(String prefixNo, String idNo, String processStatus, String atonceType) {
		if(logger.isInfoEnabled()) logger.info("[addCaseFlow]");
		if( null!=prefixNo && !"".equals(prefixNo) ) {
			Float workDay = null;
			boolean canUpdate = true;
			if( PrefixConstants.PREFIX_STATUS_W.equals(processStatus) ) {
				canUpdate = false;//電子核定書寄送時間(W) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_X.equals(processStatus) ) {
				canUpdate = false;//電子核定書收取時間(X) - 時間不要覆蓋
			} else if( PrefixConstants.PREFIX_STATUS_Y.equals(processStatus) ) {
				canUpdate = false;//電子核定書查閱時間(Y) - 時間不要覆蓋
			}

			Cedb1010 cedb1010 = cedb1010Dao.findByUniqueKey(prefixNo, idNo, processStatus);
			if(null==cedb1010) {
				if( PrefixConstants.PREFIX_STATUS_Q.equals(processStatus) ) {
					//查閱 (Q)可寫多筆
					//insert
					cedb1010 = new Cedb1010();
					cedb1010.setPrefixNo(prefixNo);
					cedb1010.setIdNo(idNo);
					cedb1010.setProcessDate(Datetime.getYYYMMDD());
					cedb1010.setProcessTime(Datetime.getHHMMSS());
					cedb1010.setProcessStatus(processStatus);
					workDay = getWorkDay(cedb1010);
					cedb1010.setWorkDay(workDay);
					cedb1010Dao.insert(cedb1010);
				} else {
					cedb1010 = cedb1010Dao.findByPrefixNoAndStatus(prefixNo, processStatus);
					if(null==cedb1010) {
						//insert
						cedb1010 = new Cedb1010();
						cedb1010.setPrefixNo(prefixNo);
						cedb1010.setIdNo(idNo);
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						cedb1010.setProcessStatus(processStatus);
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.insert(cedb1010);
					} else {
						if(canUpdate) {
							//update
							cedb1010.setIdNo(idNo);
							cedb1010.setProcessDate(Datetime.getYYYMMDD());
							cedb1010.setProcessTime(Datetime.getHHMMSS());
							workDay = getWorkDay(cedb1010);
							cedb1010.setWorkDay(workDay);
							cedb1010Dao.updateByPrefixNoAndStatus(cedb1010);
						}
					}
				}
			} else {
				//update
				if(canUpdate) {
					if ( "".equals(Common.get(atonceType))) {
						removeCaseFlow(prefixNo, processStatus);
						removeCaseFlow(prefixNo, PrefixConstants.PREFIX_STATUS_B);
					} // if
					else {
						cedb1010.setProcessDate(Datetime.getYYYMMDD());
						cedb1010.setProcessTime(Datetime.getHHMMSS());
						workDay = getWorkDay(cedb1010);
						cedb1010.setWorkDay(workDay);
						cedb1010Dao.updateByPrefixNoAndIdNoAndStatus(cedb1010);
					} // else
				}
			}
			//多寫案件歷程FLOW_LOG
			insertFlowLog(prefixNo, idNo, processStatus, workDay);
		}
	}

	public void removeCaseFlow(String prefixNo, String processStatus) {
		if(logger.isDebugEnabled()) logger.debug("[removeCaseFlow]");
		if( CommonStringUtils.isNotEmpty(prefixNo) && CommonStringUtils.isNotEmpty(processStatus) ) {
			cedb1010Dao.deleteByPrefixNoAndStatus(prefixNo, processStatus);
		}
	}

	/** 新增案件歷程FLOW_LOG */
	private void insertFlowLog(String prefixNo, String idNo, String processStatus, Float workDay) {
		FlowLog flowLog = new FlowLog();
		flowLog.setPrefixNo(prefixNo);
		flowLog.setIdNo(idNo);
		flowLog.setProcessDate(Datetime.getYYYMMDD());
		flowLog.setProcessTime(Datetime.getHHMMSS());
		flowLog.setProcessStatus(processStatus);
		flowLog.setWorkDay(workDay);
		flowLog.setModIdNo(idNo);
		flowLog.setModDate(Datetime.getYYYMMDD());
		flowLog.setModTime(Datetime.getHHMMSS());
		flowLogDao.insert(flowLog);
	}

	public Float getWorkDay(Cedb1010 cedb1010) {
		if(logger.isDebugEnabled()) logger.debug("[getWorkDay]");
		Float result = new Float(0.0);
		if( PrefixConstants.PREFIX_STATUS_1.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_9.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_A.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_B.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_C.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_E.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_Q.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_W.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_X.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_Y.equals(cedb1010.getProcessStatus()) ) {
			result = new Float(0.0);
		} else if( PrefixConstants.PREFIX_STATUS_3.equals(cedb1010.getProcessStatus()) ) {
			result = isExtendCase(cedb1010.getPrefixNo())?new Float(0.08):countWorkDay(cedb1010);
		} else if( PrefixConstants.PREFIX_STATUS_4.equals(cedb1010.getProcessStatus()) ) {
			result = isExtendCase(cedb1010.getPrefixNo())?new Float(0.08):countWorkDay(cedb1010);
		} else if( PrefixConstants.PREFIX_STATUS_5.equals(cedb1010.getProcessStatus()) ) {
			result = isExtendCase(cedb1010.getPrefixNo())?new Float(0.08):countWorkDay(cedb1010);
		} else {
			result = countWorkDay(cedb1010);
		}
		if(logger.isDebugEnabled()) logger.debug("[getWorkDay][result:"+result+"]");
		if(result < 0f) {
			result = new Float(0.0);
		}
		DecimalFormat df = new DecimalFormat();
		df.setMaximumFractionDigits(2);
		df.setDecimalSeparatorAlwaysShown(false);
		df.setGroupingUsed(false); //取消千分號
		return Float.parseFloat( df.format( result ) );
	}
	
	/** 判斷是否為展期案件(有申請展延時，辦理天數預設為 0.08) */
	private boolean isExtendCase(String prefixNo) {
		boolean result = false;
		if(!"".equals(Common.get(prefixNo))) {
			Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo);
			if(null!=cedb1000 && "Y".equalsIgnoreCase(cedb1000.getExtendMark())) {
				result = true;
			}
		}
		return result;
	}
	
	private Float countWorkDay(Cedb1010 curr) {
		//default value is 0
		Float result = new Float(0.00);
		Cedb1010 lastOne = cedb1010Dao.findLastOne(curr);
		if(null!=lastOne) {
			result = countWorkDay(lastOne.getProcessDate(),lastOne.getProcessTime(),curr.getProcessDate(),curr.getProcessTime());
		}
		return result;
	}

	public Float countWorkDay(String startDate, String startTime, String endDate, String endTime) {
		if(logger.isDebugEnabled()) logger.debug("[countWorkDay][start:"+startDate+" "+startTime+"][end:"+endDate+" "+endTime+"]");
		Float result = new Float(0.00);
		if( "".equals(Common.get(startDate)) ) return result;
		if( "".equals(Common.get(startTime)) ) return result;
		if( "".equals(Common.get(endDate)) ) return result;
		if( "".equals(Common.get(endTime)) ) return result;
		if( startDate.compareTo(endDate) > 0 ) {
			//起日 大於 迄日, 無法計算, 回傳 0
			return new Float(0.00);
			//起日 大於 迄日, 日期時間對調
			//String tempDate = startDate;
			//String tempTime = startTime;
			//startDate = endDate;
			//startTime = endTime;
			//endDate = tempDate;
			//endTime = tempTime;
		} else if( startDate.compareTo(endDate) == 0 ) {
			//起日 同 迄日, 起時 大於 迄時, 無法計算, 回傳 0
			if( startTime.compareTo(endTime) > 0 ) {
				return new Float(0.00);
			//	起日 同 迄日, 起時 大於 迄時, 時間對調
			//	String tempTime = startTime;
			//	startTime = endTime;
			//	endTime = tempTime;
			}
		}

		WorkDay workDay = new WorkDay(result,startDate,startTime,endDate,endTime);
		workDay = countDay(workDay);
		result = workDay.getDays();
		if(logger.isDebugEnabled()) logger.debug("[countWorkDay][result:"+result+"]");
		return result;
	}

	/** 上午上班開始時間 08:30 */
	private static final String DEFAULT_FIRST_HALF_START = "083000";
	/** 上午上班結束時間 12:30 */
	private static final String DEFAULT_FIRST_HALF_END = "123059";
	/** 下午上班開始時間 13:30 */
	private static final String DEFAULT_SECOND_HALF_START = "133000";
	/** 下午上班結束時間  17:30 */
	private static final String DEFAULT_SECOND_HALF_END = "173059";

	/** 計算天數 */
	private WorkDay countDay(WorkDay workDay) {
		if(null!=workDay) {
			Float days = workDay.getDays();
			String sd = workDay.getStartDate();
			String st = workDay.getStartTime();
			String ed = workDay.getEndDate();
			String et = workDay.getEndTime();
			
			if( sd.equals(ed) ) {
				//1.同一天
				//1.1.判斷是否為假日
				if( csmdWorkDayDao.checkIsHoliday(sd) ) {
					//假日就略過不算
				} else {
					workDay = countTime(workDay);
					days+= workDay.getDays();
				}
			} else {
				//2.不同天
				//2.1.先算到開始日結束
				WorkDay start = new WorkDay(0f,sd,st,sd,DEFAULT_SECOND_HALF_END);
				start = countDay(start);
				days+= start.getDays();
				
				//2.2.相差超過一天
				sd = dateAdd(sd, 1);
				if( ed.compareTo(sd) > 0 ) {
					days+= csmdWorkDayDao.sumWorkDays(sd,dateAdd(ed, -1));
					sd = ed;
				}

				//2.3.再算開始日隔天到結束日
				WorkDay next = new WorkDay(0f,sd,DEFAULT_FIRST_HALF_START,ed,et);
				next = countDay(next);
				days+= next.getDays();
			}
			workDay.setDays(days);
		}
		return workDay;
	}

	/** 計算時間 */
	private WorkDay countTime(WorkDay workDay) {
		if(null!=workDay) {
			Float days = workDay.getDays();
			String startDate = workDay.getStartDate();
			String startTime = workDay.getStartTime();
			String endDate = workDay.getEndDate();
			String endTime = workDay.getEndTime();

			//判斷開始時間
			if( DEFAULT_FIRST_HALF_START.compareTo(startTime)>0 ) {
				//還沒上班就開始, 算上午開始時間
				startTime = DEFAULT_FIRST_HALF_START;
			} else if( DEFAULT_FIRST_HALF_END.compareTo(startTime)<0 && DEFAULT_SECOND_HALF_START.compareTo(startTime)>0 ) {
				//中午休息時就開始, 算下午開始時間
				startTime = DEFAULT_SECOND_HALF_START;
			} else if( DEFAULT_SECOND_HALF_END.compareTo(startTime)<0 ) {
				//下班以後才開始, 今天就不算
				startTime = endTime;
			}

			//判斷結束時間
			if( DEFAULT_FIRST_HALF_START.compareTo(endTime)>0 ) {
				//還沒上班就結束, 今天就不算
				endTime = startTime;
			} else if( DEFAULT_FIRST_HALF_END.compareTo(endTime)<0 && DEFAULT_SECOND_HALF_START.compareTo(endTime)>0 ) {
				//中午休息時就結束, 算上午結束時間
				endTime = DEFAULT_FIRST_HALF_END;
			} else if( DEFAULT_SECOND_HALF_END.compareTo(endTime)<0 ) {
				//下班以後才結束, 算下午結束時間
				endTime = DEFAULT_SECOND_HALF_END;
			}

			if( startTime.equals(endTime) ) {
				//開始時間等於結束時間, 不算時數
			} else {
				if( DEFAULT_FIRST_HALF_END.compareTo(startTime)>=0 && DEFAULT_FIRST_HALF_END.compareTo(endTime)>=0 ) {
					//早上開始，早上結束
					days+= subtractTime(startTime, endTime);
				} else if( DEFAULT_FIRST_HALF_END.compareTo(startTime)>0 && DEFAULT_SECOND_HALF_END.compareTo(endTime)>=0 ) {
					//早上開始，下午結束
					//先算早上開始到中午休息
					WorkDay start = new WorkDay(0f,startDate,startTime,endDate,DEFAULT_FIRST_HALF_END);
					start = countTime(start);
					days+= start.getDays();
					//再算下午開始到結束
					WorkDay next = new WorkDay(0f,startDate,DEFAULT_SECOND_HALF_START,endDate,endTime);
					next = countTime(next);
					days+= next.getDays();
				} else if( DEFAULT_SECOND_HALF_START.compareTo(startTime)==0 && DEFAULT_FIRST_HALF_END.compareTo(endTime)==0 ) {
					//中午休息開始，中午休息結束
					days+= subtractTime(workDay.getStartTime(), workDay.getEndTime());
				} else if( DEFAULT_SECOND_HALF_END.compareTo(startTime)>=0 && DEFAULT_SECOND_HALF_END.compareTo(endTime)>=0 ) {
					//下午開始，下午結束
					days+= subtractTime(startTime, endTime);
				}
			}
			workDay.setDays(days);
		}
		return workDay;
	}
	
	private static final DateFormat HHMISS = new SimpleDateFormat("HHmmss");
	private static final BigDecimal DIVISOR = new BigDecimal(8 * 60 * 60 * 1000);//一天算8小時
	private static final Integer SCALE = 2;//取到小數點後二位
	private static final Integer ROUNTING_MODE = BigDecimal.ROUND_HALF_UP;//四捨五入
	/** 日期相減取差異天數 */
	private Float subtractTime(String startTime, String endTime) {
		Float result = new Float(0.00);
		if( "".equals(Common.get(startTime).trim()) ) return result;
		if( "".equals(Common.get(endTime).trim()) ) return result;
		try {
			Date start = HHMISS.parse(startTime);
			Date end = HHMISS.parse(endTime);
			long diff = end.getTime() - start.getTime();
			result = (new BigDecimal(diff).divide(DIVISOR, SCALE, ROUNTING_MODE)).floatValue();
		}catch(Exception e){
			e.printStackTrace();
		}
		return result;
	}

	private static final DateFormat YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final NumberFormat yyyMMdd = new DecimalFormat("0000000");

	/**
	 * 加日期
	 * @param dateStr
	 * @param num
	 * @return
	 */
	private String dateAdd(String dateStr, Integer num) {
		try {
			Calendar cal = Calendar.getInstance();
			cal.setTime(YYYYMMDD.parse(String.valueOf(Integer.parseInt(dateStr) + 19110000)));
			cal.add(Calendar.DATE, num);
			dateStr = yyyMMdd.format(Integer.parseInt(YYYYMMDD.format(cal.getTime()))-19110000);
		}catch(Exception e){
			e.printStackTrace();
		}
		return dateStr;
	}

	private Cedb1010Dao cedb1010Dao;
	private Cedb1000Dao cedb1000Dao;
	private CsmdWorkDayDao csmdWorkDayDao;
	private FlowLogDao flowLogDao;

	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public CsmdWorkDayDao getCsmdWorkDayDao() {return csmdWorkDayDao;}
	public void setCsmdWorkDayDao(CsmdWorkDayDao dao) {this.csmdWorkDayDao = dao;}
	public FlowLogDao getFlowLogDao() {return flowLogDao;}
	public void setFlowLogDao(FlowLogDao dao) {this.flowLogDao = dao;}
	
	protected class WorkDay {
		private Float days;
		private String startDate;
		private String startTime;
		private String endDate;
		private String endTime;

		public WorkDay(Float days, String startDate, String startTime,
				String endDate, String endTime) {
			this.days = days;
			this.startDate = startDate;
			this.startTime = startTime;
			this.endDate = endDate;
			this.endTime = endTime;
		}

		public Float getDays() {return days;}
		public void setDays(Float f) {this.days = f;}
		public String getStartDate() {return Common.get(startDate);}
		public void setStartDate(String s) {this.startDate = Common.set(s);}
		public String getStartTime() {return Common.get(startTime);}
		public void setStartTime(String s) {this.startTime = Common.set(s);}
		public String getEndDate() {return Common.get(endDate);}
		public void setEndDate(String s) {this.endDate = Common.set(s);}
		public String getEndTime() {return Common.get(endTime);}
		public void setEndTime(String s) {this.endTime = Common.set(s);}

		@Override
		public String toString() {
			StringBuilder builder = new StringBuilder();
			builder.append("WorkDay [days=");
			builder.append(days);
			builder.append(", startDate=");
			builder.append(startDate);
			builder.append(", startTime=");
			builder.append(startTime);
			builder.append(", endDate=");
			builder.append(endDate);
			builder.append(", endTime=");
			builder.append(endTime);
			builder.append("]");
			return builder.toString();
		}
	}

}