package com.kangdainfo.tcfi.service.impl;

import java.util.List;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.tcfi.service.Pre3005Service;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.ReceiptNoSetup;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.CmpyStatusDao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiptNoSetupDAO;

public class Pre3005ServiceImpl implements Pre3005Service {
	
	Cedb1001Dao cedb1001Dao;
	Cedb1000Dao cedb1000Dao;
	Eedb1000Dao eedb1000DaoEicm;
	CmpyStatusDao cmpyStatusDao;
	PrefixReceiptNoDAO prefixReceiptNoDAO;
	ReceiptNoSetupDAO receiptNoSetupDAO;
	Cedb1022Dao cedb1022Dao;
	
	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	public Eedb1000Dao getEedb1000DaoEicm() {return eedb1000DaoEicm;}
	public void setEedb1000DaoEicm(Eedb1000Dao dao) {this.eedb1000DaoEicm = dao;}
	public CmpyStatusDao getCmpyStatusDao() {return cmpyStatusDao;}
	public void setCmpyStatusDao(CmpyStatusDao dao) {this.cmpyStatusDao = dao;}
	public PrefixReceiptNoDAO getPrefixReceiptNoDAO() {return prefixReceiptNoDAO;}
	public void setPrefixReceiptNoDAO(PrefixReceiptNoDAO dao) {this.prefixReceiptNoDAO = dao;}
	public ReceiptNoSetupDAO getReceiptNoSetupDAO() {return receiptNoSetupDAO;}
	public void setReceiptNoSetupDAO(ReceiptNoSetupDAO receiptNoSetupDAO) {this.receiptNoSetupDAO = receiptNoSetupDAO;}
	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}
	
	public Eedb1000 getEedb1000ByTelixNo(String telixNo) {
		if("".equals(Common.get(telixNo))) return null;
		return eedb1000DaoEicm.findByTelixNo(telixNo);
	}

	/*	Pre3005Service 檢還、撤件、撤回退費
	 *  1. 將cedb1010這個表格內同預查編號的紀錄巡過一輪, 看要加入的status是否已在表格中，
	 *     如果沒有就insert新的紀錄進去；
	 *     如果有就更新依prefixNo + status所找到的那筆紀錄
	 *     並且更新cedb1000內該筆預查編號的紀錄
	 *  2. 回寫cedb1000
	 *  3. 如果這個案件的電子流水號是osc開頭, 則會在外面做介接, 此時eedbFalg 為 false
	 *     eedbFalg 為 true 則回寫eedb1300
	 *  4. 依預查編號清除CmpyStatus資料表當中屬於該預查編號的紀錄
	 * */
	public int doSaveFor0102( Cedb1000 cedb1000,Eedb1000 eedb1000, String status, boolean eedbFlag ) throws Exception {

		cedb1001Dao.updateWhenPre3005(cedb1000.getPrefixNo());
		//寫入案件歷程
		ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000.getPrefixNo(), cedb1000.getUpdateIdNo(), status);
		cedb1000Dao.setWhenPre3005(cedb1000) ;
		if ( eedbFlag )
			eedb1000DaoEicm.update(eedb1000);
		
		cmpyStatusDao.deleteWhenPre3005(cedb1000.getPrefixNo());
		
	    return 0 ;
	}
	
	public int doSaveFor03( Cedb1000 cedb1000,Eedb1000 eedb1000, PrefixReceiptNo prefixReceiptNo, ReceiptNoSetup receiptNoSetup, String status, boolean eedbFlag ) throws Exception {

		cedb1001Dao.updateWhenPre3005(cedb1000.getPrefixNo());
		//寫入案件歷程
		ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000.getPrefixNo(), cedb1000.getUpdateIdNo(), status);
		cedb1000Dao.setWhenPre3005(cedb1000) ;
		if (prefixReceiptNo != null) 
			prefixReceiptNoDAO.updateByPrefixNo(prefixReceiptNo);
		
		receiptNoSetupDAO.update(receiptNoSetup);
		if ( eedbFlag )
			eedb1000DaoEicm.update(eedb1000);
		
		cmpyStatusDao.deleteWhenPre3005(cedb1000.getPrefixNo());
		
	    return 0 ;
	}
	
	public int doSaveFor04( Cedb1000 cedb1000, PrefixReceiptNo prefixReceiptNo, ReceiptNoSetup receiptNoSetup, String status) throws Exception {

		cedb1001Dao.updateWhenPre3005(cedb1000.getPrefixNo());
		//寫入案件歷程
		ServiceGetter.getInstance().getCaseFlowService().addCaseFlow(cedb1000.getPrefixNo(), cedb1000.getUpdateIdNo(), status);
		if (prefixReceiptNo != null) {
			prefixReceiptNoDAO.updateByPrefixNo(prefixReceiptNo);
		}

		receiptNoSetupDAO.update(receiptNoSetup);
		cedb1000Dao.setWhenPre3005(cedb1000) ;
		
		cmpyStatusDao.deleteWhenPre3005(cedb1000.getPrefixNo());
		
	    return 0 ;
	}
	
	public PrefixReceiptNo selectPrefixReceiptNoByPrefixNo(String prefixNo) {
		return prefixReceiptNoDAO.selectByPrefixNo(prefixNo);
	}
	public Cedb1022 selectCedb1022ByPrefixNo(String prefixNo) {
		return cedb1022Dao.findByPrefixNo(prefixNo);
	}
	public List<PrefixReceiptNo> selectPrefixReceiptNoByReturnDate(String returnDateStart, String returnDateEnd) {
		return prefixReceiptNoDAO.selectByReturnDatePDF(returnDateStart, returnDateEnd);
	}
}