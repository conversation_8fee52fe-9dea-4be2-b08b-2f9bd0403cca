package com.kangdainfo.util.pdf;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public abstract class CommonPdfUtils {

	public static java.io.File setAutoShowPrintUI(java.io.File src) {
		java.io.File tempFile = null;
		java.io.OutputStream os = null;
		com.lowagie.text.Document document = null;
		com.lowagie.text.pdf.PdfWriter pdfWriter = null;
		com.lowagie.text.pdf.PdfReader pdfReader = null;
		com.lowagie.text.pdf.PdfImportedPage page = null;
		com.lowagie.text.pdf.PdfContentByte cb = null;
		try {
			// 建立暫存檔
			tempFile = java.io.File.createTempFile("maniPdf", ".pdf");
			// 輸出
			os = new java.io.FileOutputStream(tempFile);
			// 輸入
			pdfReader = new com.lowagie.text.pdf.PdfReader(src.getAbsolutePath());
			int pageNum = pdfReader.getNumberOfPages();
			document = new com.lowagie.text.Document();
			pdfWriter = com.lowagie.text.pdf.PdfWriter.getInstance(document, os);
			// 設定PDF版本(1.7)
			pdfWriter.setPdfVersion(com.lowagie.text.pdf.PdfWriter.PDF_VERSION_1_7);
			// 壓縮
			pdfWriter.setCompressionLevel(com.lowagie.text.pdf.PdfStream.BEST_COMPRESSION);

			StringBuffer script = new StringBuffer();
			script.append("var pp=this.getPrintParams();");
			script.append("pp.interactive = pp.constants.interactionLevel.full;");
			script.append("pp.bitmapDPI = 150;");
			script.append("pp.gradientDPI = 75;");
			script.append("pp.pageHandling = pp.constants.handling.shrink;");
			// script.append("pp.downloadFarEastFonts = false;");
			// script.append("pp.fontPolicy = pp.constants.fontPolicy.pageRange;");
			script.append("this.print(pp);");
			//script.append("this.closeDoc(true);");

			com.lowagie.text.pdf.PdfAction action = com.lowagie.text.pdf.PdfAction.javaScript(script.toString(), pdfWriter);
			//com.lowagie.text.pdf.PdfAction action = new com.lowagie.text.pdf.PdfAction(com.lowagie.text.pdf.PdfAction.PRINTDIALOG);
			pdfWriter.setOpenAction(action);
			//pdfWriter.setPageAction(com.lowagie.text.pdf.PdfWriter.PAGE_OPEN, action);
			document.open();
			cb = pdfWriter.getDirectContent();
			for (int i = 1; i <= pageNum; i++) {
				document.newPage();
				page = pdfWriter.getImportedPage(pdfReader, i);
				cb.addTemplate(page, 0, 0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != document && document.isOpen())
				document.close();
			if (null != pdfWriter)
				pdfWriter.close();
			if (null != pdfReader)
				pdfReader.close();
			com.kangdainfo.util.io.CommonIOUtils.closeQuietly(os);
		}
		return tempFile;
	}

	public static java.io.File setAutoPrint(java.io.File src) {
		java.io.File tempFile = null;
		java.io.OutputStream os = null;
		com.lowagie.text.Document document = null;
		com.lowagie.text.pdf.PdfWriter pdfWriter = null;
		com.lowagie.text.pdf.PdfReader pdfReader = null;
		com.lowagie.text.pdf.PdfImportedPage page = null;
		com.lowagie.text.pdf.PdfContentByte cb = null;
		try {
			// 建立暫存檔
			tempFile = java.io.File.createTempFile("maniPdf", ".pdf");
			// 輸出
			os = new java.io.FileOutputStream(tempFile);
			// 輸入
			pdfReader = new com.lowagie.text.pdf.PdfReader(
					src.getAbsolutePath());
			int pageNum = pdfReader.getNumberOfPages();
			document = new com.lowagie.text.Document();
			pdfWriter = com.lowagie.text.pdf.PdfWriter
					.getInstance(document, os);
			// 設定PDF版本(1.7)
			pdfWriter
					.setPdfVersion(com.lowagie.text.pdf.PdfWriter.PDF_VERSION_1_7);
			// 壓縮
			pdfWriter
					.setCompressionLevel(com.lowagie.text.pdf.PdfStream.BEST_COMPRESSION);

			StringBuffer script = new StringBuffer();
			script.append("var pp=this.getPrintParams();");
			script.append("pp.interactive = pp.constants.interactionLevel.silent;");
			script.append("pp.bitmapDPI = 150;");
			script.append("pp.gradientDPI = 75;");
			script.append("pp.pageHandling = pp.constants.handling.shrink;");
			// script.append("pp.downloadFarEastFonts = false;");
			// script.append("pp.fontPolicy = pp.constants.fontPolicy.pageRange;");
			script.append("this.print(pp);");
			script.append("this.closeDoc(true);");

			com.lowagie.text.pdf.PdfAction action = com.lowagie.text.pdf.PdfAction
					.javaScript(script.toString(), pdfWriter);
			pdfWriter.setPageAction(com.lowagie.text.pdf.PdfWriter.PAGE_OPEN,
					action);
			document.open();
			cb = pdfWriter.getDirectContent();
			for (int i = 1; i <= pageNum; i++) {
				document.newPage();
				page = pdfWriter.getImportedPage(pdfReader, i);
				cb.addTemplate(page, 0, 0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != document && document.isOpen())
				document.close();
			if (null != pdfWriter)
				pdfWriter.close();
			if (null != pdfReader)
				pdfReader.close();
			com.kangdainfo.util.io.CommonIOUtils.closeQuietly(os);
		}
		return tempFile;
	}

	/**
	 * 合併 PDF檔
	 * 
	 * @param pdfFiles
	 * @param 奇數頁時是否需要加上空白頁
	 * @return
	 */
	public static java.io.File concatPdfFiles(
			java.util.List<java.io.File> pdfFiles, boolean shouldAddBlank) {

		java.io.File tempFile = null;
		java.io.OutputStream os = null;
		com.lowagie.text.Document document = null;
		com.lowagie.text.pdf.PdfWriter pdfWriter = null;
		com.lowagie.text.pdf.PdfReader pdfReader = null;
		com.lowagie.text.pdf.PdfImportedPage page = null;
		com.lowagie.text.pdf.PdfContentByte cb = null;
		try {
			// 建立暫存檔
			tempFile = java.io.File.createTempFile("JoinPdf", ".pdf");
			// 輸出
			os = new java.io.FileOutputStream(tempFile);
			// 輸入
			java.util.List<com.lowagie.text.pdf.PdfReader> readers = new java.util.ArrayList<com.lowagie.text.pdf.PdfReader>();
			for (java.io.File pdfFile : pdfFiles) {
				if (null != pdfFile) {
					pdfReader = new com.lowagie.text.pdf.PdfReader(
							pdfFile.getAbsolutePath());
					readers.add(pdfReader);
				}
			}
			// Create a writer for the outputstream
			document = new com.lowagie.text.Document();
			pdfWriter = com.lowagie.text.pdf.PdfWriter
					.getInstance(document, os);
			document.open();
			cb = pdfWriter.getDirectContent(); // Holds the PDF
			// data
			int pageOfCurrentReaderPDF = 0;
			// Loop through the PDF files and add to the output.
			for (com.lowagie.text.pdf.PdfReader reader : readers) {
				// Create a new page in the target for each source page.
				while (pageOfCurrentReaderPDF < reader.getNumberOfPages()) {

					document.newPage();
					pageOfCurrentReaderPDF++;
					page = pdfWriter.getImportedPage(reader,
							pageOfCurrentReaderPDF);
					cb.addTemplate(page, 0, 0);

					// add blank page when total page number is odd.
					if (shouldAddBlank
						&& (pageOfCurrentReaderPDF == reader.getNumberOfPages() 
						&& reader.getNumberOfPages() % 2 != 0)) {
							document.newPage();
							//Use this method to make sure a page is added, even if it's empty.
							//invoking newPage() after a blank page will add a newPage. setPageEmpty(true) won't have any effect.
							pdfWriter.setPageEmpty(false);
					}
				}
				pageOfCurrentReaderPDF = 0;
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != document && document.isOpen())
				document.close();
			if (null != pdfWriter)
				pdfWriter.close();
			if (null != pdfReader)
				pdfReader.close();
			com.kangdainfo.util.io.CommonIOUtils.closeQuietly(os);
			// if(null!=tempFile) tempFile.deleteOnExit();
		}

		return tempFile;
	}

	public static void main(String[] args) {
		
		List<File> pdfFiles = new ArrayList<File>();
		pdfFiles.add(new File("D:/apache-tomcat-6.0.39/temp/5566.pdf"));
		pdfFiles.add(new File("D:/apache-tomcat-6.0.39/temp/5566.pdf"));

		concatPdfFiles(pdfFiles, true);
	}

	/** 分割PDF檔 */
	public static void split(java.io.File file) {
		java.io.File tempFile = null;
		java.io.OutputStream os = null;
		com.lowagie.text.Document document = null;
		com.lowagie.text.pdf.PdfWriter pdfWriter = null;
		com.lowagie.text.pdf.PdfReader pdfReader = null;
		com.lowagie.text.pdf.PdfCopy pdfCopy = null;
		com.lowagie.text.pdf.PdfImportedPage page = null;
		try {
			// read
			pdfReader = new com.lowagie.text.pdf.PdfReader(
					file.getAbsolutePath());
			int n = pdfReader.getNumberOfPages();
			int i = 0;
			while (i < n) {
				// 建立暫存檔
				tempFile = java.io.File.createTempFile("Split_" + i + "_",
						".pdf");
				//
				document = new com.lowagie.text.Document(
						pdfReader.getPageSizeWithRotation(1));
				// copy
				pdfCopy = new com.lowagie.text.pdf.PdfCopy(document,
						new java.io.FileOutputStream(tempFile));
				document.open();
				page = pdfCopy.getImportedPage(pdfReader, ++i);
				pdfCopy.addPage(page);
				document.close();
				pdfCopy.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (document.isOpen())
				document.close();
			if (null != pdfWriter)
				pdfWriter.close();
			if (null != pdfReader)
				pdfReader.close();
			com.kangdainfo.util.io.CommonIOUtils.closeQuietly(os);
			if (null != tempFile)
				tempFile.deleteOnExit();
		}
	}

	/** 旋轉PDF檔 */
	public static java.io.File rotate(java.io.File file, int degree) {
		java.io.File tempFile = null;
		java.io.OutputStream os = null;
		com.lowagie.text.pdf.PdfWriter pdfWriter = null;
		com.lowagie.text.pdf.PdfReader pdfReader = null;
		com.lowagie.text.pdf.PdfDictionary pageDict = null;
		com.lowagie.text.pdf.PdfStamper stamper = null;
		try {
			// 建立暫存檔
			tempFile = java.io.File.createTempFile("Rotate", ".pdf");
			pdfReader = new com.lowagie.text.pdf.PdfReader(
					file.getAbsolutePath());
			int n = pdfReader.getNumberOfPages();
			int rot;
			for (int i = 1; i <= n; i++) {
				rot = pdfReader.getPageRotation(i);
				pageDict = pdfReader.getPageN(i);
				pageDict.put(com.lowagie.text.pdf.PdfName.ROTATE,
						new com.lowagie.text.pdf.PdfNumber(rot + degree));
			}
			stamper = new com.lowagie.text.pdf.PdfStamper(pdfReader,
					new java.io.FileOutputStream(tempFile));
			stamper.close();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != pdfWriter)
				pdfWriter.close();
			if (null != pdfReader)
				pdfReader.close();
			com.kangdainfo.util.io.CommonIOUtils.closeQuietly(os);
		}
		return tempFile;
	}

}