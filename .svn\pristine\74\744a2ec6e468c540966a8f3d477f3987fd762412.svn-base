<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE8012">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE8012" />
</jsp:include>
<%
System.out.println("state:"+obj.getState());
if ("queryAll".equals(obj.getState())) {
   obj.setQueryAllFlag("true") ;
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE8012)obj.queryOne();	
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_seqNo(obj.getSeqNo());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}

if ( "true".equals(obj.getQueryAllFlag()) ) {
	  objList = (java.util.ArrayList) obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;
insertDefault = new Array();

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){

	}else if(form1.state.value=="insert"||
			form1.state.value=="insertError"||
			form1.state.value=="update"||
			form1.state.value=="updateError"){
	   alertStr += checkEmpty(form1.addSeqNo, "禁用名稱序號");	
	   alertStr += checkEmpty(form1.banWord, "禁用名稱");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	
	return true;	
}

function queryOne(c1,c2,c3,c4,c5,c6,c7,c8){
	$('#seqNo').val(c1);
	$('#banWord').val(c2);
	$('#letterDesc').val(c3);
	$('#modIdNo').val(c4);
	$('#modDate').val(c5);
	$('#unit').val(c6);
	$('#letterDate').val(c7);
	$('#letterNo').val(c8);
	setAllReadonly();
	btnFollowPK();
	btnIQUD();
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
		    case "insert":
		    	$('#modIdNo').val("<%=obj.getLoginUserName()%>");
		    	$('#modDate').val("<%=Datetime.getYYYMMDD()%>");
		    	break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->
<div id="queryContainer" style="width:500px;height:120px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">不宜(不得)使用之文字</td>
		<td class="queryTDInput">
			<input type="hidden" id="q_seqNo" name="q_seqNo" />
			<input class="field_Q" type="text" id="q_banWord" name="q_banWord" size="20" maxlength="20" value="<%=obj.getQ_banWord()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">說明</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" id="q_letterDesc" name="q_letterDesc" size="20" maxlength="20" value="<%=obj.getQ_letterDesc()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">相關函號</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" id="q_letterNo" name="q_letterNo" size="20" maxlength="20" value="<%=obj.getQ_letterNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">異動人員</td>
		<td class="queryTDInput">
			<select class="field_Q" type="text" id="q_modifier" name="q_modifier" >
				<option value="">請選擇</option>
				<c:import url="../ajax/cedbc000Options.jsp" />
			</select>
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">異動日期</td>
		<td class="queryTDInput">
			<%=View.getPopCalendar("field_Q","q_modDate",obj.getQ_modDate()) %>
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">			
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE8012'/>
</c:import>
<br/>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
	<input type="hidden" id="seqNo" name="seqNo" value="<%=obj.getSeqNo()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto;">
	<table class="table_form" style="width:100%;height:auto;">
	<tr>
		<td class="td_form">不宜(不得)使用之文字</td>
		<td class="td_form_white" colspan="3">
			<input class="field" type="text" id="banWord" name="banWord" size="105" maxlength="26" value="<%=obj.getBanWord()%>" />
		</td>
	</tr>
	<tr>
		<td class="td_form">說明</td>
		<td class="td_form_white" colspan="3">
			<textarea class="field" id="letterDesc" name="letterDesc" cols="105" rows="3" ><%=obj.getLetterDesc()%></textarea>
		</td>
	</tr>
	<tr>
		<td class="td_form">相關函號</td>
		<td class="td_form_white" colspan="3">
			單位:<input class="field" type="text" id="unit" name="unit" size="20" maxlength="26" value="<%=obj.getUnit()%>" />
			日期:<%=View.getPopCalendar("field","letterDate",obj.getLetterDate()) %>
			<input class="field" type="text" id="letterNo" name="letterNo" size="30" maxlength="26" value="<%=obj.getLetterNo()%>" />號函
		</td>
	</tr>
	<tr>
		<td class="td_form">異動人員</td>
		<td class="td_form_white">
			<input class="field_RO" type="text" id="modIdNo" name="modIdNo" size="20" value="<%=obj.getModIdNo()%>" readonly="true" />
		</td>
		<td class="td_form">異動日期</td>
		<td class="td_form_white">
			<input class="field_RO" type="text" id="modDate" name="modDate" size="20" value="<%=obj.getModDate()%>" readonly="true" />
		</td>
	</tr>
	</table>
	</div>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
</td></tr>


<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer" style="height: 240px;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>		
		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',0,false);" href="#">不宜(不得)使用之文字</a></th>
		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">說明</a></th>
		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">相關函號</a></th>
		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">異動人員</a></th>
		<th class="listTH" style="text-align:left;"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">異動日期</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = { true, true, true,false, true, true,true ,true ,true };
	boolean displayArray[] = {false, true, true, true, true, true,false,false,false};
	String[] alignArray = {"","left;width:200px;","left","left;width:200px;","left;width:120px;","left;width:100px;","","",""};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>