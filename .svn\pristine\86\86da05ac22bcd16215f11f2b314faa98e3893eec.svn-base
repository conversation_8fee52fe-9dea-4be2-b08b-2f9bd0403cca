package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 由預查編號或統一編號匯入營業項目
 *
 */
public class PRE3001_02 extends SuperBean {
	/** 預查編號 */
	private String prefixNo;
	/** 統一編號 */
	private String banNo;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		if( !"".equals(getPrefixNo()) ) {
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" SEQ_NO");
			sqljob.appendSQL(",BUSI_ITEM_NO");
			sqljob.appendSQL(",BUSI_ITEM");
			sqljob.appendSQL("FROM CEDB1002");
			sqljob.appendSQL("WHERE PREFIX_NO = ?");
			sqljob.addParameter(getPrefixNo());
		} else if ( !"".equals(getBanNo()) ) {
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" SEQ_NO");
			sqljob.appendSQL(",BUSI_ITEM_NO");
			sqljob.appendSQL(",BUSI_ITEM");
			sqljob.appendSQL("FROM Cedb2002");
			sqljob.appendSQL("WHERE BAN_NO = ?");
			sqljob.addParameter(getBanNo());
		}
		sqljob.appendSQL("ORDER BY 1");
		java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<Map<String,Object>> it = objList.iterator();
			Map<String,Object> o;
			String[] rowArray = new String[4];
			while (it.hasNext()) {
				o = it.next();
				rowArray = new String[4];
				rowArray[0] = Common.get(o.get("BUSI_ITEM_NO"))+"@"+Common.get(o.get("BUSI_ITEM"));
				rowArray[1] = Common.get(o.get("SEQ_NO"));
				rowArray[2] = Common.get(o.get("BUSI_ITEM_NO"));
				rowArray[3] = Common.get(o.get("BUSI_ITEM"));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getPrefixNo() {return checkGet(prefixNo);}
	public void setPrefixNo(String s) {this.prefixNo = checkSet(s);}

	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}

}