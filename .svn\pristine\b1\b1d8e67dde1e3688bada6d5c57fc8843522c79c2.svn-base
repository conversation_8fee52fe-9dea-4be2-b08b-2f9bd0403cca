package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;

public class PRE9003 extends SuperBean {
	
	private static final String ROOT_MENU_TITLE = "功能選單";

	protected String sysid;
	protected String id;
	protected String pid;
	protected String name;
	protected String target;
	protected String title;
	protected String url;
	protected String sorted;
	protected String pname;
	protected String code;
	
	public String getSysid() { return checkGet(sysid); }
	public void setSysid(String s) { sysid = checkSet(s); }
	public String getPname() { return checkGet(pname); }
	public void setPname(String s) { pname = checkSet(s); }
	public String getId() {return checkGet(id);}
	public void setId(String s) { id = checkSet(s);}
	public String getName() { return checkGet(name); }
	public void setName(String s) {name = checkSet(s);}
	public String getPid() {return checkGet(pid);}
	public void setPid(String s) {pid = checkSet(s);}
	public String getTarget() {return checkGet(target);}
	public void setTarget(String s) {target = checkSet(s);}
	public String getTitle() {return checkGet(title);}
	public void setTitle(String s) {title = checkSet(s);}
	public String getUrl() {return Common.get(url);}//URL含特殊字元,改用Common.get
	public void setUrl(String s) {url = Common.set(s);}//URL含特殊字元,改用Common.set
	public String getSorted() {return checkGet(sorted);}
	public void setSorted(String s) {sorted = checkSet(s);}
	public String getCode() {return checkGet(code);}
	public void setCode(String code) {this.code = checkSet(code);}
	
	@Override
	public Object doQueryOne() throws Exception {
		PRE9003 obj = this;
		FunctionMenu c = ServiceGetter.getInstance().getPrefixService().getFunctionMenuById(Common.getInt(getId()));
		if(c != null){
			obj.setId(Common.get(c.getId()));
			obj.setPid(Common.get(c.getPid()==0?obj.getSysid():c.getPid()+""));
			obj.setCode(Common.get(c.getCode()));
			obj.setName(Common.get(c.getTitle()));
			obj.setUrl(Common.get(c.getUrl()));
			obj.setTarget(Common.get(c.getTarget()));
			obj.setSorted(Common.get(c.getSorted()));
				
			/**
			 * 取得父節點名稱
			 */
			FunctionMenu parent = ServiceGetter.getInstance().getPrefixService().getFunctionMenuById(Common.getInt(c.getPid()));
			obj.setPname(Common.get(parent!=null?parent.getTitle():ROOT_MENU_TITLE));
		}else{
			this.setErrorMsg("查無該筆資料！");
		}
		return obj;
	}

	@Override
	public void doCreate() throws Exception {
		if(getUpdateCheck()){
			FunctionMenu c = new FunctionMenu();
			c.setCode(getCode());
			c.setTitle(getName());
			c.setPid(Common.getInteger(getPid()) == -11?null:Common.getInteger(getPid()));
			c.setTarget(getTarget());
			c.setUrl(getUrl());
			c.setSorted(Common.getInteger(getSorted()));
			c.setEnable("Y");
			c = ServiceGetter.getInstance().getPrefixService().insertFunctionMenu(c);
			reloadFunctionMenu();
			//reload
			if(c != null){
				this.setId(Common.get(c.getId()));
				this.setPid(c.getPid()==0?this.getSysid():c.getPid()+"");
				this.setCode(c.getCode());
				this.setName(c.getTitle());
				this.setUrl(c.getUrl());
				this.setTarget(c.getTarget());
				this.setSorted(Common.get(c.getSorted()));
				
				/**
				 * 取得父節點名稱
				 */
				FunctionMenu parent = ServiceGetter.getInstance().getPrefixService().getFunctionMenuById(Common.getInt(c.getPid()));
				this.setPname(parent!=null?parent.getTitle():ROOT_MENU_TITLE);
			}
		}else{
			throw new MoeaException("已存在相同功能代號的資料，請重新輸入！");
		}
		
	}

	@Override
	public void doUpdate() throws Exception {
		FunctionMenu c = ServiceGetter.getInstance().getPrefixService().getFunctionMenuById(Common.getInt(getId()));
		if(c == null)	throw new Exception("資料不存在!");
		if(getUpdateCheck()){
			c.setCode(getCode());
			c.setTitle(getName());
			c.setPid(Common.getInteger(getPid()) == -11?null:Common.getInteger(getPid()));
			c.setTarget(getTarget());
			c.setUrl(getUrl());
			c.setSorted(Common.getInteger(getSorted()));
			ServiceGetter.getInstance().getPrefixService().updateFunctionMenu(c);
			reloadFunctionMenu();
		}else{
			throw new MoeaException("已存在相同功能代號的資料，請重新輸入！");
		}
	}

	@Override
	public void doDelete() throws Exception {
		FunctionMenu c = ServiceGetter.getInstance().getPrefixService().getFunctionMenuById(Common.getInt(getId()));
		if(c == null)	throw new MoeaException("資料不存在!");
		
		ServiceGetter.getInstance().getPrefixService().deleteFunctionMenu(c);
		reloadFunctionMenu();
	}

	/** 組合功能樹 */
	public String buildManageTree() throws Exception {
		java.util.List<FunctionMenu> fMenu = ServiceGetter.getInstance().getPrefixService().getFunctionMenuAll();
		if(fMenu != null && fMenu.size() > 0){
			StringBuilder sb = new StringBuilder();
			for(FunctionMenu dtl: fMenu){
				sb.append("d.add(");

				//id
				sb.append(dtl.getId()).append(",");
				//pid
				if (dtl.getPid()!=0) sb.append(dtl.getPid()).append(",");
				else sb.append(getSysid()).append(",");
				//name
				sb.append("'").append(dtl.getTitle()).append("',");
				//url
				sb.append("'").append("pre9003_dTreeForm.jsp?sid=").append(dtl.getId()).append("&fid=").append(dtl.getPid()==null?getSysid():dtl.getPid()).append("',");
				//title
				sb.append("''").append(",");
				//target
				//sb.append(Common.sqlChar(dtl.getTarget()));//作業功能維護 不使用 target
				sb.append("''");

				sb.append(");\n");
			}
			return sb.toString();
		}
		return "";
	}
	
	/** 組合POP功能樹 */
	public String buildCheckBoxTree(String treeName, String jsFunctionName, boolean bUrl, boolean bRootCheckBox) throws Exception {	
		StringBuffer sb = new StringBuffer(1024).append("");
		if (Common.get(treeName).equals("")) treeName = ROOT_MENU_TITLE;
		//建立根節點
		sb.append("d.add(");					
		sb.append(getSysid()).append(",-1,'");
		if (bRootCheckBox) {
			sb.append("<input type=checkbox id=").append(getSysid()).append(" name=auth class=checkbox onclick=");
			sb.append(jsFunctionName).append("(this,\"").append(treeName).append("\") value=").append(getSysid()).append(">");			
		}
		sb.append(treeName).append("'");
		if (bUrl) sb.append(",'").append("pre9003_dTreeForm.jsp?sid=").append(getSysid()).append("&fid=-1'");
		sb.append(");\n");
		
		java.util.List<FunctionMenu> fMenu = ServiceGetter.getInstance().getPrefixService().getFunctionMenuAll();
		if(fMenu != null && fMenu.size() > 0){
			for(FunctionMenu dtl: fMenu){
				sb.append("d.add(").append(dtl.getId()).append(",");
				
				if (dtl.getPid()!=0) sb.append(dtl.getPid()).append(",");
				else sb.append(getSysid()).append(",");
				
				sb.append("'<input type=checkbox id=").append(dtl.getId()).append(" name=auth class=checkbox onclick=").append(jsFunctionName).append("(this,\"").append(dtl.getTitle()).append("\") value=").append(dtl.getId()).append(">").append(dtl.getTitle()).append("',");
				if (bUrl) sb.append("'").append("pre9003_dTreeForm.jsp?sid=").append(dtl.getId()).append("&fid=").append(dtl.getPid()).append("',");
				else sb.append("'',");
				
				sb.append("''").append(",");
				sb.append(Common.sqlChar(dtl.getTarget()));
				sb.append(");\n");
			}
		}
		return sb.toString();
	}
	
	/** 檢核  code 是否重複 */
	protected boolean getUpdateCheck(){
		FunctionMenu o = ServiceGetter.getInstance().getPrefixService().getFunctionMenuByCode(this.getCode());
		if(o == null)
			return true;
		else{
			if("update".equals(this.getState()) && o.getId().equals(Common.getInt(this.getId())))
				return true;
		}
		return false;
	}
	
	protected void reloadFunctionMenu(){
		ServiceGetter.getInstance().getFunctionMenuLoader().reload();
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}

}