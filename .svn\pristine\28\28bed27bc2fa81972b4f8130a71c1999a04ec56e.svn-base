<!-- 
程式目的：當月所有承辦人員馬上辦及檢還件數統計表
程式代號：pre2007
程式日期：1030603
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE2007">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>     
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE2007" />
</jsp:include>

<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} // end if
else if ( "update".equals(obj.getState()) ) {
	obj.update() ;
} // end else if
else if ( "print".equals(obj.getState()) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE2007.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function init(){
	if ( form1.state.value == "queryAllSuccess" )
		document.getElementById("listContainer").style.display = '';
	else
	  document.getElementById("listContainer").style.display = 'none';
}

function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_yearMonth,"年月份");
	alertStr += checkYYYMM(form1.q_yearMonth,"");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("print") ;				
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;	
			case "doQueryOne":
				$('#state').val( "queryOne" ) ;
				break ;
			case "doUpdate":
				$('#state').val( "update" ) ;	
				break ;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};

	$table = $("#listContainer table");
	if($table.find("tr").size() > 1) {
		$table.find("tr:last").find("td:first").text("總和");
	}

	$("#doReset").click(function(){
		$("#q_yearMonth").val('');
	});
	
});

function keyDown() {
	if (event.keyCode==13) {
		$("#doQueryAll").click();
	}
}

</script>
</head>
<body topmargin="5" onload="showErrorMsg('<%=obj.getErrorMsg()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE2007'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%">  
		<tr>
			<td class="td_form" width="120px">查詢年月份：</td>
			<td class="td_form_white"> 
				<input class="field_Q" type="text" id="q_yearMonth" name="q_yearMonth" size="10" maxlength="5" value="<%=obj.getQ_yearMonth()%>" onKeyDown="keyDown()">
				<input class="toolbar_default" type="button"  id="doReset" name="doReset" value="重新輸入" onClick="reset()" >
				<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
			</td>
		</tr>			
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>  
</td></tr>

<!-- Query Area  -->
<tr><td class="bg" >
  <div id="listContainer">
	<table class="table_form" width="55%" cellspacing="0" cellpadding="0">
	  <thead id="listTHEAD">
	  <tr>
	    <th class="listTH" width="7%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">NO.</a></th>
	    <th class="listTH" width="10%"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">承辦人</a></th>
	    <th class="listTH" width="25%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">馬上辦案件數</a></th>
	    <th class="listTH" width="20%"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">檢還案件數</a></th>
	  </tr>
	  </thead>
	  <tbody id="listTBODY">
	  <%
	  boolean primaryArray[] = {true,false,false};
	  boolean displayArray[] = {true,true,true};
	  String[] alignArray = {"left", "center","center"};
	  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag()));
	  %>
	  </tbody>
	</table>
</div>
</td></tr>
<!--FormArea  -->
<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
	<tr><td style="text-align:center;">
		<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
		<jsp:include page="../../home/<USER>" >
			<jsp:param name="btnInsert" value="N" />
			<jsp:param name="btnQueryAll" value="N" />
			<jsp:param name="btnUpdate" value="N" />
			<jsp:param name="btnDelete" value="N" />
			<jsp:param name="btnClear" value="N" />
			<jsp:param name="btnConfirm" value="N" />
			<jsp:param name="btnListHidden" value="N" />
			<jsp:param name="btnPreview" value="N" />
			<jsp:param name="btnCancel" value="N" />
			<jsp:param name="btnListPrint" value="N" />
		</jsp:include>
		<!-- 新增按鈕區 -->
	</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>