--EICM4AP.SYSTEM_CODE
ALTER TABLE EICM4AP.SYSTEM_CODE ADD CODE_PARAM3 VARCHAR2(200);
--EICM.CEDB1000
ALTER TABLE EICM.CEDB1000 ADD REFUND_NO NVARCHAR2(20);
comment on column EICM.CEDB1000.REFUND_NO is '退費公文文號';
ALTER TABLE EICM.CEDB1000 ADD EXTEND_REASON NVARCHAR2(10);
comment on column EICM.CEDB1000.EXTEND_REASON is '展期原因';
ALTER TABLE EICM.CEDB1000 ADD EXTEND_OTHER NVARCHAR2(255);
comment on column EICM.CEDB1000.EXTEND_OTHER is '展期其他原因';
ALTER TABLE EICM.CEDB1000 ADD EXTEND_DATE VARCHAR2(20);
comment on column EICM.CEDB1000.EXTEND_DATE is '展期日期';
--EICM.CEDB1006
ALTER TABLE EICM.CEDB1006 ADD REFUND_NO NVARCHAR2(20);
comment on column EICM.CEDB1006.REFUND_NO is '退費公文文號';
ALTER TABLE EICM.CEDB1006 ADD EXTEND_REASON NVARCHAR2(10);
comment on column EICM.CEDB1006.EXTEND_REASON is '展期原因';
ALTER TABLE EICM.CEDB1006 ADD EXTEND_OTHER NVARCHAR2(255);
comment on column EICM.CEDB1006.EXTEND_OTHER is '展期其他原因';
ALTER TABLE EICM.CEDB1006 ADD EXTEND_DATE VARCHAR2(20);
comment on column EICM.CEDB1006.EXTEND_DATE is '展期日期';
--EICM.CEDB1100
ALTER TABLE EICM.CEDB1100 ADD REFUND_NO NVARCHAR2(20);
comment on column EICM.CEDB1100.REFUND_NO is '退費公文文號';
ALTER TABLE EICM.CEDB1100 ADD EXTEND_REASON NVARCHAR2(10);
comment on column EICM.CEDB1100.EXTEND_REASON is '展期原因';
ALTER TABLE EICM.CEDB1100 ADD EXTEND_OTHER NVARCHAR2(255);
comment on column EICM.CEDB1100.EXTEND_OTHER is '展期其他原因';
ALTER TABLE EICM.CEDB1100 ADD EXTEND_DATE VARCHAR2(20);
comment on column EICM.CEDB1100.EXTEND_DATE is '展期日期';
--EICM.CEDB1027
ALTER TABLE EICM.CEDB1027 ADD BACK_DATE NVARCHAR2(14);
comment on column EICM.CEDB1027.BACK_DATE is '退件日期';
ALTER TABLE EICM.CEDB1027 ADD BACK_TIME NVARCHAR2(14);
comment on column EICM.CEDB1027.BACK_TIME is '退件時間';
ALTER TABLE EICM.CEDB1027 ADD BACK_REASON NVARCHAR2(10);
comment on column EICM.CEDB1027.BACK_REASON is '退件原因(01:查無此人,02:遷移不明,03:地址欠詳,04:查無地址,05:拒收,90:其他)';
ALTER TABLE EICM.CEDB1027 ADD OTHER_METHOD NVARCHAR2(20);
comment on column EICM.CEDB1027.OTHER_METHOD is '處理方式(不用寄,原址寄,改址,其他)';
ALTER TABLE EICM.CEDB1027 ADD ATONCE NVARCHAR2(10);
comment on column EICM.CEDB1027.ATONCE is '馬上辦_勾選';
ALTER TABLE EICM.CEDB1027 ADD ATONCE_REMARK NVARCHAR2(10);
comment on column EICM.CEDB1027.ATONCE_REMARK is '馬上辦_備註';
--EICM.CEDBC004
ALTER TABLE EICM.CEDBC004 ADD MOD_ID_NO VARCHAR2(20);
comment on column EICM.CEDBC004.MOD_ID_NO is '異動人員';
ALTER TABLE EICM.CEDBC004 ADD MOD_DATE VARCHAR2(20);
comment on column EICM.CEDBC004.MOD_DATE is '異動日期';
ALTER TABLE EICM.CEDBC004 ADD MOD_TIME VARCHAR2(20);
comment on column EICM.CEDBC004.MOD_TIME is '異動時間';
ALTER TABLE EICM.CEDBC004 ADD UNIT NVARCHAR2(20);
comment on column EICM.CEDBC004.UNIT is '單位';
ALTER TABLE EICM.CEDBC004 ADD LETTER_DATE NVARCHAR2(7);
comment on column EICM.CEDBC004.LETTER_DATE is '函號日期';
ALTER TABLE EICM.CEDBC004 ADD LETTER_NO NVARCHAR2(15);
comment on column EICM.CEDBC004.LETTER_NO is '函號';
ALTER TABLE EICM.CEDBC004 ADD LETTER_DESC NVARCHAR2(60);
comment on column EICM.CEDBC004.LETTER_DESC is '函說明';
--EICM.CEDBC058
ALTER TABLE EICM.CEDBC058 ADD SOURCE VARCHAR2(100);
comment on column EICM.CEDBC058.SOURCE is '字源';


--ECIM.CEDB1004
ALTER TABLE EICM.CEDB1004 ADD REVOKE_APP_DATE NVARCHAR2(20);
comment on column EICM.CEDB1004.REVOKE_APP_DATE is '解/撤/廢日';

--ECIM.CEDB1000
ALTER TABLE EICM.CEDB1000 ADD OTHER_REASON NVARCHAR2(20);
comment on column EICM.CEDB1000.OTHER_REASON is '其他(非屬公司法第18條同名公司之原因)';

--ECIM.CEDB1006
ALTER TABLE EICM.CEDB1006 ADD OTHER_REASON NVARCHAR2(20);
comment on column EICM.CEDB1006.OTHER_REASON is '其他(非屬公司法第18條同名公司之原因)';

--ECIM.CEDB1100
ALTER TABLE EICM.CEDB1100 ADD OTHER_REASON NVARCHAR2(20);
comment on column EICM.CEDB1100.OTHER_REASON is '其他(非屬公司法第18條同名公司之原因)';

--EICM.DECLARATORY_STATUTES
ALTER TABLE EICM.DECLARATORY_STATUTES ADD RCV_TYPE_1 VARCHAR2(60);
ALTER TABLE EICM.DECLARATORY_STATUTES ADD RCV_TYPE_2 VARCHAR2(60);
ALTER TABLE EICM.DECLARATORY_STATUTES MODIFY RCV_TYPE_1 VARCHAR2(1000)
comment on column DECLARATORY_STATUTES.RCV_TYPE_1 is '正本單位';
ALTER TABLE EICM.DECLARATORY_STATUTES MODIFY RCV_TYPE_2 VARCHAR2(1000)
comment on column EICM.DECLARATORY_STATUTES.RCV_TYPE_2 is '副本單位';

--ECIM.CEDB1000 修改REMARK長度 60 -> 80
ALTER TABLE CEDB1000 MODIFY REMARK NVARCHAR2(80);

--ECIM.CEDB1004 add column
ALTER TABLE EICM.CEDB1004 ADD CMPY_STATUS NVARCHAR2(5);
comment ON COLUMN EICM.CEDB1004.CMPY_STATUS is '公司狀態';

ALTER TABLE EICM.CEDB1004 ADD APPLY_NAME NVARCHAR2(130);
comment ON COLUMN EICM.CEDB1004.APPLY_NAME is '申請人';

ALTER TABLE EICM.CEDB1004 ADD RESERVE_DATE NVARCHAR2(7);
comment ON COLUMN EICM.CEDB1004.RESERVE_DATE is '保留日期';

create index EICM.CEDB1100_IDX2 on EICM.CEDB1100(APPLY_ID,APPLY_NAME,ATTOR_NAME);

create index EICM.CEDB1100_IDX3 on EICM.CEDB1100(APPLY_NAME);


ALTER TABLE EICM.CEDB1027 MODIFY OTHER_METHOD NVARCHAR2(400);

ALTER TABLE EICM.CEDB1022 MODIFY APPLY_LAW_NAME NVARCHAR2(260);
ALTER TABLE EICM.CEDB1122 MODIFY APPLY_LAW_NAME NVARCHAR2(260);

ALTER TABLE EICM.CEDB1001 MODIFY REMARK NVARCHAR2(1000);
ALTER TABLE EICM.CEDB1007 MODIFY REMARK NVARCHAR2(1000);
ALTER TABLE EICM.CEDB1101 MODIFY REMARK NVARCHAR2(1000);

create index EICM.FLOW_LOG_IDX1 on EICM.FLOW_LOG(PREFIX_NO);
create index EICM.FLOW_LOG_IDX2 on EICM.FLOW_LOG(PROCESS_STATUS);

---EICM.CEDB1000 修改APPLY_ADDR長度(改為與OSSM_APPL_FLOW.APPLY_ADDR_COMBO等長)---
alter table eicm.cedb1000 modify apply_addr nvarchar2(400);
alter table eicm.cedb1006 modify apply_addr nvarchar2(400);
alter table eicm.cedb1100 modify apply_addr nvarchar2(400);
alter table eicm.cedb1000 modify attor_addr nvarchar2(400);
alter table eicm.cedb1006 modify attor_addr nvarchar2(400);
alter table eicm.cedb1100 modify attor_addr nvarchar2(400);
alter table eicm.cedb1023 modify get_addr nvarchar2(400);
alter table eicm.cedb1123 modify get_addr nvarchar2(400);
alter table eicm.post_record modify apply_addr nvarchar2(400);
alter table eicm.post_record modify get_addr nvarchar2(400);


