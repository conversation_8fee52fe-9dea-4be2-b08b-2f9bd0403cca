package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.EncapsulationVo;

/**
 * 封裝資料
 */
public interface EncapsulateService {

	/**
	 * 封裝預查資料
	 * <pre>
	 * 將預查資料 另存至 封裝資料
	 * CEDB1000 => CEDB1100
	 * CEDB1001 => CEDB1101
	 * CEDB1002 => CEDB1102
	 * CEDB1010 => CEDB1110
	 * </pre>
	 * @param year
	 * @throws Exception
	 */
	public void doEncapsulateByYear(String year, String idNo) throws Exception;

	/**
	 * 封裝預查資料
	 * <pre>
	 * 將預查資料 另存至 封裝資料
	 * CEDB1000 => CEDB1100
	 * CEDB1001 => CEDB1101
	 * CEDB1002 => CEDB1102
	 * CEDB1010 => CEDB1110
	 * </pre>
	 * @param prefixNo
	 * @param idNo
	 */
	public void doEncapsulateByPrefixNo(String prefixNo, String idNo) throws Exception;

	/**
	 * 查詢封裝資料
	 * @param prefixNo
	 * @return
	 */
	public EncapsulationVo findEncapsulation(String prefixNo);
	
}