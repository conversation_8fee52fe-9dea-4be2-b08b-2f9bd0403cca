<!DOCTYPE html>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9005" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE9005)obj.queryOne();
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault = new Array(
	new Array("startDate", getToday()),
	new Array("endDate", "<%=Datetime.getDateAdd("m",1,Datetime.getYYYMMDD())%>"),
	new Array("enable", "Y")
);

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkQuery();
		alertStr += checkDate(form1.q_startDate,"查詢公告開始日期");
		alertStr += checkDate(form1.q_endDate,"查詢公告結束日期");
		alertStr += checkDateSE(form1.q_startDate,form1.q_endDate,"查詢公告日期");
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.subject,"主旨");
		alertStr += checkEmpty(form1.content,"內容");		
		alertStr += checkLen(form1.content,"內容",1000);
		alertStr += checkDate(form1.startDate,"公告開始日期");
		alertStr += checkDate(form1.endDate,"公告結束日期");
		alertStr += checkDateSE(form1.startDate, form1.endDate, "公告日期");		
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}
function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">

<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:400px;height:250px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
		<td class="queryTDLable">主旨：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_subject" size="25" maxlength="50" value="<%=obj.getQ_subject()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">內容：</td>
		<td class="queryTDInput">
			<input type="text" class="field_Q" name="q_content" size="25" value="<%=obj.getQ_content()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">公告日期：</td>
		<td class="queryTDInput">
			<%=View.getPopCalendar("field_Q","q_startDate",obj.getQ_startDate())%>~
			<%=View.getPopCalendar("field_Q","q_endDate",obj.getQ_endDate())%>
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input type="hidden" name="q_id" value="<%=obj.getQ_id()%>" class="field_Q">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9005'/>
</c:import>
<br/>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getId()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="Y" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="Y" />
	<jsp:param name="btnDelete" value="Y" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="Y" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">公告開始日期：</td>
		<td class="td_form_white">
			<%=View.getPopCalendar("field","startDate",obj.getStartDate())%>
		</td>
		<td class="td_form">公告結束日期：</td>
		<td class="td_form_white">
			<%=View.getPopCalendar("field","endDate",obj.getEndDate())%>
		</td>
	</tr>
	<tr>
		<td class="td_form">是否為重要公告</td>
		<td colspan="3" class="td_form_white">
			<input class="field" type="checkbox" name="isImportant" value="Y" <%="Y".equals(obj.getIsImportant())?"checked":""%> />
		</td>
	</tr>
	<tr>
		<td class="td_form"><font color="red">*</font>主旨：</td>
		<td colspan="3" class="td_form_white">
			<input class="field" type="text" name="subject" size="70" maxlength="250" value="<%=obj.getSubject()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form"><font color="red">*</font>內容：</td>
		<td colspan="3" class="td_form_white">
			<textarea class="field" name="content" cols="80" rows="4"><%=obj.getContent()%></textarea>
		</td>
	</tr>
	<tr>
		<td class="td_form">是否啟用</td>
		<td colspan="3" class="td_form_white">
			<input class="field" type="checkbox" name="enable" value="Y" <%="Y".equals(obj.getEnable())?"checked":""%> />
		</td>
	</tr>
	</table>
	</div>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" ><a class="text_link_w" >序號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">主旨</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">開始日期</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">結束日期</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">是否為重要公告</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">是否啟用</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = { true,false,false,false,false,false};
	boolean displayArray[] = {false, true, true, true, true, true};
	String[] alignArray = {"center","left","center","center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>