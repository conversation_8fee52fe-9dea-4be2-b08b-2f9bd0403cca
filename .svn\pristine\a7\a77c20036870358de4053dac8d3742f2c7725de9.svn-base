package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;

public class Cedb2002Dao extends BaseDaoJdbc implements RowMapper<Cedb2002> {

	private static String DEFAULT_ORDER = "ORDER BY BAN_NO, SEQ_NO";
	private static String sql_findByBanNo = "SELECT * FROM Cedb2002 WHERE BAN_NO = ?";
	
	public java.util.List<Cedb2002> findByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNo);
		sqljob.appendSQL(DEFAULT_ORDER);
		sqljob.addParameter(banNo);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}
	
	private static String sql_findByBanNoAndBusiItemNo = "SELECT * FROM CEDB2002 WHERE BAN_NO = ? AND BUSI_ITEM_NO = ?";
	public Cedb2002 findByBanNoAndBusiItemNo(String banNo, String itemNo) {
		if (banNo==null||itemNo==null) return null;
		SQLJob sqljob = new SQLJob(sql_findByBanNoAndBusiItemNo);
		sqljob.addParameter(banNo);
		sqljob.addParameter(itemNo);
		List<Cedb2002> tempList = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return tempList.isEmpty() ? null : tempList.get(0);
	}
	
	private static String sql_findBusiItemNoByBanNo = "SELECT BUSI_ITEM_NO FROM Cedb2002 WHERE BAN_NO = ?";

	public List<String> findBusiItemNoByBanNo(String banNo) {
		if(null==banNo || "".equals(banNo)) return null;
		SQLJob sqljob = new SQLJob(sql_findBusiItemNoByBanNo);
		sqljob.appendSQL(DEFAULT_ORDER);
		sqljob.addParameter(banNo);
		return getJdbcTemplate().queryForList(sqljob.getSQL(), sqljob.getParametersArray(), String.class);
	}

	public Cedb2002 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb2002 obj = null;
		if(null!=rs) {
			obj = new Cedb2002();
			obj.setBanNo(Common.get(rs.getString("BAN_NO")));
			obj.setSeqNo(Common.get(rs.getString("SEQ_NO")));
			obj.setBusiItemNo(Common.get(rs.getString("BUSI_ITEM_NO")));
			obj.setBusiItem(Common.get(rs.getString("BUSI_ITEM")));
			obj.setApproveDate(Common.get(rs.getString("APPROVE_DATE")));
			obj.setUpdateUser(Common.get(rs.getString("UPDATE_USER")));
			obj.setUpdateDate(Common.get(rs.getString("UPDATE_DATE")));
			obj.setUpdateTime(Common.get(rs.getString("UPDATE_TIME")));
			obj.setConfirmCode(Common.get(rs.getString("CONFIRM_CODE")));
		}
		return obj;
	}
}