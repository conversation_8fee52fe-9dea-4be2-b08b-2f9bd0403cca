package com.kangdainfo.tcfi.view.ajax;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
*<br>程式目的：共用代碼-多選
*<br>程式代號：PopSystemCodeMultiSelect
*<br>程式日期：1000805
*<br>程式作者：Jack Du
*<br>--------------------------------------------------------
*<br>修改作者　　修改日期　　　修改目的
*<br>--------------------------------------------------------
*/
public class PopSystemCodeMultiSelect extends SuperBean
{
	public java.util.ArrayList<String[]> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		//arrList.add(new String[]{"", com.kangdainfo.tcfi.util.PrefixConstants.SelectALL});
		if (!"".equals(getCodeKind())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND ENABLE='Y' ORDER BY SORTED, CODE");
			sqljob.addParameter(getCodeKind());
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (null!=datas && !datas.isEmpty()) {
				String[] rows;
				for(Map<String,Object> data : datas) {
					rows = new String[2];
					rows[0] = Common.get(data.get("CODE"));
					rows[1] = Common.get(data.get("CODE_NAME"));
					arrList.add(rows);
				}
			}
		}
		return arrList;
	}

	public static String getQuerylist(ArrayList<?> objList, String checkedList) {
		List<String> checkedArray = splitCodes(checkedList);
    	StringBuilder sb = new StringBuilder();
    	int currIdx = 0;
    	if (objList!=null && objList.size()>0) {
			String rowArray[] = new String[2];
			java.util.Iterator<?> it = objList.iterator();
			//int rowStatus = 0;
			while(it.hasNext()) {
				currIdx++;
				String classTR = (currIdx%2==0?"listTREven":"listTROdd");
				String classTD = (currIdx%2==0?"listTDEven":"listTDOdd");

				rowArray = (String[]) it.next();
				String code = Common.escapeReturnChar(rowArray[0]);//代碼
				String codeName = Common.escapeReturnChar(rowArray[1]);//名稱
				//顯示TR
				sb.append("<tr id=\"").append("listContainerRow").append(code).append("\"")
					.append(" class='").append(classTR).append("'")
					.append(" onmouseover=\"this.className='listTRMouseover'\"")
					.append(" onmouseout=\"this.className='").append(classTR).append("'\"")
					.append(" onClick=\"listContainerRowClick('").append(code).append("');\"")
					.append(" >\n");
				//顯示TD
				sb.append("<td class='").append(classTD).append("'>");
				sb.append("<input type=\"checkbox\"")
					.append(" class=\"field\"")
					.append(" name=\"checkedItem\"")
					.append(" value=\"").append(code).append("-").append(codeName).append("\" ");
				//判斷是否要勾選
				if(null != checkedArray && !checkedArray.isEmpty()) {
					for(String checkedItem : checkedArray) {
						if(checkedItem.equalsIgnoreCase(code))
							sb.append(" checked ");
					}
				}
				sb.append("/>");
				sb.append("</td>\n");
				sb.append("<td class='").append(classTD).append("' style=\"text-align:center;\">").append(Common.get(code)).append("</td>\n");
				sb.append("<td class='").append(classTD).append("' style=\"text-align:left;\">").append(Common.get(codeName)).append("</td>\n");
				sb.append("</tr>\n");
				//rowStatus++;
			}
    	} else {
			sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>查無資料，請您重新輸入查詢條件！</td></tr>");
    	}
		return sb.toString();
    }
	
	private static List<String> splitCodes(String codes) {
		if(null!=codes && !"".equals(codes)) {
			List<String> results = new ArrayList<String>();
			String[] array1 = codes.split(",");
			for(String str1 : array1) {
				results.add(str1);
			}
			return results;
		}
		return null;
	}

    /** 代碼類別說明 */
	public String getCodeKindDesc() {
		String result = "";
		if (!"".equals(getCodeKind())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND CODE=?");
			sqljob.addParameter("00");
			sqljob.addParameter(getCodeKind());
			List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (null!=datas && !datas.isEmpty()) {
				Map<String,Object> data = datas.get(0);
				if(null!=data) {
					result = Common.get(data.get("CODE_NAME"));
				}
			}
		}
		return result;
	}

	/** 代碼類別 */
    private String codeKind;
    /** 代碼 */
    private String code;
    /** 代碼名稱 */
    private String codeName;
	/** 已勾選資料(用逗號,串聯) */
	private String checkedList;

	//getters and setters
    public String getCodeKind() {return checkGet(codeKind);}
	public void setCodeKind(String codeKind) {this.codeKind = checkSet(codeKind);}

	public String getCode() {return checkGet(code);}
	public void setCode(String code) {this.code = checkSet(code);}

	public String getCodeName() {return checkGet(codeName);}
	public void setCodeName(String codeName) {this.codeName = checkSet(codeName);}

	public String getCheckedList() {	return checkGet(checkedList);	}
	public void setCheckedList(String s) {	this.checkedList = checkSet(s);	} 

	@Override
	public void doCreate() throws Exception {
	}

	@Override
	public void doUpdate() throws Exception {
	}

	@Override
	public void doDelete() throws Exception {
	}

	@Override
	public Object doQueryOne() throws Exception {
		return null;
	}

}