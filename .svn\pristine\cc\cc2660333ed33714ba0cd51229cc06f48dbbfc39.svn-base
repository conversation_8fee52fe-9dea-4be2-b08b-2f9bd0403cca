package com.kangdainfo.tcfi.scheduling;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.tcfi.model.eicm.bo.Queue;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;

/**
 * OSSS一站式介接排程
 */
public class UpdateOsssQuartzJobBean extends BaseQuartzJobBean {
	
	protected void executeJob(JobExecutionContext context) throws JobExecutionException {
		SystemCode ossSync = ServiceGetter.getInstance().getSystemCode01Loader().getSystemCodeByCode("OSSSync");
		if(null!=ossSync && "Y".equals(ossSync.getCodeName()) ) {
			Queue queueObj = ServiceGetter.getInstance().getUpdateOsssStatusService().getToDoQueue();	
			if(queueObj != null) {
				ServiceGetter.getInstance().getUpdateOsssStatusService().doSyncOsss(queueObj);
				try {
				    Thread.sleep(1000);//1000 milliseconds is one second.
				} catch(Exception ex) {
				}
				//同步一站式申辦類型
				ServiceGetter.getInstance().getPrefixService().doSyncOsssApplyType(queueObj.getPrefixNo());
			}
		}
	}
}
