package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;


/**
 * 投審會異動資料傳輸紀錄檔(CSML_CMPY_TRAN_MOEAIC)
 *
 */
public class CsmlCmpyTranMoeaic extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** PK流水號 */
	private Long idntpk;
	/** 公司統編 */
	private String banNo;
	/** 預查編號 */
	private String prefixNo;
	/** 變更代碼 */
	private String updCode;
	/** 變更日期 */
	private Date updDate;
	/** 傳送日期 */
	private Date transferDate;
	/** 狀態 */
	private String status;
	/** 備註 */
	private String remark;
	/** 核准文號 */
	private String receiveNo;
	/** 核准日期 */
	private String respDate;
	/** 案由代碼(00：預查;01：設立;02：變更;03：投資;04：增資;05：減資;06：審定;07：解散;08：廢止;09：撤銷) */
	private String caseCode;
	/** 案由說明 */
	private String caseDesc;
	/** 投審會核准文號 */
	private String moeaicNo;
	/** 公司名稱 */
	private String cmpyName;
	/** 審定金額 */
	private Long impAmount;

	public Long getIdntpk() {return idntpk;}
	public void setIdntpk(Long idntpk) {this.idntpk = idntpk;}
	public String getBanNo() {return banNo;}
	public void setBanNo(String banNo) {this.banNo = banNo;}
	public String getPrefixNo() {return prefixNo;}
	public void setPrefixNo(String prefixNo) {this.prefixNo = prefixNo;}
	public String getUpdCode() {return updCode;}
	public void setUpdCode(String updCode) {this.updCode = updCode;}
	public Date getUpdDate() {return updDate;}
	public void setUpdDate(Date updDate) {this.updDate = updDate;}
	public Date getTransferDate() {return transferDate;}
	public void setTransferDate(Date transferDate) {this.transferDate = transferDate;}
	public String getStatus() {return status;}
	public void setStatus(String status) {this.status = status;}
	public String getRemark() {return remark;}
	public void setRemark(String remark) {this.remark = remark;}
	public String getReceiveNo() {return receiveNo;}
	public void setReceiveNo(String receiveNo) {this.receiveNo = receiveNo;}
	public String getRespDate() {return respDate;}
	public void setRespDate(String respDate) {this.respDate = respDate;}
	public String getCaseCode() {return caseCode;}
	public void setCaseCode(String caseCode) {this.caseCode = caseCode;}
	public String getCaseDesc() {return caseDesc;}
	public void setCaseDesc(String caseDesc) {this.caseDesc = caseDesc;}
	public String getMoeaicNo() {return moeaicNo;}
	public void setMoeaicNo(String moeaicNo) {this.moeaicNo = moeaicNo;}
	public String getCmpyName() {return cmpyName;}
	public void setCmpyName(String cmpyName) {this.cmpyName = cmpyName;}
	public Long getImpAmount() {return impAmount;}
	public void setImpAmount(Long impAmount) {this.impAmount = impAmount;}

}