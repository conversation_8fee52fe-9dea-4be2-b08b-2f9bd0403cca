package com.kangdainfo.tcfi.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.ekera.presearch.Examine;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3000;
import com.kangdainfo.tcfi.model.eedb.bo.Eedb3100;
//import com.kangdainfo.tcfi.model.eedb.bo.Eedb3300;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3000Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3100Dao;
import com.kangdainfo.tcfi.model.eedb.dao.Eedb3300Dao;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1001;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1010;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1019;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1022;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1023;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2002;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc054;
import com.kangdainfo.tcfi.model.eicm.bo.Eedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.FlowLog;
import com.kangdainfo.tcfi.model.eicm.bo.LmsmBussMain;
import com.kangdainfo.tcfi.model.eicm.bo.PrefixReceiptNo;
import com.kangdainfo.tcfi.model.eicm.bo.SystemCode;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1001Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1010Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1019Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1022Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb1023Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedb2002Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Cedbc054Dao;
import com.kangdainfo.tcfi.model.eicm.dao.Eedb1000Dao;
import com.kangdainfo.tcfi.model.eicm.dao.FlowLogDao;
import com.kangdainfo.tcfi.model.eicm.dao.GeneralityBusitemDao;
import com.kangdainfo.tcfi.model.eicm.dao.PrefixReceiptNoDAO;
import com.kangdainfo.tcfi.model.eicm.dao.ReceiptNoSetupDAO;
import com.kangdainfo.tcfi.model.eicm.dao.SystemCodeDao;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplFlow;
import com.kangdainfo.tcfi.model.osss.bo.OssmApplMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmBussItem;
import com.kangdainfo.tcfi.model.osss.bo.OssmFeeMain;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgChange;
import com.kangdainfo.tcfi.model.osss.bo.OssmOrgName;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplFlowDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmApplMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmBussItemDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmFeeMainDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgChangeDao;
import com.kangdainfo.tcfi.model.osss.dao.OssmOrgNameDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBusiItemDao;
import com.kangdainfo.tcfi.model.lms.dao.LmsmBussMainDao;
import com.kangdainfo.tcfi.service.Pre1001Service;
import com.kangdainfo.tcfi.util.PrefixConstants;
import com.kangdainfo.tcfi.util.TcfiView;
import com.kangdainfo.tcfi.view.pre.PRE1001;

/**
 * 預查申請資料收文
 *
 */
public class Pre1001ServiceImpl implements Pre1001Service {
	private Logger logger = Logger.getLogger(this.getClass());

	/** 取號存檔  */
	public synchronized String savePrefixCase(PRE1001 bean, String userId) {
		if(logger.isInfoEnabled()) logger.info("[savePrefixCase][userId:"+userId+"]");
		if(logger.isInfoEnabled()) logger.info("[savePrefixCase][網路流水號:"+bean.getTelixNo()+"]");
		String prefixNo = "";
		try {
			//網路流水號
			String telixNo = Common.get(bean.getTelixNo());
			
			//判斷電子收文號是否已存在
			if(!"".equals(telixNo) ) {
				Cedb1000 cedb1000 = cedb1000Dao.findByTelixNo(telixNo);
				if (null!=cedb1000) {
					if( telixNo.startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
						saveOsssStatus101(telixNo, userId, cedb1000.getPrefixNo());
					} 
					return "";
				}
			}
			
			//取預查編號
			Cedb1019 cedb1019 = cedb1019Dao.findByYearNo(Datetime.getYYY());
			if(null!=cedb1019) {
				Cedb1019 newCedb1019 = cedb1019Dao.updatePrefixNoSeq(cedb1019); //將流水號 +1
				prefixNo = newCedb1019.getPrefixNo();
			}
			if(logger.isInfoEnabled()) logger.info("[savePrefixCase][取預查編號][prefixNo:"+prefixNo+"]");
			if(!"".equals(prefixNo)) {
				//統一編號
				String banNo = Common.get(bean.getBanNo());
				//是否為設立案
				String setup = Common.get(bean.getSetup());
				//系統日期
				String yyymmdd = Datetime.getYYYMMDD();
				//系統時間
				String hhmmss = Datetime.getHHMMSS();

				//預查主檔(含Cedb1000 與 Cedb1023)
				saveCedb1000(bean, prefixNo, userId, yyymmdd, hhmmss);
		    	//公司名稱
				saveCedb1001(prefixNo, telixNo, banNo);
				//營業項目
				saveCedb1002(prefixNo, telixNo, banNo);
				//法人資料
				saveCedb1022(bean, prefixNo, telixNo);
				//案件歷程
				saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_1, yyymmdd, hhmmss);
				// 收據編號
				if (telixNo.startsWith("OSC") || telixNo.startsWith("OSS")) {
					saveReceiptNoOSSS(prefixNo, bean);
				} else {
					saveReceiptNoEICM(prefixNo, bean);
				}
				
				if( !"".equals(Common.get(telixNo)) ) {
					//若有網路收文號, 表示非紙本收文, 不需進收文登打, 直接寫收文登打紀錄
					saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_2, yyymmdd, hhmmss);

					//回寫案件狀態
					if( telixNo.startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
						//一站式案件
						saveOsssStatus101(telixNo, userId, prefixNo);
					} else if( telixNo.startsWith("Z") ) {
						//一維條碼案件
						Eedb1000 eedb1000 = new Eedb1000();
						eedb1000.setTelixNo(telixNo);
						eedb1000.setPrefixNo(prefixNo);
						eedb1000.setReceiveDate(yyymmdd);
						eedb1000.setReceiveTime(hhmmss);
						eedb1000.setCaseCode("Y".equals(setup)?"Z1":"Z2");
						eedb1000.setRegUnitCode("00");
						eedb1000.setProcessStatus("BB");
						eedb1000Dao.insert(eedb1000);
					}
				}
			}
		} catch(Exception e) {
			e.printStackTrace();
			prefixNo = "";
		}
		//回傳預查編號
		return prefixNo;
	}
	
	/** 智慧型預查 */
	public String doPreSearch(String prefixNo) {
		String result = new Examine().searchPrefix(prefixNo);// 審查後傳回結果。另一個searchPrefixResult 則是回傳先前的審核結果
		if(logger.isInfoEnabled()) logger.info("[preSearch][prefixNo:" + prefixNo + "已收文]");
		return result;
	}
	
	/** 更新一站式狀態為已收文  */
	public void saveOsssStatus101(String telixNo, String userId, String prefixNo) {
		try {
			ossmApplFlowDao.updateProcessByTelixNo(telixNo, PrefixConstants.OSSS_STATUS_101, userId, prefixNo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/** 寫預查主檔(CEDB1000) */
	private void saveCedb1000(PRE1001 bean, String prefixNo, String userId, String yyymmdd, String hhmmss) {
		//網路流水號
		String telixNo = Common.get(bean.getTelixNo()).toUpperCase();
		//統一編號
		String banNo = Common.get(bean.getBanNo());
		//申請人身分ID
		String applyId = Common.get(bean.getApplyId()).toUpperCase();
		//申請人姓名
		String applyName = Common.get(bean.getApplyName());
		//代理人事務所所在地
		String attorAddr = Common.get(bean.getAttorAddr());
		//代理人姓名
		String attorName = Common.get(bean.getAttorName());
		//代理人證書號碼
		String attorNo = Common.get(bean.getAttorNo());
		//代理人聯絡電話
		String attorTel = Common.get(bean.getAttorTel());
		//聯絡人地址
		String contactAddr = Common.get(bean.getContactAddr());
		//聯絡人行動電話
		String contactCel = Common.get(bean.getContactCel());
		//聯絡人姓名
		String contactName = Common.get(bean.getContactName());
		//聯絡人聯絡電話
		String applyTel = Common.get(bean.getApplyTel());
		//是否有預查表附件
		String isPrefixForm = ("Y".equalsIgnoreCase(Common.get(bean.getIsPrefixForm()))?"Y":"N");
		//附件-正副本別
		String docType = Common.get(bean.getDocType());
		//附件-預查表編號
		String prefixFormNo = Common.get(bean.getPrefixFormNo());
		//附件-是否有其他機關核准函附件
		String isOtherForm = ("Y".equalsIgnoreCase(Common.get(bean.getIsOtherForm()))?"Y":"N");
		//附件-是否有說明書附件
		String isSpec = ("Y".equalsIgnoreCase(Common.get(bean.getIsSpec()))?"Y":"N");
		//附件-是否有其他附件
		String isOtherSpec = ("Y".equalsIgnoreCase(Common.get(bean.getIsOtherSpec()))?"Y":"N");
		//附件-其他附件註記
		String otherSpecRemark = Common.get(bean.getOtherSpecRemark());
		//領件方式
		String contactGetKind = Common.get(bean.getContactGetKind());
		//郵寄註記
		String getKindRemark = Common.get(bean.getGetKindRemark());
		//公司地址
		String companyAddr = Common.get(bean.getCompanyAddr());
		//預查種類
		String changeType = PrefixConstants.CHANGE_TYPE_0;
		String applyKind = "1";
		if( "Y".equalsIgnoreCase(bean.getSetup()) ) {
			changeType = PrefixConstants.CHANGE_TYPE_0;
			applyKind = "1";
		} else {
			applyKind = "2";
			if( "Y".equalsIgnoreCase(bean.getChangeItem()) && "Y".equalsIgnoreCase(bean.getChangeName()) ) {
    			//公司名稱及所營事業變更預查
    			changeType = PrefixConstants.CHANGE_TYPE_3;
			} else if( "Y".equalsIgnoreCase(bean.getChangeItem()) && !"Y".equalsIgnoreCase(bean.getChangeName()) ) {
    			//公司所營事業變更預查
    			changeType = PrefixConstants.CHANGE_TYPE_2;
			} else if( !"Y".equalsIgnoreCase(bean.getChangeItem()) && "Y".equalsIgnoreCase(bean.getChangeName()) ) {
    			//公司名稱變更預查
    			changeType = PrefixConstants.CHANGE_TYPE_1;
			}
		}
		
		//組織別
		String orgType = "18".equals(bean.getOrgType())? "05":bean.getOrgType();
		// 依一站式開發廠商提供的一站式測試案件, 有限合夥的orgType=18
		
		//是否為閉鎖性
		String closed = bean.getClosed();
		//非股份有限公司不可有閉鎖性屬性
		if (!"01".equals(orgType)) {
			closed = "N";
		}
		//原公司名稱
		String oldCompanyName = "";
		//公司名稱
		String companyName = bean.getCompanyName();
		if( PrefixConstants.CHANGE_TYPE_0.equals(changeType) ) {
			//查詢法人資料
			//String orgCorpName = findOrgCorpName(prefixNo, telixNo);
			//if( !"".equals(orgCorpName) ) {
			//	applyName = orgCorpName + " 代表人:" + applyName;
			//}
		} else {
			try {
				if(!"".equals(banNo)) {
					Cedb2000 cedb2000 = cedb2000Dao.findByBanNo(banNo);
					if(null!=cedb2000) {
						oldCompanyName = cedb2000.getCompanyName();
					} else {
						// QASC：11005310013 申請有限合夥變更案會找不到資料，若CEDB2000無資料則去lms撈
						LmsmBussMain mainvo = lmsmBussMainDao.queryLmsInfoByBanNo(banNo);
						if (mainvo != null) {
							oldCompanyName=Common.get(mainvo.getLmsName());
						}
					}
				}
			} catch (Exception ex) {
				oldCompanyName = "";
			}
			//所營變更 不涉及 公司名稱, 故直接設定company_name
			if( PrefixConstants.CHANGE_TYPE_2.equals(changeType) ) {
				if( !"".equals(Common.get(oldCompanyName)) ) {
					companyName = oldCompanyName;
				}
			}
		}

    	//預查主檔
		Cedb1000 cedb1000 = new Cedb1000();
		//預查種類
		cedb1000.setApplyKind(applyKind);
		//申請人地址
		cedb1000.setApplyAddr(companyAddr);
		//申請人身分證統一編號
		cedb1000.setApplyId(applyId);
		//申請人姓名
		cedb1000.setApplyName(TcfiView.normalizeName(applyName));
		//申請人聯絡電話
		cedb1000.setApplyTel(applyTel);
		//代理人所在地
		cedb1000.setAttorAddr(attorAddr);
		//代理人姓名
		cedb1000.setAttorName(attorName);
		//代理人證書號碼
		cedb1000.setAttorNo(attorNo);
		//代理人聯絡電話
		cedb1000.setAttorTel(attorTel);
		//附件-是否有預查表附件
		cedb1000.setIsPrefixForm(isPrefixForm);
		//附件-正副本別
		cedb1000.setDocType(docType);
		//附件-預查表編號
		cedb1000.setPrefixFormNo(prefixFormNo);
		//附件-是否有其他機關核准函附件
		cedb1000.setIsOtherForm(isOtherForm);
		//附件-是否有說明書附件
		cedb1000.setIsSpec(isSpec);
		//附件-是否有其他附件
		cedb1000.setIsOtherSpec(isOtherSpec);
		//附件-其他附件註記
		cedb1000.setOtherSpecRemark(otherSpecRemark);
		//領件方式
		cedb1000.setGetKind(contactGetKind);
		//郵寄註記
		cedb1000.setGetKindRemark(getKindRemark);
		//統一編號
		cedb1000.setBanNo(banNo);
		//網路流水號
		cedb1000.setTelixNo(telixNo);
		//公司名稱
		cedb1000.setCompanyName(companyName);
		//原公司名稱
		cedb1000.setOldCompanyName(oldCompanyName);
		//預查編號
		cedb1000.setPrefixNo(prefixNo);
		//異動人員
		cedb1000.setUpdateIdNo(userId);
		//異動原因
		cedb1000.setUpdateCode("11");
		//收文確認註記
		cedb1000.setRcvCheck("N");
		//提辦案件
		cedb1000.setControlCd1("N");
		//抽換案件
		cedb1000.setControlCd2("N");
		//延展保留期限註記
		cedb1000.setReserveMark("N");
		//案件性質，1 為一般
		cedb1000.setApplyType("1");
		//收文日期
		cedb1000.setReceiveDate(yyymmdd);
		//收文時間
		cedb1000.setReceiveTime(hhmmss);
		cedb1000.setUpdateDate(yyymmdd);
		cedb1000.setUpdateTime(hhmmss);
		cedb1000.setApproveResult(PrefixConstants.APPROVE_RESULT_A);
		cedb1000.setPrefixStatus(PrefixConstants.PREFIX_STATUS_1);
		if( !"".equals(Common.get(telixNo)) ) {
			cedb1000.setPrefixStatus(PrefixConstants.PREFIX_STATUS_2);
		}
		//國外匯款使用英文名稱
		cedb1000.setExtRemitEname(bean.getExtRemitEname());
		
		//寫入Cedb1000
		cedb1000Dao.insert(cedb1000);
		//寫入Cedb1023
		saveCedb1023(prefixNo, contactName, contactAddr, contactCel, changeType, closed, orgType);
		//寫入個資軌跡TrackLog(CEDB1000)
		//ServiceGetter.getInstance().getTrackLogService().doUpateTrack(PrefixConstants.FUN_CODE_1001, cedb1000);
	}

	/** 寫公司名稱(CEDB1001) */
	private void saveCedb1001(String prefixNo, String telixNo, String banNo) {
		List<Cedb1001> cedb1001s = new ArrayList<Cedb1001>();
		//1.判斷網路流水號
		if( Common.get(telixNo).startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
			//1.1.若為OSC開頭, 表示為一站式案件, 從一站式資料查公司名稱
			List<OssmOrgName> list = ossmOrgNameDao.findByTelixNo(telixNo);
			if(null!=list && !list.isEmpty()) {
				Cedb1001 cedb1001 = null;
				for(OssmOrgName o : list) {
					cedb1001 = new Cedb1001();
					cedb1001.setPrefixNo(prefixNo);
					cedb1001.setSeqNo(Common.formatFrontZero(String.valueOf(o.getSeqNo()),2));
					cedb1001.setCompanyName(Common.get(o.getOrgName()));
					cedb1001.setApproveResult("N");
					cedb1001s.add(cedb1001);
				}
			}
		} else if( Common.get(telixNo).startsWith("Z") ) {
			//1.2.若為Z開頭, 表示為一維條碼案件, 從一維條碼資料查公司名稱
			List<Eedb3000> list = eedb3000Dao.findByTelixNo(telixNo);
			if(null!=list && !list.isEmpty()) {
				Cedb1001 cedb1001 = null;
				for(Eedb3000 o : list) {
					cedb1001 = new Cedb1001();
					cedb1001.setPrefixNo(prefixNo);
					cedb1001.setSeqNo(Common.formatFrontZero(String.valueOf(o.getSeqNo()),2));
					cedb1001.setCompanyName(o.getCompanyName());
					cedb1001.setApproveResult("N");
					cedb1001s.add(cedb1001);
				}
			}
		}
		//2.若查無公司名稱時, 從公司資料補
		if(null==cedb1001s || cedb1001s.isEmpty()) {
			if( !"".equals(Common.get(telixNo)) ) {
				try {
					if(!"".equals(banNo)) {
						Cedb2000 o = cedb2000Dao.findByBanNo(banNo);
						Cedb1001 cedb1001 = null;
						if(null!=o) {
							cedb1001 = new Cedb1001();
							cedb1001.setPrefixNo(prefixNo);
							cedb1001.setSeqNo("01");
							cedb1001.setCompanyName(o.getCompanyName());
							cedb1001.setApproveResult("N");
							cedb1001s.add(cedb1001);
						} else {
							// QASC：11005310013 申請有限合夥變更案會找不到資料，若CEDB2000無資料則去lms撈
							LmsmBussMain mainvo = lmsmBussMainDao.queryLmsInfoByBanNo(banNo);
							if (mainvo != null) {
								cedb1001 = new Cedb1001();
								cedb1001.setPrefixNo(prefixNo);
								cedb1001.setSeqNo("01");
								cedb1001.setCompanyName(Common.get(mainvo.getLmsName()));
								cedb1001.setApproveResult("N");
								cedb1001s.add(cedb1001);
							}
						}
					}
				} catch (Exception ex) {
				}
			}
		}
		//3.存檔
		if(null!=cedb1001s && !cedb1001s.isEmpty()) {
			for(Cedb1001 cedb1001 : cedb1001s) {
				cedb1001Dao.insert(cedb1001);
			}
		}
	}
	
	/** 寫營業項目(CEDB1002) */
	private void saveCedb1002(String prefixNo, String telixNo, String banNo) {
		telixNo = Common.get(telixNo);

		List<Cedb1002> cedb1002s = new ArrayList<Cedb1002>();
		//1.判斷網路流水號
		if( telixNo.startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
			//1.1.若為OSC開頭, 表示為一站式案件, 從一站式資料查營業項目
			List<OssmBussItem> list = ossmBussItemDao.findByTelixNo(telixNo);
			if(null!=list && !list.isEmpty()) {
				Cedb1002 cedb1002 = null;
				for(OssmBussItem o : list) {
					cedb1002 = new Cedb1002();
					cedb1002.setPrefixNo(prefixNo);
					cedb1002.setSeqNo(Common.formatFrontZero(o.getSeqNo(),3));
					cedb1002.setBusiItem(Common.get(o.getBusiItem()));
					cedb1002.setBusiItemNo(Common.get(o.getBusiItemNo()));
					cedb1002s.add(cedb1002);
				}
			}
		} else if( telixNo.startsWith("Z") ) {
			//1.2.若為Z開頭, 表示為一維條碼案件, 從一維條碼資料查營業項目
			List<Eedb3100> list = eedb3100Dao.findByTelixNo(telixNo);
			if(null!=list && !list.isEmpty()) {
				Cedb1002 cedb1002 = null;
				for(Eedb3100 o : list) {
					cedb1002 = new Cedb1002();
					cedb1002.setPrefixNo(prefixNo);
					cedb1002.setSeqNo(Common.formatFrontZero(o.getSeqNo(),3));
					cedb1002.setBusiItem(Common.get(o.getBusiItem()));
					cedb1002.setBusiItemNo(Common.get(o.getBusiItemNo()));
					cedb1002s.add(cedb1002);
				}
			}
		}
		//2.若查無營業項目時, 從公司資料補
		if(null==cedb1002s || cedb1002s.isEmpty()) {
			if( !"".equals(telixNo) ) {
				try {
					if(!"".equals(banNo)) {
						List<Cedb2002> list = cedb2002Dao.findByBanNo(banNo);
						if(null!=list && !list.isEmpty()) {
							Cedb1002 cedb1002 = null;
							for(Cedb2002 o : list) {
								cedb1002 = new Cedb1002();
								cedb1002.setPrefixNo(prefixNo);
								cedb1002.setSeqNo(Common.formatFrontZero(o.getSeqNo(),3));
								cedb1002.setBusiItem(Common.get(o.getBusiItem()));
								cedb1002.setBusiItemNo(Common.get(o.getBusiItemNo()).replaceAll("　", ""));
								cedb1002s.add(cedb1002);
							}
						} else {
							// QASC：11005310013 申請有限合夥變更案會找不到資料，若CEDB2002無資料則去lms撈
							List<Cedb2002> itemList = lmsmBusiItemDao.findCedb2002ByBanNo(banNo);
							if(null != itemList && !itemList.isEmpty()) {
								Cedb1002 cedb1002 = null;
								for(Cedb2002 o : itemList) {
									cedb1002 = new Cedb1002();
									cedb1002.setPrefixNo(prefixNo);
									cedb1002.setSeqNo(Common.formatFrontZero(o.getSeqNo(),3));
									cedb1002.setBusiItem(Common.get(o.getBusiItem()));
									cedb1002.setBusiItemNo(Common.get(o.getBusiItemNo()).replaceAll("　", ""));
									cedb1002s.add(cedb1002);
								}
							}
						}
					}
				} catch (Exception ex) {
				}
			}
		}
		//3.存檔
		if(null!=cedb1002s && !cedb1002s.isEmpty()) {
			for(Cedb1002 cedb1002 : cedb1002s) {
				cedb1002Dao.insert(cedb1002);
			}
		}
	}
	
	/** 寫案件歷程 */
	private void saveCedb1010(String prefixNo, String idNo, String processStatus, String yyymmdd, String hhmmss) {
		Float workDay = new Float(0.0);
		Cedb1010 cedb1010 = new Cedb1010();
		cedb1010.setPrefixNo(prefixNo);
		cedb1010.setIdNo(idNo);
		cedb1010.setProcessDate(yyymmdd);
		cedb1010.setProcessTime(hhmmss);
		cedb1010.setProcessStatus(processStatus);
		cedb1010.setWorkDay(workDay);
		cedb1010Dao.insert(cedb1010);
		//多寫案件歷程FLOW_LOG
		FlowLog flowLog = new FlowLog();
		flowLog.setPrefixNo(prefixNo);
		flowLog.setIdNo(idNo);
		flowLog.setProcessDate(yyymmdd);
		flowLog.setProcessTime(hhmmss);
		flowLog.setProcessStatus(processStatus);
		flowLog.setWorkDay(workDay);
		flowLog.setModIdNo(idNo);
		flowLog.setModDate(yyymmdd);
		flowLog.setModTime(hhmmss);
		flowLogDao.insert(flowLog);
	}
	
	/** 寫收據資料(一站式收件) */
	private void saveReceiptNoOSSS(String prefixNo, PRE1001 bean) {
		OssmFeeMain ossmFeeMain = ossmFeeMainDao.findByTelixNoAndProcessNo(bean.getTelixNo(), "B");
		if (ossmFeeMain == null)
			ossmFeeMain = ossmFeeMainDao.findByTelixNoAndProcessNo(bean.getTelixNo(), "I");
		PrefixReceiptNo prefixReceiptNo = new PrefixReceiptNo();
		prefixReceiptNo.setPrefixNo(prefixNo);
		prefixReceiptNo.setTelixNo(bean.getTelixNo());
		prefixReceiptNo.setRecipientName(bean.getContactName());
		prefixReceiptNo.setRecipientAddr(bean.getContactAddr());
		if (ossmFeeMain != null) {
			prefixReceiptNo.setReceiptNo(ossmFeeMain.getReceiptNo());	
			prefixReceiptNo.setReceiptType("0");
			
			if (ossmFeeMain.getPayType() == 1) { // 一站式的信用卡
				prefixReceiptNo.setPayType("2");
			} else if (ossmFeeMain.getPayType() == 2) {  // 一站式的金融帳戶
				prefixReceiptNo.setPayType("4");
			} else if (ossmFeeMain.getPayType() == 3) {  // 一站式的金融卡d
				prefixReceiptNo.setPayType("3");
			} else {
				prefixReceiptNo.setPayType(null);
			}
			
			prefixReceiptNo.setPayDate(ossmFeeMain.getAccountDate());
			prefixReceiptNo.setAmount(ossmFeeMain.getAmount());
		}
		prefixReceiptNoDAO.insert(prefixReceiptNo);
	}
	
	/** 寫收據資料(EICM或現場紙本) */
	private void saveReceiptNoEICM(String prefixNo, PRE1001 bean) {
		// EICM案件, 依TELIX_NO找到收據資料檔, 並寫入預查編號;
		// 紙本收件,  收文時會要求輸入收據號, 依收據號找到收據資料檔, 並寫入預查編號
		PrefixReceiptNo prefixReceiptNo = new PrefixReceiptNo();
		if (bean.getTelixNo() != null && !"".equals(bean.getTelixNo())) 
			prefixReceiptNo = prefixReceiptNoDAO.selectByTelixNo(bean.getTelixNo());
		else
			prefixReceiptNo = prefixReceiptNoDAO.selectByReceiptNo(bean.getReceiptNo());
		
		if (prefixReceiptNo != null) {
			prefixReceiptNo.setRecipientName(bean.getContactName());
			prefixReceiptNo.setRecipientAddr(bean.getContactAddr());
			prefixReceiptNo.setPrefixNo(prefixNo);
			prefixReceiptNoDAO.updateByTelixNo(prefixReceiptNo);
		}
	}

	/** 寫法人資料 */
	private void saveCedb1022(PRE1001 bean, String prefixNo, String telixNo) {
		Cedb1022 cedb1022 = null;
		//1.判斷網路流水號
		if( Common.get(telixNo).startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
			//1.1.若為OSC開頭, 表示為一站式案件
			OssmOrgChange o = ossmOrgChangeDao.findByTelixNo(telixNo);
			if(null!=o) {
				if("2".equals(o.getApplicantType())) {
					//2:法人(政府或公司法人)
					if( "Y".equalsIgnoreCase(bean.getSetup()) ) {
						//若為設立案就寫
						cedb1022 = new Cedb1022();
						cedb1022.setPrefixNo(prefixNo);
						cedb1022.setApplyBanNo(Common.get(o.getCorpBanNo()));
						cedb1022.setApplyLawName(Common.get(o.getCorpName()));
					} else {
						//若為變更案，則判斷若不是被申請公司才寫
						String banNo = Common.get(bean.getBanNo());
						if(!banNo.equals(Common.get(o.getCorpBanNo()))) {
							cedb1022 = new Cedb1022();
							cedb1022.setPrefixNo(prefixNo);
							cedb1022.setApplyBanNo(Common.get(o.getCorpBanNo()));
							cedb1022.setApplyLawName(Common.get(o.getCorpName()));
						}
					}
				} else if("1".equals(o.getApplicantType())) {
					//1:自然人
					//若為變更案
					if( "Y".equalsIgnoreCase(bean.getChangeItem())
						|| "Y".equalsIgnoreCase(bean.getChangeName()) )
					{
						//且申請人為公司負責人，且為法人代表時，查公司系統資料來補
						OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
						if(null!=ossmApplMain) {
							//統一編號
							String banNo = ossmApplMain.getBanNo();
							//申請人身分ID
							String applyId = Common.get(bean.getApplyId()).toUpperCase();
							
							SQLJob sqljob = new SQLJob();
							sqljob.appendSQL("SELECT a.CORP_BAN_NO, a.CORP_NAME");
							sqljob.appendSQL("FROM icms.csmx_cmpy_seqno_map c");
							sqljob.appendSQL("inner join Icms.Csmm_Share_Holder b on c.ban_no=b.ban_no and c.sh_cmpy_modif_no=b.cmpy_modif_no");
							sqljob.appendSQL("inner join Icms.CSMM_CORP_LIST a on c.ban_no=a.ban_no and c.corp_cmpy_modif_no=a.cmpy_modif_no and a.seq_no=b.corp_seq_no");
							sqljob.appendSQL("WHERE c.is_newest>'N'");
							sqljob.appendSQL("and b.Cmpy_Rep='Y'");
							sqljob.appendSQL("and b.ban_no=?");
							sqljob.appendSQL("and b.id_no=?");
							sqljob.addParameter(banNo);
							sqljob.addSqltypes(oracle.jdbc.OracleTypes.VARCHAR);
							sqljob.addParameter(applyId);
							sqljob.addSqltypes(oracle.jdbc.OracleTypes.VARCHAR);

							if(logger.isInfoEnabled()) logger.info(sqljob);
							java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getIcmsGeneralQueryDao().queryForList(sqljob);
							if (objList != null && objList.size() > 0) {
								Map<String,Object> map = objList.get(0);
								if(null!=map) {
									String corpBanNo = Common.get(map.get("CORP_BAN_NO"));
									String corpName = Common.get(map.get("CORP_NAME"));
									cedb1022 = new Cedb1022();
									cedb1022.setPrefixNo(prefixNo);
									cedb1022.setApplyBanNo(corpBanNo);
									cedb1022.setApplyLawName(corpName);
								}
							}
						}
					}
				}
			}
		} else if( Common.get(telixNo).startsWith("Z") ) {
			//1.2.若為Z開頭, 表示為一維條碼案件
			cedb1022 = new Cedb1022();
			cedb1022.setPrefixNo(prefixNo);
			cedb1022.setApplyBanNo(Common.get(bean.getApplyBanNo()));
			cedb1022.setApplyLawName(Common.get(bean.getApplyLawName()));
		}
		//2.存檔
		if(null!=cedb1022) {
			cedb1022Dao.insert(cedb1022);
		}
	}

//	/** 查法人名稱 */
//	private String findOrgCorpName(String prefixNo, String telixNo) {
//		String orgCorpName = "";
//		//1.判斷網路流水號
//		if( Common.get(telixNo).startsWith("OSC") ) {
//			//1.1.若為OSC開頭, 表示為一站式案件
//			OssmOrgChange o = ossmOrgChangeDao.findByTelixNo(telixNo);
//			if(null!=o && "2".equals(o.getApplicantType())) {
//				orgCorpName = Common.get(o.getCorpName());
//			}
//		} else if( Common.get(telixNo).startsWith("Z") ) {
//			//1.2.若為Z開頭, 表示為一維條碼案件
//			Eedb3300 o = eedb3300Dao.findByTelixNo(telixNo);
//			if(null!=o) {
//				orgCorpName = Common.get(o.getOrgCorpName());
//			}
//		}
//		return orgCorpName;
//	}
	/** 取號存檔時判斷這個電子收文號是否已開收據 */
	public boolean selectPrefixReceiptNoByTelixNo(String telixNo) {
		PrefixReceiptNo prefixReceiptNo = prefixReceiptNoDAO.selectByTelixNo(telixNo);
		if (prefixReceiptNo == null)
			return false;
		else
			return true;
	}
	
	/** 現場收文時憑申請者手上的收據單號搜尋prefixReceiptNo以確認是否確實開立過此收據 */
	public boolean selectPrefixReceiptNoByReceiptNo(String receiptNo) {
		PrefixReceiptNo prefixReceiptNo = prefixReceiptNoDAO.selectByReceiptNo(receiptNo);
		if (prefixReceiptNo == null)
			return false;
		else
			return true;
	}
	
	/** 寫收件人資料 */
	private void saveCedb1023(String prefixNo, String contactName, String contactAddr, String contactCel, String changeType, String closed, String orgType) {
		Cedb1023 cedb1023 = new Cedb1023();
		cedb1023.setPrefixNo(prefixNo);
		cedb1023.setGetAddr(contactAddr);
		cedb1023.setGetName(contactName);
		//收文寫null, 發完簡訊後, 由簡訊系統回寫'D'
		cedb1023.setSms(null);
		cedb1023.setContactCel(contactCel);
		cedb1023.setChangeType(changeType);
		cedb1023.setClosed(closed);
		cedb1023.setOrgType(orgType);
		cedb1023Dao.insert(cedb1023);
		//寫入個資軌跡TrackLog(CEDB1023)
		//ServiceGetter.getInstance().getTrackLogService().doUpateTrack(PrefixConstants.FUN_CODE_1001, cedb1023);
	}

	/** 判斷電子收文號是否已收文 */
	public boolean queryTelixNoExist(String telixNo) {
		boolean isExist = false;
		if( !"".equals(Common.get(telixNo)) ) {
			Cedb1000 cedb1000 = cedb1000Dao.findByTelixNo(telixNo);
			if (null!=cedb1000) {
				isExist = true;
			}
		}
		return isExist;
	}
	
	/** 線上審核案件 - 直接核准 */
	public void checkOnlineAudit(String prefixNo, String userId) {
		Cedb1000 cedb1000 = cedb1000Dao.findByPrefixNo(prefixNo);
		if(null!=cedb1000) {
			String receiveDate = Common.get(cedb1000.getReceiveDate());
			String receiveTime = Common.get(cedb1000.getReceiveTime());
			String telixNo = Common.get(cedb1000.getTelixNo());
			
			if( telixNo.startsWith("OSC") || Common.get(telixNo).startsWith("OSS") ) {
				OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
				String companyName = Common.get(ossmApplMain.getOrgName());
				String ossApplyType = ossmApplMain.getApplyType();
				if (PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossApplyType)) {
					//電子流水號為英文L0100開頭，屬於線上審核，線上審核案件，直接核准
					cedb1000.setCompanyName(companyName);
					cedb1000.setApproveResult(PrefixConstants.APPROVE_RESULT_Y);
					cedb1000.setApproveDate(receiveDate);
					cedb1000.setApproveTime(receiveTime);
					cedb1000.setAssignDate(receiveDate);
					cedb1000.setAssignTime(receiveTime);
					cedb1000.setCloseDate(receiveDate);
					cedb1000.setCloseTime(receiveTime);
					cedb1000.setPrefixStatus(PrefixConstants.PREFIX_STATUS_8);//(8)發文結案
					cedb1000.setIdNo(PrefixConstants.L0100_ID_NO);
					cedb1000.setStaffName(PrefixConstants.L0100_STAFF_NAME);
					Integer reserveDays = new Integer(180);//保留半年
					cedb1000.setReserveDays(reserveDays);
					String reserveMark = "N";//不延展保留一個月
					cedb1000.setReserveMark(reserveMark);
					cedb1000.setReserveDate(ServiceGetter.getInstance().getPrefixService().countReserveDate(receiveDate, reserveDays, reserveMark));
					//備份
					ServiceGetter.getInstance().getBackupService().doBackup(prefixNo, userId);
					//更新主檔
					cedb1000Dao.update(cedb1000);
					//線上審核不涉及名稱變更, 故直接核准公司名稱
					cedb1001Dao.updateWhenL0100(prefixNo);
					//新增歷程3:已分文
					saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_3, receiveDate, receiveTime);
					//新增歷程5:已審核
					saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_5, receiveDate, receiveTime);
					//新增歷程7:發文登打完成
					saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_7, receiveDate, receiveTime);
					//新增歷程8:發文結案
					saveCedb1010(prefixNo, userId, PrefixConstants.PREFIX_STATUS_8, receiveDate, receiveTime);
					//如果有免繳的時候,把[網路流水號]換成[預查編號]
					generalityBusitemDao.updateTelixNoToPrefixNo(cedb1000.getPrefixNo(), cedb1000.getBanNo(), cedb1000.getTelixNo());
				}
			}
		}
	}

	/** 查詢一站式待收案件 */
	public List<String> readOsssCase(int limit) {
		List<String> results = new ArrayList<String>();
		//所有線上申辦文
		List<OssmApplFlow> flows = ossmApplFlowDao.doReadCaseByRownum(limit);
		if(null!=flows && !flows.isEmpty()) {
			for(OssmApplFlow flow : flows){
				results.add(flow.getTelixNo());
			}
		}
		return results;
	}

	/** 查詢 - 線上收文每次收文件數 */
	public Integer findReceiveLimit() {
		Integer limit = 99999;
		try {
			SystemCode c = systemCodeDao.findByPk(PrefixConstants.CODE_KIND_01, "ReceiveNum");
			if(null!=c) {
				limit = Integer.parseInt(c.getCodeName());
			}
		} catch (Exception e) {
			e.printStackTrace();
			limit = 99999;
		}
		return limit;
	}
	
	/**
	 * 組合areacode與addr兩各欄位構成完整的地址
	 */
	private String getAreaCodeAddrBulid(String areacode, String addr) {
		String prefect_addr = "";
		//使用AreaCodeOptionHelper轉換代碼變成縣市名稱
		if (areacode != null && areacode.length() > 0 && addr != null && addr.length() > 0) {
			prefect_addr = getAreaZoneName(areacode) + addr;
		} else if(addr != null && addr.length() > 0) {
			prefect_addr = addr;
		}
		return prefect_addr;
	}

	/**
	 * 透過 areaCode 查詢 areaName 並回傳
	 */
	private String getAreaZoneName(String areaCode) {
		try {
			Cedbc054 c054 = new Cedbc054();
			if (areaCode.length() == 2 && (areaCode.equals("63") || areaCode.equals("64"))) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "00000000");
				return c054 == null ? "" : c054.getAreaName();
			} else if (areaCode.length() == 5 && (areaCode.substring(0, 1).equals("63") || areaCode.substring(0, 1).equals("64"))) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "00000");
				return c054 == null ? "" : c054.getAreaName();
			} else if (areaCode.length() == 7) {
				c054 = cedbc054Dao.findByAreaCode(areaCode + "000");
				return c054 == null ? "" : c054.getAreaName();
			} else {
				c054 = cedbc054Dao.findByAreaCode(areaCode);
				return c054 == null ? "" : c054.getAreaName();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public PRE1001 generateOnlineCase(String telixNo) {
		if(logger.isInfoEnabled()) logger.info("[線上收文][generateOnlineCase][網路流水號:"+telixNo+"]");
		//查主檔
		OssmApplMain ossmApplMain = ossmApplMainDao.findByTelixNo(telixNo);
		if(null==ossmApplMain){
			if(logger.isInfoEnabled()) logger.info("[線上收文][generateOnlineCase][網路流水號:"+telixNo+"][查無一站式主檔OssmApplMain]");
			return null;
		}
		//查流程
		List<OssmApplFlow> ossmApplFlows = ossmApplFlowDao.findByTelixNoAndProcessNoEqBorI(telixNo);
		if(null==ossmApplFlows || ossmApplFlows.isEmpty()){
			if(logger.isInfoEnabled()) logger.info("[線上收文][generateOnlineCase][網路流水號:"+telixNo+"][查無一站式流程檔OssmApplFlow]");
			return null;
		}
		OssmApplFlow ossmApplFlow = ossmApplFlows.get(0);
		
		PRE1001 bean = new PRE1001();
		//網路流水號
		bean.setTelixNo(telixNo);
		//統編
		bean.setBanNo(Common.get(ossmApplMain.getBanNo()));
		//申請人身分ID
		bean.setApplyId(Common.get(ossmApplMain.getApplyId()));
		//申請人姓名
		bean.setApplyName(TcfiView.normalizeName(Common.get(ossmApplMain.getApplyName())));
		//公司地址
		String companyAddr = "";
		//如有英文地址，就取英文地址。
		String applyEngAddr = Common.get(ossmApplMain.getApplyEngAddr());
		if( !"".equals(applyEngAddr) )
			companyAddr = applyEngAddr;
		else {
			int applyAddrMaxLength = 150;
			// 先抓ApplyAddrComb
			String applyAddrComb = Common.get(ossmApplMain.getApplyAddrComb());
			if(!"".equals(Common.get(applyAddrComb))) {
				if( Common.get(applyAddrComb).length() > applyAddrMaxLength )
					applyAddrComb = Common.get(applyAddrComb).substring(0,applyAddrMaxLength);
				companyAddr = Common.get(applyAddrComb);
			}
			else {
				//沒填 ApplyAddrComb 的時候, 改抓 ApplyAddr
				String applyAddr = getAreaCodeAddrBulid(ossmApplMain.getApplyAreaCode(), ossmApplMain.getApplyAddr());
				if(!"".equals(Common.get(applyAddr))) {
					if( Common.get(applyAddr).length() > applyAddrMaxLength )
						applyAddr = Common.get(applyAddr).substring(0,applyAddrMaxLength);
					companyAddr = Common.get(applyAddr);
				}
			}
		}
		bean.setCompanyAddr(companyAddr);
		//代理人事務所所在地
		String attorAddr = Common.get(ossmApplMain.getAttorAddrComb());
		if (attorAddr == null || "".equals(attorAddr))
			attorAddr = getAreaCodeAddrBulid(Common.get(ossmApplMain.getAttorAreaCode()), Common.get(ossmApplMain.getAttorAddr()));
		bean.setAttorAddr(attorAddr);
		//代理人姓名
		bean.setAttorName(Common.get(ossmApplMain.getAttorName()));
		//代理人證書號碼
		bean.setAttorNo(Common.get(ossmApplMain.getAttorNo()));
		//代理人聯絡電話
		bean.setAttorTel(Common.get(ossmApplMain.getAttorTel()));
		//聯絡人地址
		String contactAddr = Common.get(ossmApplMain.getContactAddrComb());
		if (contactAddr == null || "".equals(contactAddr))
			contactAddr = getAreaCodeAddrBulid(Common.get(ossmApplMain.getContactAreaCode()), Common.get(ossmApplMain.getContactAddr()));
		bean.setContactAddr(contactAddr);
		//聯絡人行動電話
		bean.setContactCel(Common.get(ossmApplMain.getContactCel()));
		//聯絡人姓名
		bean.setContactName(Common.get(ossmApplMain.getContactName()));
		//聯絡人聯絡電話
		bean.setApplyTel(Common.get(ossmApplMain.getContactTel()));
		//領件方式 //2024/04/03 針對一站式的所有自取進入預查時轉為線上列印
		bean.setContactGetKind(ossmGetKindToPrefixGetKind(Common.get(ossmApplFlow.getGetKind()).replace("0", "")));
		//郵寄註記
		bean.setGetKindRemark("");
		//附件 -是否有預查表附件
		bean.setIsPrefixForm("N");
		//附件 -正副本別
		bean.setDocType("1");//1:預查表正本
		//附件-預查表編號
		bean.setPrefixFormNo("");
		//附件-是否有其他機關核准函附件
		bean.setIsOtherForm("N");
		//附件-是否有說明書附件
		bean.setIsSpec("N");
		//附件-是否有其他附件
		bean.setIsOtherSpec("N");
		//附件-其他附件註記
		bean.setOtherSpecRemark("");
		//預查種類
		String setup = "N";
		String changeName = "N";
		String changeItem = "N";
		//申辦類型
		String ossApplyType = Common.get(ossmApplMain.getApplyType());
		if ( ossmApplMain.getTelixNo().startsWith("OSC") ) {
			if(ossApplyType.startsWith("C1")) {
				setup = "Y";
			} else {
				if (PrefixConstants.OSSS_APPLY_TYPE_L1010.equals(ossApplyType)) {
					//公司所營事業變更預查(L1010)
					changeItem = "Y";
				} else if (PrefixConstants.OSSS_APPLY_TYPE_L1110.equals(ossApplyType)) {
					//公司名稱及所營事業變更預查(L1110)
					changeName = "Y";
					changeItem = "Y";
				} else if (PrefixConstants.OSSS_APPLY_TYPE_L1100.equals(ossApplyType)) {
					//公司名稱變更預查(L1100)
					changeName = "Y";
				} else if (PrefixConstants.OSSS_APPLY_TYPE_L0100.equals(ossApplyType)) {
					//線上審核(L0100)(不涉及名稱變更)
					changeItem = "Y";
				} else {
					//預設
					changeItem = "Y";
				}
			}
		} else if (ossmApplMain.getTelixNo().startsWith("OSS")) {
			if(ossApplyType.startsWith("S1")) {
				setup = "Y";
			} else {
				if (PrefixConstants.OSSS_APPLY_TYPE_O1000.equals(ossApplyType)) {
					//有限合夥名稱及所營事業變更預查(O1000)
					// QASC：11005310012 處理勾選名稱及所營業務變更問題
					List<OssmOrgName> nameList = ossmOrgNameDao.findByTelixNo(telixNo);
					if(null != nameList && !nameList.isEmpty()) {
						changeName = "Y";
					}
					List<OssmBussItem> itemList = ossmBussItemDao.findByTelixNo(telixNo);
					if(null != itemList && !itemList.isEmpty()) {
						changeItem = "Y";
					}
				}
			}
		}
		//預查種類 - 設立
		bean.setSetup(setup);
		//預查種類 - 名稱變更
		bean.setChangeName(changeName);
		//預查種類 - 所營變更
		bean.setChangeItem(changeItem);
		//是否為閉鎖性
		bean.setClosed(Common.get(ossmApplMain.getIfClosed()));
		//組織別
		bean.setOrgType(Common.get(ossmApplMain.getOrgType()));
		//公司名稱
		bean.setCompanyName(Common.get(ossmApplMain.getOrgName()));
		
		//為了取得國外匯款使用英文名稱
		String extRemitEname = "";
		OssmOrgChange ossmOrgChange = ossmOrgChangeDao.findByTelixNo(telixNo);
		if (ossmOrgChange != null) {
			extRemitEname = ossmOrgChange.getCmpyRemitEname();
		}
		bean.setExtRemitEname(extRemitEname);
		return bean;
	}
	
	// 2024/04/03 針對一站式需求，將原本自取改為線上列印
	private String ossmGetKindToPrefixGetKind(String ossmGetKind) {
		if ("1".equals(ossmGetKind)) {
			return "3";
		}
		return ossmGetKind;
	}

	private SystemCodeDao systemCodeDao;
	public SystemCodeDao getSystemCodeDao() {return systemCodeDao;}
	public void setSystemCodeDao(SystemCodeDao dao) {this.systemCodeDao = dao;}
	private OssmApplMainDao ossmApplMainDao;
	public OssmApplMainDao getOssmApplMainDao() {return ossmApplMainDao;}
	public void setOssmApplMainDao(OssmApplMainDao dao) {this.ossmApplMainDao = dao;}
	private OssmApplFlowDao ossmApplFlowDao;
	public OssmApplFlowDao getOssmApplFlowDao() {return ossmApplFlowDao;}
	public void setOssmApplFlowDao(OssmApplFlowDao dao) {this.ossmApplFlowDao = dao;}
	private OssmFeeMainDao ossmFeeMainDao;
	public OssmFeeMainDao getOssmFeeMainDao() {return ossmFeeMainDao;}
	public void setOssmFeeMainDao(OssmFeeMainDao dao) {this.ossmFeeMainDao = dao;}
	private Cedb1000Dao cedb1000Dao;
	public Cedb1000Dao getCedb1000Dao() {return cedb1000Dao;}
	public void setCedb1000Dao(Cedb1000Dao dao) {this.cedb1000Dao = dao;}
	private OssmOrgNameDao ossmOrgNameDao;
	public OssmOrgNameDao getOssmOrgNameDao() {return ossmOrgNameDao;}
	public void setOssmOrgNameDao(OssmOrgNameDao dao) {this.ossmOrgNameDao = dao;}
	private OssmBussItemDao ossmBussItemDao;
	public OssmBussItemDao getOssmBussItemDao() {return ossmBussItemDao;}
	public void setOssmBussItemDao(OssmBussItemDao dao) {this.ossmBussItemDao = dao;}
	private OssmOrgChangeDao ossmOrgChangeDao;
	public OssmOrgChangeDao getOssmOrgChangeDao() {return ossmOrgChangeDao;}
	public void setOssmOrgChangeDao(OssmOrgChangeDao dao) {this.ossmOrgChangeDao = dao;}
	private Cedbc054Dao cedbc054Dao;
	public Cedbc054Dao getCedbc054Dao() {return cedbc054Dao;}
	public void setCedbc054Dao(Cedbc054Dao dao) {this.cedbc054Dao = dao;}
	private Cedb1019Dao cedb1019Dao;
	public Cedb1019Dao getCedb1019Dao() {return cedb1019Dao;}
	public void setCedb1019Dao(Cedb1019Dao dao) {this.cedb1019Dao = dao;}
	private Cedb2000Dao cedb2000Dao;
	public Cedb2000Dao getCedb2000Dao() {return cedb2000Dao;}
	public void setCedb2000Dao(Cedb2000Dao dao) {this.cedb2000Dao = dao;}
	private Cedb1023Dao cedb1023Dao;
	public Cedb1023Dao getCedb1023Dao() {return cedb1023Dao;}
	public void setCedb1023Dao(Cedb1023Dao dao) {this.cedb1023Dao = dao;}
	private Cedb1022Dao cedb1022Dao;
	public Cedb1022Dao getCedb1022Dao() {return cedb1022Dao;}
	public void setCedb1022Dao(Cedb1022Dao dao) {this.cedb1022Dao = dao;}
	private Cedb1002Dao cedb1002Dao;
	public Cedb1002Dao getCedb1002Dao() {return cedb1002Dao;}
	public void setCedb1002Dao(Cedb1002Dao dao) {this.cedb1002Dao = dao;}
	private Cedb1001Dao cedb1001Dao;
	public Cedb1001Dao getCedb1001Dao() {return cedb1001Dao;}
	public void setCedb1001Dao(Cedb1001Dao dao) {this.cedb1001Dao = dao;}
	private Cedb2002Dao cedb2002Dao;
	public Cedb2002Dao getCedb2002Dao() {return cedb2002Dao;}
	public void setCedb2002Dao(Cedb2002Dao dao) {this.cedb2002Dao = dao;}
	private Eedb3100Dao eedb3100Dao;
	public Eedb3100Dao getEedb3100Dao() {return eedb3100Dao;}
	public void setEedb3100Dao(Eedb3100Dao dao) {this.eedb3100Dao = dao;}
	private Eedb3000Dao eedb3000Dao;
	public Eedb3000Dao getEedb3000Dao() {return eedb3000Dao;}
	public void setEedb3000Dao(Eedb3000Dao dao) {this.eedb3000Dao = dao;}
	private Eedb3300Dao eedb3300Dao;
	public Eedb3300Dao getEedb3300Dao() {return eedb3300Dao;}
	public void setEedb3300Dao(Eedb3300Dao dao) {this.eedb3300Dao = dao;}
	private Eedb1000Dao eedb1000Dao;
	public Eedb1000Dao getEedb1000Dao() {return eedb1000Dao;}
	public void setEedb1000Dao(Eedb1000Dao dao) {this.eedb1000Dao = dao;}
	public GeneralityBusitemDao generalityBusitemDao;
	public GeneralityBusitemDao getGeneralityBusitemDao() {return generalityBusitemDao;}
	public void setGeneralityBusitemDao(GeneralityBusitemDao dao) {this.generalityBusitemDao = dao;}
	private Cedb1010Dao cedb1010Dao;
	public Cedb1010Dao getCedb1010Dao() {return cedb1010Dao;}
	public void setCedb1010Dao(Cedb1010Dao dao) {this.cedb1010Dao = dao;}
	private FlowLogDao flowLogDao;
	public FlowLogDao getFlowLogDao() {return flowLogDao;}
	public void setFlowLogDao(FlowLogDao dao) {this.flowLogDao = dao;}
	private ReceiptNoSetupDAO receiptNoSetupDAO;
	public ReceiptNoSetupDAO getReceiptNoSetupDAO() {return receiptNoSetupDAO;}
	public void setReceiptNoSetupDAO(ReceiptNoSetupDAO dao) {this.receiptNoSetupDAO = dao;}
	private PrefixReceiptNoDAO prefixReceiptNoDAO;
	public PrefixReceiptNoDAO getPrefixReceiptNoDAO() {return prefixReceiptNoDAO;}
	public void setPrefixReceiptNoDAO(PrefixReceiptNoDAO dao) {this.prefixReceiptNoDAO = dao;}
	private LmsmBussMainDao lmsmBussMainDao;
	public LmsmBussMainDao getLmsmBussMainDao() {return lmsmBussMainDao;}
	public void setLmsmBussMainDao(LmsmBussMainDao dao) {this.lmsmBussMainDao = dao;}
	private LmsmBusiItemDao lmsmBusiItemDao;
	public LmsmBusiItemDao getLmsmBusiItemDao() {return lmsmBusiItemDao;}
	public void setLmsmBusiItemDao(LmsmBusiItemDao dao) {this.lmsmBusiItemDao = dao;}

}