package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.model.bo.CommonUser;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc000;

/*
程式目的：人工更改分文
程式代號：pre8005
撰寫日期：103.07.02
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
public class PRE8005 extends SuperBean {	
  
	private String q_type;	
	private String q_prefixStart;	// 核覆日期起
	private String q_prefixEnd;		// 核覆日期迄
	private String q_receiveStart;
	private String q_receiveEnd;
	private String q_assignStart;
	private String q_assignEnd;
  
	private String[] checkedPrefixNo;   // 被勾選的預查編號清單 + 他們在清單中的位置
	private String[] idNo ;            
	private String[] seqNo;

	// ----------------------------------getters and setters of local variable bellow ---------------------------
	public String getQ_type() {return checkGet(q_type);}
	public void setQ_type(String s) {q_type = checkSet(s);}
	public String getQ_prefixStart() {return checkGet(q_prefixStart);}
	public void setQ_prefixStart(String s) {q_prefixStart = checkSet(s);}
	public String getQ_prefixEnd() {return checkGet(q_prefixEnd);}
	public void setQ_prefixEnd(String s) {q_prefixEnd = checkSet(s);}
	public String getQ_receiveStart() {return checkGet(q_receiveStart);}
	public void setQ_receiveStart(String s) {q_receiveStart = checkSet(s);}
	public String getQ_receiveEnd() {return checkGet(q_receiveEnd);}
	public void setQ_receiveEnd(String s) {q_receiveEnd = checkSet(s);}
	public String getQ_assignStart() {return checkGet(q_assignStart);}
	public void setQ_assignStart(String s) {q_assignStart = checkSet(s);}
	public String getQ_assignEnd() {return checkGet(q_assignEnd);}
	public void setQ_assignEnd(String s) {q_assignEnd = checkSet(s);}
  
	public String[] getCheckedPrefixNo() {return checkedPrefixNo;}
	public void setCheckedPrefixNo(String[] s) {checkedPrefixNo = s;}
	public String[] getIdNo() {return idNo;}
	public void setIdNo(String[] s) {idNo = s;}
	public String[] getSeqNo() {return seqNo;}
	public void setSeqNo(String[] s) {seqNo = s;}
	// ----------------------------------------------------------------------------------------------------------

	// ----------------------------------function never used bellow----------------------------------------------
	public ArrayList<String[]> doQueryAll() throws Exception {
		ArrayList<String[]> arrList = new ArrayList<String[]>();
		List<Cedb1000> list1000 = null;
		
		if ("prefix".equals(getQ_type())){
			/* 輸入起號與迄號  -> 將起迄號之間的所有預查編號撈出
			 * 起號與迄號同       -> 只撈起號那一筆
			 * 只輸入起號            -> 只撈起號那一筆
			 * 只輸入迄號            -> 只撈迄號那一筆
			 */
			if(!"".equals(Common.get(getQ_prefixStart())) && !"".equals(Common.get(getQ_prefixEnd())))
				list1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ForPre8005(getQ_type(), getQ_prefixStart(), getQ_prefixEnd());
			else if(!"".equals(Common.get(getQ_prefixStart())))
				list1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ForPre8005(getQ_type(), getQ_prefixStart(), getQ_prefixStart());
			else if(!"".equals(Common.get(getQ_prefixEnd())))
				list1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ForPre8005(getQ_type(), getQ_prefixEnd(), getQ_prefixEnd());
		}else if ("receive".equals(getQ_type())){
			list1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ForPre8005(getQ_type(), getQ_receiveStart(), getQ_receiveEnd());
		}else if ("assign".equals(getQ_type())){
			list1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ForPre8005(getQ_type(), getQ_assignStart(), getQ_assignEnd());
		}
		
		if (list1000 != null && list1000.size() > 0){
			CommonUser user = ServiceGetter.getInstance().getAuthenticationService().getCurrentUser();
			Cedbc000 cedbc000 = ServiceGetter.getInstance().getApproveService().selectCedbc000ById(user.getUserId());
			SQLJob sqljob = new SQLJob("select id_no as code, staff_name as name from eicm.cedbc000 where ( staff_unit = ? and ( group_id = '11' or group_id = '17' )) or id_no = 'kangda' ");
			sqljob.addParameter(cedbc000.getStaffUnit());
			List<java.util.Map<String,Object>> stafflist = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			Cedb1000 o = null;
			String[] rowArray; 
			for (int i=0; i<list1000.size(); i++) {
				o = (Cedb1000)list1000.get(i);
				rowArray = new String[5];
				rowArray[0] = o.getPrefixNo();
				rowArray[1] = Common.get(o.getIdNo());
				rowArray[2] = getSelectionList(stafflist, Common.get(o.getIdNo()), Common.get(o.getPrefixNo()));
				rowArray[3] = Common.formatYYYMMDD(Common.get(o.getAssignDate()), 4);
				rowArray[4] = Common.formatHHMMSS(Common.get(o.getAssignTime()), 1);
				arrList.add(rowArray);
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}
	
	public void doUpdate() throws Exception{
		if ( !alternateList()) {
			throw new MoeaException("請先勾選想更改承辦人的預查案件編號");
		}else {
			  String userId = getLoginUserId();
			  ServiceGetter.getInstance().getPre8005Service().doSave(getCheckedPrefixNo(), getSeqNo(), getIdNo(), userId);
		}
	} // end doUpdate()	

	public Object doQueryOne() throws Exception{ return null ; }
	
	public void doCreate() throws Exception{}
	
	public void doDelete() throws Exception{}
	
	private String getSelectionList(List<java.util.Map<String,Object>> list, String staffName, String prefixNo) {
		StringBuffer tempSb= new StringBuffer();
		tempSb.append("<select name=\"idNo\" id=\"idNo\" onChange=\"doChangeStaff(this,"+prefixNo+")\">\n");
		// tempSb.append("<option value=''></option>\n");
		if (list!=null && list.size()>0){
			java.util.Map<String,Object> obj;
			String code = "", name = "";
      		for (int i=0; i<list.size(); i++) {
      			obj = (java.util.Map<String,Object>) list.get(i);
      			code = Common.get(obj.get("CODE"));
      			name = Common.get(obj.get("NAME"));
      			tempSb.append("<option value='").append(code).append("'");
      			if (code.equals(staffName)) tempSb.append(" selected ");
      			tempSb.append(">").append(name).append("</option>\n");
      		}
		}
		tempSb.append("</select>\n");
		return tempSb.toString();
	} // getSetectionList()
	
	public boolean alternateList() { 
		if ( getCheckedPrefixNo() == null || getCheckedPrefixNo().length <= 0 ) {
			return false;
		} // if
		seqNo = new String[getCheckedPrefixNo().length];
		for (int i = 0;i<getCheckedPrefixNo().length; i++) {
			seqNo[i] = getCheckedPrefixNo()[i].substring(9);
			checkedPrefixNo[i] = getCheckedPrefixNo()[i].substring(0,9);
		} 
		for (int i=0;i<getCheckedPrefixNo().length;i++) {
			try {
				Integer.parseInt(seqNo[i]);
			} catch (Exception e) {
				throw e;
			}
			//System.out.println("\"" + getCheckedPrefixNo()[i] + "\" + \"" +getIdNo()[temp]+"\"");
		}
		return true;
	} // alternaterList	
	  
	public static String getQuerylist(boolean primaryArray[], boolean displayArray[], String[] arrAlign,
	  		ArrayList<String[]> objList, String queryAllFlag, boolean withListNo, boolean linkArray[], String target,String noDataMsg,boolean checkHtml, 
	  		boolean defaultRow, int column, boolean disable, String checkboxName) {
		int i;
	  	boolean trFlag = false, targetFlag = false, even = false;
	  	String pk = "";
	  	int index = 0;
	  	StringBuilder sb = new StringBuilder();
	  	if (objList!=null && objList.size()>0) {
	  		String rowArray[]=new String[primaryArray.length];
	  		java.util.Iterator<String[]> it = objList.iterator();
				
	  		//boolean defaultRow = true;
	  		String defaultKey = "null";
	  		for(i=0;i<primaryArray.length;i++){
	  			if (primaryArray[i]) defaultKey = "";
	  		}

	  		while(it.hasNext()) {
	  			rowArray= it.next();
	  			String classTR="listTROdd", classTD = "listTDOdd";				
	  			if (even) {
	  				classTR = "listTREven";
	  				classTD = "listTDEven";
	  			}				
				
	  			pk = "";
	  			for(i=0;i<primaryArray.length;i++){			
	  				if (primaryArray[i]) pk+=Common.escapeReturnChar(rowArray[i]);
	  			}				
	  			StringBuilder v = new StringBuilder().append(defaultKey);
	  			for(i=0;i<primaryArray.length;i++){
	  				if (primaryArray[i]) {
	  					if (trFlag) {
	  						v.append(",'").append(Common.escapeReturnChar(rowArray[i])).append("'");
	  					} else {
	  						v.append("'").append(Common.escapeReturnChar(rowArray[i])).append("'");
	  						trFlag = true;
	  					}
	  				}
	  			}
	  			if (targetFlag==false && target!=null && !"".equals(Common.get(target))) {
	  				v.append(",'").append(target).append("'");
	  				targetFlag = true;
	  			}					
					
	  			//顯示TR
	  			if (linkArray!=null) {
	  				sb.append(" <tr class='highLight' >");
	  			} else {
	  				sb.append(" <tr id=\"").append("listContainerRow").append(pk).append("\"");
	  				sb.append(" class='").append(classTR).append("' onmouseover=\"this.className='listTRMouseover'\" onmouseout=\"this.className='").append(classTR).append("'\" onClick=\"listContainerRowClick('").append(pk).append("');\" >\n");	
	  			}
					
	  			String dc="";
	  			if(column >= 0 && column < displayArray.length){
	  				if(( disable && Common.get(rowArray[column]).length() > 0) ||
	  						(!disable && Common.get(rowArray[column]).equals(""))){
	  					dc = "disabled";
	  				}
	  			}
	  			//顯示TD	
	  			if (withListNo) sb.append(" <td class='listTD' >").append("<input class=\"field_Q\" type='checkbox' ").append(dc).append(" id=\"").append(checkboxName).append("\" name=\"").append(checkboxName).append("\" value=\"").append(Common.checkGet(rowArray[0])).append(index).append("\" ").append("></td>\n"); //sb.append(" <td class='").append(classTD).append("' style=\"text-align:right\">").append(counter).append("</td>\n");			
				targetFlag = false;
				for(i=0;i<displayArray.length;i++){
					if (displayArray[i]) {
						if (arrAlign!=null && arrAlign.length>0) {
							if ( i == 1 )
								sb.append(" <td class='").append(classTD).append("' id=\"" + v.toString().replaceAll("'", "") + "\" style=\"text-align:").append(arrAlign[i]).append("\">"); //.append(Common.get(rowArray[i])).append("</td>\n");
							else
								sb.append(" <td class='").append(classTD).append("'"  + " style=\"text-align:").append(arrAlign[i]).append("\">"); //.append(Common.get(rowArray[i])).append("</td>\n");
						} else {
							sb.append(" <td class='").append(classTD).append("' >");
						}
						if (linkArray!=null && linkArray[i]) {
							sb.append("<a href='#' class='sLink2' onClick=\"listContainerRowClick('").append(pk).append("');queryOne(").append(v).append(",").append(i).append(")\"");
							sb.append(">").append(checkHtml?Common.checkGet(rowArray[i]):Common.get(rowArray[i])).append("</a>");
						} else sb.append(checkHtml?Common.checkGet(rowArray[i]):Common.get(rowArray[i]));	
						sb.append("</td>\n");
					}
				}
				sb.append("</tr>\n");
				trFlag = false;
				even = !even;
				index++;
	  		}
	  	} else {
	  		if ("true".equals(queryAllFlag)) sb.append(" <tr class='highLight' ><td class='listTDOdd' colspan='100'>").append("".equals(Common.get(noDataMsg))?"查無資料，請您重新輸入查詢條件！":noDataMsg).append("</td></tr>");
	  	}
	  	return sb.toString();
	}
  
} // PPE8005