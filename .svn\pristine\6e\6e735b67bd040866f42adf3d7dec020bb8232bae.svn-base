<!DOCTYPE html>
<%
/**
程式目的：撤回退費DI檔下載
程式代號：pre3014
程式日期：1130510
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3014" />
</jsp:include>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3014">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>     
<%
if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE3014)obj.doQueryOne();
}else if ("downloadDiFile".equals(obj.getState())) {
	java.io.File report = null;
	String fileType = "PRE3014.di";
	report = obj.downloadDiFile();
	
	if(null!=report){
		obj.outputFile(response, report, fileType);
		out.clear();
		out = pageContext.pushBody();
	}
}
%>
<html>
<head>
<meta http-equiv="x-ua-compatible" content="IE=Edge"/> 
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">

function checkField(){
	var alertStr="";
	if(form1.state.value == "queryOne"){
		alertStr += checkEmpty(form1.q_prefixNo,"預查編號");
	}else if(form1.state.value=="downloadDiFile"){
		if($('#prefixNo').val() == "")	alertStr += "請先執行查詢";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	<!-- beforeSubmit(); -->
	return true;
}

function doSomeCheck(){
	if (form1.prefixNo.value == "") {
		alert('下載前請先執行查詢!');
		return false;
	}
	
	if (form1.returnChNo.value == "") {
		alert('下載前請先輸入支票號碼!');
		return false;
	}
	
	var prefixNo = form1.prefixNo.value;
	var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre3005.jsp?prefixNo='+prefixNo);
	if ( x == 'ok'  )
		return true;
	else 
		document.getElementById("ERRMSG").innerHTML = x;
	return false;
}

$(document).ready(function() {
	if($('#prefixNo').val() != ""){
		//不開放選，因為也沒存這欄位
		//$("input[name='getKind']").each(function(){$(this).removeAttr("disabled");});
		$('#returnChNo').removeAttr("disabled");
		if ($('#noPayMark').val().startsWith("已免繳一次")) {
			alert("本案為免繳規費預查案件, 僅可撤件不需退費");
			$('#processType').val("撤件");
		}
	}
	
	$('#doQueryOne').click(function(){
		$('#state').val("queryOne");
	});
	$('#doDownloadDiFile').click(function(){
		if(doSomeCheck()){
			$('#state').val("downloadDiFile");
			form1.submit();
		}
	});
	
});

</script>
</head>
<body topmargin="0" onLoad="showErrorMsg('<%=obj.getPopErrorMsg()%>')">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE3014'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
	<table width="100%">
		<tr>
			<td style="text-align:left" width="70%">
				<input class="toolbar_default" type="submit" id="doQueryOne" name="doQueryOne" value="查　詢" onclick="">&nbsp;
				<input class="toolbar_default" type="button" id="doDownloadDiFile" name="doDownloadDiFile" value="下載電子交換檔(.di)">&nbsp;
			</td>
			<td class="td_form_white" width="30%" align="right">
				<c:import url="../common/shortcut.jsp">
					<c:param name="functions" value='PRE4001,PRE3008,PRE3013'/>
				</c:import>
	    	</td>
		</tr>
	</table>
</td></tr>

<!-- Query Area  -->

<tr><td class="bg" >
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" style="width:200px">預查編號：</td>
			<td class="td_form_white" colspan="3"> 
				<input class="field_Q" type="text" id="q_prefixNo" name="q_prefixNo" size="10" maxlength="9" value="<%=obj.getQ_prefixNo()%>">
			</td>
		</tr>
	</table>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- 資料列表 -->

<tr><td>
	<table cellpadding=0 cedlsapcing=0>
      <tr>
        <td nowrap id="t1" class="tab_border1" width = 200>公文資料</td>
      </tr>
    </table>
    <table class="table_form" width="100%" cellpadding="0" cellspacing="0">
    	<tr>
			<td class="td_form" style="width:200px">公文資料預查編號：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input class="field_RO" id="prefixNo" name="prefixNo" value="<%=obj.getPrefixNo()%>" disabled = "disabled">
				</font>
			</td>
		</tr>
		<tr>
			<td class="td_form" >作業型別：</td>
        	<td class="td_form_white" colspan="3">
        		<font color="black">
          		<input class="field_RO" type="text" id="processType" name="processType" value="<%=obj.getProcessType()%>">
          		</font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >退費日期：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input class="field_RO" type="text" id="returnDate" name="returnDate" value="<%=obj.getReturnDate()%>">
				</font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >公司/有限合夥名稱：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input class="field_RO cmex" type="text" id="companyName" name="companyName" value="<%=obj.getCompanyName()%>" style="width: 50%;">
				</font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >申請人：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input class="field_RO" type="text" id="applyName" name="applyName" value="<%=obj.getApplyName()%>">
				</font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >承辦人：</td>
			<td class="td_form_white" colspan="3"> 
				<font color="black">
				<input class="field_RO" type="text" id="staffName" name="staffName" value="<%=obj.getStaffName()%>">
				</font>
        	</td>
		</tr>
		<tr>
			<td class="td_form" >退費支票號：</td>
			<td class="td_form_white" > 
				<font color="black">
				<input class="field_Q" type="text" id="returnChNo" name=returnChNo size="12" maxlength="11" value="<%=obj.getReturnChNo()%>" disabled="disabled">
				</font>
        	</td>
		</tr>
    </table>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- Form area -->

<!-- hidden area -->
<tr><td>
	<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
	<input type="hidden" id="noPayMark" name="noPayMark" value="<%=obj.getNoPayMark()%>">
	<input type="hidden" id="amount" name="amount" value="<%=obj.getAmount()%>">
	<input type="hidden" id="sendDate" name="sendDate" value="<%=obj.getSendDate()%>">
	<input type="hidden" id="staffBranchNo" name="staffBranchNo" value="<%=obj.getStaffBranchNo()%>">
	<input type="hidden" id="staffEmail" name="staffEmail" value="<%=obj.getStaffEmail()%>">
	<input type="hidden" id="attorName" name="attorName" value="<%=obj.getAttorName()%>">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="N" />
		<jsp:param name="btnQueryAll" value="N" />
		<jsp:param name="btnUpdate" value="N" />
		<jsp:param name="btnDelete" value="N" />
		<jsp:param name="btnClear" value="N" />
		<jsp:param name="btnConfirm" value="N" />
		<jsp:param name="btnListHidden" value="N" />
		<jsp:param name="btnPreview" value="N" />
		<jsp:param name="btnCancel" value="N" />
		<jsp:param name="btnListPrint" value="N" />
	</jsp:include>
</td></tr>

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>