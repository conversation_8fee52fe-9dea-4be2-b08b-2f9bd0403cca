package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.CmpyMemoInfo;

public class CmpyMemoInfoDao extends BaseDaoJdbc implements
		RowMapper<CmpyMemoInfo> {

	private static String sql_find = "SELECT * FROM CMPY_MEMO_INFO ";
	public java.util.List<CmpyMemoInfo> query(String rcvNo, String companyName, String reserveDateS, String reserveDateE) {
		// if(obj == null) return null;
		SQLJob sqljob = new SQLJob(sql_find);
		if (!"".equals(Common.get(rcvNo))){
			sqljob.appendSQLCondition(" RCV_NO = ? ");
			sqljob.addParameter(rcvNo);
		}
		if (!"".equals(Common.get(companyName))){
			sqljob.appendSQLCondition(" COMPANY_NAME like ? ");
			sqljob.addLikeParameter(companyName);
		}
		if (!"".equals(Common.get(reserveDateS))){
			sqljob.appendSQLCondition(" RESERVE_DATE >= ? ");
			sqljob.addParameter(reserveDateS);
		}
		if (!"".equals(Common.get(reserveDateE))){
			sqljob.appendSQLCondition(" RESERVE_DATE <= ? ");
			sqljob.addParameter(reserveDateE);
		}
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (java.util.List<CmpyMemoInfo>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static String sql_update = "UPDATE CMPY_MEMO_INFO SET RCV_NO = ?, COMPANY_NAME = ?, RESERVE_DATE = ?, ENABLE = ?, UPDATE_DATE = ?, UPDATE_TIME = ?, UPDATE_USER = ? "
			+ "WHERE RCV_NO = ?";
	public void update(CmpyMemoInfo obj) {
		if("".equals(Common.get(obj.getId())))	return;
		SQLJob sqljob = new SQLJob(sql_update);
		sqljob.addParameter(obj.getRcvNo());
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addParameter(obj.getReserveDate());
		sqljob.addParameter(obj.getEnable());
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addParameter(obj.getUpdateUser());
		sqljob.addParameter(obj.getId());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
			,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR}
		);
	}
	
	private static String sql_insert = "INSERT INTO CMPY_MEMO_INFO (RCV_NO, COMPANY_NAME, RESERVE_DATE, ENABLE, UPDATE_DATE, UPDATE_TIME, UPDATE_USER) "
			+ "VALUES (?, ?, ?, ?, ?, ?, ?) ";
	public void insert(CmpyMemoInfo obj) {
		if(obj == null)	return;
		SQLJob sqljob = new SQLJob(sql_insert);
		sqljob.addParameter(obj.getRcvNo());
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addParameter(obj.getReserveDate());
		sqljob.addParameter(obj.getEnable());
		sqljob.addParameter(obj.getUpdateDate());
		sqljob.addParameter(obj.getUpdateTime());
		sqljob.addParameter(obj.getUpdateUser());
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray()
			,new int[]{
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR,
				java.sql.Types.VARCHAR}
		);
	}

	private static String sql_deleteByRcvNo = "DELETE CMPY_MEMO_INFO WHERE RCV_NO = ?  ";
	public void delete(String rcvNo) {
		if (rcvNo == null || "".equals(rcvNo))	return;
		SQLJob sqljob = new SQLJob(sql_deleteByRcvNo);
		sqljob.addParameter(rcvNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray());
	}

	@Override
	public CmpyMemoInfo mapRow(ResultSet rs, int idx) throws SQLException {
		CmpyMemoInfo obj = null;
		if (null != rs) {
			obj = new CmpyMemoInfo();
			obj.setRcvNo(rs.getString("RCV_NO"));
			obj.setCompanyName(rs.getString("COMPANY_NAME"));
			obj.setReserveDate(rs.getString("RESERVE_DATE"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setUpdateDate(rs.getString("UPDATE_DATE"));
			obj.setUpdateTime(rs.getString("UPDATE_TIME"));
		}
		return obj;
	}

}
