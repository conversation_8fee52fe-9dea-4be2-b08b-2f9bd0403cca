package com.kangdainfo.tcfi.view.common;

import java.util.ArrayList;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.FunctionMenu;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 視窗TITLE
 *
 */
public class FunctionTitle extends SuperBean {

	private String function;

	public String getFunctionTitle() {
		String result = "";
		if(!"".equals(function)) {
			FunctionMenu f = ServiceGetter.getInstance().getFunctionMenuLoader().getFunctionMenuByCode(function);
			if(null!=f) {
				result = CommonStringUtils.append(f.getTitle(),"(",f.getCode(),")");
			} else {
				result = function;
			}
		}
		return result;
	}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {return null;}
	@Override
	public Object doQueryOne() throws Exception {return this;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getFunction() {return checkGet(function);}
	public void setFunction(String s) {this.function = checkSet(s);}

}