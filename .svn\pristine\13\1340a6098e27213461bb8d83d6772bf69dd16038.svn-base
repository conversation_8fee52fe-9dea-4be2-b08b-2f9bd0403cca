<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
try {
	//查詢發文待登打件數
	StringBuffer sql = new StringBuffer();
	sql.append(" SELECT COUNT(1) AS COUNT");
	sql.append(" FROM CEDB1000 A");
	sql.append(" WHERE A.APPROVE_RESULT <> 'A'");//已審核(APPROVE_RESULT IN 'Y','N')
	sql.append(" AND (A.CLOSE_DATE IS NULL OR A.CLOSE_DATE='')");//未結案
	sql.append(" AND A.APPROVE_DATE IS NOT NULL");//已審核
	sql.append(" AND A.PREFIX_STATUS NOT IN ('A','E')");//不為撤回(A)或撤回退費(E)
	sql.append(" AND 0=(SELECT COUNT(1) FROM CEDB1010 WHERE PREFIX_NO=A.PREFIX_NO AND PROCESS_STATUS='7')");//流程紀錄不含發文登打完成(7)
	sql.append(" ORDER BY A.PREFIX_NO");
	//System.out.println(sql.toString());
	java.util.List<java.util.Map<String,Object>> datas = com.kangdainfo.ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sql.toString());
	if (null!=datas && !datas.isEmpty()) {
		out.write(com.kangdainfo.common.util.Common.get(datas.get(0).get("COUNT")));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>