<!DOCTYPE html>
<!--
程式目的：群組資料及權限維護
程式代號：PRE9002
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9002">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9002" />
</jsp:include>
<%
	
String sys_id = "-11"; 

if("queryOne".equals(obj.getState())){
	obj = (com.kangdainfo.tcfi.view.pre.PRE9002)obj.queryOne();
}else if("update".equals(obj.getState())){
	obj.update();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript" src="../../js/dtree.js"></script>
<link rel="stylesheet" href="../../js/dtree.css" type="text/css" />
<style>
.bg {
	background-color: #FBFFFD;
  	border: 1px solid #EDEDED;
  	color: #000000;
  	cursor: default;
  	left: 0px;
  	margin: 1px;
  	padding: 2px 6px 0px 6px;
  	top: 0px;
  	width:175px;
  	height:400px;
}

.root{
	background-color: #FFFFFF;
}

.folder{
	background-color: #FFFFFF;
}

.program{
	background-color: #BBBBFF;
  	font-size: 12px;
}

.auth1{
  	background-color: #AAFFAA;
  	font-size: 12px;
}

.auth2{
  	background-color: #FFAAAA;
  	font-size: 12px;
}

.showAll{
  	background-color: #FFFFF;
  	font-size: 12px;
}
</style>
<script type="text/javascript">
var insertDefault = new Array(
	new Array("startDate", getToday()),
	new Array("endDate", "<%=Datetime.getDateAdd("m",1,Datetime.getYYYMMDD())%>"),
	new Array("enable", "Y")
);

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){

	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}
function queryOne(id){}

function checkUrl(surl){
	form1.action = surl;
	form1.submit();
}

function init(){
}

function checkListIsSelect(buttonName){
	var alertStr="";
	if(buttonName=="add" && !d.hasElementChecked()){
		alertStr += "[你必須先選擇左邊功能選單某一節點或多重節點]";
	}
	if(buttonName=="remove" && !s.hasElementChecked()){
		alertStr += "[你必須先選擇右邊功能選單某一節點或多重節點]";
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }

	if(buttonName=="add"){
		document.all("optype").value = "add";
	}else{
		document.all("optype").value = "remove";
	}
	form1.state.value = "update";
}
</script>
</head>

<body topmargin="0" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">
	<input type="hidden" name="id" value="<%=obj.getId()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
	<input type="hidden" name="codeName" value="<%=obj.getCodeName()%>">
	<input type="hidden" name="optype">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9002'/>
</c:import>

<table cellpadding=0 cellspacing=0 valign="top">
	<tr>
		<td nowrap ID=t1 CLASS="tab_border2" width="100" height="25"><a href="#" onClick="return checkUrl('pre9002.jsp');">身分別資料</a></td>
		<td nowrap ID=t2 CLASS="tab_border1" width="100">身分別權限</td>
	</tr>
	<tr>
		<td nowrap class="tab_line1"></td>
		<td nowrap class="tab_line2"></td>	
	</tr>
</table>

<table border="1" width="100%">
	<tr><td nowrap>
		<table class="table_form" width="100%" height="25" border="0">
    		<tr>	
		    	<td nowrap class="td_form" width="100">角色權限：</td>
		        <td nowrap class="td_form_white"><%=obj.getCodeName()%></td>
		     </tr>
		</table>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
	</td></tr>
	<tr><td nowrap>
		<table border="1" width="100%">
			<tr><td nowrap width="45%" valign="top">
					<div id="d" class="dtree">
						<a href="javascript: d.openAll();">
							<%=org.apache.commons.lang.StringEscapeUtils.escapeHtml(application.getInitParameter("open_all"))%></a> | <a href="javascript: d.closeAll();"><%=org.apache.commons.lang.StringEscapeUtils.escapeHtml(application.getInitParameter("close_all"))%>
						</a><br/><br/>
						<script type="text/javascript">
							var d = new dTree('d','../../images/');
							d.config.folderLinks=true;
							d.config.useCookies=false;
							d.config.useIcons = false;
							d.config.objForm =  document.form1;
							d.config.checkboxPrefix = "d";
							d.config.checkParent = true;
							<%=obj.buildCheckBoxTree("d", "請選擇要授權的項目", "auth", "d", "d.checkBoxClick", sys_id, "", true, false, false)%>	
							document.write(d);
						</script>
					</div>
				</td>
				<td nowrap align="center" valign="top">
					<table border="0">
					 	<tr><td nowrap align="center" valign="middle">
								<input class="toolbar_default" type="submit" name="add"    value="加入權限" onClick="return checkListIsSelect(this.name)">
			        			<br><br><br>
			        	 	
								<input class="toolbar_default" type="submit" name="remove" value="移除權限" onClick="return checkListIsSelect(this.name)">
								<br><br><br>
							</td>
						</tr>
					</table>
				</td>
				<td nowrap width="45%" valign="top">
					<div id="s" class="dtree">
						<a href="javascript: s.openAll();">
							<%=org.apache.commons.lang.StringEscapeUtils.escapeHtml(application.getInitParameter("open_all"))%></a> | <a href="javascript: s.closeAll();"><%=org.apache.commons.lang.StringEscapeUtils.escapeHtml(application.getInitParameter("close_all"))%>
						</a><br /><br />
						<script type="text/javascript" >
							var s = new dTree('s','../../images/');
							s.config.folderLinks=true;
							s.config.useCookies=false;
							s.config.useIcons = false;
							s.config.objForm =  document.form1;
							s.config.checkboxPrefix = "s";
							s.config.checkParent = false;
							<%=obj.buildCheckBoxTree("s", "已授權項目", "authed", "s", "s.checkBoxClick", sys_id, obj.getId(), false, false, false)%>		
							document.write(s);
						</script>
					</div>&nbsp;
				</td>
			</tr>
		</table>
	</td></tr>
</table>

</form>
</body>
</html>