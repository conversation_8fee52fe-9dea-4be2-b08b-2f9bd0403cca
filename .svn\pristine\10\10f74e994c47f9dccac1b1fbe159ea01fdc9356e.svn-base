package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmmMoeaicApprove;

public class CsmmMoeaicApproveDao extends BaseDaoJdbc implements RowMapper<CsmmMoeaicApprove> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM ICMS.CSMM_MOEAIC_APPROVE WHERE PREFIX_NO = ?";
	public List<CsmmMoeaicApprove> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<CsmmMoeaicApprove>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmmMoeaicApprove mapRow(ResultSet rs, int idx) throws SQLException {
		CsmmMoeaicApprove obj = null;
		if(null!=rs) {
			obj = new CsmmMoeaicApprove();
			obj.setReceiveNo(rs.getString("RECEIVE_NO"));
			obj.setPrefixNo(rs.getString("PREFIX_NO"));
			obj.setCmpyName(rs.getString("CMPY_NAME"));
			obj.setRespDate(rs.getString("RESP_DATE"));
			obj.setCaseCode(rs.getString("CASE_CODE"));
			obj.setRcvNo(rs.getString("RCV_NO"));
			obj.setRecDate(rs.getString("REC_DATE"));
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setCaseDesc(rs.getString("CASE_DESC"));
			obj.setCmpyType(rs.getString("CMPY_TYPE"));
			obj.setBanNo(rs.getString("BAN_NO"));
			obj.setPlnAmount(rs.getInt("PLN_AMOUNT"));
			obj.setImpDate(rs.getString("IMP_DATE"));
			obj.setTbpk(rs.getString("TBPK"));
			obj.setSendword(rs.getString("SENDWORD"));
		}
		return obj;
	}

}