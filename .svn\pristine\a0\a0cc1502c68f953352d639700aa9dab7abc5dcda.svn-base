<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<%@ page import="com.kangdainfo.common.util.SQLJob" %>
<%@ page import="com.kangdainfo.ServiceGetter" %>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String prefixNo = "";
try {
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL("select PREFIX_NO from Cedb1019 where year_no = trim(to_char((to_number(to_char(sysdate,'yyyy'))-1911),'099'))");
	//System.out.println(sqljob);
	List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
	if (null!=datas && !datas.isEmpty()) {
		prefixNo = (String)datas.get(0).get("PREFIX_NO");
	}
} catch (Exception e) {
	e.printStackTrace();
}
out.write(prefixNo);
%>