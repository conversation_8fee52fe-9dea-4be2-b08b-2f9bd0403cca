package com.kangdainfo.tcfi.view.pre;

/*
程式目的：馬上辦更改申請人清冊 
程式代號：pre4012
撰寫日期：103.05.05
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.tcfi.exception.MoeaException;
import com.kangdainfo.util.report.JasperReportMaker;


public class PRE4012 extends SuperBean {	
  
 
  private String q_DateStart ;  // 馬上辦日期起
  private String q_DateEnd ;    // 馬上辦日期迄
  
  private String prefixNo ;         // 預查編號
  private String telixNo ;          // 電子收文流水號
  private String applyName ;        // 申請人姓名
 
  
  
  // ----------------------------------getters and setters of local variable bellow ---------------------------
  
  public String getQ_DateStart() {return checkGet(q_DateStart);}
  public void setQ_DateStart(String s) {q_DateStart = checkSet(s);}
  public String getQ_DateEnd() {return checkGet(q_DateEnd);}
  public void setQ_DateEnd(String s) {q_DateEnd = checkSet(s);}
  
  public String getPrefixNo() {return checkGet(prefixNo);}
  public void setPrefixNo(String s) {prefixNo = checkSet(s);}
  public String getTelixNo() {return checkGet(telixNo);}
  public void setTelixNo(String s) {telixNo = checkSet(s);}
  public String getApplyName() {return checkGet(applyName);}
  public void setApplyName(String s) {applyName = checkSet(s);}
    
  // ----------------------------------------------------------------------------------------------------------
  
  
  
  // ----------------------------------function never used bellow----------------------------------------------
  
  public void doCreate() throws Exception{	  
  } // end doCreate()
	  
  public void doUpdate() throws Exception{
  } // end doUpdate()		
	  
  public void doDelete() throws Exception{			
  } // end doDelete()	
	  
  public Object doQueryOne() throws Exception{ 
    return null ;
  } // end doQueryOne()

  @SuppressWarnings("rawtypes")
  public ArrayList doQueryAll() {
	  try {   
   	      if(logger.isInfoEnabled()) logger.info(doAppendSqljob(getQ_DateStart(), getQ_DateEnd()));	
   	      List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_DateStart(), getQ_DateEnd() ));
   	      if ( rs == null || rs.size() <= 0 ) {
   	    	  setErrorMsg("查無資料，請變更查詢日期");
   		      throw new MoeaException( "查無資料，請變更查詢日期" ) ;
   	      } // end if
   	      else { 
   	    	  ArrayList<String[]> dataList = new ArrayList<String[]>();
   	    	  int i = 0;
   	    	  while( i < rs.size()){
   	    		  String[] rowArray = new String[3] ;
   	    		  rowArray[0] = Common.get(rs.get(i).get("telix_no"));
   	    		  rowArray[1] = Common.get(rs.get(i).get("prefix_no"));
   	    		  rowArray[2] = Common.get(rs.get(i).get("apply_name"));
   	    		  dataList.add(rowArray);		  
   	    		  i++;
   	    	  } // end while
		
   	    	  setErrorMsg("查詢成功！");
   	    	  return dataList;
   	      } // end else 
	  } // try 
	  catch( MoeaException e ) {
		  e.printStackTrace();
		  setErrorMsg(e.getMessage());
		  return null;
	  } // catch
  } // doQueryAll()

  // -----------------------------------------------------------------------------------------------------------
    
  public String DateFormat( String inputDate ) {
	  String tempDate = "" ;
	  String year = inputDate.substring(0, 3) ;
	  String month = inputDate.substring(4, 6) ;
	  String day = inputDate.substring(7) ;
	  tempDate = tempDate.concat( year + "年" + month + "月" + day + "日" ) ;
	  return tempDate ;
  } // DateFormat()
  
  public String TimeFormat( String inputTime ) {
	  String tempTime = "" ;
	  String hour = inputTime.substring(0, 2) ;
	  String minute = inputTime.substring(2, 4) ;
	  String second = inputTime.substring(4) ;
	  tempTime = tempTime.concat(hour + "點" + minute + "分" + second + "秒") ;
	  return tempTime ;
  } // TimeFormat()
  
  // ---------------------------------------------------------------------------------------
  
  public static SQLJob doAppendSqljob( String dateStart, String dateEnd ) {
	  SQLJob sqljob = new SQLJob() ;
	  sqljob.appendSQL( "SELECT PREFIX_NO,TELIX_NO,APPLY_NAME" ) ;
	  sqljob.appendSQL( "FROM CEDB1000 WHERE PREFIX_NO IN" ) ;
	  sqljob.appendSQL( "(SELECT CEDB1010.PREFIX_NO FROM CEDB1010 JOIN EEDB1000 ON CEDB1010.PREFIX_NO = EEDB1000.PREFIX_NO" );
	  sqljob.appendSQL( "WHERE CEDB1010.PROCESS_STATUS = 'C' AND COME_FROM = 'C'" ) ;
	  sqljob.appendSQL( "AND PROCESS_DATE BETWEEN '" + dateStart + "'AND'" + dateEnd + "' )" ) ; 
	  sqljob.appendSQL( "ORDER BY TELIX_NO") ; 
	  return sqljob ;
  } // doAppendSqljob()
  
	public File doPrintPdf() throws Exception {  
		File report = null ;
		try {
			System.out.println(Datetime.getHHMMSS());
			String jasperPath = ServiceGetter.getInstance().getWebContextInfo().getRealPath("tcfi/report/pre4012.jasper");
			Map<String, Object> parameters = new HashMap<String,Object>();
			String printDate = Datetime.getRocDateFromYYYYMMDD(Datetime.getCurrentDate("yyyy/MM/dd" ));//列印時間
			String printTime = Datetime.getHHMMSS() ;
	  
			printDate = DateFormat( printDate ) ;
			printTime = TimeFormat( printTime ) ;
			parameters.put("printDate", printDate);
			parameters.put("printTime", printTime);
	  
			String year = getQ_DateStart().substring(0, 3) ;
			String month = getQ_DateStart().substring(3, 5) ;
			String day = getQ_DateStart().substring(5) ;
			String yearE = getQ_DateEnd().substring(0, 3) ;
			String monthE = getQ_DateEnd().substring(3, 5) ;
			String dayE = getQ_DateEnd().substring(5) ;
			parameters.put("dateStart", year + "年" + month +"月" + day + "日")  ;
			parameters.put("dateEnd", yearE + "年" + monthE + "月" + dayE + "日"  ) ;
	    
			if(logger.isInfoEnabled()) logger.info(doAppendSqljob(getQ_DateStart(), getQ_DateEnd() ) );	
			List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob( getQ_DateStart(), getQ_DateEnd() ));
			if ( rs == null || rs.size() <= 0 )
				setErrorMsg( "查無資料，請變更查詢日期" );
			else {
				ArrayList<PRE4012> dataList = new ArrayList<PRE4012>();
				int i = 0;
				Map<String,Object> temp = null;
				PRE4012 data = null;
				while( i < rs.size()){
					data = new PRE4012();
				    temp = rs.get(i);
				    data.setTelixNo(Common.get(temp.get("TELIX_NO"))) ;
				    data.setPrefixNo(Common.get(temp.get("PREFIX_NO")));
				    data.setApplyName(Common.get(temp.get("APPLY_NAME")));
				    dataList.add(data);
				    i++;
				}
	    
				report = JasperReportMaker.makePdfReport(dataList, parameters, jasperPath);
				System.out.println(Datetime.getHHMMSS());
			}
		}catch( Exception e ) {
			e.printStackTrace();
			if (e.getMessage()!=null && e.getMessage().length()<200) setErrorMsg(Common.escapeJavaScript(e.getMessage()));
			else setErrorMsg("無法產製報表!若問題持續,請洽詢系統管理者或相關承辦人員！");
		}
		return report;
	}
	
	public static String checkForjsp( String dateStart, String dateEnd ) {
		  List<Map<String,Object>> rs = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(doAppendSqljob(dateStart, dateEnd));
		  if ( rs == null || rs.size() <= 0 )
			  return "查無資料，請變更查詢條件！";
		  else
			  return "ok";
	} 

}