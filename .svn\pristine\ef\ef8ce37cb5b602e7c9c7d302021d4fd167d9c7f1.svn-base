package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.Datetime;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.util.lang.CommonStringUtils;

/**
 * 案件異動紀錄查詢
 *
 */
public class PRE3007 extends SuperBean {
	//查詢條件
	/** 預查編號 */
	private String q_prefixNo;
	/** 統一編號 */
	private String q_banNo;
	/** 申請人身分ID */
	private String q_applyId;
	/** 申請人姓名 */
	private String q_applyName;
	/** 公司名稱 */
	private String q_companyName;
	//資料鍵值
	private String[] prefixNos;
	//目前的鍵值
	private String current;
	
	private static final String COMPOSITE_KEY = "*";

	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.PREFIX_NO");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(" ,nvl(");
		sqljob.appendSQL("  (select code_name from system_code where code_kind='13'");
		sqljob.appendSQL("     and code=(select change_type from cedb1023 where prefix_no=a.prefix_no)");
		sqljob.appendSQL("  )");
		sqljob.appendSQL("  ,decode(a.apply_kind,'1','設立','2','變更','') ) as CHANGE_TYPE");
		sqljob.appendSQL(",A.APPROVE_RESULT");
		sqljob.appendSQL(",A.COMPANY_NAME");
		sqljob.appendSQL(",A.RESERVE_DATE");
		sqljob.appendSQL(",A.COMPANY_STUS");
		sqljob.appendSQL(",A.RECEIVE_DATE");
		sqljob.appendSQL(",A.STAFF_NAME");
		sqljob.appendSQL(",A.UPDATE_ID_NO");
		sqljob.appendSQL(",A.UPDATE_DATE");
		sqljob.appendSQL(",A.UPDATE_TIME");
		sqljob.appendSQL("FROM CEDB1006 A");
		sqljob.appendSQL("WHERE 1=1");
		if( !"".equals(getQ_prefixNo()) ) {
			sqljob.appendSQL("AND (A.PREFIX_NO LIKE ?)");
			sqljob.addSuffixLikeParameter(getQ_prefixNo());
		}
		if( !"".equals(getQ_banNo()) ) {
			sqljob.appendSQL("AND (A.BAN_NO = ?)");
			sqljob.addParameter(getQ_banNo());
		}
		if( !"".equals(getQ_applyId()) ) {
			sqljob.appendSQL("AND (A.APPLY_ID = ?)");
			sqljob.addParameter(getQ_applyId().toUpperCase());
		}
		if( !"".equals(getQ_applyName()) ) {
			sqljob.appendSQL("AND (A.APPLY_NAME = ?)");
			sqljob.addParameter(getQ_applyName());
		}
		if( !"".equals(getQ_companyName()) ) {
			sqljob.appendSQL("AND ( A.COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%"));
			//需同步查詢CEDB1001
			//sqljob.appendSQL("OR A.PREFIX_NO IN ( SELECT PREFIX_NO FROM CEDB1001 WHERE COMPANY_NAME LIKE "+Common.sqlChar(getQ_companyName()+"%")+" )");
			//改用Lucene查詢
			List<String> tempPrefixNos = ServiceGetter.getInstance().getIndexSearchService().searchPrefixNos(getQ_companyName());
			if(null!=tempPrefixNos && !tempPrefixNos.isEmpty()) {
				sqljob.appendSQL("OR A.PREFIX_NO IN (");
				boolean isFirst = true;
				for(String tempPrefixNo : tempPrefixNos) {
					if(!isFirst) sqljob.appendSQL(",");
					sqljob.appendSQL("'"+tempPrefixNo+"'");
					isFirst = false;
				}
				sqljob.appendSQL(")");
			}
			sqljob.appendSQL(")");
		}
		sqljob.appendSQL("ORDER BY A.PREFIX_NO, A.UPDATE_DATE, A.UPDATE_TIME");
		java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<Map<String,Object>> it = objList.iterator();
			Map<String,Object> o;
			String[] rowArray = new String[12];
			String prefixNo, updateIdNo, updateDate, updateTime;
			while (it.hasNext()) {
				o = it.next();
				prefixNo = Common.get(o.get("PREFIX_NO"));
				updateIdNo = Common.get(o.get("UPDATE_ID_NO"));
				updateDate = Common.get(o.get("UPDATE_DATE"));
				updateTime = Common.get(o.get("UPDATE_TIME"));
				
				rowArray = new String[12];
				//組合鍵(PREFIX_NO*UPDATE_ID_NO*UPDATE_DATE*UPDATE_TIME)(例:098004265*A223000510*0980206*091113)
				rowArray[0] = CommonStringUtils.append(prefixNo,COMPOSITE_KEY,updateIdNo,COMPOSITE_KEY,updateDate,COMPOSITE_KEY,updateTime);
				rowArray[1] = prefixNo;
				rowArray[2] = Datetime.formatRocDate(updateDate);
				rowArray[3] = Datetime.formatRocTime(updateTime);
				rowArray[4] = ServiceGetter.getInstance().getCedbc000CodeLoader().getNameById(updateIdNo);
				rowArray[5] = Common.get(o.get("APPLY_NAME"));
				rowArray[6] = Common.get(o.get("CHANGE_TYPE"));
				rowArray[7] = ServiceGetter.getInstance().getSystemCode05Loader().getCodeNameByCode(Common.get(o.get("APPROVE_RESULT")));
				rowArray[8] = Common.get(o.get("COMPANY_NAME"));
				rowArray[9] = Common.get(o.get("STAFF_NAME"));
				rowArray[10] = Datetime.formatRocDate(Common.get(o.get("RESERVE_DATE")));
				rowArray[11] = ServiceGetter.getInstance().getSystemCode11Loader().getCodeNameByCode(Common.get(o.get("COMPANY_STUS")));
				arrList.add(rowArray);	
			}
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getQ_prefixNo() {return checkGet(q_prefixNo);}
	public void setQ_prefixNo(String s) {this.q_prefixNo = checkSet(s);}

	public String getQ_banNo() {return checkGet(q_banNo);}
	public void setQ_banNo(String s) {this.q_banNo = checkSet(s);}

	public String getQ_applyId() {return checkGet(q_applyId);}
	public void setQ_applyId(String s) {this.q_applyId = checkSet(s);}

	public String getQ_applyName() {return checkGet(q_applyName);}
	public void setQ_applyName(String s) {this.q_applyName = checkSet(s);}

	public String getQ_companyName() {return checkGet(q_companyName);}
	public void setQ_companyName(String s) {this.q_companyName = checkSet(s);}

	public String[] getPrefixNos() {return prefixNos;}
	public void setPrefixNos(String[] prefixNos) {this.prefixNos = prefixNos;}

	public String getCurrent() {return checkGet(current);}
	public void setCurrent(String s) {this.current = checkSet(s);}

}