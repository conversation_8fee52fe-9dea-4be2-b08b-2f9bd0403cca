package com.kangdainfo.tcfi.loader;

/**
 * 片語
 *
 */
public class SystemCode15Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_15";
	private static final String CODE_KIND = "15";//15:片語

	//singleton
	private static SystemCode15Loader instance;
	public SystemCode15Loader() {
		if (SystemCode15Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode15Loader.instance);
		}
		SystemCode15Loader.instance = this;
	}
	public static SystemCode15Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}
