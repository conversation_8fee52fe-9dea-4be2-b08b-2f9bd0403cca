<!--
程式目的：收文-補列印回執聯
程式代號：PRE1005
撰寫日期：103.05.07
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE1005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean> 
<%
if ("rePrint".equals(obj.getState())){
	obj.rePrint();
} else if ("rePrintTCO".equals(obj.getState())) {
	obj.rePrintTCO();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<SCRIPT LANGUAGE="JAVASCRIPT">
$( window ).load(function() {
	//$('#print').hide();
	var startNo = commonUtils.getURLParameter('PREFIX_NO_START');
	var endNo = commonUtils.getURLParameter('PREFIX_NO_END');
	var from = commonUtils.getURLParameter("from");
	var printAddressTag = commonUtils.getURLParameter("printAddressTag");

	if(printAddressTag == 'Y') {
		$("input[name=type][value=T]").prop('checked', true);
	}
	if (form1.state.value == "rePrintTCOSuccess") {
		sendToPrinter();
	}
	$("#resetAll").click(function(){
		//form1.reset();
		$("#start").val("");
		$("#end").val("");
	});
	$("#rePrint").click(function(){
		form1.state.value = "rePrint";
		if($("#start").val().length < 9){
			showMsgBar("預查編號太短或未填寫(PREFIX NO ERROR)");
		} else {
			if(!$("#end").val())
				$("#end").val($("#start").val());
			
			$("#start").focus();
			form1.submit();
		}
	});
	$("#rePrintTCO").click(function(){
		form1.state.value = "rePrintTCO";
		if($("#start").val().length < 9){
			showMsgBar("預查編號太短或未填寫(PREFIX NO ERROR)");
		} else {
			if(!$("#end").val())
				$("#end").val($("#start").val());
			
			$("#start").focus();
			form1.submit();
		}
	});
	
	if(startNo && endNo && from == 'pre1001') {

		var isPrinted = commonUtils.getURLParameter('printed');
		if(!isPrinted) {
			form1.action = window.location.href + '&printed=true';
			if (document.getElementsByName("type")[1].checked == true) {
				form1.state.value = "rePrint";	
				$("#rePrint").click();
			} else {
				form1.state.value = "rePrintTCO";	
			}
			form1.submit();
		} else {
			window.close();
		}
		//$('#rePrint').click();
	}
	
	<%if(!"".equals(Common.get(obj.getErrorMsg()))){%>
		showMsgBar('<%=obj.getErrorMsg()%>');
	<%}%>
	
	if( from == 'pre1001' ) {
		//pre1001 取號存檔後不能在pre1005設定focus, 否則pre1001的focus會跑掉
	} else {
		$("#start").focus();
	}
});

function keyDown() {
	if (event.keyCode==13) {
		$("#rePrint").click();
	}
}
function sendToPrinter() {
	var ret;
	var myobject = new ActiveXObject("GoDEXATL.Function");
	ret = myobject.openport("6");
 	try
   	{ 
 		var prefixNo;
		var receiveDate;
		var applyType;
 		var tagx = form1.newPrinterString.value;
 		var cases = tagx.split("｜");
 		for (var i=0; i<cases.length; i++) {
 			prefixNo = cases[i].split("@")[0];
 			receiveDate = cases[i].split("@")[4].substring(0,3)+'/'+cases[i].split("@")[4].substring(3,5)+'/'+cases[i].split("@")[4].substring(5);
 			applyType = cases[i].split("@")[1]=='1'?'設立':'變更';
 			ret = myobject.setup(93, 12, 4, 0, 3,0);
 			ret = myobject.sendcommand("^L\r\n");
 	   		ret = myobject.ecTextOut(235, 50, 30, "細明體", "經濟部商業發展署");
 	   		ret = myobject.ecTextOut(235, 100, 24, "細明體", "預查編號 : "+ prefixNo);
 	   		ret = myobject.ecTextOut(235, 150,24, "細明體", "收文日期 : "+ receiveDate);
 	   		ret = myobject.ecTextOut(235, 200,24, "細明體", "申請種類 : "+ applyType);
 	   		ret = myobject.ecTextOut(235, 250,24, "細明體", "自取案件請向預查第3號櫃台領取");
 	   		ret = myobject.ecTextOut(235, 300,24, "細明體", "客服專線 : 4121166");
 	   		ret = myobject.ecTextOut(235, 350,24, "細明體", "收文櫃台電話 : (049)2359171 #2322");
 	   		ret = myobject.ecTextOut(235, 400,24, "細明體", "發文櫃台電話 : (049)2359171 #2323");
 	   		ret = myobject.sendcommand("BA3,235,480,2,6,80,0,0,"+prefixNo);
 	   		ret = myobject.sendcommand("BA3,235,600,2,6,80,0,3,"+prefixNo);   	
 	   		ret = myobject.sendcommand("E\r\n");
 		}
   	}
  	catch(e) { 
  		alert("error:"+ ret); 
  	} 
  	finally {
      myobject.closeport();
  	}
}

function sendToPrinter4address() {
	var ret;
	var myobject = new ActiveXObject("GoDEXATL.Function");
	ret = myobject.openport("6");
 	try
   	{ 
 		var prefixNo;
		var contactName;
		var contactAddr;
 		var tagx = form1.newPrinterString.value;
 		var cases = tagx.split("｜");
 		for (var i=0; i<cases.length; i++) {
 			prefixNo = cases[i].split("@")[0];
 			contactName = cases[i].split("@")[5];
 			contactAddr = cases[i].split("@")[6];
 			ret = myobject.setup(93, 12, 4, 0, 3,0);
 			ret = myobject.sendcommand("^L\r\n");
 	   		ret = myobject.ecTextOutR(390, 112, 24, "細明體", "預查編號 : "+ prefixNo, 4);
 	   		ret = myobject.ecTextOutR(290, 112, 24, "細明體", contactAddr, 4);
 	   		ret = myobject.ecTextOutR(186, 112, 32, "細明體", contactName + "先生/小姐收", 4);
 	   		ret = myobject.ecTextOutR(60, 388, 24, "細明體", "郵寄");
 	   		ret = myobject.sendcommand("BA3,60,600,2,6,80,0,3,"+prefixNo);   	
 	   		ret = myobject.sendcommand("E\r\n");
 		}
   	}
  	catch(e) { 
  		alert("error:"+ ret); 
  	} 
  	finally {
      myobject.closeport();
  	}
}
</SCRIPT>
</head>
<body>
<form id="form1" name="printPrefixForm" method="post" action="">

<!--最上方BANNER-->
<c:import url="../common/function_banner.jsp">
	<c:param name="function" value='PRE1005'/>
</c:import>

<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr>
		<td align="left">
			<input class="toolbar_default" type="button" id="resetAll" name="btnQuery1" value="重新輸入" />&nbsp;
			<input class="toolbar_default" type="button" id="rePrint" name="btnQuery2" value="補列印回執聯" />&nbsp;
			<input class="toolbar_default" type="button" id="rePrintTCO" value="補印回執聯(名稱預查科用)"/>
		</td>
		<td align="right">
			<c:import url="../common/shortcut.jsp">
				<c:param name="functions" value=''/>
			</c:import>
		</td>
	</tr>
</table>

<table class="table_form" width="100%" cellpadding="2" cellspacing="0">
	<tr>
		<TD colspan="2" class="" align="left" width="100%">輸入條件</TD>
	</tr>
	<TR>
		<TD class="td_form">預查編號起迄</TD>
		<TD class="td_form_white">
			<input type="text" class="field" id="start" name="PREFIX_NO_START" maxlength="9" size="10" value="" onKeyDown="keyDown()">~
			<input type="text" class="field" id="end" name="PREFIX_NO_END" maxlength="9" size="10" value="" onKeyDown="keyDown()">
			(欲列印一張可輸入相同預查編號)
		</TD>
	</TR>
	<TR>
		<TD class="td_form">列印方式</TD>
		<TD class="td_form_white">
			<input type="radio" name="type" value="F" checked="checked" />依據領件方式
			<input type="radio" name="type" value="T">地址條
		</TD>
	</TR>
</table>
	<c:import url="../common/msgbar.jsp">
		<c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>

<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<input type="hidden" name="print" value="<%=obj.getPrint()%>">
<input type="hidden" name="newPrinterString" value="<%=obj.getNewPrinterString()%>">


<div id="print" style="display:none">
	<%
	if("rePrint".equals(obj.getState()) && !"".equals(obj.getTagx())){System.out.println("rrrrr");%>
		<iframe id="printTag" src="<%=obj.getPrint()%>.jsp?tagx=<%=java.net.URLEncoder.encode(obj.getTagx(), "UTF-8")%>"></iframe>
	<%}
	%>
</div>

</form>
</body>
</html>
