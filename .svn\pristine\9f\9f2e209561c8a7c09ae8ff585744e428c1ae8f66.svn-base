package com.kangdainfo.moea.bo;

import java.util.Comparator;

import com.kangdainfo.tcfi.model.eedb.bo.Eedb3000;

public class SeqNoComparatorFor3000 implements Comparator<Object> {
  public SeqNoComparatorFor3000() {
  }
  public int compare(Object o1, Object o2) {
    Eedb3000 co1 = (Eedb3000) o1;
    Eedb3000 co2 = (Eedb3000) o2;
    return co1.getSeqNo().compareTo(co2.getSeqNo());
  }

  public boolean equals(Object obj) {
    throw new java.lang.UnsupportedOperationException(
      "Method equals() not yet implemented.");
  }


}
