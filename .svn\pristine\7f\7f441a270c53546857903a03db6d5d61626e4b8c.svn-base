<!--
程式目的：清算完結
程式代號：PRE3006
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean> 
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE3006" />
</jsp:include>
<%
if("init".equals(obj.getState()))	obj.init();

if ("windingUp".equals(obj.getState())) {
	obj.doWindingUp();
	if("updateSuccess".equals(obj.getState()))	
		obj = (com.kangdainfo.tcfi.view.pre.PRE3006)obj.queryOne();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if("deleteSuccess".equals(obj.getState()))	
		obj = (com.kangdainfo.tcfi.view.pre.PRE3006)obj.queryOne();
}else{
	obj = (com.kangdainfo.tcfi.view.pre.PRE3006)obj.queryOne();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault;
function checkField(){
	var alertStr="";
	if(form1.state.value=="windingUp"){
		alertStr += checkEmpty(form1.clearStatusCode,"清算類別");
		alertStr += checkEmpty(form1.clearUnit, "核備清算單位");
		alertStr += checkEmpty(form1.clearDate, "核備清算日期");
		alertStr += checkEmpty(form1.clearWord, "核備清算文號(字)");
		alertStr += checkEmpty(form1.clearNo, "核備清算文號(號)");
		alertStr += checkEmpty(form1.receiveNo, "收文文號");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	
	beforeSubmit();
}

function init() {
	window.moveTo(0,0);
	window.resizeTo(screen.width,screen.height*0.96);
	if($('#id').val() != ""){
		$('#spanDoClear').show();
		$('#spanDoUpdate').show();
	}else{
		$('#spanDoInsert').show();
	}
}

$(document).ready(function() {
	$('#doUpdate').click(function(){
		if(!confirm("確定修改資料?"))	return false;
		$('#state').val("windingUp");
	});
	$('#doInsert').click(function(){
		if(!confirm("確定清算完結?"))	return false;
		$('#state').val("windingUp");
	});
	$('#doClear').click(function(){
		if(!confirm("確定取消清算完結?"))	return false;
		$('#state').val("delete");
	});
	$('#firstBtn').click(function(){
		var banNos = form1.sendBanNos.value.split(",");
		if(banNos != null && banNos.length > 0){
			if($('#banNo').val() != banNos[0]){
				$('#state').val("queryOne");
				$('#banNo').val(banNos[0]);
				$('#thisRecord').val("0");
				form1.submit();
			}
		}
	});
	$('#prevBtn').click(function(){
		var banNos = form1.sendBanNos.value.split(",");
		if(banNos != null && banNos.length > 0){
			var index = parseInt($('#thisRecord').val()) - 1;
			if(index >= 0 && index < banNos.length){
				$('#state').val("queryOne");
				$('#banNo').val(banNos[index]);
				$('#thisRecord').val(index);
				form1.submit();
			}
		}
	});
	$('#nextBtn').click(function(){
		var banNos = form1.sendBanNos.value.split(",");
		if(banNos != null && banNos.length > 0){
			var index = parseInt($('#thisRecord').val()) + 1;
			if(index >= 0 && index < banNos.length){
				$('#state').val("queryOne");
				$('#banNo').val(banNos[index]);
				$('#thisRecord').val(index);
				form1.submit();
			}
		}
	});
	$('#lastBtn').click(function(){
		var banNos = form1.sendBanNos.value.split(",");
		if(banNos != null && banNos.length > 0){
			var index = parseInt(banNos.length) - 1;
			if(index >= 0 && index < banNos.length){
				if($('#banNo').val() != banNos[index]){
					$('#state').val("queryOne");
					$('#banNo').val(banNos[index]);
					$('#thisRecord').val(index);
					form1.submit();
				}
			}
		}
	});
	$('#doExit').click(function(){
		window.close();
	});
});
</script>
</head>
<body topmargin="0" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE3006'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
	<table width="100%">
		<tr>
			<td style="text-align:left" width="60%">
				<span id="spanDoClear" style="display:none;">
					<input class="toolbar_default" type="submit" followPK="false" id="doClear" name="doClear" value="取消清算完結" >&nbsp;
				</span>
				<span id="spanDoUpdate" style="display:none;">
					<input class="toolbar_default" type="submit" followPK="false" id="doUpdate" name="doUpdate" value="修改資料">&nbsp;
				</span>
				<span id="spanDoInsert" style="display:none;">
					<input class="toolbar_default" type="submit" followPK="false" id="doInsert" name="doInsert" value="存檔">&nbsp;
				</span>
				<input type="hidden" id="id" name="id" value="<%=obj.getId()%>">
				<input type="hidden" id="sendBanNos" name="sendBanNos" value="<%=obj.getSendBanNos()%>">
				<input type="hidden" id="thisRecord" name="thisRecord" value="<%=obj.getThisRecord()%>">
				<input type="hidden" id="orgnType" name="orgnType" value="<%=obj.getOrgnType()%>">
				<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
				<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
				<jsp:include page="../../home/<USER>" >
					<jsp:param name="btnInsert" value="N" />
					<jsp:param name="btnQueryAll" value="N" />
					<jsp:param name="btnUpdate" value="N" />
					<jsp:param name="btnDelete" value="N" />
					<jsp:param name="btnClear" value="N" />
					<jsp:param name="btnConfirm" value="N" />
					<jsp:param name="btnListPrint" value="N" />
					<jsp:param name="btnListHidden" value="N" />
				</jsp:include>
			</td>
			<td class="title_form" style="text-align:right" colspan="1" width="40%">
				<span name="page_btn" id="page_btn" style="display: black">
					<button class="btn_Page" name="firstBtn" id="firstBtn"><img src="../../images/pre/btn_first.gif"></button>
					<button class="btn_Page" name="prevBtn" id="prevBtn"><img src="../../images/pre/btn_up.gif"></button>
					<input type="text" size="8" id="current" value="<%=obj.getBanNo()%>" readonly class="field_RO">
					<button class="btn_Page" name="nextBtn" id="nextBtn"><img src="../../images/pre/btn_down.gif"></button>
					<button class="btn_Page" name="lastBtn" id="lastBtn"><img src="../../images/pre/btn_last.gif"></button>&nbsp;
					<input class="toolbar_default" type="button" id="doExit" name="doExit" value="離　開">
				</span>
			</td>
		</tr>
	</table>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- FORM AREA read only-->
<tr><td>
	<table cellpadding=0 cellspacing=0 valign="top">
		<tr>
			<td nowrap ID=t1 CLASS="tab_border1" width="100" height="25">公司/有限合夥基本資料</td>
		</tr>
		<tr>
			<td nowrap class="tab_line1"></td>
		</tr>
	</table>
</td></tr>
<tr><td nowrap class="bg">
	<table class="table_form" width="100%">
		<tr>
			<td nowrap class="td_form" width="15%">統一編號：</td>
			<td nowrap class="td_form_white" width="25%">
				<%=obj.getBanNo()%>
				<input type="hidden" id="banNo" name="banNo" value="<%=obj.getBanNo()%>">
			</td>
			<td nowrap class="td_form" width="15%">目前狀態：</td>
			<td nowrap class="td_form_white" width="15%">
				<%=obj.getStatusCodeName()%>
				<input type="hidden" id="statusCodeName" name="statusCodeName" value="<%=obj.getStatusCodeName()%>">
			</td>
			<td nowrap class="td_form" width="15%">狀態代碼：</td>
			<td nowrap class="td_form_white" width="15%">
				<%=obj.getStatusCode()%>
				<input type="hidden" id="statusCode" name="statusCode" value="<%=obj.getStatusCode()%>">
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">代表人：</td>
			<td nowrap class="td_form_white">
				<%=obj.getRespName()%>
				<input type="hidden" id="respName" name="respName" value="<%=obj.getRespName()%>">
			</td>
			<td nowrap class="td_form" width="15%">資本總額：</td>
			<td nowrap class="td_form_white" width="15%" colspan="3">
				<%=obj.getCapitalAmt()%>
				<input type="hidden" id="capitalAmt" name="capitalAmt" value="<%=obj.getCapitalAmt()%>">
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">名稱：</td>
			<td nowrap class="td_form_white" colspan="3">
				<%=obj.getCompanyName()%>
				<input type="hidden" id="companyName" name="companyName" value="<%=obj.getCompanyName()%>">
			</td>
			<td nowrap class="td_form">組織型態：</td>
			<td nowrap class="td_form_white">
				<%=obj.getOrgnTypeName()%>
				<input type="hidden" id="orgnTypeName" name="orgnTypeName" value="<%=obj.getOrgnTypeName()%>">
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">地址：</td>
			<td nowrap class="td_form_white" colspan="5">
				<%=obj.getCompanyAddr()%>
				<input type="hidden" id="companyAddr" name="companyAddr" value="<%=obj.getCompanyAddr()%>">
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">申登機關：</td>
			<td nowrap class="td_form_white" colspan="5">
				<%=obj.getRegUnit()%>
				<input type="hidden" id="regUnit" name="regUnit" value="<%=obj.getRegUnit()%>">
			</td>
		</tr>
	</table>
</td></tr>

<!-- FORM AREA -->
<tr><td>
	<table cellpadding=0 cellspacing=0 valign="top">
		<tr>
			<td nowrap ID=t1 CLASS="tab_border1" width="100" height="25">清算完結資料</td>
		</tr>
		<tr>
			<td nowrap class="tab_line1"></td>
		</tr>
	</table>
</td></tr>
<tr><td nowrap class="bg">
	<table class="table_form" width="100%">
		<tr>
			<td nowrap class="td_form" width="15%">清算類別：</td>
			<td nowrap class="td_form_white" width="85%">
				<select class="field_RO" name="clearStatusCode">
					<%=obj.getOptionClearStatusCode(obj.getStatusCode(),obj.getOrgnType(),obj.getClearStatusCode()) %>
				</select>
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">核備清算單位：</td>
			<td nowrap class="td_form_white">
				<select class="field" name="clearUnit">
					<%=View.getOptionCourts(obj.getClearUnit(),false,1) %>
				</select>
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">核備清算日期：</td>
			<td nowrap class="td_form_white">
				<%=View.getPopCalendar("field","clearDate",obj.getClearDate())%>
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">核備清算文號：</td>
			<td nowrap class="td_form_white">
				<input class="field" type="text" name="clearWord" size="30" maxlength="30" value="<%=obj.getClearWord()%>">
				(字) (第)
				<input class="field" type="text" name="clearNo" size="10" maxlength="10" value="<%=obj.getClearNo()%>">
				(號)
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">收文文號：</td>
			<td nowrap class="td_form_white">
				<input class="field" type="text" name="receiveNo" size="15" maxlength="11" value="<%=obj.getReceiveNo()%>">
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">備註：</td>
			<td nowrap class="td_form_white">
				<textarea class="field" name="remark" cols="80" rows="4" style="white-space: pre-line !important;" ><%=obj.getRemark()%></textarea>
			</td>
		</tr>
	</table>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>
</table>
</form>
</body>
</html>