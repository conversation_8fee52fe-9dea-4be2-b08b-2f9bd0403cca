package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SuperBean;
import com.kangdainfo.tcfi.model.eicm.bo.TrackLog;
import com.kangdainfo.tcfi.util.PrefixConstants;

public class PRE9007 extends SuperBean {
	
	//private String tabId;
	private String q_funcCode;
	private String q_opDateS;
	private String q_opDateE;
	private String q_opUser;
	
	//public String getTabId() {return checkGet(tabId);}
	//public void setTabId(String tabId) {this.tabId = checkSet(tabId);}
	public String getQ_funcCode() {return checkGet(q_funcCode);}
	public void setQ_funcCode(String q_funcCode) {this.q_funcCode = checkSet(q_funcCode);}
	public String getQ_opDateS() {return checkGet(q_opDateS);}
	public void setQ_opDateS(String q_opDateS) {this.q_opDateS = checkSet(q_opDateS);}
	public String getQ_opDateE() {return checkGet(q_opDateE);}
	public void setQ_opDateE(String q_opDateE) {this.q_opDateE = checkSet(q_opDateE);}
	public String getQ_opUser() {return checkGet(q_opUser);}
	public void setQ_opUser(String q_opUser) {this.q_opUser = checkSet(q_opUser);}

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		java.util.List<TrackLog> objList = ServiceGetter.getInstance().getTrackLogService().getTrackLog(
				getQ_funcCode(), "PRE9007".equals(getProgID())?PrefixConstants.TRACK_LOG_SEARCH:PrefixConstants.TRACK_LOG_UPDATE, getQ_opUser(), getQ_opDateS(), getQ_opDateE());
		if (objList != null && objList.size() > 0) {
			java.util.Iterator<?> it = objList.iterator();
			TrackLog o;
			String[] rowArray = new String[6];
			while (it.hasNext()) {
				o = (TrackLog) it.next();
				rowArray = new String[6];
				rowArray[0] = Common.get(o.getFuncName());
				rowArray[1] = PrefixConstants.TRACK_LOG_UPDATE.equals(o.getType())?"修改":"查詢";
				rowArray[2] = Common.get(o.getRemark());
				rowArray[3] = Common.get(o.getName());
				rowArray[4] = Common.formatYYYMMDD(o.getDate(), 4) + " " + Common.formatHHMMSS(o.getTime());
				rowArray[5] = Common.get(o.getIp());
				arrList.add(rowArray);	
			}
		}else{
			this.setErrorMsg("查無資料，請您重新輸入查詢條件！");
		}
		return arrList;
	}

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

}