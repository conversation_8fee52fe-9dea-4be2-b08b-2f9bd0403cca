package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.bo.PrefixQueryVo;

/**
 * 發文登打
 */
public interface Pre2001Service {

	/**
	 * 重設發文
	 * @param prefixNo
	 * @param userId
	 * 
	 */
	public String resetCloseDate(String prefixNo, String userId);
	
	/**
	 * 存檔
	 * @param prefixNo
	 * @param remark1
	 * @param approveRemark
	 * @return
	 */
	public String save(String prefixNo, String userId, String remark1, String approveRemark);

	/**
	 * @param prefixNo
	 * @param userId
	 * @return
	 */
	public PrefixQueryVo findPrefixQueryVo(String prefixNo, String userId);

}