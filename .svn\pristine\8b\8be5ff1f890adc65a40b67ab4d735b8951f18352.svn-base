package com.kangdainfo.tcfi.lucene.util;

import java.io.File;

import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.IndexWriterConfig;
import org.apache.lucene.index.IndexWriterConfig.OpenMode;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.apache.lucene.util.Version;

public class LuceneManager {
	//------lock 1
	private static Object lock_writer = new Object();
	//Lucene 版本(升級至4.8會有連詞問題, 如"第一","三好")
	@SuppressWarnings("deprecation")
	public static final Version LUCENE_VERSION = Version.LUCENE_47;

	public static IndexWriter getIndexWriter(String indexPath, OpenMode openMode) {
		synchronized(lock_writer){
			//if (writer == null || !indexWriter.equalsIgnoreCase(indexPath)) {
			IndexWriter writer = null;
				try {
					File indexFile = new File(indexPath);
					if (!indexFile.exists())
						indexFile.mkdir();
	
					Directory fsDirectory = FSDirectory.open(indexFile);
					IndexWriterConfig confIndex = new IndexWriterConfig(LUCENE_VERSION, new StandardAnalyzer(
							LUCENE_VERSION));
					confIndex.setOpenMode(openMode);
					if (IndexWriter.isLocked(fsDirectory)) {
						IndexWriter.unlock(fsDirectory);
					}
					writer = new IndexWriter(fsDirectory, confIndex);
					return writer;
				} catch (Exception e) {
					e.printStackTrace();
				}
			return writer;
			//}
		}
	}

}