package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * 工作日檔(CSMD_WORK_DAY)
 *
 */
public class CsmdWorkDay extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 申登機關代碼 */
	private String regUnitCode;
	/** 工作日期 */
	private Date workDate;
	/** 星期幾 */
	private String week;
	/** 工作或休假(Y:工作;N:休假) */
	private String work1;
	/** 全天或半天(Y:全天;N:半天) */
	private String half;
	/** 註記(空白:工作日;1:一般週休日;2:國定例假日;3:颱風假;4:其他) */
	private String flag;

	public String getRegUnitCode() {return regUnitCode;}
	public void setRegUnitCode(String s) {this.regUnitCode = s;}
	public Date getWorkDate() {return workDate;}
	public void setWorkDate(Date d) {this.workDate = d;}
	public String getWeek() {return week;}
	public void setWeek(String s) {this.week = s;}
	public String getWork1() {return work1;}
	public void setWork1(String s) {this.work1 = s;}
	public String getHalf() {return half;}
	public void setHalf(String s) {this.half = s;}
	public String getFlag() {return flag;}
	public void setFlag(String s) {this.flag = s;}

}