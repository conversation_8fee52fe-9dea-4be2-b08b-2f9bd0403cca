<?xml version="1.0" encoding="UTF-8"?>
<project name="prefix" basedir="." default="package-web" >
	<!-- dist dir -->
	<property name="dist.dir" value="dist"/>
	<!-- dist war file -->
	<property name="dist.war" value="${dist.dir}\war"/>
	<!-- dist webinf -->
	<property name="dist.webinf" value="${dist.war}\WEB-INF"/>
	<!-- dist lib dir -->
	<property name="dist.lib" value="${dist.webinf}\lib"/>
	<!-- dist classes dir -->
	<property name="dist.classes" value="${dist.webinf}\classes"/>
	<!-- dist war file -->
	<property name="dist.war.file" value="${dist.dir}\prefix.war"/>
	<!-- java src path -->
	<property name="src.java" value="${basedir}\src"/>
	<!-- webapp path -->
	<property name="webapp.dir" value="${basedir}\WebContent"/>
	<!-- classpath -->
	<path id ="classpath">
		<fileset dir="lib" includes="**\*.jar"/>
		<fileset dir="WebContent\WEB-INF\lib" includes="**\*.jar"/>
	</path>

	<!-- ======================================================= -->
	<!-- Show Environment Parameter.                             -->
	<!-- ======================================================= -->
	<target name="echo">
		<echo message="The operating system is: ${os.name}" />
		<!-- absolute path of the project. -->
		<echo message="The home path is: ${basedir}" />
		<!-- absolute path of the build file. -->
		<echo message="The file name is: ${ant.file}" />
		<!-- root directory of ant. -->
		<echo message="The Project name is: ${ant.project.name}" />
		<echo message="The Ant home directory is: ${ant.home}" />
		<echo message="The Ant version is: ${ant.version}" />
		<echo message="The Java version is: ${ant.java.version}" />
		<!-- System properties. -->
		<echo message="The Java home directory is: ${java.home}" />
		<echo message="The User home directory is: ${user.home}" />
		<echo message="The User name is: ${user.name}" />
	</target>

	<!-- ======================================================= -->
	<!-- Clean dist directory.                       -->
	<!-- ======================================================= -->
	<target name="clean" description="Clean the dist Directory." depends="echo">
		<echo message="delete target directory"/>
		<delete dir="${dist.war}" />
		<delete file="${dist.war.file}" />
	</target>

	<!-- ======================================================= -->
	<!-- Make Dir and Copy targets.                              -->
	<!-- ======================================================= -->
	<target name="copy" depends="clean" description="Copy the necessary files..." >
		<mkdir dir="${dist.war}"/>
		<copy todir="${dist.war}" preservelastmodified="true">
			<fileset dir="${webapp.dir}" >
				<include name="**\*.*"/>
				<exclude name="**\classes\**"/>
			</fileset>
		</copy>
		<!--<mkdir dir="${dist.lib}"/>
				<copy todir="${dist.lib}" preservelastmodified="true">
				<fileset dir="lib" includes="**/*.jar"/>
		</copy>-->
	</target>

	<!-- ======================================================= -->
	<!-- Compile all classes.                                    -->
	<!-- ======================================================= -->
	<target name="compile" depends="copy" description="Compile the java code..." >
		<echo message="compile all classes"/>
		<mkdir dir="${dist.classes}"/>
		<javac srcdir="${src.java}" encoding="utf-8" source="1.7" target="1.7"  
             destdir="${dist.classes}" classpathref="classpath" debug="true"
			includeantruntime="false" fork="true"
			memoryInitialSize="128m" memoryMaximumSize="512m" />
		<copy todir="${dist.classes}" overwrite="true">
			<fileset dir="${src.java}">
				<include name="**\*.*"/>
				<exclude name="**\*.java"/>
				<exclude name="**\*.properties"/>
			</fileset>
		</copy>
		<native2ascii encoding="utf-8"
			src="${src.java}" dest="${dist.classes}"
			includes="*.properties"
			ext=".properties" />
	</target>

	<!-- ===================================================== -->
	<!-- Setup Environment                                     -->
	<!-- ===================================================== -->
	<target name="menu">
		<echo>deploy target :</echo>
		<echo> [local]:local.</echo>
		<echo> [test]:測試區</echo>
		<echo> [prod]:國光正式區</echo>
		<echo> [winsing]:文心備援區</echo>
		<echo> [exit]:exit</echo>
		<input message="select Environment." validargs="local,test,prod,winsing,exit"
			addproperty="env" />
		<condition property="do.abort">
			<equals arg1="exit" arg2="${env}" />
		</condition>
		<fail if="do.abort">Build aborted by user.</fail>
	</target>

	<!-- ===================================================== -->
	<!-- Replace Config File                                   -->
	<!-- ===================================================== -->
	<target name="replaceConfig" depends="menu">
		<echo>useing ${env}.properties...</echo>
		<copy file="doc\conf\${env}\app.properties" tofile="${dist.webinf}\properties\app.properties" overwrite="true" />
		<copy file="doc\conf\${env}\database.properties" tofile="${dist.webinf}\properties\database.properties" overwrite="true" />
		<copy file="doc\conf\${env}\log4j.xml" tofile="${dist.classes}\log4j.xml" overwrite="true" />
		<copy file="doc\conf\${env}\applicationContext-ap-schedule.xml" tofile="${dist.classes}\com\zhanxin\aa\cfg\applicationContext-ap-schedule.xml" overwrite="true" />
		<copy file="doc\conf\${env}\conf.properties" tofile="${dist.classes}\conf.properties" overwrite="true" />
	</target>

	<!-- ===================================================== -->
	<!-- Building War Files                                    -->
	<!-- ===================================================== -->
	<target name="package-web" depends="compile, replaceConfig">
		<echo>building war file...[${dist.war.file}]</echo>
		<war destFile="${dist.war.file}" basedir="${dist.war}" compress="true" />
	</target>

</project>