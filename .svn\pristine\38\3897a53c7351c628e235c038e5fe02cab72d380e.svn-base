<!--
程式目的：解釋函查詢
程式代號：pre4005
撰寫日期：103.05.22
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4005">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4005" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true") ;
} // end if
else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE4005)obj.queryOne();
} //end else if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4005.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
} // end else if

if ( "true".equals(obj.getQueryAllFlag()) ) 
	  objList = (java.util.ArrayList) obj.queryAll();
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>

<style>
div#printContainer {
	position: absolute;
	z-index: 30;
	left: 0;
	top: 0;
}

iframe#printContainerFrame {
	position: absolute;
	z-index: -1;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}

	
</style>


<script type="text/javascript">
function checkField(){
	var alertStr="";
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
	return true;
}

function queryOne(rcvNo){
	$('#rcvNo').val(rcvNo); 
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
} 

function init() {
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function searchIndex(btnType) {
	var total = $('#listTBODY tr').size();
	if(total > 0){
		var rcvNo = $('#rcvNo').val();
		var index = 0;
		$("#listTBODY tr").each(function(i, v) {
			if(rcvNo == ""){
				rcvNo = $(v).find("td").eq(1).text();
				return false;
			}else if(rcvNo == $(v).find("td").eq(1).text()){
				if(btnType == "prevBtn"){
					if((i-1) > 0)	index = i-1;
				}else{
					if((i+1) < (total))	index = i+1;
				}
				rcvNo = $("#listTBODY tr").eq(index).find("td").eq(1).text();
				return false;
			}
		});
		if(rcvNo != ""){
			$.ajax({
				type: 'POST',
				url: getVirtualPath() + "tcfi/ajax/jsonDeclaratory.jsp",
				data: "q=" + encodeURI(rcvNo),
			    async: false
			}).done(function (data) {
				if(data) {
					$('#rcvNo').val(rcvNo);
					$('#rcvNo1').val(data.RCV_NO);
					$('#rcvTime').val(data.RCV_TIME);
					$('#keynote').val(data.KEYNOTE);
					$('#instruction').val(data.INSTRUCTION);
					$('#receiveUnit').val(data.RCV_TYPE_1);
					$('#ccUnit').val(data.RCV_TYPE_2);
				}
			});
			//$.post(getVirtualPath() + "tcfi/ajax/jsonDeclaratory.jsp?q=" + encodeURI(rcvNo), function(data){});
		}
	}else{
		showMsgBar("請先執行查詢");
	}
}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE4005_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;
			case "doClear":
				form1.q_rcvNo.value = "";
				form1.q_rcvTime.value = "";
				form1.q_endRcvTime.value = "";
				form1.q_keynote.value = "";
				form1.q_instruction.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
	//列印
	$("#doPrintPdf").click(function(){
		if ( form1.rcvNo.value == "") {
			alert("請先選擇一筆正確的公文文號");
		} else {
			queryShow("printContainer");
		}
	});
	//上一筆
	$("#prevBtn").click(function(){
		searchIndex(this.name);
	});
	//下一筆
	$("#nextBtn").click(function(){
		searchIndex(this.name);
	});
});

function btnYes() {
	form1.printWithUnit.value = "true";
	form1.state.value = "preview";
	window.open("",'popReport');
	form1.target = 'popReport';
	queryHidden("printContainer");
	form1.submit();
	form1.target = '';
}

function btnNo() {
	form1.printWithUnit.value = "false";
	form1.state.value = "preview";
	window.open("",'popReport');
	form1.target = 'popReport';
	queryHidden("printContainer");
	form1.submit();
	form1.target = '';	
}
</script>
</head>
<body topmargin="5" onload="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4005'/>
</c:import>

<!-- TOOLBAR AREA -->
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<tr><td class="tab_line1"></td></tr>
	<tr><td class="tab_line1"></td></tr>
	<tr>
		<td align="left">
			<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name);$.blockUI({ message: '<h1>資料載入中，請稍後...</h1>' });" >&nbsp;
			<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="清　除" onClick="whatButtonFireEvent(this.name)" >&nbsp;
			<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="列　印">&nbsp;
			<input class="toolbar_default" type="button" followPK="false" id="prevBtn" name="prevBtn" value="上一筆">&nbsp;
			<input class="toolbar_default" type="button" followPK="false" id="nextBtn" name="nextBtn" value="下一筆">
		</td>
	</tr>
</table>
<!-- TOOLBAR AREA -->
<div id="printContainer" style="width:300px;height:100px;display:none" >
    <iframe id="printContainerFrame"></iframe>
	<div class="queryTitle">&nbsp</div>
	<table class="queryTable"  border="1">
	<tr>
		<td nowrap class="td_form_white" colspan="2" style="text-align:center;">
    	列印時要印出正副本嗎？
		</td>
	</tr>
    <tr>
		<td class="queryTDInput" style="text-align:center;">
			<input class="toolbar_default" type="button" id="buttonYes" name="buttonYes" value="是" onclick="btnYes()">
		</td>
		<td class="queryTDInput" style="text-align:center;">
			<input class="toolbar_default" type="button" id="buttonNo" name="buttonNo" value="否" onclick="btnNo()">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">
<!-- QueryArea  -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="10%">公文文號：</td>
			<td class="td_form_white" width="20%"> 
				<input class="field_Q" type="text" name="q_rcvNo" size="30" maxlength="30" value="<%=obj.getQ_rcvNo()%>" />
			</td>
			<td class="td_form" width="10%">公文日期：</td>
			<td class="td_form_white" width="30%">
				<%=View.getPopCalendar("field_Q","q_rcvTime",obj.getQ_rcvTime()) %> 至 <%=View.getPopCalendar("field_Q","q_endRcvTime",obj.getQ_endRcvTime()) %>
			</td>
		</tr>
		<tr>  
			<td class="td_form">函令主旨：</td>
        	<td class="td_form_white" colspan="3"> 
           		<input class="field_Q" type="text" name="q_keynote" size="90" maxlength="1000" value="<%=obj.getQ_keynote()%>" />
        	</td>
		</tr>
		<tr>
        <td nowrap class="td_form">函令說明：</td>
        <td nowrap colspan="3" class="td_form_white">
            <textarea rows="2" cols="90" class="field_Q" name="q_instruction" maxlength="2000" style="white-space: pre-wrap"><%=obj.getQ_instruction() %></textarea>
        </td>  
    </tr>			
  	</table>
	</div>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- Form Area-- -->
<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="10%">公文文號：</td>
			<td class="td_form_white" width="20%"> 
				<input class="field" type="text" id="rcvNo1" name="rcvNo1" size="30" maxlength="30" value="<%=obj.getRcvNo()%>" />
			</td>
			<td class="td_form" width="10%">公文日期：</td>
			<td class="td_form_white" width="30%"> 
				<%=View.getPopCalendar("field","rcvTime",obj.getRcvTime()) %>
			</td>
		</tr>
		<tr>  
			<td class="td_form">函令主旨：</td>
			<td class="td_form_white" colspan="3"> 
				<textarea rows="3" cols="90" class="field" id="keynote" name="keynote" style="white-space: pre-wrap"><%=obj.getKeynote() %></textarea>
			</td>
		</tr>
		<tr>
			<td nowrap class="td_form">函令說明：</td>
			<td nowrap colspan="3" class="td_form_white">
				<textarea rows="10" cols="90" class="field" id="instruction" name="instruction" style="white-space: pre-wrap"><%=obj.getInstruction() %></textarea>
			</td>  
		</tr>
		<tr>
			<td class="td_form">正本單位：</td>
			<td class="td_form_white" colspan="3"> 
				<textarea rows="2" cols="90" class="field" id="receiveUnit" name="receiveUnit" style="white-space: pre-wrap"><%=obj.getReceiveUnit() %></textarea>
			</td>
	    </tr>
	    <tr>
			<td class="td_form">副本單位：</td>
			<td class="td_form_white" colspan="3">
				<textarea rows="2" cols="90" class="field" id="ccUnit" name="ccUnit" style="white-space: pre-wrap"><%=obj.getCcUnit() %></textarea>
			</td>
		</tr>   				
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!--List區============================================================-->
<tr><td nowrap class="bgList">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  <thead id="listTHEAD">
  <tr>
    <th class="listTH" style="width:30px"><a class="text_link_w">NO.</a></th>
    <th class="listTH" style="text-align:left;width:300px"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">公文文號</a></th>
    <th class="listTH" style="text-align:left;width:80px"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">公文日期</a></th>
    <th class="listTH" style="text-align:left;" ><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">函令主旨</a></th>
  </tr>
  </thead>
  <tbody id="listTBODY">
  <%
  boolean primaryArray[] = {true,false,false,false};
  boolean displayArray[] = {false,true,true,true};
  String[] alignArray = {"left","left","left","left"};
  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(), true, null, null, "", true, false));
  %>
  </tbody>
</table>
</div>
</td></tr>

<!--Toolbar區============================================================-->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2" align="center">
		<tr><td style="text-align:center;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<input type="hidden" id="rcvNo" name="rcvNo" value="<%=obj.getRcvNo()%>">
			<input type="hidden" id="printWithUnit" name="printWithUnit" value="<%=obj.getPrintWithUnit()%>">		
			<input type="hidden" id="queryAllFlag" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>

</table>
</form>
</body>
</html>