package com.kangdainfo.tcfi.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import com.kangdainfo.common.util.DateTimeFormatter;
import com.kangdainfo.moea.bo.SearchAllData;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1000;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb2000;

public class SearchAllFilter {
	public SearchAllFilter() {
	}

	/**
	 * 去掉輔助查詢不合條件的資料(只顯示被核准的資料)
	 * 
	 * @param searchAllDatas
	 *            Vector
	 * @return Vector
	 */
	public static List<SearchAllData> SearchAllDatasFilter(List<SearchAllData> searchAllDatas) {
		List<SearchAllData> vec = new ArrayList<SearchAllData>();
		// Map map = new HashMap();
		// //先放進Map, 以利後面做比較的動作
		// for (int i = 0; i < searchAllDatas.size(); i++) {
		// SearchAllData searchAllData = (SearchAllData) searchAllDatas.get(i);
		// if (searchAllData.getCedb1000() != null){
		// //1000 以預查編號為key
		// map.put(searchAllData.getCedb1000().getPrefixNo(),searchAllData);
		// }else if (searchAllData.getCedb2000() != null){
		// //2000 以統編為key
		// map.put(searchAllData.getCedb2000().getBanNo(),searchAllData);
		// }
		// }

		if (searchAllDatas == null) {
			return null;
		}
		if (searchAllDatas.isEmpty()) {
			return vec;
		}

		SearchAllData searchAllData;
		Cedb1000 cedb1000;
		String approveResult, companyStus;
		for (int i = 0; i < searchAllDatas.size(); i++) {
			searchAllData = searchAllDatas.get(i);
			if (searchAllData.getCedb1000() != null) {
				cedb1000 = searchAllData.getCedb1000();
				approveResult = cedb1000.getApproveResult();
				// String banNo = cedb1000.getBanNo();
				// String applyKind = cedb1000.getApplyKind();
				companyStus = cedb1000.getCompanyStus();

				// KyLin : 沒有核准結果，或者核准結果不是核准保留的，都跳過(只顯示被核准的資料)
				// if (approveResult == null ||
				// (!approveResult.equalsIgnoreCase("Y")))
				if (!"Y".equalsIgnoreCase(approveResult))
					continue;
				// 被公司系統登記且核准啦，就不用保留

				// if (companyStus != null && companyStus.equals("03")) {
				if ("03".equals(companyStus))
					continue;
				// 此預查案件被公司系統吃掉了...一定要保留住

				if ("00".equals(companyStus) || "01".equals(companyStus)) {
					vec.add(searchAllData);
					continue;
				}

				// 保留期限+15天

				// if (cedb1000.getReserveDate() != null &&
				// cedb1000.getReserveDate().length() == 7) {
				// String reserve15 =
				// DateTimeFormatter.getTwDateByDay(cedb1000.getReserveDate(),
				// 15);
				// String today = DateTimeFormatter.getSystemDate();
				// if (today.compareTo(reserve15) > 0)
				// continue;
				// }

				// KyLin : 預查案件只顯示保留期限在三個月內. --> 改成15天96.07.23.
				if (cedb1000.getReserveDate() != null
						&& cedb1000.getReserveDate().length() != 0) {
					int reserve, three;
					reserve = parseInt(cedb1000.getReserveDate());
					// three = parseInt(DateTimeFormatter.getDate3MonthAgo());
					three = parseInt(DateTimeFormatter
							.west2TwDate(DateTimeFormatter.getDateStrByDay(-15)));
					if (reserve < three)
						continue;
				}

				// KyLin : 真是複雜...為何不直接用SQL下呢....

				// KyLin : 下面的邏輯重整, 假如預查檔有帶統編

				// 假如沒有統編，就要顯示（跑到此步已經檢查過核准結果與保留期限了）
				/*
				 * if (banNo == null) { vec.add(searchAllData); continue; } if
				 * (banNo.trim().length() == 0) { vec.add(searchAllData);
				 * continue; } // 有統編卻不在公司檔裡 Cedb2000 read2000 = new
				 * Facade2000().doReadByBanNoWithoutExp(banNo); if
				 * (read2000.getBanNo() == null || read2000.getBanNo().length()
				 * == 0) { vec.add(searchAllData); continue; } // if
				 * (!map.containsKey(banNo)) { // vec.add(searchAllData); //
				 * continue; // } // 有統編也在公司檔裡
				 * 
				 * if ("1".equals(applyKind)) { // 設立案直接跳過
				 * 
				 * continue; }
				 */
				// 都沒跳過，那就是要顯示啦~~
				vec.add(searchAllData);
			}
			if (searchAllData.getCedb2000() != null) {
				Cedb2000 cedb2000 = searchAllData.getCedb2000();
				String statusCode = cedb2000.getStatusCode();
				String orgnType = cedb2000.getOrgnType();
				// 2000符合條件的才顯示, 清算完結的不顯示
				// 外國公司報備不顯示於輔助查詢，不做同名檢查. 2006.06.14
				if ("08".equals(orgnType)) {
					continue;
				}

				// 清算完結狀態11~16，破產06，合併解散07都算是完全死的公司，不用秀
				// 96.02.09 六科通知，司法院說06破產，還不能釋放公司名稱
				// if (/*!"06".equals(statusCode) &&*/ !"07".equals(statusCode)
				// &&
				// !"11".equals(statusCode) && !"12".equals(statusCode) &&
				// !"13".equals(statusCode) && !"14".equals(statusCode) &&
				// !"15".equals(statusCode) && !"16".equals(statusCode)) {
				// vec.add(searchAllData);
				// continue;
				// }
				// 20111227 狀態(解散、廢止、撤銷、破產) 只鎖十年
				boolean endLessThen10Year = true;
				if (cedb2000.getEndDate() != null
						&& cedb2000.getEndDate().length() == 7) {
					java.util.Date endDate = DateTimeFormatter
							.changStrToDate(cedb2000.getEndDate());
					int YearDiff = DateTimeFormatter.getYearDiff(new Date(),
							endDate);
					if (YearDiff >= 10)
						endLessThen10Year = false;
				}
				if (("04".equals(statusCode) || "05".equals(statusCode)
						|| "06".equals(statusCode) || "09".equals(statusCode))) {
					if (endLessThen10Year) {
						vec.add(searchAllData);
						continue;
					}
				} else if (!"24".equals(statusCode) && !"07".equals(statusCode)
						&& !"11".equals(statusCode) && !"12".equals(statusCode)
						&& !"13".equals(statusCode) && !"14".equals(statusCode)
						&& !"15".equals(statusCode) && !"16".equals(statusCode)) {
					vec.add(searchAllData);
					continue;
				}
			}

			// Added by BryanLin 20080424
			if (searchAllData.getSsd() != null) {
				vec.add(searchAllData);
			}
		}
		return vec;
	}

	/**
	 * 去掉自動檢還撤件不合條件的資料, 不包含1000中預查編號與自己相同或2000中統編與自己相同者
	 * 
	 * 回傳 SearchAllData組成的Vector
	 * 
	 * @param searchAllDatas
	 *            Vector
	 * @return Vector
	 */
	public static List<SearchAllData> SearchAllDatasAutoSearchFilter(List<SearchAllData> searchAllDatas,
			Cedb1000 cedb1000org) {
		Map<String, SearchAllData> map = new HashMap<String, SearchAllData>();
		String banNo = cedb1000org.getBanNo();
		String prefixNo = cedb1000org.getPrefixNo();
		// 沒有要過濾的資料就不用查了,直接回傳空Vector
		if (searchAllDatas == null || searchAllDatas.isEmpty()) {
			return new ArrayList<SearchAllData>();
		}
		// 資料有錯的就不用查了,直接回傳空Vector
		if (prefixNo == null || prefixNo.trim().length() == 0) {
			return new ArrayList<SearchAllData>();
		}

		for (int i = 0; i < searchAllDatas.size(); i++) {
			SearchAllData searchAllData = searchAllDatas.get(i);
			if (searchAllData.getCedb1000() != null) {
				Cedb1000 cedb1000 = searchAllData.getCedb1000();
				String cedb1000CompanyStus = cedb1000.getCompanyStus();
				String cedb1000PrefixNo = cedb1000.getPrefixNo();
				// 1000符合條件的才顯示
				// 不是核准的案件就跳過
				if (cedb1000.getApproveResult() == null
						|| !cedb1000.getApproveResult().equals("Y")) {
					continue;
				}
				// 資料有錯的也跳過
				if (cedb1000PrefixNo == null
						|| cedb1000PrefixNo.trim().length() == 0) {
					continue;
				}

				if (cedb1000CompanyStus == null
						|| cedb1000CompanyStus.equals("01")
						|| cedb1000CompanyStus.equals("02")
						|| cedb1000CompanyStus.equals("04")) {
					// 不包含自己, 而且不重覆

					if (!cedb1000PrefixNo.equals(prefixNo)
							&& !map.containsKey(prefixNo)) {
						System.out.println("===SameWordBo SearchAllDatasAutoSearchFilter 過濾後找到必須檢還的案件:"
										+ cedb1000PrefixNo);
						map.put(cedb1000PrefixNo, searchAllData);
						continue;
					}
				}
			} else if (searchAllData.getCedb2000() != null) {
				Cedb2000 cedb2000 = searchAllData.getCedb2000();
				String cedb2000StatusCode = cedb2000.getStatusCode();
				String cedb2000BanNo = cedb2000.getBanNo();
				// 資料有錯的也跳過( 2000的資料沒有統編不合理的)
				if (cedb2000BanNo == null || cedb2000BanNo.trim().length() == 0) {
					continue;
				}
				// 2000符合條件的才顯示
				if (cedb2000StatusCode != null) {
					if (cedb2000StatusCode.equals("01")
							|| cedb2000StatusCode.equals("02")
							|| cedb2000StatusCode.equals("03")
							|| cedb2000StatusCode.equals("04")
							|| cedb2000StatusCode.equals("05")
							|| cedb2000StatusCode.equals("08")
							|| cedb2000StatusCode.equals("09")
							|| cedb2000StatusCode.equals("10")) {
						if (cedb2000BanNo != null
								&& cedb2000BanNo.trim().length() > 0
								&& !cedb2000BanNo.equals(banNo)) {
							map.put(cedb2000BanNo, searchAllData);
							continue;
						}
					}
				}
			}
		}// end for
		if (map.values() == null) {
			return new Vector<SearchAllData>();
		}
		return new Vector<SearchAllData>(map.values());
	}

	// 字串轉數字
	private static int parseInt(String str) {
		if (str == null)
			return 0;
		int a = 0;
		try {
			a = Integer.parseInt(str);
		} catch (Exception e) {
			// do nothing
		}
		return a;
	}
}
