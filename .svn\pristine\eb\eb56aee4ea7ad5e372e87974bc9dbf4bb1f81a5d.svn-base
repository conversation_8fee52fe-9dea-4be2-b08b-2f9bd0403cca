<!DOCTYPE html>
<!--
程式目的：登入紀錄查詢
程式代號：PRE9006
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9006">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9006" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	obj.setQueryAllFlag("true");
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkQuery();
	if(form1.q_loginDateStart.value != "")	alertStr += checkEmpty(form1.q_loginDateEnd, "登入日期(迄)");
	if(form1.q_loginDateEnd.value != "")	alertStr += checkEmpty(form1.q_loginDateStart, "登入日期(起)");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}

function queryOne(id){}

function init() {
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

$(document).ready(function() {
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
		switch (buttonName){
			case "doQueryAll":
				$('#state').val("queryAll");
				break;	
		}
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    return true;
	};
});
</script>
</head>

<body topmargin="0" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9006'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>


<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form" width="15%">使用者帳號：</td>
		<td class="td_form_white" width="35%">
			<input class="field_Q" type="text" name="q_loginId" size="15" maxlength="15" value="<%=obj.getQ_loginId()%>">
		</td>
		<td class="td_form" width="15%">使用者姓名：</td>
		<td class="td_form_white" width="35%">
			<input class="field_Q cmex" type="text" name="q_loginName" size="15" maxlength="15" value="<%=obj.getQ_loginName()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="15%">登入日期區間：</td>
		<td colspan="3" class="td_form_white" width="85%">
			<%=View.getPopCalendar("field_Q", "q_loginDateStart", obj.getQ_loginDateStart())%>
			~<%=View.getPopCalendar("field_Q", "q_loginDateEnd", obj.getQ_loginDateEnd())%>
			&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="查　詢" onClick="whatButtonFireEvent(this.name)" >
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer" style="display:none;">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" ><a class="text_link_w">序號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">使用者帳號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">使用者名稱</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">登入日期</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">連線IP</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">登入狀態</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {false,false,false,false,false};
	boolean displayArray[] = {true,true,true,true,true};
	String[] alignArray = {"center","center","center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="N" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="N" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>

<%@ include file="../../home/<USER>" %>