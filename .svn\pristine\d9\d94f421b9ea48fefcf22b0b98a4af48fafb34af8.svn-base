<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="functionBanner" scope="request" class="com.kangdainfo.tcfi.view.common.FunctionBanner">
	<jsp:setProperty name="functionBanner" property="*"/>
</jsp:useBean>
<table width="100%" cellspacing="0" cellpadding="0">
<tr><td class="td_default_banner"><c:out value="${functionBanner.functionBanner}" />&nbsp;</td></tr>
</table>
<script type="text/javascript">
<!--
$(window).load(function(){
	if (isObj(top) && isObj(top.fbody) && isObj(top.fbody.menuleft)) top.fbody.menuleft.doMax();
});
//-->
</script>