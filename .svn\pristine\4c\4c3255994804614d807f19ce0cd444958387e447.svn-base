package com.kangdainfo.tcfi.view.pre;

import java.util.ArrayList;
import java.util.Map;

import com.kangdainfo.ServiceGetter;
import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.common.util.SuperBean;

/**
 * 異動內容
 *
 */
public class PRE3001_04 extends SuperBean {

	private String banNo;

	@Override
	public ArrayList<?> doQueryAll() throws Exception {
		java.util.ArrayList<String[]> arrList = new java.util.ArrayList<String[]>();
		if(!"".equals(getBanNo())) {
			SQLJob sqljob = new SQLJob();
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" to_char(A.PREFIX_NO) PREFIX_NO");
			sqljob.appendSQL(",to_char(A.BAN_NO) BAN_NO");
			sqljob.appendSQL(",A.COMPANY_NAME");
			sqljob.appendSQL(",NULL AS APPLY_KIND");
			sqljob.appendSQL(",A.STATUS_DESCR AS APPROVE_RESULT");
			sqljob.appendSQL(",'9999999' AS APPROVE_DATE");
			sqljob.appendSQL(",NULL AS RESERVE_DATE");
			sqljob.appendSQL(",A.RESP_NAME AS APPLY_NAME");
			sqljob.appendSQL(",'CEDB2000' AS KIND");
			sqljob.appendSQL("FROM CEDB2000 A");
			sqljob.appendSQL("WHERE A.BAN_NO=?");
			sqljob.addParameter(getBanNo());
			sqljob.appendSQL("UNION");
			sqljob.appendSQL("SELECT");
			sqljob.appendSQL(" to_char(A.PREFIX_NO)");
			sqljob.appendSQL(",to_char(A.BAN_NO)");
			sqljob.appendSQL(",A.COMPANY_NAME");
			sqljob.appendSQL(",nvl(c13.code_name,decode(a.apply_kind,'1','設立','2','變更','')) as APPLY_KIND");
			sqljob.appendSQL(",(select code_name from system_code where code_kind='05' and code=A.APPROVE_RESULT) as APPROVE_RESULT");
			sqljob.appendSQL(",to_char(A.APPROVE_DATE)");
			sqljob.appendSQL(",A.RESERVE_DATE");
			sqljob.appendSQL(",(case when c.apply_law_name is not null then c.apply_law_name||' 代表人：'||a.apply_name else a.apply_name end ) AS APPLY_NAME");
			sqljob.appendSQL(",'CEDB1000' AS KIND");
			sqljob.appendSQL("FROM CEDB1000 A");
			sqljob.appendSQL(" left outer join cedb1023 b on a.prefix_no = b.prefix_no" );
			sqljob.appendSQL(" left outer join system_code c13 on c13.code_kind='13' and c13.code=b.change_type");
			sqljob.appendSQL(" left join eicm.cedb1022 c on a.prefix_no = c.prefix_no " );
			sqljob.appendSQL("WHERE A.BAN_NO=?");
			sqljob.addParameter(getBanNo());
			sqljob.appendSQL("ORDER BY APPROVE_DATE desc, PREFIX_NO");
			java.util.List<Map<String,Object>> objList = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
			if (objList != null && objList.size() > 0) {
				java.util.Iterator<Map<String,Object>> it = objList.iterator();
				Map<String,Object> o;
				String[] rowArray = new String[9];
				while (it.hasNext()) {
					o = it.next();
					rowArray = new String[9];
					rowArray[0] = Common.get(o.get("PREFIX_NO"));
					rowArray[1] = Common.get(o.get("BAN_NO"));
					rowArray[2] = Common.get(o.get("COMPANY_NAME"));
					rowArray[3] = Common.get(o.get("APPLY_KIND"));
					rowArray[4] = Common.get(o.get("APPROVE_RESULT"));
					rowArray[5] = ("9999999".equals(Common.get(o.get("APPROVE_DATE")))?"":Common.get(o.get("APPROVE_DATE")));
					rowArray[6] = Common.get(o.get("RESERVE_DATE"));
					rowArray[7] = Common.get(o.get("APPLY_NAME"));
					rowArray[8] = Common.get(o.get("KIND"));
					arrList.add(rowArray);	
				}
			}
		}
		return arrList;
	}

	public static String getQuerylist(boolean primaryArray[], boolean displayArray[], String[] arrAlign,
    		ArrayList<String[]> objList, String queryAllFlag, boolean withListNo) {
    	int i, counter=0;
    	boolean trFlag = false, even = false;
    	String pk = "";

		String kind = "";
    	StringBuilder sb = new StringBuilder();
    	if (objList!=null && objList.size()>0) {
			String rowArray[] = new String[primaryArray.length];
			java.util.Iterator<String[]> it = objList.iterator();
			
			//boolean defaultRow = true;
			String defaultKey = "null";
			for(i=0;i<primaryArray.length;i++){
				if (primaryArray[i]) defaultKey = "";
			}

			while(it.hasNext()) {
				rowArray= it.next();
				kind = rowArray[8];
				counter++;
				String classTR="listTROdd", classTD = "listTDOdd";				
				if (even) {
					classTR = "listTREven";
					classTD = "listTDEven";
				}				
			
				pk = "";
				for(i=0;i<primaryArray.length;i++){			
					if (primaryArray[i]) pk+=Common.escapeReturnChar(rowArray[i]);
				}				
				StringBuilder v = new StringBuilder().append(defaultKey);
				for(i=0;i<primaryArray.length;i++){
					if (primaryArray[i]) {
						if (trFlag) {
							v.append(",'").append(Common.escapeReturnChar(rowArray[i])).append("'");
						} else {
							v.append("'").append(Common.escapeReturnChar(rowArray[i])).append("'");
							trFlag = true;
						}
					}
				}
				
				//顯示TR
				sb.append("<tr id=\"").append("listContainerRow").append(pk).append("\"");
				sb.append(" class='").append(classTR).append("'");
				sb.append(" onmouseover=\"this.className='listTRMouseover'\"");
				sb.append(" onmouseout=\"this.className='").append(classTR).append("'\"");
				sb.append(" onClick=\"listContainerRowClick('").append(pk).append("');queryOne(").append(v).append(");\"");
				sb.append(" >\n");
				//顯示TD
				if (withListNo) sb.append(" <td class='").append(classTD).append("' style=\"text-align:right\">").append(counter).append("</td>\n");
				for(i=0;i<displayArray.length;i++){
					if (displayArray[i]) {
						if (arrAlign!=null && arrAlign.length>0) {
							sb.append(" <td class='").append(classTD).append("' style=\"text-align:").append(arrAlign[i]).append("\">");
						} else {
							sb.append(" <td class='").append(classTD).append("' >");
						}
						if(i==2) {
							if( "CEDB2000".equalsIgnoreCase(kind) ) {
								sb.append("<font color=indigo>");
							} else {
								sb.append("<font color=green>");
							}
							sb.append(Common.get(rowArray[i]));
						} else {
							sb.append(Common.get(rowArray[i]));
						}
						sb.append("</font");
						sb.append("</td>\n");
					}
				}
				sb.append("</tr>\n");
				trFlag = false;
				even = !even;
			}
    	} else {
    		if ("true".equals(queryAllFlag)) sb.append(" <tr class='highLight' ><td class='listTD' colspan='100'>").append("查無資料，請您重新輸入查詢條件！").append("</td></tr>");
    	}
		return sb.toString();
    }    

	@Override
	public Object doQueryOne() throws Exception {return null;}
	@Override
	public void doCreate() throws Exception {}
	@Override
	public void doUpdate() throws Exception {}
	@Override
	public void doDelete() throws Exception {}

	public String getBanNo() {return checkGet(banNo);}
	public void setBanNo(String s) {this.banNo = checkSet(s);}

}