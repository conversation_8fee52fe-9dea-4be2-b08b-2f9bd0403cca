<%@page import="com.kangdainfo.tcfi.model.eicm.bo.Cedb1004"%>
<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String q = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	SQLJob sqljob = new SQLJob();
	sqljob.appendSQL("select * from cedb1004 where prefix_no = ?");
	sqljob.addParameter(q);
	List datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().query(sqljob, new BeanPropertyRowMapper(Cedb1004.class));
	if (null != datas && !datas.isEmpty()) {
		out.write(gson.toJson(datas));
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>