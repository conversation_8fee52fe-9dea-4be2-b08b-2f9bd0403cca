<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.test.QueryMoeaic">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList" scope="page" class="java.util.ArrayList"/>      
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.test.QueryMoeaic)obj.queryOne();
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function queryOne(idntpk,prefixNo,banNo,updCode,updDate){
	form1.id.value=idntpk;
	form1.prefixNo.value=prefixNo;
	form1.banNo.value=banNo;
	form1.updCode.value=updCode;
	form1.updDate.value=updDate;
}
function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">

<form id="form1" name="form1" method="post" autocomplete="off">
<!--Query區============================================================-->
<div id="queryContainer" style="width:300px;height:100px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable" border="1">
	<tr>
		<td class="queryTDLable">預查編號：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_prefixNo" size="15" maxlength="10" value="<%=obj.getQ_prefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td>
<input type="hidden" name="id" value="<%=obj.getIdntpk()%>">
<input type="hidden" name="state" value="<%=obj.getState()%>">
<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
<jsp:include page="../../home/<USER>" >
	<jsp:param name="btnInsert" value="N" />
	<jsp:param name="btnQueryAll" value="Y" />
	<jsp:param name="btnUpdate" value="N" />
	<jsp:param name="btnDelete" value="N" />
	<jsp:param name="btnClear" value="Y" />
	<jsp:param name="btnConfirm" value="N" />
	<jsp:param name="btnListPrint" value="N" />
	<jsp:param name="btnListHidden" value="N" />
</jsp:include>
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:auto;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form">預查編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="prefixNo" size="25" value="<%=obj.getPrefixNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">統一編號：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="banNo" size="25" value="<%=obj.getBanNo()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">變更代碼：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updCode" size="25" value="<%=obj.getUpdCode()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form">變更日期：</td>
		<td class="td_form_white">
			<input class="field" type="text" name="updDate" size="25" value="<%=obj.getUpdDate()%>">
		</td>
	</tr>
	</table>
<c:import url="../common/msgbar.jsp">
  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
</c:import>
	</div>
</td></tr>

<!-- PAGE AREA -->
<tr><td>
<% request.setAttribute("QueryBean",obj);%>
<jsp:include page="../../home/<USER>" />
</td></tr>
<!-- PAGE AREA -->

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH"><a class="text_link_w" href="#">預查編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">統一編號</a></th>
		<th class="listTH"><a class="text_link_w" href="#">變更代碼</a></th>
		<th class="listTH"><a class="text_link_w" href="#">變更日期</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = { true, true, true, true, true};
	boolean displayArray[] = {false, true, true, true, true};
	String[] alignArray = {"center","center","center","center","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),false));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>