<%@ page pageEncoding="UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

String codeKind = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
String code = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("code")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(codeKind) && !"".equals(code)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT CODE, CODE_NAME FROM SYSTEM_CODE WHERE CODE_KIND=? AND CODE=?");
		sqljob.addParameter(codeKind);
		sqljob.addParameter(code);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEicmGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			out.write(gson.toJson(datas.get(0)));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>