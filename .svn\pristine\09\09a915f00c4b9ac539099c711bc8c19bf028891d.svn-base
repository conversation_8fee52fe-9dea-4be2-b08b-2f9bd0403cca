<%@page import="org.springframework.jdbc.core.BeanPropertyRowMapper"%>
<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*"%>
<%@ page import="com.kangdainfo.moea.bo.*"%>
<%@ page import="com.kangdainfo.tcfi.view.pre.PRE1001"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("telixNo")));
String changeType = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("changeType")));
String banNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("banNo")));
try {
	String result = PRE1001.checkNoNeedPay( telixNo, changeType, banNo);
	if ( result != null && !"".equals(result) ) {
		out.write(result);
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>