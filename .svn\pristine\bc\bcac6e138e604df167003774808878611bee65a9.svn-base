package tw.org.moea.online.oss;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 2.2.5
 * Mon Jun 17 16:05:27 CST 2013
 * Generated source version: 2.2.5
 * 
 */
 
@WebService(targetNamespace = "http://tw/org/moea/online/oss", name = "CaseStatus")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface CaseStatus {

    @WebResult(name = "return", targetNamespace = "http://tw/org/moea/online/oss", partName = "return")
    @WebMethod(operationName = "UpdateCaseStatus")
    public java.lang.String updateCaseStatus(
        @WebParam(partName = "inBean", name = "inBean")
        UpdateCaseStatusBEAN inBean
    );
}
