package com.kangdainfo.tcfi.loader;

/**
 * 預查種類
 *
 */
public class SystemCode13Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_13";
	private static final String CODE_KIND = "13";//13:預查種類

	//singleton
	private static SystemCode13Loader instance;
	public SystemCode13Loader() {
		if (SystemCode13Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode13Loader.instance);
		}
		SystemCode13Loader.instance = this;
	}
	public static SystemCode13Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}

}