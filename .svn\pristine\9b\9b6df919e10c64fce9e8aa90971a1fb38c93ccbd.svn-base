<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.google.gson.*"%>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE3003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");

try {
	Gson gson = new GsonBuilder().create();
	Map<String,String> map = new HashMap<String,String>();
	map.put("COUNT",Common.get(obj.searchCount()));
	map.put("QUERY_STR",Common.get(obj.getQueryStr()));
	out.write(gson.toJson(map));
} catch (Exception e) {
	e.printStackTrace();
}
%>