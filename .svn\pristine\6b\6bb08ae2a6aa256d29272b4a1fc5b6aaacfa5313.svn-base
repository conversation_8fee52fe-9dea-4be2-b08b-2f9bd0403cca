package com.kangdainfo.tcfi.loader;

/**
 * 申登機關
 *
 */
public class SystemCode08Loader extends SystemCodeLoader {
	private static final String CACHE_NAME = "CACHE_NAME_SYSTEM_CODE_08";
	private static final String CODE_KIND = "08";//08:申登機關

	//singleton
	private static SystemCode08Loader instance;
	public SystemCode08Loader() {
		if (SystemCode08Loader.instance != null) {
			throw new RuntimeException(
					this.getClass().getName()
					+ "is designed to be a Singleton, the instance already exist:"
					+ SystemCode08Loader.instance);
		}
		SystemCode08Loader.instance = this;
	}
	public static SystemCode08Loader getInstance() {return instance;}
	@Override
	protected String getCacheName() {return CACHE_NAME;}
	@Override
	protected String getCodeKind() {return CODE_KIND;}
}