package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedbc050;

public class Cedbc050Dao extends BaseDaoJdbc implements RowMapper<Cedbc050> {

	private static final String SQL_findAll = "SELECT * FROM CEDBC050";
	public List<Cedbc050> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<Cedbc050>) getJdbcTemplate().query(sqljob.getSQL(), this);
	}

	public Cedbc050 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedbc050 obj = null;
		if(null!=rs) {
			obj = new Cedbc050();
			obj.setUnitCode(rs.getString("UNIT_CODE"));
			obj.setUnitName(rs.getString("UNIT_NAME"));
			obj.setUnitAddress(rs.getString("UNIT_ADDRESS"));
			obj.setUnitFax(rs.getString("UNIT_FAX"));
			obj.setUnitType(rs.getString("UNIT_TYPE"));
			obj.setUnitMailCode(rs.getString("UNIT_MAIL_CODE"));
			obj.setUnitPhone(rs.getString("UNIT_PHONE"));
			obj.setRemark(rs.getString("REMARK"));
		}
		return obj;
	}

}