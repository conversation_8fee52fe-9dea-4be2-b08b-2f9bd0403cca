package com.kangdainfo.tcfi.service;

import com.kangdainfo.tcfi.model.eicm.dao.RestrictionDao;
import com.kangdainfo.tcfi.model.eicm.dao.RestrictionItemDao;

public interface Pre8011Service {
	public RestrictionDao getRestrictionDao();
	public void setRestrictionDao(RestrictionDao dao);
	public RestrictionItemDao getRestrictionItemDao();
	public void setRestrictionItemDao(RestrictionItemDao dao);
	public void doSave( String itemCode, String[] idListWillChange, String userId ) throws Exception ;
}