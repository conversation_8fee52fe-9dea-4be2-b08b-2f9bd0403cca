<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="head.jsp" %>
<jsp:include page="secure.jsp">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9003" />
</jsp:include>
<jsp:useBean id="obj" scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9003">
	<jsp:setProperty name="obj" property="*"/>
</jsp:useBean>
<%
	obj.setSysid("-11");
	String openNodeID = Common.checkGet(ESAPI.encoder().encodeForHTML(request.getParameter("openNodeID")));
%>
<html>
<head>
	<title>Authority Center</title>
	<link rel="stylesheet" href="../js/dtree.css" type="text/css" />
	<script type="text/javascript" src="../js/dtree.js"></script>
	<script type="text/javascript">
	var popWinManage;
	var popWinManageFlag=false;
	
	function openTreeNode(tid) {	
		var dt = top.frames['dTreeContent'].d;
		dt.openTo(tid, true);
	}	
	
	function reloadTree(tid) {
		try {
			if (popWinManage!=null) popWinManage.close();		
		} catch(e) {}		
		window.parent.frames['dTreeContent'].location.href = "blank.htm";
		window.location.href = "dTreeManage.jsp?openNodeID="+tid; //sScript"?openNodeID="+tid;				
	}
	
	function getURLParameter( url, name )
	{
	  var regexS = "[\\?&]"+name+"=([^&#]*)";
	  var regex = new RegExp( regexS );
	  var results = regex.exec( url );
	  if( results == null )
	    return "";
	  else
	    return results[1];
	}
	
	function popFormFolders(url,event) { 	
		var s = getURLParameter(url,'exeKind');		
		var sid = getURLParameter(url,'sid');
		var fid = getURLParameter(url,'fid');
		if ((fid=="-1") && (s=="delete" || s=="update")) {
			alert("對不起，不允許從根節點執行此動作！");
			return false;
		}			
		if (s=="delete") {
			popWinManage = window.open(url,'dTreeDelete','scrollbars=1, resizable=yes, status=yes, toolbar=no,menubar=no,width=550,height=390');			
			//window.parent.frames['dTreeContent'].location.href = "blank.htm";			
		} else {
			form1.sid.value = sid;
			form1.fid.value = fid;
			form1.exeKind.value = s;		
			form1.submit();
		}
	}
	
	function disableLeftClick() {
		if (event.button==1) return false;
	}
	</script>
</head>	
<body id="body" onload="init();">
<form name="form1" id="form1" action="dTreeForm.jsp" target="dTreeContent">
<input type="hidden" name="sid" value="">
<input type="hidden" name="fid" value="">
<input type="hidden" name="exeKind" value="">

<div class="dtree">
	<a href="javascript: d.openAll();"><%=application.getInitParameter("open_all")%></a> | <a href="javascript: d.closeAll();"><%=application.getInitParameter("close_all")%></a><br /><br />
	<div id="dtree">
	<script type="text/javascript">
		<!--
		var d = new dTree('d');
		d.config.folderLinks=true;
		d.config.useCookies=false;
		d.config.target = 'dTreeContent';
		//d.config.useIcons = false;
		//d.config.objForm =  document.form1;
		//d.config.checkBoxName = "auth";
		
		d.add(<%=obj.getSysid()%>,-1,'功能選單','dTreeForm.jsp?sid=<%=obj.getSysid()%>&fid=-1','','','','','','','');
		<%
		out.write(obj.buildManageTree());
		%>

		document.write(d);

		function init() {		
			var treeNode = "<%=openNodeID%>";	
			if (treeNode.length>0) {
				if (treeNode=="0" || treeNode=="-1") {} {
					d.openTo(treeNode);
				}				
			}
		}
		
		
		//-->		
	</script>
	</div>
</div>

</form>


<br /><br /><br /><br /><br />
<p></p><p></p><p></p><p></p><p></p>
	<div id="divContext" class="contextmenu" style="border: 1px solid gray; display: none; position: absolute; background-color: #ffffd2;">
		<a id="aInsertNav" href="">新增</a><br />
		<a id="aUpdateNav" href="">修改</a><br />
		<a id="aDeleteNav" href="" onclick="return confirm('確定要刪除該節點和其下的所有子節點?');CloseContext();">刪除</a><br />
	</div>
	
	<script type="text/javascript" src="../js/contextmenu.js"></script>
	
</body>
</html>

