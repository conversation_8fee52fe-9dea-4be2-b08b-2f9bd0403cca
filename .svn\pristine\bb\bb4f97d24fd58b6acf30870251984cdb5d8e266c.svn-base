<%
/**
程式目的：申辦案件(收文方式)統計分析表
程式代號：pre4003
程式日期：1030505
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
*/
%>
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE4003">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE4003" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	objList = obj.queryAll();
} // end if
else if ( "preview".equals( obj.getState() ) ) {
	java.io.File report = obj.doPrintPdf();
	if(null!=report)
	{
		//輸出 檔案 到client端
		obj.outputFile(response, report, "PRE4003.pdf");
		out.clear();
		out = pageContext.pushBody();
	}
	else
	{
		//查無資料時提示訊息
		//obj.setErrorMsg(com.kangdainfo.common.util.TbmcConstants.MESSAGE_NO_DATA_FOUND);
    }
	
} // end else if
else {
	// do nothing
} // end else

%>

<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
function checkField(){
	var alertStr="";
	alertStr += checkEmpty(form1.q_DateStart,"日期起始時間");
	alertStr += checkEmpty(form1.q_DateEnd,"日期結束時間");
	alertStr += checkDate(form1.q_DateStart,"日期起始時間");
	alertStr += checkDate(form1.q_DateEnd,"日期結束時間");
	if(alertStr.length!=0){ alert(alertStr); return false; }
	//beforeSubmit();
	return true;
}

function init() {
	if($('#state').val() != "init")
		$('#listContainer').show();	
}

function queryOne() {}

$(document).ready(function() {
	//override.
	localButtonFireListener.whatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
		switch (buttonName){
			case "doPrintPdf":
				$('#state').val("preview") ;
				var target = 'PRE4003_'+randomUUID().replace(/\-/g,"");
				window.open("",target);
				form1.target = target;
				form1.submit();
				form1.target = '';
				break;
			case "mail":
				$('#state').val("mail") ;
				form1.submit();
				break;
			case "gmail":
				$('#state').val("gmail") ;
				form1.submit();
				break;
			case "doQueryAll":
				$('#state').val("queryAll") ;
				break;
			case "doClear":
				form1.q_DateStart.value = "";
				form1.q_DateEnd.value = "";
				break;
		} // switch
	};
	localButtonFireListener.beforeWhatButtonFireEvent = function(buttonName){
	    // do nothing,for override.
	    return true;
	};
});

function doSomeCheck(){
	if ( checkField() ) {
		var y = "&nbsp";
		document.getElementById("ERRMSG").innerHTML = y;
		var dateStart = form1.q_DateStart.value;
		var dateEnd = form1.q_DateEnd.value;
		var withLimit = form1.withLimit.value;
		var x = getRemoteData(getVirtualPath() + '/tcfi/ajax/jsonPre4003.jsp?dateStart='+dateStart+'&dateEnd='+dateEnd+'&withLimit='+withLimit);
		if ( x == 'ok'  )
			whatButtonFireEvent("doPrintPdf");
		else {
			document.getElementById("ERRMSG").innerHTML = x;
		} // else
	} 	
}
</script>
</head>
<body topmargin="5" onLoad="init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" >

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE4003'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<tr><td class="bg" >
	<div id="formContainer" style="height:auto">
	<table class="table_form" width="100%" height="100%">  
		<tr>
			<td class="td_form" width="100px"><font color="red">*</font>收件日期：</td>
			<td class="td_form_white">
				(起)<%=View.getPopCalendar("field_Q","q_DateStart",obj.getQ_DateStart()) %>
				~
				(迄)<%=View.getPopCalendar("field_Q","q_DateEnd",obj.getQ_DateEnd()) %>
				<input type="radio" name="withLimit" value="1" <%="1".equals(obj.getWithLimit())? "checked":"" %> checked>公司案件&nbsp;
				<input type="radio" name="withLimit" value="2" <%="2".equals(obj.getWithLimit())? "checked":"" %>>有限合夥案件&nbsp;
				<input type="radio" name="withLimit" value="3" <%="3".equals(obj.getWithLimit())? "checked":"" %>>全部
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doClear" name="doClear" value="重新輸入" onClick="whatButtonFireEvent(this.name)" >
				&nbsp;<input class="toolbar_default" type="submit" followPK="false" id="doQueryAll" name="doQueryAll" value="執行查詢" onClick="whatButtonFireEvent(this.name)" >
				&nbsp;<input class="toolbar_default" type="button" followPK="false" id="doPrintPdf" name="doPrintPdf" value="報表列印" onClick="doSomeCheck()" >
				<!-- &nbsp;<input class="toolbar_default" type="button" followPK="false" id="mail" name="mail" value="測試mail功能" onClick="whatButtonFireEvent(this.name)">  -->
				<!-- &nbsp;<input class="toolbar_default" type="button" followPK="false" id="gmail" name="gmail" value="測試gmail功能" onClick="whatButtonFireEvent(this.name)">  -->
			</td>
		</tr>     
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- LIST AREA -->
<tr><td nowrap class="bgList">
	<div id="listContainer" style="display:none;">
	<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
  		<thead id="listTHEAD">
			    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">申請方式</a></th>
			    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">設立案件數</a></th>
			    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">變更案件數</a></th>
			    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',4,false);" href="#">合計</a></th>
			    <th class="listTH" style="text-align:left"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">比例</a></th>
			</tr>
		</thead>
		<tbody id="listTBODY">
		  <%
		  boolean primaryArray[] = {true,false,false,false,false};
		  boolean displayArray[] = {true,true,true,true,true};
		  String[] alignArray = {"left", "left","left","left","left"};
		  out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,"true",false));
		  %>
		</tbody>
	</table>
	</div>
</td></tr>
<!-- LIST AREA -->

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar">
	<table width="100%" border="0" CELLPADDING="0" CELLSPACING="2">
		<tr><td style="text-align:left;">
			<input type="hidden" id="state" name="state" value="<%=obj.getState()%>">
			<jsp:include page="../../home/<USER>" >
				<jsp:param name="btnInsert" value="N" />
				<jsp:param name="btnQueryAll" value="N" />
				<jsp:param name="btnUpdate" value="N" />
				<jsp:param name="btnDelete" value="N" />
				<jsp:param name="btnClear" value="N" />
				<jsp:param name="btnConfirm" value="N" />
				<jsp:param name="btnListHidden" value="N" />
				<jsp:param name="btnPreview" value="N" />
				<jsp:param name="btnCancel" value="N" />
				<jsp:param name="btnListPrint" value="N" />
			</jsp:include>
		</td></tr>
	</table>
</td></tr>
<!-- TOOLBAR AREA -->

</table>
</form>
</body>
</html>