package com.kangdainfo.tcfi.model.icms.bo;

import java.util.Date;

import com.kangdainfo.persistence.BaseModel;

/**
 * 機關代碼檔(CSMD_UNIT_CODE)
 *
 */
public class CsmdUnitCode extends BaseModel {
	private static final long serialVersionUID = 1L;
	/** 機關代碼 */
	private String unitCode;
	/** 縣市鄉鎮代碼 */
	private String areaCode;
	/** 機關識別碼 */
	private String oid;
	/** 機關單位簡碼 */
	private String simpleCode;
	/** 機關名稱 */
	private String unitName;
	/** 郵遞區號 */
	private String zipCode;
	/** 機關地址 */
	private String unitAdd;
	/** 機關電話 */
	private String unitTelNo;
	/** 機關傳真 */
	private String unitFaxNo;
	/** 發文字 */
	private String dispatchWd;
	/** 啟用 */
	private String enable;
	/** 資料異動日期 */
	private Date updateDate;
	/** 資料異動人員 */
	private String updateUser;
	/** 機關類別 */
	private String orgType;

	public String getUnitCode() {return unitCode;}
	public void setUnitCode(String s) {this.unitCode = s;}
	public String getAreaCode() {return areaCode;}
	public void setAreaCode(String s) {this.areaCode = s;}
	public String getOid() {return oid;}
	public void setOid(String s) {this.oid = s;}
	public String getSimpleCode() {return simpleCode;}
	public void setSimpleCode(String s) {this.simpleCode = s;}
	public String getUnitName() {return unitName;}
	public void setUnitName(String s) {this.unitName = s;}
	public String getZipCode() {return zipCode;}
	public void setZipCode(String s) {this.zipCode = s;}
	public String getUnitAdd() {return unitAdd;}
	public void setUnitAdd(String s) {this.unitAdd = s;}
	public String getUnitTelNo() {return unitTelNo;}
	public void setUnitTelNo(String s) {this.unitTelNo = s;}
	public String getUnitFaxNo() {return unitFaxNo;}
	public void setUnitFaxNo(String s) {this.unitFaxNo = s;}
	public String getDispatchWd() {return dispatchWd;}
	public void setDispatchWd(String s) {this.dispatchWd = s;}
	public String getEnable() {return enable;}
	public void setEnable(String s) {this.enable = s;}
	public Date getUpdateDate() {return updateDate;}
	public void setUpdateDate(Date d) {this.updateDate = d;}
	public String getUpdateUser() {return updateUser;}
	public void setUpdateUser(String s) {this.updateUser = s;}
	public String getOrgType() {return orgType;}
	public void setOrgType(String s) {this.orgType = s;}

}