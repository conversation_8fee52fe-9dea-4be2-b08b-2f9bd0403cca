package com.kangdainfo.tcfi.model.eicm.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.Common;
import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.eicm.bo.Cedb1101;

public class Cedb1101Dao extends BaseDaoJdbc implements RowMapper<Cedb1101> {

	private static final String SQL_findByPrefixNo = "SELECT * FROM CEDB1101 WHERE PREFIX_NO = ? ORDER BY PREFIX_NO, SEQ_NO";
	public List<Cedb1101> findByPrefixNo(String prefixNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNo);
		sqljob.addParameter(prefixNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		return getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	private static final String SQL_findByPrefixNoAndSeqNo = "SELECT * FROM CEDB1101 WHERE PREFIX_NO = ? AND SEQ_NO = ?";
	public Cedb1101 findByPrefixNoAndSeqNo(String prefixNo, String seqNo) {
		SQLJob sqljob = new SQLJob(SQL_findByPrefixNoAndSeqNo);
		sqljob.addParameter(prefixNo);
		sqljob.addParameter(seqNo);
		if (logger.isDebugEnabled()) logger.debug(sqljob);
		List<Cedb1101> list = getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
		return list.isEmpty()?null:list.get(0);
	}

	private static String SQL_insert = "INSERT INTO CEDB1101 (" +
			"PREFIX_NO,SEQ_NO,COMPANY_NAME,APPROVE_RESULT,REMARK" +
			") VALUES (" +
			"?,?,?,?,?" +
			")";

	public int insert(Cedb1101 obj) {
		if(null==obj) return 0;
		SQLJob sqljob = new SQLJob(SQL_insert);
		sqljob.addParameter(obj.getPrefixNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getSeqNo());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getCompanyName());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getApproveResult());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		sqljob.addParameter(obj.getRemark());
		sqljob.addSqltypes(java.sql.Types.VARCHAR);
		return getJdbcTemplate().update(sqljob.getSQL(), sqljob.getParametersArray(), sqljob.getSqltypesArray());
	}

	@Override
	public Cedb1101 mapRow(ResultSet rs, int idx) throws SQLException {
		Cedb1101 obj = null;
		if (null != rs) {
			obj = new Cedb1101();
			obj.setPrefixNo(Common.get(rs.getString("PREFIX_NO")));
			obj.setSeqNo(Common.get(rs.getString("SEQ_NO")));
			obj.setCompanyName(Common.get(rs.getString("COMPANY_NAME")));
			obj.setApproveResult(Common.get(rs.getString("APPROVE_RESULT")));
			obj.setRemark(Common.get(rs.getString("REMARK")));
		}
		return obj;
	}

}