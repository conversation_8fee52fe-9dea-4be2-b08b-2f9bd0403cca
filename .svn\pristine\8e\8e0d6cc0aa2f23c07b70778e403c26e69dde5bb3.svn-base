package com.kangdainfo.tcfi.model.icms.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

import org.springframework.jdbc.core.RowMapper;

import com.kangdainfo.common.util.SQLJob;
import com.kangdainfo.persistence.jdbc.BaseDaoJdbc;
import com.kangdainfo.tcfi.model.icms.bo.CsmdRegunit;

public class CsmdRegunitDao extends BaseDaoJdbc implements RowMapper<CsmdRegunit> {

	private static final String SQL_defaultOrder = "order by REG_UNIT_CODE";

	private static final String SQL_findAll = "SELECT * FROM ICMS.CSMD_REGUNIT WHERE ENABLE = 'Y'";
	public List<CsmdRegunit> findAll() {
		SQLJob sqljob = new SQLJob(SQL_findAll);
		sqljob.appendSQL(SQL_defaultOrder);
		if(logger.isDebugEnabled()) logger.debug(sqljob);
		return (List<CsmdRegunit>) getJdbcTemplate().query(sqljob.getSQL(), sqljob.getParametersArray(), this);
	}

	public CsmdRegunit mapRow(ResultSet rs, int idx) throws SQLException {
		CsmdRegunit obj = null;
		if(null!=rs) {
			obj = new CsmdRegunit();
			obj.setRegUnitCode(rs.getString("REG_UNIT_CODE"));
			obj.setRegUnitName(rs.getString("REG_UNIT_NAME"));
			obj.setUnitCode(rs.getString("UNIT_CODE"));
			obj.setEnable(rs.getString("ENABLE"));
			obj.setUpdateDate(rs.getDate("UPDATE_DATE"));
			obj.setUpdateUser(rs.getString("UPDATE_USER"));
			obj.setCtrlItem(rs.getString("CTRL_ITEM"));
			obj.setOid(rs.getString("OID"));
		}
		return obj;
	}

}