<%@ page contentType="text/html;charset=UTF-8"%>
<%@ include file="head.jsp"%>
<%@ page import="com.kangdainfo.tcfi.model.eicm.bo.*" %>
<html>
<head>
<meta http-equiv="cache-control" content="no-cache" />
<meta http-equiv="expires" content="0" />
<meta http-equiv="pragma" content="no-cache" />
<title>Notice</title>
<link rel="stylesheet" href="../css/default.css" type="text/css">
<link rel="stylesheet" href="../js/style.css" type="text/css">
<style>
body {
	margin-top: 0px;
	margin-left: 5px;
	margin-right: 0px;
	margin-bottom: 0px;
}

.showTD {
	padding: 2px 5px 2px 2px;
	border-left: 1px solid silver;
	border-bottom: 1px solid silver;
	border-right: 1px solid silver;
	text-align: left;
	height: 24px;
}

.th {
	font-weight: normal;
	z-index: 20;
	padding: 4px 2px 2px 2px;
	color: white;
	text-align: center;
	height: 23;
	border-left: 1px solid white;
	border-bottom: 1px solid white;
	position: relative;
}

.sLink2:link {
	font-family: "細明體", "新細明體";
	color: #C90026;
	text-decoration: none
}

.sLink2:visited {
	font-family: "細明體", "新細明體";
	color: #C90026;
	text-decoration: none
}

.sLink2:active {
	font-family: "細明體", "新細明體";
	color: #000099
}

.sLink2:hover {
	font-family: "細明體", "新細明體";
	text-decoration: none;
	color: #FF7E00;
}
</style>
<script type="text/javascript" src="../js/function.js"></script>
<script type="text/javascript">
function popBoard(newsID){
	window.open("popBoard.jsp?newsID="+newsID,"","top=100,left=210,width=600,height=420,scrollbars=yes,resizable=yes");	
}

function nodeclicked(){
	try {
		top.fbody.mainhead.document.getElementById("pathname").innerHTML = "<%=application.getServletContextName()%> > > 系統公告 ";
	} catch(e) {}
}
</script>
</head>
<body onLoad="nodeclicked();">
<table width="100%" border="0" cellpadding="0" cellspacing="0" id="table24">

<tr><td valign="top">
	<table width="100%" border="0" cellpadding="0" cellspacing="0" id="table24">
	<tr>
	<td valign="top">
		<table width="90%" border="0" align="center" cellpadding="0" cellspacing="0">
			<tr>
				<td>
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<td><img src="../images/welcome/notice_header_left.gif" alt="系統公告" width="67" height="46" /></td>
							<td width="100%" background="../images/welcome/notice3_01.gif" class="home_title">※系統公告</td>
							<td class="home_title"><img src="../images/welcome/notice_header_right.gif" width="18" height="46" /></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table width="100%" border="0" cellspacing="0" cellpadding="0">
<%
String dateStart = Datetime.getYYYMMDD();
String dateEnd = Datetime.getDateAdd("d", 7, dateStart);
List<CmpyMemoInfo> cmpyMemoDatas = ServiceGetter.getInstance().getPrefixService().getCmpyMemoInfoByCondition("","",dateStart,dateEnd);
String company ="";
String reserveDesc = "";
String add15Days = "";
Cedb1000 cedb1000 = new Cedb1000();
for ( CmpyMemoInfo cmpyMemoData : cmpyMemoDatas ) {
	reserveDesc="";
	add15Days="";
	cedb1000 = ServiceGetter.getInstance().getPrefixService().getCedb1000ByPrefixNo(cmpyMemoData.getRcvNo());
	if (cedb1000 != null) {
		add15Days = Datetime.getDateAdd("d", 15, cedb1000.getReserveDate());
		reserveDesc = "預查名稱保留中";
		if ("03".equals(cedb1000.getCompanyStus()))	
			reserveDesc = "已完成公司登記";
		else if ("01".equals(cedb1000.getCompanyStus()) || "00".equals(cedb1000.getCompanyStus())) 
			reserveDesc = "公司登記申請中";
		else if (cedb1000.getReserveDate()==null || cedb1000.getReserveDate().length() == 0)
			reserveDesc = "本案件尚無保留期限";
		else if (dateStart.compareTo(add15Days) > 0) // 過期
			reserveDesc = "保留期限已過期";
	}
	if ("".equals(company))
		company += cmpyMemoData.getRcvNo()+ " " + cmpyMemoData.getReserveDate() + " " +cmpyMemoData.getCompanyName() + " " +reserveDesc ;
	else
		company += "、"+cmpyMemoData.getRcvNo()+ " " + cmpyMemoData.getReserveDate() + " " +cmpyMemoData.getCompanyName() + " " +reserveDesc ;
}
%>
						<tr>
							<td width="100%" bgcolor="#F7F7F7" class="sLink2" colspan="2">
								<a class="sLink2">&nbsp;&nbsp;公司名稱管制：<%=company%></a></td>
						</tr>
<%
List<SystemNews> datas = ServiceGetter.getInstance().getSystemNewsLoader().loadSystemNews();
for(SystemNews data : datas) {
%>
						<tr>
							<td width="1%" class="sLink2"><img src="../images/welcome/home_icon01.gif" alt="系統公告" /></td>
							<td width="100%" bgcolor="#F7F7F7" class="sLink2">
								<%="Y".equals(ESAPI.encoder().encodeForHTML(Common.get(data.getIsImportant())))?"<b><font color='indigo'>**重要**</font></b>":"" %>
								<a class="sLink2" href="#" onClick="popBoard('<%=ESAPI.encoder().encodeForHTML(Common.get(data.getId()))%>')"><%=ESAPI.encoder().encodeForHTML(Common.get(data.getSubject())) + " " + Common.formatYYYMMDD(Common.get(data.getStartDate()),4)%></a></td>
						</tr>
<%
}
%>
						<tr>
							<td colspan="4"><img src="../images/welcome/notice_dashedline.gif" alt="系統公告" width="100%" /></td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</td>
	</tr>
	</table>
</td></tr>

</table>
</body>
</html>