<%@ page pageEncoding="UTF-8" contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="shortcut" scope="request" class="com.kangdainfo.tcfi.view.common.Shortcut">
	<jsp:setProperty name="shortcut" property="*"/>
</jsp:useBean>
<!-- QASC:11005310013 原公司/有限合夥查詢功能只前往PRE3008，修正將公司及有限合夥分開 -->
<%
Map<String,String> functionNameMap = new HashMap<String,String>();
functionNameMap.put("PRE1003", "收文確認");
functionNameMap.put("PRE1006", "收文登打");
functionNameMap.put("PRE3002", "輔助查詢");
functionNameMap.put("PRE3004", "馬上辦");
functionNameMap.put("PRE3008", "公司查詢");
functionNameMap.put("PRE3013", "有限合夥查詢");
functionNameMap.put("PRE4001", "預查查詢");
functionNameMap.put("PRE2002", "自取案件");
functionNameMap.put("1001OnlyCmp", "公司查詢");
functionNameMap.put("1001OnlyLms", "有限合夥查詢");

String functions = shortcut.getFunctions();
if( !"".equals(functions) ) {
	String[] funArray = functions.split(",");
for(String fun : funArray) {
%>
<input class="toolbar_shortcut" type="button" id="sc<%=fun %>" name="sc<%=fun %>" value="<%=functionNameMap.get(fun)%>"
	onclick="javascript:shortcut_pop('<%=fun.toLowerCase() %>');" />&nbsp;
<% } %>
<% } %>
<c:if test="${'Y' eq shortcut.shortcut }">
<input class="toolbar_default" type="button" id="sc_close" name="sc_close" value="離開"
	onclick="javascript:window.close();" />&nbsp;
</c:if>