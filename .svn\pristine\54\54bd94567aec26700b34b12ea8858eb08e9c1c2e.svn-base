<%@ page trimDirectiveWhitespaces="true"%>
<%@ page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../../home/<USER>" %>
<%@ page import="com.google.gson.*"%>
<%
response.addHeader("Pragma", "No-cache");
response.addHeader("Cache-Control", "no-cache");
String telixNo = Common.get(ESAPI.encoder().encodeForHTML(request.getParameter("q")));
try {
	Gson gson = new GsonBuilder().create();
	if (!"".equals(telixNo)) {
		SQLJob sqljob = new SQLJob();
		sqljob.appendSQL("SELECT");
		sqljob.appendSQL(" A.TELIX_NO");
		sqljob.appendSQL(",A.PREFIX_NO");
		sqljob.appendSQL(",A.APPLY_TYPE");
		sqljob.appendSQL(",<PERSON>.BAN_NO");
		sqljob.appendSQL(",A.COMPANY_NAME AS OLD_COMPANY_NAME");
		sqljob.appendSQL(",A.COMPANY_ADDR");
		sqljob.appendSQL(",A.APPLY_NAME");
		sqljob.appendSQL(",A.APPLY_ID");
		sqljob.appendSQL(",A.ATTOR_ID");
		sqljob.appendSQL(",A.ATTOR_ADDR");
		sqljob.appendSQL(",A.ATTOR_TEL");
		sqljob.appendSQL(",A.ATTOR_NO");
		sqljob.appendSQL(",A.ATTOR_NAME");
		sqljob.appendSQL(",A.CONTACT_NAME");
		sqljob.appendSQL(",A.CONTACT_ADDR");
		sqljob.appendSQL(",A.CONTACT_TEL");
		sqljob.appendSQL(",A.CONTACT_CEL");
		sqljob.appendSQL(",A.CONTACT_GET_KIND");
		sqljob.appendSQL(",A.CLOSED");
		sqljob.appendSQL(",A.ORGN_TYPE");
		sqljob.appendSQL(",(");
		sqljob.appendSQL("CASE WHEN B.CASE_CODE='Z1' THEN '0'");
		sqljob.appendSQL("WHEN (SELECT COUNT(*) FROM EEDB3000 WHERE TELIX_NO=A.TELIX_NO)=0 THEN '2'");
		sqljob.appendSQL("WHEN (SELECT COUNT(*) FROM EEDB3100 WHERE TELIX_NO=A.TELIX_NO)=0 THEN '1'");
		sqljob.appendSQL("ELSE '3' END");
		sqljob.appendSQL(") AS CHANGE_TYPE");
		sqljob.appendSQL(",B.CASE_CODE");
		sqljob.appendSQL(",(SELECT ORG_CORP_NO FROM EEDB3300 WHERE TELIX_NO=A.TELIX_NO) as ORG_CORP_NO");
		sqljob.appendSQL(",(SELECT ORG_CORP_NAME FROM EEDB3300 WHERE TELIX_NO=A.TELIX_NO) as ORG_CORP_NAME");
		sqljob.appendSQL("FROM EEDB1000 A");
		sqljob.appendSQL(", EEDB1100 B");
		sqljob.appendSQL("WHERE A.TELIX_NO =?");
		sqljob.appendSQL("AND A.TELIX_NO = B.TELIX_NO");
		sqljob.addParameter(telixNo);
		List<Map<String,Object>> datas = ServiceGetter.getInstance().getEedbGeneralQueryDao().queryForList(sqljob);
		if (null!=datas && !datas.isEmpty()) {
			//System.out.println(gson.toJson(datas.get(0)));
			out.write(gson.toJson(datas.get(0)));
		}
	}
} catch (Exception e) {
	e.printStackTrace();
}
%>