package com.kangdainfo.tcfi.model.eicm.bo;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;

public class PrefixVo extends Cedb1000 {
	private static final long serialVersionUID = 1L;

	public PrefixVo(Cedb1000 cedb1000) {
		//super(cedb1000); // 預查主檔
		try {
			BeanUtils.copyProperties(this, cedb1000);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			e.printStackTrace();
		}
	}
	
	public PrefixVo() {
		//super();
	}

	private List<Cedb1001> cedb1001s;
	private List<Cedb1002> cedb1002s;
	private List<Cedb1003> cedb1003s;
	private List<Cedb1004> cedb1004s;
	private List<Cedb1006> cedb1006s;
	private List<Cedb1009> cedb1009s;
	private List<Cedb1010> cedb1010s;
	private Cedb1022 cedb1022; // 法人資料
	private Cedb1023 cedb1023; // 收件人資料
	private Cedb1027 cedb1027; // 郵寄記錄
	private String mainFileCompanyName;
	private String lastCompanyName;
	private String updateName; //前次異動者
	private String lastKeyin; //前次登打者
	private String applyWay; //申辦方式
	private String prefixStatusDesc; //案件狀態
	private String postNo; //cedb1027的掛號號碼

	/** 收件日期時間 */
	private String receiveDateTime;
	/** 收文登打日期時間 */
	private String receiveKeyinDateTime;
	/** 分文日期時間 */
	private String assignDateTime;
	/** 審核日期時間 */
	private String approveDateTime;
	/** 發文登打日期時間 */
	private String issueKeyinDateTime;
	/** 發文日期時間 */
	private String closeDateTime;
	/** 領件日期時間 */
	private String getDateTime;
	/** 案件提示文字 */
	private String reserveTip;
	/** 公司設立日期 */
	private String cmpySetupDate;

	public String getPrefixStatusDesc() {return prefixStatusDesc;}
	public void setPrefixStatusDesc(String s) {this.prefixStatusDesc = s;}

	public String getMainFileCompanyName() {return mainFileCompanyName;}
	public void setMainFileCompanyName(String s) {this.mainFileCompanyName = s;}

	public String getLastCompanyName() {return lastCompanyName;}
	public void setLastCompanyName(String s) {this.lastCompanyName = s;}

	public List<Cedb1006> getCedb1006s() {return cedb1006s;}
	public void setCedb1006s(List<Cedb1006> cedb1006s) {this.cedb1006s = cedb1006s;}

	public List<Cedb1001> getCedb1001s() {return cedb1001s;}
	public void setCedb1001s(List<Cedb1001> cedb1001s) {this.cedb1001s = cedb1001s;}

	public List<Cedb1002> getCedb1002s() {return cedb1002s;}
	public void setCedb1002s(List<Cedb1002> cedb1002s) {this.cedb1002s = cedb1002s;}

	public List<Cedb1003> getCedb1003s() {return cedb1003s;}
	public void setCedb1003s(List<Cedb1003> cedb1003s) {this.cedb1003s = cedb1003s;}

	public List<Cedb1004> getCedb1004s() {return cedb1004s;}
	public void setCedb1004s(List<Cedb1004> cedb1004s) {this.cedb1004s = cedb1004s;}

	public List<Cedb1010> getCedb1010s() {return cedb1010s;}
	public void setCedb1010s(List<Cedb1010> cedb1010s) {this.cedb1010s = cedb1010s;}

	public Cedb1022 getCedb1022() {return cedb1022;}
	public void setCedb1022(Cedb1022 cedb1022) {this.cedb1022 = cedb1022;}

	public Cedb1023 getCedb1023() {return cedb1023;}
	public void setCedb1023(Cedb1023 cedb1023) {this.cedb1023 = cedb1023;}

	public List<Cedb1009> getCedb1009s() {return cedb1009s;}
	public void setCedb1009s(List<Cedb1009> cedb1009s) {this.cedb1009s = cedb1009s;}

	public Cedb1027 getCedb1027() {return cedb1027;}
	public void setCedb1027(Cedb1027 cedb1027) {this.cedb1027 = cedb1027;}

	public String getUpdateName() {return updateName;}
	public void setUpdateName(String s) {this.updateName = s;}

	public String getApplyWay() {return applyWay;}
	public void setApplyWay(String s) {this.applyWay = s;}

	public String getLastKeyin() {return lastKeyin;}
	public void setLastKeyin(String s) {this.lastKeyin = s;}

	public String getPostNo() {return postNo; }
	public void setPostNo(String s) {this.postNo = s;}

	public String getReceiveDateTime() {return receiveDateTime;}
	public void setReceiveDateTime(String s) {this.receiveDateTime = s;}

	public String getReceiveKeyinDateTime() {return receiveKeyinDateTime;}
	public void setReceiveKeyinDateTime(String s) {this.receiveKeyinDateTime = s;}

	public String getAssignDateTime() {return assignDateTime;}
	public void setAssignDateTime(String s) {this.assignDateTime = s;}

	public String getApproveDateTime() {return approveDateTime;}
	public void setApproveDateTime(String s) {this.approveDateTime = s;}

	public String getIssueKeyinDateTime() {return issueKeyinDateTime;}
	public void setIssueKeyinDateTime(String s) {this.issueKeyinDateTime = s;}

	public String getCloseDateTime() {return closeDateTime;}
	public void setCloseDateTime(String s) {this.closeDateTime = s;}

	public String getGetDateTime() {return getDateTime;}
	public void setGetDateTime(String s) {this.getDateTime = s;}

	public String getReserveTip() {return reserveTip;}
	public void setReserveTip(String s) {this.reserveTip = s;}

	public String getCmpySetupDate() {return cmpySetupDate;}
	public void setCmpySetupDate(String s) {this.cmpySetupDate = s;}

}