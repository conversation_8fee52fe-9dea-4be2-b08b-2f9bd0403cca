<!DOCTYPE html>
<!--
程式目的：參數代碼維護
程式代號：PRE9004
撰寫日期：
程式作者：
--------------------------------------------------------
修改作者　　修改日期　　　修改目的
--------------------------------------------------------
-->
<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="../../home/<USER>" %>
<jsp:useBean id="obj"  scope="request" class="com.kangdainfo.tcfi.view.pre.PRE9004">
    <jsp:setProperty name='obj' property='*'/>
</jsp:useBean>
<jsp:useBean id="objList"  scope="page" class="java.util.ArrayList"/>      
<jsp:include page="../../home/<USER>">
	<jsp:param name="DTREE_PROGRAM_IDENTIFIER" value="PRE9004" />
</jsp:include>
<%
if ("queryAll".equals(obj.getState())) {
	if ("false".equals(obj.getQueryAllFlag())){obj.setQueryAllFlag("true"); }
}else if ("queryOne".equals(obj.getState())) {
	obj = (com.kangdainfo.tcfi.view.pre.PRE9004)obj.queryOne();
}else if ("insert".equals(obj.getState()) || "insertError".equals(obj.getState())) {
	obj.insert();
	if ("insertSuccess".equals(obj.getState())) {
		obj.setQueryAllFlag("true");
		obj.setQ_id(obj.getId());
	}
}else if ("update".equals(obj.getState()) || "updateError".equals(obj.getState())) {
	obj.update();
}else if ("delete".equals(obj.getState()) || "deleteError".equals(obj.getState())) {
	obj.delete();
	if ("deleteSuccess".equals(obj.getState()))	obj.setErrorMsg("刪除完成，刪除功能只設定停用!!");
}
if ("true".equals(obj.getQueryAllFlag())){
	objList = obj.queryAll();
}
%>
<html>
<head>
<%@ include file="../../home/<USER>" %>
<script type="text/javascript">
var insertDefault = new Array(
	new Array("enable", "Y")
);

function checkField(){
	var alertStr="";
	if(form1.state.value=="queryAll"){
		alertStr += checkQuery();
	}else if(form1.state.value=="insert"||form1.state.value=="insertError"||form1.state.value=="update"||form1.state.value=="updateError"){
		alertStr += checkEmpty(form1.codeKind,"代碼類別");
		alertStr += checkEmpty(form1.code,"代碼");
		alertStr += checkEmpty(form1.codeName,"代碼名稱");
		alertStr += checkEmpty(form1.enable,"是否啟用");
	}
	if(alertStr.length!=0){ alert(alertStr); return false; }
	beforeSubmit();
}
function queryOne(id){
	form1.id.value=id;
	form1.state.value="queryOne";
	beforeSubmit();
	form1.submit();
}

function init() {
}
</script>
</head>

<body topmargin="0" onLoad="whatButtonFireEvent('<%=obj.getState()%>');init();">
<form id="form1" name="form1" method="post" onSubmit="return checkField()" autocomplete="off">

<!--Query區============================================================-->
<div id="queryContainer" style="width:400px;height:250px;display:none">
	<iframe id="queryContainerFrame"></iframe>
	<div class="queryTitle">查詢視窗</div>
	<table class="queryTable"  border="1">
	<tr>
		<td class="queryTDLable">代碼類別：</td>
		<td class="queryTDInput">
			<select class="field_Q" name=q_codeKind id="q_codeKind" value="<%=obj.getQ_codeKind()%>">
				<%=View.getOptionSystemCodeKind(obj.getQ_codeKind(), false, 1) %>
            </select>
		</td>
	</tr>
	<tr>
		<td class="queryTDLable">代碼名稱：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="text" name="q_codeName" size="40" maxlength="100" value="<%=obj.getQ_codeName()%>">
		</td>
	</tr>
	<!-- 
	<tr>
		<td class="queryTDLable">是否啟用：</td>
		<td class="queryTDInput">
			<input class="field_Q" type="checkbox" name="q_enable" value="Y" <%="Y".equals(obj.getEnable())?"checked":""%> />
		</td>
	</tr>
	-->
	<tr>
		<td class="queryTDInput" colspan="2" style="text-align:center;">
			<input type="hidden" name="q_id" value="<%=obj.getQ_id()%>" class="field_Q">
			<input class="toolbar_default" followPK="false" type="submit" name="querySubmit" value="確　　定">
			<input class="toolbar_default" followPK="false" type="button" name="queryCannel" value="取　　消" onClick="whatButtonFireEvent(this.name)">
		</td>
	</tr>
	</table>
</div>

<c:import url="../common/function_banner.jsp">
  <c:param name="function" value='PRE9004'/>
</c:import>

<table width="100%" cellspacing="0" cellpadding="0">

<!-- TOOLBAR AREA -->
<tr><td class="bgToolbar" style="text-align:left">
	<jsp:include page="../../home/<USER>" >
		<jsp:param name="btnInsert" value="Y" />
		<jsp:param name="btnQueryAll" value="Y" />
		<jsp:param name="btnUpdate" value="Y" />
		<jsp:param name="btnDelete" value="Y" />
		<jsp:param name="btnClear" value="Y" />
		<jsp:param name="btnConfirm" value="Y" />
		<jsp:param name="btnListPrint" value="N" />
		<jsp:param name="btnListHidden" value="N" />
	</jsp:include>
	<input type="hidden" name="id" value="<%=obj.getId()%>">
	<input type="hidden" name="state" value="<%=obj.getState()%>">
	<input type="hidden" name="queryAllFlag" value="<%=obj.getQueryAllFlag()%>">
</td></tr>
<!-- TOOLBAR AREA -->

<!-- FORM AREA -->
<tr><td class="bg">
	<div id="formContainer" style="height:250px;">
	<table class="table_form" width="100%" height="100%">
	<tr>
		<td class="td_form" width="20%"><font color="red">*</font>代碼類別：</td>
		<td colspan="3" class="td_form_white">
            <select class="field" name=codeKind id="codeKind" value="<%=obj.getCodeKind()%>">
				<%=View.getOptionSystemCodeKind(obj.getCodeKind(), false, 1) %>
            </select>
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%"><font color="red">*</font>代碼：</td>
		<td class="td_form_white" width="30%">
			<input class="field" type="text" name="code" size="10" maxlength="20" value="<%=obj.getCode()%>">
		</td>
		<td class="td_form" width="20%"><font color="red">*</font>代碼名稱：</td>
		<td class="td_form_white" width="30%">
			<input class="field" type="text" name="codeName" size="40" maxlength="100" value="<%=obj.getCodeName()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%">參數1：</td>
		<td class="td_form_white" colspan="3" >
			<input class="field" type="text" name="codeParam1" size="30" maxlength="100" value="<%=obj.getCodeParam1()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%">參數2：</td>
		<td class="td_form_white" colspan="3">
			<input class="field" type="text" name="codeParam2" size="30" maxlength="100" value="<%=obj.getCodeParam2()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%">參數3：</td>
		<td class="td_form_white" colspan="3">
			<input class="field" type="text" name="codeParam3" size="30" maxlength="100" value="<%=obj.getCodeParam3()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%">代碼說明：</td>
		<td colspan="3" class="td_form_white">
			<input class="field" type="text" name="codeDesc" size="70" maxlength="100" value="<%=obj.getCodeDesc()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%">備註：</td>
		<td colspan="3" class="td_form_white">
			<input class="field" type="text" name="remark" size="70" maxlength="200" value="<%=obj.getRemark()%>">
		</td>
	</tr>
	<tr>
		<td class="td_form" width="20%"><font color="red">*</font>是否啟用：</td>
		<td colspan="3" class="td_form_white">
		<!-- 
			<input class="field" type="checkbox" name="enable" value="Y" <%="Y".equals(obj.getEnable())?"checked":""%> />
		 -->
			<select name="enable" class="field">
	    		<%=View.getTextOption("Y;是;N;否;", obj.getEnable(), 1)%>
	    	</select>	
		</td>
	</tr>
	</table>
	</div>
	<c:import url="../common/msgbar.jsp">
	  <c:param name="errorMsg" value='<%=obj.getErrorMsg()%>'/>
	</c:import>
</td></tr>

<tr><td class="tab_line1"></td></tr>
<tr><td class="tab_line1"></td></tr>

<!-- List AREA -->
<tr><td class="bg">
<div id="listContainer">
<table class="table_form" width="100%" cellspacing="0" cellpadding="0">
	<thead id="listTHEAD">
	<tr>
		<th class="listTH" ><a class="text_link_w">序號</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',1,false);" href="#">代碼類別</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',2,false);" href="#">代碼</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',3,false);" href="#">代碼名稱</a></th>
		<th class="listTH"><a class="text_link_w" onClick="return sortTable('listTBODY',5,false);" href="#">是否啟用</a></th>
	</tr>
	</thead>
	<tbody id="listTBODY">
	<%
	boolean primaryArray[] = {true,false,false,false,false};
	boolean displayArray[] = {false,true,true,true,true};
	String[] alignArray = {"center","center","center","left","center"};
	out.write(View.getQuerylist(primaryArray,displayArray,alignArray,objList,obj.getQueryAllFlag(),true));
	%>
	</tbody>
</table>
</div>
</td></tr>
<!-- List AREA -->

</table>
</form>
</body>
</html>